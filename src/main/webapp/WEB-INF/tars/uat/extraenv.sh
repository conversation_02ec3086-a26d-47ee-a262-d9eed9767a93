#!/bin/bash

# ��ȡ Java �汾
version=$(java -version 2>&1 | awk -F[\"_] 'NR==1{print $2}' | cut -d '.' -f 1)
FINAL_XMX_RATIO=65

# ����ṩ�� CUSTOMER_XMX_RATIO����ʹ����������Ĭ��ֵ
if [[ -n "$CUSTOMER_XMX_RATIO" ]]; then
    FINAL_XMX_RATIO="$CUSTOMER_XMX_RATIO"
fi


# ����Զ����ڴ������ Java �汾
if [[ "$version" == "21" ]]; then
    # ȷ�� CDOS_MEM ������
    if [[ -z "$CDOS_MEM" ]]; then
        echo "Error: CDOS_MEM is not set."
        exit 1
    fi

    # ���� JVM �ڴ��С
    JVM_XMX_MEM=$(echo "$CDOS_MEM * $FINAL_XMX_RATIO / 100" | bc -l)
    if [[ $? -ne 0 ]]; then
        echo "Error: Failed to calculate JVM memory size."
        exit 1
    fi
    JVM_XMX_MEM=${JVM_XMX_MEM%.*}

# ����mirror���������������̵߳ĸ���
TRACE_PINNED_OPTS=""

if [["$PAAS_APP_IS_MIRROR_GROUP" == "true"]];then
TRACE_PINNED_OPTS = "-Djdk.tracePinnedThreads=full"
fi

    # ���� JVM ����
    JAVA_OPTS="${JAVA_OPTS} \
    -XX:MaxDirectMemorySize=1024M \
    --add-opens java.base/java.nio=ALL-UNNAMED \
    -Dio.netty.tryReflectionSetAccessible=true \
    -Xms${JVM_XMX_MEM}m \
    -Xmx${JVM_XMX_MEM}m \
    -Dio.grpc.triplog.shaded.netty.shaded.io.netty.tryReflectionSetAccessible=true \
    -Dcdubbo.shade.io.netty.tryReflectionSetAccessible=true \
    ${TRACE_PINNED_OPTS}"
fi



