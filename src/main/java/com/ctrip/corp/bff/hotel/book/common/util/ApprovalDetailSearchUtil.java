package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.approvalinfo.ControlInfoTypeEnum;
import com.ctrip.corp.bff.hotel.book.contract.ApprovalBaseInfoInput;
import com.ctrip.corp.bff.hotel.book.contract.ApprovalDetailSearchRequestType;
import com.ctrip.corp.bff.profile.contract.SSOBaseInfo;
import com.ctrip.corp.bff.profile.contract.SSOExtendInfo;
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryResponseType;
import com.ctrip.corp.bff.specific.contract.ApprovalInfo;
import com.ctrip.corp.bff.specific.contract.BatchApprovalDefaultResponseType;
import com.ctrip.corp.bff.specific.contract.ControlInfo;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-11-14
 **/
public class ApprovalDetailSearchUtil {
    /**
     * 酒店产线
     */
    private static final String PRODUCT_HOTEL = "Hotel";

    private static final String PRODUCT_TRANSFER = "Transfer";

    private static final String SEARCH_TYPE = "searchType";

    private static final List<String> SEARCH_TYPE_OVERSEA = Arrays.asList("oversea_hotel", "overseaHotel", "overseaHotelDetail");

    private static final List<String> SEARCH_TYPE_DOMESTIC = Arrays.asList("domestic_hotel", "domesticHotel", "domesticHotelDetail");

    /**
     * 中国酒店
     */
    private static final String CN_HOTEL = "CN_HOTEL";
    /**
     * 海外酒店
     */
    private static final String INTERNATIONAL_HOTEL = "INTERNATIONAL_HOTEL";

    public static ApprovalInfo buildDefaultApprovalInfo(BatchApprovalDefaultResponseType batchApprovalDefaultResponseType) {
        if (batchApprovalDefaultResponseType == null) {
            return null;
        }
        List<ApprovalInfo> approvalInfos = Optional.ofNullable(batchApprovalDefaultResponseType.getApprovalInfos()).orElse(new ArrayList<>());
        return approvalInfos.stream().filter(approvalInfo -> CommonConstant.OPEN.equalsIgnoreCase(approvalInfo.getDefaultApproval()))
                .findFirst().orElse(null);
    }

    public static List<String> buildDefaultCityIds(BatchApprovalDefaultResponseType batchApprovalDefaultResponseType) {
        ApprovalInfo defaultApprovalInfo = buildDefaultApprovalInfo(batchApprovalDefaultResponseType);
        if (defaultApprovalInfo == null) {
            return null;
        }
        ControlInfo controlInfo = buildControlInfo(defaultApprovalInfo.getControlInfos(), ControlInfoTypeEnum.CITY_ID.getCode());
        if (controlInfo == null || StringUtil.isEmpty(controlInfo.getContent())) {
            return null;
        }
        return Arrays.stream(controlInfo.getContent().split(",")).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public static ControlInfo buildControlInfo(List<ControlInfo> controlInfoList, String type) {
        if (CollectionUtil.isEmpty(controlInfoList) || StringUtil.isEmpty(type)) {
            return null;
        }
        return controlInfoList.stream().filter(info -> type.equalsIgnoreCase(info.getType())).findFirst().orElse(null);
    }

    /**
     * 获取单点登录过来的审批单号
     * @param ssoInfoQueryResponseType
     * @return
     */
    public static String getExtendInfoValueByTransfer(SSOInfoQueryResponseType ssoInfoQueryResponseType, String extendInfoType) {
        if (ssoInfoQueryResponseType == null) {
            return null;
        }
        if (CollectionUtil.isEmpty(ssoInfoQueryResponseType.getSsoExtendInfoList())) {
            return null;
        }
        SSOExtendInfo ssoExtendInfo = ssoInfoQueryResponseType.getSsoExtendInfoList().stream()
                .filter(extendInfo -> PRODUCT_TRANSFER.equalsIgnoreCase(extendInfo.getProductType())).findFirst().orElse(null);
        if (ssoExtendInfo == null || CollectionUtil.isEmpty(ssoExtendInfo.getExtendInfo())) {
            return null;
        }
        return ssoExtendInfo.getExtendInfo().get(extendInfoType);
    }


    /**
     * 获取单点登录过来的审批单号
     * @param ssoInfoQueryResponseType
     * @return
     */
    public static String getExtendInfoValue(SSOInfoQueryResponseType ssoInfoQueryResponseType, String extendInfoType) {
        if (ssoInfoQueryResponseType == null) {
            return null;
        }
        if (CollectionUtil.isEmpty(ssoInfoQueryResponseType.getSsoExtendInfoList())) {
            return null;
        }
        SSOExtendInfo ssoExtendInfo = ssoInfoQueryResponseType.getSsoExtendInfoList().stream()
                .filter(extendInfo -> PRODUCT_HOTEL.equalsIgnoreCase(extendInfo.getProductType())).findFirst().orElse(null);
        if (ssoExtendInfo == null || CollectionUtil.isEmpty(ssoExtendInfo.getExtendInfo())) {
            return null;
        }
        return ssoExtendInfo.getExtendInfo().get(extendInfoType);
    }

    public static List<String> buildProductType(ApprovalDetailSearchRequestType approvalDetailSearchRequest, SSOInfoQueryResponseType ssoInfoQueryResponseType) {
        String extendInfoValue = ApprovalDetailSearchUtil.getExtendInfoValueByTransfer(ssoInfoQueryResponseType, SEARCH_TYPE);
        if (StringUtil.isNotEmpty(extendInfoValue)) {
            if (SEARCH_TYPE_OVERSEA.contains(extendInfoValue)) {
                return Collections.singletonList(INTERNATIONAL_HOTEL);
            } else if (SEARCH_TYPE_DOMESTIC.contains(extendInfoValue)) {
                return Collections.singletonList(CN_HOTEL);
            }
        }
        String ssoApprovalNo = Optional.ofNullable(ssoInfoQueryResponseType).map(SSOInfoQueryResponseType::getSsoBaseInfo).map(SSOBaseInfo::getApprovalNo).orElse(null);
        if (StringUtil.isNotEmpty(ssoApprovalNo)) {
            return Arrays.asList(CN_HOTEL, INTERNATIONAL_HOTEL);
        }
        String cityType = Optional.ofNullable(approvalDetailSearchRequest.getApprovalBaseInfoInput()).map(ApprovalBaseInfoInput::getCityType).orElse(null);
        if (StringUtils.isNotEmpty(cityType)) {
            return CommonConstant.DOMESTIC.equals(cityType) ? Collections.singletonList(CN_HOTEL) : Collections.singletonList(INTERNATIONAL_HOTEL);
        }
        return Arrays.asList(CN_HOTEL, INTERNATIONAL_HOTEL);
    }
}
