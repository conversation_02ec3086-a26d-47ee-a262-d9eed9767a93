package com.ctrip.corp.bff.hotel.book.handler.orderdataaggregationqueryservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.order.data.aggregation.query.contract.OrderDataAggregationQueryServiceClient;
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataRequestType;
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 聚合订单数据查询
 * @Date 2024/4/8 19:54
 * @Version 1.0
 */
@Component
public class HandlerOfQueryHotelOrderData extends
        AbstractHandlerOfSOA<QueryHotelOrderDataRequestType, QueryHotelOrderDataResponseType, OrderDataAggregationQueryServiceClient> {

    @Override
    protected String getMethodName() {
        return "queryHotelOrderData";
    }
}
