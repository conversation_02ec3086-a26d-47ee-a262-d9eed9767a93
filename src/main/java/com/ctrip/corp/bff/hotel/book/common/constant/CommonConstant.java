package com.ctrip.corp.bff.hotel.book.common.constant;

/**
 * <AUTHOR>
 * @date 2024-07-03
 **/
public class CommonConstant {

    public static final Integer SUCCESS_20000 = 20000;
    public static final int APPID = 100053122;

    /**
     * 禁止预订
     */
    public static final String FORBID_BOOKING = "FORBID_BOOKING";

    /**
     * 可继续提交
     */
    public static final String ALLOW_BOOKING = "ALLOW_BOOKING";
    /**
     * 开关开启
     */
    public static final String OPEN = "T";

    /**
     * 开关关闭
     */
    public static final String OFF = "F";
    public static final String UNNECESSARY = "UNNECESSARY";
    public static final String EMERGENCY = "EMERGENCY";
    public static final String FAIL = "FAIL";
    public static final String PASS = "PASS";
    public static final String CONTROL_BY_PSG = "P";

    /**
     * 人民币符号RMB
     */
    public static final String RMB = "RMB";

    /**
     * 人民币符号CNY
     */
    public static final String CNY = "CNY";
    public static final String CLIENTTYPE_MINIAPP = "MiniApp";

    private static final String PUBLIC = "public";
    private static final String PRIVATE = "private";

    public static final String ONLINE = "ONLINE";
    public static final String OFFLINE = "OFFLINE";
    public static final String APP = "APP";
    public static final int SHARE_ROOM_ROOM_CAPACITY = 3;

    public static final String KEY_OVERSEA_HOTEL_MAX_ROOM_NUMBER_OFFLINE = "overseaHotelMaxRoomNumberOffline";
    public static final String KEY_OVERSEA_HOTEL_MAX_ROOM_NUMBER_ONLINE = "overseaHotelMaxRoomNumberOnline";
    public static final String KEY_OVERSEA_HOTEL_MAX_ROOM_NUMBER_APP = "overseaHotelMaxRoomNumberApp";
    public static final String KEY_DOMESTIC_HOTEL_MAX_ROOM_NUMBER_OFFLINE = "domesticHotelMaxBookNumberOffline";
    public static final String KEY_DOMESTIC_HOTEL_MAX_ROOM_NUMBER_ONLINE = "domesticHotelMaxBookNumberOnline";
    public static final String KEY_DOMESTIC_HOTEL_MAX_ROOM_NUMBER_APP = "domesticHotelMaxBookNumberApp";
    public static final int DEFAULT_MAX_ROOM_NUMBER = 8;

    /**
     * 前置
     */
    public static final String PRE_POSITION = "PRE_POSITION";
    /**
     * 后置
     */
    public static final String POST_POSITION = "POST_POSITION";
    /**
     * 海外
     */
    public static final String OVERSEA = "OVERSEA";
    /**
     * 国内
     */
    public static final String DOMESTIC = "DOMESTIC";
    /**
     * 是否支持月租房
     */
    public static final String CAN_LONG_RENT = "CAN_LONG_RENT";
    /**
     * 初始化配置
     */
    public static final String INIT_CONFIG = "initConfig.properties";

    /**
     * bagashi资源的vendorId
     */
    public static final int VENDOR_ID_BAGASHI = 2417;

    public static final String GENDER_MALE = "M";
    public static final String GENDER_FEMALE = "F";

    public static final String USE_NEW = "USE_NEW";
    public static final String COMPARE = "COMPARE";
    public static final String USE_OLD = "USE_OLD";
}
