package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoRequestType;
import com.ctrip.corp.hotel.book.query.entity.QueryBaseEntity;
import com.ctrip.corp.hotel.book.query.entity.UserInfoEntity;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2024/8/19 23:20
 */
@Component public class MapperOfGetCityBaseInfoRequestTypeByCityId
    extends AbstractMapper<Tuple2<IntegrationSoaRequestType, Integer>, GetCityBaseInfoRequestType> {

    @Override protected GetCityBaseInfoRequestType convert(Tuple2<IntegrationSoaRequestType, Integer> tuple) {
        IntegrationSoaRequestType integrationSoaRequestType = tuple.getT1();
        Integer cityId = tuple.getT2();
        GetCityBaseInfoRequestType getCityBaseInfoRequestType = new GetCityBaseInfoRequestType();
        getCityBaseInfoRequestType.setBaseInfo(getQueryBaseEntity(integrationSoaRequestType));
        getCityBaseInfoRequestType.setCityIdList(Collections.singletonList(cityId));
        return getCityBaseInfoRequestType;
    }

    @Override protected ParamCheckResult check(Tuple2<IntegrationSoaRequestType, Integer> tuple) {
        return null;
    }

    private QueryBaseEntity getQueryBaseEntity(IntegrationSoaRequestType integrationSoaRequestType) {
        QueryBaseEntity queryBaseEntity = new QueryBaseEntity();
        queryBaseEntity.setLocale(integrationSoaRequestType.getLanguage());
        queryBaseEntity.setUserInfo(getUserInfoEntity(integrationSoaRequestType));
        queryBaseEntity.setTraceId(integrationSoaRequestType.getRequestId());
        queryBaseEntity.setRequestFrom(integrationSoaRequestType.getSourceFrom().name());
        return queryBaseEntity;
    }

    private UserInfoEntity getUserInfoEntity(IntegrationSoaRequestType integrationSoaRequestType) {
        UserInfoEntity userInfoEntity = new UserInfoEntity();
        userInfoEntity.setCorpId(integrationSoaRequestType.getUserInfo().getCorpId());
        userInfoEntity.setUid(integrationSoaRequestType.getUserInfo().getUserId());
        return userInfoEntity;

    }
}
