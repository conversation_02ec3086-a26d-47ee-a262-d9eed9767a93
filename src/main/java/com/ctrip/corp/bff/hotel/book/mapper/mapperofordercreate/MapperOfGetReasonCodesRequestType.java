package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import corp.user.service.corp4jservice.GetReasoncodesRequestType;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/8/28
 */
@Component
public class MapperOfGetReasonCodesRequestType extends AbstractMapper<Tuple1<IntegrationSoaRequestType>, GetReasoncodesRequestType> {

    /**
     * 酒店
     */
    private static final String H = "H";

    private static final String NUM_128 = "128";
    @Override
    protected GetReasoncodesRequestType convert(Tuple1<IntegrationSoaRequestType> para) {
        GetReasoncodesRequestType res = new GetReasoncodesRequestType();
        res.setCorpId(Optional.ofNullable(para).map(Tuple1::getT1).map(IntegrationSoaRequestType::getUserInfo).map(UserInfo::getCorpId).orElse(null));
        res.setBizType(H);
        return res;
    }

    @Override
    protected ParamCheckResult check(Tuple1<IntegrationSoaRequestType> integrationSoaRequestTypeTuple1) {
        return null;
    }
}
