package com.ctrip.corp.bff.hotel.book.service;

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.processor.Processor;
import com.ctrip.corp.bff.framework.template.service.AbstractSyncService;
import com.ctrip.corp.bff.framework.template.service.generate.WebService;
import com.ctrip.corp.bff.hotel.book.contract.ApprovalDetailSearchRequestType;
import com.ctrip.corp.bff.hotel.book.contract.ApprovalDetailSearchResponseType;
import com.ctrip.corp.bff.hotel.book.processor.ProcessorOfApprovalDetailSearch;
import org.springframework.beans.factory.annotation.Autowired;


/**
 * <AUTHOR>
 * @description 填写页
 * @date 2024/06/26
 */
@WebService(name = "ApprovalDetailSearch")
public class ServiceOfApprovalDetailSearch extends AbstractSyncService<ApprovalDetailSearchRequestType, ApprovalDetailSearchResponseType> {
    @Autowired
    private ProcessorOfApprovalDetailSearch processorOfApprovalDetailSearch;

    @Override
    public void validateRequest(ApprovalDetailSearchRequestType requestType) throws BusinessException {

    }

    @Override
    protected Processor<ApprovalDetailSearchRequestType, ApprovalDetailSearchResponseType> getProcessor(ApprovalDetailSearchRequestType requestType) {
        return processorOfApprovalDetailSearch;
    }
}
