package com.ctrip.corp.bff.hotel.book.common.mapper;

import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextRequestType;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.soa._21685.PayConfigRequestType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/6/23 18:31
 */
@Component
public class MapperOfPayConfigRequestType extends
        AbstractMapper<Tuple1<IntegrationSoaRequestType>, PayConfigRequestType> {
    @Override
    protected PayConfigRequestType convert(Tuple1<IntegrationSoaRequestType> tuple) {
        IntegrationSoaRequestType requestType = tuple.getT1();
        PayConfigRequestType payConfigRequestType = new PayConfigRequestType();
        payConfigRequestType.setRequestId(requestType.getRequestId());
        payConfigRequestType.setCorpId(requestType.getUserInfo().getCorpId());
        return payConfigRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple1<IntegrationSoaRequestType> tuple) {
        return null;
    }
}
