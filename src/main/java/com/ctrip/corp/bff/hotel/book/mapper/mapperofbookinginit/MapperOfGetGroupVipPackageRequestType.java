package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.agg.hotel.roomavailable.entity.*;
import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.hotel.wireless.Head;
import com.ctrip.hotel.wireless.htlgetgroupvippackagefunction.soa.contract.GetGroupVipPackageRequestType;
import com.ctrip.hotel.wireless.htlgetgroupvippackagefunction.soa.contract.SearchParam;
import corp.user.service.corpUserInfoService.GetPlatformRelationByUidResponseType;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * @Author: chenchuang
 * @Date: 2024/8/22 16:26
 * @Description:
 * @Param: groupId, registerUid
 */
@Component
public class MapperOfGetGroupVipPackageRequestType extends AbstractMapper<Tuple3<BookingInitRequestType, WrapperOfCheckAvail.CheckAvailInfo,
        GetPlatformRelationByUidResponseType>
        , GetGroupVipPackageRequestType> {

    /**
     * 商旅uid注册
     */
    public static final String BUSINESS_TRAVEL_REGISTER = "BUSINESS_TRAVEL_REGISTER";

    /**
     * 携程uid注册
     */
    public static final String TRIP_REGISTER = "TRIP_REGISTER";

    /**
     * 不需要注册
     */
    public static final String NO_REGISTER = "NO_REGISTER";

    @Override
    protected GetGroupVipPackageRequestType convert(Tuple3<BookingInitRequestType, WrapperOfCheckAvail.CheckAvailInfo,
            GetPlatformRelationByUidResponseType> param) {
        BookingInitRequestType bookingInitRequest = param.getT1();
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = param.getT2();
        String groupRegisterRule = Optional.ofNullable(checkAvailInfo.getBookingRules()).map(BookingRulesType::getGroupMemberShipInfo)
                .map(GroupMemberShipType::getGroupRegisterRule).orElse("");
        Integer groupId = Optional.ofNullable(checkAvailInfo.getHotelItem()).map(HotelItem::getHotelBrandInfo).map(HotelBrandItem::getGroupId).orElse(null);
        GetGroupVipPackageRequestType request = new GetGroupVipPackageRequestType();
        Head head = new Head();
        head.setUid(getRegisterUid(groupRegisterRule, RequestHeaderUtil.getUserId(bookingInitRequest.getIntegrationSoaRequestType()), param.getT3()));
        head.setTraceLogID(bookingInitRequest.getIntegrationSoaRequestType().getRequestId());
        request.setHead(head);
        SearchParam searchParam = new SearchParam();
        searchParam.setMgrGroupId(groupId);
        searchParam.setIsRealTimeQueries(false);
        request.setSearchParam(searchParam);
        return request;
    }

    private String getRegisterUid(String registerRule, String uid, GetPlatformRelationByUidResponseType getPlatformRelationByUidResponseType) {
        if (StringUtil.isBlank(registerRule)) {
            return uid;
        }
        switch (registerRule) {
            case TRIP_REGISTER:
                return getPlatformRelationByUid(getPlatformRelationByUidResponseType);
            case NO_REGISTER:
                return null;
            case BUSINESS_TRAVEL_REGISTER:
            default:
                return uid;
        }
    }

    protected String getPlatformRelationByUid(GetPlatformRelationByUidResponseType getPlatformRelationByUidResponseType) {
        if (getPlatformRelationByUidResponseType == null) {
            return null;
        }
        return getPlatformRelationByUidResponseType.getAccountId();
    }

    @Override
    protected ParamCheckResult check(Tuple3<BookingInitRequestType, WrapperOfCheckAvail.CheckAvailInfo,
            GetPlatformRelationByUidResponseType> param) {
        return null;
    }
}
