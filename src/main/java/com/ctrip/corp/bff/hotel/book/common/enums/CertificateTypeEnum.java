package com.ctrip.corp.bff.hotel.book.common.enums;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;

import java.util.Arrays;

/**
 * @Author: chenchuang
 * @Date: 2024/9/27 21:52
 * @Description: todo
 */
public enum CertificateTypeEnum {

    /**
     * 身份证
     */
    IDENTITY_CARD("IdentityCard"),

    /**
     * 护照
     */
    PASSPORT("Passport"),

    /**
     * 台胞证
     */
    TAIWANESE_CERTIFICATE("TaiwaneseCertificate"),

    /**
     * 大陆居民往来台湾通行证
     */
    TAIWAN_PASS("TaiwanPass"),

    /**
     * 港澳通行证
     */
    HK_AND_MACAU_PASS("HKAndMacauPass"),

    /**
     * 回乡证
     */
    HOMETOWN_PERMIT("HometownPermit");

    CertificateTypeEnum(String desc) {
        this.desc = desc;
    }

    private String desc;

    public String getDesc() {
        return desc;
    }

    public static CertificateTypeEnum of(String desc) {
        if (StringUtil.isEmpty(desc)) {
            return null;
        }

        return Arrays.stream(CertificateTypeEnum.values())
                .filter(e -> StringUtil.equalsIgnoreCase(e.getDesc(), desc))
                .findFirst()
                .orElse(null);
    }
}
