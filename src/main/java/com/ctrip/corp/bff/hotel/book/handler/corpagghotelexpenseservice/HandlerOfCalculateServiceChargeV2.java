package com.ctrip.corp.bff.hotel.book.handler.corpagghotelexpenseservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.model.CalculateServiceChargeV2RequestType;
import com.ctrip.model.CalculateServiceChargeV2ResponseType;
import com.ctrip.model.CorpAggHotelExpenseServiceClient;
import org.springframework.stereotype.Component;

/**
 * @Author: chenchuang
 * @Date: 2024/8/22 16:26
 * @Description:计算服务费
 */
@Component
public class HandlerOfCalculateServiceChargeV2 extends AbstractHandlerOfSOA<CalculateServiceChargeV2RequestType,
        CalculateServiceChargeV2ResponseType, CorpAggHotelExpenseServiceClient> {

    @Override
    protected String getMethodName() {
        return "calculateServiceChargeV2";
    }
}
