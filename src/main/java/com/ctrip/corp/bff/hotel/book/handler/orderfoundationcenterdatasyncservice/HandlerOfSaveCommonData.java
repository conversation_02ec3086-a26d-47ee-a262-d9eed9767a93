package com.ctrip.corp.bff.hotel.book.handler.orderfoundationcenterdatasyncservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.soa._20183.OrderFoundationCenterDataSyncServiceClient;
import com.ctrip.soa._20183.SaveCommonDataRequestType;
import com.ctrip.soa._20183.SaveCommonDataResponseType;
import com.ctrip.soa._20184.CheckHotelAuthExtensionRequestType;
import com.ctrip.soa._20184.CheckHotelAuthExtensionResponseType;
import com.ctrip.soa._20184.OrderFoundationCenterAuthorizationServiceClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 保存订单数据
 * @Date 2024/8/20 19:05
 * @Version 1.0
 */
@Component
public class HandlerOfSaveCommonData extends
        AbstractHandlerOfSOA<SaveCommonDataRequestType, SaveCommonDataResponseType, OrderFoundationCenterDataSyncServiceClient> {

    @Override
    protected String getMethodName() {
        return "saveCommonData";
    }
}
