package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response;

import com.ctrip.corp.bff.framework.hotel.common.qconfig.QconfigOfInitConfig;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.FollowApprovalResult;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.shark.currencytemplate.CurrencyDisplayUtil;
import com.ctrip.corp.bff.framework.template.common.shark.entity.CurrencyDisplayInfo;
import com.ctrip.corp.bff.framework.template.common.shark.entity.CurrencyProductLineEnum;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.common.utils.date.DateUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.AmountInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalFlowReuseInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalFlowReuseInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ResourceNameContent;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ReuseOrderInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ReuseResultDetail;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ReuseResultInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.TripInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.common.constant.SharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.ApprovalFlowReuseFailCodeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.util.MathUtils;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfApprovalFlowReuse;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.ApprovalFlowReuse;
import com.ctrip.corp.bff.hotel.book.contract.ContinueInfo;
import com.ctrip.corp.bff.hotel.book.contract.HotelReuseOrderInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType;
import com.ctrip.corp.bff.hotel.book.contract.ReuseOrderCityInfo;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.corp.foundation.common.enums.LanguageLocaleEnum;
import com.ctrip.corp.foundation.translation.currency.util.currencystringsplit.CurrencyStringSplitInfo;
import com.ctrip.corp.hotel.book.query.entity.CityBaseInfoEntity;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType;
import com.ctrip.ibu.platform.shark.sdk.api.L10n;
import com.ctrip.soa._20184.CheckHotelAuthExtensionResponseType;
import com.ctrip.soa._20184.QueryHotelAuthExtensionResponseType;
import com.ctrip.soa._21234.SearchTripBasicInfoResponseType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.HotelAreaInfoType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.HotelInfoType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderDetailInfoType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderDetailResponseType;
import com.ctrip.soa.corp.order.orderindexextservice.v1.GetOrderFoundationDataResponseType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description 蓝色空间
 * 非智能：check后无论是否可沿用都需要提示
 * 智能：有推荐单&可延用则提示，无则不提示
 * <p>
 * 国内站：
 * APP/PC
 * 非智能：check后无论是否可沿用都需要提示
 * 智能：query后有推荐单&可延用则提示，有推荐单&不可延用&不可延用code在范围内则提示，无则不提示
 * offline
 * 人工勾选：check后无论是否可沿用都需要提示
 * 智能：query后有推荐单&可延用则提示，有推荐单&不可延用&不可延用code在范围内则提示，无则不提示
 * 智能勾选：query后无论是否有推荐单据都需要提示
 * @Date 2025/6/26 14:19
 * @Version 1.0
 */
@Component public class MapperOfApprovalFlowReuseResponse
    extends AbstractMapper<Tuple1<WrapperOfApprovalFlowReuse>, Tuple2<Boolean, OrderCreateResponseType>> {
    // 延用状态-可沿用
    private static final String REUSE = "REUSE";
    // 延用状态-不可沿用
    private static final String NOT_REUSE = "NOT_REUSE";
    // 延用状态-修改预订信息后可沿用
    private static final String MODIFY_REUSE = "MODIFY_REUSE";

    // 沿用类型-定后非智能沿用
    private static final String RE_BOOK_REUSE = "RE_BOOK_REUSE";
    // 沿用类型-智能推荐沿用
    private static final String AI_BOOK_REUSE = "AI_BOOK_REUSE";
    // 沿用类型-人工沿用
    private static final String ARTIFICIAL_REUSE = "ARTIFICIAL_REUSE";

    private static final String CITY_NAME = "CITY_NAME";
    private static final String CHECKIN = "CHECKIN";
    private static final String CHECKOUT = "CHECKOUT";
    private static final String PASSENGER = "PASSENGER";
    private static final String ROOM_QUANTITY = "ROOM_QUANTITY";
    private static final String POLICY_NAME = "POLICY_NAME";
    private static final String STAR = "STAR";
    private static final String HOTEL_NAME = "HOTEL_NAME";
    private static final String ORDER_AMOUNT = "ORDER_AMOUNT";
    private static final List<String> COMPARE_WITH_REUSE_ORDER =
        List.of(ApprovalFlowReuseFailCodeEnum.CITY_ID_DIFFERENT.getCode(),
            ApprovalFlowReuseFailCodeEnum.CHECKIN_TIME_DIFFERENT.getCode(),
            ApprovalFlowReuseFailCodeEnum.CHECKOUT_TIME_DIFFERENT.getCode(),
            ApprovalFlowReuseFailCodeEnum.CHECKIN_PERSON_DIFFERENT.getCode(),
            ApprovalFlowReuseFailCodeEnum.ROOM_COUNT_DIFFERENT.getCode(),
            ApprovalFlowReuseFailCodeEnum.POLICY_UID_DIFFERENT.getCode(),
            ApprovalFlowReuseFailCodeEnum.STAR_LEVEL_ABOVE_ORIGINAL.getCode(),
            ApprovalFlowReuseFailCodeEnum.STAR_LEVEL_BELOW_ORIGINAL.getCode(),
            ApprovalFlowReuseFailCodeEnum.DIFFERENT_HOTEL.getCode(),
            ApprovalFlowReuseFailCodeEnum.PRICE_HIGHER_THAN_ORIGINAL.getCode());

    @Override protected Tuple2<Boolean, OrderCreateResponseType> convert(Tuple1<WrapperOfApprovalFlowReuse> tuple) {
        WrapperOfApprovalFlowReuse wrapperOfApprovalFlowReuse = tuple.getT1();
        QueryHotelAuthExtensionResponseType queryHotelAuthExtensionResponseType =
            wrapperOfApprovalFlowReuse.getQueryHotelAuthExtensionResponseType();
        CheckHotelAuthExtensionResponseType checkHotelAuthExtensionResponseType =
            wrapperOfApprovalFlowReuse.getCheckHotelAuthExtensionResponseType();
        OrderCreateRequestType orderCreateRequestType = wrapperOfApprovalFlowReuse.getOrderCreateRequestType();
        OrderCreateToken orderCreateToken = wrapperOfApprovalFlowReuse.getOrderCreateToken();
        SearchTripBasicInfoResponseType searchTripBasicInfoResponseTypeOfApprovalFlowReuse =
            wrapperOfApprovalFlowReuse.getSearchTripBasicInfoResponseTypeOfApprovalFlowReuse();
        Map<String, StrategyInfo> strategyInfoMap = wrapperOfApprovalFlowReuse.getStrategyInfoMap();
        GetOrderFoundationDataResponseType getOrderFoundationDataResponseType =
            wrapperOfApprovalFlowReuse.getGetOrderFoundationDataResponseType();
        OrderDetailResponseType orderDetailResponseTypeOfApprovalFlowReuse =
            wrapperOfApprovalFlowReuse.getOrderDetailResponseTypeOfApprovalFlowReuse();
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = wrapperOfApprovalFlowReuse.getCheckAvailInfo();
        GetCityBaseInfoResponseType getCityBaseInfoResponseType =
            wrapperOfApprovalFlowReuse.getGetCityBaseInfoResponseType();
        GetCityBaseInfoResponseType getCityBaseInfoResponseTypeOfApprovalFlowReuse =
            wrapperOfApprovalFlowReuse.getGetCityBaseInfoResponseTypeOfApprovalFlowReuse();
        OrderDetailInfoType orderDetailInfoTypeOfApprovalFlowReuse =
            OrderCreateProcessorOfUtil.buildOrderDetailInfoType(orderCreateRequestType, strategyInfoMap,
                getOrderFoundationDataResponseType, checkHotelAuthExtensionResponseType,
                orderDetailResponseTypeOfApprovalFlowReuse, queryHotelAuthExtensionResponseType);
        CityBaseInfoEntity cityBaseInfoEntityOfApprovalFlowReuse =
            buildCityBaseInfoEntityOfApprovalFlowReuse(getCityBaseInfoResponseTypeOfApprovalFlowReuse,
                orderDetailInfoTypeOfApprovalFlowReuse);
        CityBaseInfoEntity cityBaseInfoEntity =
            buildCityBaseInfoEntityOfNewBook(orderCreateRequestType, getCityBaseInfoResponseType);
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig =
            wrapperOfApprovalFlowReuse.getQconfigOfCertificateInitConfig();
        GetCorpUserInfoResponseType policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse =
            wrapperOfApprovalFlowReuse.getPolicyGetCorpUserInfoResponseTypeOfApprovalFlowReuse();
        GetCorpUserInfoResponseType policyGetCorpUserInfoResponseType =
            wrapperOfApprovalFlowReuse.getPolicyGetCorpUserInfoResponseType();
        WrapperOfAccount.AccountInfo accountInfo = wrapperOfApprovalFlowReuse.getAccountInfo();
        if (orderCreateToken.containsContinueType(ContinueTypeConst.APPROVAL_FLOW_REUSE)) {
            return Tuple2.of(false, null);
        }
        if (NOT_REUSE.equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getContinueInfo()).map(ContinueInfo::getContinueCode)
                .orElse(null))) {
            return Tuple2.of(false, null);
        }
        // 非智能
        if (OrderCreateProcessorOfUtil.needCheckHotelAuthExtension(orderCreateRequestType, strategyInfoMap,
            getOrderFoundationDataResponseType, orderCreateToken)) {
            return buildApprovalFlowReuseResponseByCheck(checkHotelAuthExtensionResponseType, orderCreateRequestType,
                getOrderFoundationDataResponseType, strategyInfoMap, orderCreateToken,
                searchTripBasicInfoResponseTypeOfApprovalFlowReuse, orderDetailInfoTypeOfApprovalFlowReuse,
                cityBaseInfoEntityOfApprovalFlowReuse, cityBaseInfoEntity, checkAvailInfo,
                qconfigOfCertificateInitConfig, policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse,
                policyGetCorpUserInfoResponseType, accountInfo);
        }
        // 智能
        return buildApprovalFlowReuseResponseByQuery(queryHotelAuthExtensionResponseType, orderCreateRequestType,
            getOrderFoundationDataResponseType, strategyInfoMap, orderCreateToken,
            searchTripBasicInfoResponseTypeOfApprovalFlowReuse, orderDetailInfoTypeOfApprovalFlowReuse,
            cityBaseInfoEntityOfApprovalFlowReuse, cityBaseInfoEntity, checkAvailInfo, qconfigOfCertificateInitConfig,
            policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse, policyGetCorpUserInfoResponseType);
    }

    @Override protected ParamCheckResult check(Tuple1<WrapperOfApprovalFlowReuse> tuple) {
        // 非智能延用二次提交，校验是否还支持, 不支持拦截报错
        WrapperOfApprovalFlowReuse wrapperOfApprovalFlowReuse = tuple.getT1();
        OrderCreateRequestType orderCreateRequestType = wrapperOfApprovalFlowReuse.getOrderCreateRequestType();
        GetOrderFoundationDataResponseType getOrderFoundationDataResponseType =
            wrapperOfApprovalFlowReuse.getGetOrderFoundationDataResponseType();
        Map<String, StrategyInfo> strategyInfoMap = wrapperOfApprovalFlowReuse.getStrategyInfoMap();
        OrderCreateToken orderCreateToken = wrapperOfApprovalFlowReuse.getOrderCreateToken();
        CheckHotelAuthExtensionResponseType checkHotelAuthExtensionResponseType =
            wrapperOfApprovalFlowReuse.getCheckHotelAuthExtensionResponseType();
        // 智能二次提交再次校验
        if (!orderCreateToken.containsContinueType(ContinueTypeConst.APPROVAL_FLOW_REUSE)) {
            return null;
        }
        if (!OrderCreateProcessorOfUtil.needCheckHotelAuthExtensionOfAiContinue(orderCreateRequestType, strategyInfoMap,
            getOrderFoundationDataResponseType, orderCreateToken)) {
            return null;
        }
        // 智能推荐二次提交校验通过
        if (BooleanUtil.isTrue(Optional.ofNullable(checkHotelAuthExtensionResponseType)
            .map(CheckHotelAuthExtensionResponseType::getContinueAuth).orElse(false))) {
            return null;
        }
        throw BusinessExceptionBuilder.createAlertException(
            OrderCreateErrorEnum.APPROVAL_FLOW_REUSE_AI_CONTINUE_CHECK_ERROR,
            OrderCreateErrorEnum.APPROVAL_FLOW_REUSE_AI_CONTINUE_CHECK_ERROR.getErrorMessage());
    }

    private Tuple2<Boolean, OrderCreateResponseType> buildApprovalFlowReuseResponseByQuery(
        QueryHotelAuthExtensionResponseType queryHotelAuthExtensionResponseType,
        OrderCreateRequestType orderCreateRequestType,
        GetOrderFoundationDataResponseType getOrderFoundationDataResponseType,
        Map<String, StrategyInfo> strategyInfoMap, OrderCreateToken orderCreateToken,
        SearchTripBasicInfoResponseType searchTripBasicInfoResponseTypeOfApprovalFlowReuse,
        OrderDetailInfoType orderDetailInfoTypeOfApprovalFlowReuse,
        CityBaseInfoEntity cityBaseInfoEntityOfApprovalFlowReuse, CityBaseInfoEntity cityBaseInfoEntity,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        GetCorpUserInfoResponseType policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse,
        GetCorpUserInfoResponseType policyGetCorpUserInfoResponseType) {
        if (queryHotelAuthExtensionResponseType == null) {
            return Tuple2.of(false, null);
        }
        // 可沿用
        if (BooleanUtil.isTrue(queryHotelAuthExtensionResponseType.getContinueAuth())
            && queryHotelAuthExtensionResponseType.getOriginalOrderInfo() != null
            && TemplateNumberUtil.isNotZeroAndNull(
            queryHotelAuthExtensionResponseType.getOriginalOrderInfo().getOriginalOrderId())) {
            return buildApprovalFlowReuseResponseByQueryReuse(queryHotelAuthExtensionResponseType,
                orderCreateRequestType, orderCreateToken, searchTripBasicInfoResponseTypeOfApprovalFlowReuse,
                orderDetailInfoTypeOfApprovalFlowReuse, cityBaseInfoEntityOfApprovalFlowReuse);
        }
        // 不可沿用，但有推荐单据，且校验原因都是可修改的内容，都是需要中断流程的code（国内站支持此策略），二次提醒客户
        if (!BooleanUtil.isTrue(queryHotelAuthExtensionResponseType.getContinueAuth())
            && queryHotelAuthExtensionResponseType.getOriginalOrderInfo() != null
            && TemplateNumberUtil.isNotZeroAndNull(
            queryHotelAuthExtensionResponseType.getOriginalOrderInfo().getOriginalOrderId())) {
            if (StrategyOfBookingInitUtil.approvalFlowReuseAiModify(strategyInfoMap) && buildNeedFollowFailRemindSingle(
                queryHotelAuthExtensionResponseType.getReasonList())) {
                return buildApprovalFlowReuseResponseByQueryModify(queryHotelAuthExtensionResponseType,
                    orderCreateRequestType, orderCreateToken, searchTripBasicInfoResponseTypeOfApprovalFlowReuse,
                    orderDetailInfoTypeOfApprovalFlowReuse, cityBaseInfoEntityOfApprovalFlowReuse, cityBaseInfoEntity,
                    checkAvailInfo, qconfigOfCertificateInitConfig,
                    policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse, policyGetCorpUserInfoResponseType, strategyInfoMap);
            }
        }
        // 不可沿用，但页面勾选了智能沿用（国内站offline），无推荐结果也需二次提醒操作员
        if (BooleanUtil.parseStr(true).equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getApprovalFlowReuseInput())
                .map(ApprovalFlowReuseInput::getAiReuse).orElse(null))) {
            return buildApprovalFlowReuseResponseByQueryNoReuse(queryHotelAuthExtensionResponseType,
                orderCreateRequestType, orderCreateToken, searchTripBasicInfoResponseTypeOfApprovalFlowReuse,
                orderDetailInfoTypeOfApprovalFlowReuse, cityBaseInfoEntityOfApprovalFlowReuse, cityBaseInfoEntity,
                checkAvailInfo, qconfigOfCertificateInitConfig, policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse,
                policyGetCorpUserInfoResponseType, strategyInfoMap);
        }
        // 不可沿用，执行后续流程
        return Tuple2.of(false, new OrderCreateResponseType());
    }

    /**
     * 单订单智能不可延用时是否需要提示
     *
     * @param reasonList
     * @return
     */
    private boolean buildNeedFollowFailRemindSingle(List<String> reasonList) {
        if (CollectionUtil.isEmpty(reasonList)) {
            return false;
        }
        if (reasonList.stream().anyMatch(QconfigOfInitConfig::cannotFlowAndNoNeedEndReasonCodes)) {
            return false;
        }
        return true;
    }

    private Tuple2<Boolean, OrderCreateResponseType> buildApprovalFlowReuseResponseByQueryNoReuse(
        QueryHotelAuthExtensionResponseType queryHotelAuthExtensionResponseType,
        OrderCreateRequestType orderCreateRequestType, OrderCreateToken orderCreateToken,
        SearchTripBasicInfoResponseType searchTripBasicInfoResponseTypeOfApprovalFlowReuse,
        OrderDetailInfoType orderDetailInfoTypeOfApprovalFlowReuse,
        CityBaseInfoEntity cityBaseInfoEntityOfApprovalFlowReuse, CityBaseInfoEntity cityBaseInfoEntity,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        GetCorpUserInfoResponseType policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse,
        GetCorpUserInfoResponseType policyGetCorpUserInfoResponseType,
        Map<String, StrategyInfo> strategyInfoMap) {
        OrderCreateResponseType orderCreateResponseType = new OrderCreateResponseType();
        orderCreateResponseType.setOrderCreateToken(
            TokenParseUtil.generateToken(getOrderCreateTokenModify(orderCreateToken), OrderCreateToken.class));
        orderCreateResponseType.setApprovalFlowReuse(buildApprovalFlowReuseByQueryNoReuse(orderCreateRequestType,
            searchTripBasicInfoResponseTypeOfApprovalFlowReuse, orderDetailInfoTypeOfApprovalFlowReuse,
            queryHotelAuthExtensionResponseType, cityBaseInfoEntityOfApprovalFlowReuse, cityBaseInfoEntity,
            checkAvailInfo, qconfigOfCertificateInitConfig, policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse,
            policyGetCorpUserInfoResponseType, strategyInfoMap));
        return Tuple2.of(true, orderCreateResponseType);
    }

    protected ApprovalFlowReuse buildApprovalFlowReuseByQueryNoReuse(OrderCreateRequestType orderCreateRequestType,
        SearchTripBasicInfoResponseType searchTripBasicInfoResponseTypeOfApprovalFlowReuse,
        OrderDetailInfoType orderDetailInfoTypeOfApprovalFlowReuse,
        QueryHotelAuthExtensionResponseType queryHotelAuthExtensionResponseType,
        CityBaseInfoEntity cityBaseInfoEntityOfApprovalFlowReuse, CityBaseInfoEntity cityBaseInfoEntity,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        GetCorpUserInfoResponseType policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse,
        GetCorpUserInfoResponseType policyGetCorpUserInfoResponseType,
        Map<String, StrategyInfo> strategyInfoMap) {
        ApprovalFlowReuse approvalFlowReuse = new ApprovalFlowReuse();
        ApprovalFlowReuseInfo approvalFlowReuseInfo = new ApprovalFlowReuseInfo();
        approvalFlowReuseInfo.setReuseStatus(NOT_REUSE);
        approvalFlowReuseInfo.setReuseType(AI_BOOK_REUSE);
        approvalFlowReuseInfo.setReuseStatusTip(
            BFFSharkUtil.getSharkValue(SharkKeyConstant.REUSE_STATUS_TIP_NOT_REUSE));
        approvalFlowReuseInfo.setTripInfo(builTripInfo(searchTripBasicInfoResponseTypeOfApprovalFlowReuse));
        approvalFlowReuseInfo.setReuseOrderInfo(buildReuseOrderInfo(orderDetailInfoTypeOfApprovalFlowReuse));
        approvalFlowReuseInfo.setReuseResultInfos(
            buildReuseResultInfos(queryHotelAuthExtensionResponseType.getReasonList(), orderCreateRequestType,
                cityBaseInfoEntityOfApprovalFlowReuse, cityBaseInfoEntity, orderDetailInfoTypeOfApprovalFlowReuse,
                checkAvailInfo, qconfigOfCertificateInitConfig, policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse,
                policyGetCorpUserInfoResponseType, strategyInfoMap));
        approvalFlowReuseInfo.setReuseResultDetails(reuseResultDetails(
            Optional.ofNullable(queryHotelAuthExtensionResponseType)
                .map(QueryHotelAuthExtensionResponseType::getReasonList).orElse(null)));
        approvalFlowReuse.setApprovalFlowReuseInfo(approvalFlowReuseInfo);
        approvalFlowReuse.setHotelReuseOrderInfo(
            buildHotelReuseOrderInfo(orderCreateRequestType, orderDetailInfoTypeOfApprovalFlowReuse,
                cityBaseInfoEntityOfApprovalFlowReuse));
        return approvalFlowReuse;
    }

    /**
     * 本次弹框给客户后，如果客户选择不沿用，支持重刷base获取行程模块
     * 非智能--进入填写页，置灰（蓝色空间）/隐藏（国内站）了行程模块，check完之后，支持重新放开
     *
     * @param accountInfo
     * @param orderCreateRequestType
     * @return
     */
    protected String buildSupportReloadTripModule(WrapperOfAccount.AccountInfo accountInfo,
        OrderCreateRequestType orderCreateRequestType) {
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())) {
            return BooleanUtil.parseStr(false);
        }
        if (!accountInfo.isPackageEnabled()) {
            return BooleanUtil.parseStr(false);
        }
        return BooleanUtil.parseStr(true);
    }

    private Tuple2<Boolean, OrderCreateResponseType> buildApprovalFlowReuseResponseByQueryModify(
        QueryHotelAuthExtensionResponseType queryHotelAuthExtensionResponseType,
        OrderCreateRequestType orderCreateRequestType, OrderCreateToken orderCreateToken,
        SearchTripBasicInfoResponseType searchTripBasicInfoResponseTypeOfApprovalFlowReuse,
        OrderDetailInfoType orderDetailInfoTypeOfApprovalFlowReuse,
        CityBaseInfoEntity cityBaseInfoEntityOfApprovalFlowReuse, CityBaseInfoEntity cityBaseInfoEntity,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        GetCorpUserInfoResponseType policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse,
        GetCorpUserInfoResponseType policyGetCorpUserInfoResponseType,
        Map<String, StrategyInfo> strategyInfoMap) {
        OrderCreateResponseType orderCreateResponseType = new OrderCreateResponseType();
        orderCreateResponseType.setOrderCreateToken(
            TokenParseUtil.generateToken(getOrderCreateTokenModify(orderCreateToken), OrderCreateToken.class));
        orderCreateResponseType.setApprovalFlowReuse(buildApprovalFlowReuseByQueryModify(orderCreateRequestType,
            searchTripBasicInfoResponseTypeOfApprovalFlowReuse, orderDetailInfoTypeOfApprovalFlowReuse,
            queryHotelAuthExtensionResponseType, cityBaseInfoEntityOfApprovalFlowReuse, cityBaseInfoEntity,
            checkAvailInfo, qconfigOfCertificateInitConfig, policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse,
            policyGetCorpUserInfoResponseType, strategyInfoMap));
        return Tuple2.of(true, orderCreateResponseType);
    }

    protected OrderCreateToken getOrderCreateTokenModify(OrderCreateToken orderCreateToken) {
        orderCreateToken.addContinueTypes(ContinueTypeConst.APPROVAL_FLOW_REUSE);
        FollowApprovalResult followApprovalResult = new FollowApprovalResult();
        followApprovalResult.setCanFollowApproval(BooleanUtil.parseStr(false));
        return orderCreateToken;
    }

    protected ApprovalFlowReuse buildApprovalFlowReuseByQueryModify(OrderCreateRequestType orderCreateRequestType,
        SearchTripBasicInfoResponseType searchTripBasicInfoResponseTypeOfApprovalFlowReuse,
        OrderDetailInfoType orderDetailInfoTypeOfApprovalFlowReuse,
        QueryHotelAuthExtensionResponseType queryHotelAuthExtensionResponseType,
        CityBaseInfoEntity cityBaseInfoEntityOfApprovalFlowReuse, CityBaseInfoEntity cityBaseInfoEntity,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        GetCorpUserInfoResponseType policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse,
        GetCorpUserInfoResponseType policyGetCorpUserInfoResponseType,
        Map<String, StrategyInfo> strategyInfoMap) {
        ApprovalFlowReuse approvalFlowReuse = new ApprovalFlowReuse();
        ApprovalFlowReuseInfo approvalFlowReuseInfo = new ApprovalFlowReuseInfo();
        approvalFlowReuseInfo.setReuseStatus(MODIFY_REUSE);
        approvalFlowReuseInfo.setReuseType(AI_BOOK_REUSE);
        approvalFlowReuseInfo.setReuseStatusTip(
            BFFSharkUtil.getSharkValue(SharkKeyConstant.REUSE_STATUS_TIP_MODIFY_REUSE));
        approvalFlowReuseInfo.setTripInfo(builTripInfo(searchTripBasicInfoResponseTypeOfApprovalFlowReuse));
        approvalFlowReuseInfo.setReuseOrderInfo(buildReuseOrderInfo(orderDetailInfoTypeOfApprovalFlowReuse));
        approvalFlowReuseInfo.setReuseResultInfos(
            buildReuseResultInfos(queryHotelAuthExtensionResponseType.getReasonList(), orderCreateRequestType,
                cityBaseInfoEntityOfApprovalFlowReuse, cityBaseInfoEntity, orderDetailInfoTypeOfApprovalFlowReuse,
                checkAvailInfo, qconfigOfCertificateInitConfig, policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse,
                policyGetCorpUserInfoResponseType, strategyInfoMap));
        approvalFlowReuseInfo.setReuseResultDetails(
            reuseResultDetails(queryHotelAuthExtensionResponseType.getReasonList()));
        approvalFlowReuse.setApprovalFlowReuseInfo(approvalFlowReuseInfo);
        approvalFlowReuse.setHotelReuseOrderInfo(
            buildHotelReuseOrderInfo(orderCreateRequestType, orderDetailInfoTypeOfApprovalFlowReuse,
                cityBaseInfoEntityOfApprovalFlowReuse));
        return approvalFlowReuse;
    }

    private Tuple2<Boolean, OrderCreateResponseType> buildApprovalFlowReuseResponseByQueryReuse(
        QueryHotelAuthExtensionResponseType queryHotelAuthExtensionResponseType,
        OrderCreateRequestType orderCreateRequestType, OrderCreateToken orderCreateToken,
        SearchTripBasicInfoResponseType searchTripBasicInfoResponseTypeOfApprovalFlowReuse,
        OrderDetailInfoType orderDetailInfoTypeOfApprovalFlowReuse,
        CityBaseInfoEntity cityBaseInfoEntityOfApprovalFlowReuse) {
        OrderCreateResponseType orderCreateResponseType = new OrderCreateResponseType();
        orderCreateResponseType.setOrderCreateToken(TokenParseUtil.generateToken(
            getOrderCreateTokenReuseByCheck(orderCreateToken, queryHotelAuthExtensionResponseType),
            OrderCreateToken.class));
        orderCreateResponseType.setApprovalFlowReuse(buildApprovalFlowReuseByQueryReuse(orderCreateRequestType,
            searchTripBasicInfoResponseTypeOfApprovalFlowReuse, orderDetailInfoTypeOfApprovalFlowReuse,
            cityBaseInfoEntityOfApprovalFlowReuse));
        return Tuple2.of(true, orderCreateResponseType);
    }

    protected ApprovalFlowReuse buildApprovalFlowReuseByQueryReuse(OrderCreateRequestType orderCreateRequestType,
        SearchTripBasicInfoResponseType searchTripBasicInfoResponseTypeOfApprovalFlowReuse,
        OrderDetailInfoType orderDetailInfoTypeOfApprovalFlowReuse,
        CityBaseInfoEntity cityBaseInfoEntityOfApprovalFlowReuse) {
        ApprovalFlowReuse approvalFlowReuse = new ApprovalFlowReuse();
        ApprovalFlowReuseInfo approvalFlowReuseInfo = new ApprovalFlowReuseInfo();
        approvalFlowReuseInfo.setReuseStatus(REUSE);
        approvalFlowReuseInfo.setReuseType(AI_BOOK_REUSE);
        approvalFlowReuseInfo.setReuseStatusTip(BFFSharkUtil.getSharkValue(SharkKeyConstant.REUSE_STATUS_TIP_REUSE));
        approvalFlowReuseInfo.setTripInfo(builTripInfo(searchTripBasicInfoResponseTypeOfApprovalFlowReuse));
        approvalFlowReuseInfo.setReuseOrderInfo(buildReuseOrderInfo(orderDetailInfoTypeOfApprovalFlowReuse));
        approvalFlowReuse.setApprovalFlowReuseInfo(approvalFlowReuseInfo);
        approvalFlowReuse.setHotelReuseOrderInfo(
            buildHotelReuseOrderInfo(orderCreateRequestType, orderDetailInfoTypeOfApprovalFlowReuse,
                cityBaseInfoEntityOfApprovalFlowReuse));
        return approvalFlowReuse;
    }

    protected OrderCreateToken getOrderCreateTokenReuseByCheck(OrderCreateToken orderCreateToken,
        QueryHotelAuthExtensionResponseType queryHotelAuthExtensionResponseType) {
        orderCreateToken.addContinueTypes(ContinueTypeConst.APPROVAL_FLOW_REUSE);
        FollowApprovalResult followApprovalResult = new FollowApprovalResult();
        followApprovalResult.setCanFollowApproval(BooleanUtil.parseStr(true));
        if (TemplateNumberUtil.isNotZeroAndNull(
            queryHotelAuthExtensionResponseType.getOriginalOrderInfo().getTripId())) {
            followApprovalResult.setTripId(
                String.valueOf(queryHotelAuthExtensionResponseType.getOriginalOrderInfo().getTripId()));
        }
        followApprovalResult.setFollowOrderNo(
            String.valueOf(queryHotelAuthExtensionResponseType.getOriginalOrderInfo().getOriginalOrderId()));
        orderCreateToken.setFollowApprovalResult(followApprovalResult);
        return orderCreateToken;
    }

    protected Tuple2<Boolean, OrderCreateResponseType> buildApprovalFlowReuseResponseByCheck(
        CheckHotelAuthExtensionResponseType checkHotelAuthExtensionResponseType,
        OrderCreateRequestType orderCreateRequestType,
        GetOrderFoundationDataResponseType getOrderFoundationDataResponseType,
        Map<String, StrategyInfo> strategyInfoMap, OrderCreateToken orderCreateToken,
        SearchTripBasicInfoResponseType searchTripBasicInfoResponseTypeOfApprovalFlowReuse,
        OrderDetailInfoType orderDetailInfoTypeOfApprovalFlowReuse,
        CityBaseInfoEntity cityBaseInfoEntityOfApprovalFlowReuse, CityBaseInfoEntity cityBaseInfoEntity,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        GetCorpUserInfoResponseType policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse,
        GetCorpUserInfoResponseType policyGetCorpUserInfoResponseType, WrapperOfAccount.AccountInfo accountInfo) {
        if (checkHotelAuthExtensionResponseType == null) {
            return Tuple2.of(false, null);
        }
        OrderCreateResponseType orderCreateResponseType = new OrderCreateResponseType();
        orderCreateResponseType.setOrderCreateToken(TokenParseUtil.generateToken(
            getOrderCreateTokenReuseByCheck(checkHotelAuthExtensionResponseType, orderCreateRequestType,
                getOrderFoundationDataResponseType, strategyInfoMap, orderCreateToken), OrderCreateToken.class));
        orderCreateResponseType.setApprovalFlowReuse(
            buildApprovalFlowReuse(checkHotelAuthExtensionResponseType, orderCreateRequestType, strategyInfoMap,
                getOrderFoundationDataResponseType, searchTripBasicInfoResponseTypeOfApprovalFlowReuse,
                orderDetailInfoTypeOfApprovalFlowReuse, cityBaseInfoEntityOfApprovalFlowReuse, cityBaseInfoEntity,
                checkAvailInfo, qconfigOfCertificateInitConfig, policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse,
                policyGetCorpUserInfoResponseType, accountInfo));
        return Tuple2.of(true, orderCreateResponseType);
    }

    protected ApprovalFlowReuse buildApprovalFlowReuse(
        CheckHotelAuthExtensionResponseType checkHotelAuthExtensionResponseType,
        OrderCreateRequestType orderCreateRequestType, Map<String, StrategyInfo> strategyInfoMap,
        GetOrderFoundationDataResponseType getOrderFoundationDataResponseType,
        SearchTripBasicInfoResponseType searchTripBasicInfoResponseTypeOfApprovalFlowReuse,
        OrderDetailInfoType orderDetailInfoTypeOfApprovalFlowReuse,
        CityBaseInfoEntity cityBaseInfoEntityOfApprovalFlowReuse, CityBaseInfoEntity cityBaseInfoEntity,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        GetCorpUserInfoResponseType policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse,
        GetCorpUserInfoResponseType policyGetCorpUserInfoResponseType, WrapperOfAccount.AccountInfo accountInfo) {
        // 支持沿用
        if (BooleanUtil.isTrue(checkHotelAuthExtensionResponseType.getContinueAuth())) {
            return buildApprovalFlowReuseOfReuse(orderCreateRequestType, strategyInfoMap,
                getOrderFoundationDataResponseType, searchTripBasicInfoResponseTypeOfApprovalFlowReuse,
                orderDetailInfoTypeOfApprovalFlowReuse, accountInfo, cityBaseInfoEntityOfApprovalFlowReuse);
        }
        // 不支持沿用-重新修改信息后能沿用
        if (CollectionUtil.containsAny(checkHotelAuthExtensionResponseType.getReasonList(), COMPARE_WITH_REUSE_ORDER)) {
            return buildApprovalFlowReuseOfModifyReuse(checkHotelAuthExtensionResponseType, orderCreateRequestType,
                strategyInfoMap, getOrderFoundationDataResponseType, searchTripBasicInfoResponseTypeOfApprovalFlowReuse,
                orderDetailInfoTypeOfApprovalFlowReuse, cityBaseInfoEntityOfApprovalFlowReuse, cityBaseInfoEntity,
                checkAvailInfo, qconfigOfCertificateInitConfig, policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse,
                policyGetCorpUserInfoResponseType, accountInfo);
        }
        // 不支持沿用-无可重新修改的原因
        return buildApprovalFlowReuseOfNoReuse(checkHotelAuthExtensionResponseType, orderCreateRequestType,
            strategyInfoMap, getOrderFoundationDataResponseType, searchTripBasicInfoResponseTypeOfApprovalFlowReuse,
            orderDetailInfoTypeOfApprovalFlowReuse, cityBaseInfoEntityOfApprovalFlowReuse, accountInfo);
    }

    protected ApprovalFlowReuse buildApprovalFlowReuseOfReuse(OrderCreateRequestType orderCreateRequestType,
        Map<String, StrategyInfo> strategyInfoMap,
        GetOrderFoundationDataResponseType getOrderFoundationDataResponseType,
        SearchTripBasicInfoResponseType searchTripBasicInfoResponseTypeOfApprovalFlowReuse,
        OrderDetailInfoType orderDetailInfoTypeOfApprovalFlowReuse, WrapperOfAccount.AccountInfo accountInfo,
        CityBaseInfoEntity cityBaseInfoEntityOfApprovalFlowReuse) {
        ApprovalFlowReuse approvalFlowReuse = new ApprovalFlowReuse();
        ApprovalFlowReuseInfo approvalFlowReuseInfo = new ApprovalFlowReuseInfo();
        approvalFlowReuseInfo.setReuseStatus(REUSE);
        approvalFlowReuseInfo.setReuseType(
            buildReuseType(orderCreateRequestType, strategyInfoMap, getOrderFoundationDataResponseType));
        approvalFlowReuseInfo.setReuseStatusTip(BFFSharkUtil.getSharkValue(SharkKeyConstant.REUSE_STATUS_TIP_REUSE));
        approvalFlowReuseInfo.setTripInfo(builTripInfo(searchTripBasicInfoResponseTypeOfApprovalFlowReuse));
        approvalFlowReuseInfo.setReuseOrderInfo(buildReuseOrderInfo(orderDetailInfoTypeOfApprovalFlowReuse));
        approvalFlowReuseInfo.setSupportReloadTripModule(
            buildSupportReloadTripModule(accountInfo, orderCreateRequestType));
        approvalFlowReuse.setApprovalFlowReuseInfo(approvalFlowReuseInfo);
        approvalFlowReuse.setHotelReuseOrderInfo(
            buildHotelReuseOrderInfo(orderCreateRequestType, orderDetailInfoTypeOfApprovalFlowReuse,
                cityBaseInfoEntityOfApprovalFlowReuse));
        return approvalFlowReuse;
    }

    protected ApprovalFlowReuse buildApprovalFlowReuseOfNoReuse(
        CheckHotelAuthExtensionResponseType checkHotelAuthExtensionResponseType,
        OrderCreateRequestType orderCreateRequestType, Map<String, StrategyInfo> strategyInfoMap,
        GetOrderFoundationDataResponseType getOrderFoundationDataResponseType,
        SearchTripBasicInfoResponseType searchTripBasicInfoResponseTypeOfApprovalFlowReuse,
        OrderDetailInfoType orderDetailInfoTypeOfApprovalFlowReuse,
        CityBaseInfoEntity cityBaseInfoEntityOfApprovalFlowReuse, WrapperOfAccount.AccountInfo accountInfo) {
        ApprovalFlowReuse approvalFlowReuse = new ApprovalFlowReuse();
        ApprovalFlowReuseInfo approvalFlowReuseInfo = new ApprovalFlowReuseInfo();
        approvalFlowReuseInfo.setReuseStatus(NOT_REUSE);
        approvalFlowReuseInfo.setReuseType(
            buildReuseType(orderCreateRequestType, strategyInfoMap, getOrderFoundationDataResponseType));
        approvalFlowReuseInfo.setReuseStatusTip(
            BFFSharkUtil.getSharkValue(SharkKeyConstant.REUSE_STATUS_TIP_NOT_REUSE));
        approvalFlowReuseInfo.setTripInfo(builTripInfo(searchTripBasicInfoResponseTypeOfApprovalFlowReuse));
        approvalFlowReuseInfo.setReuseOrderInfo(buildReuseOrderInfo(orderDetailInfoTypeOfApprovalFlowReuse));
        approvalFlowReuseInfo.setReuseResultDetails(
            reuseResultDetails(checkHotelAuthExtensionResponseType.getReasonList()));
        approvalFlowReuseInfo.setSupportReloadTripModule(
            buildSupportReloadTripModule(accountInfo, orderCreateRequestType));
        approvalFlowReuse.setApprovalFlowReuseInfo(approvalFlowReuseInfo);
        approvalFlowReuse.setHotelReuseOrderInfo(
            buildHotelReuseOrderInfo(orderCreateRequestType, orderDetailInfoTypeOfApprovalFlowReuse,
                cityBaseInfoEntityOfApprovalFlowReuse));
        return approvalFlowReuse;
    }

    protected String buildReuseType(OrderCreateRequestType orderCreateRequestType,
        Map<String, StrategyInfo> strategyInfoMap,
        GetOrderFoundationDataResponseType getOrderFoundationDataResponseType) {
        if (TemplateNumberUtil.isNotZeroAndNull(
            OrderCreateProcessorOfUtil.buildApprovalReuseReBookOrderId(orderCreateRequestType, strategyInfoMap))) {
            return RE_BOOK_REUSE;
        }
        if (TemplateNumberUtil.isNotZeroAndNull(
            OrderCreateProcessorOfUtil.buildArtificialReuseNoOrderId(orderCreateRequestType,
                getOrderFoundationDataResponseType)) || TemplateNumberUtil.isNotZeroAndNull(
            OrderCreateProcessorOfUtil.buildArtificialReuseNoTripId(orderCreateRequestType,
                getOrderFoundationDataResponseType))) {
            return ARTIFICIAL_REUSE;
        }
        return null;
    }

    protected ApprovalFlowReuse buildApprovalFlowReuseOfModifyReuse(
        CheckHotelAuthExtensionResponseType checkHotelAuthExtensionResponseType,
        OrderCreateRequestType orderCreateRequestType, Map<String, StrategyInfo> strategyInfoMap,
        GetOrderFoundationDataResponseType getOrderFoundationDataResponseType,
        SearchTripBasicInfoResponseType searchTripBasicInfoResponseTypeOfApprovalFlowReuse,
        OrderDetailInfoType orderDetailInfoTypeOfApprovalFlowReuse,
        CityBaseInfoEntity cityBaseInfoEntityOfApprovalFlowReuse, CityBaseInfoEntity cityBaseInfoEntity,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        GetCorpUserInfoResponseType policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse,
        GetCorpUserInfoResponseType policyGetCorpUserInfoResponseType, WrapperOfAccount.AccountInfo accountInfo) {
        ApprovalFlowReuse approvalFlowReuse = new ApprovalFlowReuse();
        ApprovalFlowReuseInfo approvalFlowReuseInfo = new ApprovalFlowReuseInfo();
        approvalFlowReuseInfo.setReuseStatus(MODIFY_REUSE);
        approvalFlowReuseInfo.setReuseType(
            buildReuseType(orderCreateRequestType, strategyInfoMap, getOrderFoundationDataResponseType));
        approvalFlowReuseInfo.setReuseStatusTip(
            BFFSharkUtil.getSharkValue(SharkKeyConstant.REUSE_STATUS_TIP_MODIFY_REUSE));
        approvalFlowReuseInfo.setTripInfo(builTripInfo(searchTripBasicInfoResponseTypeOfApprovalFlowReuse));
        approvalFlowReuseInfo.setReuseOrderInfo(buildReuseOrderInfo(orderDetailInfoTypeOfApprovalFlowReuse));
        approvalFlowReuseInfo.setReuseResultInfos(
            buildReuseResultInfos(checkHotelAuthExtensionResponseType.getReasonList(), orderCreateRequestType,
                cityBaseInfoEntityOfApprovalFlowReuse, cityBaseInfoEntity, orderDetailInfoTypeOfApprovalFlowReuse,
                checkAvailInfo, qconfigOfCertificateInitConfig, policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse,
                policyGetCorpUserInfoResponseType, strategyInfoMap));
        approvalFlowReuseInfo.setSupportReloadTripModule(
            buildSupportReloadTripModule(accountInfo, orderCreateRequestType));
        approvalFlowReuseInfo.setReuseResultDetails(
            reuseResultDetails(checkHotelAuthExtensionResponseType.getReasonList()));
        approvalFlowReuse.setApprovalFlowReuseInfo(approvalFlowReuseInfo);
        approvalFlowReuse.setHotelReuseOrderInfo(
            buildHotelReuseOrderInfo(orderCreateRequestType, orderDetailInfoTypeOfApprovalFlowReuse,
                cityBaseInfoEntityOfApprovalFlowReuse));
        return approvalFlowReuse;
    }

    protected List<ReuseResultDetail> reuseResultDetails(List<String> reasonList) {
        if (CollectionUtil.isEmpty(reasonList)) {
            return null;
        }
        List<ReuseResultDetail> reuseResultDetails = new ArrayList<>();
        reasonList.stream().forEach(reason -> {
            if (StringUtil.isBlank(reason)) {
                return;
            }
            if (COMPARE_WITH_REUSE_ORDER.contains(reason)) {
                return;
            }
            ApprovalFlowReuseFailCodeEnum approvalFlowReuseFailCodeEnum =
                ApprovalFlowReuseFailCodeEnum.getApprovalFlowReuseFailCodeEnumByCode(reason);
            if (approvalFlowReuseFailCodeEnum == null) {
                return;
            }
            ReuseResultDetail reuseResultDetail = new ReuseResultDetail();
            reuseResultDetail.setCode(approvalFlowReuseFailCodeEnum.name());
            reuseResultDetail.setCodeDesc(BFFSharkUtil.getSharkValue(
                StringUtil.indexedFormat(SharkKeyConstant.REUSE_STATUS_TIP_REUSE_RESULT_DETAIL,
                    approvalFlowReuseFailCodeEnum.name())));
            reuseResultDetails.add(reuseResultDetail);
        });
        return reuseResultDetails;
    }

    protected List<ReuseResultInfo> buildReuseResultInfos(List<String> reasonList,
        OrderCreateRequestType orderCreateRequestType, CityBaseInfoEntity cityBaseInfoEntityOfApprovalFlowReuse,
        CityBaseInfoEntity cityBaseInfoEntity, OrderDetailInfoType orderDetailInfoTypeOfApprovalFlowReuse,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        GetCorpUserInfoResponseType policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse,
        GetCorpUserInfoResponseType policyGetCorpUserInfoResponseType,
        Map<String, StrategyInfo> strategyInfoMap) {
        if (CollectionUtil.isEmpty(reasonList)) {
            return null;
        }
        if (orderDetailInfoTypeOfApprovalFlowReuse == null) {
            return null;
        }
        List<ReuseResultInfo> reuseResultInfos = new ArrayList<>();
        reasonList.stream().forEach(reason -> {
            if (StringUtil.isBlank(reason)) {
                return;
            }
            if (!COMPARE_WITH_REUSE_ORDER.contains(reason)) {
                return;
            }

            if (ApprovalFlowReuseFailCodeEnum.CITY_ID_DIFFERENT.getCode().equalsIgnoreCase(reason)) {
                addReuseResultInfoOfCity(cityBaseInfoEntityOfApprovalFlowReuse, cityBaseInfoEntity, reuseResultInfos,
                    ApprovalFlowReuseFailCodeEnum.CITY_ID_DIFFERENT.name());
                return;
            }
            if (ApprovalFlowReuseFailCodeEnum.CHECKIN_TIME_DIFFERENT.getCode().equalsIgnoreCase(reason)) {
                addReuseResultInfoOfCheckIn(orderCreateRequestType, reuseResultInfos,
                    orderDetailInfoTypeOfApprovalFlowReuse,
                    ApprovalFlowReuseFailCodeEnum.CHECKIN_TIME_DIFFERENT.name());
                return;
            }
            if (ApprovalFlowReuseFailCodeEnum.CHECKOUT_TIME_DIFFERENT.getCode().equalsIgnoreCase(reason)) {
                addReuseResultInfoOfCheckOut(orderCreateRequestType, reuseResultInfos,
                    orderDetailInfoTypeOfApprovalFlowReuse,
                    ApprovalFlowReuseFailCodeEnum.CHECKOUT_TIME_DIFFERENT.name());
                return;
            }
            if (ApprovalFlowReuseFailCodeEnum.CHECKIN_PERSON_DIFFERENT.getCode().equalsIgnoreCase(reason)) {
                addReuseResultInfoOfPassenger(orderCreateRequestType, reuseResultInfos,
                    orderDetailInfoTypeOfApprovalFlowReuse, checkAvailInfo, qconfigOfCertificateInitConfig,
                    ApprovalFlowReuseFailCodeEnum.CHECKIN_PERSON_DIFFERENT.name(), strategyInfoMap);
                return;
            }
            if (ApprovalFlowReuseFailCodeEnum.ROOM_COUNT_DIFFERENT.getCode().equalsIgnoreCase(reason)) {
                addReuseResultInfoOfRoomQuantity(orderCreateRequestType, reuseResultInfos,
                    orderDetailInfoTypeOfApprovalFlowReuse, ApprovalFlowReuseFailCodeEnum.ROOM_COUNT_DIFFERENT.name());
                return;
            }
            if (ApprovalFlowReuseFailCodeEnum.POLICY_UID_DIFFERENT.getCode().equalsIgnoreCase(reason)) {
                addReuseResultInfoOfPolicyName(orderCreateRequestType, reuseResultInfos,
                    policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse, policyGetCorpUserInfoResponseType,
                    ApprovalFlowReuseFailCodeEnum.POLICY_UID_DIFFERENT.name());
                return;
            }
            if (ApprovalFlowReuseFailCodeEnum.STAR_LEVEL_ABOVE_ORIGINAL.getCode().equalsIgnoreCase(reason)) {
                addReuseResultInfoOfStar(orderDetailInfoTypeOfApprovalFlowReuse, reuseResultInfos, checkAvailInfo,
                    ApprovalFlowReuseFailCodeEnum.STAR_LEVEL_ABOVE_ORIGINAL.name());
                return;
            }
            if (ApprovalFlowReuseFailCodeEnum.STAR_LEVEL_BELOW_ORIGINAL.getCode().equalsIgnoreCase(reason)) {
                addReuseResultInfoOfStar(orderDetailInfoTypeOfApprovalFlowReuse, reuseResultInfos, checkAvailInfo,
                    ApprovalFlowReuseFailCodeEnum.STAR_LEVEL_BELOW_ORIGINAL.name());
                return;
            }
            if (ApprovalFlowReuseFailCodeEnum.DIFFERENT_HOTEL.getCode().equalsIgnoreCase(reason)) {
                addReuseResultInfoOfHotelName(orderDetailInfoTypeOfApprovalFlowReuse, reuseResultInfos, checkAvailInfo,
                    ApprovalFlowReuseFailCodeEnum.DIFFERENT_HOTEL.name());
                return;
            }
            if (ApprovalFlowReuseFailCodeEnum.PRICE_HIGHER_THAN_ORIGINAL.getCode().equalsIgnoreCase(reason)) {
                addReuseResultInfoOfOrderAmount(orderDetailInfoTypeOfApprovalFlowReuse, reuseResultInfos,
                    checkAvailInfo, ApprovalFlowReuseFailCodeEnum.PRICE_HIGHER_THAN_ORIGINAL.name());
                return;
            }
        });
        return reuseResultInfos;
    }

    protected void addReuseResultInfoOfOrderAmount(OrderDetailInfoType orderDetailInfoTypeOfApprovalFlowReuse,
        List<ReuseResultInfo> reuseResultInfos, WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        String checkResultType) {
        if (orderDetailInfoTypeOfApprovalFlowReuse.getPaymentInfo() == null) {
            return;
        }
        String customAmountNewBook = checkAvailInfo.getCustomCurrency();
        BigDecimal customCurrencyNewBook = checkAvailInfo.getRoomAmount();
        BigDecimal settlementOrderAmountOfReuseOrder =
            orderDetailInfoTypeOfApprovalFlowReuse.getPaymentInfo().getSettlementOrderAmount();
        String settlementCurrencyOfReuseOrder =
            orderDetailInfoTypeOfApprovalFlowReuse.getPaymentInfo().getSettlementCurrency();
        if (StringUtil.isBlank(customAmountNewBook) || StringUtil.isBlank(settlementCurrencyOfReuseOrder)
            || customCurrencyNewBook == null || settlementOrderAmountOfReuseOrder == null) {
            return;
        }
        ReuseResultInfo reuseResultInfo = new ReuseResultInfo();
        reuseResultInfo.setOrderValue(formatAmount(settlementOrderAmountOfReuseOrder, settlementCurrencyOfReuseOrder));
        reuseResultInfo.setNewBookValue(formatAmount(customCurrencyNewBook, customAmountNewBook));
        reuseResultInfo.setCheckResultType(checkResultType);
        reuseResultInfo.setCheckResultTitle(BFFSharkUtil.getSharkValue(buildCheckResultTitleSharkKey(ORDER_AMOUNT)));
        reuseResultInfos.add(reuseResultInfo);
    }

    protected String formatAmount(BigDecimal amount, String currency) {
        if (StringUtil.isBlank(currency) || amount == null) {
            return null;
        }
        CurrencyDisplayInfo currencyDisplayInfo = new CurrencyDisplayInfo();
        currencyDisplayInfo.setNumber(amount);
        currencyDisplayInfo.setCurrency(currency);
        currencyDisplayInfo.setProductLine(CurrencyProductLineEnum.HOTEL);
        return CurrencyDisplayUtil.currencyString(currencyDisplayInfo);
    }

    // 只转换金额,但是金额和币种均需要传
    public static String convertAmount(BigDecimal amount, String currency) {
        if (amount == null || currency == null) {
            return null;
        }
        CurrencyDisplayInfo currencyDisplayInfo = new CurrencyDisplayInfo();
        currencyDisplayInfo.setNumber(amount);
        currencyDisplayInfo.setCurrency(currency);
        currencyDisplayInfo.setProductLine(CurrencyProductLineEnum.HOTEL);
        CurrencyStringSplitInfo currencyStringSplitInfo =
            CurrencyDisplayUtil.currencyStringSplitInfo(currencyDisplayInfo);

        return currencyStringSplitInfo.getAmount();
    }

    // 只转换币种,但是金额和币种均需要传
    public static String convertCurrency(BigDecimal amount, String currency) {
        if (amount == null || currency == null) {
            return null;
        }
        CurrencyDisplayInfo currencyDisplayInfo = new CurrencyDisplayInfo();
        currencyDisplayInfo.setNumber(amount);
        currencyDisplayInfo.setCurrency(currency);
        currencyDisplayInfo.setProductLine(CurrencyProductLineEnum.HOTEL);
        CurrencyStringSplitInfo currencyStringSplitInfo =
            CurrencyDisplayUtil.currencyStringSplitInfo(currencyDisplayInfo);

        return currencyStringSplitInfo.getCurrency();
    }

    protected void addReuseResultInfoOfHotelName(OrderDetailInfoType orderDetailInfoTypeOfApprovalFlowReuse,
        List<ReuseResultInfo> reuseResultInfos, WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        String checkResultType) {
        String newBook = Optional.ofNullable(checkAvailInfo.getHotelName().getTextGB())
            .orElse(checkAvailInfo.getHotelName().getTextEn());
        String reuseOrder =
            Optional.ofNullable(orderDetailInfoTypeOfApprovalFlowReuse.getHotelInfo()).map(HotelInfoType::getHotelName)
                .orElse(Optional.ofNullable(orderDetailInfoTypeOfApprovalFlowReuse.getHotelInfo())
                    .map(HotelInfoType::getHotelNameEn).orElse(null));
        if (StringUtil.isBlank(newBook) || StringUtil.isBlank(reuseOrder)) {
            return;
        }
        ReuseResultInfo reuseResultInfo = new ReuseResultInfo();
        reuseResultInfo.setOrderValue(reuseOrder);
        reuseResultInfo.setNewBookValue(newBook);
        reuseResultInfo.setCheckResultType(checkResultType);
        reuseResultInfo.setCheckResultTitle(BFFSharkUtil.getSharkValue(buildCheckResultTitleSharkKey(HOTEL_NAME)));
        reuseResultInfos.add(reuseResultInfo);
    }

    protected void addReuseResultInfoOfStar(OrderDetailInfoType orderDetailInfoTypeOfApprovalFlowReuse,
        List<ReuseResultInfo> reuseResultInfos, WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        String checkResultType) {
        int starOfNewBook = checkAvailInfo.getStar();
        int starOfReuseOrder =
            Optional.ofNullable(orderDetailInfoTypeOfApprovalFlowReuse.getHotelInfo()).map(HotelInfoType::getStar)
                .orElse(0);
        ReuseResultInfo reuseResultInfo = new ReuseResultInfo();
        reuseResultInfo.setOrderValue(String.valueOf(starOfReuseOrder));
        reuseResultInfo.setNewBookValue(String.valueOf(starOfNewBook));
        reuseResultInfo.setCheckResultType(checkResultType);
        reuseResultInfo.setCheckResultTitle(BFFSharkUtil.getSharkValue(buildCheckResultTitleSharkKey(STAR)));
        reuseResultInfos.add(reuseResultInfo);
    }

    protected void addReuseResultInfoOfPolicyName(OrderCreateRequestType orderCreateRequestType,
        List<ReuseResultInfo> reuseResultInfos,
        GetCorpUserInfoResponseType policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse,
        GetCorpUserInfoResponseType policyGetCorpUserInfoResponseType, String checkResultType) {
        if (policyGetCorpUserInfoResponseType == null || policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse == null) {
            return;
        }
        String policyNameOfNewBook = buildPolicyName(orderCreateRequestType, policyGetCorpUserInfoResponseType);
        String policyNameOfReuseOrder =
            buildPolicyName(orderCreateRequestType, policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse);
        if (StringUtil.isBlank(policyNameOfNewBook) || StringUtil.isBlank(policyNameOfReuseOrder)) {
            return;
        }
        ReuseResultInfo reuseResultInfo = new ReuseResultInfo();
        reuseResultInfo.setOrderValue(policyNameOfReuseOrder);
        reuseResultInfo.setNewBookValue(policyNameOfNewBook);
        reuseResultInfo.setCheckResultType(checkResultType);
        reuseResultInfo.setCheckResultTitle(BFFSharkUtil.getSharkValue(buildCheckResultTitleSharkKey(POLICY_NAME)));
        reuseResultInfos.add(reuseResultInfo);
    }

    protected String buildCheckResultTitleSharkKey(String checkResultType) {
        return StringUtil.indexedFormat(SharkKeyConstant.REUSE_STATUS_TIP_MODIFY_CHECK_RESULT_TYPE, checkResultType);
    }

    protected String buildPolicyName(OrderCreateRequestType orderCreateRequestType,
        GetCorpUserInfoResponseType policyGetCorpUserInfoResponseType) {
        if (LanguageLocaleEnum.ZH_CN.getLocaleString()
            .equalsIgnoreCase(orderCreateRequestType.getIntegrationSoaRequestType().getLanguage())
            && StringUtil.isNotBlank(policyGetCorpUserInfoResponseType.getName())) {
            return policyGetCorpUserInfoResponseType.getName();
        }
        if (StringUtil.isNotBlank(policyGetCorpUserInfoResponseType.getNameENFirstName()) && StringUtil.isNotBlank(
            policyGetCorpUserInfoResponseType.getNameENLastName())) {
            return String.join("/", policyGetCorpUserInfoResponseType.getNameENFirstName(),
                policyGetCorpUserInfoResponseType.getNameENLastName());
        }
        return null;
    }

    protected void addReuseResultInfoOfRoomQuantity(OrderCreateRequestType orderCreateRequestType,
        List<ReuseResultInfo> reuseResultInfos, OrderDetailInfoType orderDetailInfoTypeOfApprovalFlowReuse,
        String checkResultType) {
        if (orderDetailInfoTypeOfApprovalFlowReuse == null
            || orderDetailInfoTypeOfApprovalFlowReuse.getOrderBasicInfo() == null) {
            return;
        }
        Integer checkOutOfNewBook = orderCreateRequestType.getHotelBookInput().getRoomQuantity();
        Integer checkOutOfReuseOrder = orderDetailInfoTypeOfApprovalFlowReuse.getOrderBasicInfo().getRoomQuantity();
        ReuseResultInfo reuseResultInfo = new ReuseResultInfo();
        reuseResultInfo.setOrderValue(String.valueOf(checkOutOfReuseOrder));
        reuseResultInfo.setNewBookValue(String.valueOf(checkOutOfNewBook));
        reuseResultInfo.setCheckResultType(checkResultType);
        reuseResultInfo.setCheckResultTitle(BFFSharkUtil.getSharkValue(buildCheckResultTitleSharkKey(ROOM_QUANTITY)));
        reuseResultInfos.add(reuseResultInfo);
    }

    protected void addReuseResultInfoOfPassenger(OrderCreateRequestType orderCreateRequestType,
        List<ReuseResultInfo> reuseResultInfos, OrderDetailInfoType orderDetailInfoTypeOfApprovalFlowReuse,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig, String checkResultType,
        Map<String, StrategyInfo> strategyInfoMap) {
        if (orderDetailInfoTypeOfApprovalFlowReuse == null || CollectionUtil.isEmpty(
            orderDetailInfoTypeOfApprovalFlowReuse.getOrderClientList())) {
            return;
        }
        List<String> useNamesOfApprovalFlowReuse = new ArrayList<>();
        orderDetailInfoTypeOfApprovalFlowReuse.getOrderClientList().stream().forEach(orderClientInfoType -> {
            if (orderClientInfoType == null || StringUtil.isBlank(orderClientInfoType.getClientName())) {
                return;
            }
            useNamesOfApprovalFlowReuse.add(orderClientInfoType.getClientName());
        });
        List<String> useNames = new ArrayList<>();
        orderCreateRequestType.getHotelBookPassengerInputs().stream().forEach(hotelBookPassengerInput -> {
            useNames.add(OrderCreateProcessorOfUtil.getUseName(hotelBookPassengerInput,
                orderCreateRequestType.getCityInput().getCityId(), checkAvailInfo, qconfigOfCertificateInitConfig, strategyInfoMap));
        });
        if (CollectionUtil.isEmpty(useNames) || CollectionUtil.isEmpty(useNamesOfApprovalFlowReuse)) {
            return;
        }
        ReuseResultInfo reuseResultInfo = new ReuseResultInfo();
        reuseResultInfo.setOrderValue(String.join("、", useNamesOfApprovalFlowReuse));
        reuseResultInfo.setNewBookValue(String.join("、", useNames));
        reuseResultInfo.setCheckResultType(checkResultType);
        reuseResultInfo.setCheckResultTitle(BFFSharkUtil.getSharkValue(buildCheckResultTitleSharkKey(PASSENGER)));
        reuseResultInfos.add(reuseResultInfo);
    }

    protected void addReuseResultInfoOfCheckOut(OrderCreateRequestType orderCreateRequestType,
        List<ReuseResultInfo> reuseResultInfos, OrderDetailInfoType orderDetailInfoTypeOfApprovalFlowReuse,
        String checkResultType) {

        String checkOutOfNewBook = L10n.dateTimeFormatter(
            Optional.ofNullable(orderCreateRequestType.getIntegrationSoaRequestType())
                .map(IntegrationSoaRequestType::getLanguage).orElse(null)).ymdShortString(
            DateUtil.parseDate(orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo().getCheckOut(),
                DateUtil.YYYY_MM_DD));
        String checkOutOfReuseOrder = buildCheckOut(orderCreateRequestType.getIntegrationSoaRequestType(),
            orderDetailInfoTypeOfApprovalFlowReuse);
        if (StringUtil.isBlank(checkOutOfNewBook) || StringUtil.isBlank(checkOutOfReuseOrder)) {
            return;
        }
        ReuseResultInfo reuseResultInfo = new ReuseResultInfo();
        reuseResultInfo.setOrderValue(checkOutOfReuseOrder);
        reuseResultInfo.setNewBookValue(checkOutOfNewBook);
        reuseResultInfo.setCheckResultType(checkResultType);
        reuseResultInfo.setCheckResultTitle(BFFSharkUtil.getSharkValue(buildCheckResultTitleSharkKey(CHECKOUT)));
        reuseResultInfos.add(reuseResultInfo);
    }

    protected void addReuseResultInfoOfCheckIn(OrderCreateRequestType orderCreateRequestType,
        List<ReuseResultInfo> reuseResultInfos, OrderDetailInfoType orderDetailInfoTypeOfApprovalFlowReuse,
        String checkResultType) {
        String checkinOfNewBook = L10n.dateTimeFormatter(
            Optional.ofNullable(orderCreateRequestType.getIntegrationSoaRequestType())
                .map(IntegrationSoaRequestType::getLanguage).orElse(null)).ymdShortString(
            DateUtil.parseDate(orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo().getCheckIn(),
                DateUtil.YYYY_MM_DD));
        String checkinOfReuseOrder =
            buildCheckIn(orderCreateRequestType.getIntegrationSoaRequestType(), orderDetailInfoTypeOfApprovalFlowReuse);
        if (StringUtil.isBlank(checkinOfNewBook) || StringUtil.isBlank(checkinOfReuseOrder)) {
            return;
        }
        ReuseResultInfo reuseResultInfo = new ReuseResultInfo();
        reuseResultInfo.setOrderValue(checkinOfReuseOrder);
        reuseResultInfo.setNewBookValue(checkinOfNewBook);
        reuseResultInfo.setCheckResultType(checkResultType);
        reuseResultInfo.setCheckResultTitle(BFFSharkUtil.getSharkValue(buildCheckResultTitleSharkKey(CHECKIN)));
        reuseResultInfos.add(reuseResultInfo);
    }

    protected void addReuseResultInfoOfCity(CityBaseInfoEntity cityBaseInfoEntityOfApprovalFlowReuse,
        CityBaseInfoEntity cityBaseInfoEntity, List<ReuseResultInfo> reuseResultInfos, String checkResultType) {
        if (cityBaseInfoEntity == null || cityBaseInfoEntityOfApprovalFlowReuse == null) {
            return;
        }
        if (StringUtil.isBlank(cityBaseInfoEntityOfApprovalFlowReuse.getCityName()) || StringUtil.isBlank(
            cityBaseInfoEntity.getCityName())) {
            return;
        }
        ReuseResultInfo reuseResultInfo = new ReuseResultInfo();
        reuseResultInfo.setOrderValue(cityBaseInfoEntityOfApprovalFlowReuse.getCityName());
        reuseResultInfo.setNewBookValue(cityBaseInfoEntity.getCityName());
        reuseResultInfo.setCheckResultType(checkResultType);
        reuseResultInfo.setCheckResultTitle(BFFSharkUtil.getSharkValue(buildCheckResultTitleSharkKey(CITY_NAME)));
        reuseResultInfos.add(reuseResultInfo);
    }

    protected CityBaseInfoEntity buildCityBaseInfoEntityOfNewBook(OrderCreateRequestType orderCreateRequestType,
        GetCityBaseInfoResponseType getCityBaseInfoResponseType) {
        if (getCityBaseInfoResponseType == null || CollectionUtil.isEmpty(
            getCityBaseInfoResponseType.getCityBaseInfo())) {
            return null;
        }
        if (TemplateNumberUtil.isZeroOrNull(
            Optional.ofNullable(orderCreateRequestType).map(OrderCreateRequestType::getCityInput)
                .map(CityInput::getCityId).orElse(null))) {
            return null;
        }
        return getCityBaseInfoResponseType.getCityBaseInfo().stream().filter(
            cityBaseInfoEntity -> cityBaseInfoEntity != null && TemplateNumberUtil.isNotZeroAndNull(
                cityBaseInfoEntity.getCityId()) && cityBaseInfoEntity.getCityId()
                .equals(orderCreateRequestType.getCityInput().getCityId())).toList().stream().findFirst().orElse(null);
    }

    protected CityBaseInfoEntity buildCityBaseInfoEntityOfApprovalFlowReuse(
        GetCityBaseInfoResponseType getCityBaseInfoResponseTypeOfApprovalFlowReuse,
        OrderDetailInfoType orderDetailInfoType) {
        if (getCityBaseInfoResponseTypeOfApprovalFlowReuse == null || CollectionUtil.isEmpty(
            getCityBaseInfoResponseTypeOfApprovalFlowReuse.getCityBaseInfo())) {
            return null;
        }
        if (TemplateNumberUtil.isZeroOrNull(
            Optional.ofNullable(orderDetailInfoType).map(OrderDetailInfoType::getHotelInfo)
                .map(HotelInfoType::getHotelAreaInfo).map(HotelAreaInfoType::getCityId).orElse(null))) {
            return null;
        }
        return getCityBaseInfoResponseTypeOfApprovalFlowReuse.getCityBaseInfo().stream().filter(
                cityBaseInfoEntity -> cityBaseInfoEntity != null && TemplateNumberUtil.isNotZeroAndNull(
                    cityBaseInfoEntity.getCityId()) && cityBaseInfoEntity.getCityId()
                    .equals(orderDetailInfoType.getHotelInfo().getHotelAreaInfo().getCityId())).toList().stream()
            .findFirst().orElse(null);
    }

    protected HotelReuseOrderInfo buildHotelReuseOrderInfo(OrderCreateRequestType orderCreateRequestType,
        OrderDetailInfoType orderDetailInfoTypeOfApprovalFlowReuse,
        CityBaseInfoEntity cityBaseInfoEntityOfApprovalFlowReuse) {
        if (orderDetailInfoTypeOfApprovalFlowReuse == null
            || orderDetailInfoTypeOfApprovalFlowReuse.getRoomInfo() == null) {
            return null;
        }
        HotelReuseOrderInfo hotelReuseOrderInfo = new HotelReuseOrderInfo();
        hotelReuseOrderInfo.setCheckIn(buildCheckIn(orderCreateRequestType.getIntegrationSoaRequestType(),
            orderDetailInfoTypeOfApprovalFlowReuse));
        hotelReuseOrderInfo.setCheckOut(buildCheckOut(orderCreateRequestType.getIntegrationSoaRequestType(),
            orderDetailInfoTypeOfApprovalFlowReuse));
        hotelReuseOrderInfo.setShowResourceName(buildResourceNameContent(orderDetailInfoTypeOfApprovalFlowReuse,
            orderCreateRequestType.getIntegrationSoaRequestType()));
        hotelReuseOrderInfo.setReuseOrderCityInfo(buildReuseOrderCityInfo(cityBaseInfoEntityOfApprovalFlowReuse));
        return hotelReuseOrderInfo;
    }

    protected ReuseOrderCityInfo buildReuseOrderCityInfo(CityBaseInfoEntity cityBaseInfoEntityOfApprovalFlowReuse) {
        if (StringUtil.isBlank(
            Optional.ofNullable(cityBaseInfoEntityOfApprovalFlowReuse).map(CityBaseInfoEntity::getCityName)
                .orElse(null))) {
            return null;
        }
        ReuseOrderCityInfo reuseOrderCityInfo = new ReuseOrderCityInfo();
        reuseOrderCityInfo.setLocaleCityName(cityBaseInfoEntityOfApprovalFlowReuse.getCityName());
        return reuseOrderCityInfo;
    }

    private ResourceNameContent buildResourceNameContent(OrderDetailInfoType orderDetailInfoType,
        IntegrationSoaRequestType integrationSoaRequestType) {
        if (orderDetailInfoType == null || orderDetailInfoType.getHotelInfo() == null) {
            return null;
        }
        ResourceNameContent resourceNameContent = new ResourceNameContent();
        resourceNameContent.setLocale(integrationSoaRequestType.getLanguage());
        resourceNameContent.setName(orderDetailInfoType.getHotelInfo().getHotelName());
        return resourceNameContent;
    }

    protected String buildCheckOut(IntegrationSoaRequestType integrationSoaRequestType,
        OrderDetailInfoType orderDetailInfoType) {
        if (orderDetailInfoType == null || orderDetailInfoType.getRoomInfo() == null) {
            return null;
        }
        if (StringUtil.isBlank(orderDetailInfoType.getRoomInfo().getDepartureDay())) {
            return null;
        }
        return L10n.dateTimeFormatter(
                Optional.ofNullable(integrationSoaRequestType).map(IntegrationSoaRequestType::getLanguage).orElse(null))
            .ymdShortString(
                DateUtil.parseDate(orderDetailInfoType.getRoomInfo().getDepartureDay(), DateUtil.YYYY_MM_DD_HH_mm_ss));
    }

    protected String buildCheckIn(IntegrationSoaRequestType integrationSoaRequestType,
        OrderDetailInfoType orderDetailInfoType) {
        if (orderDetailInfoType == null || orderDetailInfoType.getRoomInfo() == null) {
            return null;
        }
        if (StringUtil.isBlank(orderDetailInfoType.getRoomInfo().getArrivalDay())) {
            return null;
        }
        return L10n.dateTimeFormatter(
                Optional.ofNullable(integrationSoaRequestType).map(IntegrationSoaRequestType::getLanguage).orElse(null))
            .ymdShortString(
                DateUtil.parseDate(orderDetailInfoType.getRoomInfo().getArrivalDay(), DateUtil.YYYY_MM_DD_HH_mm_ss));
    }

    protected ReuseOrderInfo buildReuseOrderInfo(OrderDetailInfoType orderDetailInfoTypeOfApprovalFlowReuse) {
        if (orderDetailInfoTypeOfApprovalFlowReuse == null || TemplateNumberUtil.isZeroOrNull(
            orderDetailInfoTypeOfApprovalFlowReuse.getOrderId())) {
            return null;
        }
        ReuseOrderInfo reuseOrderInfo = new ReuseOrderInfo();
        reuseOrderInfo.setOrderId(String.valueOf(orderDetailInfoTypeOfApprovalFlowReuse.getOrderId()));
        if (orderDetailInfoTypeOfApprovalFlowReuse.getPaymentInfo() != null && StringUtil.isNotBlank(
            orderDetailInfoTypeOfApprovalFlowReuse.getPaymentInfo().getSettlementCurrency())
            && MathUtils.isGreaterThanZero(
            orderDetailInfoTypeOfApprovalFlowReuse.getPaymentInfo().getSettlementOrderAmount())) {
            AmountInfo amountInfo = new AmountInfo();
            amountInfo.setCurrency(
                convertCurrency(orderDetailInfoTypeOfApprovalFlowReuse.getPaymentInfo().getSettlementOrderAmount(),
                    orderDetailInfoTypeOfApprovalFlowReuse.getPaymentInfo().getSettlementCurrency()));
            amountInfo.setAmount(
                convertAmount(orderDetailInfoTypeOfApprovalFlowReuse.getPaymentInfo().getSettlementOrderAmount(),
                    orderDetailInfoTypeOfApprovalFlowReuse.getPaymentInfo().getSettlementCurrency()));
            reuseOrderInfo.setAmountInfo(amountInfo);
        }
        return reuseOrderInfo;
    }

    protected TripInfo builTripInfo(
        SearchTripBasicInfoResponseType searchTripBasicInfoResponseTypeOfApprovalFlowReuse) {
        if (searchTripBasicInfoResponseTypeOfApprovalFlowReuse == null
            || searchTripBasicInfoResponseTypeOfApprovalFlowReuse.getBasicInfo() == null) {
            return null;
        }
        if (TemplateNumberUtil.isZeroOrNull(
            searchTripBasicInfoResponseTypeOfApprovalFlowReuse.getBasicInfo().getTripId())) {
            return null;
        }
        TripInfo tripInfo = new TripInfo();
        tripInfo.setTripId(
            String.valueOf(searchTripBasicInfoResponseTypeOfApprovalFlowReuse.getBasicInfo().getTripId()));
        tripInfo.setTripName(searchTripBasicInfoResponseTypeOfApprovalFlowReuse.getBasicInfo().getTripName());
        return tripInfo;
    }

    protected OrderCreateToken getOrderCreateTokenReuseByCheck(
        CheckHotelAuthExtensionResponseType checkHotelAuthExtensionResponseType,
        OrderCreateRequestType orderCreateRequestType,
        GetOrderFoundationDataResponseType getOrderFoundationDataResponseType,
        Map<String, StrategyInfo> strategyInfoMap, OrderCreateToken orderCreateToken) {
        orderCreateToken.addContinueTypes(ContinueTypeConst.APPROVAL_FLOW_REUSE);
        orderCreateToken.setFollowApprovalResult(
            buildCanFollowApprovalResultByCheck(checkHotelAuthExtensionResponseType, orderCreateRequestType,
                getOrderFoundationDataResponseType, strategyInfoMap));
        return orderCreateToken;
    }

    protected FollowApprovalResult buildCanFollowApprovalResultByCheck(
        CheckHotelAuthExtensionResponseType checkHotelAuthExtensionResponseType,
        OrderCreateRequestType orderCreateRequestType,
        GetOrderFoundationDataResponseType getOrderFoundationDataResponseType,
        Map<String, StrategyInfo> strategyInfoMap) {
        FollowApprovalResult followApprovalResult = new FollowApprovalResult();
        followApprovalResult.setCanFollowApproval(BooleanUtil.parseStr(
            Optional.ofNullable(checkHotelAuthExtensionResponseType)
                .map(CheckHotelAuthExtensionResponseType::getContinueAuth).orElse(false)));
        // 重新预订原单号&沿用校验的行程号
        if (TemplateNumberUtil.isNotZeroAndNull(
            OrderCreateProcessorOfUtil.buildApprovalReuseReBookOrderId(orderCreateRequestType, strategyInfoMap))) {
            followApprovalResult.setFollowOrderNo(String.valueOf(
                OrderCreateProcessorOfUtil.buildApprovalReuseReBookOrderId(orderCreateRequestType, strategyInfoMap)));
            if (TemplateNumberUtil.isNotZeroAndNull(Optional.ofNullable(checkHotelAuthExtensionResponseType)
                .map(CheckHotelAuthExtensionResponseType::getTripId).orElse(null))) {
                followApprovalResult.setTripId(String.valueOf(checkHotelAuthExtensionResponseType.getTripId()));
            }
            return followApprovalResult;
        }
        // 人工沿用输入的订单号&沿用校验的行程号
        if (TemplateNumberUtil.isNotZeroAndNull(
            OrderCreateProcessorOfUtil.buildArtificialReuseNoOrderId(orderCreateRequestType,
                getOrderFoundationDataResponseType))) {
            followApprovalResult.setFollowOrderNo(String.valueOf(
                OrderCreateProcessorOfUtil.buildArtificialReuseNoOrderId(orderCreateRequestType,
                    getOrderFoundationDataResponseType)));
            if (TemplateNumberUtil.isNotZeroAndNull(Optional.ofNullable(checkHotelAuthExtensionResponseType)
                .map(CheckHotelAuthExtensionResponseType::getTripId).orElse(null))) {
                followApprovalResult.setTripId(String.valueOf(checkHotelAuthExtensionResponseType.getTripId()));
            }
            return followApprovalResult;
        }
        // 人工输入的行程号&沿用校验的订单号
        if (TemplateNumberUtil.isNotZeroAndNull(
            OrderCreateProcessorOfUtil.buildArtificialReuseNoTripId(orderCreateRequestType,
                getOrderFoundationDataResponseType))) {
            followApprovalResult.setTripId(String.valueOf(
                OrderCreateProcessorOfUtil.buildArtificialReuseNoTripId(orderCreateRequestType,
                    getOrderFoundationDataResponseType)));
            if (TemplateNumberUtil.isNotZeroAndNull(Optional.ofNullable(checkHotelAuthExtensionResponseType)
                .map(CheckHotelAuthExtensionResponseType::getOrderId).orElse(null))) {
                followApprovalResult.setTripId(String.valueOf(checkHotelAuthExtensionResponseType.getOrderId()));
            }
            return followApprovalResult;
        }
        return null;
    }
}
