package com.ctrip.corp.bff.hotel.book.common.enums;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;

import java.util.Arrays;

/**
 * @Author: chenchuang
 * @Date: 2024/9/27 21:16
 * @Description: 房间限制类型
 */
public enum RegistrantLimitTypeEnum {


    /**
     * 无需
     */
    NONE(new String[]{"All", "OrderOneOnly", "RoomOneOnly"}),

    /**
     * 按房间首人登记
     */
    ROOM(new String[]{"RoomOneLeast"}),

    /**
     * 按订单首人登记
     */
    ORDER(new String[]{"OrderOneLeast"})

    ;

    private final String[] codes;

    RegistrantLimitTypeEnum(String[] codes) {
        this.codes = codes;
    }

    public static RegistrantLimitTypeEnum value(String code) {
        if (StringUtil.isBlank(code)) {
            return NONE;
        }
        return Arrays.stream(RegistrantLimitTypeEnum.values())
                .filter(t-> Arrays.stream(t.getCodes()).anyMatch(c->StringUtil.equalsIgnoreCase(c, code)))
                .findFirst()
                .orElse(NONE);
    }

    public String[] getCodes() {
        return codes;
    }

}
