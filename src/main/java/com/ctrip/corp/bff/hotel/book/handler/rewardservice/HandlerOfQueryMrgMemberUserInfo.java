package com.ctrip.corp.bff.hotel.book.handler.rewardservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.hotel.order.rewardservice.contract.CtripMemberUserInfoResponse;
import com.ctrip.hotel.order.rewardservice.contract.QueryCtripMrgMemberUserInfoRequest;
import com.ctrip.hotel.order.rewardservice.contract.RewardServiceClient;
import org.springframework.stereotype.Component;

/**
 * @Author: chenchuang
 * @Date: 2024/8/22 16:26
 * @Description:获取集团会员信息
 */
@Component
public class HandlerOfQueryMrgMemberUserInfo extends AbstractHandlerOfSOA<QueryCtripMrgMemberUserInfoRequest,
        CtripMemberUserInfoResponse, RewardServiceClient> {

    @Override
    protected String getMethodName() {
        return "queryMrgMemberUserInfo";
    }
}
