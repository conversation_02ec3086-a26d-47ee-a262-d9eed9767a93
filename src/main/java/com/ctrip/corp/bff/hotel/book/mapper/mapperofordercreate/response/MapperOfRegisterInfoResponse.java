package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response;

import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil;
import com.ctrip.corp.bff.framework.hotel.entity.contract.ConfirmDetailExtend;
import com.ctrip.corp.bff.framework.hotel.entity.contract.ConfirmDetailInfo;
import com.ctrip.corp.bff.framework.hotel.entity.contract.ConfirmInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple8;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType;
import com.ctrip.corp.bff.hotel.book.contract.RegisterInputInfo;
import com.ctrip.corp.bff.hotel.book.contract.RegisterOutputInfo;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfRegisterConfig;
import com.ctrip.corp.foundation.common.util.Null;
import com.ctrip.model.RegisterResponseType;
import corp.user.service.corpUserInfoService.CorpUserHotelVipCard;
import corp.user.service.corpUserInfoService.GetCorpUserHotelVipCardResponseType;
import corp.user.service.group4j.accounts.BizModeBindRelationData;
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2025/3/14
 */
@Component
public class MapperOfRegisterInfoResponse extends AbstractMapper<Tuple8<RegisterInputInfo, GetCorpUserHotelVipCardResponseType,
        RegisterResponseType, QconfigOfRegisterConfig, OrderCreateToken, IntegrationSoaRequestType, WrapperOfCheckAvail.BaseCheckAvailInfo,
        QueryBizModeBindRelationResponseType>, Tuple2<Boolean, OrderCreateResponseType>> {

    /**
     * 重复注册code
     */
    private static final String DUPLICATE_REGISTRATION = "DUPLICATE_REGISTRATION";
    /**
     * 注册失败
     */
    private static final String REGISTRATION_FAIL = "REGISTRATION_FAIL";


    /**
     * agg重复注册code
     */
    private static final int REPEAT = 501;

    /**
     * agg注册失败code
     */
    private static final int FAIL = 500;

    private static final int SUCCESS = 200;
    @Override
    protected Tuple2<Boolean, OrderCreateResponseType> convert(Tuple8<RegisterInputInfo, GetCorpUserHotelVipCardResponseType,
            RegisterResponseType, QconfigOfRegisterConfig, OrderCreateToken, IntegrationSoaRequestType, WrapperOfCheckAvail.BaseCheckAvailInfo,
            QueryBizModeBindRelationResponseType> para) {
        RegisterInputInfo registerInputInfo = para.getT1();
        GetCorpUserHotelVipCardResponseType getCorpUserHotelVipCardResponseType = para.getT2();
        RegisterResponseType registerResponseType = para.getT3();
        QconfigOfRegisterConfig qconfigOfRegisterConfig = para.getT4();
        OrderCreateToken orderCreateToken = para.getT5();
        IntegrationSoaRequestType integrationSoaRequestType = para.getT6();
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = para.getT7();
        QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType = para.getT8();
        Integer groupId = Null.or(checkAvailInfo, WrapperOfCheckAvail.BaseCheckAvailInfo::getGroupId);
        // 非注册场景
        if (registerInputInfo == null) {
            return Tuple2.of(false, null);
        }
        String membershipNo = buildMembershipNo(getCorpUserHotelVipCardResponseType, integrationSoaRequestType, checkAvailInfo, queryBizModeBindRelationResponseType);
        // 已经有会员卡号
        if (StringUtil.isNotBlank(membershipNo)) {
            return Tuple2.of(true, buildRepeatRegisterResponse(membershipNo, orderCreateToken, groupId));
        }
        // 无结果-注册失败
        if (registerResponseType == null || registerResponseType.getRegisterStatusCode() == null) {
            return Tuple2.of(true, buildFailRegisterResponse(orderCreateToken, groupId));
        }
        // 注册失败
        if (registerResponseType.getRegisterStatusCode() != null
                && registerResponseType.getRegisterStatusCode().equals(FAIL)) {
            return Tuple2.of(true, buildFailRegisterResponse(orderCreateToken, groupId));
        }
        // 重复注册
        if (registerResponseType.getRegisterStatusCode() != null
                && registerResponseType.getRegisterStatusCode().equals(REPEAT)
                && StringUtil.isNotBlank(registerResponseType.getVenderMemberNO())) {
            return Tuple2.of(true, buildRepeatRegisterResponse(registerResponseType.getVenderMemberNO(), orderCreateToken, groupId));
        }
        // 注册成功
        if (registerResponseType.getRegisterStatusCode() != null
                && registerResponseType.getRegisterStatusCode().equals(SUCCESS)
                && StringUtil.isNotBlank(registerResponseType.getVenderMemberNO())) {
            return Tuple2.of(true, buildSuccessRegisterResponse(orderCreateToken, registerResponseType.getVenderMemberNO()));
        }
        // 异步注册需要记录
        if (qconfigOfRegisterConfig != null && QconfigOfRegisterConfig.ASYNC.equalsIgnoreCase(qconfigOfRegisterConfig.getExecutiveTypeByGroupId(groupId))) {
            orderCreateToken.setMembershipRegisterId(registerResponseType.getRegisterId());
        }
        return Tuple2.of(false, null);
    }

    /**
     * 重复注册
     * @param membershipNo
     * @param orderCreateToken
     * @return
     */
    protected OrderCreateResponseType buildRepeatRegisterResponse(String membershipNo, OrderCreateToken orderCreateToken, Integer groupId) {
        OrderCreateResponseType orderCreateResponseType = new OrderCreateResponseType();
        orderCreateResponseType.setRegisterOutputInfo(new RegisterOutputInfo(membershipNo));
        orderCreateResponseType.setOrderCreateToken(TokenParseUtil.generateToken(orderCreateToken, OrderCreateToken.class));
        orderCreateResponseType.setConfirmInfo(buildConfirmInfo(DUPLICATE_REGISTRATION, groupId));
        return orderCreateResponseType;
    }

    /**
     * 注册失败
     * @param orderCreateToken
     * @return
     */
    protected OrderCreateResponseType buildFailRegisterResponse(OrderCreateToken orderCreateToken, Integer groupId) {
        OrderCreateResponseType orderCreateResponseType = new OrderCreateResponseType();
        orderCreateResponseType.setOrderCreateToken(TokenParseUtil.generateToken(orderCreateToken, OrderCreateToken.class));
        orderCreateResponseType.setConfirmInfo(buildConfirmInfo(REGISTRATION_FAIL, groupId));
        return orderCreateResponseType;
    }


    /**
     * 注册成功
     * @param orderCreateToken
     * @param registerMemberNo
     * @return
     */
    protected OrderCreateResponseType buildSuccessRegisterResponse(OrderCreateToken orderCreateToken, String registerMemberNo) {
        OrderCreateResponseType res = new OrderCreateResponseType();
        res.setOrderCreateToken(TokenParseUtil.generateToken(orderCreateToken, OrderCreateToken.class));
        res.setRegisterOutputInfo(new RegisterOutputInfo(registerMemberNo));
        return res;
    }

    private static final String GROUP_ID = "GROUP_ID";

    protected ConfirmInfo buildConfirmInfo(String confirmType, Integer groupId) {
        ConfirmDetailInfo confirmDetailInfo = new ConfirmDetailInfo();
        confirmDetailInfo.setCode(confirmType);

        ConfirmDetailExtend extend = new ConfirmDetailExtend();
        extend.setKey(GROUP_ID);
        extend.setValue(groupId == null ? null : String.valueOf(groupId));
        confirmDetailInfo.setConfirmDetailExtends(Arrays.asList(extend));

        ConfirmInfo res = new ConfirmInfo();
        res.setConfirmDetailInfos(Arrays.asList(confirmDetailInfo));
        return res;
    }

    protected String buildMembershipNo(GetCorpUserHotelVipCardResponseType getCorpUserHotelVipCardResponseType,
                                     IntegrationSoaRequestType integrationSoaRequestType,
                                     WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
                                     QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType) {
        if (getCorpUserHotelVipCardResponseType == null || CollectionUtil.isEmpty(getCorpUserHotelVipCardResponseType.getCorpUserHotelVipCardList())) {
            return null;
        }
        if (queryBizModeBindRelationResponseType == null) {
            return null;
        }
        if (checkAvailInfo == null) {
            return null;
        }
        BizModeBindRelationData bizModeBindRelationData = CollectionUtil.findFirst(queryBizModeBindRelationResponseType.getBizModeBindRelationDataList(),
                t -> t != null && StringUtil.equalsIgnoreCase(RequestHeaderUtil.getUserId(integrationSoaRequestType), t.getDimensionId()));
        if (bizModeBindRelationData == null || StringUtil.isBlank(bizModeBindRelationData.getPrimaryDimensionId())) {
            return null;
        }
        CorpUserHotelVipCard corpUserHotelVipCard = CollectionUtil.findFirst(getCorpUserHotelVipCardResponseType.getCorpUserHotelVipCardList(),
                x -> x != null && StringUtil.equalsIgnoreCase(x.getUid(), bizModeBindRelationData.getPrimaryDimensionId())
                        && x.getHotelGroupID() != null && checkAvailInfo.getGroupId() == x.getHotelGroupID());
        if (corpUserHotelVipCard == null) {
            return null;
        }
        return corpUserHotelVipCard.getHtlVipCardID();
    }

    @Override
    protected ParamCheckResult check(Tuple8<RegisterInputInfo, GetCorpUserHotelVipCardResponseType, RegisterResponseType, QconfigOfRegisterConfig,
            OrderCreateToken, IntegrationSoaRequestType, WrapperOfCheckAvail.BaseCheckAvailInfo, QueryBizModeBindRelationResponseType> para) {
        return null;
    }
}
