package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.BffPayTypeEnum;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.order.data.aggregation.query.contract.OrderGenericInfoType;
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataResponseType;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/27 13:42
 * 获取客户选中的房费 服务费支付方式
 */
public class HotelPayTypeUtil {
    private static final String PAY_CODE_CORP_PAY = "ROOM";
    private static final String PAY_CODE_SERVICE_PAY = "SERVICE";
    public static final String BIZC = "BIZC";

    /**
     * 获取房费支付方式（创单接口使用）
     *
     * @param hotelPayTypeInputs
     * @return
     */
    public static HotelPayTypeEnum getRoomPayType(List<HotelPayTypeInput> hotelPayTypeInputs) {
        if (CollectionUtil.isEmpty(hotelPayTypeInputs)) {
            return null;
        }
        return HotelPayTypeEnum.getValue(hotelPayTypeInputs.stream().filter(Objects::nonNull)
            .filter(o -> !StringUtil.isEmpty(o.getPayType()))
            .filter(hotelPayTypeInput -> StringUtil.equalsIgnoreCase(hotelPayTypeInput.getPayCode(), PAY_CODE_CORP_PAY))
            .findFirst().map(HotelPayTypeInput::getPayType).orElse(null));
    }

    public static BffPayTypeEnum buildBffPayTypeEnum(HotelPayTypeEnum roomType) {
        if (roomType == null) {
            return BffPayTypeEnum.NONE;
        }
        switch (roomType) {
            case CORP_PAY:
                return BffPayTypeEnum.CORP_PAY;
            case SELF_PAY:
                return BffPayTypeEnum.SELF_PAY;
            case UNION_PAY:
                return BffPayTypeEnum.UNION_PAY;
            case ADVANCE_PAY:
                return BffPayTypeEnum.ADVANCE_PAY;
            case MIX_PAY:
                return BffPayTypeEnum.MIX_PAY;
            case CASH:
                return BffPayTypeEnum.CASH;
            case FLASH_STAY_PAY:
                return BffPayTypeEnum.FLASH_STAY_PAY;
            default:
                return BffPayTypeEnum.NONE;
        }
    }

    /**
     * 获取房费支付方式（考虑修改订单默认情况 bookingInit接口使用）
     *
     * @param requestType
     * @param queryOrderDataResponseType
     * @return
     */
    public static HotelPayTypeEnum getSelectedRoomPayType(BookingInitRequestType requestType, QueryHotelOrderDataResponseType queryOrderDataResponseType) {
        if (requestType == null) {
            return HotelPayTypeEnum.NONE;
        }
        HotelPayTypeEnum hotelPayTypeEnum = getRoomPayType(requestType.getHotelPayTypeInput());
        if (hotelPayTypeEnum != null && hotelPayTypeEnum != HotelPayTypeEnum.NONE) {
            return hotelPayTypeEnum;
        }
        if (StrategyOfBookingInitUtil.modify(requestType.getStrategyInfos())
            || StrategyOfBookingInitUtil.copyOrder(requestType.getStrategyInfos())
            || StrategyOfBookingInitUtil.applyModify(requestType.getStrategyInfos())) {
            return getRoomPayTypeFromOrder(queryOrderDataResponseType);
        }
        return HotelPayTypeEnum.NONE;
    }

    public static HotelPayTypeEnum getRoomPayTypeFromOrder(QueryHotelOrderDataResponseType queryOrderDataResponseType) {
        // 原单是虚拟支付
        boolean isPrbal = StringUtil.equalsIgnoreCase(
            Optional.ofNullable(queryOrderDataResponseType.getOrderGenericInfo()).map(OrderGenericInfoType::getPayType)
                .orElse(null), HotelPayTypeEnum.PRBAL.getCode());
        if (isPrbal) {
            return HotelPayTypeEnum.PRBAL;
        }
        // 原单支付方式
        boolean isMixPayOldOrder =
            BooleanUtil.isTrue(Optional.ofNullable(queryOrderDataResponseType.getOrderGenericInfo())
                .map(OrderGenericInfoType::getMultiPayFlag).orElse(false));
        if (isMixPayOldOrder) {
            return HotelPayTypeEnum.MIX_PAY;
        }
        String payTypeOldOrder = Optional.ofNullable(queryOrderDataResponseType.getOrderGenericInfo())
            .map(OrderGenericInfoType::getPayType).orElse(null);
        boolean isAcountPayOldOrder = StringUtil.equalsIgnoreCase("ACCNT", payTypeOldOrder);
        if (isAcountPayOldOrder) {
            return HotelPayTypeEnum.CORP_PAY;
        }
        // 公务卡
        if (BIZC.equalsIgnoreCase(payTypeOldOrder)) {
            return HotelPayTypeEnum.CORPORATE_CARD_PAY;
        }
        // 其它有值的情况
        if (StringUtil.isNotBlank(payTypeOldOrder)) {
            HotelPayTypeEnum value = HotelPayTypeEnum.getValueByOrder(payTypeOldOrder);
            return HotelPayTypeEnum.NONE == value ? HotelPayTypeEnum.SELF_PAY : value;
        }
        // 无值的情况
        return HotelPayTypeEnum.NONE;
    }

    /**
     * 注意创单环节所有流程不可使用此方法，因为创单环节服务费支付方式不仅仅是客户输入还有可能是同房费
     * 建议使用：getServicePayType(List<HotelPayTypeInput> hotelPayTypeInputs, ResourceToken resourceToken)
     *
     * @param hotelPayTypeInputs
     * @return
     */
    public static HotelPayTypeEnum getServicePayType(List<HotelPayTypeInput> hotelPayTypeInputs) {
        if (CollectionUtil.isEmpty(hotelPayTypeInputs)) {
            return null;
        }
        return HotelPayTypeEnum.getValue(hotelPayTypeInputs.stream().filter(Objects::nonNull)
            .filter(hotelPayTypeInput -> StringUtil.equalsIgnoreCase(hotelPayTypeInput.getPayCode(), PAY_CODE_SERVICE_PAY))
            .collect(Collectors.toList()).stream().findFirst().map(HotelPayTypeInput::getPayType).orElse(null));
    }

    /**
     * 创单服务费支付方式
     * 1.现付房型---客户输入
     * 2.预付房型---等于房费支付方式
     *
     * @param hotelPayTypeInputs
     * @param resourceToken
     * @return
     */
    public static HotelPayTypeEnum getServicePayType(List<HotelPayTypeInput> hotelPayTypeInputs,
        ResourceToken resourceToken) {
        return getServicePayType(hotelPayTypeInputs, null, resourceToken);
    }

    /**
     * 创单服务费支付方式
     * 1.现付房型---客户输入
     * 2.预付房型---等于房费支付方式
     *
     * @param hotelPayTypeInputs
     * @param resourceToken
     * @return
     */
    public static HotelPayTypeEnum getServicePayType(List<HotelPayTypeInput> hotelPayTypeInputs,
        HotelPayTypeEnum selectedRoomPayType,
        ResourceToken resourceToken) {
        HotelPayTypeEnum hotelServicePayTypeEnumInput = getServicePayType(hotelPayTypeInputs);
        if (Arrays.asList(HotelPayTypeEnum.CORP_PAY, HotelPayTypeEnum.SELF_PAY, HotelPayTypeEnum.CORPORATE_CARD_PAY,
            HotelPayTypeEnum.PRBAL).contains(hotelServicePayTypeEnumInput)) {
            return hotelServicePayTypeEnumInput;
        }
        if (resourceToken == null) {
            return hotelServicePayTypeEnumInput;
        }
        if (resourceToken.getBookInitResourceToken() == null
            || resourceToken.getBookInitResourceToken().getServiceChargeResourceToken() == null
            || MathUtils.isLessOrEqualsZero(
            resourceToken.getBookInitResourceToken().getServiceChargeResourceToken().getServiceChargeAmount())) {
            return hotelServicePayTypeEnumInput;
        }
        if (selectedRoomPayType == null || selectedRoomPayType == HotelPayTypeEnum.NONE) {
            selectedRoomPayType = getRoomPayType(hotelPayTypeInputs);
        }
        // 公账、个人服务费不支持客户选择根据房费支付方式判断
        return getServicePrepayType(selectedRoomPayType);
    }

    private static HotelPayTypeEnum getServicePrepayType(HotelPayTypeEnum roomPayType) {
        if (roomPayType == null) {
            return null;
        }
        switch (roomPayType) {
            case CORP_PAY:
            case ADVANCE_PAY:
            case MIX_PAY:
            case FLASH_STAY_PAY:
                return HotelPayTypeEnum.CORP_PAY;
            case SELF_PAY:
            case UNION_PAY:
                return HotelPayTypeEnum.SELF_PAY;
            case CORPORATE_CARD_PAY:
                return HotelPayTypeEnum.CORPORATE_CARD_PAY;
            case PRBAL:
                return HotelPayTypeEnum.PRBAL;
            default:
                return null;
        }
    }
}
