package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import corp.user.service.corpUserInfoService.GetCorpUserInfoRequestType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2024/4/9 18:47
 * @Version 1.0
 */
@Component public class MapperOfGetCorpUserInfoRequestPolicy
    extends AbstractMapper<Tuple1<HotelPolicyInput>, GetCorpUserInfoRequestType> {

    @Override protected GetCorpUserInfoRequestType convert(Tuple1<HotelPolicyInput> tuple1) {
        GetCorpUserInfoRequestType getCorpUserInfoRequestType = new GetCorpUserInfoRequestType();
        HotelPolicyInput hotelPolicyInput = tuple1.getT1();
        getCorpUserInfoRequestType.setUid(hotelPolicyInput.getPolicyInput().getPolicyUid());
        return getCorpUserInfoRequestType;
    }

    @Override protected ParamCheckResult check(Tuple1<HotelPolicyInput> tuple1) {
        return null;
    }
}
