package com.ctrip.corp.bff.hotel.book.service;

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.processor.Processor;
import com.ctrip.corp.bff.framework.template.service.AbstractSyncService;
import com.ctrip.corp.bff.framework.template.service.generate.WebService;
import com.ctrip.corp.bff.hotel.book.contract.*;
import com.ctrip.corp.bff.hotel.book.processor.ProcessorOfHotelPassengerFormInit;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Author: z.c. wang
 * @Description 酒店出行人表单
 * @Date: 2025/3/14 14:32
 * @Version 1.0
 */
@WebService(name = "hotelPassengerFormInit")
public class ServiceOfHotelPassengerFormInit extends AbstractSyncService<HotelPassengerFormInitRequestType, HotelPassengerFormInitResponseType> {

    @Autowired
    private ProcessorOfHotelPassengerFormInit processorOfHotelPassengerFormInit;

    @Override
    public void validateRequest(HotelPassengerFormInitRequestType requestType) throws BusinessException {

    }

    @Override
    protected Processor<HotelPassengerFormInitRequestType, HotelPassengerFormInitResponseType> getProcessor(HotelPassengerFormInitRequestType requestType) {
        return processorOfHotelPassengerFormInit;
    }

}
