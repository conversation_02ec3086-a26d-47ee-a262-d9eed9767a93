package com.ctrip.corp.bff.hotel.book.common.enums.passenger;

import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CertificateInfo;
import com.ctrip.corp.bff.hotel.book.common.enums.CertificateTypeEnum;

import java.text.MessageFormat;

import static com.ctrip.corp.bff.hotel.book.common.constant.SharkKeyConstant.ID_CARD_TYPE_NAME;

/**
 * @Author: z.c. wang
 * @Description 出行人证件公共code，与集成服务公共契约一致
 * @Date: 2025/3/24 15:41
 * @Version 1.0
 */
public enum PassengerCertificateTypeEnum {

    /**
     * 身份证
     */
    IDENTITY_CARD(CertificateTypeEnum.IDENTITY_CARD, "ID"),

    /**
     * 护照
     */
    PASSPORT(CertificateTypeEnum.PASSPORT, "PASSPORT"),

    /**
     * 学生证
     */
    STUDENT_ID_CARD(null, "STUDENT_ID_CARD"),

    /**
     * 军人证
     */
    MILITARY_CARD(null, "MILITARY_CARD"),

    /**
     * 驾驶证
     */
    DRIVING_LICENSE(null, "DRIVING_LICENSE"),

    /**
     * 回乡证
     */
    HOMEPERMIT(CertificateTypeEnum.HOMETOWN_PERMIT, "HOMEPERMIT"),

    /**
     * 台胞证
     */
    MTP(CertificateTypeEnum.TAIWANESE_CERTIFICATE, "MTP"),

    /**
     * 其它
     */
    OTHERDOCUMENT(null, "OTHERDOCUMENT"),

    /**
     * 港澳通行证
     */
    HKMACPASS(CertificateTypeEnum.HK_AND_MACAU_PASS, "HKMACPASS"),

    /**
     * 国际海员证
     */
    SEAMAN_CARD(null, "SEAMAN_CARD"),

    /**
     * 外国人永久居留证
     */
    FOREIGNER_PERMANENT_RESIDENCE_CARD(null, "FOREIGNER_PERMANENT_RESIDENCE_CARD"),

    /**
     * 台湾通行证
     */
    TAIWANPASS(CertificateTypeEnum.TAIWAN_PASS, "TAIWANPASS"),

    /**
     * 旅行证
     */
    TRAVELDOCUMENT(null, "TRAVELDOCUMENT"),

    /**
     * 外国人永久居留身份证
     */
    FOREIGNER_PERMANENT_RESIDENT_ID_CARD(null, "FOREIGNER_PERMANENT_RESIDENT_ID_CARD"),

    /**
     * 港澳台居民居住证
     */
    RESIDENCEPERMITHKT(null, "RESIDENCEPERMITHKT"),

    /**
     * 海外当地旅行证件
     */
    OVERSEA_AND_LOCAL_TRAVEL_CARD(null, "OVERSEA_AND_LOCAL_TRAVEL_CARD"),

    /**
     * 俄籍国内护照
     */
    RUSSIA_DOMESTIC_PASSPORT(null, "RUSSIA_DOMESTIC_PASSPORT");


    private CertificateTypeEnum aggSupportCertificateType;

    private String sharkName;

    PassengerCertificateTypeEnum(CertificateTypeEnum aggSupportCertificateType, String sharkName) {
        this.aggSupportCertificateType = aggSupportCertificateType;
        this.sharkName = sharkName;
    }

    public String getSharkDesc() {
        return BFFSharkUtil.getSharkValue(MessageFormat.format(ID_CARD_TYPE_NAME, sharkName));
    }

    /**
     * 根据公共定义的证件类型匹配枚举
     *
     * @param commonCertificateType
     * @return
     */
    public static PassengerCertificateTypeEnum findByCommonCertificateType(String commonCertificateType) {
        for (PassengerCertificateTypeEnum value : PassengerCertificateTypeEnum.values()) {
            if (StringUtil.equalsIgnoreCase(value.name(), commonCertificateType)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据公共定义的证件类型匹配枚举
     *
     * @param commonCertificateType
     * @return
     */
    public static PassengerCertificateTypeEnum findByCommonCertificateType(CertificateInfo commonCertificateType) {
        if (commonCertificateType == null) {
            return null;
        }
        for (PassengerCertificateTypeEnum value : PassengerCertificateTypeEnum.values()) {
            if (StringUtil.equalsIgnoreCase(value.name(), commonCertificateType.getCertificateType())) {
                return value;
            }
        }
        return null;
    }

    /**
     * 根据agg证件类型匹配枚举
     *
     * @param aggCertificateType
     * @return
     */
    public static PassengerCertificateTypeEnum findByAggCertificateType(String aggCertificateType) {
        CertificateTypeEnum aggCertificateTypeEnum = CertificateTypeEnum.of(aggCertificateType);
        if (aggCertificateTypeEnum == null) {
            return null;
        }
        for (PassengerCertificateTypeEnum value : PassengerCertificateTypeEnum.values()) {
            if (value.aggSupportCertificateType != null && value.aggSupportCertificateType == aggCertificateTypeEnum) {
                return value;
            }
        }
        return null;
    }
}
