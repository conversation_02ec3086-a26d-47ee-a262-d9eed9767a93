package com.ctrip.corp.bff.hotel.book.handler.corpuserinfoservice4j;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.corpUserInfoService.CorpUserInfoService4jClient;
import corp.user.service.corpUserInfoService.SaveContactInvoiceDefaultInfoRequestType;
import corp.user.service.corpUserInfoService.SaveContactInvoiceDefaultInfoResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/11/18 13:19
 */
@Component public class HandlerOfSaveContactInvoiceDefaultInfo extends
    AbstractHandlerOfSOA<SaveContactInvoiceDefaultInfoRequestType, SaveContactInvoiceDefaultInfoResponseType, CorpUserInfoService4jClient> {

    @Override protected String getMethodName() {
        return "saveContactInvoiceDefaultInfo";
    }
}
