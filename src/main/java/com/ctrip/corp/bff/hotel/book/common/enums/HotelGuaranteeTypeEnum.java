package com.ctrip.corp.bff.hotel.book.common.enums;

import java.util.Arrays;
import java.util.Optional;

/**
 * @Author: chenchuang
 * @Date: 2024/8/27 19:47
 * @Description: 担保类型枚举
 */
public enum HotelGuaranteeTypeEnum {
    /**
     * 公司支付担保
     */
    CORP_GUARANTEE("CORP_GUARANTEE"),

    /**
     * 个人支付担保
     */
    SELF_GUARANTEE("SELF_GUARANTEE"),

    /**
     * 个人支付担保
     */
    NONE("NONE"),

    /**
     * 现付
     */
    CASH_GUARANTEE("CASH_GUARANTEE"),

    /**
     * 无需担保
     */
    NO_GUARANTEE("NO_GUARANTEE"),

    /**
     * 公司信用卡担保
     */
    CORP_CREDIT_CARD_GUARANTEE("CORP_CREDIT_CARD_GUARANTEE"),
    ;

    /**
     * code
     */
    private String code;

    HotelGuaranteeTypeEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public static HotelGuaranteeTypeEnum getValue(String value) {
        Optional<HotelGuaranteeTypeEnum> result =
                Arrays.stream(HotelGuaranteeTypeEnum.values()).filter(paType -> paType.getCode().equalsIgnoreCase(value)).findFirst();
        return result.orElse(NONE);
    }

    public static HotelGuaranteeTypeEnum getGuaranteeMethod(String guaranteeType) {
        if (guaranteeType == null) {
            return HotelGuaranteeTypeEnum.NONE;
        }
        switch (guaranteeType) {
            case ACCOUNT_GUARANTEE:
                return HotelGuaranteeTypeEnum.CORP_GUARANTEE;
            case INDIVIDUAL_GUARANTEE:
                return HotelGuaranteeTypeEnum.SELF_GUARANTEE;
            case CORP_CREDIT_CARD_GUARANTEE_STR:
                return HotelGuaranteeTypeEnum.CORP_CREDIT_CARD_GUARANTEE;
            default:
                return HotelGuaranteeTypeEnum.NONE;
        }
    }
    public static final String CORP_CREDIT_CARD_GUARANTEE_STR = "CORP_CREDIT_CARD_GUARANTEE";
    public static final String ACCOUNT_GUARANTEE = "ACCOUNT_GUARANTEE";
    public static final String INDIVIDUAL_GUARANTEE = "INDIVIDUAL_GUARANTEE";
    public static final String NONE_STR = "NONE";

    public static String buildGuaranteeMethod(HotelGuaranteeTypeEnum guaranteeType) {
        if (guaranteeType == null || guaranteeType == HotelGuaranteeTypeEnum.NONE) {
            return NONE_STR;
        }
        switch (guaranteeType) {
            case CORP_GUARANTEE:
                return ACCOUNT_GUARANTEE;
            case SELF_GUARANTEE:
                return INDIVIDUAL_GUARANTEE;
            case CORP_CREDIT_CARD_GUARANTEE:
                return CORP_CREDIT_CARD_GUARANTEE_STR;
            default:
                return NONE_STR;
        }
    }
}
