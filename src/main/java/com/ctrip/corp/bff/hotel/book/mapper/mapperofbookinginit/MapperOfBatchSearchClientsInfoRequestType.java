package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import corp.user.service.UserInfoQueryService.BatchSearchClientsInfoRequestType;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/17
 */
@Component
public class MapperOfBatchSearchClientsInfoRequestType extends AbstractMapper<Tuple1<IntegrationSoaRequestType>, BatchSearchClientsInfoRequestType> {
    private static final int BUSINESSTYPE_HOTEL = 4;

    @Override
    protected BatchSearchClientsInfoRequestType convert(Tuple1<IntegrationSoaRequestType> para) {
        IntegrationSoaRequestType integrationSoaRequestType = para.getT1();
        BatchSearchClientsInfoRequestType batchSearchClientsInfoRequestType = new BatchSearchClientsInfoRequestType();
        batchSearchClientsInfoRequestType.setBusinessType(BUSINESSTYPE_HOTEL);
        batchSearchClientsInfoRequestType.setUID(
            Optional.ofNullable(integrationSoaRequestType).map(IntegrationSoaRequestType::getUserInfo)
                .map(UserInfo::getUserId).orElse(null));
        batchSearchClientsInfoRequestType.setUIDList(Arrays.asList(
            Optional.ofNullable(integrationSoaRequestType).map(IntegrationSoaRequestType::getUserInfo)
                .map(UserInfo::getUserId).orElse(null)));
        return batchSearchClientsInfoRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple1<IntegrationSoaRequestType> integrationSoaRequestTypeTuple1) {
        return null;
    }
}
