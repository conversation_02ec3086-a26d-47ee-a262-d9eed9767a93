package com.ctrip.corp.bff.hotel.book.common.constant;

/**
 * <AUTHOR>
 * @date 2024/6/21 18:27
 */
public class CustomConfigKeyConstant {
    // 支持两个员工人名重复
    public static String ALLOW_REPEAT_NAME_OF_EMPLOYEE_EMPLOYEE = "allowRepeatNameOfEmployee2Employee";
    // 支持两个非员工人名重复
    public static String ALLOW_REPEAT_NAME_OF_NONEMPLOYEE_NONEMPLOYEE = "allowRepeatNameOfNonEmployee2NonEmployee";
    // 支持两个员工非员工人名重复
    public static String ALLOW_REPEAT_NAME_OF_EMPLOYEE_NONEMPLOYEE = "allowRepeatNameOfEmployee2NonEmployee";
    // 入住人必须是政策执行人
    public static String CLIENT_PSG_POLICY_PSG_MUST_SAME = "clientPsgPolicyPsgMustSame";
    // 秘书为老板预订禁止本人下单
    public static String BOOKING_FOR_BOSS_FORBID_SELF = "bookingForBossForbidSelf";
    // checkdata不做校验 直接通过
    public static String CHECK_DATA_PASS = "checkDataPass";
    // 重复预订管控根据房费支付方式
    public static String REPEAT_ORDER_CONTROL_BY_PAY_TYPE = "repeatOrderControlByPayType";
    // 含公帐支付，按公帐金额匹配审批流
    public static String APPROVAL_MATCH_PAY_NEW = "approvalMatchPayNew";
    /**
     * pwc范围内的公司
     */
    public static final String PWC_CORP_IDS = "PwcCorpIds";
    /**
     * 是否阿里账号
     */
    public static final String ALI_GROUP_ID = "aliGroupId";
    /**
     * 是否阿斯利康账号
     */
    public static final String ASLK_CORPS = "ASLKCorps";
    /**
     * 是否支持3DS支付验证信息
     */
    public static final String SUPPORT_PAYMENT_3DS = "supportPayment3DS";
    /**
     * 发票类型下是否展示发票明细
     */
    public static final String SHOW_INVOICE_DETAIL = "showInvoiceDetail";
    /**
     * 发票默认展示
     */
    public static final String INVOICE_DEFAULT_SHOW = "invoiceDefaultShow";

    /**
     * 发票说明默认勾选
     */
    public static final String INVOICE_TIP_CHECKED = "invoiceTipChecked";

    /**
     * 入住人短信默认勾选配置
     */
    public static final String PASSENGER_SMS_CONFIRM_DEFAULT_CHECKED = "allowPassengerSmsDefaultChecked";

    /**
     * 需要展示超标提示名单
     */
    public static final String CORP_ID_NEED_OVER_POLICY_NOTICE = "CorpIdNeedOverPolicyNotice";

    /**
     * 是否支付时间限制
     */
    public static final String PAYMENT_TIME_LIMIT_MINUTES = "paymentTimeLimitMinutes";

    /**
     * 是否支持代订选择
     */
    public static final String DISTINGUISH_RESERVATION = "distinguishReservation";

    /**
     * 是否返回发票的地址
     */
    public static final String INVOICE_DISTRIBUTE_ADDRESS = "invoiceDistributeAddress";
    /**
     * 是否支持资源校验
     */
    public static final String RESOURCE_CHECK = "resourceCheck";
    /**
     * 是否不支持提前审批校验---默认支持，如果发布生产有问题，需要关闭，走后续兜底
     */
    public static final String UN_SUPPORT_APPROVAL_CHECK = "unSupportApprovalCheck";


    /**
     * 行程客户重新延用---仅支持重新预订入口进入
     */
    public static final String SUPPORT_REBOOK_TRIP_APPROVAL_FLOW = "supportRebookTripApprovalFlow";


    /**
     * 创单是否支持敏感信息加密传输
     */
    public static final String NEED_ENCRYPT_SENSITIVE_INFORMATION = "needEncryptSensitiveInformation";
    /**
     * 协议酒店不展示差旅标准 仅浙江银行特殊需求 不展示 其他公司都需要
     */
    public static final String SHIELD_TRAVEL_STANDARD = "shieldTravelStandard";
    /**
     * 缺失rc的校验
     */
    public static final String RC_CHECK = "rcCheck";    
    /**
     * 是否支持非员工姓名校验弱提醒
     */
    public static final String INFOID_REMIND = "infoIdRemind";
    /**
     * 是否走savecommondata保存成本中心
     */
    public static final String SAVE_COMMON_DATA_COST_CENTER = "saveCommonDataCostCenter";
    /**
     * offline新支付，由BFF公共决定走哪个收银台
     */
    public static final String OFFLINE_NEW_PAY = "offlineNewPay";


    /**
     * 是否支持发票话术模块
     */
    public static final String SUPPORT_INVOICE_TIP = "supportInvoiceTip";
    /**
     * 是否支持新发票逻辑-消费凭证下沉结算组，不再调用agg的发票信息
     */
    public static final String NEW_INVOICE = "newInvoice";


    /**
     * 三峡公司发票提示
     */
    public static final String SAN_XIA_INVOICE_TIP = "sanXiaInvoice";


    /**
     * 是否支持微信小程序支付
     */
    public static final String SUPPORT_MINI_APP = "supportMiniApp";

    /**
     * 无感行程白名单 对标项目ID=99的公司 https://corpint.ctripcorp.com/corp-user-offlinecorpusersys/Project99
     * 2122877511是tesetnet的
     * 113062,176584,240048,295370,318286,444195,beigenecn,CHANEL,corp_uitest3,corp_uitest5,Ctrip,guangsu,qlc3,sanjingzhuyou
     */
    public static final String NON_INDUCTIVE_TRIP_WHITE_LIST = "nonInductiveTripWhiteList";

    /**
     * 是否走新套餐逻辑
     */
    public static final String USE_NEW_PACKAGE = "useNewPackage";

    /**
     * 成本中心对比
     */
    public static final String COST_CENTER_NEW_COMPARE = "costCenterNewCompare";

    /**
     * 成本中心使用
     */
    public static final String COST_CENTER_NEW_USE = "costCenterNewUse";

    /**
     * 蓝色空间成本中心对比
     */
    public static final String BLUE_SPACE_COST_CENTER_NEW_COMPARE = "blueSpaceCostCenterNewCompare";

    /**
     * 蓝色空间成本中心使用
     */
    public static final String BLUE_SPACE_COST_CENTER_NEW_USE = "blueSpaceCostCenterNewUse";
}
