package com.ctrip.corp.bff.hotel.book.common.enums;

/**
 * <AUTHOR>
 * @date 2024-09-26
 **/
public enum TripApplicationEnum {
    /**
     * 新出差申请
     */
    APPLICATION("APPLICATION"),
    /**
     * 老出差申请
     */
    OLD_APPLICATION("OLD_APPLICATION"),
    /**
     * 提前审批模式
     */
    APPROVAL("APPROVAL"),
    /**
     * 其他模式
     */
    NONE("NONE"),
    ;

    private String code;

    TripApplicationEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}
