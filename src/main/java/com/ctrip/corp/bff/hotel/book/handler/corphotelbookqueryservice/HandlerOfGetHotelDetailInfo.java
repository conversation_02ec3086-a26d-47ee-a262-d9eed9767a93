package com.ctrip.corp.bff.hotel.book.handler.corphotelbookqueryservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoRequestType;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType;
import com.ctrip.corp.hotel.book.query.entity.GetHotelDetailInfoRequestType;
import com.ctrip.corp.hotel.book.query.entity.GetHotelDetailInfoResponseType;
import com.ctrip.corp.hotel.book.query.service.CorpHotelBookQueryServiceClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/8/16 19:38
 */

@Component
public class HandlerOfGetHotelDetailInfo extends
    AbstractHandlerOfSOA<GetHotelDetailInfoRequestType, GetHotelDetailInfoResponseType, CorpHotelBookQueryServiceClient> {

    @Override
    protected String getMethodName() {
        return "getHotelDetailInfo";
    }
}
