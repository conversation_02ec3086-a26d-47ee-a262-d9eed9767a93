package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response;

import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount.AccountInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.AmountInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.EmailInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.PaymentGuaranteePolyEnum;
import com.ctrip.corp.bff.hotel.book.common.util.HotelPayTypeUtil;
import com.ctrip.corp.bff.hotel.book.common.util.MathUtils;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail.BaseCheckAvailInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType;
import com.ctrip.corp.bff.hotel.book.contract.PayByUserConfirmInfo;
import com.ctrip.corp.bff.hotel.book.contract.PayByUserInfo;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/4/24 21:28
 */
@Component public class MapperOfPayByUserConfirmInfoResponse extends
    AbstractMapper<Tuple4<OrderCreateToken, OrderCreateRequestType, WrapperOfCheckAvail.BaseCheckAvailInfo,
        WrapperOfAccount.AccountInfo>, Tuple2<Boolean, OrderCreateResponseType>> {
    @Override protected Tuple2<Boolean, OrderCreateResponseType> convert(
        Tuple4<OrderCreateToken, OrderCreateRequestType, BaseCheckAvailInfo, AccountInfo> tuple) {
        OrderCreateResponseType responseType = new OrderCreateResponseType();
        OrderCreateToken orderCreateToken = tuple.getT1();
        OrderCreateRequestType orderCreateRequestType = tuple.getT2();
        WrapperOfCheckAvail.BaseCheckAvailInfo baseCheckAvailInfo = tuple.getT3();
        WrapperOfAccount.AccountInfo accountInfo = tuple.getT4();
        if (!BooleanUtil.parseStr(true).equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getPayByUserInfo()).map(PayByUserInfo::getPayByUser)
                .orElse(null))) {
            return Tuple2.of(false, responseType);
        }
        if (orderCreateToken.containsContinueType(ContinueTypeConst.PAY_BY_USER_CONFIRM)) {
            return Tuple2.of(false, responseType);
        }
        orderCreateToken.addContinueTypes(ContinueTypeConst.PAY_BY_USER_CONFIRM);
        responseType.setOrderCreateToken(TokenParseUtil.generateToken(orderCreateToken, OrderCreateToken.class));
        responseType.setPayByUserConfirmInfo(
            buildPayByUserConfirmInfo(accountInfo, baseCheckAvailInfo, orderCreateRequestType));
        return Tuple2.of(true, responseType);
    }

    @Override protected ParamCheckResult check(
        Tuple4<OrderCreateToken, OrderCreateRequestType, WrapperOfCheckAvail.BaseCheckAvailInfo, AccountInfo> tuple) {
        return null;
    }

    private PayByUserConfirmInfo buildPayByUserConfirmInfo(WrapperOfAccount.AccountInfo accountInfo,
        WrapperOfCheckAvail.BaseCheckAvailInfo baseCheckAvailInfo, OrderCreateRequestType orderCreateRequestType) {
        PayByUserConfirmInfo payByUserConfirmInfo = new PayByUserConfirmInfo();
        EmailInfo emailInfo = new EmailInfo();
        emailInfo.setEmail(accountInfo.getEmail());
        payByUserConfirmInfo.setEmailInfo(emailInfo);
        HotelPayTypeEnum hotelPayTypeEnum =
            HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        if (hotelPayTypeEnum != HotelPayTypeEnum.GUARANTEE_SELF_PAY) {
            return payByUserConfirmInfo;
        }
        AmountInfo payByUserShowAmount = new AmountInfo();
        if (buildUseCustomCurrencyPay(accountInfo, baseCheckAvailInfo)) {
            if (MathUtils.isGreaterThanZero(baseCheckAvailInfo.getRoomAmount())) {
                payByUserShowAmount.setAmount(baseCheckAvailInfo.getRoomAmount().abs().toPlainString());
                payByUserShowAmount.setCurrency(accountInfo.settlementCurrencyForAgg());
            }
        } else {
            if (MathUtils.isGreaterThanZero(baseCheckAvailInfo.getGuaranteeAmount())) {
                payByUserShowAmount.setAmount(baseCheckAvailInfo.getGuaranteeAmount().abs().toPlainString());
                payByUserShowAmount.setCurrency(baseCheckAvailInfo.getOriginCurrency());
            }
        }
        payByUserConfirmInfo.setPayByUserShowAmount(payByUserShowAmount);
        return payByUserConfirmInfo;
    }

    private boolean buildUseCustomCurrencyPay(WrapperOfAccount.AccountInfo accountInfo,
        WrapperOfCheckAvail.BaseCheckAvailInfo baseCheckAvailInfo) {
        String accountCustomCurrency = accountInfo.settlementCurrencyForAgg();
        if (baseCheckAvailInfo.getPaymentGuaranteePolyEnum() == PaymentGuaranteePolyEnum.PAY_TO_CTRIP) {
            return OrderCreateProcessorOfUtil.buildMultiCurrency(accountCustomCurrency);
        }
        return false;
    }
}
