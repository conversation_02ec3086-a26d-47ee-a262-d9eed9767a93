package com.ctrip.corp.bff.hotel.book.handler.corpagghotelsalestrategyservice;

import com.ctrip.corp.agg.hotel.salestrategy.CorpAggHotelSaleStrategyServiceClient;
import com.ctrip.corp.agg.hotel.salestrategy.entity.CalculateTravelRewardsRequestType;
import com.ctrip.corp.agg.hotel.salestrategy.entity.CalculateTravelRewardsResponseType;
import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import org.springframework.stereotype.Component;

/**
 * @Author: chenchuang
 * @Date: 2024/8/22 16:26
 * @Description:旅行奖励费用计算
 */
@Component
public class HandlerOfCalculateTravelRewards extends AbstractHandlerOfSOA<CalculateTravelRewardsRequestType,
        CalculateTravelRewardsResponseType, CorpAggHotelSaleStrategyServiceClient> {

    @Override
    protected String getMethodName() {
        return "calculateTravelRewards";
    }
}
