package com.ctrip.corp.bff.hotel.book.common.enums;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/10/18 10:18
 */
public enum PaymentGuaranteePolyEnum {
    /**
     * 无需担保
     */
    NONE("NONE"),

    /**
     * 担保到酒店
     */
    PAY_TO_HOTEL("HOTEL"),

    /**
     * 担保到携程
     */
    PAY_TO_CTRIP("TRIP"),

    /**
     * VCC
     */
    VCC("VCC");

    private String paymentGuaranteePoly;

    PaymentGuaranteePolyEnum(String paymentGuaranteePoly) {
        this.paymentGuaranteePoly = paymentGuaranteePoly;
    }

    public static PaymentGuaranteePolyEnum getPaymentGuaranteePolyEnum(String paymentGuaranteePoly) {
        for (PaymentGuaranteePolyEnum paymentGuaranteePolyEnum : PaymentGuaranteePolyEnum.values()) {
            if (paymentGuaranteePolyEnum.paymentGuaranteePoly.equalsIgnoreCase(paymentGuaranteePoly)) {
                return paymentGuaranteePolyEnum;
            }
        }
        return NONE;
    }

    public boolean isPayToHotel() {
        return this == PAY_TO_HOTEL;
    }

    public boolean isPayToCtrip() {
        return this == PAY_TO_CTRIP;
    }

    public boolean isVcc() {
        return this == VCC;
    }

    /**
     * 担保到携程
     */
    private static final String TRIP_CODE = "TRIP";

    /**
     * 担保到酒店
     */
    private static final String HOTEL_CODE = "HOTEL";

    /**
     * VCC担保
     */
    private static final String VCC_CODE = "VCC";
    public static PaymentGuaranteePolyEnum getPaymentGuaranteeType(String paymentGuaranteePoly) {
        // 担保到携程
        if (TRIP_CODE.equalsIgnoreCase(paymentGuaranteePoly)) {
            return PaymentGuaranteePolyEnum.PAY_TO_CTRIP;
        }
        // 担保到酒店
        if (HOTEL_CODE.equalsIgnoreCase(paymentGuaranteePoly)) {
            return PaymentGuaranteePolyEnum.PAY_TO_HOTEL;
        }
        // VCC
        if (VCC_CODE.equalsIgnoreCase(paymentGuaranteePoly)) {
            return PaymentGuaranteePolyEnum.VCC;
        }
        return PaymentGuaranteePolyEnum.NONE;
    }
}
