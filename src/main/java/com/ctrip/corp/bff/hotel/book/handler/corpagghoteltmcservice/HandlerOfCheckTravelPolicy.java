package com.ctrip.corp.bff.hotel.book.handler.corpagghoteltmcservice;

import com.ctrip.corp.agg.hotel.tmc.CorpAggHotelTMCServiceClient;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyRequestType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType;
import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.bff.mice.basic.auth.contract.CorpBffMiceBasicAuthServiceClient;
import com.ctrip.corp.bff.mice.basic.auth.contract.TmsCreateOrderVerifyRequestType;
import com.ctrip.corp.bff.mice.basic.auth.contract.TmsCreateOrderVerifyResponseType;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Description 差标、提前审批管控查询
 * @Date 2024/8/12 13:32
 * @Version 1.0
 */
@Component
public class HandlerOfCheckTravelPolicy extends
        AbstractHandlerOfSOA<CheckTravelPolicyRequestType, CheckTravelPolicyResponseType, CorpAggHotelTMCServiceClient> {
    @Override
    protected String getMethodName() {
        return "CheckTravelPolicy";
    }

    @Override
    protected String getLogErrorCode(CheckTravelPolicyResponseType response) {
        return Optional.ofNullable(response).map(CheckTravelPolicyResponseType::getResponseCode)
                .orElse(0).toString();
    }
}
