package com.ctrip.corp.bff.hotel.book.common.enums;

/**
 * <AUTHOR>
 * @Date 2024/8/26 23:59
 */
public enum PolicyServiceIdEnum {
    /**
     * 取消政策
     */
    CANCEL_POLICY("CANCEL_POLICY"),

    /**
     * 确认政策
     */
    CONFIRM_POLICY("CONFIRM_POLICY"),

    /**
     * 餐标政策
     */
    MEAL_STANDARD_POLICY("MEAL_STANDARD_POLICY"),

    /**
     * 服务费取消政策
     */
    CANCEL_POLICY_OF_SERVICE_FEE("CANCEL_POLICY_OF_SERVICE_FEE"),;

    private final String value;

    PolicyServiceIdEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
