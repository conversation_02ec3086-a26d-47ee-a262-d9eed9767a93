package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.soa._21234.GetUserLiteInfoRequestType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/10/22 11:09
 */
@Component public class MapperOfGetUserLiteInfoRequestType
    extends AbstractMapper<Tuple1<OrderCreateRequestType>, GetUserLiteInfoRequestType> {

    @Override
    protected GetUserLiteInfoRequestType convert(Tuple1<OrderCreateRequestType> orderCreateRequestTypeTuple1) {
        OrderCreateRequestType orderCreateRequestType = orderCreateRequestTypeTuple1.getT1();
        GetUserLiteInfoRequestType getUserLiteInfoRequestType = new GetUserLiteInfoRequestType();
        getUserLiteInfoRequestType.setUid(
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId());
        return getUserLiteInfoRequestType;
    }

    @Override protected ParamCheckResult check(Tuple1<OrderCreateRequestType> orderCreateRequestTypeTuple1) {
        return null;
    }
}
