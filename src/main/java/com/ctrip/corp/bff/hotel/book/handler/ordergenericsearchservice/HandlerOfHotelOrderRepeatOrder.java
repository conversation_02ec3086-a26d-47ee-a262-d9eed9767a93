package com.ctrip.corp.bff.hotel.book.handler.ordergenericsearchservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.soa._21210.HotelOrderRepeatOrderRequestType;
import com.ctrip.soa._21210.HotelOrderRepeatOrderResponseType;
import com.ctrip.soa._22074.OrderGenericSearchSerivceClient;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Description 重复订单查询
 * @Date 2024/8/12 14:20
 * @Version 1.0
 */
@Component
public class HandlerOfHotelOrderRepeatOrder extends
        AbstractHandlerOfSOA<HotelOrderRepeatOrderRequestType, HotelOrderRepeatOrderResponseType, OrderGenericSearchSerivceClient> {

    @Override
    protected String getMethodName() {
        return "queryHotelRepeatOrder";
    }

    @Override
    protected String getLogErrorCode(HotelOrderRepeatOrderResponseType response) {
        return Optional.ofNullable(response).map(HotelOrderRepeatOrderResponseType::getResponseCode)
                .orElse(0).toString();
    }
}
