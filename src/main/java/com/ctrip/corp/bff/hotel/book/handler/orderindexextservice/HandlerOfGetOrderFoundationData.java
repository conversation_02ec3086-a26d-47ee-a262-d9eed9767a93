package com.ctrip.corp.bff.hotel.book.handler.orderindexextservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.soa._21210.HotelOrderRepeatOrderRequestType;
import com.ctrip.soa._21210.HotelOrderRepeatOrderResponseType;
import com.ctrip.soa._21759.OrderIndexExtServiceClient;
import com.ctrip.soa._22074.OrderGenericSearchSerivceClient;
import com.ctrip.soa.corp.order.orderindexextservice.v1.GetOrderFoundationDataRequestType;
import com.ctrip.soa.corp.order.orderindexextservice.v1.GetOrderFoundationDataResponseType;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Description 单号类型查询
 * @Date 2025/6/30 14:20
 * @Version 1.0
 */
@Component
public class HandlerOfGetOrderFoundationData extends
        AbstractHandlerOfSOA<GetOrderFoundationDataRequestType, GetOrderFoundationDataResponseType, OrderIndexExtServiceClient> {

    @Override
    protected String getMethodName() {
        return "getOrderFoundationData";
    }
}
