package com.ctrip.corp.bff.hotel.book.qconfig.entity;

import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.List;
import java.util.Map;

/**
 * @Author: yfx
 * @Date: 2018/8/27
 */
@Component
public class CodeMappingConfig {

    private String actionName;

    private Map<String, Integer> mappingRelations;

    private Map<String, String> mappingActionRelations;

    public String getActionName() {
        return actionName;
    }

    public void setActionName(String actionName) {
        this.actionName = actionName;
    }

    public Map<String, Integer> getMappingRelations() {
        return mappingRelations;
    }

    public void setMappingRelations(Map<String, Integer> mappingRelations) {
        this.mappingRelations = mappingRelations;
    }

    public Map<String, String> getMappingActionRelations() {
        return mappingActionRelations;
    }

    public void setMappingActionRelations(Map<String, String> mappingActionRelations) {
        this.mappingActionRelations = mappingActionRelations;
    }

    public Integer getMappingCode(String code) {
        if (CollectionUtil.isEmpty(mappingRelations) || !mappingRelations.containsKey(code)) {
            return null;
        }
        return mappingRelations.get(code);
    }

    public String getMappingActionType(String code) {
        if (CollectionUtil.isEmpty(mappingActionRelations) || !mappingActionRelations.containsKey(code)) {
            return null;
        }
        return mappingActionRelations.get(code);
    }
}
