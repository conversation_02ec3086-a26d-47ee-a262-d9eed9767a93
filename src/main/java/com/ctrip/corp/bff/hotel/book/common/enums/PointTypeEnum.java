package com.ctrip.corp.bff.hotel.book.common.enums;

/**
 * <AUTHOR>
 * @description
 * @date 2024/9/3
 */
public enum PointTypeEnum {
    /**
     * 线上积分
     */
    ONLINE_POINTS("ONLINE_INTEGRAL"),
    /**
     * 线下积分
     */
    OFFLINE_POINTS("OFFLINE_INTEGRAL");

    private String code;

    PointTypeEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}
