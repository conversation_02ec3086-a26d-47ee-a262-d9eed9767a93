package com.ctrip.corp.bff.hotel.book.service;

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.processor.Processor;
import com.ctrip.corp.bff.framework.template.service.AbstractSyncService;
import com.ctrip.corp.bff.framework.template.service.generate.WebService;
import com.ctrip.corp.bff.hotel.book.contract.HotelPassengerCheckRequestType;
import com.ctrip.corp.bff.hotel.book.contract.HotelPassengerCheckResponseType;
import com.ctrip.corp.bff.hotel.book.processor.ProcessorOfHotelPassengerCheck;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Author: z.c. wang
 * @Description 酒店出行人校验
 * @Date: 2025/3/14 14:34
 * @Version 1.0
 */
@WebService(name = "hotelPassengerCheck")
public class ServiceOfHotelPassengerCheck extends AbstractSyncService<HotelPassengerCheckRequestType, HotelPassengerCheckResponseType> {

    @Autowired
    private ProcessorOfHotelPassengerCheck processorOfHotelPassengerCheck;

    @Override
    public void validateRequest(HotelPassengerCheckRequestType requestType) throws BusinessException {

    }

    @Override
    protected Processor<HotelPassengerCheckRequestType, HotelPassengerCheckResponseType> getProcessor(HotelPassengerCheckRequestType requestType) {
        return processorOfHotelPassengerCheck;
    }

}
