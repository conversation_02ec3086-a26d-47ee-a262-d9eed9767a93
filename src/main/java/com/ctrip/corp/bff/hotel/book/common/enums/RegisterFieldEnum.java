package com.ctrip.corp.bff.hotel.book.common.enums;

import java.util.Arrays;

/**
 * @Author: zyp
 * @Date: 2018/8/27
 */
public enum RegisterFieldEnum {

    /**
     * 当地名称（中文或英文名）
     */
    LOCAL_NAME("1"),
    /**
     * 英文名
     */
    NAME_EN("4"),
    /**
     * 手机号
     */
    PHONE("6"),
    /**
     * 邮箱
     */
    EMAIL("7"),
    /**
     * 身份证
     */
    IDENTITY_CARD("8"),
    /**
     * 生日
     */
    BIRTH("9"),
    /**
     * 性别
     */
    GENDER("10")
    ;

    private final String aggCode;

    RegisterFieldEnum(String aggCode) {
        this.aggCode = aggCode;
    }

    public static RegisterFieldEnum valueByAggCode(String aggCode) {
        return Arrays.stream(RegisterFieldEnum.values()).filter(t -> t.aggCode.equalsIgnoreCase(aggCode)).findFirst().orElse(null);
    }
}
