package com.ctrip.corp.bff.hotel.book.handler.corphotelmemberservice;

import com.ctrip.corp.agg.direct.hotel.member.CorpHotelMemberServiceClient;
import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.model.RegisterRequestType;
import com.ctrip.model.RegisterResponseType;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/9/5
 */
@Component
public class HandlerOfRegister extends AbstractHandlerOfSOA<RegisterRequestType, RegisterResponseType, CorpHotelMemberServiceClient> {
    @Override
    protected String getMethodName() {
        return "register";
    }

    @Override
    protected String getLogErrorCode(RegisterResponseType responseType) {
        return String.valueOf(Optional.ofNullable(responseType)
                .map(RegisterResponseType::getResponseCode).orElse(0));
    }
}
