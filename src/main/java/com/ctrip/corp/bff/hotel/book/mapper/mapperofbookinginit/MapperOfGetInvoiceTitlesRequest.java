package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.hotel.book.common.enums.BookingInitErrorEnum;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.soa.platform.sps.InvoiceService.v1.GetInvoiceTitlesRequest;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 发票抬头响应
 * @Date 2024/8/22 10:01
 * @Version 1.0
 */
@Component
public class MapperOfGetInvoiceTitlesRequest extends
    AbstractMapper<Tuple1<BookingInitRequestType>, GetInvoiceTitlesRequest> {
    @Override
    protected GetInvoiceTitlesRequest convert(Tuple1<BookingInitRequestType> tuple) {
        BookingInitRequestType bookingInitRequestType = tuple.getT1();
        GetInvoiceTitlesRequest result = new GetInvoiceTitlesRequest();

        String uid = RequestHeaderUtil.getUserId(bookingInitRequestType.getIntegrationSoaRequestType());
        result.setUid(uid);
        result.setNeedDigital(Boolean.TRUE);

        return result;
    }

    @Override
    protected ParamCheckResult check(Tuple1<BookingInitRequestType> tuple) {
        if (tuple == null || tuple.getT1() == null) {
            return new ParamCheckResult(false,
                BookingInitErrorEnum.MAPPER_PARAM_CHECK_ERROR,
                this.getClass().getName() + " error");
        }
        return null;
    }
}
