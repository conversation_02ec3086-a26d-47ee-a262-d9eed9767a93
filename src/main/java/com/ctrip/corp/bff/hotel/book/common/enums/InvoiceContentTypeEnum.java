package com.ctrip.corp.bff.hotel.book.common.enums;

/**
 * @Author: chenchuang
 * @Date: 2024/9/13 16:34

 */
public enum InvoiceContentTypeEnum {

    /**
     * 发票明细
     */
    INVOICE_DETAIL("invoiceDetail"),

    /**
     * 发票抬头类型
     */
    TITLE_TYPE("titleType"),

    /**
     * 发票抬头
     */
    INVOICE_TITLE("invoiceTitle"),

    /**
     * 纳税人识别号
     */
    TAX_PAYER_NUM("taxPayerNum"),

    /**
     * 纳税人识别号(个人)
     */
    PERSON_TAX_PAYER_NUM("personTaxPayerNum"),

    /**
     * 公司名称
     */
    CORP_NAME("corpName"),

    /**
     * 公司地址
     */
    CORP_ADDRESS("corpAddress"),

    /**
     * 公司电话
     */
    CORP_TEL("corpTel"),

    /**
     * 开户银行名称
     */
    BANK_NAME("bankName"),

    /**
     * 开户银行账号
     */
    BANK_ACCOUNT_NUM("bankAccountNum"),

    /**
     * 配送方式
     */
    INVOICE_DELIVER("invoiceDeliver"),

    /**
     * 发票备注
     */
    INVOICE_REMARK("invoiceRemark"),

    /**
     * 邮寄地址
     */
    ADDRESS_INFO("addressInfo"),

    /**
     * 电子邮件
     */
    EMAIL_INFO("emailInfo")
    ;


    private String key;

    InvoiceContentTypeEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

}
