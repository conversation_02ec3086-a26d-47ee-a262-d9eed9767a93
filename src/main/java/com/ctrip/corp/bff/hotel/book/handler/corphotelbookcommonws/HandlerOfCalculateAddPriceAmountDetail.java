package com.ctrip.corp.bff.hotel.book.handler.corphotelbookcommonws;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.hotelbook.commonws.CorpHotelBookCommonWSClient;
import com.ctrip.model.CalculateAddPriceAmountDetailRequestType;
import com.ctrip.model.CalculateAddPriceAmountDetailResponseType;
import org.springframework.stereotype.Component;

/**
 * @Author: chenchuang
 * @Date: 2024/8/22 16:26
 * @Description:税费计算
 */
@Component
public class HandlerOfCalculateAddPriceAmountDetail extends AbstractHandlerOfSOA<CalculateAddPriceAmountDetailRequestType,
        CalculateAddPriceAmountDetailResponseType, CorpHotelBookCommonWSClient> {

    @Override
    protected String getMethodName() {
        return "calculateAddPriceAmountDetail";
    }
}
