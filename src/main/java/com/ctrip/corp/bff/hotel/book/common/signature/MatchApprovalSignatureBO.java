package com.ctrip.corp.bff.hotel.book.common.signature;

import io.protostuff.Tag;

/**
 * <AUTHOR>
 * @date 2024/10/23 17:32
 */
public class MatchApprovalSignatureBO {
    @Tag(1)
    public String serverFrom;
    @Tag(2)
    public String companyId;
    @Tag(3)
    public String uid;
    @Tag(4)
    public String policyUserId;
    @Tag(5)
    public String approvalType;
    @Tag(6)
    private ApproveObjInfoSignatureBO approveObjInfoSignatureBO;

    public ApproveObjInfoSignatureBO getApproveObjInfoSignatureBO() {
        return approveObjInfoSignatureBO;
    }

    public void setApproveObjInfoSignatureBO(ApproveObjInfoSignatureBO approveObjInfoSignatureBO) {
        this.approveObjInfoSignatureBO = approveObjInfoSignatureBO;
    }

    public String getServerFrom() {
        return serverFrom;
    }

    public void setServerFrom(String serverFrom) {
        this.serverFrom = serverFrom;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getPolicyUserId() {
        return policyUserId;
    }

    public void setPolicyUserId(String policyUserId) {
        this.policyUserId = policyUserId;
    }

    public String getApprovalType() {
        return approvalType;
    }

    public void setApprovalType(String approvalType) {
        this.approvalType = approvalType;
    }
}
