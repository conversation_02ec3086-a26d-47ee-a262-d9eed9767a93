package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.agg.commonws.entity.GetPackageRoomListRequestType;
import com.ctrip.corp.agg.commonws.entity.PkgPersonInfoType;
import com.ctrip.corp.agg.commonws.pkg.room.snapshot.entity.GetPackageRoomSnapshotRequestType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.PackageRoomInfoType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomItem;
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomMealEntity;
import com.ctrip.corp.agg.hotel.roomavailable.entity.XProductEntityType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.XProductInfoType;
import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.enums.BooleanValueEnum;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.hotel.book.common.util.CorpHotelBookCommonWSUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.hotelbook.commonws.entity.RequestBaseInfoType;
import com.ctrip.corp.hotelbook.commonws.entity.UserInfoType;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/10/8 21:33
 */
@Component
public class MapperOfGetPackageRoomSnapshotRequestType
    extends AbstractMapper<Tuple3<BookingInitRequestType,
        ResourceToken,
        WrapperOfCheckAvail.CheckAvailInfo>, GetPackageRoomSnapshotRequestType> {
    @Override protected GetPackageRoomSnapshotRequestType convert(
        Tuple3<BookingInitRequestType, ResourceToken, WrapperOfCheckAvail.CheckAvailInfo> tuple) {
        BookingInitRequestType bookingInitRequestType = tuple.getT1();
        ResourceToken resourceToken = tuple.getT2();
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = tuple.getT3();
        List<XProductEntityType> xProductEntity = Optional.ofNullable(checkAvailInfo)
            .map(WrapperOfCheckAvail.CheckAvailInfo::getRoomItem).map(RoomItem::getXProductInfo).map(XProductInfoType::getXProductEntity).orElse(new ArrayList<>());
        List<String> tokens = xProductEntity.stream().filter(entity -> entity.getXProductType() == 1)
            .map(XProductEntityType::getXProductToken).collect(Collectors.toList());
        if (!supportAccommodationInfo(resourceToken, tokens)){
            return null;
        }
        GetPackageRoomSnapshotRequestType requestType = new GetPackageRoomSnapshotRequestType();
        requestType.setRequestBaseInfo(buildRequestBaseInfoType(bookingInitRequestType));
        requestType.setTokens(tokens);
        return requestType;
    }
    public static RequestBaseInfoType buildRequestBaseInfoType(BookingInitRequestType bookingInitRequestType) {
        IntegrationSoaRequestType integrationSoaRequestType = bookingInitRequestType.getIntegrationSoaRequestType();
        RequestBaseInfoType baseInfo = new RequestBaseInfoType();
        baseInfo.setTraceId(bookingInitRequestType.getIntegrationSoaRequestType().getRequestId());
        UserInfoType userInfo = new UserInfoType();
        userInfo.setUid(RequestHeaderUtil.getUserId(integrationSoaRequestType));
        userInfo.setCorpId(RequestHeaderUtil.getCorpId(integrationSoaRequestType));
        baseInfo.setUserInfo(userInfo);
        baseInfo.setBookingChannel(CorpHotelBookCommonWSUtil.getChannel(integrationSoaRequestType.getSourceFrom()));
        baseInfo.setLocale(integrationSoaRequestType.getLanguage());
        return baseInfo;
    }
    private boolean supportAccommodationInfo(ResourceToken resourceToken, List<String> accommodationTokens) {
        BooleanValueEnum booleanValueEnum = Optional.ofNullable(resourceToken)
            .map(ResourceToken::getRoomResourceToken)
            .map(RoomResourceToken::getSupportAccommodation)
            .orElse(null);
        return booleanValueEnum == BooleanValueEnum.T && !CollectionUtils.isEmpty(accommodationTokens);
    }

    @Override
    protected ParamCheckResult check(Tuple3<BookingInitRequestType, ResourceToken, WrapperOfCheckAvail.CheckAvailInfo> tuple) {
        return null;
    }
}
