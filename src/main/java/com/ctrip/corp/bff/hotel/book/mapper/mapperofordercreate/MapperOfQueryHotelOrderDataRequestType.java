package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataRequestType;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/12 21:31
 */
@Component public class MapperOfQueryHotelOrderDataRequestType
    extends AbstractMapper<Tuple2<ResourceToken, IntegrationSoaRequestType>, QueryHotelOrderDataRequestType> {
    private static final List<String> MODULE_LIST =
        Arrays.asList("ValueAddedInfo", "RepeatOrder", "RemarkInfo", "OrderProcessPolicy", "FeeOrderInfo", "VendorInfo",
            "HotelInsurance");

    @Override protected QueryHotelOrderDataRequestType convert(Tuple2<ResourceToken, IntegrationSoaRequestType> tuple) {
        ResourceToken resourceToken = tuple.getT1();
        IntegrationSoaRequestType integrationSoaRequestType = tuple.getT2();
        QueryHotelOrderDataRequestType queryHotelOrderDataRequestType = new QueryHotelOrderDataRequestType();
        queryHotelOrderDataRequestType.setOrderId(resourceToken.getOrderResourceToken().getOrderId());
        queryHotelOrderDataRequestType.setRequestId(integrationSoaRequestType.getRequestId());
        queryHotelOrderDataRequestType.setEid(integrationSoaRequestType.getEid());
        queryHotelOrderDataRequestType.setUid(integrationSoaRequestType.getUserInfo().getUserId());
        queryHotelOrderDataRequestType.setLocal(integrationSoaRequestType.getLanguage());
        if (integrationSoaRequestType.getSourceFrom() == SourceFrom.Offline) {
            queryHotelOrderDataRequestType.setOperationChannel("Offline");
            queryHotelOrderDataRequestType.setDeleteFlag(true);
        }
        queryHotelOrderDataRequestType.setModuleList(MODULE_LIST);
        return queryHotelOrderDataRequestType;
    }

    @Override protected ParamCheckResult check(Tuple2<ResourceToken, IntegrationSoaRequestType> tuple) {
        return null;
    }
}
