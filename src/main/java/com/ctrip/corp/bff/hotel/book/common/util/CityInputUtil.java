package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.bff.framework.hotel.common.util.WaitFutureUtil;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelGeoInfoResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput;
import com.ctrip.corp.bff.framework.template.handler.WaitFuture;
import com.ctrip.corp.order.data.aggregation.query.contract.*;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/7/22
 */
public class CityInputUtil {

    public static CityInput getCityInput(ResourceToken resourceToken,
                                         WaitFuture<QueryHotelOrderDataRequestType, QueryHotelOrderDataResponseType> queryHotelOrderDataResponseTypeWaitFuture) {
        if (TemplateNumberUtil.isNotZeroAndNull(
                Optional.ofNullable(resourceToken).map(ResourceToken::getHotelResourceToken)
                        .map(HotelResourceToken::getHotelGeoInfoResourceToken).map(HotelGeoInfoResourceToken::getCityId)
                        .orElse(null))) {
            CityInput cityInput = new CityInput();
            cityInput.setCityId(resourceToken.getHotelResourceToken().getHotelGeoInfoResourceToken().getCityId());
            return cityInput;
        }
        Integer cityId = Optional.ofNullable(WaitFutureUtil.safeGetFuture(queryHotelOrderDataResponseTypeWaitFuture))
                .map(QueryHotelOrderDataResponseType::getHotelInfo).map(HotelInfoType::getHotelProduct)
                .map(HotelProductType::getHotel).map(HotelType::getCityId).orElse(-1);
        CityInput cityInput = new CityInput();
        cityInput.setCityId(cityId);
        return cityInput;
    }
}
