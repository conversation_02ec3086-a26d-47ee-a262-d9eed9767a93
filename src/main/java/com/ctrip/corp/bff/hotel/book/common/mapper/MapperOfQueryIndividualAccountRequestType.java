package com.ctrip.corp.bff.hotel.book.common.mapper;

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import corp.user.service.CorpAccountQueryService.QueryIndividualAccountRequestType;
import corp.user.service.CorpAccountQueryService.SearchType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/6/24 7:19
 */
@Component
public class MapperOfQueryIndividualAccountRequestType
    extends AbstractMapper<Tuple1<IntegrationSoaRequestType>, QueryIndividualAccountRequestType> {
    @Override
    protected QueryIndividualAccountRequestType convert(Tuple1<IntegrationSoaRequestType> tuple1) {
        IntegrationSoaRequestType integrationSoaRequestType = tuple1.getT1();
        QueryIndividualAccountRequestType queryIndividualAccountRequestType = new QueryIndividualAccountRequestType();
        queryIndividualAccountRequestType.setCorpId(
                integrationSoaRequestType.getUserInfo().getCorpId());
        queryIndividualAccountRequestType.setSearchKey(
                integrationSoaRequestType.getUserInfo().getUserId());
        queryIndividualAccountRequestType.setSearchType(SearchType.UID);
        return queryIndividualAccountRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple1<IntegrationSoaRequestType> tuple1) {
        return null;
    }
}
