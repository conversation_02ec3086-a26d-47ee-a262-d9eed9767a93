package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.basebiz.members.core.contract.GetInboundParameterRequestType;
import com.ctrip.basebiz.members.core.contract.ParameterItem;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoRequestType;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/23 9:10
 */
@Component
public class MapperOfGetInboundParameterRequestType
    extends AbstractMapper<Tuple1<OrderCreateRequestType>, GetInboundParameterRequestType> {

    @Override protected GetInboundParameterRequestType convert(Tuple1<OrderCreateRequestType> tuple1) {
        GetInboundParameterRequestType request = new GetInboundParameterRequestType();
        OrderCreateRequestType orderCreateRequestType = tuple1.getT1();
        request.setInboundKey(orderCreateRequestType.getOfflineInfo().getCookieId());
        List<ParameterItem> parameterList = new ArrayList<>();
        ParameterItem parameterItem = new ParameterItem();
        parameterItem.setKey("BizType");
        parameterItem.setValue("CRP");
        parameterList.add(parameterItem);
        request.setParameterList(parameterList);
        return request;
    }

    @Override protected ParamCheckResult check(Tuple1<OrderCreateRequestType> tuple1) {
        return null;
    }
}
