package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.hotel.common.constant.AccountInfoConstant;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import corp.user.service.CorpAccountQueryService.GeneralBatchSearchAccountInfoRequestType;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/5/6 15:39
 */
@Component public class MapperOfGeneralBatchSearchAccountInfoRequestType extends
    AbstractMapper<Tuple2<List<HotelBookPassengerInput>, IntegrationSoaRequestType>, GeneralBatchSearchAccountInfoRequestType> {
    @Override protected GeneralBatchSearchAccountInfoRequestType convert(
        Tuple2<List<HotelBookPassengerInput>, IntegrationSoaRequestType> tuple) {
        List<HotelBookPassengerInput> hotelBookPassengerInputs = tuple.getT1();
        IntegrationSoaRequestType integrationSoaRequestType = tuple.getT2();
        List<String> searchFields = buildInitAccountFields();
        GeneralBatchSearchAccountInfoRequestType request = new GeneralBatchSearchAccountInfoRequestType();
        request.setRid(integrationSoaRequestType.getRequestId());
        request.setUids(buildUids(hotelBookPassengerInputs, integrationSoaRequestType));
        request.setSearchFields(searchFields);
        return request;
    }

    @Override protected ParamCheckResult check(
        Tuple2<List<HotelBookPassengerInput>, IntegrationSoaRequestType> listIntegrationSoaRequestTypeTuple2) {
        return null;
    }

    protected List<String> buildUids(List<HotelBookPassengerInput> hotelBookPassengerInputs,
        IntegrationSoaRequestType integrationSoaRequestType) {
        List<String> uids = new ArrayList<>();
        uids.add(integrationSoaRequestType.getUserInfo().getUserId());
        if (CollectionUtil.isEmpty(hotelBookPassengerInputs)) {
            return uids;
        }
        uids.addAll(hotelBookPassengerInputs.stream().filter(hotelBookPassengerInput -> hotelBookPassengerInput != null
                && hotelBookPassengerInput.getHotelPassengerInput() != null && BooleanUtil.parseStr(true)
                .equalsIgnoreCase(hotelBookPassengerInput.getHotelPassengerInput().getEmployee()))
            .map(hotelBookPassengerInput -> hotelBookPassengerInput.getHotelPassengerInput().getUid()).toList());
        return uids;
    }

    private List<String> buildInitAccountFields() {
        return Arrays.asList(AccountInfoConstant.ACC_FIELD_ISCHKAHEADAPPROVE,
            AccountInfoConstant.ACC_FIELD_ISCHKAHEADAPPROVEI);
    }
}
