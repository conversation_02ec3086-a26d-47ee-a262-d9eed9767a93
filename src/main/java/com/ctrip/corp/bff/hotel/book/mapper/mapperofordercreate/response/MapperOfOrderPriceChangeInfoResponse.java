package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response;

import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.AggBookPriceResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.BookInitResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.CorpXProductInfoToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ServiceChargeResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.AmountInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple6;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelBalanceTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.HotelPayTypeUtil;
import com.ctrip.corp.bff.hotel.book.common.util.MathUtils;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail.BaseCheckAvailInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType;
import com.ctrip.corp.bff.hotel.book.contract.PriceChangeInfo;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType;
import com.ctrip.corp.order.data.aggregation.query.contract.AmountType;
import com.ctrip.corp.order.data.aggregation.query.contract.FeeDetailType;
import com.ctrip.corp.order.data.aggregation.query.contract.FeeType;
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataResponseType;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description 变价响应
 * @Date 2024/11/1 11:39
 * @Version 1.0
 */
@Component public class MapperOfOrderPriceChangeInfoResponse extends
    AbstractMapper<Tuple5<OrderCreateToken, OrderCreateRequestType, BaseCheckAvailInfo,
            QueryHotelOrderDataResponseType, ResourceToken>, Tuple2<Boolean, OrderCreateResponseType>> {
    private static final String ORDER_CHANGE_HIGHER = "ORDER_CHANGE_HIGHER";

    @Override protected Tuple2<Boolean, OrderCreateResponseType> convert(
        Tuple5<OrderCreateToken, OrderCreateRequestType, BaseCheckAvailInfo,
            QueryHotelOrderDataResponseType, ResourceToken> tuple) {
        OrderCreateToken orderCreateToken = tuple.getT1();
        OrderCreateRequestType orderCreateRequestType = tuple.getT2();
        BaseCheckAvailInfo baseCheckAvailInfo = tuple.getT3();
        QueryHotelOrderDataResponseType queryHotelOrderDataResponseType = tuple.getT4();
        ResourceToken resourceToken = tuple.getT5();
        OrderCreateResponseType orderCreateResponseType = new OrderCreateResponseType();
        PriceChangeInfo priceChangeInfo = buildPriceChangeInfo(baseCheckAvailInfo, queryHotelOrderDataResponseType,
            resourceToken, orderCreateRequestType);
        if (priceChangeInfo == null) {
            return Tuple2.of(false, orderCreateResponseType);
        }
        orderCreateResponseType.setPriceChangeInfo(priceChangeInfo);
        orderCreateToken.addContinueTypes(ContinueTypeConst.ORDER_PRICE_CHANGE);
        orderCreateResponseType.setOrderCreateToken(
            TokenParseUtil.generateToken(orderCreateToken, OrderCreateToken.class));
        return Tuple2.of(true, orderCreateResponseType);
    }

    @Override protected ParamCheckResult check(
        Tuple5<OrderCreateToken, OrderCreateRequestType, BaseCheckAvailInfo,
            QueryHotelOrderDataResponseType, ResourceToken> tuple) {
        return null;
    }

    private PriceChangeInfo buildPriceChangeInfo(WrapperOfCheckAvail.BaseCheckAvailInfo baseCheckAvailInfo,
        QueryHotelOrderDataResponseType queryHotelOrderDataResponseType, ResourceToken resourceToken,
        OrderCreateRequestType orderCreateRequestType) {
        String customCurrency = baseCheckAvailInfo.getOriginCurrency();
        String originalCustomCurrency = Optional.ofNullable(queryHotelOrderDataResponseType)
            .map(QueryHotelOrderDataResponseType::getOrderAmountInfo).map(FeeType::getTotalAmountInfo)
            .map(AmountType::getSettlementCurrency).orElse(null);
        // 用户更改了配置币种，前后不一致时不提示
        if (!StringUtil.equalsIgnoreCase(customCurrency, originalCustomCurrency)) {
            return null;
        }
        BigDecimal priceOfOrder = buildPriceOfOrder(baseCheckAvailInfo, resourceToken, orderCreateRequestType);
        BigDecimal priceOfOriginalOrder = Optional.ofNullable(queryHotelOrderDataResponseType)
            .map(QueryHotelOrderDataResponseType::getOrderAmountInfo).map(FeeType::getTotalAmountInfo)
            .map(AmountType::getSettlementAmount)
            .map(total -> total.subtract(buildCouponPriceFromOriginalOrder(queryHotelOrderDataResponseType)))
            .filter(MathUtils::isGreaterThanZero).orElse(null);
        // 价格下调不提示
        if (priceOfOrder == null || priceOfOriginalOrder == null
            || MathUtils.isLessOrEqualsZero(priceOfOrder.subtract(priceOfOriginalOrder))) {
            return null;
        }
        PriceChangeInfo priceChangeInfo = new PriceChangeInfo();
        AmountInfo amountInfo = new AmountInfo();
        priceChangeInfo.setPriceChangeType(ORDER_CHANGE_HIGHER);
        amountInfo.setAmount(priceOfOrder.subtract(priceOfOriginalOrder).toPlainString());
        amountInfo.setCurrency(customCurrency);
        priceChangeInfo.setPriceChangeAmountInfo(amountInfo);
        return priceChangeInfo;
    }

    private BigDecimal buildPriceOfOrder(WrapperOfCheckAvail.BaseCheckAvailInfo baseCheckAvailInfo,
        ResourceToken resourceToken, OrderCreateRequestType orderCreateRequestType) {
        // 预付使用费用计算的结果
        if (baseCheckAvailInfo.getHotelBalanceTypeEnum().isPP() || baseCheckAvailInfo.getHotelBalanceTypeEnum()
            .isUseFG()) {
            return buildPriceOfOrderPP(resourceToken, orderCreateRequestType);
        }
        // 现付使用可定反查的结果
        return buildPriceOfOrderFG(baseCheckAvailInfo, resourceToken, orderCreateRequestType);
    }

    private BigDecimal buildPriceOfOrderFG(WrapperOfCheckAvail.BaseCheckAvailInfo baseCheckAvailInfo,
        ResourceToken resourceToken, OrderCreateRequestType orderCreateRequestType) {
        BigDecimal servicePrice = Optional.ofNullable(resourceToken).map(ResourceToken::getBookInitResourceToken)
            .map(BookInitResourceToken::getServiceChargeResourceToken)
            .map(ServiceChargeResourceToken::getServiceChargeAmount).orElse(BigDecimal.ZERO);
        // 税后 优惠后 价格 含税
        BigDecimal roomPriceIncludeTaxAfterPromotion = baseCheckAvailInfo.getRoomAmount();
        BigDecimal couponPrice = baseCheckAvailInfo.getCouponAmount();
        BigDecimal xProductPrice = buildXProductPrice(orderCreateRequestType);
        List<BigDecimal> plusLst = Arrays.asList(roomPriceIncludeTaxAfterPromotion, servicePrice, xProductPrice);
        List<BigDecimal> subtractLst = Arrays.asList(couponPrice);
        BigDecimal res = calcPrice(plusLst, subtractLst);
        return MathUtils.isGreaterThanZero(res) ? res : BigDecimal.ZERO;
    }

    private BigDecimal buildXProductPrice(OrderCreateRequestType orderCreateRequestType) {
        if (orderCreateRequestType.getHotelInsuranceInput() == null || CollectionUtil.isEmpty(
            orderCreateRequestType.getHotelInsuranceInput().getHotelInsuranceDetailInputs())) {
            return BigDecimal.ZERO;
        }
        final BigDecimal[] res = {BigDecimal.ZERO};
        orderCreateRequestType.getHotelInsuranceInput().getHotelInsuranceDetailInputs()
            .forEach(hotelInsuranceDetailInput -> {
                if (StringUtil.isBlank(hotelInsuranceDetailInput.getInsuranceToken())) {
                    return;
                }
                CorpXProductInfoToken insuranceToken =
                    TokenParseUtil.parseToken(hotelInsuranceDetailInput.getInsuranceToken(),
                        CorpXProductInfoToken.class);
                if (null == insuranceToken || null == insuranceToken.getPriceMark()) {
                    return;
                }
                if (MathUtils.isGreaterThanZero(insuranceToken.getAmountPerUnit())) {
                    res[0] = res[0].add(insuranceToken.getAmountPerUnit());
                }
            });
        return res[0];
    }

    private BigDecimal calcPrice(List<BigDecimal> plusLst, List<BigDecimal> subtractLst) {
        BigDecimal res = BigDecimal.ZERO;
        if (CollectionUtil.isNotEmpty(plusLst)) {
            for (BigDecimal plus : plusLst) {
                res = res.add(MathUtils.isGreaterThanZero(plus) ? plus : BigDecimal.ZERO);
            }
        }
        if (CollectionUtil.isNotEmpty(subtractLst)) {
            for (BigDecimal subtract : subtractLst) {
                res = res.subtract(MathUtils.isGreaterThanZero(subtract) ? subtract : BigDecimal.ZERO);
            }
        }
        return res;
    }

    private BigDecimal buildPriceOfOrderPP(ResourceToken resourceToken, OrderCreateRequestType orderCreateRequestType) {
        if (resourceToken == null) {
            return BigDecimal.ZERO;
        }
        HotelPayTypeEnum hotelPayTypeEnum =
            HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        if (hotelPayTypeEnum == null) {
            return BigDecimal.ZERO;
        }
        BigDecimal result = null;
        BigDecimal accountPaymentAmount = Optional.ofNullable(resourceToken.getBookInitResourceToken())
            .map(BookInitResourceToken::getAggBookPriceResourceToken)
            .map(AggBookPriceResourceToken::getAccountPaymentAmount).orElse(BigDecimal.ZERO);
        BigDecimal individualPaymentAmount = Optional.ofNullable(resourceToken.getBookInitResourceToken())
            .map(BookInitResourceToken::getAggBookPriceResourceToken)
            .map(AggBookPriceResourceToken::getIndividualPaymentAmount).orElse(BigDecimal.ZERO);
        BigDecimal virtualPaymentAmount = Optional.ofNullable(resourceToken.getBookInitResourceToken())
            .map(BookInitResourceToken::getAggBookPriceResourceToken)
            .map(AggBookPriceResourceToken::getVirtualPaymentAmount).orElse(BigDecimal.ZERO);
        BigDecimal corporateCardPaymentAmount = Optional.ofNullable(resourceToken.getBookInitResourceToken())
            .map(BookInitResourceToken::getAggBookPriceResourceToken)
            .map(AggBookPriceResourceToken::getCorporateCardPaymentAmount).orElse(BigDecimal.ZERO);
        // 预付房型仅支持 公帐、个人、银联、混合、闪住
        switch (hotelPayTypeEnum) {
            case PRBAL:
                result = virtualPaymentAmount;
                break;
            case CORPORATE_CARD_PAY:
                result = corporateCardPaymentAmount;
                break;
            case CORP_PAY:
            case ADVANCE_PAY:
            case FLASH_STAY_PAY:
                result = accountPaymentAmount;
                break;
            case SELF_PAY:
            case UNION_PAY:
                result = individualPaymentAmount;
                break;
            case MIX_PAY:
                result = MathUtils.add(accountPaymentAmount, individualPaymentAmount);
                break;
            default:
                result = BigDecimal.ZERO;
        }
        return result;
    }

    private BigDecimal buildCouponPriceFromOriginalOrder(QueryHotelOrderDataResponseType queryOrderDataResponseType) {
        return Optional.ofNullable(queryOrderDataResponseType).map(QueryHotelOrderDataResponseType::getOrderAmountInfo)
            .map(FeeType::getFeeDetailList).orElse(Collections.emptyList()).stream()
            .filter(t -> StringUtil.equalsIgnoreCase("couponFee", t.getFeeCode())).map(FeeDetailType::getFeeAmountInfo)
            .findFirst().map(AmountType::getPayAmount).orElse(BigDecimal.ZERO);
    }
}
