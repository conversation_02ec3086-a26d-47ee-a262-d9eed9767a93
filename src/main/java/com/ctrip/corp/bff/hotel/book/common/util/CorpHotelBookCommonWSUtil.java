package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.CorpXProductInfoToken;
import com.ctrip.corp.bff.framework.template.common.utils.HostUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.hotelbook.commonws.entity.CorpXGuestInfoType;
import com.ctrip.corp.hotelbook.commonws.entity.CorpXProductInfoType;
import com.ctrip.corp.hotelbook.commonws.entity.RequestBaseInfoType;
import com.ctrip.corp.hotelbook.commonws.entity.UserInfoType;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2024/9/10 13:56
 * @Version 1.0
 */
public class CorpHotelBookCommonWSUtil {
    public static final String ACCOUNT_PAY = "ACCOUNT_PAY";
    public static final String INDIVIDUAL_PAY = "INDIVIDUAL_PAY";
    public static final String MIX_PAY = "MIX_PAY";
    public static final String CASH_PAY = "CASH_PAY";
    public static final String UNION_PAY = "UNION_PAY";
    private static final String REQUEST_FROM_APP = "APP";
    private static final String REQUEST_FROM_ONLINE = "ONLINE";
    private static final String REQUEST_FROM_OFFLINE = "OFFLINE";
    public static final String WECHAT = "WECHAT";
    public static String getChannel(SourceFrom sourceFrom) {
        if (sourceFrom == null) {
            return REQUEST_FROM_APP;
        }
        if (sourceFrom == SourceFrom.Online) {
            return REQUEST_FROM_ONLINE;
        }
        if (sourceFrom == SourceFrom.Offline) {
            return REQUEST_FROM_OFFLINE;
        }
        return REQUEST_FROM_APP;
    }

    public static RequestBaseInfoType buildRequestBaseInfoType(BookingInitRequestType bookingInitRequestType) {
        IntegrationSoaRequestType integrationSoaRequestType = bookingInitRequestType.getIntegrationSoaRequestType();
        RequestBaseInfoType baseInfo = new RequestBaseInfoType();
        baseInfo.setTraceId(bookingInitRequestType.getIntegrationSoaRequestType().getRequestId());
        UserInfoType userInfo = new UserInfoType();
        userInfo.setUid(RequestHeaderUtil.getUserId(integrationSoaRequestType));
        userInfo.setPolicyUid(
            Optional.ofNullable(bookingInitRequestType.getPolicyInput()).map(PolicyInput::getPolicyUid)
                .orElse(bookingInitRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId()));
        userInfo.setCorpId(RequestHeaderUtil.getCorpId(integrationSoaRequestType));
        baseInfo.setUserInfo(userInfo);
        baseInfo.setBookingChannel(getChannel(integrationSoaRequestType.getSourceFrom()));
        baseInfo.setRequestFrom(
                Optional.ofNullable(integrationSoaRequestType.getSourceFrom()).map(SourceFrom::name).orElse(SourceFrom.H5.name()));
        baseInfo.setLocale(integrationSoaRequestType.getLanguage());
        return baseInfo;
    }

    public static RequestBaseInfoType buildRequestBaseInfoTypeWithPolicyUidNotNull(BookingInitRequestType bookingInitRequestType) {
        IntegrationSoaRequestType integrationSoaRequestType = bookingInitRequestType.getIntegrationSoaRequestType();
        RequestBaseInfoType baseInfo = new RequestBaseInfoType();
        baseInfo.setTraceId(bookingInitRequestType.getIntegrationSoaRequestType().getRequestId());
        UserInfoType userInfo = new UserInfoType();
        userInfo.setUid(RequestHeaderUtil.getUserId(integrationSoaRequestType));
        String policyUid = Optional.ofNullable(bookingInitRequestType.getPolicyInput()).map(PolicyInput::getPolicyUid)
            .orElse(bookingInitRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId());
        if (StringUtil.isEmpty(policyUid)) {
            policyUid = RequestHeaderUtil.getUserId(integrationSoaRequestType);
        }
        userInfo.setPolicyUid(policyUid);
        userInfo.setCorpId(RequestHeaderUtil.getCorpId(integrationSoaRequestType));
        baseInfo.setUserInfo(userInfo);
        baseInfo.setBookingChannel(getChannel(integrationSoaRequestType.getSourceFrom()));
        baseInfo.setRequestFrom(
            Optional.ofNullable(integrationSoaRequestType.getSourceFrom()).map(SourceFrom::name).orElse(SourceFrom.H5.name()));
        baseInfo.setLocale(integrationSoaRequestType.getLanguage());
        baseInfo.setSubBookingChannel(buildSubChannel(integrationSoaRequestType));
        baseInfo.setPos(HostUtil.mapToAccountPos(Optional.ofNullable(integrationSoaRequestType.getUserInfo()).map(UserInfo::getPos).orElse(null)));
        return baseInfo;
    }


    /**
     * 微信小程序
     */
    private static final String WE_CHAT = "WECHAT";

    /**
     * 企业微信
     */
    private static final String WECOM = "WECOM";

    /**
     * 子渠道
     *
     * @param integrationSoaRequestType
     * @return
     */
    public static String buildSubChannel(IntegrationSoaRequestType integrationSoaRequestType) {
        String channel = RequestHeaderUtil.getChannel(integrationSoaRequestType);
        String subChannel = RequestHeaderUtil.getSubChannel(integrationSoaRequestType);
        if (StringUtil.equalsIgnoreCase(WECOM, subChannel)) {
            return WECOM;
        }
        if (StringUtil.equalsIgnoreCase(WE_CHAT, channel)) {
            return WE_CHAT;
        }
        return StringUtil.isEmpty(subChannel) ? null : subChannel;
    }


    public static List<CorpXProductInfoType> buildCorpXProductInfoType(BookingInitRequestType bookingInitRequestType) {
        if (bookingInitRequestType.getHotelInsuranceInput() == null
                || CollectionUtil.isEmpty(bookingInitRequestType.getHotelInsuranceInput().getHotelInsuranceDetailInputs())) {
            return new ArrayList<>();
        }

        List<CorpXProductInfoType> corpXProductInfoTypeList = new ArrayList<>();
        bookingInitRequestType.getHotelInsuranceInput().getHotelInsuranceDetailInputs().forEach(
                hotelInsuranceDetailInput -> {
                    if (CollectionUtil.isEmpty(hotelInsuranceDetailInput.getInsuranceHotelBookPassengerInputs())) {
                        return;
                    }
                    CorpXProductInfoToken insuranceToken = TokenParseUtil.parseToken(hotelInsuranceDetailInput.getInsuranceToken(),
                            CorpXProductInfoToken.class);
                    CorpXProductInfoType corpXProductInfoType = new CorpXProductInfoType();
                    corpXProductInfoType.setOwnerType("PERSON");
                    corpXProductInfoType.setPriceMark(insuranceToken.getPriceMark());
                    List<CorpXGuestInfoType> corpXGuestInfoTypes = new ArrayList<>();
                    hotelInsuranceDetailInput.getInsuranceHotelBookPassengerInputs().forEach(
                            insuranceHotelBookPassengerInput -> {
                                HotelPassengerInput hotelPassengerInput = insuranceHotelBookPassengerInput.getHotelPassengerInput();
                                CorpXGuestInfoType corpXGuestInfoType = new CorpXGuestInfoType();
                                corpXGuestInfoType.setUid(hotelPassengerInput.getUid());
                                corpXGuestInfoType.setEmployee("T".equalsIgnoreCase(hotelPassengerInput.getEmployee()));
                                corpXGuestInfoType.setRoomIndex(hotelPassengerInput.getRoomIndex());
                                corpXGuestInfoTypes.add(corpXGuestInfoType);
                            }
                    );
                    corpXProductInfoType.setGuestList(corpXGuestInfoTypes);
                    corpXProductInfoTypeList.add(corpXProductInfoType);
                }
        );
        return corpXProductInfoTypeList;
    }

    public static String buildPaymentMethod(HotelPayTypeEnum hotelPayTypeEnum) {
        if (hotelPayTypeEnum == null || hotelPayTypeEnum == HotelPayTypeEnum.NONE) {
            return "";
        }
        switch (hotelPayTypeEnum) {
            case CORP_PAY:
            case ADVANCE_PAY:
            case FLASH_STAY_PAY:
                return ACCOUNT_PAY;
            case SELF_PAY:
                return INDIVIDUAL_PAY;
            case MIX_PAY:
                return MIX_PAY;
            case CASH:
                return CASH_PAY;
            case UNION_PAY:
                return UNION_PAY;
            case PRBAL:
            case CORPORATE_CARD_PAY:
                return hotelPayTypeEnum.getCode();
            default:
                return "";
        }
    }

    public static String buildServicePaymentMethod(List<HotelPayTypeInput> hotelPayTypeInputs) {
        if (CollectionUtil.isEmpty(hotelPayTypeInputs)) {
            return null;
        }
        return CorpHotelBookCommonWSUtil.buildPaymentMethod(HotelPayTypeUtil.getServicePayType(hotelPayTypeInputs));
    }

}
