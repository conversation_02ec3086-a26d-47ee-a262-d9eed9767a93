package com.ctrip.corp.bff.hotel.book.common.wrapper;

import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType;
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextResponseType;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount.AccountInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.soa.corp.order.orderindexextservice.v1.GetOrderFoundationDataResponseType;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/30 12:42
 */
public class WrapperOfCheckHotelAuthExtension {
    private WrapperOfAccount.AccountInfo accountInfo;
    private GetTravelPolicyContextResponseType getTravelPolicyContextResponseType;
    private CheckTravelPolicyResponseType checkTravelPolicyResponseType;
    private QueryCheckAvailContextResponseType queryCheckAvailContextResponseType;
    private OrderCreateRequestType orderCreateRequestType;
    private ResourceToken resourceToken;
    private WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo;
    private QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig;
    private Map<String, StrategyInfo> strategyInfoMap;
    private GetOrderFoundationDataResponseType getOrderFoundationDataResponseType;
    private OrderCreateToken orderCreateToken;

    public static class Build {
        private final WrapperOfCheckHotelAuthExtension wrapperOfCheckHotelAuthExtension =
            new WrapperOfCheckHotelAuthExtension();

        public Build withAccountInfo(AccountInfo accountInfo) {
            wrapperOfCheckHotelAuthExtension.accountInfo = accountInfo;
            return this;
        }

        public Build withGetTravelPolicyContextResponseType(
            GetTravelPolicyContextResponseType getTravelPolicyContextResponseType) {
            wrapperOfCheckHotelAuthExtension.getTravelPolicyContextResponseType = getTravelPolicyContextResponseType;
            return this;
        }

        public Build withCheckTravelPolicyResponseType(CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
            wrapperOfCheckHotelAuthExtension.checkTravelPolicyResponseType = checkTravelPolicyResponseType;
            return this;
        }

        public Build withQueryCheckAvailContextResponseType(
            QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
            wrapperOfCheckHotelAuthExtension.queryCheckAvailContextResponseType = queryCheckAvailContextResponseType;
            return this;
        }

        public Build withOrderCreateRequestType(OrderCreateRequestType orderCreateRequestType) {
            wrapperOfCheckHotelAuthExtension.orderCreateRequestType = orderCreateRequestType;
            return this;
        }

        public Build withResourceToken(ResourceToken resourceToken) {
            wrapperOfCheckHotelAuthExtension.resourceToken = resourceToken;
            return this;
        }

        public Build withCheckAvailInfo(WrapperOfCheckAvail.BaseCheckAvailInfo baseCheckAvailInfo) {
            wrapperOfCheckHotelAuthExtension.checkAvailInfo = baseCheckAvailInfo;
            return this;
        }

        public Build withQconfigOfCertificateInitConfig(QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig) {
            wrapperOfCheckHotelAuthExtension.qconfigOfCertificateInitConfig = qconfigOfCertificateInitConfig;
            return this;
        }

        public Build withStrategyInfoMap(Map<String, StrategyInfo> strategyInfoMap) {
            wrapperOfCheckHotelAuthExtension.strategyInfoMap = strategyInfoMap;
            return this;
        }

        public Build withGetOrderFoundationDataResponseType(
            GetOrderFoundationDataResponseType getOrderFoundationDataResponseType) {
            wrapperOfCheckHotelAuthExtension.getOrderFoundationDataResponseType = getOrderFoundationDataResponseType;
            return this;
        }

        public Build withOrderCreateToken(OrderCreateToken orderCreateToken) {
            wrapperOfCheckHotelAuthExtension.orderCreateToken = orderCreateToken;
            return this;
        }

        public WrapperOfCheckHotelAuthExtension build() {
            return wrapperOfCheckHotelAuthExtension;
        }
    }

    public static Build builder() {
        return new WrapperOfCheckHotelAuthExtension.Build();
    }

    public WrapperOfAccount.AccountInfo getAccountInfo() {
        return accountInfo;
    }

    public GetTravelPolicyContextResponseType getGetTravelPolicyContextResponseType() {
        return getTravelPolicyContextResponseType;
    }

    public CheckTravelPolicyResponseType getCheckTravelPolicyResponseType() {
        return checkTravelPolicyResponseType;
    }

    public QueryCheckAvailContextResponseType getQueryCheckAvailContextResponseType() {
        return queryCheckAvailContextResponseType;
    }

    public OrderCreateRequestType getOrderCreateRequestType() {
        return orderCreateRequestType;
    }

    public ResourceToken getResourceToken() {
        return resourceToken;
    }

    public WrapperOfCheckAvail.BaseCheckAvailInfo getCheckAvailInfo() {
        return checkAvailInfo;
    }

    public QconfigOfCertificateInitConfig getQconfigOfCertificateInitConfig() {
        return qconfigOfCertificateInitConfig;
    }

    public Map<String, StrategyInfo> getStrategyInfoMap() {
        return strategyInfoMap;
    }

    public GetOrderFoundationDataResponseType getGetOrderFoundationDataResponseType() {
        return getOrderFoundationDataResponseType;
    }

    public OrderCreateToken getOrderCreateToken() {
        return orderCreateToken;
    }
}
