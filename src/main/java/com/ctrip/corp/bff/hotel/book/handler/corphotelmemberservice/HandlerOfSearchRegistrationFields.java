package com.ctrip.corp.bff.hotel.book.handler.corphotelmemberservice;

import com.ctrip.corp.agg.direct.hotel.member.CorpHotelMemberServiceClient;
import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.model.SearchRegistrationFieldsRequestType;
import com.ctrip.model.SearchRegistrationFieldsResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/10/17
 */
@Component
public class HandlerOfSearchRegistrationFields
        extends AbstractHandlerOfSOA<SearchRegistrationFieldsRequestType, SearchRegistrationFieldsResponseType, CorpHotelMemberServiceClient> {
    @Override
    protected String getMethodName() {
        return "searchRegistrationFields";
    }
}
