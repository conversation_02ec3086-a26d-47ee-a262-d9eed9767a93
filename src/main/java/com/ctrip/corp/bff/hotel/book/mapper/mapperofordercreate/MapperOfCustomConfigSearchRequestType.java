package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.corpsz.configuration.common.contract.CustomConfigSearchRequestType;
import com.ctrip.corp.corpsz.configuration.common.contract.CustomConfigTypeEnum;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2025/05/29
 */
@Component public class MapperOfCustomConfigSearchRequestType
    extends AbstractMapper<Tuple1<IntegrationSoaRequestType>, CustomConfigSearchRequestType> {

    @Override protected CustomConfigSearchRequestType convert(Tuple1<IntegrationSoaRequestType> param) {
        CustomConfigSearchRequestType customConfigSearchRequestType = new CustomConfigSearchRequestType();
        IntegrationSoaRequestType integrationSoaRequestType = param.getT1();
        customConfigSearchRequestType.setCorpId(integrationSoaRequestType.getUserInfo().getCorpId());
        customConfigSearchRequestType.setUId(integrationSoaRequestType.getUserInfo().getUserId());
        customConfigSearchRequestType.setCustomConfigTypeList(Arrays.asList(CustomConfigTypeEnum.INPUT_EXTERNAL_EID));
        return customConfigSearchRequestType;
    }

    @Override protected ParamCheckResult check(Tuple1<IntegrationSoaRequestType> param) {
        return null;
    }
}
