package com.ctrip.corp.bff.hotel.book.handler.orderfoundationcenterauthorizationservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.soa._20184.*;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Description
 * @Date 2024/8/12 14:47
 * @Version 1.0
 */
@Component
public class HandlerOfCheckHotelAuthExtension extends
        AbstractHandlerOfSOA<CheckHotelAuthExtensionRequestType, CheckHotelAuthExtensionResponseType, OrderFoundationCenterAuthorizationServiceClient> {

    @Override
    protected String getMethodName() {
        return "checkHotelAuthExtension";
    }

    @Override
    protected String getLogErrorCode(CheckHotelAuthExtensionResponseType response) {
        return Optional.ofNullable(response).map(CheckHotelAuthExtensionResponseType::getResponseCode)
                .orElse(0).toString();
    }
}
