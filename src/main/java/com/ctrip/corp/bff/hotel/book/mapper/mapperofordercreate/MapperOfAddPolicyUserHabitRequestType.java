package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import corp.user.service.dbdecouplingService.contract.AddPolicyUserHabitRequestType;
import org.springframework.stereotype.Component;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/12/12 21:37
 */
@Component public class MapperOfAddPolicyUserHabitRequestType
    extends AbstractMapper<Tuple1<OrderCreateRequestType>, AddPolicyUserHabitRequestType> {

    @Override protected AddPolicyUserHabitRequestType convert(Tuple1<OrderCreateRequestType> tuple) {
        OrderCreateRequestType orderCreateRequestType = tuple.getT1();
        AddPolicyUserHabitRequestType requestType = new AddPolicyUserHabitRequestType();
        requestType.setUid(orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId());
        requestType.setPolicyUid(orderCreateRequestType.getHotelPolicyInput().getPolicyInput().getPolicyUid());
        requestType.setOperator(orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId());
        requestType.setRid(orderCreateRequestType.getIntegrationSoaRequestType().getRequestId());
        return requestType;
    }

    @Override protected ParamCheckResult check(Tuple1<OrderCreateRequestType> tuple) {
        return null;
    }
}
