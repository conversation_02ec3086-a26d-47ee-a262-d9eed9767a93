package com.ctrip.corp.bff.hotel.book.handler.corphotelbookcommonws;

import com.ctrip.corp.agg.commonws.pkg.room.snapshot.entity.GetPackageRoomSnapshotRequestType;
import com.ctrip.corp.agg.commonws.pkg.room.snapshot.entity.GetPackageRoomSnapshotResponseType;
import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.hotelbook.commonws.CorpHotelBookCommonWSClient;
import com.ctrip.corp.hotelbook.commonws.entity.ErrorCodeInfo;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: junlongma
 * @Date: 2024-10-8
 * @Description: 酒店套餐
 */
@Component
public class HandlerOfGetPackageRoomSnapshot extends AbstractHandlerOfSOA<GetPackageRoomSnapshotRequestType,
    GetPackageRoomSnapshotResponseType, CorpHotelBookCommonWSClient> {
    @Override
    protected String getMethodName() {
        return "getPackageRoomSnapshot";
    }

    @Override protected String getLogErrorCode(GetPackageRoomSnapshotResponseType response) {
        Integer errorCode = Optional.ofNullable(response).map(GetPackageRoomSnapshotResponseType::getErrorCodeInfoList)
            .orElse(new ArrayList<>()).stream().filter(Objects::nonNull).findFirst().map(
            ErrorCodeInfo::getErrorCode).orElse(null);
        return errorCode == null ? null : String.valueOf(errorCode);
    }
}
