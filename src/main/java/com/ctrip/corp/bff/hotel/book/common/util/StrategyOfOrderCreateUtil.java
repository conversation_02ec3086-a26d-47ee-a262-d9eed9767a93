package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.bff.framework.hotel.common.constant.BooleanConstant;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.foundation.common.util.Null;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2025/07/24 14:21
 * @Version 1.0
 * @Description
 */
public class StrategyOfOrderCreateUtil {
    // 蓝色空间人名策略
    public static final String PASSENGER_NAME_CONCAT_USE_CONFIG = "PASSENGER_NAME_CONCAT_USE_CONFIG";
    // 蓝色空间人名策略值为T
    public static final String DISABLE_ORDER_PRICE_CHANGE = "DISABLE_ORDER_PRICE_CHANGE";

    private static final String STRATEGY_VALUE_T = "T";

    // 蓝色空间人名策略根据qconfig读取规则
    public static boolean passengerNameConcatUseConfig(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueIsT(strategyInfoMap, PASSENGER_NAME_CONCAT_USE_CONFIG);
    }



    // 蓝色空间不需要修改单变价提醒-FE未对接过
    public static boolean disableOrderPriceChange(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueIsT(strategyInfoMap, DISABLE_ORDER_PRICE_CHANGE);
    }

    private static boolean strategyValueIsT(Map<String, StrategyInfo> strategyInfos, String key) {
        if (CollectionUtil.isEmpty(strategyInfos) || StringUtil.isEmpty(key)) {
            return false;
        }
        if (strategyInfos.get(key) == null) {
            return false;
        }
        return STRATEGY_VALUE_T.equalsIgnoreCase(strategyInfos.get(key).getStrategyValue());
    }
}
