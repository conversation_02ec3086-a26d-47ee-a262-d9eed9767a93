package com.ctrip.corp.bff.hotel.book.handler.corphotelbookservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.hotelbooking.CorpHotelBookServiceClient;
import com.ctrip.corp.hotelbooking.hotelws.entity.SubmitOrderRequestType;
import com.ctrip.corp.hotelbooking.hotelws.entity.SubmitOrderResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/9/14 14:31
 */
@Component
public class HandlerOfSubmitOrder
    extends AbstractHandlerOfSOA<SubmitOrderRequestType, SubmitOrderResponseType, CorpHotelBookServiceClient> {
    @Override protected String getMethodName() {
        return "submitOrder";
    }
}
