package com.ctrip.corp.bff.hotel.book.common.signature;

import io.protostuff.Tag;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/23 18:31
 */
public class ApproveObjInfoSignatureBO {
    @Tag(1)
    private CorpBookingInfoSignatureBO corpBookingInfoSignatureBO;
    @Tag(2)
    private ApproveObjSignatureBO approveObjSignatureBO;
    @Tag(3)
    private List<AccreditorSignatureBO> accreditorSignatureBOS;

    public CorpBookingInfoSignatureBO getCorpBookingInfoSignatureBO() {
        return corpBookingInfoSignatureBO;
    }

    public void setCorpBookingInfoSignatureBO(CorpBookingInfoSignatureBO corpBookingInfoSignatureBO) {
        this.corpBookingInfoSignatureBO = corpBookingInfoSignatureBO;
    }

    public ApproveObjSignatureBO getApproveObjSignatureBO() {
        return approveObjSignatureBO;
    }

    public void setApproveObjSignatureBO(ApproveObjSignatureBO approveObjSignatureBO) {
        this.approveObjSignatureBO = approveObjSignatureBO;
    }

    public List<AccreditorSignatureBO> getAccreditorSignatureBOS() {
        return accreditorSignatureBOS;
    }

    public void setAccreditorSignatureBOS(List<AccreditorSignatureBO> accreditorSignatureBOS) {
        this.accreditorSignatureBOS = accreditorSignatureBOS;
    }
}
