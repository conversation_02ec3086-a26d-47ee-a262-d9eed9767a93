package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.bff.framework.hotel.common.enums.CertificateTypeEnum;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;

/**
 * <AUTHOR>
 * @date 2024/6/23 22:07
 */
public class CheckDataUtil {

    private static String TYPE_PHONE_HK = "mobile_phone_hk";
    private static String TYPE_PHONE_CN = "mobile_phone_cn";
    private static String TYPE_PHONE_FOREIGN = "mobile_phone_foreign";

    public static String KEY_INVOICE_EMAIL = "invoiceEmail_{0}";
    public static String KEY_INVOICE_EMAIL_PREFIX = "invoiceEmail_";
    private static String KEY_CLIENT_PSG_EMAIL = "clientPsgEmail_{0}_{1}";
    private static String KEY_CLIENT_PSG_PHONE = "clientPsgPhone_{0}_{1}";
    private static String KEY_CLIENT_PSG_CARD = "clientPsgCard_{0}_{1}";
    private static String KEY_INSURANCE_PSG_EMAIL = "insurancePsgEmail_{0}_{1}_{2}";
    private static String KEY_INSURANCE_PSG_PHONE = "insurancePsgPhone_{0}_{1}_{2}";
    private static String KEY_INSURANCE_PSG_CARD = "insurancePsgCard_{0}_{1}_{2}";
    public static final String KEY_CONTACT_PSG_EMAIL = "contactPsgEmail";
    public static final String KEY_CONTACT_PSG_PHONE = "contactPsgPhone";

    private static final String PHONE_COUNTRYCODE_CHINA = "86";
    private static final String PHONE_COUNTRYCODE_HK = "852";


    public static String getCardType(String certificateType) {
        return CertificateTypeEnum.getCheckDataTypeByCertificateType(certificateType);
    }

    public static String getClientPsgEmailKey(String id, int indexOf) {
        return StringUtil.indexedFormat(KEY_CLIENT_PSG_EMAIL, id, indexOf);
    }

    public static String getClientPsgPhoneKey(String id, int indexOf) {
        return StringUtil.indexedFormat(KEY_CLIENT_PSG_PHONE, id, indexOf);
    }

    public static String getClientPsgCardKey(String id, int indexOf) {
        return StringUtil.indexedFormat(KEY_CLIENT_PSG_CARD, id, indexOf);
    }

    public static String getInsurancePsgEmailKey(String id, int indexOfPsg, int indexOfInsurance) {
        return StringUtil.indexedFormat(KEY_INSURANCE_PSG_EMAIL, id, indexOfPsg, indexOfInsurance);
    }

    public static String getInsurancePsgPhoneKey(String id, int indexOfPsg, int indexOfInsurance) {
        return StringUtil.indexedFormat(KEY_INSURANCE_PSG_PHONE, id, indexOfPsg, indexOfInsurance);
    }

    public static String getInsurancePsgCardKey(String id, int indexOfPsg, int indexOfInsurance) {
        return StringUtil.indexedFormat(KEY_INSURANCE_PSG_CARD, id, indexOfPsg, indexOfInsurance);
    }

    public static String getPhoneType(String countryCode) {
        if (StringUtil.isBlank(countryCode)) {
            return TYPE_PHONE_CN;
        }
        switch (countryCode) {
            case PHONE_COUNTRYCODE_HK:
                return TYPE_PHONE_HK;
            case PHONE_COUNTRYCODE_CHINA:
                return TYPE_PHONE_CN;
            default:
                return TYPE_PHONE_FOREIGN;
        }
    }

    public static String getInvoiceEmailKey(String hotelInvoiceType) {
        return StringUtil.indexedFormat(KEY_INVOICE_EMAIL, hotelInvoiceType);
    }
}
