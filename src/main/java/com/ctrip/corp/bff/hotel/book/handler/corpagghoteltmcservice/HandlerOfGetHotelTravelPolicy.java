package com.ctrip.corp.bff.hotel.book.handler.corpagghoteltmcservice;

import com.ctrip.corp.agg.hotel.tmc.CorpAggHotelTMCServiceClient;
import com.ctrip.corp.agg.hotel.tmc.entity.GetHotelTravelPolicyRequestType;
import com.ctrip.corp.agg.hotel.tmc.entity.GetHotelTravelPolicyResponseType;
import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import org.springframework.stereotype.Component;

/**
 /**
 * @Author: chenchuang
 * @Date: 2024/8/22 16:26
 * @Description:获取差标
 */
@Component
public class HandlerOfGetHotelTravelPolicy extends AbstractHandlerOfSOA<GetHotelTravelPolicyRequestType,
        GetHotelTravelPolicyResponseType, CorpAggHotelTMCServiceClient> {

    @Override
    protected String getMethodName() {
        return "getHotelTravelPolicy";
    }
}
