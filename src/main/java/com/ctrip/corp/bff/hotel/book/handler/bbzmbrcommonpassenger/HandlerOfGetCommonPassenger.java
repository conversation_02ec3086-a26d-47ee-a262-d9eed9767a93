package com.ctrip.corp.bff.hotel.book.handler.bbzmbrcommonpassenger;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import ctrip.BBZ.members.bbzmbrCommonPassenger.BbzmbrcommonpassengerClient;
import ctrip.BBZ.members.bbzmbrCommonPassenger.GetCommonPassengerRequestType;
import ctrip.BBZ.members.bbzmbrCommonPassenger.GetCommonPassengerResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024-10-28 19:24:08
 */
@Component
public class HandlerOfGetCommonPassenger extends AbstractHandlerOfSOA<GetCommonPassengerRequestType, GetCommonPassengerResponseType, BbzmbrcommonpassengerClient> {

    @Override
    protected String getMethodName() {
        return "getCommonPassenger";
    }

}
