package com.ctrip.corp.bff.hotel.book.handler.userinfoqueryservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.UserInfoQueryService.BatchSearchClientsInfoRequestType;
import corp.user.service.UserInfoQueryService.BatchSearchClientsInfoResponseType;
import corp.user.service.UserInfoQueryService.UserInfoQueryServiceClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/10/17
 */
@Component
public class HandlerOfBatchSearchClientsInfo extends AbstractHandlerOfSOA<BatchSearchClientsInfoRequestType, BatchSearchClientsInfoResponseType, UserInfoQueryServiceClient> {
    @Override
    protected String getMethodName() {
        return "batchSearchClientsInfo";
    }
}
