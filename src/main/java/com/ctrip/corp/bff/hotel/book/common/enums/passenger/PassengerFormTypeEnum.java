package com.ctrip.corp.bff.hotel.book.common.enums.passenger;

/**
 * @Author: z.c. wang
 * @Description 出行人表单类型
 * @Date: 2025/3/25 16:56
 * @Version 1.0
 */
public enum PassengerFormTypeEnum {
    /**
     * 国籍
     */
    NATIONALITY,

    /**
     * 名
     */
    FIRST_NAME_AND_MIDDLE_NAME,

    /**
     * 姓
     */
    LAST_NAME,

    /**
     * 邮箱
     */
    WORKEMAIL,

    /**
     * 手机
     */
    PHONE,

    /**
     * 生日
     */
    BIRTH,

    /**
     * 英文名
     */
    EN_FIRST_NAME,

    /**
     * 英文姓
     */
    EN_LAST_NAME,

    /**
     * 本地名
     */
    LOCAL_LAST_NAME,

    /**
     * 本地姓
     */
    LOCAL_FIRST_NAME,

    /**
     * 证件类型
     */
    DOCUMENT_TYPE,

    /**
     * 签发地
     */
    ISSUED_PLACE,

    /**
     * 有效期
     */
    EXPIRATION_DATE,

    /**
     * 证件号
     */
    ID_NUMBER,

    /**
     * 性别
     */
    GENDER
    ;

    PassengerFormTypeEnum() {
    }
}
