package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.agg.hotel.roomavailable.entity.AcquirerInfoType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.MultipleLanguageText;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig;
import com.ctrip.corp.bff.framework.hotel.common.util.CityInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.HotelDateRangeUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount.AccountInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.BookInitResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.CreateOrderResult;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.FinishedResultToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.FollowApprovalResult;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.FollowAuthInfoBO;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.PayAmountResult;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.PaymentItemTypeResult;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.shark.DateDisplayUtil;
import com.ctrip.corp.bff.framework.template.common.shark.NumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.common.utils.date.DateUtil;
import com.ctrip.corp.bff.framework.template.entity.PosEnum;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.MiceInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.SSOInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.TripInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple10;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple11;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple12;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple9;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.CustomConfigKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.SharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelBalanceTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.PaymentGuaranteePolyEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.RoomTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.CompletionPageUtil;
import com.ctrip.corp.bff.hotel.book.common.util.HotelPayTypeUtil;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail.CheckAvailContextInfo;
import com.ctrip.corp.bff.hotel.book.contract.BookModeInfo;
import com.ctrip.corp.bff.hotel.book.contract.BookUrlInfo;
import com.ctrip.corp.bff.hotel.book.contract.ClientInfo;
import com.ctrip.corp.bff.hotel.book.contract.FollowApprovalInfoInput;
import com.ctrip.corp.bff.hotel.book.contract.OfflineInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.PayMentInfoInput;
import com.ctrip.corp.bff.mice.basic.auth.contract.TmsCreateOrderVerifyResponseType;
import com.ctrip.corp.bff.payment.contract.AcquirerDetail;
import com.ctrip.corp.bff.payment.contract.HotelExtendInfo;
import com.ctrip.corp.bff.payment.contract.InsuranceInfo;
import com.ctrip.corp.bff.payment.contract.MixPaymentOrderInfo;
import com.ctrip.corp.bff.payment.contract.PaymentExtendInfo;
import com.ctrip.corp.bff.payment.contract.PaymentInfo;
import com.ctrip.corp.bff.payment.contract.PaymentOrderCreateRequestType;
import com.ctrip.corp.bff.payment.contract.PaymentOrderInfo;
import com.ctrip.corp.bff.payment.contract.PaymentUrlInfo;
import com.ctrip.corp.bff.payment.contract.TripCowrieInfo;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderRequestType;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType;
import com.ctrip.corp.order.paymentcenter.bill.contract.QueryPaymentBillConfigResponseType;
import com.ctrip.soa._21234.CreateTripResponseType;
import com.ctrip.soa.corp.booking.preparews.v1.Customer;
import com.google.gson.annotations.SerializedName;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 创单请求类型转换
 * @Date 2024/8/9 10:15
 * @Version 1.0
 */
@Component
public class MapperOfPaymentOrderCreateRequestType extends
    AbstractMapper<Tuple12<CreateOrderResponseType, CheckAvailContextInfo, OrderCreateRequestType,
        AccountInfo, QueryPaymentBillConfigResponseType, CreateOrderRequestType,
        OrderCreateToken, ResourceToken, TmsCreateOrderVerifyResponseType, Boolean,
        CreateTripResponseType, Map<String, StrategyInfo>>, PaymentOrderCreateRequestType> {

    /**
     * 登录态
     */
    private static final String TICKET = "ticket";
    /**
     * 国内登录态
     */
    private static final String C_TICKET = "cticket";
    /**
     * 蓝色空间登录态
     */
    private static final String BIZ_TICKET = "bizticket";
    /**
     * 中国酒店
     */
    private static final String CN_HOTEL = "CN_HOTEL";
    /**
     * 海外酒店
     */
    private static final String INTERNATIONAL_HOTEL = "INTERNATIONAL_HOTEL";

    private static final String CLIENTTYPE_ONLINE = "Online";
    private static final String CLIENTTYPE_OFFLINE = "Offline";
    private static final String CLIENTTYPE_H5 = "H5";
    private static final String CLIENTTYPE_MINIAPP = "MiniApp";
    private static final String CLIENTTYPE_APP = "APP";
    private static final String APPSOURCE_WECHATMINIAPPCLIENT = "WeChatMiniAppClient";
    private static final String APPSOURCE_WECHAT = "Wechat";
    private static final String TRUE_FLAG = "T";
    private static final String FALSE_FLAG = "F";
    private static final String LANGUAGE_ZH_CN = "zh-CN";
    private static final String INSURE_PROVIDER_CTRIP = "1";

    private static final String PAYMENT_TO_SUPPLIER_CORP = "SupplierCorp";
    private static final String PAYMENT_TO_CTRIP_CORP = "CtripCorp";

    private static final String PAY_TYPE_MIX_PAYMENT = "MixPayment";
    private static final String PAY = "pay";
    private static final String GUARANTEE = "guarantee";
    private static final String PREVIOUS_PAY = "previousPay";
    private static final String PRE_AUTHORIZED = "preAuthorized";

    private static final String PAY_MODE_UNION_PAY = "unionPay";
    private static final String PAY_MODE_CTRIP_PAY = "ctripPay";


    /**
     * 服务费
     */
    public static final String SERVICE_FEE = "key.corp.hotel.paymentorder.mixpay.servicefee.title";

    /**
     * 房费
     */
    public static final String ROOM_FEE = "key.corp.hotel.paymentorder.mixpay.roomfee.title";

    /**
     * 订单号
     */
    public static final String ORDER_ID = "key.corp.hotel.paymentorder.orderid.title";

    private static final String CTRIP_CORP = "ctripCorp";
    private static final String MIX_PAYMENT = "mixPayment";
    private static final String SUPPLIER_CORP = "supplierCorp";
    private static final String VERSION = "1.0.0";
    private static final String ORDERTYPE = "Hotel";
    private static String WECHAT_SCAN_CODE = "OGP_WechatScanCode";
    private static String WECHAT_PAY_BY_OTHERS = "WechatPaybyothers";

    private static String CANCEL_BACK = "CANCEL_BACK";
    private static String FROM = "FROM";
    private static String FROM_ORDER_LIST = "FROM_ORDER_LIST";
    private static String CONTACT_ORDER_LOCAL = "orderId={0}&locale={1}";
    private static String CONTACT_LOCAL = "locale={0}";

    @Override
    protected PaymentOrderCreateRequestType convert(
        Tuple12<CreateOrderResponseType, CheckAvailContextInfo, OrderCreateRequestType,
                            AccountInfo, QueryPaymentBillConfigResponseType, CreateOrderRequestType,
                            OrderCreateToken, ResourceToken, TmsCreateOrderVerifyResponseType, Boolean,
                        CreateTripResponseType, Map<String, StrategyInfo>> tuple) {
        CreateOrderResponseType createOrderResponseType = tuple.getT1();
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContext = tuple.getT2();
        OrderCreateRequestType orderCreateRequestType = tuple.getT3();
        WrapperOfAccount.AccountInfo accountInfo = tuple.getT4();
        QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType =
            Optional.ofNullable(tuple.getT5()).orElse(new QueryPaymentBillConfigResponseType());
        OrderCreateToken orderCreateToken = tuple.getT7();
        ResourceToken resourceToken = tuple.getT8();
        boolean offlineNewPay = BooleanUtil.isTrue(tuple.getT10());
        CreateTripResponseType createTripResponseType = tuple.getT11();
        Map<String, StrategyInfo> strategyInfos = tuple.getT12();
        TmsCreateOrderVerifyResponseType tmsCreateOrderVerifyResponseType = tuple.getT9();

        SourceFrom sourceFrom = orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom();

        PaymentOrderCreateRequestType paymentOrderCreateRequestType = new PaymentOrderCreateRequestType();
        paymentOrderCreateRequestType.setIntegrationSoaRequestType(
                orderCreateRequestType.getIntegrationSoaRequestType());

        Integer cityId = orderCreateRequestType.getCityInput().getCityId();
        boolean isOverSea = CityInfoUtil.oversea(cityId);

        paymentOrderCreateRequestType.setStrategyInfos(buildStrategyInfoList(orderCreateRequestType));
        paymentOrderCreateRequestType.setProductType(isOverSea ? INTERNATIONAL_HOTEL : CN_HOTEL);
        String clientType = Optional.ofNullable(orderCreateRequestType.getPaymentInfoInput())
                .map(PayMentInfoInput::getClientType).orElse(null);
        paymentOrderCreateRequestType.setClientType(getClientType(sourceFrom, clientType, offlineNewPay));
        String appSource = Optional.ofNullable(orderCreateRequestType.getClientInfo()).map(ClientInfo::getAppSource)
                .orElse(null);
        paymentOrderCreateRequestType.setAppSource(getAppSource(appSource));

        PaymentInfo paymentInfo =
            buildPaymentInfo(queryPaymentBillConfigResponseType, checkAvailContext, orderCreateRequestType,
                createOrderResponseType, orderCreateToken, resourceToken, accountInfo, offlineNewPay);
        paymentOrderCreateRequestType.setPaymentInfo(paymentInfo);
        paymentOrderCreateRequestType.setPaymentOrderInfo(
            buildPaymentOrderInfo(createOrderResponseType, checkAvailContext, orderCreateRequestType,
                paymentInfo, orderCreateToken, accountInfo, resourceToken));
        paymentOrderCreateRequestType.setPaymentUrlInfo(buildPaymentUrlInfo(orderCreateRequestType,
            createOrderResponseType, orderCreateToken, queryPaymentBillConfigResponseType, resourceToken,
            checkAvailContext, tmsCreateOrderVerifyResponseType, createTripResponseType, accountInfo, strategyInfos));
        paymentOrderCreateRequestType.setPaymentExtendInfo(
                buildPaymentExtendInfo(orderCreateRequestType, accountInfo, checkAvailContext,
                        createOrderResponseType, orderCreateToken));
        paymentOrderCreateRequestType.setPayMode(buildPayMode(orderCreateRequestType));
        paymentOrderCreateRequestType.setFinancialCashierFlag(
            BooleanUtil.parseStr(queryPaymentBillConfigResponseType.getFinancialCashierFlag()));
        return paymentOrderCreateRequestType;
    }

    @Override
    protected ParamCheckResult check(
        Tuple12<CreateOrderResponseType, CheckAvailContextInfo, OrderCreateRequestType,
            AccountInfo, QueryPaymentBillConfigResponseType, CreateOrderRequestType,
            OrderCreateToken, ResourceToken, TmsCreateOrderVerifyResponseType, Boolean,
            CreateTripResponseType, Map<String, StrategyInfo>> tuple) {
        OrderCreateRequestType orderCreateRequestType = tuple.getT3();
        QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType = tuple.getT5();
        checkMiniApp(orderCreateRequestType, queryPaymentBillConfigResponseType);
        return null;
    }

    /**
     * 黑名单不支持微信，当前客户是微信小程序支付，拦截预订
     */
    protected void checkMiniApp(OrderCreateRequestType orderCreateRequestType,
        QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType) {
        if (!CommonConstant.CLIENTTYPE_MINIAPP.equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getPaymentInfoInput()).map(PayMentInfoInput::getClientType)
                .orElse(null))) {
            return;
        }
        if (queryPaymentBillConfigResponseType == null || CollectionUtil.isEmpty(
            queryPaymentBillConfigResponseType.getDisabledPayWayList())) {
            return;
        }
        if (queryPaymentBillConfigResponseType.getDisabledPayWayList().stream().filter(Objects::nonNull)
            .anyMatch(disabledPayWay -> WECHAT_SCAN_CODE.equalsIgnoreCase(disabledPayWay))) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.NO_SUPPORT_PAY,
                OrderCreateErrorEnum.NO_SUPPORT_PAY.getErrorMessage());
        }
    }

    protected String buildPayMode(OrderCreateRequestType orderCreateRequestType) {
        HotelPayTypeEnum roomPayType = HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        if (roomPayType == HotelPayTypeEnum.UNION_PAY) {
            return PAY_MODE_UNION_PAY;
        }
        return PAY_MODE_CTRIP_PAY;
    }
    private List<StrategyInfo> buildStrategyInfoList(OrderCreateRequestType orderCreateRequestType) {
        StrategyInfo strategyInfo = new StrategyInfo();
        strategyInfo.setStrategyKey(TICKET);
        if (orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getPos() == PosEnum.CHINA) {
            strategyInfo.setStrategyValue(C_TICKET);
        } else {
            strategyInfo.setStrategyValue(BIZ_TICKET);
        }
        return Collections.singletonList(strategyInfo);
    }

    private PaymentInfo buildPaymentInfo(QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType,
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContext, OrderCreateRequestType orderCreateRequestType,
        CreateOrderResponseType createOrderResponseType, OrderCreateToken orderCreateToken, ResourceToken resourceToken,
        WrapperOfAccount.AccountInfo accountInfo, Boolean offlineNewPay) {
        PaymentInfo paymentInfo = new PaymentInfo();
        paymentInfo.setBusType(null);
        paymentInfo.setMerchantId(queryPaymentBillConfigResponseType.getMerchantId());
        paymentInfo.setPayTypes(getPayTypes(checkAvailContext, orderCreateRequestType, resourceToken));
        paymentInfo.setPaymentTo(buildPaymentTo(checkAvailContext, orderCreateRequestType, resourceToken));
        List<String> enabledPayWayList = queryPaymentBillConfigResponseType.getEnabledPayWayList();
        if (CollectionUtil.isNotEmpty(enabledPayWayList)) {
            paymentInfo.setWhitePayWay(StringUtils.join(enabledPayWayList, ","));
        }
        List<String> disabledPayWayList = buildDisabledPayWayList(queryPaymentBillConfigResponseType,
            orderCreateRequestType, createOrderResponseType, orderCreateToken, accountInfo, offlineNewPay);
        if (CollectionUtil.isNotEmpty(disabledPayWayList)) {
            paymentInfo.setBlackPayWays(StringUtils.join(disabledPayWayList, ","));
        }
        paymentInfo.setNeedCardPayFee(buildNeedCardPayFee(checkAvailContext, orderCreateRequestType));
        paymentInfo.setPaymentId(buildPaymentId(createOrderResponseType, orderCreateToken, orderCreateRequestType));
        return paymentInfo;
    }

    protected List<String> buildDisabledPayWayList(
        QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType,
        OrderCreateRequestType orderCreateRequestType, CreateOrderResponseType createOrderResponseType,
        OrderCreateToken orderCreateToken, WrapperOfAccount.AccountInfo accountInfo, Boolean offlineNewPay) {
        List<String> disabledPayWayList = new ArrayList<>();
        boolean supportWechatPayByOthers =
            supportWechatPayByOthers(orderCreateRequestType, accountInfo, createOrderResponseType, orderCreateToken,
                offlineNewPay);
        if (CollectionUtil.isEmpty(queryPaymentBillConfigResponseType.getDisabledPayWayList())) {
            if (!supportWechatPayByOthers) {
                disabledPayWayList.add(WECHAT_PAY_BY_OTHERS);
            }
            return disabledPayWayList;
        }
        if (CollectionUtil.containsIgnoreCase(queryPaymentBillConfigResponseType.getDisabledPayWayList(),
            WECHAT_PAY_BY_OTHERS)) {
            return queryPaymentBillConfigResponseType.getDisabledPayWayList();
        }
        disabledPayWayList.addAll(queryPaymentBillConfigResponseType.getDisabledPayWayList());
        if (!supportWechatPayByOthers) {
            disabledPayWayList.add(WECHAT_PAY_BY_OTHERS);
        }
        return disabledPayWayList;
    }

    private String buildNeedCardPayFee(WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo,
        OrderCreateRequestType orderCreateRequestType) {
        if (!StrategyOfBookingInitUtil.needCardPayFee(orderCreateRequestType.getStrategyInfos())) {
            return FALSE_FLAG;
        }
        // 蓝色空间的老逻辑---接入时着重关注
        if (checkAvailContextInfo.isForceVccPay()) {
            return BooleanUtil.parseStr(false);
        }
        if (checkAvailContextInfo.getHotelBalanceTypeEnum() == HotelBalanceTypeEnum.FG) {
            return BooleanUtil.parseStr(false);
        }
        return BooleanUtil.parseStr(true);
    }

    protected String buildPaymentId(CreateOrderResponseType createOrderResponseType, OrderCreateToken orderCreateToken,
        OrderCreateRequestType orderCreateRequestType) {
        CreateOrderResult createOrderResult =
            OrderCreateProcessorOfUtil.buildCreateOrderResult(createOrderResponseType, orderCreateRequestType,
                orderCreateToken);
        return createOrderResult.getTransactionId();
    }

    private PaymentUrlInfo buildPaymentUrlInfo(OrderCreateRequestType orderCreateRequestType,
        CreateOrderResponseType createOrderResponseType, OrderCreateToken orderCreateToken,
        QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType, ResourceToken resourceToken,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        TmsCreateOrderVerifyResponseType tmsCreateOrderVerifyResponseType,
        CreateTripResponseType createTripResponseType,
        WrapperOfAccount.AccountInfo accountInfo, Map<String, StrategyInfo> strategyInfos) {
        PaymentUrlInfo paymentUrlInfo = new PaymentUrlInfo();
        paymentUrlInfo.setRecallUrl(queryPaymentBillConfigResponseType.getRecallUrl());
        paymentUrlInfo.setNotifyUrl(queryPaymentBillConfigResponseType.getNotifyUrl());
        paymentUrlInfo.setSuccessBackUrl(
            buildSuccessBackUrl(orderCreateRequestType.getBookUrlInfos(), orderCreateRequestType,
                createOrderResponseType, orderCreateToken, queryPaymentBillConfigResponseType, resourceToken,
                checkAvailInfo, tmsCreateOrderVerifyResponseType, createTripResponseType, accountInfo));
        if (Arrays.asList(SourceFrom.H5, SourceFrom.CRN, SourceFrom.Native)
            .contains(orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom())) {
            paymentUrlInfo.setErrorBackUrl(buildBookUrl(orderCreateRequestType.getBookUrlInfos(), "E_BACK"));
        }
        paymentUrlInfo.setFromUrl(buildFromUrl(orderCreateRequestType, createOrderResponseType, orderCreateToken,
            checkAvailInfo, resourceToken));
        if (StrategyOfBookingInitUtil.relativeRecall(strategyInfos)) {
            paymentUrlInfo.setRelativeRecallUrl(queryPaymentBillConfigResponseType.getRelativeRecallUrl());
        }
        if (StrategyOfBookingInitUtil.relativeNotify(strategyInfos)) {
            paymentUrlInfo.setRelativeNotifyUrl(queryPaymentBillConfigResponseType.getRelativeNotifyUrl());
        }
        paymentUrlInfo.setCancelBackUrl(
            buildCancelBackUrl(orderCreateRequestType, createOrderResponseType, orderCreateToken, checkAvailInfo,
                resourceToken));
        return paymentUrlInfo;
    }

    /**
     * CANCEL_BACK=xxx域名/m/hotel/order?&backUrl=%2Fm%2Ftrips&
     * 拼接订单相关参数：orderId={0}&locale={1}
     *
     * @return
     */
    protected String buildCancelBackUrl(OrderCreateRequestType orderCreateRequestType,
        CreateOrderResponseType createOrderResponseType, OrderCreateToken orderCreateToken,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo, ResourceToken resourceToken) {
        if (orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getPos() == PosEnum.CHINA) {
            return null;
        }
        if (!Arrays.asList(SourceFrom.H5, SourceFrom.CRN, SourceFrom.Native)
            .contains(orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom())) {
            return null;
        }
        if (needJumpOrderHomePage(orderCreateRequestType, checkAvailInfo, resourceToken)) {
            return buildFromOrderListUrl(orderCreateRequestType);
        }
        StringBuilder cancelBackUrl = new StringBuilder();
        String cancelBack = buildBookUrl(orderCreateRequestType.getBookUrlInfos(), CANCEL_BACK);
        if (StringUtil.isBlank(cancelBack)) {
            return null;
        }
        cancelBackUrl.append(cancelBack);
        CreateOrderResult createOrderResult =
            OrderCreateProcessorOfUtil.buildCreateOrderResult(createOrderResponseType, orderCreateRequestType,
                orderCreateToken);
        return cancelBackUrl.append(StringUtil.indexedFormat(CONTACT_ORDER_LOCAL,
            String.valueOf(Optional.ofNullable(createOrderResult).map(CreateOrderResult::getOrderID).orElse(0L)),
            orderCreateRequestType.getIntegrationSoaRequestType().getLanguage())).toString();
    }

    protected String buildFromOrderListUrl(OrderCreateRequestType orderCreateRequestType) {
        StringBuilder fromOrderListUrl = new StringBuilder();
        String fromOrderList = buildBookUrl(orderCreateRequestType.getBookUrlInfos(), FROM_ORDER_LIST);
        if (StringUtil.isBlank(fromOrderList)) {
            return null;
        }
        return fromOrderListUrl.append(fromOrderList).append(StringUtil.indexedFormat(CONTACT_LOCAL,
            orderCreateRequestType.getIntegrationSoaRequestType().getLanguage())).toString();
    }

    /**
     * 自定义取消支付跳转地址,默认跳转订单详情。
     *
     * 跳订单列表页的场景：
     * 1.会员担保到酒店个人担保
     * 2.协议无担保，服务费个人
     * 3.协议担保到酒店个人担保服务费公账
     * 4.协议担保到酒店个人担保服务费个人
     * 支持继续支付的跳订单详情页
     */
    protected boolean needJumpOrderHomePage(OrderCreateRequestType orderCreateRequestType,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo, ResourceToken resourceToken) {
        HotelPayTypeEnum roomPayType = HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        HotelPayTypeEnum serviceFeeType =
            HotelPayTypeUtil.getServicePayType(orderCreateRequestType.getHotelPayTypeInput(), roomPayType,
                resourceToken);
        if (!Arrays.asList(HotelPayTypeEnum.CASH, HotelPayTypeEnum.GUARANTEE_SELF_PAY,
            HotelPayTypeEnum.GUARANTEE_CORP_PAY).contains(roomPayType)) {
            return false;
        }
        // 会员+担保到酒店+个人担保
        if (checkAvailInfo.getRoomTypeEnum() == RoomTypeEnum.M) {
            return roomPayType == HotelPayTypeEnum.GUARANTEE_SELF_PAY
                && checkAvailInfo.getPaymentGuaranteePolyEnum() == PaymentGuaranteePolyEnum.PAY_TO_HOTEL;
        }
        if (checkAvailInfo.getRoomTypeEnum() == RoomTypeEnum.C) {
            // 协议+无担保+服务费个人
            if (roomPayType == HotelPayTypeEnum.CASH && serviceFeeType == HotelPayTypeEnum.SELF_PAY) {
                return true;
            }
            // 协议+担保到酒店+个人担保
            if (roomPayType == HotelPayTypeEnum.GUARANTEE_SELF_PAY
                && checkAvailInfo.getPaymentGuaranteePolyEnum() == PaymentGuaranteePolyEnum.PAY_TO_HOTEL) {
                return true;
            }
        }
        return false;
    }

    /**
     * FROM_URL=appxxx域名/m/hotel/order?&backUrl=%2Fm%2Ftrips&
     * orderId={0}&locale={1}
     * <p>
     * <p>
     * FROM_URL=pcxxx域名/hotel/order?
     * 拼接订单相关参数：orderId={0}&locale={1}
     *
     * @return
     */
    protected String buildFromUrl(OrderCreateRequestType orderCreateRequestType,
        CreateOrderResponseType createOrderResponseType, OrderCreateToken orderCreateToken,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo, ResourceToken resourceToken) {
        if (orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getPos() == PosEnum.CHINA) {
            return buildFromUrl(orderCreateRequestType.getBookUrlInfos(), orderCreateRequestType,
                createOrderResponseType, orderCreateToken);
        }
        if (needJumpOrderHomePage(orderCreateRequestType, checkAvailInfo, resourceToken)) {
            return buildFromOrderListUrl(orderCreateRequestType);
        }
        StringBuilder fromUrl = new StringBuilder();
        String from = buildBookUrl(orderCreateRequestType.getBookUrlInfos(), FROM);
        if (StringUtil.isBlank(from)) {
            return null;
        }
        fromUrl.append(from);
        CreateOrderResult createOrderResult =
            OrderCreateProcessorOfUtil.buildCreateOrderResult(createOrderResponseType, orderCreateRequestType,
                orderCreateToken);
        return fromUrl.append(StringUtil.indexedFormat(CONTACT_ORDER_LOCAL,
            String.valueOf(Optional.ofNullable(createOrderResult).map(CreateOrderResult::getOrderID).orElse(0L)),
            orderCreateRequestType.getIntegrationSoaRequestType().getLanguage())).toString();
    }

    protected String buildFromUrl(List<BookUrlInfo> bookUrlInfos, OrderCreateRequestType orderCreateRequestType,
        CreateOrderResponseType createOrderResponseType, OrderCreateToken orderCreateToken) {
        CreateOrderResult createOrderResult =
            OrderCreateProcessorOfUtil.buildCreateOrderResult(createOrderResponseType, orderCreateRequestType,
                orderCreateToken);
        if (Arrays.asList(SourceFrom.H5, SourceFrom.CRN, SourceFrom.Native)
            .contains(orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom())) {
            String fromUrlOrderDetail = buildBookUrl(bookUrlInfos, "FROM_URL_ORDER_DETAIL");
            if (StringUtil.isBlank(fromUrlOrderDetail)) {
                return null;
            }
            return StringUtil.indexedFormat(fromUrlOrderDetail, String.valueOf(createOrderResult.getOrderID()),
                orderCreateRequestType.getIntegrationSoaRequestType().getLanguage());
        }
        if (orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom() == SourceFrom.Online) {
            String fromUrl = buildBookUrl(bookUrlInfos, "FROM_URL");
            if (StringUtil.isBlank(fromUrl)) {
                return null;
            }
            return fromUrl + String.valueOf(createOrderResult.getOrderID());
        }
        return null;
    }

    private static final String CONTACT_SBACK_ONLINE =
        "OrderID={0}&MerchantID={1}&TransactionID={2}&finishedResultToken={3}";
    private static final String CONTACT_SBACK_OFFLINE =
        "OrderID={0}&uid={1}&cookieId={2}&corpLocale={3}&MerchantID={4}&TransactionID={5}";
    private static final String CONTACT_SBACK_APP =
        "OrderNumber={0}&Uid={1}&CorpId={2}&OldOrderId={3}&WSTransactionID={4}&IsOversea={5}{6}&HtlType={7}"
            + "&AuthPass={8}&cityId={9}&hotelId={10}&checkIn={11}&checkOut={12}&mssokey={13}";

    protected String buildBookUrl(List<BookUrlInfo> bookUrlInfos, String urlType) {
        if (CollectionUtil.isEmpty(bookUrlInfos)) {
            return null;
        }
        if (StringUtil.isBlank(urlType)) {
            return null;
        }
        BookUrlInfo bookUrlInfo =
            bookUrlInfos.stream().filter(urlInfo -> urlInfo != null && urlType.equalsIgnoreCase(urlInfo.getUrlType()))
                .collect(Collectors.toList()).stream().findFirst().orElse(null);
        if (bookUrlInfo == null) {
            return null;
        }
        return bookUrlInfo.getUrlValue();
    }

    protected String buildSuccessBackUrl(List<BookUrlInfo> bookUrlInfos, OrderCreateRequestType orderCreateRequestType,
        CreateOrderResponseType createOrderResponseType, OrderCreateToken orderCreateToken,
        QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType, ResourceToken resourceToken,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        TmsCreateOrderVerifyResponseType tmsCreateOrderVerifyResponseType,
        CreateTripResponseType createTripResponseType, WrapperOfAccount.AccountInfo accountInfo) {
        if (CollectionUtil.isEmpty(bookUrlInfos)) {
            return null;
        }
        if (StringUtil.isNotBlank(
            Optional.ofNullable(orderCreateRequestType.getMiceInput()).map(MiceInput::getMiceActivityId).orElse(null))) {
            if (StringUtil.isNotBlank(
                Optional.ofNullable(tmsCreateOrderVerifyResponseType).map(TmsCreateOrderVerifyResponseType::getActivityDetailUrl)
                    .orElse(null))) {
                return tmsCreateOrderVerifyResponseType.getActivityDetailUrl();
            }
            return buildBookUrl(bookUrlInfos, "S_BACK_MICE");
        }

        String finishedResultToken = getFinishedResultTokenStr(orderCreateRequestType, orderCreateToken);
        CreateOrderResult createOrderResult =
            OrderCreateProcessorOfUtil.buildCreateOrderResult(createOrderResponseType, orderCreateRequestType,
                orderCreateToken);
        String sback = CompletionPageUtil.buildSBackUrl(orderCreateRequestType, createOrderResult.getOrderID());
        if (StringUtil.isBlank(sback)) {
            return StringUtil.EMPTY;
        }
        if (orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom() == SourceFrom.Online) {
            return sback + StringUtil.indexedFormat(CONTACT_SBACK_ONLINE,
                String.valueOf(createOrderResult.getOrderID()), queryPaymentBillConfigResponseType.getMerchantId(),
                buildPaymentId(createOrderResponseType, orderCreateToken, orderCreateRequestType), finishedResultToken);
        }
        if (orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom() == SourceFrom.Offline) {
            return sback + StringUtil.indexedFormat(CONTACT_SBACK_OFFLINE,
                String.valueOf(createOrderResult.getOrderID()),
                orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId(),
                Optional.ofNullable(orderCreateRequestType.getOfflineInfo()).map(OfflineInfo::getCookieId)
                    .orElse(StringUtil.EMPTY), orderCreateRequestType.getIntegrationSoaRequestType().getLanguage(),
                queryPaymentBillConfigResponseType.getMerchantId(),
                buildPaymentId(createOrderResponseType, orderCreateToken, orderCreateRequestType));
        }
        String oldOrderId = StringUtil.EMPTY;
        if (resourceToken.getOrderResourceToken() != null && TemplateNumberUtil.isNotZeroAndNull(
            resourceToken.getOrderResourceToken().getOrderId())) {
            oldOrderId = String.valueOf(resourceToken.getOrderResourceToken().getOrderId());
        }
        String travelIDString =
            travelIDParam(orderCreateRequestType, accountInfo, createTripResponseType, orderCreateToken);
        boolean htlType = "C".equalsIgnoreCase(checkAvailInfo.getRoomType());
        return sback + StringUtil.indexedFormat(CONTACT_SBACK_APP, String.valueOf(createOrderResult.getOrderID()),
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId(),
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId(), oldOrderId,
            resourceToken.getReservationResourceToken().getWsId(),
            CityInfoUtil.oversea(orderCreateRequestType.getCityInput().getCityId()), travelIDString, htlType,
            buildFlowPass(orderCreateToken, orderCreateRequestType),
            String.valueOf(orderCreateRequestType.getCityInput().getCityId()),
            String.valueOf(resourceToken.getHotelResourceToken().getHotelId()),
            orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo().getCheckIn(),
            orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo().getCheckOut(),
            Optional.ofNullable(orderCreateRequestType.getSsoInput()).map(SSOInput::getSsoKey).orElse(null));
    }

    protected String travelIDParam(OrderCreateRequestType orderCreateRequestType, WrapperOfAccount.AccountInfo accountInfo,
        CreateTripResponseType createTripResponseType, OrderCreateToken orderCreateToken) {
        String travelIDString = "";
        Long travelID =
            OrderCreateProcessorOfUtil.getTripId(accountInfo, orderCreateRequestType, createTripResponseType,
                orderCreateToken);
        Optional.ofNullable(orderCreateRequestType.getTripInput()).map(TripInput::getTripId).orElse(null);
        if (travelID != null && travelID > 0 && !OrderCreateProcessorOfUtil.buildIsTripFollow(orderCreateRequestType,
            orderCreateToken)) {
            travelIDString = "&Travel=" + travelID;
        }
        if (StringUtil.isNotEmpty(
            Optional.ofNullable(orderCreateRequestType.getPaymentInfoInput()).map(PayMentInfoInput::getJumpType)
                .orElse(null))) {
            travelIDString +=
                StringUtil.indexedFormat("&JumpType={0}", orderCreateRequestType.getPaymentInfoInput().getJumpType());
        }
        return travelIDString;
    }

    protected String getFinishedResultTokenStr(OrderCreateRequestType orderCreateRequestType,
        OrderCreateToken orderCreateToken) {
        FinishedResultToken finishedResultToken = new FinishedResultToken();
        finishedResultToken.setPayType(buildFinishResultTokenPayType(orderCreateRequestType));
        finishedResultToken.setOverSea(CityInfoUtil.oversea(orderCreateRequestType.getCityInput().getCityId()));
        finishedResultToken.setFollowAuthInfoBO(buildFollowAuthInfoBO(orderCreateToken, orderCreateRequestType));
        return TokenParseUtil.generateToken(finishedResultToken, FinishedResultToken.class);
    }

    protected String buildFinishResultTokenPayType(OrderCreateRequestType orderCreateRequestType) {
        HotelPayTypeEnum roomPayType = HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        switch (roomPayType) {
            case CORP_PAY:
                return "corp";
            case SELF_PAY:
                return "ccard";
            case MIX_PAY:
                return "mixed";
            case CASH:
            case GUARANTEE_CORP_PAY:
            case GUARANTEE_SELF_PAY:
                return "cash";
            case FLASH_STAY_PAY:
                return "FLASH_STAY_PAY";
            default:
                return "cash";
        }
    }

    protected FollowAuthInfoBO buildFollowAuthInfoBO(OrderCreateToken orderCreateToken,
        OrderCreateRequestType orderCreateRequestType) {
        if (orderCreateToken.getFollowApprovalResult() == null) {
            return null;
        }
        // 用户选择了沿用, 并且是在可沿用场景下
        FollowAuthInfoBO bffPayBillInfoBO = new FollowAuthInfoBO();
        bffPayBillInfoBO.setPass(buildFlowPass(orderCreateToken, orderCreateRequestType));
        bffPayBillInfoBO.setTripId(StringUtil.isNotBlank(orderCreateToken.getFollowApprovalResult().getTripId()) ?
            Long.parseLong(orderCreateToken.getFollowApprovalResult().getTripId()) : 0L);
        return bffPayBillInfoBO;
    }

    private boolean buildFlowPass(OrderCreateToken orderCreateToken, OrderCreateRequestType orderCreateRequestType) {
        String followSelected = Optional.ofNullable(orderCreateRequestType.getFollowApprovalInfoInput())
            .map(FollowApprovalInfoInput::getFollowSelected).orElse(null);
        String canFollowApproval = Optional.ofNullable(orderCreateToken.getFollowApprovalResult())
            .map(FollowApprovalResult::getCanFollowApproval).orElse(null);
        return BooleanUtil.parseStr(Boolean.TRUE).equals(followSelected) && BooleanUtil.parseStr(Boolean.TRUE)
                .equals(canFollowApproval);
    }

    /**
     * 账户配置是否多币种
     *
     * @param accountInfo
     * @return
     */
    private boolean isCustomCurrency(WrapperOfAccount.AccountInfo accountInfo) {
        return StringUtil.isNotBlank(accountInfo.getCurrency()) && isCustomCurrency(accountInfo.getCurrency());
    }

    /**
     * 是否多币种
     *
     * @return
     */
    private boolean isCustomCurrency(String currency) {
        return !StringUtil.equalsIgnoreCase(currency, "CNY") && !StringUtil.equalsIgnoreCase(currency, "RMB");
    }

    private PaymentOrderInfo buildPaymentOrderInfo(CreateOrderResponseType createOrderResponseType,
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContext, OrderCreateRequestType orderCreateRequestType,
        PaymentInfo paymentInfo, OrderCreateToken orderCreateToken,
        WrapperOfAccount.AccountInfo accountInfo, ResourceToken resourceToken) {
        PaymentOrderInfo paymentOrderInfo = new PaymentOrderInfo();
        String roomType = checkAvailContext.getRoomType();
        boolean isContextLanguageZHCN = isContextZHCN(orderCreateRequestType);
        boolean isToIBU = isCustomCurrency(accountInfo) || !isContextLanguageZHCN;

        paymentOrderInfo.setDisplayOrderTitle(buildCustomTitle(orderCreateRequestType, checkAvailContext));
        paymentOrderInfo.setOrderTitle(getOrderTitle(roomType));
        paymentOrderInfo.setOrderPromptInfo(
            buildOrderPromptInfo(orderCreateRequestType, checkAvailContext, orderCreateToken, createOrderResponseType));
        paymentOrderInfo.setOrderId(
            String.valueOf(OrderCreateProcessorOfUtil.buildOrderId(orderCreateToken, createOrderResponseType)));
        if (orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom() == SourceFrom.Online) {
            paymentOrderInfo.setOrderSubTitle("");
        }
        CreateOrderResult createOrderResult =
            OrderCreateProcessorOfUtil.buildCreateOrderResult(createOrderResponseType, orderCreateRequestType,
                orderCreateToken);
        Optional.ofNullable(createOrderResult.getPayAmountResult()).ifPresent(personalPayAmountInfo -> {
            paymentOrderInfo.setOrderAmount(personalPayAmountInfo.getAmount());
            paymentOrderInfo.setOrderCurrency(personalPayAmountInfo.getCurrency());
            paymentOrderInfo.setExchangeRate(personalPayAmountInfo.getExchangeRateToCNY());
        });
        if (isToIBU) {
            paymentOrderInfo.setOrderSummary(
                buildOrderSummary(createOrderResponseType, orderCreateRequestType, checkAvailContext,
                    orderCreateToken));
        }
        paymentOrderInfo.setExternalNo(createOrderResult.getPaymentExternalNo());
        setMixPaymentOrderInfos(paymentOrderInfo, createOrderResponseType, paymentInfo, orderCreateToken,
            orderCreateRequestType, checkAvailContext, resourceToken);
        paymentOrderInfo.setPaymentDescription(
            Optional.ofNullable(resourceToken).map(ResourceToken::getBookInitResourceToken)
                .map(BookInitResourceToken::getCancelTip).orElse(null));
        return paymentOrderInfo;
    }

    private String getOrderTitle(String roomType) {
        if (roomType == null) {
            return "";
        }
        return "M".equalsIgnoreCase(roomType) ? BFFSharkUtil.getSharkValue(SharkKeyConstant.PAYMENT_ORDER_TITLE_M) :
            BFFSharkUtil.getSharkValue(SharkKeyConstant.PAYMENT_ORDER_TITLE_C);
    }

    private BigDecimal getServiceFeeSelf(CreateOrderResponseType createOrderResponseType, OrderCreateToken orderCreateToken,
        OrderCreateRequestType orderCreateRequestType, ResourceToken resourceToken) {
        HotelPayTypeEnum servicePayType =
            HotelPayTypeUtil.getServicePayType(orderCreateRequestType.getHotelPayTypeInput(), resourceToken);
        if (servicePayType != HotelPayTypeEnum.SELF_PAY) {
            return BigDecimal.ZERO;
        }
        CreateOrderResult createOrderResult =
            OrderCreateProcessorOfUtil.buildCreateOrderResult(createOrderResponseType, orderCreateRequestType,
                orderCreateToken);
        if (CollectionUtil.isEmpty(createOrderResult.getPaymentItemTypeResults())) {
            return BigDecimal.ZERO;
        }
        return createOrderResult.getPaymentItemTypeResults().stream()
            .filter(item -> StringUtil.equalsIgnoreCase(item.getFeeType(), "BookServiceFee")).findFirst()
            .map(PaymentItemTypeResult::getPayAmountResult).map(PayAmountResult::getAmount).orElse(BigDecimal.ZERO);
    }

    private BigDecimal getRoomFee(CreateOrderResponseType createOrderResponseType, OrderCreateToken orderCreateToken,
        OrderCreateRequestType orderCreateRequestType, ResourceToken resourceToken) {
        CreateOrderResult createOrderResult =
            OrderCreateProcessorOfUtil.buildCreateOrderResult(createOrderResponseType, orderCreateRequestType,
                orderCreateToken);
        if (CollectionUtil.isEmpty(createOrderResult.getPaymentItemTypeResults())) {
            return BigDecimal.ZERO;
        }
        BigDecimal orderAmount =
            Optional.ofNullable(createOrderResult.getPayAmountResult()).map(PayAmountResult::getAmount)
                .orElse(BigDecimal.ZERO);
        return orderAmount.subtract(
            getServiceFeeSelf(createOrderResponseType, orderCreateToken, orderCreateRequestType, resourceToken));
    }

    private MixPaymentOrderInfo buildMixPaymentOrderInfoRoomFee(PaymentInfo paymentInfo,
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContext, ResourceToken resourceToken,
        OrderCreateRequestType orderCreateRequestType, BigDecimal roomFee, String currency) {
        if (isMixPay(checkAvailContext, orderCreateRequestType, resourceToken)) {
            // 服务费到携程 担保到酒店 混合支付
            return buildMixPaymentOrderInfo(BFFSharkUtil.getSharkValue(ROOM_FEE), roomFee, Arrays.asList("Guarantee"),
                    PAYMENT_TO_SUPPLIER_CORP, currency);
        }
        // 添加房费
        return buildMixPaymentOrderInfo(BFFSharkUtil.getSharkValue(ROOM_FEE), roomFee, paymentInfo.getPayTypes(),
                paymentInfo.getPaymentTo(), currency);
    }

    private void setMixPaymentOrderInfos(PaymentOrderInfo paymentOrderInfo,
        CreateOrderResponseType createOrderResponseType, PaymentInfo paymentInfo, OrderCreateToken orderCreateToken,
        OrderCreateRequestType orderCreateRequestType, WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContext,
        ResourceToken resourceToken) {
        HotelPayTypeEnum roomPayType = HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        List<MixPaymentOrderInfo> mixPaymentOrderInfos = new ArrayList<>();
        BigDecimal serviceFee =
            getServiceFeeSelf(createOrderResponseType, orderCreateToken, orderCreateRequestType, resourceToken);
        BigDecimal roomFee =
            getRoomFee(createOrderResponseType, orderCreateToken, orderCreateRequestType, resourceToken);
        String currency = OrderCreateProcessorOfUtil.buildOrderCurrency(createOrderResponseType, orderCreateRequestType,
            orderCreateToken);
        // 服务费+房费 属于混合支付范围
        if (serviceFee.compareTo(BigDecimal.ZERO) > 0 && roomFee.compareTo(BigDecimal.ZERO) > 0) {
            // 添加服务费
            mixPaymentOrderInfos.add(buildMixPaymentOrderInfo(BFFSharkUtil.getSharkValue(SERVICE_FEE), serviceFee,
                Collections.singletonList(PAY), PAYMENT_TO_CTRIP_CORP, currency));
            mixPaymentOrderInfos.add(
                buildMixPaymentOrderInfoRoomFee(paymentInfo, checkAvailContext, resourceToken, orderCreateRequestType,
                    roomFee, currency));
        }

        // 单独只有服务费 将服务费作为订单金额传参 是普通支付
        if (serviceFee.compareTo(BigDecimal.ZERO) > 0 && roomFee.compareTo(BigDecimal.ZERO) == 0) {
            mixPaymentOrderInfos.add(buildMixPaymentOrderInfo(BFFSharkUtil.getSharkValue(SERVICE_FEE), serviceFee,
                Collections.singletonList(PAY), PAYMENT_TO_CTRIP_CORP, currency));
        }

        // 只有房费 无服务费
        if (serviceFee.compareTo(BigDecimal.ZERO) == 0 && roomFee.compareTo(BigDecimal.ZERO) > 0) {
            mixPaymentOrderInfos.add(
                buildMixPaymentOrderInfo(BFFSharkUtil.getSharkValue(ROOM_FEE), roomFee, paymentInfo.getPayTypes(),
                    paymentInfo.getPaymentTo(), currency));
        }

        Boolean flashOrder = roomPayType == HotelPayTypeEnum.FLASH_STAY_PAY;
        if (flashOrder && roomFee.compareTo(BigDecimal.ZERO) > 0) {
            mixPaymentOrderInfos.add(
                buildMixPaymentOrderInfo(BFFSharkUtil.getSharkValue(ROOM_FEE), roomFee, paymentInfo.getPayTypes(),
                    paymentInfo.getPaymentTo(), currency));
        }
        paymentOrderInfo.setMixPaymentOrderInfos(mixPaymentOrderInfos);
        paymentOrderInfo.setAutoApplyBill(BooleanUtil.parseStr(true));
    }

    private MixPaymentOrderInfo buildMixPaymentOrderInfo(String title, BigDecimal fee, List<String> payTypes,
            String paymentTo, String currency) {
        MixPaymentOrderInfo mixPaymentOrderInfo = new MixPaymentOrderInfo();
        mixPaymentOrderInfo.setTitle(title);
        mixPaymentOrderInfo.setAmount(fee);
        mixPaymentOrderInfo.setCurrency(currency);
        mixPaymentOrderInfo.setPayTypes(payTypes);
        mixPaymentOrderInfo.setPaymentTo(paymentTo);
        return mixPaymentOrderInfo;
    }

    protected PaymentExtendInfo buildPaymentExtendInfo(OrderCreateRequestType orderCreateRequestType,
            WrapperOfAccount.AccountInfo accountInfo,
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContext,
            CreateOrderResponseType createOrderResponseType,
            OrderCreateToken orderCreateToken) {
        PaymentExtendInfo paymentExtendInfo = new PaymentExtendInfo();
        paymentExtendInfo.setInsuranceInfos(
            buildInsuranceInfos(createOrderResponseType, orderCreateToken, orderCreateRequestType));

        if (StrategyOfBookingInitUtil.bookingWithPersonalAccount(orderCreateRequestType.getStrategyInfos())) {
            paymentExtendInfo.setTripCowrieInfo(
                    buildTripCowrieInfo(accountInfo, checkAvailContext));
        }
        paymentExtendInfo.setAcquirerDetails(buildAcquirerDetails(checkAvailContext, orderCreateRequestType));
        // 对比降噪
        if (orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getPos() != PosEnum.CHINA
            && checkAvailContext.getRoomTypeEnum() != null) {
            HotelExtendInfo hotelExtendInfo = new HotelExtendInfo();
            hotelExtendInfo.setRoomType(checkAvailContext.getRoomTypeEnum().name());
            paymentExtendInfo.setHotelExtendInfo(hotelExtendInfo);
        }
        return paymentExtendInfo;
    }

    protected List<AcquirerDetail> buildAcquirerDetails(WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContext,
        OrderCreateRequestType orderCreateRequestType) {
        if (!QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.SUPPORT_PAYMENT_3DS,
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId())) {
            return null;
        }
        if (CollectionUtil.isEmpty(checkAvailContext.getAcquirerInfoList())) {
            return null;
        }
        if (checkAvailContext.getPaymentGuaranteePolyEnum() != PaymentGuaranteePolyEnum.PAY_TO_HOTEL) {
            return null;
        }
        return Optional.ofNullable(checkAvailContext.getAcquirerInfoList()).orElse(Collections.emptyList()).stream()
            .filter(Objects::nonNull).map(this::buildAcquirerDetail).collect(Collectors.toList());
    }

    protected List<InsuranceInfo> buildInsuranceInfos(CreateOrderResponseType createOrderResponseType,
        OrderCreateToken orderCreateToken, OrderCreateRequestType orderCreateRequestType) {
        CreateOrderResult createOrderResult =
            OrderCreateProcessorOfUtil.buildCreateOrderResult(createOrderResponseType, orderCreateRequestType,
                orderCreateToken);
        if (CollectionUtil.isNotEmpty(createOrderResult.getInsuranceRoutAmountResults())) {
            return createOrderResult.getInsuranceRoutAmountResults().stream().map(amount -> {
                InsuranceInfo insuranceInfo = new InsuranceInfo();
                insuranceInfo.setAmount(amount.getTotalAmount());
                insuranceInfo.setId(INSURE_PROVIDER_CTRIP);
                return insuranceInfo;
            }).collect(Collectors.toList());
        }
        return null;
    }

    private String getClientType(SourceFrom sourceFrom, String clientTypeRequest, Boolean offlineNewPay) {
        String clientType = "";
        if (sourceFrom == SourceFrom.Offline && BooleanUtil.isTrue(offlineNewPay)) {
            clientType = CLIENTTYPE_OFFLINE;
        } else if (sourceFrom == SourceFrom.Online || sourceFrom == SourceFrom.Offline) {
            clientType = CLIENTTYPE_ONLINE;
        } else if (StringUtil.isBlank(clientTypeRequest) || StringUtil.equalsIgnoreCase(CLIENTTYPE_H5,
                clientTypeRequest)) {
            clientType = CLIENTTYPE_H5;
        } else if (StringUtil.equalsIgnoreCase(CLIENTTYPE_MINIAPP, clientTypeRequest)) {
            clientType = "MINI_APP";
        } else {
            clientType = CLIENTTYPE_APP;
        }
        return clientType;
    }

    private String getAppSource(String appSource) {
        if (StringUtil.equalsIgnoreCase(appSource, APPSOURCE_WECHATMINIAPPCLIENT)) {
            return APPSOURCE_WECHAT;
        }
        return null;
    }

    /**
     * 支付类型，必传：
     * pay:普通支付
     * guarantee:担保支付
     * preAuthorized:预授权
     * previousPay:先付款
     * afterPay:后付款
     * mixPayment:混合支付
     *
     * @param checkAvailContext
     * @return
     */
    private List<String> getPayTypes(WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContext,
        OrderCreateRequestType orderCreateRequestType, ResourceToken resourceToken) {
        if (isMixPay(checkAvailContext, orderCreateRequestType, resourceToken)) {
            return Arrays.asList(MIX_PAYMENT);
        }
        // 纯服务支付
        if (OrderCreateProcessorOfUtil.buildOnlyServiceSelfPay(orderCreateRequestType, resourceToken)) {
            return Arrays.asList(PAY);
        }
        List<String> payTypes = new ArrayList<>();
        HotelPayTypeEnum roomPayType = HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        // 现付担保
        if (roomPayType == HotelPayTypeEnum.GUARANTEE_SELF_PAY) {
            // 担保到携程
            if (checkAvailContext.getPaymentGuaranteePolyEnum() == PaymentGuaranteePolyEnum.PAY_TO_CTRIP) {
                payTypes.add(PREVIOUS_PAY);
                payTypes.add(PRE_AUTHORIZED);
            }
            payTypes.add(GUARANTEE);
        } else {
            // 预付
            payTypes.add(PAY);
        }
        return payTypes;
    }

    /**
     * 收款方 ctripCorp:携程公司 supplierCorp:供应商 mixPayment:混合支付
     *
     * @param checkAvailContext
     * @param orderCreateRequestType
     * @param resourceToken
     * @return
     */
    private String buildPaymentTo(WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContext,
        OrderCreateRequestType orderCreateRequestType, ResourceToken resourceToken) {
        // 混合支付
        if (isMixPay(checkAvailContext, orderCreateRequestType, resourceToken)) {
            return MIX_PAYMENT;
        }
        // 纯服务支付
        if (OrderCreateProcessorOfUtil.buildOnlyServiceSelfPay(orderCreateRequestType, resourceToken)) {
            return CTRIP_CORP;
        }
        // 担保到酒店
        if (isGuaranteeToHotel(orderCreateRequestType, checkAvailContext)) {
            return SUPPLIER_CORP;
        }
        // 房费担保到携程 或者 预付到携程
        return CTRIP_CORP;
    }

    /**
     * 房费担保到酒店
     *
     * @param orderCreateRequestType
     * @param checkAvailContext
     * @return
     */
    private boolean isGuaranteeToHotel(OrderCreateRequestType orderCreateRequestType,
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContext) {
        HotelPayTypeEnum roomPayType = HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        if (roomPayType != HotelPayTypeEnum.GUARANTEE_SELF_PAY) {
            return false;
        }
        return checkAvailContext.getPaymentGuaranteePolyEnum() == PaymentGuaranteePolyEnum.PAY_TO_HOTEL;
    }

    /**
     * 混合场景 服务费到携程+房费担保到酒店
     *
     * @return
     */
    private boolean isMixPay(WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContext,
        OrderCreateRequestType orderCreateRequestType, ResourceToken resourceToken) {
        HotelPayTypeEnum roomPayType = HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        HotelPayTypeEnum servicePayType =
            HotelPayTypeUtil.getServicePayType(orderCreateRequestType.getHotelPayTypeInput(), resourceToken);
        if (servicePayType != HotelPayTypeEnum.SELF_PAY) {
            return false;
        }
        if (roomPayType != HotelPayTypeEnum.GUARANTEE_SELF_PAY) {
            return false;
        }
        return checkAvailContext.getPaymentGuaranteePolyEnum() == PaymentGuaranteePolyEnum.PAY_TO_HOTEL;
    }

    private TripCowrieInfo buildTripCowrieInfo(WrapperOfAccount.AccountInfo accountInfo,
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContext) {
        TripCowrieInfo tripCowrieInfo = new TripCowrieInfo();
        tripCowrieInfo.setAccountId(accountInfo.getAccountId());
        tripCowrieInfo.setSubAccountId(String.valueOf(accountInfo.getSubAccountID()));
        tripCowrieInfo.setCorpOrderType(checkAvailContext.getRoomType());

        return tripCowrieInfo;
    }
    private AcquirerDetail buildAcquirerDetail(AcquirerInfoType acquirerInfoType) {
        AcquirerDetail acquirerDetail = new AcquirerDetail();
        acquirerDetail.setAcquirerBin(acquirerInfoType.getAcquireBin());
        acquirerDetail.setAcquirerMid(acquirerInfoType.getAcquirerMid());
        acquirerDetail.setAcquirerPw(acquirerInfoType.getAcquirerPw());
        acquirerDetail.setScheme(acquirerInfoType.getScheme());
        return acquirerDetail;
    }

    private String buildCustomTitle(OrderCreateRequestType orderCreateRequestType,
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContext) {
        CustomTitleModel customTitleModel = new CustomTitleModel();

        List<CustomTitleForPayH5Model> list = new ArrayList<>();
        CustomTitleForPayH5Model customTitleForPayH5Model = new CustomTitleForPayH5Model();
        list.add(customTitleForPayH5Model);

        if (orderCreateRequestType.getHotelBookInput() != null) {
            String checkIn = orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo().getCheckIn();
            String checkOut = orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo().getCheckOut();
            Date checkInDate = DateUtil.parseDate(checkIn, DateUtil.YYYY_MM_DD);
            Date checkOutDate = DateUtil.parseDate(checkOut, DateUtil.YYYY_MM_DD);

            customTitleForPayH5Model.setCheckin(buildCheckIn(orderCreateRequestType));
            customTitleForPayH5Model.setCheckout(buildCheckOut(orderCreateRequestType));
            customTitleForPayH5Model.setNight((int) DateUtil.betweenDay(checkInDate, checkOutDate));
            customTitleForPayH5Model.setRoom(orderCreateRequestType.getHotelBookInput().getRoomQuantity());
            customTitleForPayH5Model.setTitle(checkAvailContext.getHotelName().getTextGB());
            // customTitleForPayH5Model.setContent(checkAvailContext.getHotelName().getTextGB());
        }
        customTitleModel.setCustomTitle(list);
        customTitleModel.setTitleType(1);
        return JsonUtil.toJson(customTitleModel);
    }

    private String buildOrderPromptInfo(OrderCreateRequestType orderCreateRequestType,
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContext, OrderCreateToken orderCreateToken,
        CreateOrderResponseType createOrderResponseType) {
        if (!isOnline(orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom())) {
            return null;
        }
        if (isContextZHCN(orderCreateRequestType)) {
            return null;
        }
        OrderPromptInfo orderPromptInfo = new OrderPromptInfo();
        orderPromptInfo.setVersion(VERSION);
        orderPromptInfo.setOrderType(ORDERTYPE);

        TripDescription tripDescription = new TripDescription();
        HotelData hotelData = new HotelData();
        tripDescription.setHotelData(hotelData);
        if (orderCreateRequestType.getHotelBookInput() != null) {
            String checkIn = orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo().getCheckIn();
            String checkOut = orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo().getCheckOut();

            hotelData.setRoomCount(String.valueOf(orderCreateRequestType.getHotelBookInput().getRoomQuantity()));
            hotelData.setNightCount(String.valueOf(
                HotelDateRangeUtil.getDays(orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo())));
            hotelData.setCheckInDate(checkIn);
            hotelData.setCheckOutDate(checkOut);


        }
        String hotelName =
            Optional.ofNullable(checkAvailContext.getHotelName()).map(MultipleLanguageText::getTextGB).orElse("");
        hotelData.setHotelName(hotelName);
        String roomName =
            Optional.ofNullable(checkAvailContext.getRoomName()).map(MultipleLanguageText::getTextGB).orElse("");
        hotelData.setRoomType(roomName);
        StringBuffer hotelExtend = new StringBuffer();
        hotelExtend.append(StringUtil.indexedFormat(BFFSharkUtil.getSharkValue(ORDER_ID),
            String.valueOf(OrderCreateProcessorOfUtil.buildOrderId(orderCreateToken, createOrderResponseType))));
        hotelData.setHotelExtend(hotelExtend.toString());
        orderPromptInfo.setTripDescription(tripDescription);
        return JsonUtil.toJson(orderPromptInfo);
    }

    private String buildOrderSummary(CreateOrderResponseType createOrderResponseType,
            OrderCreateRequestType orderCreateRequestType,
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContext, OrderCreateToken orderCreateToken) {
        OrderSummary orderSummary = new OrderSummary();
        orderSummary.setStencil(3);

        OrderSummaryHeader header = new OrderSummaryHeader();
        header.setMainTitle(checkAvailContext.getHotelName().getTextEn());
        orderSummary.setHeaderInfo(header);

        OrderSummaryHotel hotel = new OrderSummaryHotel();
        String checkIn = orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo().getCheckIn();
        String checkOut = orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo().getCheckOut();
        Date checkInDate = DateUtil.parseDate(checkIn, DateUtil.YYYY_MM_DD);
        Date checkOutDate = DateUtil.parseDate(checkOut, DateUtil.YYYY_MM_DD);
        hotel.setNightNum((int) DateUtil.betweenDay(checkInDate, checkOutDate));
        hotel.setRoomNum(orderCreateRequestType.getHotelBookInput().getRoomQuantity());
        hotel.setCheckIn(buildCheckIn(orderCreateRequestType));
        hotel.setCheckOut(buildCheckOut(orderCreateRequestType));
        orderSummary.setHotelInfo(hotel);

        OrderSummaryOrderAmount orderAmount = new OrderSummaryOrderAmount();

        CreateOrderResult createOrderResult =
            OrderCreateProcessorOfUtil.buildCreateOrderResult(createOrderResponseType, orderCreateRequestType,
                orderCreateToken);
        Optional.ofNullable(createOrderResult.getPayAmountResult())
            .ifPresent(personalPayAmountInfo -> {
                orderAmount.setDetailAmount(personalPayAmountInfo.getAmount().doubleValue());
                orderAmount.setDetailCurrency(personalPayAmountInfo.getCurrency());
                // SHARK orderAmount.setDetailName(PaymentResourceEnum.TOTAL_PRICE.toMessage() + "：");
            });
        orderAmount.setGroup(0);
        orderSummary.setOrderAmountInfo(orderAmount);

        return JsonUtil.toJson(orderSummary);
    }

    private String buildCheckIn(OrderCreateRequestType orderCreateRequestType) {
        /*if (orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getPos() == PosEnum.CHINA) {
            String checkIn = orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo().getCheckIn();
            Date checkInDate = DateUtil.parseDate(checkIn, DateUtil.YYYY_MM_DD);
            return DateUtil.fromDateToString(checkInDate, "MM-dd");
        }*/
        String checkIn = orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo().getCheckIn();
        Date checkInDate = DateUtil.parseDate(checkIn, DateUtil.YYYY_MM_DD);
        return DateDisplayUtil.ymdShortString(checkInDate,
            orderCreateRequestType.getIntegrationSoaRequestType().getLanguage());
    }

    private String buildCheckOut(OrderCreateRequestType orderCreateRequestType) {
        /*if (orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getPos() == PosEnum.CHINA) {
            String checkOut = orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo().getCheckOut();
            Date checkOutDate = DateUtil.parseDate(checkOut, DateUtil.YYYY_MM_DD);
            return DateUtil.fromDateToString(checkOutDate, "MM-dd");
        }*/
        String checkOut = orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo().getCheckOut();
        Date checkOutDate = DateUtil.parseDate(checkOut, DateUtil.YYYY_MM_DD);
        return DateDisplayUtil.ymdShortString(checkOutDate,
            orderCreateRequestType.getIntegrationSoaRequestType().getLanguage());
    }

    private boolean isOnline(SourceFrom sourceFrom) {
        return sourceFrom == SourceFrom.Online || sourceFrom == SourceFrom.Offline;
    }

    private boolean isContextZHCN(OrderCreateRequestType orderCreateRequestType) {
        return StringUtil.equalsIgnoreCase(orderCreateRequestType.getIntegrationSoaRequestType().getLanguage(),
                LANGUAGE_ZH_CN);
    }

    protected boolean supportWechatPayByOthers(OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo, CreateOrderResponseType createOrderResponseType,
        OrderCreateToken orderCreateToken, Boolean offlineNewPay) {
        String canWechatPayByOthers = orderCreateRequestType.getPaymentInfoInput().getCanWechatPayByOthers();
        if (!StringUtil.equalsIgnoreCase(canWechatPayByOthers, "T")) {
            return false;
        }
        if (!accountInfo.supportWechat()) {
            return false;
        }
        if (!getClientType(orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom(),
            orderCreateRequestType.getPaymentInfoInput().getClientType(), BooleanUtil.isTrue(offlineNewPay)).equals(
            CLIENTTYPE_APP)) {
            return false;
        }
        if (isCustomCurrency(
            OrderCreateProcessorOfUtil.buildOrderCurrency(createOrderResponseType, orderCreateRequestType,
                orderCreateToken))) {
            return false;
        }
        if (isCustomCurrency(accountInfo)) {
            return false;
        }
        if (!isContextZHCN(orderCreateRequestType)) {
            return false;
        }
        return true;
    }

    class OrderSummaryOrderAmount {
        private String detailCurrency;

        private Double detailAmount;

        private String detailName;

        private Integer group;

        private String additionalDesTop;

        public String getDetailCurrency() {
            return detailCurrency;
        }

        public void setDetailCurrency(String detailCurrency) {
            this.detailCurrency = detailCurrency;
        }

        public Double getDetailAmount() {
            return detailAmount;
        }

        public void setDetailAmount(Double detailAmount) {
            this.detailAmount = detailAmount;
        }

        public String getDetailName() {
            return detailName;
        }

        public void setDetailName(String detailName) {
            this.detailName = detailName;
        }

        public Integer getGroup() {
            return group;
        }

        public void setGroup(Integer group) {
            this.group = group;
        }

        public String getAdditionalDesTop() {
            return additionalDesTop;
        }

        public void setAdditionalDesTop(String additionalDesTop) {
            this.additionalDesTop = additionalDesTop;
        }
    }

    class OrderSummaryHotel {
        private String checkIn;

        private String checkOut;

        private Integer roomNum;

        private Integer nightNum;

        public String getCheckIn() {
            return checkIn;
        }

        public void setCheckIn(String checkIn) {
            this.checkIn = checkIn;
        }

        public String getCheckOut() {
            return checkOut;
        }

        public void setCheckOut(String checkOut) {
            this.checkOut = checkOut;
        }

        public Integer getRoomNum() {
            return roomNum;
        }

        public void setRoomNum(Integer roomNum) {
            this.roomNum = roomNum;
        }

        public Integer getNightNum() {
            return nightNum;
        }

        public void setNightNum(Integer nightNum) {
            this.nightNum = nightNum;
        }
    }

    class OrderSummaryHeader {
        private String mainTitle;

        private String subTitle;

        private List<String> titleInfoList;

        public String getMainTitle() {
            return mainTitle;
        }

        public void setMainTitle(String mainTitle) {
            this.mainTitle = mainTitle;
        }

        public String getSubTitle() {
            return subTitle;
        }

        public void setSubTitle(String subTitle) {
            this.subTitle = subTitle;
        }

        public List<String> getTitleInfoList() {
            return titleInfoList;
        }

        public void setTitleInfoList(List<String> titleInfoList) {
            this.titleInfoList = titleInfoList;
        }
    }

    class OrderSummary {
        private Integer stencil;

        private OrderSummaryHeader headerInfo;

        private OrderSummaryHotel hotelInfo;

        private OrderSummaryOrderAmount orderAmountInfo;

        public Integer getStencil() {
            return stencil;
        }

        public void setStencil(Integer stencil) {
            this.stencil = stencil;
        }

        public OrderSummaryHeader getHeaderInfo() {
            return headerInfo;
        }

        public void setHeaderInfo(OrderSummaryHeader headerInfo) {
            this.headerInfo = headerInfo;
        }

        public OrderSummaryHotel getHotelInfo() {
            return hotelInfo;
        }

        public void setHotelInfo(OrderSummaryHotel hotelInfo) {
            this.hotelInfo = hotelInfo;
        }

        public OrderSummaryOrderAmount getOrderAmountInfo() {
            return orderAmountInfo;
        }

        public void setOrderAmountInfo(OrderSummaryOrderAmount orderAmountInfo) {
            this.orderAmountInfo = orderAmountInfo;
        }
    }

    class PriceDetail {
        @SerializedName("Index") private int index;
        @SerializedName("ItemName") private String itemName;
        @SerializedName("Amount") private String amount;

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }

        public String getItemName() {
            return itemName;
        }

        public void setItemName(String itemName) {
            this.itemName = itemName;
        }

        public String getAmount() {
            return amount;
        }

        public void setAmount(String amount) {
            this.amount = amount;
        }
    }

    class OrderPromptInfo {
        @SerializedName("Version") private String version;
        @SerializedName("AmountStatement") private String amountStatement;
        @SerializedName("PriceDetailList") private List<PriceDetail> priceDetailList;
        @SerializedName("OrderType") private String orderType;
        @SerializedName("Customers") private List<Customer> customers;
        @SerializedName("Invoice") private String invoice;
        @SerializedName("Contacts") private Customer contacts;
        @SerializedName("TripDescription") private TripDescription tripDescription;

        public String getVersion() {
            return version;
        }

        public void setVersion(String version) {
            this.version = version;
        }

        public String getAmountStatement() {
            return amountStatement;
        }

        public void setAmountStatement(String amountStatement) {
            this.amountStatement = amountStatement;
        }

        public List<PriceDetail> getPriceDetailList() {
            return priceDetailList;
        }

        public void setPriceDetailList(List<PriceDetail> priceDetailList) {
            this.priceDetailList = priceDetailList;
        }

        public String getOrderType() {
            return orderType;
        }

        public void setOrderType(String orderType) {
            this.orderType = orderType;
        }

        public List<com.ctrip.soa.corp.booking.preparews.v1.Customer> getCustomers() {
            return customers;
        }

        public void setCustomers(List<Customer> customers) {
            this.customers = customers;
        }

        public String getInvoice() {
            return invoice;
        }

        public void setInvoice(String invoice) {
            this.invoice = invoice;
        }

        public com.ctrip.soa.corp.booking.preparews.v1.Customer getContacts() {
            return contacts;
        }

        public void setContacts(Customer contacts) {
            this.contacts = contacts;
        }

        public TripDescription getTripDescription() {
            return tripDescription;
        }

        public void setTripDescription(TripDescription tripDescription) {
            this.tripDescription = tripDescription;
        }
    }

    class TripDescription {
        @SerializedName("HotelData") private HotelData hotelData;

        public HotelData getHotelData() {
            return hotelData;
        }

        public void setHotelData(HotelData hotelData) {
            this.hotelData = hotelData;
        }
    }

    class HotelData {
        @SerializedName("Index") private int index;
        @SerializedName("BrowseAmount") private String browseAmount;
        @SerializedName("RoomCount") private String roomCount;
        @SerializedName("NightCount") private String nightCount;
        @SerializedName("HotelName") private String hotelName;
        @SerializedName("RoomType") private String roomType;
        @SerializedName("CheckInDate") private String checkInDate;
        @SerializedName("CheckOutDate") private String checkOutDate;
        @SerializedName("HotelExtend") private String hotelExtend;

        public int getIndex() {
            return index;
        }

        public void setIndex(int index) {
            this.index = index;
        }

        public String getBrowseAmount() {
            return browseAmount;
        }

        public void setBrowseAmount(String browseAmount) {
            this.browseAmount = browseAmount;
        }

        public String getRoomCount() {
            return roomCount;
        }

        public void setRoomCount(String roomCount) {
            this.roomCount = roomCount;
        }

        public String getNightCount() {
            return nightCount;
        }

        public void setNightCount(String nightCount) {
            this.nightCount = nightCount;
        }

        public String getHotelName() {
            return hotelName;
        }

        public void setHotelName(String hotelName) {
            this.hotelName = hotelName;
        }

        public String getRoomType() {
            return roomType;
        }

        public void setRoomType(String roomType) {
            this.roomType = roomType;
        }

        public String getCheckInDate() {
            return checkInDate;
        }

        public void setCheckInDate(String checkInDate) {
            this.checkInDate = checkInDate;
        }

        public String getCheckOutDate() {
            return checkOutDate;
        }

        public void setCheckOutDate(String checkOutDate) {
            this.checkOutDate = checkOutDate;
        }

        public String getHotelExtend() {
            return hotelExtend;
        }

        public void setHotelExtend(String hotelExtend) {
            this.hotelExtend = hotelExtend;
        }
    }

    class CustomTitleModel {
        /**
         * 订单标题显示样式
         * <p>
         * 0：表示通用样式
         * <p>
         * 1：表示酒店样式
         * <p>
         * 2：表示机票样式
         * <p>
         * 3：表示用车样式
         */
        private Integer titleType;

        /**
         * 订单标题,数组里是Json字符串(可传多个，通用示例除外)
         * <p>
         * 通用示例如下：
         * <p>
         * "customTitle":[{"title":"上海到北京","content":"2017.05.02起飞"}] (只可传一组)
         * <p>
         * 机票示例如下：
         * <p>
         * "customTitle":[{"tag":"去程","title":"上海到北京","content":"2017.05.02起飞"}]
         * <p>
         * 酒店示例如下：
         * <p>
         * "customTitle": [{"title":"某快捷酒店","content":"描述内容"，"checkin ":"03月26日",
         * "checkout ":"03月28日","night ":2,"room ":3}] (只可传一组)
         * <p>
         * 用车示例如下：
         * <p>
         * "customTitle":[{"carModel":"别克GL8(沪牌)","takeCarTime":"06-28 08:30","takeCarAddress":"虹桥火车站停车场",
         * "recarTime":"06-29 08:30","recarAddress":"虹桥火车站停车场","useDuration":"2天11小时"}] (只可传一组)
         */
        private List<CustomTitleForPayH5Model> customTitle;

        public Integer getTitleType() {
            return titleType;
        }

        public void setTitleType(Integer titleType) {
            this.titleType = titleType;
        }

        public List<CustomTitleForPayH5Model> getCustomTitle() {
            return customTitle;
        }

        public void setCustomTitle(List<CustomTitleForPayH5Model> customTitle) {
            this.customTitle = customTitle;
        }
    }

    class CustomTitleForPayH5Model {
        private String checkin;

        private String checkout;

        private Integer night;

        private Integer room;

        private String title;

        private String content;

        public String getCheckin() {
            return checkin;
        }

        public void setCheckin(String checkin) {
            this.checkin = checkin;
        }

        public String getCheckout() {
            return checkout;
        }

        public void setCheckout(String checkout) {
            this.checkout = checkout;
        }

        public Integer getNight() {
            return night;
        }

        public void setNight(Integer night) {
            this.night = night;
        }

        public Integer getRoom() {
            return room;
        }

        public void setRoom(Integer room) {
            this.room = room;
        }

        public String getTitle() {
            return title;
        }

        public void setTitle(String title) {
            this.title = title;
        }

        public String getContent() {
            return content;
        }

        public void setContent(String content) {
            this.content = content;
        }
    }
}
