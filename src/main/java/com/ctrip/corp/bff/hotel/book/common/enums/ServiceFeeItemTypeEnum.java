package com.ctrip.corp.bff.hotel.book.common.enums;

import com.ctrip.framework.ucs.common.util.StringUtils;

import java.util.Arrays;
import java.util.List;

/**
 * @Author: chenchuang
 * @Date: 2024/9/12 10:32
 */
public enum ServiceFeeItemTypeEnum {

    /**
     * 普通服务费-预订商旅管理服务费
     */
    ORDINARY("ORDINARY", "BookingOrderSC", "ordinary"),
    /**
     * Offline特殊服务费-非工作时间-非工作时间商旅管理服务费
     */
    OUT_WORK_TIME("OUT_WORK_TIME", "BookingOrderSC_NonWorkingTime", "outworktime"),
    /**
     * Offline特殊服务费-VIP预订-VIP商旅管理服务费
     */
    VIP_BOOKING("VIP_BOOKING", "BookingOrderSC_U_VIP","vipbooking"),

    /**
     * 未过时修改服务费 V2版本服务费才涉及此概念
     */
    MODIFY_ORDER_SC_WITHIN_LMT("ModifyOrderSC_WithinLMT", "ModifyOrderSC_WithinLMT", "modifywithinlmt"),
    /**
     * 过时修改服务费
     */
    MODIFY_ORDER_SC_OVER_LMT("ModifyOrderSC_OverLMT", "ModifyOrderSC_OverLMT", "modifyoverlmt"),;


    private String code;

    public String paramKey;

    public String getParamKey() {
        return paramKey;
    }

    public String getCode() {
        return code;
    }

    /**
     * agg新版服务费
     */
    private String aggChargeItemCode;

    public String getAggChargeItemCode() {
        return aggChargeItemCode;
    }

    ServiceFeeItemTypeEnum(String code, String aggChargeItemCode, String paramKey) {
        this.code = code;
        this.aggChargeItemCode = aggChargeItemCode;
        this.paramKey = paramKey;
    }

    public static List<ServiceFeeItemTypeEnum> NONEEDFEELIST = Arrays.asList(ServiceFeeItemTypeEnum.ORDINARY,
            ServiceFeeItemTypeEnum.MODIFY_ORDER_SC_OVER_LMT, ServiceFeeItemTypeEnum.MODIFY_ORDER_SC_WITHIN_LMT);

    public static ServiceFeeItemTypeEnum value(String aggChargeItemCode) {
        if (StringUtils.isBlank(aggChargeItemCode)) {
            return ORDINARY;
        }
        return Arrays.stream(values()).filter(
            t -> aggChargeItemCode.equalsIgnoreCase(t.getCode()) || aggChargeItemCode.equalsIgnoreCase(
                t.getAggChargeItemCode())).findFirst().orElse(ORDINARY);
    }
}
