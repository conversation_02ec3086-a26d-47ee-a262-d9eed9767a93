package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.microfinance.giftcardpay.ws.contract.RetrieveTicketsByOrderTypeRequestType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/3/24 22:57
 */
@Component public class MapperOfRetrieveTicketsByOrderTypeRequestType
    extends AbstractMapper<Tuple1<IntegrationSoaRequestType>, RetrieveTicketsByOrderTypeRequestType> {
    private static final int ORDER_TYPE = 1;

    @Override protected RetrieveTicketsByOrderTypeRequestType convert(Tuple1<IntegrationSoaRequestType> tuple1) {
        IntegrationSoaRequestType integrationSoaRequestType = tuple1.getT1();
        RetrieveTicketsByOrderTypeRequestType request = new RetrieveTicketsByOrderTypeRequestType();
        request.setCustomerID(integrationSoaRequestType.getUserInfo().getUserId());
        request.setOrderType(ORDER_TYPE);
        return request;
    }

    @Override protected ParamCheckResult check(Tuple1<IntegrationSoaRequestType> tuple1) {
        return null;
    }
}
