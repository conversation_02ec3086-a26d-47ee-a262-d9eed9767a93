package com.ctrip.corp.bff.hotel.book.handler.orderfoundationcentercostinfoservice;

import com.corp.order.service.orderfoundationcentercostinfoservice.SaveOrderCostCenterRequestType;
import com.corp.order.service.orderfoundationcentercostinfoservice.SaveOrderCostCenterResponseType;
import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.soa._20185.OrderFoundationCenterCostInfoServiceClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 保存订单成本中心数据
 * @Date 2024/8/20 19:05
 * @Version 1.0
 */
@Component
public class HandlerOfSaveCostCenter extends
        AbstractHandlerOfSOA<SaveOrderCostCenterRequestType, SaveOrderCostCenterResponseType, OrderFoundationCenterCostInfoServiceClient> {

    @Override
    protected String getMethodName() {
        return "saveOrderCostCenter";
    }
}
