package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.CreateOrderResult;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.PayAmountResult;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.PaymentItemTypeResult;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.HostUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelBalanceTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.util.HotelPayTypeUtil;
import com.ctrip.corp.bff.hotel.book.common.util.MathUtils;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.PayMentInfoInput;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType;
import com.ctrip.corp.hotelbooking.hotelws.entity.PaymentItemType;
import com.ctrip.corp.order.common.enumeration.SubProductLineEnum;
import com.ctrip.corp.order.common.enumeration.fee.config.ConfigChannelEnum;
import com.ctrip.corp.order.common.enumeration.fee.config.ConfigPaymentTypeEnum;
import com.ctrip.corp.order.common.enumeration.fee.config.ConfigScenarioCodeEnum;
import com.ctrip.corp.order.paymentcenter.bill.contract.ExtendParamType;
import com.ctrip.corp.order.paymentcenter.bill.contract.HotelConfigRequestType;
import com.ctrip.corp.order.paymentcenter.bill.contract.QueryPaymentBillConfigRequestType;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/23 18:31
 */
@Component public class MapperOfQueryPaymentBillConfigRequestType extends
    AbstractMapper<Tuple5<ResourceToken, CreateOrderResponseType, OrderCreateRequestType, OrderCreateToken, Boolean>, QueryPaymentBillConfigRequestType> {
    private static final int HOTEL = 2;
    private static final String HOTEL_CONTRACT = "C";
    private static final String CORPPAYTYPE_PUB = "PUB";
    private static final String CORPPAYTYPE_OWN = "OWN";
    public static final String INSURANCE_FEE = "InsuranceFee";
    public static final String BOOK_SERVICE_FEE = "BookServiceFee";
    public static final String HOTEL_ROOM_FEE = "HotelRoomFee";
    private static final String PAYMENT_GUARANTEE_FLAG_P = "P";
    private static final String PAYMENT_GUARANTEE_FLAG_G = "G";
    private static final String PAYMENT_GUARANTEE_FLAG_N = "N";

    private static final String DINGTALK_SUBCHANNEL = "DingTalk";
    private static final String DINGTALK_SUBCHANNEL_DING = "DING_TALK";
    private static final String BOOK_MODEL_PERSONAL_ACCOUNT = "bookingWithPersonalAccount";

    @Override protected QueryPaymentBillConfigRequestType convert(
        Tuple5<ResourceToken, CreateOrderResponseType, OrderCreateRequestType, OrderCreateToken, Boolean> tuple) {
        ResourceToken resourceToken = tuple.getT1();
        CreateOrderResponseType createOrderResponseType = tuple.getT2();
        OrderCreateRequestType orderCreateRequestType = tuple.getT3();
        OrderCreateToken orderCreateToken = tuple.getT4();
        boolean offlineNewPay = BooleanUtils.isTrue(tuple.getT5());
        QueryPaymentBillConfigRequestType requestType = new QueryPaymentBillConfigRequestType();
        requestType.setOrderId(OrderCreateProcessorOfUtil.buildOrderId(orderCreateToken, createOrderResponseType));
        requestType.setProductLine(HOTEL);
        requestType.setSubProductLine(getSubProductLine(resourceToken));
        requestType.setScenarioCode(getScenarioCode());
        requestType.setChannel(getChannel(orderCreateRequestType.getIntegrationSoaRequestType()));
        requestType.setCorpId(orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId());
        requestType.setCorpPayType(
            CorpPayInfoUtil.isPublic(orderCreateRequestType.getCorpPayInfo()) ? CORPPAYTYPE_PUB : CORPPAYTYPE_OWN);
        requestType.setPaymentType(ConfigPaymentTypeEnum.PERSONAL.getCode());
        requestType.setHotelConfigRequest(
            getHotelConfigRequestType(createOrderResponseType, orderCreateRequestType, resourceToken,
                orderCreateToken));
        requestType.setRequestId(orderCreateRequestType.getIntegrationSoaRequestType().getRequestId());
        requestType.setLoginUid(orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId());
        requestType.setPolicyUid(
            Optional.ofNullable(orderCreateRequestType.getHotelPolicyInput()).map(HotelPolicyInput::getPolicyInput)
                .map(PolicyInput::getPolicyUid)
                .orElse(orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId()));
        requestType.setFormPaymentFlag(false);
        requestType.setPos(
            HostUtil.mapToAccountPos(orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getPos()));
        requestType.setDingTalkChannelFlag(
            isDingTalkChannelFlag(orderCreateRequestType.getIntegrationSoaRequestType()));
        requestType.setCurrency(
            OrderCreateProcessorOfUtil.buildOrderCurrency(createOrderResponseType, orderCreateRequestType,
                orderCreateToken));
        requestType.setLanguage(orderCreateRequestType.getIntegrationSoaRequestType().getLanguage());
        requestType.setCanWechatPayByOthers(BooleanUtil.parseStr(true).equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getPaymentInfoInput())
                .map(PayMentInfoInput::getCanWechatPayByOthers).orElse(null)));
        requestType.setBookModel(buildBookModel(orderCreateRequestType));
        requestType.setFormPaymentFlag(
            !offlineNewPay && OrderCreateProcessorOfUtil.requireFromPay(orderCreateRequestType, resourceToken,
                orderCreateToken));
        requestType.setExtendParamList(buildExtendParamList(offlineNewPay));
        return requestType;
    }

    @Override
    protected ParamCheckResult check(Tuple5<ResourceToken, CreateOrderResponseType, OrderCreateRequestType, OrderCreateToken, Boolean> tuple) {
        CreateOrderResponseType createOrderResponseType = tuple.getT2();
        OrderCreateToken orderCreateToken = tuple.getT4();
        OrderCreateRequestType orderCreateRequestType = tuple.getT3();
        ResourceToken resourceToken = tuple.getT1();
        if (!orderCreateToken.isUseOrderCreate()) {
            return null;
        }
        if (!OrderCreateProcessorOfUtil.requirePaymentOrderCreate(orderCreateRequestType, resourceToken,
            orderCreateToken)) {
            return null;
        }
        // 需要跳转金融平台的，要校验个人部分金额节点
        boolean priceChangeOrConfirmOrder = !OrderCreateProcessorOfUtil.requireCreateOrder(orderCreateToken);
        if (!priceChangeOrConfirmOrder && (createOrderResponseType.getOrderPaymentInfo() == null
            || createOrderResponseType.getOrderPaymentInfo().getOrderAmountInfo() == null
            || createOrderResponseType.getOrderPaymentInfo().getOrderAmountInfo().getPersonalPayAmountInfo() == null)) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.CREATE_ORDER_AMOUNT_ERROR);
        }
        if (priceChangeOrConfirmOrder && orderCreateToken.getCreateOrderResult().getPayAmountResult() == null) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.CREATE_ORDER_AMOUNT_ERROR);
        }
        return null;
    }

    protected List<ExtendParamType> buildExtendParamList(boolean offlineNewPay) {
        if (!offlineNewPay) {
            return null;
        }
        ExtendParamType extendParamType = new ExtendParamType();
        extendParamType.setKey("formGrayFlag");
        extendParamType.setValue("true");
        return Arrays.asList(extendParamType);
    }
    protected String buildBookModel(OrderCreateRequestType orderCreateRequestType) {
        if (StrategyOfBookingInitUtil.bookingWithPersonalAccount(orderCreateRequestType.getStrategyInfos())) {
            return BOOK_MODEL_PERSONAL_ACCOUNT;
        }
        return null;
    }

    protected boolean isDingTalkChannelFlag(IntegrationSoaRequestType integrationSoaRequestType) {
        return DINGTALK_SUBCHANNEL.equalsIgnoreCase(RequestHeaderUtil.getSubChannel(integrationSoaRequestType))
            || DINGTALK_SUBCHANNEL_DING.equalsIgnoreCase(RequestHeaderUtil.getSubChannel(integrationSoaRequestType));
    }

    protected HotelConfigRequestType getHotelConfigRequestType(CreateOrderResponseType createOrderResponseType,
        OrderCreateRequestType orderCreateRequestType, ResourceToken resourceToken, OrderCreateToken orderCreateToken) {
        HotelConfigRequestType hotelConfigRequestType = new HotelConfigRequestType();
        hotelConfigRequestType.setTransactionId(resourceToken.getReservationResourceToken().getWsId());
        hotelConfigRequestType.setIncludeInsuranceFlag(
            buildIncludeInsuranceFlag(createOrderResponseType, orderCreateToken, orderCreateRequestType));
        hotelConfigRequestType.setIncludeBeforeServiceFeeFlag(
            getIncludeBeforeServiceFeeFlag(orderCreateRequestType, createOrderResponseType, resourceToken,
                orderCreateToken));
        hotelConfigRequestType.setPaymentGuaranteeFlag(
            getPaymentGuaranteeFlag(createOrderResponseType, resourceToken, orderCreateToken, orderCreateRequestType));
        hotelConfigRequestType.setWechatMiniAppFlag(CommonConstant.CLIENTTYPE_MINIAPP.equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getPaymentInfoInput()).map(PayMentInfoInput::getClientType)
                .orElse(null)));
        return hotelConfigRequestType;
    }

    protected String getPaymentGuaranteeFlag(CreateOrderResponseType createOrderResponseType,
        ResourceToken resourceToken, OrderCreateToken orderCreateToken, OrderCreateRequestType orderCreateRequestType) {
        CreateOrderResult createOrderResult =
            OrderCreateProcessorOfUtil.buildCreateOrderResult(createOrderResponseType, orderCreateRequestType,
                orderCreateToken);
        if (Arrays.asList(HotelBalanceTypeEnum.PP, HotelBalanceTypeEnum.USEFG).contains(
            HotelBalanceTypeEnum.getHotelBalanceTypeEnum(resourceToken.getRoomResourceToken().getBalanceType()))) {
            return PAYMENT_GUARANTEE_FLAG_P;
        }
        HotelPayTypeEnum roomType = HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        if (roomType == HotelPayTypeEnum.GUARANTEE_SELF_PAY) {
            return PAYMENT_GUARANTEE_FLAG_G;
        }
        return PAYMENT_GUARANTEE_FLAG_N;
    }

    protected boolean getIncludeBeforeServiceFeeFlag(OrderCreateRequestType orderCreateRequestType,
        CreateOrderResponseType createOrderResponseType, ResourceToken resourceToken,
        OrderCreateToken orderCreateToken) {
        CreateOrderResult createOrderResult =
            OrderCreateProcessorOfUtil.buildCreateOrderResult(createOrderResponseType, orderCreateRequestType,
                orderCreateToken);
        if (HotelPayTypeUtil.getServicePayType(orderCreateRequestType.getHotelPayTypeInput(), resourceToken)
            != HotelPayTypeEnum.SELF_PAY) {
            return false;
        }
        if (CollectionUtil.isNotEmpty(createOrderResult.getPaymentItemTypeResults())) {
            return true;
        }
        List<PaymentItemTypeResult> paymentItemTypes =
            getPaymentItemResult(createOrderResult.getPaymentItemTypeResults(),
                BOOK_SERVICE_FEE);
        if (CollectionUtil.isEmpty(paymentItemTypes)) {
            return false;
        }
        // 创单时服务费只有一种支付方式
        PaymentItemTypeResult paymentItemType = paymentItemTypes.stream().findFirst().orElse(null);
        if (paymentItemType == null || paymentItemType.getPayAmountResult() == null) {
            return false;
        }
        return MathUtils.isGreaterThanZero(paymentItemType.getPayAmountResult().getAmount());
    }

    public static List<PaymentItemType> getPaymentItem(List<PaymentItemType> paymentItemList, String feeType) {
        if (CollectionUtil.isEmpty(paymentItemList)) {
            return null;
        }
        return paymentItemList.stream().filter(Objects::nonNull)
            .filter(paymentItemType -> StringUtil.equalsIgnoreCase(paymentItemType.getFeeType(), feeType))
            .collect(Collectors.toList());
    }

    public static List<PaymentItemTypeResult> getPaymentItemResult(List<PaymentItemTypeResult> paymentItemList,
        String feeType) {
        if (CollectionUtil.isEmpty(paymentItemList)) {
            return null;
        }
        return paymentItemList.stream().filter(Objects::nonNull)
            .filter(paymentItemType -> StringUtil.equalsIgnoreCase(paymentItemType.getFeeType(), feeType))
            .collect(Collectors.toList());
    }

    protected boolean buildIncludeInsuranceFlag(CreateOrderResponseType createOrderResponseType,
        OrderCreateToken orderCreateToken, OrderCreateRequestType orderCreateRequestType) {
        CreateOrderResult createOrderResult =
            OrderCreateProcessorOfUtil.buildCreateOrderResult(createOrderResponseType, orderCreateRequestType,
                orderCreateToken);
        if (CollectionUtil.isNotEmpty(createOrderResult.getInsuranceRoutAmountResults())) {
            return true;
        }
        List<PaymentItemTypeResult> paymentItemTypes =
            getPaymentItemResult(createOrderResult.getPaymentItemTypeResults(), INSURANCE_FEE);
        if (CollectionUtil.isEmpty(paymentItemTypes)) {
            return false;
        }
        // 创单时保险只有一种支付方式
        PaymentItemTypeResult paymentItemType = paymentItemTypes.stream().findFirst().orElse(null);
        if (paymentItemType == null || paymentItemType.getPayAmountResult() == null) {
            return false;
        }
        return MathUtils.isGreaterThanZero(paymentItemType.getPayAmountResult().getAmount());
    }

    protected String getChannel(IntegrationSoaRequestType integrationSoaRequestType) {
        if (integrationSoaRequestType.getSourceFrom() == SourceFrom.Online) {
            return ConfigChannelEnum.ONLINE.getCode();
        }
        if (integrationSoaRequestType.getSourceFrom() == SourceFrom.Offline) {
            return ConfigChannelEnum.OFFLINE.getCode();
        }
        return ConfigChannelEnum.APP.getCode();
    }

    protected String getScenarioCode() {
        return ConfigScenarioCodeEnum.BOOK.getCode();
    }

    protected String getSubProductLine(ResourceToken resourceToken) {
        if (resourceToken.getRoomResourceToken() == null) {
            return SubProductLineEnum.HOTEL_MEMBER.getCode();
        }
        if (HOTEL_CONTRACT.equalsIgnoreCase(resourceToken.getRoomResourceToken().getRoomType())) {
            return SubProductLineEnum.HOTEL_CONTRACT.getCode();
        }
        if (BooleanUtils.isTrue(resourceToken.getRoomResourceToken().getTmcPrice())) {
            return SubProductLineEnum.HOTEL_CONTRACT_TO_MEMBER.getCode();
        }
        return SubProductLineEnum.HOTEL_MEMBER.getCode();
    }
}
