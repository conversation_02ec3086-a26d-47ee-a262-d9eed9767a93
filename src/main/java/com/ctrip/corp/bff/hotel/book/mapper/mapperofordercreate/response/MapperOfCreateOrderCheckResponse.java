package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response;

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.util.HotelPayTypeUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType;
import com.ctrip.soa._24373.CheckInfoType;
import com.ctrip.soa._24373.CreateOrderCheckResponseType;
import com.ctrip.soa._24373.VerifyInfoLimitType;
import org.apache.commons.lang.BooleanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/1/3 19:25
 */
@Component public class MapperOfCreateOrderCheckResponse extends
    AbstractMapper<Tuple2<CreateOrderCheckResponseType, OrderCreateRequestType>, Tuple2<Boolean, OrderCreateResponseType>> {
    private static final String FLASH_ORDER = "FlashOrder";

    // 闪住code
    public static final String FLASH_VERIFY_INFO_CODE = "FlashOrderUnFinish";

    // 您有{0}笔闪住订单在途，暂不可使用闪住功能，请更换其它支付方式
    private static final String FLASH_ORDER_UN_FINISH_SHARK =
        "ctrip.com.hotel.booking.biz.alter.flash.pay.intercept.FlashOrderUnFinish";

    @Override protected Tuple2<Boolean, OrderCreateResponseType> convert(
        Tuple2<CreateOrderCheckResponseType, OrderCreateRequestType> tuple) {
        return null;
    }

    @Override protected ParamCheckResult check(Tuple2<CreateOrderCheckResponseType, OrderCreateRequestType> tuple) {
        CreateOrderCheckResponseType orderCheckResponse = tuple.getT1();
        OrderCreateRequestType orderCreateRequestType = tuple.getT2();
        flashIntercept(orderCreateRequestType, orderCheckResponse);
        return null;
    }

    /**
     * 闪住订单拦截
     *
     * @return
     */
    public void flashIntercept(OrderCreateRequestType orderCreateRequestType,
        CreateOrderCheckResponseType orderCheckResponse) {
        if (HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput())
            != HotelPayTypeEnum.FLASH_STAY_PAY) {
            return;
        }
        if (Optional.ofNullable(orderCheckResponse).map(CreateOrderCheckResponseType::getResponseCode).orElse(0)
            .equals(CommonConstant.SUCCESS_20000)) {
            CheckInfoType checkInfoType =
                Optional.ofNullable(orderCheckResponse.getScenarioVerifyInfoList()).orElse(new ArrayList<>()).stream()
                    .filter(info -> FLASH_ORDER.equalsIgnoreCase(info.getScenario()) && !info.getVerifyResult())
                    .findFirst().orElse(null);
            if (checkInfoType == null) {
                return;
            }
            VerifyInfoLimitType verifyInfoLimitType =
                Optional.ofNullable(checkInfoType.getVerifyInfoList()).orElse(new ArrayList<>()).stream()
                    .filter(info -> FLASH_VERIFY_INFO_CODE.equalsIgnoreCase(info.getCode())).findFirst().orElse(null);
            if (Optional.ofNullable(checkInfoType.getFailCodeList()).orElse(new ArrayList<>())
                .contains(FLASH_VERIFY_INFO_CODE)) {
                Integer overLimit =
                    Optional.ofNullable(verifyInfoLimitType).map(VerifyInfoLimitType::getLimit).orElse(1);
                String sharkMessage = BFFSharkUtil.getSharkValue(FLASH_ORDER_UN_FINISH_SHARK);
                String errorMessage = StringUtil.indexedFormat(sharkMessage, overLimit);
                throw BusinessExceptionBuilder.createAlertException(
                    OrderCreateErrorEnum.FLASH_CHECK_ERROR.getErrorCode(), errorMessage, errorMessage,
                    OrderCreateErrorEnum.FLASH_CHECK_ERROR.getErrorMessage());
            }
        }
    }
}
