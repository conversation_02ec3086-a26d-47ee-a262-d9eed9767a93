package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import corp.user.service.CorpAccountQueryService.GetAuthDelayRequestType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/8/16 19:37
 */
@Component
public class MapperOfGetAuthDelayRequestType
    extends AbstractMapper<Tuple1<IntegrationSoaRequestType>, GetAuthDelayRequestType> {
    private static final String BUSINESS_TYPE_H = "H";

    @Override
    protected GetAuthDelayRequestType convert(Tuple1<IntegrationSoaRequestType> tuple1) {
        IntegrationSoaRequestType soaRequestType = tuple1.getT1();
        GetAuthDelayRequestType getAuthDelayRequestType = new GetAuthDelayRequestType();
        getAuthDelayRequestType.setUid(soaRequestType.getUserInfo().getUserId());
        getAuthDelayRequestType.setBusinessType(BUSINESS_TYPE_H);
        return getAuthDelayRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple1<IntegrationSoaRequestType> tuple1) {
        return null;
    }
}
