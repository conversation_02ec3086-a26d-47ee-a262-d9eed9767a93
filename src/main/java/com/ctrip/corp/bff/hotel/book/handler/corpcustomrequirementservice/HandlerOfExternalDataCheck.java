package com.ctrip.corp.bff.hotel.book.handler.corpcustomrequirementservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.corpsz.corpcustomrequirementservice.common.contract.ExternalDataCheckRequestType;
import com.ctrip.corp.corpsz.corpcustomrequirementservice.common.contract.ExternalDataCheckResponseType;
import corp.booking.corpcustomrequirementservice.CorpCustomRequirementServiceClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/05/29
 */
@Component
public class HandlerOfExternalDataCheck extends
    AbstractHandlerOfSOA<ExternalDataCheckRequestType, ExternalDataCheckResponseType, CorpCustomRequirementServiceClient> {

    @Override
    protected String getMethodName() {
        return "externalDataCheck";
    }
}
