package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.arch.coreinfo.entity.InfoKey;
import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.bff.framework.hotel.common.util.CoreInfoUtil;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.CorpXProductInfoToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.EmailInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.hotel.book.common.enums.InvoiceEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.InvoiceTitleTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.HotelInvoiceUtil;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.contract.HotelInvoiceInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType;
import com.ctrip.order.reimbursement.DeliveryType;
import com.ctrip.order.reimbursement.HotelExtendInvoiceType;
import com.ctrip.order.reimbursement.InvoiceType;
import com.ctrip.order.reimbursement.ReimburseInfoType;
import com.ctrip.order.reimbursement.ReimburseSettlementSyncRequestType;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/19 21:54
 */
@Component public class MapperOfReimburseSettlementSyncRequestType extends
    AbstractMapper<Tuple4<OrderCreateRequestType, CreateOrderResponseType, QueryCheckAvailContextResponseType, OrderCreateToken>, ReimburseSettlementSyncRequestType> {
    @Override protected ReimburseSettlementSyncRequestType convert(
        Tuple4<OrderCreateRequestType, CreateOrderResponseType, QueryCheckAvailContextResponseType, OrderCreateToken> param) {
        OrderCreateRequestType orderCreateRequestType = param.getT1();
        CreateOrderResponseType createOrderResponseType = param.getT2();
        OrderCreateToken orderCreateToken = param.getT4();
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType = param.getT3();
        ReimburseSettlementSyncRequestType reqType = new ReimburseSettlementSyncRequestType();
        reqType.setRequestId(orderCreateRequestType.getIntegrationSoaRequestType().getRequestId());
        SourceFrom sourceFrom = orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom();
        reqType.setRequestSource(getServerFromEnum(sourceFrom));
        reqType.setOrderId(OrderCreateProcessorOfUtil.buildOrderId(orderCreateToken, createOrderResponseType));
        reqType.setProductLine(2);
        String subProductLine = queryCheckAvailContextResponseType.getRoomInfo().getRoomType();
        subProductLine =
            "M".equalsIgnoreCase(queryCheckAvailContextResponseType.getRoomInfo().getTmcPriceType()) ? "C2M" :
                subProductLine;
        reqType.setSubProductLine(subProductLine);
        reqType.setOperator(
            sourceFrom == SourceFrom.Offline ? orderCreateRequestType.getIntegrationSoaRequestType().getEid() :
                orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId());
        HotelInvoiceInfo hotelInvoiceInfo =
            HotelInvoiceUtil.getHotelInvoiceInfoInsurance(orderCreateRequestType.getHotelInvoiceInfos());
        List<String> xProductInvoiceProductCodes = new ArrayList<>();
        orderCreateRequestType.getHotelInsuranceInput().getHotelInsuranceDetailInputs().forEach(insuranceInfoInput -> {
            CorpXProductInfoToken insuranceToken =
                TokenParseUtil.parseToken(insuranceInfoInput.getInsuranceToken(), CorpXProductInfoToken.class);
            xProductInvoiceProductCodes.add(insuranceToken.getProductCode());
        });
        ReimburseInfoType reimburseInfo = new ReimburseInfoType();
        InvoiceType invoice = new InvoiceType();
        InvoiceEnum invoiceEnum = InvoiceEnum.convertInvoiceEnum(hotelInvoiceInfo.getInvoiceInfo().getInvoiceType());
        InvoiceTitleTypeEnum invoiceTitleTypeEnum =
            InvoiceTitleTypeEnum.convertInvoiceTitleTypeEnum(hotelInvoiceInfo.getInvoiceInfo().getInvoiceTitleType());
        invoice.setInvoiceType(invoiceEnum == InvoiceEnum.D_INVOICE ? InvoiceEnum.D_VAT_INVOICE.getAggCode() : "N");
        invoice.setEInvoiceFlag(true);
        // 抬头类型
        invoice.setBuyerType(getHeadTypeEnum(invoiceTitleTypeEnum, invoiceEnum));
        // 抬头
        invoice.setBuyerName(hotelInvoiceInfo.getInvoiceInfo().getInvoiceTitle());

        if (StringUtil.isNotBlank(hotelInvoiceInfo.getInvoiceInfo().getTaxNumber())
            && invoiceTitleTypeEnum == InvoiceTitleTypeEnum.PERSONAL && Arrays.asList(InvoiceEnum.D_INVOICE,
            InvoiceEnum.D_VAT_INVOICE).contains(invoiceEnum)) {
            // 数电+个人 需要传入身份证号
            invoice.setBuyerTaxID(CoreInfoUtil.encrypt(
                new InfoKey(KeyType.Identity_Card, hotelInvoiceInfo.getInvoiceInfo().getTaxNumber())));
        } else {
            // 纳税人识别号
            invoice.setBuyerTaxID(hotelInvoiceInfo.getInvoiceInfo().getTaxNumber());
        }
        reimburseInfo.setInvoice(invoice);
        DeliveryType deliveryInfo = new DeliveryType();
        if (StringUtil.isNotBlank(
            Optional.ofNullable(hotelInvoiceInfo.getInvoiceInfo().getEmailInfo()).map(EmailInfo::getTransferEmail)
                .orElse(null))) {
            deliveryInfo.setEmail(CoreInfoUtil.encrypt(
                new InfoKey(KeyType.Mail, hotelInvoiceInfo.getInvoiceInfo().getEmailInfo().getTransferEmail())));
        }
        reimburseInfo.setDeliveryInfo(deliveryInfo);

        HotelExtendInvoiceType hotelExtendInfo = new HotelExtendInvoiceType();
        hotelExtendInfo.setHotelRemark(
            BooleanUtil.parseStr(true).equalsIgnoreCase(hotelInvoiceInfo.getHotelRemark()) ? "T" : "F");
        // 开票种类 (1:房费 2:服务费 3:保险费)
        hotelExtendInfo.setInvoiceCategoryList(Lists.newArrayList(3));
        hotelExtendInfo.setInsuranceTokenList(xProductInvoiceProductCodes);

        reimburseInfo.setHotelExtendInfo(hotelExtendInfo);

        reqType.setReimburseInfo(reimburseInfo);

        return reqType;
    }

    @Override protected ParamCheckResult check(
        Tuple4<OrderCreateRequestType, CreateOrderResponseType, QueryCheckAvailContextResponseType, OrderCreateToken> param) {
        return null;
    }

    /**
     * 企业 CP 非企业 NC, 个人 PS**
     *
     * @return
     */
    protected String getHeadTypeEnum(InvoiceTitleTypeEnum invoiceTitleTypeEnum, InvoiceEnum invoiceEnumRoom) {
        if (invoiceTitleTypeEnum == InvoiceTitleTypeEnum.ENTERPRISE || Arrays.asList(InvoiceEnum.S_VAT,
            InvoiceEnum.D_VAT_INVOICE).contains(invoiceEnumRoom)) {
            return "C";
        }
        if (invoiceTitleTypeEnum == InvoiceTitleTypeEnum.GOVERNMENT) {
            return "P";
        }
        if (invoiceTitleTypeEnum == InvoiceTitleTypeEnum.PERSONAL) {
            return "I";
        }
        return null;
    }

    protected String getServerFromEnum(SourceFrom sourceFrom) {
        if (sourceFrom == SourceFrom.Online) {
            return "Online";
        }
        if (sourceFrom == SourceFrom.Offline) {
            return "Offline";
        }
        return "App";
    }
}
