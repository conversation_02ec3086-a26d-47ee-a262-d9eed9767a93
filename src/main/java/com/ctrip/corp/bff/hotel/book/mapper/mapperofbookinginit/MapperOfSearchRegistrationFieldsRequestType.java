package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelBrandItem;
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelItem;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.model.SearchRegistrationFieldsRequestType;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/17
 */
@Component
public class MapperOfSearchRegistrationFieldsRequestType extends AbstractMapper<Tuple2<IntegrationSoaRequestType,
    WrapperOfCheckAvail.CheckAvailInfo>, SearchRegistrationFieldsRequestType> {
    @Override
    protected SearchRegistrationFieldsRequestType convert(Tuple2<IntegrationSoaRequestType, WrapperOfCheckAvail.CheckAvailInfo> para) {
        if (para == null) {
            return null;
        }
        IntegrationSoaRequestType integrationSoaRequestType = para.getT1();
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = para.getT2();
        Integer groupId = Optional.ofNullable(checkAvailInfo)
                .map(WrapperOfCheckAvail.CheckAvailInfo::getHotelItem)
                .map(HotelItem::getHotelBrandInfo)
                .map(HotelBrandItem::getGroupId)
                .orElse(null);
        if (groupId == null) {
            return null;
        }
        SearchRegistrationFieldsRequestType res = new SearchRegistrationFieldsRequestType();
        res.setRequestId(Optional.ofNullable(integrationSoaRequestType).map(IntegrationSoaRequestType::getRequestId).orElse(null));
        res.setMgrGroupId(groupId);
        res.setLocale(Optional.ofNullable(integrationSoaRequestType).map(IntegrationSoaRequestType::getLanguage).orElse(null));
        return res;
    }

    @Override
    protected ParamCheckResult check(Tuple2<IntegrationSoaRequestType,
        WrapperOfCheckAvail.CheckAvailInfo> integrationSoaRequestTypeIntegerTuple2) {
        return null;
    }
}
