package com.ctrip.corp.bff.hotel.book.common.enums;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;

/**
 * <AUTHOR>
 * @Date 2024/10/22 23:00
 */
public enum RoomTypeEnum {
    M("M"),
    C2M("C2M"),
    C("C");
    private final String value;

    public static RoomTypeEnum fromValue(String value) {
        if (StringUtil.isEmpty(value)) {
            return null;
        }
        for (RoomTypeEnum roomTypeEnum : RoomTypeEnum.values()) {
            if (StringUtil.equalsIgnoreCase(roomTypeEnum.getValue(), value)) {
                return roomTypeEnum;
            }
        }
        return null;
    }

    RoomTypeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
