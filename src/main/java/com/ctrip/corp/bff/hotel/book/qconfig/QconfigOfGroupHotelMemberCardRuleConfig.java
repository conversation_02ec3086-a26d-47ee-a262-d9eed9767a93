package com.ctrip.corp.bff.hotel.book.qconfig;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.QconfigEntityOfGroupHotelIdAndRuleMapping;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.QconfigEntityOfGroupHotelMemberCardRule;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024/10/29
 */
@Component
public class QconfigOfGroupHotelMemberCardRuleConfig {

    @QConfig("groupHotelMemberCardRule.json")
    private QconfigEntityOfGroupHotelMemberCardRule qconfigEntityOfGroupHotelMemberCardRule;

    public QconfigEntityOfGroupHotelMemberCardRule getQconfigEntityOfGroupHotelMemberCardRule() {
        return qconfigEntityOfGroupHotelMemberCardRule;
    }

}
