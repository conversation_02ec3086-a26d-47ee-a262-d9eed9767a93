package com.ctrip.corp.bff.hotel.book.qconfig;

import com.ctrip.corp.bff.hotel.book.common.enums.EarnPointsPatternEnum;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.CustomizedSharkConfig;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/14 10:50
 */
@Component
public class QConfigOfCustomizedSharkConfig {
    @QConfig("CustomizedSharkConfig.json")
    private List<CustomizedSharkConfig> customizedSharkConfigs;

    public List<CustomizedSharkConfig> getCustomizedSharkConfigs() {
        return customizedSharkConfigs;
    }

    public void setCustomizedSharkConfigs(List<CustomizedSharkConfig> customizedSharkConfigs) {
        this.customizedSharkConfigs = customizedSharkConfigs;
    }

    /**
    @QConfig("100026471#earn_point_config.json")
    protected EarnPointsContainer qconfig;
    public EarnPointsPatternEnum getEarnPointsPatternByCode(String code) {
        if (qconfig == null || CollUtil.isEmpty(qconfig.getPatternList())) {
            return null;
        }
        return qconfig.getPatternList()
                .stream()
                .filter(earnPointsModel -> BffStringUtil.equalsIgnoreCase(earnPointsModel.getCode(), code))
                .findFirst()
                .map(EarnPointsModel::getPattern)
                .map(EarnPointsPatternEnum::getValue)
                .orElse(null);
    }*/
}
