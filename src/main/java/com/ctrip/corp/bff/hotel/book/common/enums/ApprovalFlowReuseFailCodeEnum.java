package com.ctrip.corp.bff.hotel.book.common.enums;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;

/**
 * <AUTHOR>
 * @date 2025/7/2 20:55
 * @description 订单沿用失败原因枚举
 */
public enum ApprovalFlowReuseFailCodeEnum {
    /**
     * 价格超过差标
     */
    PRICE_EXCEEDS_STANDARD("030"),
    /**
     * 审批授权模式不相同
     */
    APPROVAL_AUTH_MODE_DIFFERENT("029"),
    /**
     * 非单订单
     */
    NON_SINGLE_ORDER("028"),
    /**
     * 价格超过超过允许的金额上限
     */
    PRICE_EXCEEDS_MAX_LIMIT("027"),
    /**
     * 非行程订单
     */
    NON_TRIP("026"),
    /**
     * 未授权通过
     */
    UNAUTHORIZED("025"),
    /**
     * 价格高于原单
     */
    PRICE_HIGHER_THAN_ORIGINAL("024"),
    /**
     * 星级低于差标
     */
    STAR_LEVEL_BELOW_STANDARD("023"),
    /**
     * 星级高于差标
     */
    STAR_LEVEL_ABOVE_STANDARD("022"),
    /**
     * 价格低于差标
     */
    PRICE_BELOW_STANDARD("021"),
    /**
     * 价格高于差标
     */
    PRICE_ABOVE_STANDARD("020"),
    /**
     * 不同酒店
     */
    DIFFERENT_HOTEL("019"),
    /**
     * 星级低于原酒店
     */
    STAR_LEVEL_BELOW_ORIGINAL("018"),
    /**
     * 星级高于原酒店
     */
    STAR_LEVEL_ABOVE_ORIGINAL("017"),
    /**
     * 非协议酒店
     */
    NON_CONTRACT_HOTEL("016"),
    /**
     * 产生取消费
     */
    CANCELLATION_FEE_GENERATED("015"),
    /**
     * policyUid不同
     */
    POLICY_UID_DIFFERENT("014"),
    /**
     * 房间数不同
     */
    ROOM_COUNT_DIFFERENT("013"),
    /**
     * 入住人不同
     */
    CHECKIN_PERSON_DIFFERENT("012"),
    /**
     * 离店时间不同
     */
    CHECKOUT_TIME_DIFFERENT("011"),
    /**
     * 入住时间不同
     */
    CHECKIN_TIME_DIFFERENT("010"),
    /**
     * 城市ID不同
     */
    CITY_ID_DIFFERENT("009"),
    /**
     * UID不同
     */
    UID_DIFFERENT("008"),
    /**
     * 订单已被延用
     */
    ORDER_ALREADY_REUSED("007"),
    /**
     * 出差审批单
     */
    BUSINESS_APPROVAL_ORDER("006"),
    /**
     * 提前审批
     */
    EARLY_APPROVAL("005"),
    /**
     * 取消渠道非offline
     */
    CANCEL_CHANNEL_NOT_OFFLINE("004"),
    /**
     * 取消时间超过30天
     */
    CANCEL_BEFORE_DAYS("003"),
    /**
     * 配置数据为空
     */
    CONFIG_DATA_EMPTY("002"),
    /**
     * 订单数据为空
     */
    ORDER_DATA_EMPTY("001"),
    /**
     * 未知错误
     */
    UNKNOWN("");

    private final String code;

    ApprovalFlowReuseFailCodeEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }

    public static ApprovalFlowReuseFailCodeEnum getApprovalFlowReuseFailCodeEnumByCode(String code) {
        if (StringUtil.isBlank(code)) {
            return UNKNOWN;
        }
        for (ApprovalFlowReuseFailCodeEnum failCode : values()) {
            if (failCode.getCode().equals(code)) {
                return failCode;
            }
        }
        return UNKNOWN;
    }
}
