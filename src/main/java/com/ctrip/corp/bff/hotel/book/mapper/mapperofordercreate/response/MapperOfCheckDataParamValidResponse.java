package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response;

import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceInput;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.common.constant.CustomConfigKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.util.CheckDataUtil;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.tools.contract.CheckDataResponseType;
import com.ctrip.corp.bff.tools.contract.DataCheckResult;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/23 20:12
 */
@Component public class MapperOfCheckDataParamValidResponse
    extends AbstractMapper<Tuple2<OrderCreateRequestType, CheckDataResponseType>, Boolean> {
    // 邮箱
    private static final String SHARK_EMAIL = "ctrip.com.hotel.booking.biz.text.transferdata.email";
    // 手机
    private static final String SHARK_PHONE = "ctrip.com.hotel.booking.biz.text.transferdata.phone";
    // 证件
    private static final String SHARK_CARD = "ctrip.com.hotel.booking.biz.text.transferdata.card";
    // 入住人：{0}信息不正确，请返回修改
    private static final String CLIENT_PSG_TRANSFER_DATA_ERROR = "ctrip.com.hotel.booking.biz.text.client.transferdata";
    // 联系人：{0}信息不正确，请返回修改
    private static final String CONTACT_PSG_TRANSFER_DATA_ERROR =
        "ctrip.com.hotel.booking.biz.text.contact.transferdata";
    // 保险出行人：{0}信息不正确，请返回修改
    private static final String INSURANCE_PSG_TRANSFER_DATA_ERROR =
        "ctrip.com.hotel.booking.biz.text.insurance.transferdata";

    private static final String TRUE_STR = "T";

    @Override protected Boolean convert(Tuple2<OrderCreateRequestType, CheckDataResponseType> tuple) {
        return true;
    }

    @Override protected ParamCheckResult check(Tuple2<OrderCreateRequestType, CheckDataResponseType> tuple) {
        OrderCreateRequestType orderCreateRequestType = tuple.getT1();
        CheckDataResponseType checkDataResponseType = tuple.getT2();
        if (QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.CHECK_DATA_PASS,
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId())) {
            return null;
        }
        // 敏感信息格式校验
        if (checkDataResponseType == null || CollectionUtil.isEmpty(checkDataResponseType.getResultList())) {
            return null;
        }
        List<DataCheckResult> dataCheckResultFail = checkDataResponseType.getResultList().stream()
            .filter(r -> r != null && StringUtil.equalsIgnoreCase(r.getCheckResult(), "F"))
            .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(dataCheckResultFail)) {
            return null;
        }
        Map<String, String> dataCheckResultFailMap = dataCheckResultFail.stream()
            .collect(Collectors.toMap(DataCheckResult::getKey, DataCheckResult::getCheckResult));
        ParamCheckResult paramCheckResult;
        // 入住人校验结果
        paramCheckResult = validCheckDataClientPsg(dataCheckResultFailMap, orderCreateRequestType.getHotelBookPassengerInputs());
        if (paramCheckResult != null) {
            return paramCheckResult;
        }
        // 联系人校验结果
        paramCheckResult = validCheckDataContactPsg(dataCheckResultFailMap);
        if (paramCheckResult != null) {
            return paramCheckResult;
        }
        // 保险出行人校验结果
        paramCheckResult = validCheckDataInsurancePsg(dataCheckResultFailMap, orderCreateRequestType.getHotelInsuranceInput());
        if (paramCheckResult != null) {
            return paramCheckResult;
        }
        // 发票邮箱检验结果
        paramCheckResult = validCheckDataInvoiceEmail(dataCheckResultFail);
        if (paramCheckResult != null) {
            return paramCheckResult;
        }
        return paramCheckResult;
    }

    /**
     * 发票邮箱验证不通过
     *
     * @return
     */
    protected ParamCheckResult validCheckDataInvoiceEmail(List<DataCheckResult> dataCheckResultFail) {
        if (dataCheckResultFail.stream().anyMatch(
            d -> StringUtil.containsIgnoreCase(d.getKey(), CheckDataUtil.KEY_INVOICE_EMAIL_PREFIX) && !checkPass(
                d.getCheckResult()))) {
            return new ParamCheckResult(false,
                OrderCreateErrorEnum.PARAM_VALID_INVOICE_EMAIL_SENSITIVE_INFORMATION_ERROR,
                OrderCreateErrorEnum.PARAM_VALID_INVOICE_EMAIL_SENSITIVE_INFORMATION_ERROR.getErrorCode().toString());
        }
        return null;
    }

    /**
     * 联系人：手机、邮箱信息不正确，请返回修改
     *
     * @return
     */
    protected ParamCheckResult validCheckDataContactPsg(Map<String, String> dataCheckResultFailMap) {
        String checkResultEmail = dataCheckResultFailMap.get(CheckDataUtil.KEY_CONTACT_PSG_EMAIL);
        String checkResultPhone = dataCheckResultFailMap.get(CheckDataUtil.KEY_CONTACT_PSG_PHONE);
        if (checkPass(checkResultEmail) && checkPass(checkResultPhone)) {
            return null;
        }
        return new ParamCheckResult(false,
            OrderCreateErrorEnum.PARAM_VALID_CONTACT_PSG_SENSITIVE_INFORMATION_ERROR.getErrorCode(),
            OrderCreateErrorEnum.PARAM_VALID_CONTACT_PSG_SENSITIVE_INFORMATION_ERROR.getErrorCode().toString(),
            OrderCreateErrorEnum.PARAM_VALID_CONTACT_PSG_SENSITIVE_INFORMATION_ERROR.getErrorMessage(),
            StringUtil.indexedFormat(BFFSharkUtil.getSharkValue(CONTACT_PSG_TRANSFER_DATA_ERROR),
                getTransferErrorMsg(checkResultPhone, checkResultEmail)));
    }

    private String getTransferErrorMsg(String phonePass, String emailPass) {
        return getTransferErrorMsg(phonePass, emailPass, TRUE_STR);
    }

    /**
     * 入住人：张朝阳/zhang chao yang 手机、邮箱、证件信息不正确，请返回修改；入住人：张朝阳2/zhang chao yang2 手机、邮箱信息不正确，请返回修改
     *
     * @return
     */
    protected ParamCheckResult validCheckDataClientPsg(Map<String, String> dataCheckResultFailMap,
        List<HotelBookPassengerInput> orderCreateHotelPassengerInputs) {
        if (orderCreateHotelPassengerInputs == null) {
            return null;
        }
        List<String> clientPsgErrorMsg = new ArrayList<>();
        orderCreateHotelPassengerInputs.stream().forEach(p -> {
            if (p == null) {
                return;
            }
            String id =
                StringUtil.isNotBlank(p.getHotelPassengerInput().getUid()) ? p.getHotelPassengerInput().getUid() :
                    p.getHotelPassengerInput().getInfoId();
            String checkResultEmail = dataCheckResultFailMap.get(
                CheckDataUtil.getClientPsgEmailKey(id, orderCreateHotelPassengerInputs.indexOf(p)));
            String checkResultPhone = dataCheckResultFailMap.get(
                CheckDataUtil.getClientPsgPhoneKey(id, orderCreateHotelPassengerInputs.indexOf(p)));
            String checkResultCard = dataCheckResultFailMap.get(
                CheckDataUtil.getClientPsgCardKey(id, orderCreateHotelPassengerInputs.indexOf(p)));
            if (checkPass(checkResultEmail) && checkPass(checkResultPhone) && checkPass(checkResultCard)) {
                return;
            }
            clientPsgErrorMsg.add(
                getName(p) + " " + getTransferErrorMsg(checkResultPhone, checkResultEmail, checkResultCard));
        });
        if (CollectionUtil.isEmpty(clientPsgErrorMsg)) {
            return null;
        }
        return new ParamCheckResult(false,
            OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_SENSITIVE_INFORMATION_ERROR.getErrorCode(),
            OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_SENSITIVE_INFORMATION_ERROR.getErrorCode().toString(),
            OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_SENSITIVE_INFORMATION_ERROR.getErrorMessage(),
            StringUtil.indexedFormat(BFFSharkUtil.getSharkValue(CLIENT_PSG_TRANSFER_DATA_ERROR),
                clientPsgErrorMsg.stream().collect(Collectors.joining(";"))));
    }

    protected ParamCheckResult validCheckDataInsurancePsg(Map<String, String> dataCheckResultFailMap,
        HotelInsuranceInput insuranceInput) {
        if (insuranceInput == null) {
            return null;
        }
        if (CollectionUtil.isEmpty(insuranceInput.getHotelInsuranceDetailInputs())) {
            return null;
        }
        List<String> clientPsgErrorMsg = new ArrayList<>();
        insuranceInput.getHotelInsuranceDetailInputs().stream().forEach(x -> {
            if (x == null || CollectionUtil.isEmpty(x.getInsuranceHotelBookPassengerInputs())) {
                return;
            }
            x.getInsuranceHotelBookPassengerInputs().stream().forEach(p -> {
                if (p == null) {
                    return;
                }
                String id =
                    StringUtil.isNotBlank(p.getHotelPassengerInput().getUid()) ? p.getHotelPassengerInput().getUid() :
                        p.getHotelPassengerInput().getInfoId();
                String checkResultEmail = dataCheckResultFailMap.get(
                    CheckDataUtil.getInsurancePsgEmailKey(id, x.getInsuranceHotelBookPassengerInputs().indexOf(p),
                        insuranceInput.getHotelInsuranceDetailInputs().indexOf(x)));
                String checkResultPhone = dataCheckResultFailMap.get(
                    CheckDataUtil.getInsurancePsgPhoneKey(id, x.getInsuranceHotelBookPassengerInputs().indexOf(p),
                        insuranceInput.getHotelInsuranceDetailInputs().indexOf(x)));
                String checkResultCard = dataCheckResultFailMap.get(
                    CheckDataUtil.getInsurancePsgCardKey(id, x.getInsuranceHotelBookPassengerInputs().indexOf(p),
                        insuranceInput.getHotelInsuranceDetailInputs().indexOf(x)));
                if (checkPass(checkResultEmail) && checkPass(checkResultPhone) && checkPass(checkResultCard)) {
                    return;
                }
                clientPsgErrorMsg.add(
                    getName(p) + " " + getTransferErrorMsg(checkResultPhone, checkResultEmail, checkResultCard));
            });
        });
        if (CollectionUtil.isEmpty(clientPsgErrorMsg)) {
            return null;
        }
        return new ParamCheckResult(false,
            OrderCreateErrorEnum.PARAM_VALID_INSURANCE_PSG_SENSITIVE_INFORMATION_ERROR.getErrorCode(),
            OrderCreateErrorEnum.PARAM_VALID_INSURANCE_PSG_SENSITIVE_INFORMATION_ERROR.getErrorCode().toString(),
            OrderCreateErrorEnum.PARAM_VALID_INSURANCE_PSG_SENSITIVE_INFORMATION_ERROR.getErrorMessage(),
            StringUtil.indexedFormat(BFFSharkUtil.getSharkValue(INSURANCE_PSG_TRANSFER_DATA_ERROR),
                clientPsgErrorMsg.stream().collect(Collectors.joining(";"))));
    }

    private String getTransferErrorMsg(String phonePass, String emailPass, String cardPass) {
        List<String> transferErrorMsg = new ArrayList<>();
        if (!checkPass(phonePass)) {
            transferErrorMsg.add(BFFSharkUtil.getSharkValue(SHARK_PHONE));
        }
        if (!checkPass(emailPass)) {
            transferErrorMsg.add(BFFSharkUtil.getSharkValue(SHARK_EMAIL));
        }
        if (!checkPass(cardPass)) {
            transferErrorMsg.add(BFFSharkUtil.getSharkValue(SHARK_CARD));
        }
        if (CollectionUtil.isEmpty(transferErrorMsg)) {
            return null;
        }
        return transferErrorMsg.stream().collect(Collectors.joining("、"));
    }

    /**
     * 中文/英文：张朝阳/zhang chao yang
     * 仅中文：张朝阳
     * 仅英文：zhang/chao yang
     *
     * @param passengerInput
     * @return
     */
    private String getName(HotelBookPassengerInput passengerInput) {
        String ename = getEname(passengerInput);
        if (StringUtil.isNotBlank(passengerInput.getName()) && StringUtil.isNotBlank(ename)) {
            return passengerInput.getName() + "/" + ename.replace("/", " ");
        }
        if (StringUtil.isNotBlank(passengerInput.getName())) {
            return passengerInput.getName();
        }
        if (StringUtil.isNotBlank(ename)) {
            return ename;
        }
        return "";
    }

    private String getEname(HotelBookPassengerInput passengerInput) {
        if (passengerInput.getPassengerBasicInfo() == null) {
            return "";
        }
        if (StringUtil.isNotBlank(passengerInput.getPassengerBasicInfo().getPreferFirstName()) && StringUtil.isNotBlank(
            passengerInput.getPassengerBasicInfo().getPreferLastName())) {
            return passengerInput.getPassengerBasicInfo().getPreferLastName() + "/"
                + passengerInput.getPassengerBasicInfo().getPreferFirstName();
        }
        return "";
    }

    private boolean checkPass(String checkResult) {
        if (StringUtil.isBlank(checkResult)) {
            return true;
        }
        return StringUtil.equalsIgnoreCase(checkResult, TRUE_STR);
    }
}
