package com.ctrip.corp.bff.hotel.book.common.enums.passenger;

import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;

/**
 * @Author: z.c. wang
 * @Description 出行人校验错误
 * @Date: 2025/3/14 16:27
 * @Version 1.0
 */
public enum PassengerCheckErrorEnum {
    /**
     * 出行人为空
     */
    PASSENGER_EMPTY_INVALID("ERROR"),

    /**
     * 出行人超出最大限制
     */
    PASSENGER_OVER_MAX("ERROR"),

    /**
     * 出行人超出最小限制
     */
    PASSENGER_OVER_MIN("ERROR"),

    /**
     * 出行人姓名为空
     */
    PASSENGER_NAME_NULL("ERROR"),

    /**
     * 出行人英文名为空
     */
    PASSENGER_EN_NAME_NULL("ERROR"),

    /**
     * 出行人需要当地名
     */
    PASSENGER_NEED_LOCAL_NAME("ERROR"),

    /**
     * 出行人需要英文名
     */
    PASSENGER_NEED_EN_NAME("ERROR"),

    /**
     * 国籍不在资源白名单内
     */
    NATIONALITY_NOT_ALLOW("ERROR"),

    /**
     * 国籍为空
     */
    NATIONALITY_NULL("ERROR"),

    /**
     * 出行人为非员工
     */
    PASSENGER_NOT_EMPLOYEE("ERROR"),

    /**
     * 人下不存在资源所需证件
     */
    CERTIFICATE_NOT_CONTAIN("ERROR"),


    CERTIFICATE_WITH_EN_NAME_NOT_CONTAIN("ERROR"),

    /**
     * 人下存在资源所需证件但未选中
     */
    CERTIFICATE_CONTAIN_CHOOSE_WRONG("ERROR"),

    /**
     * 证件过期
     */
    CERTIFICATE_EXPIRATION_EXPIRED("ERROR"),

    /**
     * 证件有效期不满6个月
     */
    CERTIFICATE_EXPIRATION_EXPIRED_WARNING("WARNING"),

    /**
     * 仅限内宾
     */
    ONLY_FOR_CN_GUEST("ERROR"),

    /**
     * 仅限中宾
     */
    ONLY_FOR_CN_GAT_GUEST("ERROR"),

    CERTIFICATE_LACK_NAME("ERROR"),

    /**
     * 证件缺英文姓名
     */
    CERTIFICATE_LACK_EN_NAME("ERROR"),

    /**
     * 请选择性别
     */
    GENDER_NULL("ERROR"),

    /**
     * 请填写工作邮箱
     */
    EMAIL_NULL("ERROR"),

    /**
     * 请填写手机号
     */
    PHONE_NULL("ERROR"),

    /**
     * 请选择出生日期
     */
    BIRTH_NULL("ERROR"),

    /**
     * 证件号码错误
     */
    ID_NUMBER_INVALID("ERROR"),

    /**
     * 法定名不合法
     */
    LEGAL_FIRST_NAME_INVALID("ERROR"),

    /**
     * 法定姓不合法
     */
    LEGAL_LAST_NAME_INVALID("ERROR"),

    /**
     * 证件类型不支持
     */
    CERTIFICATE_TYPE_NOT_SUPPORT("ERROR"),
    ;

    /**
     * 错误类型 WARNING、ERROR
     */
    private final String errorType;

    PassengerCheckErrorEnum(String errorType) {
        this.errorType = errorType;
    }

    public String getErrorType() {
        return errorType;
    }

    public String getSharkKey() {
        return "passenger.check.tip." + name().toLowerCase();
    }

    public String getErrorMessage() {
        String sharkValue = BFFSharkUtil.getSharkValue(getSharkKey());
        if (StringUtil.isNotBlank(sharkValue)) {
            return sharkValue;
        }
        return name();
    }
}
