package com.ctrip.corp.bff.hotel.book.qconfig.entity;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/14 7:24
 */
public class CustomizedSharkConfig {

    /**
     * 定制shark场景描述
     */
    private String sceneDes;
    /**
     * shark定制场景
     */
    private String scene;

    /**
     * 支持配置的公司集合
     */
    private List<String> corpIds;

    /**
     * 每个公司可能有多个定制shark
     */
    private List<String> sharkKeyPres;


    public String getSceneDes() {
        return sceneDes;
    }

    public void setSceneDes(String sceneDes) {
        this.sceneDes = sceneDes;
    }

    public String getScene() {
        return scene;
    }

    public void setScene(String scene) {
        this.scene = scene;
    }

    public List<String> getCorpIds() {
        return corpIds;
    }

    public void setCorpIds(List<String> corpIds) {
        this.corpIds = corpIds;
    }

    public List<String> getSharkKeyPres() {
        return sharkKeyPres;
    }

    public void setSharkKeyPres(List<String> sharkKeyPres) {
        this.sharkKeyPres = sharkKeyPres;
    }
}
