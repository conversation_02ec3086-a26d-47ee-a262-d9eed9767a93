package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckAgreementRcInfoType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckBookAheadRcInfoType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckOverStandardRcInfoType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.RoomModeInfoType;
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextResponseType;
import com.ctrip.corp.agg.hotel.tmc.entity.FinalFloatingAmountSettingType;
import com.ctrip.corp.agg.hotel.tmc.entity.FinalPolicyType;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfHotelTravelPolicy;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelRcInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.common.RcToken;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.RCContent;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.RCInfo;
import com.ctrip.corp.bff.hotel.book.common.constant.OverStandardTypeConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.SharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.RcCustomizedSharkByCorpEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.RcTypeEnum;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.CustomizedSharkConfig;
import com.ctrip.corp.foundation.common.enums.LanguageLocaleEnum;
import com.ctrip.corp.foundation.common.util.StringUtilsExt;
import com.google.common.collect.Lists;
import corp.user.service.corp4jservice.GetReasoncodesResponseType;
import corp.user.service.corp4jservice.ReasoncodeInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/9/14 10:30
 */
public class HotelRCUtil {
    public static String getValueByCorpId(String inputKeyPre, String sharkKey, String corpId, String scene,
        List<CustomizedSharkConfig> customizedSharkConfigs) {
        if (CollectionUtil.isEmpty(customizedSharkConfigs)) {
            return BFFSharkUtil.getSharkValue(sharkKey);
        }
        if (StringUtil.isBlank(inputKeyPre) || StringUtil.isBlank(corpId) || StringUtil.isBlank(scene)) {
            return "";
        }
        // 对应shark定制场景下 支持该公司的sharkKey配置
        CustomizedSharkConfig customizedSharkConfigModel =
            customizedSharkConfigs.stream().filter(o -> scene.equals(o.getScene()))
                .filter(o -> o.getCorpIds().contains(corpId)).filter(o -> o.getSharkKeyPres().contains(inputKeyPre))
                .findFirst().orElse(null);

        if (customizedSharkConfigModel == null) {
            return BFFSharkUtil.getSharkValue(sharkKey);
        }
        String sharkKeyByCorpId = StringUtil.indexedFormat("{0}.{1}", inputKeyPre, corpId);
        return BFFSharkUtil.getSharkValue(sharkKeyByCorpId);
    }

    public static String overStandardType(CheckOverStandardRcInfoType checkOverStandardRcInfoType) {
        if (checkOverStandardRcInfoType == null) {
            return null;
        }
        boolean overPrice = !Boolean.TRUE.equals(checkOverStandardRcInfoType.isPriceInControl());
        boolean overStar = !Boolean.TRUE.equals(checkOverStandardRcInfoType.isStarInControl());
        // 星级和价格都超标
        if (overPrice && overStar){
            return OverStandardTypeConstant.OVER_PRICE_AND_STAR;
        }
        // 价格超标
        if (overPrice){
            return OverStandardTypeConstant.OVER_PRICE;
        }
        // 星级超标
        if (overStar){
            return OverStandardTypeConstant.OVER_STAR;
        }
        return null;
    }

    public static HotelRcInfo buildRCContentOverStand(GetReasoncodesResponseType getReasoncodesResponseType,
        IntegrationSoaRequestType integrationSoaRequestType, CheckOverStandardRcInfoType checkOverStandardRcInfoType,
        List<CustomizedSharkConfig> customizedSharkConfigs,
        WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo hotelTravelPolicyInfo) {
        List<ReasoncodeInfo> reasonCodeInfos = getPriceReasons(getReasoncodesResponseType, RcTypeEnum.LOW_PRICE);
        HotelRcInfo hotelRcInfo = new HotelRcInfo();
        hotelRcInfo.setRcType(RcTypeEnum.LOW_PRICE.name());
        hotelRcInfo.setRcTitle(
            buildRcTitleOverStand(checkOverStandardRcInfoType, integrationSoaRequestType, customizedSharkConfigs,
                hotelTravelPolicyInfo));
        hotelRcInfo.setRcDesc(RcTypeEnum.LOW_PRICE.getRcDesc());
        reasonCodeInfos.stream().filter(o -> EDIT_RC_CODE.equals(o.getReasonCode())).collect(Collectors.toList())
            .stream().findFirst().ifPresent(reasoncodeInfoVV -> hotelRcInfo.setRcTip(
                getRcValueByLanguage(reasoncodeInfoVV.getRcTip(), reasoncodeInfoVV.getRcTipEn(),
                    integrationSoaRequestType)));
        RCInfo rcInfo = new RCInfo();
        rcInfo.setRcContents(buildRcContents(reasonCodeInfos, RcTypeEnum.LOW_PRICE, integrationSoaRequestType));
        hotelRcInfo.setRcInfo(rcInfo);
        return hotelRcInfo;
    }

    private static String buildRcTitleOverStand(CheckOverStandardRcInfoType checkOverStandardRcInfoType,
        IntegrationSoaRequestType integrationSoaRequestType, List<CustomizedSharkConfig> customizedSharkConfigs,
        WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo hotelTravelPolicyInfo) {
        List<Integer> overStandardRoomIndex = getOverStandardRoomIndex(checkOverStandardRcInfoType);
        String title = null;
        String overStandardType = HotelRCUtil.overStandardType(checkOverStandardRcInfoType);
        if (CollectionUtils.isNotEmpty(overStandardRoomIndex) && isShowRoomTitle(checkOverStandardRcInfoType)) {
            // 按房间管控 出行人模式低价RC按房间管控返回Title
            StringBuilder stringBuilder = new StringBuilder();
            for (int i = 0; i < overStandardRoomIndex.size(); i++) {
                if (i == overStandardRoomIndex.size() - 1) {
                    stringBuilder.append(StringUtil.indexedFormat(BFFSharkUtil.getSharkValue(SharkKeyConstant.RC_ROOM),
                        overStandardRoomIndex.get(i)));
                } else {
                    stringBuilder.append(StringUtil.indexedFormat(BFFSharkUtil.getSharkValue(SharkKeyConstant.RC_ROOM),
                        overStandardRoomIndex.get(i))).append("、");
                }
            }
            // 获取按公司定制rc：若该公司且输入的keyPre支持定制,则获取到的为定制文案，否则为keyPre对应的兜底文案
            RcCustomizedSharkByCorpEnum rcCustomizedSharkByCorpEnumHasSuffix =
                RcCustomizedSharkByCorpEnum.getNowEnumByRcType(
                    RcCustomizedSharkByCorpEnum.LOW_PRICE_R_RC.getRcCode() + getRcTypeSuffix(RcTypeEnum.LOW_PRICE,
                        overStandardType));
            String customizedRc = HotelRCUtil.getValueByCorpId(
                rcCustomizedSharkByCorpEnumHasSuffix != null ? rcCustomizedSharkByCorpEnumHasSuffix.getShark() :
                    RcCustomizedSharkByCorpEnum.LOW_PRICE_R_RC.getShark(),
                rcCustomizedSharkByCorpEnumHasSuffix != null ? rcCustomizedSharkByCorpEnumHasSuffix.getShark() : null,
                integrationSoaRequestType.getUserInfo().getCorpId(),
                RcCustomizedSharkByCorpEnum.LOW_PRICE_R_RC.getScene(), customizedSharkConfigs);
            if (StringUtil.isNotBlank(customizedRc) && customizedRc.contains("{0}")) {
                // 按房间管控话术
                title = StringUtil.indexedFormat(customizedRc, stringBuilder.toString());
            } else {
                // 只有定制话术才有不存在按房间管控的可能
                title = customizedRc;
            }
        } else {
            // 按单管控 按公司定制rc
            RcCustomizedSharkByCorpEnum rcCustomizedSharkByCorpEnum =
                RcCustomizedSharkByCorpEnum.getNowEnumByRcType(RcTypeEnum.LOW_PRICE.getCode());
            RcCustomizedSharkByCorpEnum rcCustomizedSharkByCorpEnumHasSuffix =
                RcCustomizedSharkByCorpEnum.getNowEnumByRcType(
                    RcTypeEnum.LOW_PRICE.getCode() + getRcTypeSuffix(RcTypeEnum.LOW_PRICE, overStandardType));
            String customizedRc = HotelRCUtil.getValueByCorpId(
                rcCustomizedSharkByCorpEnumHasSuffix != null ? rcCustomizedSharkByCorpEnumHasSuffix.getShark() :
                    rcCustomizedSharkByCorpEnum.getShark(),
                rcCustomizedSharkByCorpEnumHasSuffix != null ? rcCustomizedSharkByCorpEnumHasSuffix.getShark() : null,
                integrationSoaRequestType.getUserInfo().getCorpId(),
                RcCustomizedSharkByCorpEnum.getNowEnumByRcType(RcTypeEnum.LOW_PRICE.getCode()).getScene(),
                customizedSharkConfigs);
            if (StringUtil.isNotBlank(customizedRc)) {
                title = customizedRc;
            } else {
                if (OverStandardTypeConstant.OVER_PRICE.equals(overStandardType)) {
                    title = BFFSharkUtil.getSharkValue(RcCustomizedSharkByCorpEnum.LOW_PRICE_RC_PRICE.getShark());
                }
                if (OverStandardTypeConstant.OVER_STAR.equals(overStandardType)) {
                    title = BFFSharkUtil.getSharkValue(RcCustomizedSharkByCorpEnum.LOW_PRICE_RC_STAR.getShark());
                }
                if (StringUtil.isEmpty(title)) {
                    title = RcTypeEnum.LOW_PRICE.getRcTitle();
                }
            }
        }
        if (OverStandardTypeConstant.OVER_PRICE.equalsIgnoreCase(overStandardType) && Optional.ofNullable(
            getOpenFloatAmount(hotelTravelPolicyInfo)).orElse(false)) {
            title += BFFSharkUtil.getSharkValue(
                StringUtilsExt.format(SharkKeyConstant.FLOAT_OVER_PRICE, getFloatAmountType(hotelTravelPolicyInfo)));
        }
        return title;
    }

    private static String getRcTypeSuffix(RcTypeEnum rcTypeEnum, String overStandardType) {
        if (rcTypeEnum != RcTypeEnum.LOW_PRICE) {
            return "";
        }
        if (OverStandardTypeConstant.OVER_PRICE.equals(overStandardType)) {
            return "_PRICE";
        }
        if (OverStandardTypeConstant.OVER_STAR.equals(overStandardType)) {
            return "_STAR";
        }
        return "";
    }

    private static Boolean getOpenFloatAmount(WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo hotelTravelPolicyInfo) {
        return Optional.ofNullable(hotelTravelPolicyInfo)
            .map(WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo::isOpenFloatAmount).orElse(null);
    }

    private static String getFloatAmountType(WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo hotelTravelPolicyInfo) {
        return Optional.ofNullable(hotelTravelPolicyInfo)
            .map(WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo::getFloatAmountType).orElse("");
    }

    private static List<Integer> getOverStandardRoomIndex(CheckOverStandardRcInfoType checkOverStandardRcInfoType) {
        if (checkOverStandardRcInfoType == null) {
            return Lists.newArrayList();
        }
        if (CollectionUtil.isEmpty(checkOverStandardRcInfoType.getRoomModeInfoList())) {
            return Lists.newArrayList();
        }
        Predicate<RoomModeInfoType> controlPredicate =
            r -> BooleanUtils.isFalse(r.isPriceInControl()) || BooleanUtils.isFalse(r.isStarInControl());
        return checkOverStandardRcInfoType.getRoomModeInfoList().stream().filter(Objects::nonNull)
            .filter(controlPredicate).map(RoomModeInfoType::getRoomIndex).sorted().collect(Collectors.toList());
    }

    private static boolean isShowRoomTitle(CheckOverStandardRcInfoType checkOverStandardRcInfoType) {
        if (CollectionUtil.isEmpty(checkOverStandardRcInfoType.getRoomModeInfoList())) {
            return false;
        }
        List<Integer> overStandardRoomIndex = getOverStandardRoomIndex(checkOverStandardRcInfoType);
        if (CollectionUtil.isEmpty(overStandardRoomIndex)) {
            return false;
        }
        return overStandardRoomIndex.size() != checkOverStandardRcInfoType.getRoomModeInfoList().size();
    }

    public static final String EDIT_RC_CODE = "VV";

    public static HotelRcInfo buildRCContentAgreement(CheckAgreementRcInfoType checkAgreementRcInfoType,
        GetReasoncodesResponseType getReasoncodesResponseType, IntegrationSoaRequestType integrationSoaRequestType,
        List<CustomizedSharkConfig> customizedSharkConfigs) {
        if (checkAgreementRcInfoType == null) {
            return null;
        }
        if (!BooleanUtils.isTrue(Boolean.TRUE.equals(checkAgreementRcInfoType.isRequired()) && !Boolean.TRUE.equals(
            checkAgreementRcInfoType.isSelected()))) {
            return null;
        }
        List<ReasoncodeInfo> reasonCodeInfos = getPriceReasons(getReasoncodesResponseType, RcTypeEnum.AGREEMENT);
        HotelRcInfo hotelRcInfo = new HotelRcInfo();
        hotelRcInfo.setRcType(RcTypeEnum.AGREEMENT.name());
        hotelRcInfo.setRcTitle(buildRcTitleAgreement(integrationSoaRequestType, customizedSharkConfigs));
        hotelRcInfo.setRcDesc(RcTypeEnum.AGREEMENT.getRcDesc());
        hotelRcInfo.setRcTip(buildRcTip(reasonCodeInfos, integrationSoaRequestType));
        reasonCodeInfos.stream().filter(o -> EDIT_RC_CODE.equals(o.getReasonCode())).collect(Collectors.toList())
            .stream().findFirst().ifPresent(reasoncodeInfoVV -> hotelRcInfo.setRcTip(
                getRcValueByLanguage(reasoncodeInfoVV.getRcTip(), reasoncodeInfoVV.getRcTipEn(),
                    integrationSoaRequestType)));
        RCInfo rcInfo = new RCInfo();
        rcInfo.setRcContents(buildRcContents(reasonCodeInfos, RcTypeEnum.AGREEMENT, integrationSoaRequestType));
        hotelRcInfo.setRcInfo(rcInfo);
        return hotelRcInfo;
    }

    public static String buildRcTip(List<ReasoncodeInfo> rcInfo, IntegrationSoaRequestType integrationSoaRequestType) {
        if (CollectionUtil.isEmpty(rcInfo)) {
            return null;
        }
        ReasoncodeInfo rc = CollectionUtil.findFirst(rcInfo, t -> t != null && HotelRCUtil.EDIT_RC_CODE.equals(t.getReasonCode()));
        if (rc == null) {
            return null;
        }
        LanguageLocaleEnum languageLocaleEnum = LanguageLocaleEnum.getLanguageLocaleEnum(
                Optional.ofNullable(integrationSoaRequestType).map(IntegrationSoaRequestType::getLanguage).orElse(null));
        return languageLocaleEnum == LanguageLocaleEnum.ZH_CN || languageLocaleEnum == LanguageLocaleEnum.ZH_HK || languageLocaleEnum == LanguageLocaleEnum.ZH_TW
                ? rc.getRcTip() : rc.getRcTipEn();
    }

    private static String buildRcTitleAgreement(IntegrationSoaRequestType integrationSoaRequestType,
        List<CustomizedSharkConfig> customizedSharkConfigs) {
        // 按公司定制rc
        String customizedRc = HotelRCUtil.getValueByCorpId(
            RcCustomizedSharkByCorpEnum.getNowEnumByRcType(RcTypeEnum.AGREEMENT.getCode()).getShark(),
            RcCustomizedSharkByCorpEnum.getNowEnumByRcType(RcTypeEnum.AGREEMENT.getCode()).getShark(),
            integrationSoaRequestType.getUserInfo().getCorpId(),
            RcCustomizedSharkByCorpEnum.getNowEnumByRcType(RcTypeEnum.AGREEMENT.getCode()).getScene(),
            customizedSharkConfigs);
        if (StringUtil.isNotBlank(customizedRc)) {
            return customizedRc;
        }
        return RcTypeEnum.AGREEMENT.getRcTitle();
    }

    public static HotelRcInfo buildRCContentBookAhead(CheckBookAheadRcInfoType checkBookAheadRcInfoType,
        GetReasoncodesResponseType getReasoncodesResponseType, IntegrationSoaRequestType integrationSoaRequestType,
        List<CustomizedSharkConfig> customizedSharkConfigs) {
        if (checkBookAheadRcInfoType == null) {
            return null;
        }
        if (!org.apache.commons.lang3.BooleanUtils.isTrue(
            Boolean.TRUE.equals(checkBookAheadRcInfoType.isRequired()) && !Boolean.TRUE.equals(
                checkBookAheadRcInfoType.isSelected()))) {
            return null;
        }
        List<ReasoncodeInfo> reasonCodeInfos = getPriceReasons(getReasoncodesResponseType, RcTypeEnum.BOOK_AHEAD);
        HotelRcInfo hotelRcInfo = new HotelRcInfo();
        hotelRcInfo.setRcType(RcTypeEnum.BOOK_AHEAD.name());
        hotelRcInfo.setRcTitle(buildRcTitleBookAhead(integrationSoaRequestType, customizedSharkConfigs,
            checkBookAheadRcInfoType.getBookAheadDay()));
        hotelRcInfo.setRcDesc(RcTypeEnum.BOOK_AHEAD.getRcDesc());
        hotelRcInfo.setRcTip(buildRcTip(reasonCodeInfos, integrationSoaRequestType));
        reasonCodeInfos.stream().filter(o -> EDIT_RC_CODE.equals(o.getReasonCode())).collect(Collectors.toList())
            .stream().findFirst().ifPresent(reasoncodeInfoVV -> hotelRcInfo.setRcTip(
                getRcValueByLanguage(reasoncodeInfoVV.getRcTip(), reasoncodeInfoVV.getRcTipEn(),
                    integrationSoaRequestType)));
        hotelRcInfo.setBookAheadDay(checkBookAheadRcInfoType.getBookAheadDay() != null ? String.valueOf(checkBookAheadRcInfoType.getBookAheadDay()) : null);
        RCInfo rcInfo = new RCInfo();
        rcInfo.setRcContents(buildRcContents(reasonCodeInfos, RcTypeEnum.BOOK_AHEAD, integrationSoaRequestType));
        hotelRcInfo.setRcInfo(rcInfo);
        return hotelRcInfo;
    }

    public static List<ReasoncodeInfo> getPriceReasonsWithoutException(GetReasoncodesResponseType getReasoncodesResponseType,
                                                       RcTypeEnum rcTypeEnum) {
        if (getReasoncodesResponseType == null) {
            return null;

        }
        if (CollectionUtil.isEmpty(getReasoncodesResponseType.getReasonCodes())) {
            return null;
        }
        List<ReasoncodeInfo> reasonCodeInfos =
                getReasoncodesResponseType.getReasonCodes().stream().filter(Objects::nonNull).filter(
                                reasonCode -> reasonCode.getRcType() != null
                                        && (reasonCode.getRcType() & rcTypeEnum.getRcType()) == rcTypeEnum.getRcType())
                        .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(reasonCodeInfos)) {
            return null;
        }
        Collections.sort(reasonCodeInfos, ((o1, o2) -> {
            if (EDIT_RC_CODE.equals(o1.getReasonCode())) {
                return 1;
            }
            if (EDIT_RC_CODE.equals(o2.getReasonCode())) {
                return -1;
            }
            return 0;
        }));
        return reasonCodeInfos;
    }

    public static List<ReasoncodeInfo> getPriceReasons(GetReasoncodesResponseType getReasoncodesResponseType,
        RcTypeEnum rcTypeEnum) {
        if (getReasoncodesResponseType == null) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.REASONCODE_EMPTY,
                "getRcInfos error");

        }
        if (CollectionUtil.isEmpty(getReasoncodesResponseType.getReasonCodes())) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.REASONCODE_EMPTY,
                "getRcInfos error");
        }
        List<ReasoncodeInfo> reasonCodeInfos =
            getReasoncodesResponseType.getReasonCodes().stream().filter(Objects::nonNull).filter(
                    reasonCode -> reasonCode.getRcType() != null
                        && (reasonCode.getRcType() & rcTypeEnum.getRcType()) == rcTypeEnum.getRcType())
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(reasonCodeInfos)) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.REASONCODE_EMPTY,
                "getRcInfos error");
        }
        Collections.sort(reasonCodeInfos, ((o1, o2) -> {
            if (EDIT_RC_CODE.equals(o1.getReasonCode())) {
                return 1;
            }
            if (EDIT_RC_CODE.equals(o2.getReasonCode())) {
                return -1;
            }
            return 0;
        }));
        return reasonCodeInfos;
    }

    private static String buildRcTitleBookAhead(IntegrationSoaRequestType integrationSoaRequestType,
        List<CustomizedSharkConfig> customizedSharkConfigs, Integer daysInAdvance) {
        String title = null;
        // 按公司定制rc
        String customizedRc = HotelRCUtil.getValueByCorpId(
            RcCustomizedSharkByCorpEnum.getNowEnumByRcType(RcTypeEnum.BOOK_AHEAD.getCode()).getShark(),
            RcCustomizedSharkByCorpEnum.getNowEnumByRcType(RcTypeEnum.BOOK_AHEAD.getCode()).getShark(),
            integrationSoaRequestType.getUserInfo().getCorpId(),
            RcCustomizedSharkByCorpEnum.getNowEnumByRcType(RcTypeEnum.BOOK_AHEAD.getCode()).getScene(),
            customizedSharkConfigs);
        if (StringUtil.isNotBlank(customizedRc)) {
            title = customizedRc;
        } else {
            title = RcTypeEnum.BOOK_AHEAD.getRcTitle();
        }
        if (title.contains("{0}")) {
            return title.replace("{0}", daysInAdvance == null ? "1" : daysInAdvance.toString());
        } else {
            return title;
        }
    }

    private static String getRcValueByLanguage(String rcValue, String defaultEnRcValue,
        IntegrationSoaRequestType requestType) {
        return LanguageLocaleEnum.ZH_CN.getLanguageLocaleString().equalsIgnoreCase(requestType.getLanguage()) ?
            rcValue : defaultEnRcValue;
    }

    public static List<RCContent> buildRcContents(List<ReasoncodeInfo> reasonCodeInfos, RcTypeEnum rcTypeEnum,
        IntegrationSoaRequestType integrationSoaRequestType) {
        if (CollectionUtil.isEmpty(reasonCodeInfos)) {
            return null;
        }
        List<RCContent> rcContents = new ArrayList<>();
        reasonCodeInfos.stream().forEach(reasonCodeInfo -> {
            if (StringUtil.isEmpty(reasonCodeInfo.getReasonInfo())) {
                return;
            }
            RCContent rcContent = new RCContent();
            rcContent.setType(rcTypeEnum.name());
            rcContent.setCode(reasonCodeInfo.getReasonCode());
            String rcValue = getRcValueByLanguage(reasonCodeInfo.getReasonInfo(), reasonCodeInfo.getReasonInfoEn(),
                integrationSoaRequestType);
            String canInput = EDIT_RC_CODE.equals(reasonCodeInfo.getReasonCode()) ? BooleanUtil.parseStr(true) :
                BooleanUtil.parseStr(false);
            rcContent.setValue(rcValue);
            rcContent.setCanInput(canInput);
            RcToken rcToken = new RcToken();
            rcToken.setType(rcTypeEnum.name());
            rcToken.setCode(reasonCodeInfo.getReasonCode());
            rcToken.setCanInput(canInput);
            rcToken.setValue(rcContent.getValue());
            rcContent.setRcToken(TokenParseUtil.generateToken(rcToken, RcToken.class));
            rcContents.add(rcContent);
        });
        return rcContents;
    }
}
