package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.agg.hotel.expense.contract.model.BaseChargeAmount;
import com.ctrip.corp.agg.hotel.expense.contract.model.ChargeAmountInfoType;
import com.ctrip.corp.agg.hotel.expense.contract.model.ServiceChargePriceType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomItem;
import com.ctrip.corp.agg.hotel.salestrategy.entity.*;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.HotelDateRangeUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfHotelTravelPolicy;
import com.ctrip.corp.bff.framework.hotel.entity.contract.AddPriceInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.BookInitResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ReservationResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ServiceChargeResourceToken;
import com.ctrip.corp.bff.framework.template.common.utils.HostUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.AmountInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple7;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.BookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.util.CorpHotelBookCommonWSUtil;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfBookingInitCommonInfo;
import com.ctrip.corp.foundation.common.util.Null;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodResponseType;
import com.ctrip.model.CalculateServiceChargeV2ResponseType;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 个人账户，奖励计算
 * @Date 2024/8/22 10:02
 * @Version 1.0
 */
@Component
public class MapperOfCalculateTravelRewardsRequestType extends AbstractMapper<Tuple4<OrderCreateRequestType,
        WrapperOfCheckAvail.CheckAvailContextInfo,
        ResourceToken,
        WrapperOfAccount.AccountInfo>, CalculateTravelRewardsRequestType> {

    public static final String CNY = "CNY";

    public static final String WECHAT = "WECHAT";
    public static final String PRIVATE = "P";
    public static final String PUBLIC = "C";

    @Override
    protected CalculateTravelRewardsRequestType convert(Tuple4<OrderCreateRequestType,
            WrapperOfCheckAvail.CheckAvailContextInfo,
            ResourceToken,
            WrapperOfAccount.AccountInfo> param) {

        CalculateTravelRewardsRequestType request = new CalculateTravelRewardsRequestType();
        OrderCreateRequestType orderCreateRequestType = param.getT1();
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo = param.getT2();
        ResourceToken resourceToken = param.getT3();
        WrapperOfAccount.AccountInfo accountInfo = param.getT4();

        request.setRequestBaseInfo(getRequestBaseInfoType(orderCreateRequestType,
                Null.or(accountInfo, WrapperOfAccount.AccountInfo::getCurrency),
                Null.or(checkAvailInfo, WrapperOfCheckAvail.CheckAvailContextInfo::getCustomExchange)));
        request.setCheckAvailId(checkAvailInfo.getWsId());
        request.setCouponAmount(getCouponAmount(checkAvailInfo));
        request.setPolicyToken(Optional.ofNullable(resourceToken)
                .map(ResourceToken::getReservationResourceToken).map(ReservationResourceToken::getPolicyToken).orElse(null));
        request.setCorpXProductList(buildCorpXProductInfoType(orderCreateRequestType));
        request.setServiceChargeAmount(buildServiceAmount(Optional.ofNullable(resourceToken).map(ResourceToken::getBookInitResourceToken)
                .map(BookInitResourceToken::getServiceChargeResourceToken).orElse(null), Null.or(accountInfo, WrapperOfAccount.AccountInfo::getCurrency)));
        request.setGuestInfoList(getGuestIngetfoList(orderCreateRequestType));
        return request;
    }

    /**
     * 叠加优惠券总金额
     * @param checkAvailInfo:房型价格下载，包括房型信息
     * @return
     */
    protected PriceType getCouponAmount(WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo) {
        if (checkAvailInfo == null) {
            return null;
        }

        // 叠加优惠券总金额
        BigDecimal amount = checkAvailInfo.getCouponAmount();
        if (amount == null || amount.intValue() == 0) {
            return null;
        }
        PriceType couponAmount = new PriceType(amount, CNY);
        return couponAmount;
    }

    /**
     * 服务费计算
     *
     * @param customCurrency
     * @return
     */
    public PriceType buildServiceAmount(ServiceChargeResourceToken serviceChargeResourceToken,
                                               String customCurrency) {
        if (serviceChargeResourceToken == null || serviceChargeResourceToken.getServiceChargeAmount() == null) {
            return null;
        }
        BigDecimal price = Optional.ofNullable(serviceChargeResourceToken.getServiceChargeAmount()).orElse(new BigDecimal(0));
        return new PriceType(price, customCurrency);
    }


    public List<CorpXProductInfoType> buildCorpXProductInfoType(OrderCreateRequestType orderCreateRequestType) {
        if (orderCreateRequestType.getHotelInsuranceInput() == null
                || CollectionUtil.isEmpty(orderCreateRequestType.getHotelInsuranceInput().getHotelInsuranceDetailInputs())) {
            return null;
        }
        List<CorpXProductInfoType> corpXProductInfoTypeList = new ArrayList<>();
        orderCreateRequestType.getHotelInsuranceInput().getHotelInsuranceDetailInputs().forEach(
                hotelInsuranceDetailInput -> {
                    if (CollectionUtil.isEmpty(hotelInsuranceDetailInput.getInsuranceHotelBookPassengerInputs())) {
                        return;
                    }
                    CorpXProductInfoType corpXProductInfoType = new CorpXProductInfoType();
                    corpXProductInfoType.setOwnerType("PERSON");
                    List<CorpXGuestInfoType> corpXGuestInfoTypes = new ArrayList<>();
                    hotelInsuranceDetailInput.getInsuranceHotelBookPassengerInputs().forEach(
                            insuranceHotelBookPassengerInput -> {
                                HotelPassengerInput hotelPassengerInput = insuranceHotelBookPassengerInput.getHotelPassengerInput();
                                CorpXGuestInfoType corpXGuestInfoType = new CorpXGuestInfoType();
                                corpXGuestInfoType.setUid(hotelPassengerInput.getUid());
                                corpXGuestInfoType.setEmployee("T".equalsIgnoreCase(hotelPassengerInput.getEmployee()));
                                corpXGuestInfoType.setRoomIndex(hotelPassengerInput.getRoomIndex());
                                corpXGuestInfoTypes.add(corpXGuestInfoType);
                            }
                    );
                    corpXProductInfoType.setGuestList(corpXGuestInfoTypes);
                    corpXProductInfoTypeList.add(corpXProductInfoType);
                }
        );
        return corpXProductInfoTypeList;
    }

    protected RequestBaseInfoType getRequestBaseInfoType(OrderCreateRequestType request, String customCurrency,
                                                       BigDecimal customExchange) {

        IntegrationSoaRequestType soaRequestType = request.getIntegrationSoaRequestType();
        String policyUid = getPolicyUid(Optional.ofNullable(request.getHotelPolicyInput())
                .map(HotelPolicyInput::getPolicyInput).orElse(null), request.getIntegrationSoaRequestType().getUserInfo());
        UserInfo userInfo = soaRequestType.getUserInfo();

        RequestBaseInfoType requestBaseInfoType = new RequestBaseInfoType();
        requestBaseInfoType.setUid(userInfo.getUserId());
        requestBaseInfoType.setCorpPayType(getTravelCorpPayType(request.getCorpPayInfo()));
        requestBaseInfoType.setBookingChannel(StrategyOfBookingInitUtil.wechatMiniProgram(request.getStrategyInfos()) ? WECHAT
                : CorpHotelBookCommonWSUtil.getChannel(soaRequestType.getSourceFrom()));
        requestBaseInfoType.setCorpId(userInfo.getCorpId());
        requestBaseInfoType.setPolicyUid(policyUid);
        requestBaseInfoType.setCustomCurrency(customCurrency);
        requestBaseInfoType.setCustomExchange(customExchange);
        requestBaseInfoType.setLocale(soaRequestType.getLanguage());
        requestBaseInfoType.setPos(HostUtil.mapToAccountPos(userInfo.getPos()));
        requestBaseInfoType.setTraceId(soaRequestType.getRequestId());
        return requestBaseInfoType;
    }


    /**
     * 政策执行人
     * @param policyInput
     * @return
     */
    protected String getPolicyUid(PolicyInput policyInput, UserInfo userInfo) {
        String policyUid = Optional.ofNullable(policyInput).map(PolicyInput::getPolicyUid).orElse(null);
        return StringUtil.isBlank(policyUid) ? userInfo.getUserId() : policyUid;
    }

    private String getTravelCorpPayType(CorpPayInfo corpPayInfo) {
        if (CorpPayInfoUtil.isPublic(corpPayInfo)) {
            return PUBLIC;
        } else {
            return PRIVATE;
        }
    }

    @Override
    protected ParamCheckResult check(Tuple4<OrderCreateRequestType,
            WrapperOfCheckAvail.CheckAvailContextInfo,
            ResourceToken,
            WrapperOfAccount.AccountInfo> param) {
        return null;
    }


    protected List<GuestInfoType> getGuestIngetfoList(OrderCreateRequestType request) {
        if (CollectionUtils.isEmpty(request.getHotelBookPassengerInputs())) {
            return null;
        }
        return request.getHotelBookPassengerInputs().stream().filter(Objects::nonNull)
                .filter(z -> z.getHotelPassengerInput() != null)
                .map(t -> {
            GuestInfoType guestInfoType = new GuestInfoType();
            guestInfoType.setUid(StringUtil.isNotBlank(t.getHotelPassengerInput().getUid()) ? t.getHotelPassengerInput().getUid() : t.getHotelPassengerInput().getInfoId());
            guestInfoType.setEmployee("T".equals(t.getHotelPassengerInput().getEmployee()));
            return guestInfoType;
        }).collect(Collectors.toList());
    }

}
