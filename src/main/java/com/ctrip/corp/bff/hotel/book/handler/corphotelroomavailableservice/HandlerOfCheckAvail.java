package com.ctrip.corp.bff.hotel.book.handler.corphotelroomavailableservice;

import com.ctrip.corp.agg.hotel.roomavailable.CorpHotelRoomAvailableServiceClient;
import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckAvailRequestType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckAvailResponseType;
import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import org.springframework.stereotype.Component;

/**
 * @Author: chenchuang
 * @Date: 2024/8/22 16:26
 * @Description:校验可订接口
 */
@Component
public class HandlerOfCheckAvail extends AbstractHandlerOfSOA<CheckAvailRequestType, CheckAvailResponseType, CorpHotelRoomAvailableServiceClient> {

    @Override
    protected String getMethodName() {
        return "checkAvail";
    }
}
