package com.ctrip.corp.bff.hotel.book.handler.orderdataaggregationqueryservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.order.data.aggregation.query.contract.OrderDataAggregationQueryServiceClient;
import com.ctrip.corp.order.data.aggregation.query.contract.OrderOperationRequestType;
import com.ctrip.corp.order.data.aggregation.query.contract.OrderOperationResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/11/7 16:51
 */
@Component public class HandlerOfOrderOperation extends
    AbstractHandlerOfSOA<OrderOperationRequestType, OrderOperationResponseType, OrderDataAggregationQueryServiceClient> {

    @Override protected String getMethodName() {
        return "orderOperation";
    }
}
