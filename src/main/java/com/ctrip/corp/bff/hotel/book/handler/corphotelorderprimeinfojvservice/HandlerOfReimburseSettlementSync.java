package com.ctrip.corp.bff.hotel.book.handler.corphotelorderprimeinfojvservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.order.reimbursement.OrderReimbursementServiceClient;
import com.ctrip.order.reimbursement.ReimburseSettlementSyncRequestType;
import com.ctrip.order.reimbursement.ReimburseSettlementSyncResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/9/19 21:49
 */
@Component
public class HandlerOfReimburseSettlementSync extends
    AbstractHandlerOfSOA<ReimburseSettlementSyncRequestType, ReimburseSettlementSyncResponseType, OrderReimbursementServiceClient> {

    @Override
    protected String getMethodName() {
        return "reimburseSettlementSync";
    }
}
