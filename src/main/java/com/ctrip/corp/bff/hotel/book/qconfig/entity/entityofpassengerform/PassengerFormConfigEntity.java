package com.ctrip.corp.bff.hotel.book.qconfig.entity.entityofpassengerform;

import java.util.List;

/**
 * @Author: z.c. wang
 * @Description 酒店出行人表单配置
 * @Date: 2025/3/14 14:37
 * @Version 1.0
 */
public class PassengerFormConfigEntity {

    /**
     * 不同国家地区支持的证件类型配置
     */
    private List<CertificatesBasedOnCountryEntity> certificateListBasedOnCountry;

    /**
     * 证件规则信息集合 - 默认
     */
    private List<RuleInfoEntity> passengerBaseRuleInfos;
    /**
     * 证件规则信息集合
     */
    private List<RuleInfoEntity> certificateDefaultRuleInfos;

    /**
     * 证件规则信息集合 - 默认
     */
    private List<CertificateRuleListEntity> certificateFormRuleInfos;

    /**
     * 非员工姓名规则
     */
    private List<NameRuleItemEntityEntity> nameFormRuleInfos;

    public List<NameRuleItemEntityEntity> getNameFormRuleInfos() {
        return nameFormRuleInfos;
    }

    public void setNameFormRuleInfos(List<NameRuleItemEntityEntity> nameFormRuleInfos) {
        this.nameFormRuleInfos = nameFormRuleInfos;
    }

    public List<RuleInfoEntity> getCertificateDefaultRuleInfos() {
        return certificateDefaultRuleInfos;
    }

    public void setCertificateDefaultRuleInfos(List<RuleInfoEntity> certificateDefaultRuleInfos) {
        this.certificateDefaultRuleInfos = certificateDefaultRuleInfos;
    }

    public List<CertificateRuleListEntity> getCertificateFormRuleInfos() {
        return certificateFormRuleInfos;
    }

    public void setCertificateFormRuleInfos(List<CertificateRuleListEntity> certificateFormRuleInfos) {
        this.certificateFormRuleInfos = certificateFormRuleInfos;
    }

    public List<RuleInfoEntity> getPassengerBaseRuleInfos() {
        return passengerBaseRuleInfos;
    }

    public void setPassengerBaseRuleInfos(List<RuleInfoEntity> passengerBaseRuleInfos) {
        this.passengerBaseRuleInfos = passengerBaseRuleInfos;
    }

    public List<CertificatesBasedOnCountryEntity> getCertificateListBasedOnCountry() {
        return certificateListBasedOnCountry;
    }

    public void setCertificateListBasedOnCountry(List<CertificatesBasedOnCountryEntity> certificateListBasedOnCountry) {
        this.certificateListBasedOnCountry = certificateListBasedOnCountry;
    }
}
