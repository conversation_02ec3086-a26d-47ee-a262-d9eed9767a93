package com.ctrip.corp.bff.hotel.book.handler.corpendorsementinfoservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.pub.endorsement.contract.CorpEndorsementInfoClient;
import com.ctrip.corp.pub.endorsement.contract.synctrip.SyncTripRequestType;
import com.ctrip.corp.pub.endorsement.contract.synctrip.SyncTripResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/12/12 16:39
 */
@Component
public class HandlerOfSyncTrip extends
    AbstractHandlerOfSOA<SyncTripRequestType, SyncTripResponseType, CorpEndorsementInfoClient> {

    @Override
    protected String getMethodName() {
        return "syncTrip";
    }
}
