package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.CreateOrderResult;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.hotel.book.common.enums.RoomTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail.CheckAvailContextInfo;
import com.ctrip.corp.bff.hotel.book.contract.BookUrlInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType;
import com.ctrip.soa._21685.TransactionPayUrlRequestType;
import org.checkerframework.checker.units.qual.C;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/22 23:00
 */
@Component public class MapperOfTransactionPayUrlRequestType extends
    AbstractMapper<Tuple4<OrderCreateRequestType, CreateOrderResponseType, OrderCreateToken,
        CheckAvailContextInfo>, TransactionPayUrlRequestType> {
    private static final String BOOKING_SCENARIO = "BOOK";
    /**
     * 会员酒店
     */
    public static final String HOTELTYPE_M = "M";

    /**
     * 协议酒店
     */
    public static final String HOTELTYPE_C = "C";

    @Override protected TransactionPayUrlRequestType convert(
        Tuple4<OrderCreateRequestType, CreateOrderResponseType, OrderCreateToken, CheckAvailContextInfo> request) {
        OrderCreateRequestType orderCreateRequestType = request.getT1();
        CreateOrderResponseType createOrderResponseType = request.getT2();
        OrderCreateToken orderCreateToken = request.getT3();
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo = request.getT4();
        CreateOrderResult createOrderResult =
            OrderCreateProcessorOfUtil.buildCreateOrderResult(createOrderResponseType, orderCreateRequestType,
                orderCreateToken);
        TransactionPayUrlRequestType transactionPayUrlRequestType = new TransactionPayUrlRequestType();
        transactionPayUrlRequestType.setRequestId(orderCreateRequestType.getIntegrationSoaRequestType().getRequestId());
        transactionPayUrlRequestType.setProductLine(2);
        transactionPayUrlRequestType.setSubProductLine(
            checkAvailContextInfo.getRoomTypeEnum() == RoomTypeEnum.C ? HOTELTYPE_C : HOTELTYPE_M);
        transactionPayUrlRequestType.setOrderId(createOrderResult.getOrderID());
        transactionPayUrlRequestType.setTransactionScenario(BOOKING_SCENARIO);
        transactionPayUrlRequestType.setTransactionUuid(createOrderResult.getTransactionUUID());
        transactionPayUrlRequestType.setSuccessUrl(buildSuccessUrl(orderCreateRequestType, createOrderResult));
        return transactionPayUrlRequestType;
    }

    @Override protected ParamCheckResult check(
        Tuple4<OrderCreateRequestType, CreateOrderResponseType, OrderCreateToken, CheckAvailContextInfo> request) {
        return null;
    }

    public String buildSuccessUrl(OrderCreateRequestType orderCreateRequestType, CreateOrderResult createOrderResult) {
        String orderDetailInput = buildOrderDetail(orderCreateRequestType);
        if (StringUtil.isBlank(orderDetailInput)) {
            return null;
        }
        // APP:https://ct.ctrip.com/m/Detail/Hotel/{0}   其他场景暂不支持丰享 /weboffline/orderdetail/orderDetail?   /hotel-online-after-booking/pages/orderdetail/index?
        return StringUtil.indexedFormat(orderDetailInput, String.valueOf(createOrderResult.getOrderID()));
    }

    protected String buildOrderDetail(OrderCreateRequestType orderCreateRequestType) {
        if (CollectionUtil.isEmpty(orderCreateRequestType.getBookUrlInfos())) {
            return null;
        }
        return orderCreateRequestType.getBookUrlInfos().stream()
            .filter(bookUrlInfo -> "ORDER_DETAIL_URL".equalsIgnoreCase(bookUrlInfo.getUrlType())).findFirst()
            .map(BookUrlInfo::getUrlValue).orElse(null);
    }
}
