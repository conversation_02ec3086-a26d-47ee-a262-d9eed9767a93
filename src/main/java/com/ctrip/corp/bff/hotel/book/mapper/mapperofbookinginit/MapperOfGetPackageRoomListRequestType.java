package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.agg.commonws.entity.GetPackageRoomListRequestType;
import com.ctrip.corp.agg.commonws.entity.PkgPersonInfoType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.PackageRoomInfoType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomItem;
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomMealEntity;
import com.ctrip.corp.agg.hotel.roomavailable.entity.XProductEntityType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.XProductInfoType;
import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.enums.BooleanValueEnum;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.hotel.book.common.util.CorpHotelBookCommonWSUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.bff.specific.contract.ApprovalTextInfoRequestType;
import com.ctrip.corp.hotelbook.commonws.entity.RequestBaseInfoType;
import com.ctrip.corp.hotelbook.commonws.entity.UserInfoType;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/10/8 21:33
 */
@Component
public class MapperOfGetPackageRoomListRequestType
    extends AbstractMapper<Tuple3<BookingInitRequestType,
        ResourceToken,
        WrapperOfCheckAvail.CheckAvailInfo>, GetPackageRoomListRequestType> {
    @Override protected GetPackageRoomListRequestType convert(
        Tuple3<BookingInitRequestType, ResourceToken, WrapperOfCheckAvail.CheckAvailInfo> tuple) {
        BookingInitRequestType bookingInitRequestType = tuple.getT1();
        ResourceToken resourceToken = tuple.getT2();
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = tuple.getT3();
        PackageRoomInfoType packageRoomInfo = Optional.ofNullable(checkAvailInfo)
            .map(WrapperOfCheckAvail.CheckAvailInfo::getRoomItem)
            .map(RoomItem::getPackageRoomInfo)
            .orElse(new PackageRoomInfoType());
        RoomResourceToken roomResourceToken = Optional.ofNullable(resourceToken).map(ResourceToken::getRoomResourceToken).orElse(new RoomResourceToken());
        int adultNumber = Optional.ofNullable(roomResourceToken.getAdult()).orElse(0);
        if (packageRoomInfo.getPackageId() == null && StringUtil.isBlank(packageRoomInfo.getPackageRoomToken())) {
            return null;
        }
        GetPackageRoomListRequestType requestType = new GetPackageRoomListRequestType();
        requestType.setRequestBaseInfo(buildRequestBaseInfoType(bookingInitRequestType));
        requestType.setPackageId(packageRoomInfo.getPackageId() != null ? Collections.singletonList(packageRoomInfo.getPackageId()) : null);
        requestType.setPackageTokenList(StringUtil.isNotBlank(packageRoomInfo.getPackageRoomToken())
                ? Collections.singletonList(packageRoomInfo.getPackageRoomToken()) : null);
        if (adultNumber > 0) {
            PkgPersonInfoType pkgPersonInfoType = new PkgPersonInfoType();
            pkgPersonInfoType.setPkgId(packageRoomInfo.getPackageId());
            pkgPersonInfoType.setAdultQty(adultNumber);
            requestType.setPkgPersonInfos(Collections.singletonList(pkgPersonInfoType));
        }
        return requestType;
    }
    public static RequestBaseInfoType buildRequestBaseInfoType(BookingInitRequestType bookingInitRequestType) {
        IntegrationSoaRequestType integrationSoaRequestType = bookingInitRequestType.getIntegrationSoaRequestType();
        RequestBaseInfoType baseInfo = new RequestBaseInfoType();
        baseInfo.setTraceId(bookingInitRequestType.getIntegrationSoaRequestType().getRequestId());
        UserInfoType userInfo = new UserInfoType();
        userInfo.setUid(RequestHeaderUtil.getUserId(integrationSoaRequestType));
        userInfo.setCorpId(RequestHeaderUtil.getCorpId(integrationSoaRequestType));
        baseInfo.setUserInfo(userInfo);
        baseInfo.setBookingChannel(CorpHotelBookCommonWSUtil.getChannel(integrationSoaRequestType.getSourceFrom()));
        baseInfo.setLocale(integrationSoaRequestType.getLanguage());
        return baseInfo;
    }

    @Override
    protected ParamCheckResult check(Tuple3<BookingInitRequestType, ResourceToken, WrapperOfCheckAvail.CheckAvailInfo> tuple) {
        return null;
    }
}
