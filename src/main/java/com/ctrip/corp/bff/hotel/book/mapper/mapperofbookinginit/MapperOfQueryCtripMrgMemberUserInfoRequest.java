package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.agg.hotel.roomavailable.entity.BookingRulesType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.GroupMemberShipType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelBrandItem;
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelItem;
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelRatePlan;
import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.hotel.order.rewardservice.contract.QueryCtripMrgMemberUserInfoRequest;
import corp.user.service.corpUserInfoService.GetPlatformRelationByUidResponseType;
import corp.user.service.group4j.accounts.BizModeBindRelationData;
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * @Author: chenchuang
 * @Date: 2024/9/29 20:21
 */
@Component
public class MapperOfQueryCtripMrgMemberUserInfoRequest extends
        AbstractMapper<Tuple4<BookingInitRequestType, WrapperOfCheckAvail.CheckAvailInfo,
                        GetPlatformRelationByUidResponseType, QueryBizModeBindRelationResponseType>, QueryCtripMrgMemberUserInfoRequest> {
    @Override
    protected QueryCtripMrgMemberUserInfoRequest convert(Tuple4<BookingInitRequestType, WrapperOfCheckAvail.CheckAvailInfo,
            GetPlatformRelationByUidResponseType, QueryBizModeBindRelationResponseType> param) {
        QueryCtripMrgMemberUserInfoRequest request = new QueryCtripMrgMemberUserInfoRequest();
        QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType = param.getT4();
        Integer groupId = Optional.ofNullable(param.getT2()).map(WrapperOfCheckAvail.CheckAvailInfo::getHotelItem)
                .map(HotelItem::getHotelBrandInfo).map(HotelBrandItem::getGroupId).orElse(null);
        String registerRule = Optional.ofNullable(param.getT2())
            .map(WrapperOfCheckAvail.CheckAvailInfo::getBookingRules)
            .map(BookingRulesType::getGroupMemberShipInfo)
            .map(GroupMemberShipType::getGroupRegisterRule)
            .orElse(null);
        request.setUid(getRegisterUid(registerRule, getPrimaryId(queryBizModeBindRelationResponseType,
                RequestHeaderUtil.getUserId(param.getT1().getIntegrationSoaRequestType())), param.getT3()));
        request.setMgrgroupid(Optional.ofNullable(groupId).map(String::valueOf).orElse(null));
        return request;
    }

    private String getPrimaryId(QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType, String userId) {
        if (queryBizModeBindRelationResponseType == null) {
            return null;
        }
        BizModeBindRelationData bizModeBindRelationData = CollectionUtil.findFirst(queryBizModeBindRelationResponseType.getBizModeBindRelationDataList(),
                t -> t != null && StringUtil.equalsIgnoreCase(userId, t.getDimensionId()));
        if (bizModeBindRelationData == null || StringUtil.isBlank(bizModeBindRelationData.getPrimaryDimensionId())) {
            return null;
        }
        return bizModeBindRelationData.getPrimaryDimensionId();
    }


    /**
     * 商旅uid注册
     */
    public static final String BUSINESS_TRAVEL_REGISTER = "BUSINESS_TRAVEL_REGISTER";

    /**
     *  携程uid注册
     */
    public static final String TRIP_REGISTER = "TRIP_REGISTER";

    /**
     * 不需要注册
     */
    public static final String NO_REGISTER = "NO_REGISTER";
    /**
     * 获取散客uid或者商旅uid
     * @param registerRule
     * @return
     */
    public String getRegisterUid(String registerRule, String uid, GetPlatformRelationByUidResponseType getPlatformRelationByUidResponseType) {
        if (StringUtil.isBlank(registerRule)) {
            return uid;
        }
        switch (registerRule) {
            case TRIP_REGISTER:
                return Optional.ofNullable(getPlatformRelationByUidResponseType).map(GetPlatformRelationByUidResponseType::getAccountId).orElse(null);
            case NO_REGISTER:
                return null;
            case BUSINESS_TRAVEL_REGISTER:
            default:
                return uid;
        }
    }

    @Override
    protected ParamCheckResult check(Tuple4<BookingInitRequestType, WrapperOfCheckAvail.CheckAvailInfo,
            GetPlatformRelationByUidResponseType, QueryBizModeBindRelationResponseType> param) {
        return null;
    }
}
