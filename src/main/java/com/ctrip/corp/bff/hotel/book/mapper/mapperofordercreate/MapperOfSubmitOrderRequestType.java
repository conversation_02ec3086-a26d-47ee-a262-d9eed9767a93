package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderRequestType;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType;
import com.ctrip.corp.hotelbooking.hotelws.entity.OfflineSubmitOrderExt;
import com.ctrip.corp.hotelbooking.hotelws.entity.SubmitOrderRequestType;
import com.fasterxml.jackson.annotation.JsonProperty;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description 提交订单请求参数
 * @Date 2024/8/9 10:15
 * @Version 1.0
 */
@Component
public class MapperOfSubmitOrderRequestType
        extends AbstractMapper<Tuple2<CreateOrderResponseType, OrderCreateToken>, SubmitOrderRequestType> {

    @Override
    protected SubmitOrderRequestType convert(Tuple2<CreateOrderResponseType, OrderCreateToken> createOrderResponseTypeTuple1) {
        SubmitOrderRequestType result = new SubmitOrderRequestType();
        CreateOrderResponseType createOrderResponseType = createOrderResponseTypeTuple1.getT1();
        OrderCreateToken orderCreateToken = createOrderResponseTypeTuple1.getT2();
        result.setOrderId(OrderCreateProcessorOfUtil.buildOrderId(orderCreateToken, createOrderResponseType));
        return result;
    }

    @Override
    protected ParamCheckResult check(Tuple2<CreateOrderResponseType, OrderCreateToken> createOrderResponseTypeTuple1) {
        return null;
    }
}
