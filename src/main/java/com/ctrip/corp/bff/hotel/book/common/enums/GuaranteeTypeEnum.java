package com.ctrip.corp.bff.hotel.book.common.enums;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;

/**
 * <AUTHOR>
 * @description copy的BffGuaranteeTypeEnum
 * @date 2024/8/28
 */
public enum GuaranteeTypeEnum {
    /**
     * 无担保
     */
    NONE("NONE"),

    /**
     * 全程担保
     */
    FULL_ALWAYS("FULL"),

    /**
     * 首日一律
     */
    FIRST_DAY_ALWAYS("FIRST_DAY"),

    /**
     * 首日超时
     */
    FIRST_DAY_ARRIVAL_TIME("FIRST_DAY"),

    /**
     * 峰时担保
     */
    RUSH_HOUR("OTHER"),

    /**
     * 首日一律+峰时
     */
    FIRST_DAY_ALWAYS_RUSH_HOUR("FIRST_DAY"),

    /**
     * 首日超时+峰时
     */
    FIRST_DAY_ARRIVAL_TIME_RUSH_HOUR( "FIRST_DAY"),

    /**
     * 预付扣首日
     */
    FIRST_DAY_PREPAY( "FIRST_DAY"),

    /**
     * 预付扣全额
     */
    FULL_PREPAY("FULL"),

    /**
     * 供应商自定义
     */
    VENDOR_SPECIAL_REQUIRED("OTHER");

    /**
     * 传给接口值
     */
    String deductionType;

    GuaranteeTypeEnum(String deductionType) {
        this.deductionType = deductionType;
    }

    /**
     * 传给接口值
     *
     * @return
     */
    public String getDeductionType() {
        return deductionType;
    }

    public static GuaranteeTypeEnum findByName(String name){
        for (GuaranteeTypeEnum guaranteeType : GuaranteeTypeEnum.values()){
            if (guaranteeType.name().equalsIgnoreCase(name)){
                return guaranteeType;
            }
        }
        return null;
    }

    public static final String OTHER_GUARANTEE_TYPE = "OTHER";
    public static final String NONE_GUARANTEE_TYPE = "NONE";
    public static String getGuaranteeType(String value, boolean isFree, boolean isFg){
        if (StringUtil.isBlank(value)) {
            return NONE_GUARANTEE_TYPE;
        }
        GuaranteeTypeEnum bffGuaranteeTypeEnum = findByName(value);
        if (bffGuaranteeTypeEnum == null){
            return OTHER_GUARANTEE_TYPE;
        }

        return bffGuaranteeTypeEnum.getDeductionType();
    }
}
