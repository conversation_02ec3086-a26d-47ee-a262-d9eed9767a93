package com.ctrip.corp.bff.hotel.book.handler.corpbffspecificservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.bff.specific.contract.CorpBffSpecificServiceClient;
import com.ctrip.corp.bff.specific.contract.CostCenterCheckRequestType;
import com.ctrip.corp.bff.specific.contract.CostCenterCheckResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/04/29 22:31
 */
@Component public class HandlerOfCostCenterCheck extends
    AbstractHandlerOfSOA<CostCenterCheckRequestType, CostCenterCheckResponseType, CorpBffSpecificServiceClient> {

    @Override protected String getMethodName() {
        return "costCenterCheck";
    }
}
