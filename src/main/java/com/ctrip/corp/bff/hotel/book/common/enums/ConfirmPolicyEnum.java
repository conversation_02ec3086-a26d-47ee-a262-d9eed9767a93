package com.ctrip.corp.bff.hotel.book.common.enums;

/**
 * <AUTHOR>
 * @Date 2024/8/30 19:19
 */
public enum ConfirmPolicyEnum {
    /**
     * 立即确认
     */
    JUSTIFY_CONFIRM("JUSTIFY_CONFIRM"),
    /**
     * 今天10点前确认
     */
    CONFIRM_BY_TODAY_10("CONFIRM_BY_TODAY_10"),
    /**
     * 明天10点前确认
     */
    CONFIRM_BY_TOMORROW_10("CONFIRM_BY_TOMORROW_10"),
    /**
     * 等待确认
     */
    WAIT_CONFIRM("WAIT_CONFIRM");

    private final String value;

    ConfirmPolicyEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
