package com.ctrip.corp.bff.hotel.book.handler.geolocationservice;

import com.ctrip.basebiz.geolocation.service.GeoLocationServiceClient;
import com.ctrip.basebiz.geolocation.service.GetAllNationalityRequestType;
import com.ctrip.basebiz.geolocation.service.GetAllNationalityResponseType;
import com.ctrip.corp.bff.framework.template.entity.IntegrationResponse;
import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.bff.tools.contract.CountryQueryResponseType;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/4/10
 */
@Component
public class HandlerOfGetAllNationality extends AbstractHandlerOfSOA<GetAllNationalityRequestType, GetAllNationalityResponseType, GeoLocationServiceClient> {
    @Override
    protected String getMethodName() {
        return "getAllNationality";
    }

    @Override
    protected String getLogErrorCode(GetAllNationalityResponseType response) {
        return Optional.ofNullable(response).map(GetAllNationalityResponseType::getResultCode).filter(Objects::nonNull).map(String::valueOf).orElse(null);
    }
}
