package com.ctrip.corp.bff.hotel.book.common.constant;

/**
 * <AUTHOR>
 * @date 2024/9/26 16:26
 */
public class InitConfigInfoConstant {
    /**
     * 是否允许分摊
     */
    public static final String CAN_SHARE_FEE = "CAN_SHARE_FEE";
    /**
     * 最大预订间数
     */
    public static final String MAX_ROOM_QUANTITY = "MAX_ROOM_QUANTITY";
    /**
     * 最小预定间数
     */
    public static final String MIN_ROOM_QUANTITY = "MIN_ROOM_QUANTITY";
    /**
     * 最大入住人数
     */
    public static final String ROOM_CAPACITY = "ROOM_CAPACITY";
    /**
     * 房间数是否可改
     */
    public static final String CAN_CHANGE_ROOM_QUANTITY = "CAN_CHANGE_ROOM_QUANTITY";

    /**
     * 同住
     */
    public static final String SHARE_ROOM = "SHARE_ROOM";

    /**
     * 日期是否可改
     */
    public static final String CAN_CHANGE_DATE = "CAN_CHANGE_DATE";
    /**
     * 是否可以紧急预定
     */
    public static final String CAN_EMERGENCY = "CAN_EMERGENCY";

    /**
     * 是否需要勾选隐私条款
     */
    public static final String NEED_CHECKED_PRIVACY_POLICY = "NEED_CHECKED_PRIVACY_POLICY";
    /**
     * 入住人是否需要包含本人
     */
    public static final String REQUIRE_INCLUDE_SELF = "REQUIRE_INCLUDE_SELF";

    /**
     * 是否调用淡旺季差标
     */
    public static final String HIT_ALTERNATELY_STANDARD = "HIT_ALTERNATELY_STANDARD";
    /**
     * 是否允许特别授权
     */
    public static final String CAN_SPECIAL_AUTH = "CAN_SPECIAL_AUTH";
    /**
     * 是否支持月租房
     */
    public static final String CAN_LONG_RENT = "CAN_LONG_RENT";
    /**
     * 是否允许自动确认
     */
    public static final String CAN_AUTO_CONFIRM = "CAN_AUTO_CONFIRM";
    /**
     * 是否需要审批号或紧急预定
     */
    public static final String NEED_SUBAPPROVALNO_OR_EMERGENCY = "NEED_SUBAPPROVALNO_OR_EMERGENCY";

    /**
     * 是否支持微信小程序支付
     */
    public static final String SUPPORT_MINI_APP_PAY = "SUPPORT_MINI_APP_PAY";
    /**
     * 是否支持行程重新预订延用审批
     */
    public static final String SUPPORT_REBOOK_TRIP_APPROVAL_FLOW = "SUPPORT_REBOOK_TRIP_APPROVAL_FLOW";
    /**
     * 是否支持出差申请新版
     */
    public static final String SUPPORT_APPLICATION = "SUPPORT_APPLICATION";

    /**
     * 仅配置出差申请开关
     */
    public static final String ONLY_SUPPORT_APPLICATION = "ONLY_SUPPORT_APPLICATION";
    /**
     * 是否支持行程打包
     */
    public static final String TRIP = "TRIP";
    /**
     * 是否支持联系人记忆功能
     */
    public final static String SUPPORT_CONTACT_MEMORY_DISPLAY = "SUPPORT_CONTACT_MEMORY_DISPLAY";
    /**
     * 是否区分代订类别---接入机械手平台
     */
    public final static String IS_DISTINGUISH_RESERVATION = "IS_DISTINGUISH_RESERVATION";

    /**
     * 是否支付时间限制
     */
    public static final String PAYMENT_TIME_LIMIT_MINUTES = "PAYMENT_TIME_LIMIT_MINUTES";

    /**
     * 是否展示支付方式下文案
     */
    public static final String AMADEUS_FG_TIP = "amadeusFgTip";

    /**
     * 新出差申请:APPLICATION
     * 老出差申请:OLD_APPLICATION
     * 提前审批模式:APPROVAL
     * 其他模式:NONE
     */
    public static final String TRIP_APPLICATION = "TRIP_APPLICATION";

    /**
     * 是否无感行程白名单
     */
    public static final String NON_INDUCTIVE_TRIP_WHITE_LIST = "NON_INDUCTIVE_TRIP_WHITE_LIST";

    /**
     * 行程关联的出差申请单号
     */
    public static final String ENDORSEMENT_ID_WITH_TRIP = "ENDORSEMENT_ID_WITH_TRIP";

    /**
     * pwcCase
     */
    public static final String PWC_CASE = "PWC_CASE";

    /**
     * 可订返回的
     */
    public static final String MAX_BOOKING_ROOM_NUM = "MAX_BOOKING_ROOM_NUM";

    /**
     * 双付丰享支付配置
     */
    public static final String CUSTOMER_PAY_TRIGGER_TO_ACCNT = "CUSTOMER_PAY_TRIGGER_TO_ACCNT";

    /**
     * ams配置支持公帐支付
     */
    public static final String SUPPORT_CORP_PAYMENT = "SUPPORT_CORP_PAYMENT";

    /**
     * ams配置支持个人支付
     */
    public static final String SUPPORT_PERSONAL_PAYMENT = "SUPPORT_PERSONAL_PAYMENT";

    /**
     * 是否仅员工可订
     */
    public static final String ONLY_EMPLOYEE_BOOKING = "ONLY_EMPLOYEE_BOOKING";

    /**
     * 是否外露发邮件按钮
     */
    public static final String SEND_EMAIL_AVAILABLE = "SEND_EMAIL_AVAILABLE";

    /**
     * 发票说明默认选中
     */
    public static final String INVOICE_TIP_CHECKED = "INVOICE_TIP_CHECKED";

    /**
     * 入住人短信默认勾选配置
     */
    public static final String PASSENGER_SMS_CONFIRM_DEFAULT_CHECKED = "PASSENGER_SMS_CONFIRM_DEFAULT_CHECKED";

    /**
     * 中国大陆：CN HONG_KONG：港  MACAO：澳  TAIWAN：台 海外：OVERSEA
     */
    public static final String CITY_REGION = "CITY_REGION";
    public static final String NEW_INVOICE = "NEW_INVOICE";
    /**
     * 是否允许加价
     */
    public static final String SUPPORT_ADD_PRICE = "SUPPORT_ADD_PRICE";


    /**
     * 是否展示审批模块（蓝色空间）
     */
    public static final String SHOW_APPROVAL_MODULE = "SHOW_APPROVAL_MODULE";

    /**
     * （蓝色空间） 单据管控模式：BUSINESS_APPLY 出差申请, APPROVAL 提前审批
     */
    public static final String BILL_CONTROL_MODE = "BILL_CONTROL_MODE";

    /**
     * （蓝色空间） 是否无感行程模式
     */
    public static final String NO_FEEL_TRIP = "NO_FEEL_TRIP";
    /**
     * 是否允许修改出行人
     */
    public static final String MODIFY_PERSON_NUMBER = "MODIFY_PERSON_NUMBER";
    /**
     * 新版成本中心配置
     */
    public static final String COST_CENTER_NEW = "COST_CENTER_NEW";
    /**
     * 是否出行人模式
     */
    public static final String TRAVEL_STAND_POLICY = "TRAVEL_STAND_POLICY";
}
