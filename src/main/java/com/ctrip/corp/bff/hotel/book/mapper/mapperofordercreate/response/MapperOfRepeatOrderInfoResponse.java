package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response;

import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.date.DateUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.RCContent;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.RCInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.*;
import com.ctrip.corp.bff.hotel.book.common.constant.CustomConfigKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.SharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.util.HotelPayTypeUtil;
import com.ctrip.corp.bff.hotel.book.contract.*;
import com.ctrip.ibu.platform.shark.sdk.api.L10n;
import com.ctrip.soa._21210.HotelClientInfoType;
import com.ctrip.soa._21210.HotelOrderRepeatOrderResponseType;
import com.ctrip.soa._21210.RepeatOrderInfoType;
import corp.user.service.corp4jservice.GetReasoncodesResponseType;
import corp.user.service.corp4jservice.ReasoncodeInfo;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 重复订单信息响应
 * @Date 2024/7/15 14:21
 * @Version 1.0
 * 为了只与前端交互一次 此流程不结束，在下游rc流程中一并返回
 */
@Component
public class MapperOfRepeatOrderInfoResponse
        extends AbstractMapper<Tuple7<HotelOrderRepeatOrderResponseType, GetReasoncodesResponseType,
        WrapperOfAccount.AccountInfo, CorpPayInfo, IntegrationSoaRequestType,
        List<HotelPayTypeInput>, ResourceToken>, Tuple2<Boolean, OrderCreateResponseType>> {

    private static final int SUCCESS_CODE = 20000;

    /**
     * 禁止预订
     */
    private static final String FORBID_BOOKING = "FORBID_BOOKING";

    /**
     * 可继续提交
     */
    private static final String ALLOW_BOOKING = "ALLOW_BOOKING";

    /**
     * 可继续提交提示
     */
    private static final String ALLOW_BOOKING_TIP = "ALLOW_BOOKING_TIP";


    /**
     * 可继续提交强调提示
     */
    private static final String ALLOW_BOOKING_EMPHASIZE_TIP = "ALLOW_BOOKING_EMPHASIZE_TIP";

    /**
     * 因私
     */
    private static final String PRIVATE = "OWN";

    
    /**
     * 重复预订原因开启
     */
    private static final String T = "T";

    /**
     * 未修改tag
     */
    private static final int REPEAT_TAG_NOT_MODIFY = 0;

    /**
     * 城市名称
     */
    private static final String CITYNAME = "CITYNAME";

    /**
     * 入住人名
     */
    private static final String GUESTNAMES = "GUESTNAMES";

    /**
     * 日期
     */
    private static final String CHECKDATE = "CHECKDATE";

    /**
     * 会员酒店： 公司规定重复预订仅允许个人支付，请更换支付方式，除特殊审批外，重复订单不允许报销。
     * 协议酒店：公司规定重复预订仅允许个人支付，请返回选择到店付房型，除特殊审批外，重复订单不允许报销。
     */
    private static final String FORBID_BOOKING_CONTROL_BY_PAY_TYPE_SHARK_KEY = "ctrip.com.hotel.booking.biz.repeat.booking.forbid.text.bypaytype.";

    /**
     * 除特殊审批外，重复订单不允许报销。
     */
    private static final String CONTINUE_BOOKING_CONTROL_BY_PAY_TYPE_SHARK_KEY = "ctrip.com.hotel.booking.biz.repeat.booking.continue.text.bypaytype";

    /**
     * 出行人已预订重复行程，请前往【我的订单】中取消或修改原订单后，再继续预订。注意：无需等待酒店回复结果，原订单修改提交后即可继续预订。
     */
    private static final String FORBID_BOOKING_SHARK_KEY_N = "ctrip.com.hotel.booking.biz.repeat.booking.forbid.text.N";

    /**
     * 出行人已预订重复行程，请前往【我的订单】中取消或修改原订单后，再继续预订。
     */
    private static final String FORBID_BOOKING_SHARK_KEY = "ctrip.com.hotel.booking.biz.repeat.booking.forbid.text";

    /**
     * 出行人已预订重复行程，请前往【我的订单】中取消或修改原订单后，再继续预订。
     */
    private static final String CONTINUE_BOOKING_SHARK_KEY = "ctrip.com.hotel.booking.biz.repeat.booking.continue.emphasize.text";

    /**
     * 注意：无需等待酒店回复结果，原订单修改提交后即可继续预订。
     */
    private static final String CONTINUE_BOOKING_TIP_SHARK_KEY = "ctrip.com.hotel.booking.biz.repeat.booking.continue.tip.text";

    @Override
    protected Tuple2<Boolean, OrderCreateResponseType> convert(Tuple7<HotelOrderRepeatOrderResponseType,
            GetReasoncodesResponseType, WrapperOfAccount.AccountInfo, CorpPayInfo,
            IntegrationSoaRequestType, List<HotelPayTypeInput>, ResourceToken> para) {
        if (para == null || para.getT1() == null || SUCCESS_CODE != para.getT1().getResponseCode()
                || CollectionUtils.isEmpty(para.getT1().getRepeatOrderList())) {
            return Tuple2.of(false, null);
        }
        OrderCreateResponseType res = new OrderCreateResponseType();
        res.setRepeatOrderInfo(buildRepeatOrderInfo(para.getT3(), para.getT1().getRepeatOrderList(),
                para.getT4(), para.getT5(), para.getT6(), para.getT7()));
        return Tuple2.of(false, res);
    }

    protected RepeatOrderInfo buildRepeatOrderInfo(WrapperOfAccount.AccountInfo accountInfo,
                                                   List<RepeatOrderInfoType> repeatOrderList,
                                                   CorpPayInfo corpPayInfo,
                                                   IntegrationSoaRequestType integrationSoaRequestType,
                                                   List<HotelPayTypeInput> hotelPayTypeInputs,
                                                   ResourceToken resourceToken) {
        String repeatOrderRule = buildRepeatOrderRule(accountInfo, repeatOrderList, corpPayInfo,
                hotelPayTypeInputs, integrationSoaRequestType);
        RepeatOrderInfo repeatOrderInfo = new RepeatOrderInfo();
        repeatOrderInfo.setRepeatOrderRule(repeatOrderRule);
        repeatOrderInfo.setRepeatOrderDetails(buildRepeatOrderDetails(repeatOrderList, integrationSoaRequestType, accountInfo, corpPayInfo));
        repeatOrderInfo.setRemindInfos(buildRemindInfos(integrationSoaRequestType, resourceToken, accountInfo, repeatOrderRule));
        return repeatOrderInfo;
    }

    protected List<RemindInfo> buildRemindInfos(IntegrationSoaRequestType integrationSoaRequestType,
                                                ResourceToken resourceToken,
                                                WrapperOfAccount.AccountInfo accountInfo,
                                                String repeatOrderRule) {
        if (SourceFrom.Offline != Optional.ofNullable(integrationSoaRequestType).map(IntegrationSoaRequestType::getSourceFrom).orElse(null)
                && QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.REPEAT_ORDER_CONTROL_BY_PAY_TYPE,
                Optional.ofNullable(integrationSoaRequestType).map(IntegrationSoaRequestType::getUserInfo)
                        .map(UserInfo::getCorpId).orElse(null))) {
            if (FORBID_BOOKING.equalsIgnoreCase(repeatOrderRule)) {
                return Arrays.asList(new RemindInfo(FORBID_BOOKING, BFFSharkUtil.getSharkValue(String.format("%s%s",
                        FORBID_BOOKING_CONTROL_BY_PAY_TYPE_SHARK_KEY,
                        Optional.ofNullable(resourceToken).map(ResourceToken::getHotelResourceToken)
                                .map(HotelResourceToken::getHotelType).map(Enum::name).orElse("")))));
            }
            return Arrays.asList(new RemindInfo(FORBID_BOOKING, BFFSharkUtil.getSharkValue(CONTINUE_BOOKING_CONTROL_BY_PAY_TYPE_SHARK_KEY)));

        }
        if (accountInfo != null && accountInfo.repeatOrderBookingByModify()) {
            if (FORBID_BOOKING.equalsIgnoreCase(repeatOrderRule)) {
                return Arrays.asList(new RemindInfo(FORBID_BOOKING, BFFSharkUtil.getSharkValue(FORBID_BOOKING_SHARK_KEY_N)));
            }
            return Arrays.asList(new RemindInfo(ALLOW_BOOKING_EMPHASIZE_TIP, BFFSharkUtil.getSharkValue(CONTINUE_BOOKING_SHARK_KEY)),
                    new RemindInfo(ALLOW_BOOKING_TIP, BFFSharkUtil.getSharkValue(CONTINUE_BOOKING_TIP_SHARK_KEY)));

        }
        if (FORBID_BOOKING.equalsIgnoreCase(repeatOrderRule)) {
            return Arrays.asList(new RemindInfo(FORBID_BOOKING, BFFSharkUtil.getSharkValue(FORBID_BOOKING_SHARK_KEY)));
        }
        return Arrays.asList(new RemindInfo(ALLOW_BOOKING_EMPHASIZE_TIP, BFFSharkUtil.getSharkValue(CONTINUE_BOOKING_SHARK_KEY)));
    }
    /**
     * 修改中tag
     */
    public static final int REPEAT_TAG_MODIFYING = 1;

    /**
     * 因私
     */
    private static final String OWN = "OWN";

    /**
     * 修改中
     */
    private static final String MODIFYING = "Modifying";

    protected List<RepeatOrderDetail> buildRepeatOrderDetails(List<RepeatOrderInfoType> repeatOrderList,
                                                              IntegrationSoaRequestType integrationSoaRequestType,
                                                              WrapperOfAccount.AccountInfo accountInfo,
                                                              CorpPayInfo corpPayInfo) {
        if (CollectionUtils.isEmpty(repeatOrderList)) {
            return null;
        }
        boolean isAllOwnOrder = repeatOrderList.stream().filter(Objects::nonNull).allMatch(t -> StringUtil.equalsIgnoreCase(t.getCorpPayType(), OWN));
        return repeatOrderList.stream().filter(Objects::nonNull).map(t -> {
            String orderStatus = buildOrderStatus(integrationSoaRequestType, accountInfo, t.getOrderStatus(), t.getRepeatTag(), corpPayInfo, isAllOwnOrder);
            RepeatOrderDetail repeatOrderDetail = new RepeatOrderDetail();
            repeatOrderDetail.setOrderId(String.valueOf(t.getOrderId()));
            repeatOrderDetail.setOrderStatus(orderStatus);
            repeatOrderDetail.setOrderStatusDescription(buildOrderStatusDescription(orderStatus));
            repeatOrderDetail.setOrderElements(buildOrderElements(t, integrationSoaRequestType));
            return repeatOrderDetail;
        }).collect(Collectors.toList());
    }

    private String buildOrderStatus(IntegrationSoaRequestType integrationSoaRequestType,
                                    WrapperOfAccount.AccountInfo accountInfo,
                                    String orderStatus,
                                    Integer repeatTag,
                                    CorpPayInfo corpPayInfo,
                                    Boolean isAllOwnOrder) {
        if (CorpPayInfoUtil.isPrivate(corpPayInfo)) {
            return orderStatus;
        }
        if (BooleanUtils.isTrue(isAllOwnOrder)) {
            return orderStatus;
        }
        if (!accountInfo.repeatOrderBookingByModify()) {
            return orderStatus;
        }
        if (repeatTag == null || !repeatTag.equals(REPEAT_TAG_MODIFYING)) {
            return orderStatus;
        }
        if (QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.REPEAT_ORDER_CONTROL_BY_PAY_TYPE,
                Optional.ofNullable(integrationSoaRequestType).map(IntegrationSoaRequestType::getUserInfo)
                        .map(UserInfo::getCorpId).orElse(null))) {
            return orderStatus;
        }
        return MODIFYING;
    }

    public String buildOrderStatusDescription(String orderStatus) {
        return BFFSharkUtil.getSharkValue(
                StringUtil.indexedFormat(SharkKeyConstant.REPEAT_ORDER_ORDERSTATUS_DESCRIPTION, orderStatus));
    }

    protected List<OrderElement> buildOrderElements(RepeatOrderInfoType repeatOrderInfoType,
                                                    IntegrationSoaRequestType integrationSoaRequestType) {
        if (repeatOrderInfoType == null) {
            return null;
        }
        List<OrderElement> res = new ArrayList<>();
        if (StringUtils.isNotBlank(repeatOrderInfoType.getCityName())) {
            res.add(new OrderElement(CITYNAME, repeatOrderInfoType.getCityName()));
        }
        if (CollectionUtils.isNotEmpty(repeatOrderInfoType.getClientList())) {
            res.add(new OrderElement(GUESTNAMES, repeatOrderInfoType.getClientList().stream()
                    .filter(Objects::nonNull).map(HotelClientInfoType::getName).collect(Collectors.joining(","))));
        }
        if (StringUtils.isNotBlank(repeatOrderInfoType.getCheckInTime()) &&
                StringUtils.isNotBlank(repeatOrderInfoType.getCheckOutTime())) {
            res.add(new OrderElement(CHECKDATE, String.join("-", L10n.dateTimeFormatter(
                    Optional.ofNullable(integrationSoaRequestType).map(IntegrationSoaRequestType::getLanguage).orElse(null))
                            .ymdShortString(DateUtil.parseDate(repeatOrderInfoType.getCheckInTime(), DateUtil.YYYY_MM_DD)),
                    L10n.dateTimeFormatter(Optional.ofNullable(integrationSoaRequestType)
                            .map(IntegrationSoaRequestType::getLanguage).orElse(null))
                            .ymdShortString(DateUtil.parseDate(repeatOrderInfoType.getCheckOutTime(), DateUtil.YYYY_MM_DD)))));
        }
        return res;

    }

    protected String buildRepeatOrderRule(WrapperOfAccount.AccountInfo accountInfo,
                                          List<RepeatOrderInfoType> repeatOrderList,
                                          CorpPayInfo corpPayInfo,
                                          List<HotelPayTypeInput> hotelPayTypeInputs,
                                          IntegrationSoaRequestType integrationSoaRequestType) {
        // 因私不管控
        if (CorpPayInfoUtil.isPrivate(corpPayInfo)) {
            return ALLOW_BOOKING;
        }
        if (CollectionUtils.isEmpty(repeatOrderList)) {
            return ALLOW_BOOKING;
        }
        // 重复订单都是因私不管控
        if (repeatOrderList.stream().filter(Objects::nonNull).allMatch(t -> StringUtils.equalsIgnoreCase(t.getCorpPayType(), PRIVATE))) {
            return ALLOW_BOOKING;
        }
        if (accountInfo == null) {
            return ALLOW_BOOKING;
        }
        // 重复预订管控根据房费支付方式
        if (SourceFrom.Offline != Optional.ofNullable(integrationSoaRequestType).map(IntegrationSoaRequestType::getSourceFrom).orElse(null)
                && QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.REPEAT_ORDER_CONTROL_BY_PAY_TYPE,
                Optional.ofNullable(integrationSoaRequestType)
                        .map(IntegrationSoaRequestType::getUserInfo)
                        .map(UserInfo::getCorpId).orElse(null))) {
            return Arrays.asList(HotelPayTypeEnum.CORP_PAY, HotelPayTypeEnum.ADVANCE_PAY, HotelPayTypeEnum.FLASH_STAY_PAY, HotelPayTypeEnum.MIX_PAY)
                    .contains(HotelPayTypeUtil.getRoomPayType(hotelPayTypeInputs))
                    ? FORBID_BOOKING : ALLOW_BOOKING;
        }
        if (accountInfo.repeatOrderBookingForbid()) {
            return FORBID_BOOKING;
        }
        // 任一订单未修改则禁止预订(repeattag未下发视为未修改)
        if (accountInfo.repeatOrderBookingByModify()
                && repeatOrderList.stream()
                .filter(Objects::nonNull).anyMatch(t -> t.getRepeatTag() == null || t.getRepeatTag() == REPEAT_TAG_NOT_MODIFY)) {
            return FORBID_BOOKING;
        }
        return ALLOW_BOOKING;

    }


    @Override
    protected ParamCheckResult check(Tuple7<HotelOrderRepeatOrderResponseType, GetReasoncodesResponseType,
            WrapperOfAccount.AccountInfo, CorpPayInfo, IntegrationSoaRequestType, List<HotelPayTypeInput>, ResourceToken> para) {
        if (para == null || para.getT1() == null || SUCCESS_CODE != para.getT1().getResponseCode()
                || CollectionUtils.isEmpty(para.getT1().getRepeatOrderList())) {
            return null;
        }
        if (FORBID_BOOKING.equalsIgnoreCase(buildRepeatOrderRule(para.getT3(),
                para.getT1().getRepeatOrderList(), para.getT4(), para.getT6(), para.getT5()))) {
            return null;
        }
        if (para.getT3() == null) {
            return null;
        }
        if (!StringUtils.equalsIgnoreCase(T, para.getT3().getRepeatBookingReason())) {
            return null;
        }
        if (para.getT2() == null) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.CONFLICT_RC_NOT_OBTAIN,
                    OrderCreateErrorEnum.CONFLICT_RC_NOT_OBTAIN.name());
        }
        if (CollectionUtils.isEmpty(para.getT2().getReasonCodes())
                || CollectionUtils.isEmpty(para.getT2().getReasonCodes().stream().filter(Objects::nonNull)
                .filter(x -> BooleanUtils.isTrue(x.isState()))
                .collect(Collectors.toList()))) {
            return new ParamCheckResult(false,
                    OrderCreateErrorEnum.CONFLICT_RC_NOT_CONFIGURE, OrderCreateErrorEnum.CONFLICT_RC_NOT_CONFIGURE.name());
        }
        return null;
    }
}
