package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckAvailResponseType;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.CorpHotelBookCommonWSUtil;
import com.ctrip.corp.bff.hotel.book.common.util.HotelPayTypeUtil;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedInvoiceTypeRequestType;
import com.ctrip.corp.hotelbook.commonws.entity.RequestBaseInfoType;
import com.ctrip.corp.hotelbook.commonws.entity.UserInfoType;
import com.ctrip.model.CalculateServiceChargeV2ResponseType;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description 支持的发票类型查询
 * @Date 2024/8/22 10:00
 * @Version 1.0
 */
@Component
// todo:入参枚举优化
public class MapperOfGetSupportedInvoiceRequestType
        extends AbstractMapper<Tuple5<WrapperOfCheckAvail.CheckAvailInfo
                , CalculateServiceChargeV2ResponseType
                , BookingInitRequestType, HotelPayTypeEnum, HotelPayTypeEnum>, GetSupportedInvoiceTypeRequestType> {
    public static final String FILL_ORDER = "FILL_ORDER";

    public static final String PRIVATE = "P";
    public static final String PUBLIC = "C";
    @Override
    protected GetSupportedInvoiceTypeRequestType convert(Tuple5<WrapperOfCheckAvail.CheckAvailInfo
                , CalculateServiceChargeV2ResponseType
                , BookingInitRequestType, HotelPayTypeEnum, HotelPayTypeEnum> param) {
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = param.getT1();
        BookingInitRequestType bookingInitRequestType = param.getT3();
        HotelPayTypeEnum roomPayTypeEnum = param.getT4();
        HotelPayTypeEnum servicePayTypeEnum = param.getT5();

        GetSupportedInvoiceTypeRequestType request = new GetSupportedInvoiceTypeRequestType();
        request.setRequestBaseInfo(CorpHotelBookCommonWSUtil.buildRequestBaseInfoTypeWithPolicyUidNotNull(bookingInitRequestType));
        request.setScene(FILL_ORDER);
        request.setCorpPayType(CorpPayInfoUtil.isPublic(bookingInitRequestType.getCorpPayInfo()) ? PUBLIC : PRIVATE);
        request.setCheckAvailId(checkAvailInfo.getWsId());
        request.setPaymentMethod(CorpHotelBookCommonWSUtil.buildPaymentMethod(roomPayTypeEnum));

        String serviceChargePayment = CorpHotelBookCommonWSUtil.buildPaymentMethod(servicePayTypeEnum);
        request.setServiceChargePaymentMethod(StringUtils.isNotBlank(serviceChargePayment) ? serviceChargePayment : StringUtil.EMPTY);
        request.setHasServiceCharge(servicePayTypeEnum != HotelPayTypeEnum.NONE);
        request.setCorpXProductList(CorpHotelBookCommonWSUtil.buildCorpXProductInfoType(bookingInitRequestType));
        request.setBookingWithPersonalAccount(StrategyOfBookingInitUtil.bookingWithPersonalAccount(bookingInitRequestType.getStrategyInfos()));
        return request;
    }

    @Override
    protected ParamCheckResult check(Tuple5<WrapperOfCheckAvail.CheckAvailInfo
            , CalculateServiceChargeV2ResponseType
            , BookingInitRequestType, HotelPayTypeEnum, HotelPayTypeEnum> param) {
        return null;
    }
}
