package com.ctrip.corp.bff.hotel.book.common.enums;

import com.ctrip.corp.bff.framework.hotel.common.enums.check.IChecker;

/**
 * <AUTHOR>
 * @date 2024/8/28 21:05
 */
public enum BookingInitErrorEnum implements IChecker {
    /**
     * mapper入参校验错误
     */
    MAPPER_PARAM_CHECK_ERROR(601, "mapper param check error"),
    /**
     * 酒店静态信息mapper入参校验错误
     */
    HOTEL_DETAIL_REQUEST_MAPPER_PARAM_CHECK_ERROR(602, "hotelDetailRequestMapper param check error"),
    /**
     * 支付方式接口报错
     */
    DISTRIBUTE_PAYMENT_AMOUNT_ERROR(603, "distributePaymentAmount error"),
    /**
     * 可定接口报错
     */
    CHECK_AVAIL_ERROR(604, "checkAvail error"),
    /**
     * RC没有维护
     */
    REASONCODE_EMPTY(653, "buildRCInfoView rc is null"),
    /**
     * resourceToken有误
     */
    PARAM_VALID_RESOURCE_TOKEN_ERROR(655, "resourceToken error"),

    /**
     * 房费支付方式异常
     */
    ROOM_PAY_TYPE_ERROR(661, "RoomPayType error"),

    /**
     * 当前房型无可用担保方式，请选择其他房型。
     */
    NO_SUPPORT_GUARANTEE_PAY(662, "no support guarantee pay"),

    /**
     * 当前房型无可用支付方式，请选择其他房型。
     */
    NO_SUPPORT_PAY(663, "no support pay"),

    /**
     * 无支付方式可选，请联系贵司差旅负责人更改后台配置
     */
    NO_SUPPORT_SERVICE_PAY(664, "payType is null"),
    /**
     * 支付方式接口报错
     */
    ACTION_NAME_GET_SUPPORTED_PAYMENT_METHOD(665, "getSupportedPaymentMethod error"),
    /**
     * 服务费计算接口报错
     */
    ACTION_NAME_CALCULATE_SERVICE_CHARGE(666, "calculateServiceChargeV2 error"),

    /**
     * 预订节点缺失
     */
    PARAM_VALID_HOTEL_BOOK_INPUT_NULL(499, "hotelBookInput null error"),

    /**
     * 资源token缺失
     */
    PARAM_VALID_RESOURCE_TOKEN_NULL(498, "resourceToken null error"),
    /**
     * 房间数入参错误
     */
    PARAM_VALID_MISS_ROOM_QUANTITY_ERROR(497, "roomQuantity is null"),
    ;

    private final Integer errorCode;
    private final String errorMessage;

    BookingInitErrorEnum(Integer errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    @Override public String getKey() {
        return this.name();
    }
}
