package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataRequestType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @Description 聚合详情
 * @Date 2024/5/13 16:07
 * @Version 1.0
 */
@Component
public class MapperOfQueryHotelOrderDataRequest extends AbstractMapper<Tuple2<IntegrationSoaRequestType, Long>, QueryHotelOrderDataRequestType> {

    @Override
    protected QueryHotelOrderDataRequestType convert(Tuple2<IntegrationSoaRequestType, Long> param) {
        IntegrationSoaRequestType integrationSoaRequestType = param.getT1();
        QueryHotelOrderDataRequestType request = new QueryHotelOrderDataRequestType();
        request.setOrderId(param.getT2());
        request.setLocal(integrationSoaRequestType.getLanguage());
        request.setRequestId(UUID.randomUUID().toString());
        request.setUid(Optional.ofNullable(param.getT1()).map(IntegrationSoaRequestType::getUserInfo).map(UserInfo::getUserId).orElse(null));
        request.setEid(Optional.ofNullable(param.getT1()).map(IntegrationSoaRequestType::getEid).orElse(StringUtils.EMPTY));
        request.setModuleList(Arrays.asList("ValueAddedInfo", "RepeatOrder", "RemarkInfo", "OrderProcessPolicy", "FeeOrderInfo", "VendorInfo", "HotelInsurance"));
        // todo:sourceFrom
        if (!StringUtils.isEmpty(request.getEid())) {
            request.setDeleteFlag(true);
            request.setOperationChannel("Offline");
        }
        return request;
    }

    @Override
    protected ParamCheckResult check(Tuple2<IntegrationSoaRequestType, Long> param) {
        return null;
    }

}
