package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.agg.hotel.expense.contract.model.ChargeAmountInfoType;
import com.ctrip.corp.agg.hotel.expense.contract.model.ServiceChargePriceType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookingRulesType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.CancelPolicyType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.GuaranteeDetailType;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.entity.contract.FlashStayInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.OriginalOrderInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.RoomAttributeInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.OrderResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.ObjectUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.PosEnum;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalFlowReuseInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.TripInput;
import com.ctrip.corp.bff.hotel.book.common.builder.BookingInitAssembleRequest;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.CustomConfigKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.BookingInitErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelBalanceTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelGuaranteeTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.InvoiceEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.PaymentGuaranteePolyEnum;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.TripInfoInput;
import com.ctrip.corp.foundation.common.util.Null;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedInvoiceTypeResponseType;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodResponseType;
import com.ctrip.corp.hotelbook.commonws.entity.GuaranteeMethodInfoType;
import com.ctrip.model.CalculateServiceChargeV2ResponseType;
import com.google.common.collect.Lists;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.zookeeper.Op;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.*;
import java.util.stream.Collectors;

import static com.ctrip.corp.bff.framework.template.common.utils.StringUtil.equalsIgnoreCase;
import static com.ctrip.corp.bff.framework.template.common.utils.StringUtil.isEmpty;

/**
 * <AUTHOR>
 * @date 2024/8/29 22:06
 */
public class BookingInitUtil {

    public static final String ACCOUNT_PAY = "ACCOUNT_PAY";
    public static final String CORP_PAY = "CORP_PAY";
    public static final String SELF_PAY = "SELF_PAY";
    public static final String INDIVIDUAL_PAY = "INDIVIDUAL_PAY";
    public static final String MIX_PAY = "MIX_PAY";
    public static final String CASH_PAY = "CASH_PAY";
    public static final String UNION_PAY = "UNION_PAY";
    public static final String DISABLED = "DISABLED";
    public static final String FLASH_STAY_PAY = "FLASH_STAY_PAY";
    public static final String PRBAL = "PRBAL";
    public static final String CORPORATE_CARD_PAY = "CORPORATE_CARD_PAY";
    /**
     * 商旅uid注册
     */
    public static final String BUSINESS_TRAVEL_REGISTER = "BUSINESS_TRAVEL_REGISTER";

    /**
     * 携程uid注册
     */
    public static final String TRIP_REGISTER = "TRIP_REGISTER";

    /**
     * 不需要注册
     */
    public static final String NO_REGISTER = "NO_REGISTER";

    /**
     * 会员卡有效状态
     */
    public static final int VIP_WORK_STATUS_EFFECTIVE = 1;

    public static final int VIP_WORK_STATUS_DISABLE = 3;

    public static final String PRE_CHARGE = "PreCharge";

    public static final String TITLE_TYPE_E = "TITLE_TYPE_E";
    public static final String TITLE_TYPE_P = "TITLE_TYPE_P";
    public static final String TITLE_TYPE_G = "TITLE_TYPE_G";
    /**
     * 无需担保
     */
    private static final String GUARANTEE_TYPE_NONE = "None";

    /**
     * 免费取消
     */
    private static final String FREE = "FREE";

    /**
     * 获取服务费支付方式
     *
     * @param getSupportedPaymentMethodResponseType
     * @return
     */
    public static HotelPayTypeEnum getServicePayType(List<HotelPayTypeInput> hotelPayTypeInputs,
        HotelPayTypeEnum servicePayTypeEnum,
        GetSupportedPaymentMethodResponseType getSupportedPaymentMethodResponseType, FlashStayInput flashStayInput,
        WrapperOfAccount.AccountInfo accountInfo,
        CalculateServiceChargeV2ResponseType calculateServiceChargeV2Response) {
        List<HotelPayTypeEnum> serviceChargeList = Optional.ofNullable(getSupportedPaymentMethodResponseType)
            .map(GetSupportedPaymentMethodResponseType::getServiceChargePaymentMethodList).orElse(Lists.newArrayList())
            .stream().map(m -> {
                HotelPayTypeEnum hotelPayTypeEnum = buildPayTypeEnum(m.getPaymentMethod());
                Boolean disable = StringUtil.equalsIgnoreCase(DISABLED, m.getStatus());
                return hotelPayTypeEnum;
            }).filter(Objects::nonNull).sorted().collect(Collectors.toList());
        HotelPayTypeEnum servicePayType = getDefaultServicePayment(
            getRoomPayType(servicePayTypeEnum, getSupportedPaymentMethodResponseType, flashStayInput, accountInfo),
            HotelPayTypeUtil.getServicePayType(hotelPayTypeInputs), serviceChargeList,
            getChargeAmountInfoList(calculateServiceChargeV2Response));
        return servicePayType;
    }

    public static List<ChargeAmountInfoType> getChargeAmountInfoList(
        CalculateServiceChargeV2ResponseType responseType) {
        if (responseType == null || CollectionUtils.isEmpty(responseType.getChargeAmountInfoList())) {
            return null;
        }
        List<ChargeAmountInfoType> chargeAmountInfoTypes =
            responseType.getChargeAmountInfoList().stream().filter((s) -> {
                return s != null && s.getChargeAmountPack() != null &&
                    Optional.ofNullable(s.getChargeAmountPack().getChargeAmountCustomCurrency())
                        .map(ServiceChargePriceType::getAmount).orElse(new BigDecimal(BigInteger.ZERO)).compareTo(BigDecimal.ZERO) > 0;
            }).filter(infoType -> PRE_CHARGE.equals(infoType.getChargeMoment())).collect(Collectors.toList());
        return chargeAmountInfoTypes;
    }

    /**
     * @param defaultRoomPayType
     * @param selectServicePayment
     * @param servicePaymentMethodList
     * @param serviceFeeAmountList
     * @return
     */
    private static HotelPayTypeEnum getDefaultServicePayment(HotelPayTypeEnum defaultRoomPayType,
        HotelPayTypeEnum selectServicePayment, List<HotelPayTypeEnum> servicePaymentMethodList,
        List<ChargeAmountInfoType> serviceFeeAmountList) {
        if (CollectionUtil.isEmpty(serviceFeeAmountList)) {
            return HotelPayTypeEnum.NONE;
        }

        if (servicePaymentMethodList.stream().anyMatch(m -> m == HotelPayTypeEnum.PRBAL)) {
            return HotelPayTypeEnum.PRBAL;
        }

        if (servicePaymentMethodList.stream().anyMatch(m -> m == HotelPayTypeEnum.UNION_PAY)) {
            return HotelPayTypeEnum.SELF_PAY;
        }

        if (Arrays.asList(HotelPayTypeEnum.CORPORATE_CARD_PAY, HotelPayTypeEnum.CORP_PAY, HotelPayTypeEnum.MIX_PAY).contains(defaultRoomPayType)) {
            if (serviceFeeAmountList.stream().anyMatch(m -> "AccountPay".equals(m.getPaymentMethod()))) {
                return HotelPayTypeEnum.CORP_PAY;
            }

            return HotelPayTypeEnum.NONE;
        } else if (HotelPayTypeEnum.SELF_PAY == defaultRoomPayType) {
            if (serviceFeeAmountList.stream().anyMatch(m -> "PersonalPay".equals(m.getPaymentMethod()))) {
                return HotelPayTypeEnum.SELF_PAY;
            }
            return HotelPayTypeEnum.NONE;
        } else if (HotelPayTypeEnum.CASH == defaultRoomPayType) {
            if (CollectionUtils.isEmpty(servicePaymentMethodList) && serviceFeeAmountList.stream()
                .anyMatch(m -> "PersonalPay".equals(m.getPaymentMethod()))) {
                throw BusinessExceptionBuilder.createAlertException(BookingInitErrorEnum.NO_SUPPORT_SERVICE_PAY,
                    BookingInitErrorEnum.NO_SUPPORT_SERVICE_PAY.getErrorMessage());
            }
            if (servicePaymentMethodList.stream().anyMatch(m -> m == selectServicePayment)) {
                return selectServicePayment;
            }
            if (servicePaymentMethodList.stream().anyMatch(m -> m == HotelPayTypeEnum.CORP_PAY)) {
                return HotelPayTypeEnum.CORP_PAY;
            }

            if (servicePaymentMethodList.stream().anyMatch(m -> m == HotelPayTypeEnum.SELF_PAY)) {
                return HotelPayTypeEnum.SELF_PAY;
            }

        } else if (HotelPayTypeEnum.FLASH_STAY_PAY == defaultRoomPayType) {
            return HotelPayTypeEnum.FLASH_STAY_PAY;
        }

        return HotelPayTypeEnum.NONE;
    }

    /**
     * 获取默认的房费支付方式
     *
     * @param response
     * @param accountInfo
     * @return
     */
    public static HotelPayTypeEnum getRoomPayType(HotelPayTypeEnum selectedPayType,
        GetSupportedPaymentMethodResponseType response, FlashStayInput flashStayInput,
        WrapperOfAccount.AccountInfo accountInfo) {
        return getRoomPayTypeCore(response,
            Optional.ofNullable(flashStayInput).map(FlashStayInput::getSupportFlashStay).map("T"::equals).orElse(false),
            selectedPayType, accountInfo);
    }

    /**
     * 获取默认的房费支付方式
     *
     * @param response
     * @param supportFlash
     * @param selectPayType
     * @param accountInfo
     * @return
     */
    private static HotelPayTypeEnum getRoomPayTypeCore(GetSupportedPaymentMethodResponseType response, Boolean supportFlash,
        HotelPayTypeEnum selectPayType, WrapperOfAccount.AccountInfo accountInfo) {
        List<HotelPayTypeEnum> payList =
            Optional.ofNullable(response).map(GetSupportedPaymentMethodResponseType::getPaymentMethodList)
                .orElse(Lists.newArrayList()).stream().map(m -> {
                    // 过滤闪住方式
                    if (!BooleanUtil.toBoolean(supportFlash) && FLASH_STAY_PAY.equalsIgnoreCase(m.getPaymentMethod())) {
                        return null;
                    }
                    HotelPayTypeEnum hotelPayTypeEnum = buildPayTypeEnum(m.getPaymentMethod());
                    boolean disable = StringUtil.equalsIgnoreCase(DISABLED, m.getStatus());
                    if (!disable && hotelPayTypeEnum != HotelPayTypeEnum.NONE) {
                        return hotelPayTypeEnum;
                    }
                    return null;
                }).filter(Objects::nonNull).sorted().collect(Collectors.toList());

        // todo:差标政策开关
        HotelPayTypeEnum roomPayType = getBffRoomPayType(selectPayType, null, payList);
        if (roomPayType == HotelPayTypeEnum.NONE) {
            throw BusinessExceptionBuilder.createAlertException(BookingInitErrorEnum.NO_SUPPORT_PAY, "not support pay");
        }
        return roomPayType;
    }

    public static HotelPayTypeEnum getBffRoomPayType(HotelPayTypeEnum defaultPayment, String hotelBookingFilterByPolicy,
        List<HotelPayTypeEnum> payList) {

        boolean isDefaultPolicy = StringUtil.equalsIgnoreCase("S", hotelBookingFilterByPolicy);

        if (payList.contains(HotelPayTypeEnum.UNION_PAY)) {
            return HotelPayTypeEnum.UNION_PAY;
        }

        if (CollectionUtils.isEmpty(payList)) {
            return HotelPayTypeEnum.NONE;
        }

        if (payList.contains(defaultPayment)) {
            return defaultPayment;
        }

        // 有闪住默认闪住支付方式
        if (payList.contains(HotelPayTypeEnum.FLASH_STAY_PAY)) {
            return HotelPayTypeEnum.FLASH_STAY_PAY;
        }

        if (payList.contains(HotelPayTypeEnum.MIX_PAY) && isDefaultPolicy) {
            // 符合差标勾选不可改，默认混付
            return HotelPayTypeEnum.MIX_PAY;
        }
        // 默认公务卡
        if (payList.contains(HotelPayTypeEnum.CORPORATE_CARD_PAY)) {
            return HotelPayTypeEnum.CORPORATE_CARD_PAY;
        }
        // 默认虚拟支付
        if (payList.contains(HotelPayTypeEnum.PRBAL)) {
            return HotelPayTypeEnum.PRBAL;
        }

        if (payList.contains(HotelPayTypeEnum.CORP_PAY)) {
            return HotelPayTypeEnum.CORP_PAY;
        }

        if (payList.contains(HotelPayTypeEnum.SELF_PAY)) {
            return HotelPayTypeEnum.SELF_PAY;
        }

        return CollectionUtil.findFirst(payList, Objects::nonNull);
    }

    public static HotelPayTypeEnum buildPayTypeEnum(String payTypeEnum) {
        if (StringUtil.isBlank(payTypeEnum)) {
            return HotelPayTypeEnum.NONE;
        }

        switch (payTypeEnum) {
            case ACCOUNT_PAY:
            case CORP_PAY:
                return HotelPayTypeEnum.CORP_PAY;
            case INDIVIDUAL_PAY:
            case SELF_PAY:
                return HotelPayTypeEnum.SELF_PAY;
            case MIX_PAY:
                return HotelPayTypeEnum.MIX_PAY;
            case CASH_PAY:
                return HotelPayTypeEnum.CASH;
            case UNION_PAY:
                return HotelPayTypeEnum.UNION_PAY;
            case FLASH_STAY_PAY:
                return HotelPayTypeEnum.FLASH_STAY_PAY;
            case PRBAL:
                return HotelPayTypeEnum.PRBAL;
            case CORPORATE_CARD_PAY:
                return HotelPayTypeEnum.CORPORATE_CARD_PAY;
            default:
                return HotelPayTypeEnum.NONE;
        }
    }


    /**
     * 获取服务费金额信息
     *
     * 蓝色空间降噪-调服务费金额传参-兜底恒取公帐的
     *
     * @param responseType
     * @param servicePayType
     * @param roomPayType
     * @return
     */
    public static ChargeAmountInfoType getChargeAmountInfoType(CalculateServiceChargeV2ResponseType responseType,
        HotelPayTypeEnum servicePayType, HotelPayTypeEnum roomPayType) {
        List<ChargeAmountInfoType> chargeAmountInfoTypes = getChargeAmountInfoList(responseType);

        ChargeAmountInfoType infoType = null;
        if (CollectionUtils.isNotEmpty(chargeAmountInfoTypes)) {
            if (servicePayType.isCorpPay()) {
                if (HotelPayTypeEnum.CASH == roomPayType) {
                    infoType = chargeAmountInfoTypes.stream()
                        .filter(m -> StringUtil.equalsIgnoreCase(m.getPaymentMethod(), "PersonalPay")).findFirst()
                        .orElse(null);
                } else {
                    infoType = chargeAmountInfoTypes.stream()
                        .filter(m -> StringUtil.equalsIgnoreCase(m.getPaymentMethod(), "AccountPay")).findFirst()
                        .orElse(null);
                }
            } else if (servicePayType.isSelfPay()) {
                infoType = chargeAmountInfoTypes.stream()
                    .filter(m -> StringUtil.equalsIgnoreCase(m.getPaymentMethod(), "PersonalPay")).findFirst()
                    .orElse(null);
            } else if (roomPayType == HotelPayTypeEnum.PRBAL) {
                infoType = chargeAmountInfoTypes.stream()
                        .filter(m -> StringUtil.equalsIgnoreCase(m.getPaymentMethod(), "AccountPay")).findFirst()
                        .orElse(null);
            }
        }

        if (infoType == null || infoType.getChargeAmountPack() == null
            || infoType.getChargeAmountPack().getChargeAmountCustomCurrency() == null) {
            return null;
        }
        return infoType;
    }

    public static String getRoomAttribute(List<RoomAttributeInfo> attributeInfos, String attributeKey) {
        if (CollectionUtils.isEmpty(attributeInfos)) {
            return null;
        }
        for (RoomAttributeInfo attributeInfo : attributeInfos) {
            if (attributeInfo != null && Objects.equals(attributeKey, attributeInfo.getAttributeType())) {
                return attributeInfo.getAttributeValue();
            }
        }
        return null;
    }

    /**
     * 获取订单号
     *
     * @param bookingInitRequestType
     * @return
     */
    public static Long getOrderId(BookingInitRequestType bookingInitRequestType, ResourceToken resourceToken) {
        Long orderId = Optional.ofNullable(resourceToken).map(ResourceToken::getOrderResourceToken)
                .map(OrderResourceToken::getOrderId).orElse(null);
        if (orderId != null) {
            return orderId;
        }
        if (!StringUtils.isEmpty(bookingInitRequestType.getOrderId())) {
            return Long.valueOf(bookingInitRequestType.getOrderId());
        }
        if (StringUtil.isNotBlank(Optional.ofNullable(bookingInitRequestType.getOriginalOrderInput())
                .map(OriginalOrderInput::getOriginalOrderId).orElse(null))) {
            return TemplateNumberUtil.parseLong(Optional.ofNullable(bookingInitRequestType.getOriginalOrderInput())
                    .map(OriginalOrderInput::getOriginalOrderId).orElse(null));
        }
        return null;
    }
    
    public static String getSourceFrom(SourceFrom sourceFrom) {
        if (sourceFrom == SourceFrom.Offline) {
            return "offline";
        }
        if (sourceFrom == SourceFrom.Online) {
            return "online";
        }
        return "app";
    }

    public static boolean buildShowOrgPrice(WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo,
                                      ResourceToken resourceToken, WrapperOfAccount.AccountInfo accountInfo) {
        boolean isOriginal = isOriginal(checkAvailInfo, accountInfo, resourceToken);
        return isShowOrgPrice(isOriginal, accountInfo.getCurrency(), checkAvailInfo.getOriginCurrency());
    }

    private static boolean isOriginal(WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo,
                              WrapperOfAccount.AccountInfo accountInfo, ResourceToken resourceToken) {
        boolean isCustomCurrency = isCustomCurrency(accountInfo.getCurrency());
        boolean amadeus = checkAvailInfo.isAmadues();
        String balanceType = Optional.ofNullable(resourceToken).map(ResourceToken::getRoomResourceToken)
                .map(RoomResourceToken::getBalanceType).orElse(null);
        boolean isForceVcc = BooleanUtil.isTrue(checkAvailInfo.isForceVccPay());
        if (!isCustomCurrency) {
            return HotelBalanceTypeEnum.FG.getDesc().equals(balanceType) || HotelBalanceTypeEnum.PH.getDesc()
                    .equals(balanceType) || isForceVcc;

        }
        if (amadeus) {
            return true;
        }
        if (HotelBalanceTypeEnum.PH.getDesc().equals(balanceType)) {
            return true;
        }
        if (HotelBalanceTypeEnum.FG.getDesc().equals(balanceType)) {
            // 现付无需担保 todo:担保逻辑判断
            GuaranteeDetailType guaranteeDetailType = Optional.ofNullable(checkAvailInfo.getBookingRules())
                    .map(BookingRulesType::getCancelPolicyInfo).map(CancelPolicyType::getGuaranteePolicyInfo).orElse(null);
            if (guaranteeDetailType == null) {
                return true;
            }
            String guaranteeReason = guaranteeDetailType.getGuaranteeReason();
            List<String> guaranteeReasonsTOS = Arrays.asList("All", "ArrivalTime", "RoomQuantity");
            if (!CollectionUtil.containsIgnoreCase(guaranteeReasonsTOS, guaranteeReason)) {
                return true;
            }
            // 担保到酒店
            return PaymentGuaranteePolyEnum.PAY_TO_HOTEL.equals(PaymentGuaranteePolyEnum.getPaymentGuaranteePolyEnum(guaranteeDetailType.getPaymentGuaranteePoly()));
        }
        return false;
    }

    private static boolean isShowOrgPrice(Boolean isOriginal, String customCurrency, String originCurrency) {
        return BooleanUtils.isFalse(isSameCurrency(customCurrency, originCurrency)) && BooleanUtils.isTrue(
                isOriginal);
    }

    private final static String CNY = "CNY";
    private static final String RMB = "RMB";
    private static boolean isSameCurrency(String currency1, String currency2) {
        return Optional.ofNullable(currency1).orElse(CNY).replace(RMB, CNY)
                .equalsIgnoreCase(Optional.ofNullable(currency2).orElse(CNY).replace(RMB, CNY));

    }

    private static boolean isCustomCurrency(String customCurrency) {
        return !Optional.ofNullable(customCurrency).orElse(CNY).equalsIgnoreCase(CNY);
    }

    public static BigDecimal buildDayOfAvgPriceCny(WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo) {
        if (CollectionUtil.isEmpty(checkAvailInfo.getRoomDailyInfos())) {
            return null;
        }
        BigDecimal sum = checkAvailInfo.getRoomDailyInfos().stream().map(o -> {
            if (ObjectUtil.isNotNull(o.getSalePromotionInfo())) {
                return o.getSalePromotionInfo().getAfterPromotCNYAmount();
            }
            return o.getCNYAmount();
        }).reduce(BigDecimal::add).get();
        if (MathUtils.isGreaterThanZero(sum)) {
            return MathUtils.divide(sum, checkAvailInfo.getRoomDailyInfos().size());
        }
        return null;
    }

    public static BigDecimal buildDayOfAvgPriceOriginal(WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo) {
        if (CollectionUtil.isEmpty(checkAvailInfo.getRoomDailyInfos())) {
            return null;
        }
        BigDecimal sum = checkAvailInfo.getRoomDailyInfos().stream().map(o -> {
            if (ObjectUtil.isNotNull(o.getSalePromotionInfo())) {
                return o.getSalePromotionInfo().getAfterPromotOriginAmount();
            }
            return o.getAmount();
        }).reduce(BigDecimal::add).get();
        if (MathUtils.isGreaterThanZero(sum)) {
            return MathUtils.divide(sum, checkAvailInfo.getRoomDailyInfos().size());
        }
        return null;
    }

    public static BigDecimal buildDayOfAvgPriceCustom(WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo) {
        if (CollectionUtil.isEmpty(checkAvailInfo.getRoomDailyInfos())) {
            return null;
        }
        BigDecimal sum = checkAvailInfo.getRoomDailyInfos().stream().map(o -> {
            if (ObjectUtil.isNotNull(o.getSalePromotionInfo())) {
                return o.getSalePromotionInfo().getAfterPromotCustomAmount();
            }
            return o.getCustomAmount();
        }).reduce(BigDecimal::add).get();
        if (MathUtils.isGreaterThanZero(sum)) {
            return MathUtils.divide(sum, checkAvailInfo.getRoomDailyInfos().size());
        }
        return null;
    }

    /**
     * 取消话术使用
     *
     * @param getSupportedPaymentMethodResponseType
     * @return
     */
    public static boolean hasGuaGuaranteeType(GetSupportedPaymentMethodResponseType getSupportedPaymentMethodResponseType){
        List<GuaranteeMethodInfoType> guaranteeMethodList = Optional.ofNullable(getSupportedPaymentMethodResponseType)
            .map(GetSupportedPaymentMethodResponseType::getGuaranteeMethodList)
            .orElse(null);
        if (CollectionUtil.isEmpty(guaranteeMethodList)) {
            return false;
        }
        List<HotelGuaranteeTypeEnum> guaranteeTypeEnums = guaranteeMethodList.stream()
            .filter(Objects::nonNull)
            .map(GuaranteeMethodInfoType::getGuaranteeMethod)
            .map(HotelGuaranteeTypeEnum::getGuaranteeMethod)
            .filter(t -> t != null && HotelGuaranteeTypeEnum.NONE != t).sorted()
            .collect(Collectors.toList());
        return !CollectionUtil.isEmpty(guaranteeTypeEnums);
    }

    public static boolean buildModifyServiceFeeOrder(BookingInitRequestType bookingInitRequestType) {
        return StrategyOfBookingInitUtil.modify(bookingInitRequestType.getStrategyInfos())
            || StrategyOfBookingInitUtil.onlyApplyModify(bookingInitRequestType.getStrategyInfos());
    }
    public static HotelGuaranteeTypeEnum buildHotelGuaranteeTypeEnum(GetSupportedPaymentMethodResponseType getSupportedPaymentMethodResponseType,
        List<HotelPayTypeInput> hotelPayTypeInputs) {
        List<GuaranteeMethodInfoType> guaranteeMethodInfoTypes = Optional.ofNullable(getSupportedPaymentMethodResponseType)
            .map(GetSupportedPaymentMethodResponseType::getGuaranteeMethodList).orElse(null);
        if (CollectionUtil.isEmpty(guaranteeMethodInfoTypes)) {
            return null;
        }
        HotelGuaranteeTypeEnum selectGuaranteeType = buildSelectGuaranteeType(hotelPayTypeInputs);
        return buildDefaultGuaranteeType(guaranteeMethodInfoTypes, selectGuaranteeType);
    }
    private static final String GUARANTEE = "GUARANTEE";
    public static HotelGuaranteeTypeEnum buildSelectGuaranteeType(List<HotelPayTypeInput> hotelPayTypeInput) {
        if (CollectionUtil.isEmpty(hotelPayTypeInput)) {
            return null;
        }
        for (HotelPayTypeInput input : hotelPayTypeInput) {
            if (input == null || !GUARANTEE.equalsIgnoreCase(input.getPayCode())) {
                continue;
            }
            return HotelGuaranteeTypeEnum.getValue(input.getPayType());
        }
        return null;
    }

    public static HotelGuaranteeTypeEnum buildDefaultGuaranteeType(List<GuaranteeMethodInfoType> guaranteeMethodInfoTypes,
        HotelGuaranteeTypeEnum selectGuaranteeType) {
        List<HotelGuaranteeTypeEnum> guaranteeTypeEnums =
                Null.or(guaranteeMethodInfoTypes, new ArrayList<GuaranteeMethodInfoType>()).stream().filter(Objects::nonNull).map(GuaranteeMethodInfoType::getGuaranteeMethod)
                .map(HotelGuaranteeTypeEnum::getGuaranteeMethod).filter(t -> t != null && HotelGuaranteeTypeEnum.NONE != t)
                .sorted().collect(Collectors.toList());
        if (CollectionUtil.isEmpty(guaranteeTypeEnums)) {
            return null;
        }
        if (guaranteeTypeEnums.contains(selectGuaranteeType)) {
            return selectGuaranteeType;
        }
        return guaranteeTypeEnums.stream().findFirst().orElse(null);
    }


    public static TripInput buildTripInput(TripInfoInput tripInfoInput) {
        if (tripInfoInput == null) {
            return null;
        }
        TripInput tripInput = new TripInput();
        tripInput.setTripId(tripInfoInput.getTripId());
        return tripInput;
    }

    public static boolean supportInvoiceTip(BookingInitRequestType request,
        GetSupportedInvoiceTypeResponseType getSupportedInvoiceTypeResponseType) {
        if (!QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.SUPPORT_INVOICE_TIP,
            request.getIntegrationSoaRequestType().getUserInfo().getCorpId())) {
            return false;
        }
        return !hasInvoice(getSupportedInvoiceTypeResponseType);
    }

    public static boolean supportNewInvoice(BookingInitRequestType request) {
        if (QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.NEW_INVOICE,
            request.getIntegrationSoaRequestType().getUserInfo().getCorpId())) {
            return true;
        }
        return false;
    }

    protected static boolean hasInvoice(GetSupportedInvoiceTypeResponseType getSupportedInvoiceTypeResponseType) {
        if (getSupportedInvoiceTypeResponseType == null) {
            return false;
        }
        if (CollectionUtil.isEmpty(getSupportedInvoiceTypeResponseType.getInvoiceTypeList())) {
            return false;
        }
        if (getSupportedInvoiceTypeResponseType.getInvoiceTypeList().stream().anyMatch(
            invoiceInfoType -> invoiceInfoType != null && InvoiceEnum.INVOICE.getAggCode()
                .equalsIgnoreCase(invoiceInfoType.getType()))) {
            return true;
        }
        return false;
    }

    public static String buildCostCenterNew(BookingInitRequestType requestType) {
        if (requestType.getIntegrationSoaRequestType().getUserInfo().getPos() != PosEnum.CHINA) {
            return buildBlueSpaceCostCenterNew(requestType);
        }
        if (QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.COST_CENTER_NEW_USE,
            requestType.getIntegrationSoaRequestType().getUserInfo().getCorpId())) {
            return CommonConstant.USE_NEW;
        }
        if (QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.COST_CENTER_NEW_COMPARE,
            requestType.getIntegrationSoaRequestType().getUserInfo().getCorpId())) {
            return CommonConstant.COMPARE;
        }
        return CommonConstant.USE_OLD;
    }

    public static String buildBlueSpaceCostCenterNew(BookingInitRequestType requestType) {
        if (QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.BLUE_SPACE_COST_CENTER_NEW_USE,
            requestType.getIntegrationSoaRequestType().getUserInfo().getCorpId())) {
            return CommonConstant.USE_NEW;
        }
        if (QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.BLUE_SPACE_COST_CENTER_NEW_COMPARE,
            requestType.getIntegrationSoaRequestType().getUserInfo().getCorpId())) {
            return CommonConstant.COMPARE;
        }
        return CommonConstant.USE_OLD;
    }

    /**
     * 审批沿用灰度
     *
     * @return
     */
    public static boolean approvalFlowReuseNew(BookingInitRequestType bookingInitRequestType,
        Map<String, StrategyInfo> strategyInfoMap) {
        // 重新预定-vo控制，传入新节点代表肯定是灰度开启了
        if (StringUtil.isNotBlank(Optional.ofNullable(bookingInitRequestType.getOriginalOrderInput())
            .map(OriginalOrderInput::getOriginalOrderId).orElse(null)) && StrategyOfBookingInitUtil.approvalReuseReBook(
            strategyInfoMap)) {
            return true;
        }
        return false;
    }
}