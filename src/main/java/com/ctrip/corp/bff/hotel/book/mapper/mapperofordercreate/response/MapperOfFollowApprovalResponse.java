package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response;

import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QconfigOfInitConfig;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount.AccountInfo;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfCityBaseInfo;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfCityBaseInfo.CityBaseInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.FollowApprovalResult;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.TripInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple6;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple7;
import com.ctrip.corp.bff.hotel.book.common.constant.CustomConfigKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.contract.FollowApprovalInfo;
import com.ctrip.corp.bff.hotel.book.contract.FollowApprovalInfoInput;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType;
import com.ctrip.corp.bff.hotel.book.contract.ReasonInfo;
import com.ctrip.corp.hotel.book.query.entity.CityBaseInfoEntity;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType;
import com.ctrip.soa._20184.CheckHotelAuthExtensionResponseType;
import com.ctrip.soa._20184.OriginalOrderInfoType;
import com.ctrip.soa._20184.QueryHotelAuthExtensionResponseType;
import com.ctrip.soa._21234.ApprovalInfo;
import com.ctrip.soa._21234.SearchTripDetailResponseType;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * 校验沿用审批：订单详情重新预订进入通过CheckHotelAuthExtensionResponseType校验是否可沿用 不可沿用/可以沿用都需提醒客户 由客户选择继续提交
 * 智能沿用审批：QueryHotelAuthExtensionResponseType查询是否存在可沿用订单 无可沿用单据直接走后续流程 有可延用单据需提醒客户 由客户选择继续提
 * @Date 2024/7/15 14:19
 * @Version 1.0
 */
@Component
public class MapperOfFollowApprovalResponse extends
    AbstractMapper<Tuple7<QueryHotelAuthExtensionResponseType, CheckHotelAuthExtensionResponseType,
            OrderCreateRequestType, CityBaseInfo, OrderCreateToken, SearchTripDetailResponseType,
            WrapperOfAccount.AccountInfo>,
        Tuple2<Boolean, OrderCreateResponseType>> {
    private static final String FOLLOW_APPROVAL_RESULT_T = "T";
    private static final String FOLLOW_APPROVAL_RESULT_F = "F";
    // 不可延用原因
    private static final String FAIL_FOLLOW_REASON =
        "ctrip.com.hotel.booking.biz.text.fail.follow.reason.{0}";
    public static final List<String> APPROVAL_PASS = Arrays.asList("A", "T", "a", "t");

    @Override
    protected Tuple2<Boolean, OrderCreateResponseType> convert(Tuple7<QueryHotelAuthExtensionResponseType,
        CheckHotelAuthExtensionResponseType, OrderCreateRequestType, WrapperOfCityBaseInfo.CityBaseInfo,
        OrderCreateToken, SearchTripDetailResponseType, WrapperOfAccount.AccountInfo> tuple) {
        QueryHotelAuthExtensionResponseType queryHotelAuthExtensionResponseType = tuple.getT1();
        CheckHotelAuthExtensionResponseType checkHotelAuthExtensionResponseType = tuple.getT2();
        OrderCreateRequestType orderCreateRequestType = tuple.getT3();
        WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo = tuple.getT4();
        OrderCreateToken orderCreateToken = tuple.getT5();
        SearchTripDetailResponseType searchTripDetailResponseType = tuple.getT6();
        WrapperOfAccount.AccountInfo accountInfo = tuple.getT7();
        // offline人工延用---目前仅支持行程，对行程单详情进行校验
        if (OrderCreateProcessorOfUtil.requireArtificialFollowApproval(orderCreateRequestType, orderCreateToken)) {
            return buildOrderCreateResponseTypeByTripDetail(orderCreateRequestType, searchTripDetailResponseType,
                orderCreateToken, cityBaseInfo);
        }
        // 校验沿用审批
        if (checkHotelAuthExtensionResponseType != null) {
            return buildOrderCreateResponseTypeByCheck(checkHotelAuthExtensionResponseType, orderCreateRequestType,
                cityBaseInfo, orderCreateToken, accountInfo);
        }
        // 智能沿用审批
        if (queryHotelAuthExtensionResponseType != null) {
            return buildOrderCreateResponseTypeByQuery(queryHotelAuthExtensionResponseType,
                cityBaseInfo, orderCreateToken, orderCreateRequestType, accountInfo);
        }
        // 无审批延用执行后续流程
        return Tuple2.of(false, null);
    }

    @Override
    protected ParamCheckResult check(Tuple7<QueryHotelAuthExtensionResponseType,
                CheckHotelAuthExtensionResponseType, OrderCreateRequestType, CityBaseInfo,
            OrderCreateToken, SearchTripDetailResponseType, AccountInfo> tuple) {
        return null;
    }

    private Tuple2<Boolean, OrderCreateResponseType> buildOrderCreateResponseTypeByTripDetail(
        OrderCreateRequestType orderCreateRequestType, SearchTripDetailResponseType searchTripDetailResponseType,
        OrderCreateToken orderCreateToken, WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo) {
        OrderCreateResponseType orderCreateResponseType = new OrderCreateResponseType();
        orderCreateResponseType.setCityInfo(
            Optional.ofNullable(cityBaseInfo).map(CityBaseInfo::getCityInfo).orElse(null));
        orderCreateToken.addContinueTypes(ContinueTypeConst.FOLLOW_APPROVAL);
        orderCreateResponseType.setOrderCreateToken(
            TokenParseUtil.generateToken(orderCreateToken, OrderCreateToken.class));
        // 用户还未选择初次判断-不可沿用 提示客户 由客户选择 不沿用 后二次提交
        if (!canApprovalFlow(searchTripDetailResponseType)) {
            orderCreateResponseType.setFollowApprovalInfo(buildNoFollowApprovalInfo(null, null, null));
            return Tuple2.of(true, orderCreateResponseType);
        }
        // 用户还未选择初次判断-可沿用 提示客户 由客户选择 沿用/不沿用 后二次提交
        orderCreateToken.setFollowApprovalResult(
            buildCanFollowApprovalResult(orderCreateRequestType.getFollowApprovalInfoInput().getFollowTripId(), null));
        orderCreateResponseType.setFollowApprovalInfo(
            buildCanFollowApprovalInfo(orderCreateRequestType.getFollowApprovalInfoInput().getFollowTripId(), null));
        orderCreateResponseType.setOrderCreateToken(
            TokenParseUtil.generateToken(orderCreateToken, OrderCreateToken.class));
        return Tuple2.of(true, orderCreateResponseType);
    }

    private FollowApprovalInfo buildCanFollowApprovalInfo(String tripId, String followOrderNo) {
        FollowApprovalInfo followApprovalInfo = new FollowApprovalInfo();
        followApprovalInfo.setFollowApprovalResult(FOLLOW_APPROVAL_RESULT_T);
        followApprovalInfo.setTripId(tripId);
        followApprovalInfo.setOrderId(followOrderNo);
        return followApprovalInfo;
    }

    private FollowApprovalInfo buildNoFollowApprovalInfo(String tripId, String followOrderNo, List<String> reasonList) {
        FollowApprovalInfo followApprovalInfo = new FollowApprovalInfo();
        followApprovalInfo.setFollowApprovalResult(FOLLOW_APPROVAL_RESULT_F);
        followApprovalInfo.setTripId(tripId);
        followApprovalInfo.setOrderId(followOrderNo);
        followApprovalInfo.setReasonInfos(buildReasonInfos(reasonList));
        return followApprovalInfo;
    }

    private FollowApprovalResult buildCanFollowApprovalResult(String tripId, String followOrderNo) {
        FollowApprovalResult followApprovalResult = new FollowApprovalResult();
        followApprovalResult.setCanFollowApproval(FOLLOW_APPROVAL_RESULT_T);
        followApprovalResult.setTripId(tripId);
        followApprovalResult.setFollowOrderNo(followOrderNo);
        return followApprovalResult;
    }

    private FollowApprovalResult buildNoFollowApprovalResult(String tripId, String followOrderNo) {
        FollowApprovalResult followApprovalResult = new FollowApprovalResult();
        followApprovalResult.setCanFollowApproval(FOLLOW_APPROVAL_RESULT_F);
        followApprovalResult.setTripId(tripId);
        followApprovalResult.setFollowOrderNo(followOrderNo);
        return followApprovalResult;
    }


    /**
     * 行程单详情对应的审批是否可沿用
     * <p>
     * externalId有值 打包单已提交审批
     * <p>
     * status：A、T：审批状态已通过/无需审批
     *
     * @return
     */
    protected boolean canApprovalFlow(SearchTripDetailResponseType searchTripDetailResponseType) {
        if (searchTripDetailResponseType == null) {
            return false;
        }
        if (CollectionUtil.isEmpty(searchTripDetailResponseType.getApprovalInfoList())
            || searchTripDetailResponseType.getApprovalInfoList().get(0) == null) {
            return false;
        }
        ApprovalInfo approvalInfo = searchTripDetailResponseType.getApprovalInfoList().get(0);
        if (StringUtil.isBlank(approvalInfo.getExternalId())) {
            return false;
        }
        return APPROVAL_PASS.contains(approvalInfo.getStatus());
    }

    /**
     * offline智能延用
     * 行程勾选了智能延用，必须给客户反馈，如果不可沿用，需要给出具体原因
     * 单订单智能延用，特定原因无需提示给客户，直接走后续流程，非特定原因需要提示给客户
     *
     * App、pc智能延用
     * 行程不支持
     * 单订单特定原因无需提示给客户，直接走后续流程，非特定原因需要提示给客户
     * @param queryHotelAuthExtensionResponseType
     * @param cityBaseInfo
     * @param orderCreateToken
     * @param orderCreateRequestType
     * @return
     */
    private Tuple2<Boolean, OrderCreateResponseType> buildOrderCreateResponseTypeByQuery(
        QueryHotelAuthExtensionResponseType queryHotelAuthExtensionResponseType,
        WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo,
        OrderCreateToken orderCreateToken, OrderCreateRequestType orderCreateRequestType,
        WrapperOfAccount.AccountInfo accountInfo) {
        // 智能沿用审批推荐行程号
        String flowTripId = TemplateNumberUtil.getValue(Optional.ofNullable(queryHotelAuthExtensionResponseType)
            .map(QueryHotelAuthExtensionResponseType::getOriginalOrderInfo).map(OriginalOrderInfoType::getTripId)
            .orElse(null)) > 0 ? String.valueOf(queryHotelAuthExtensionResponseType.getOriginalOrderInfo().getTripId()) : null;
        // 智能延用审批推荐订单号
        String originalOrderId = TemplateNumberUtil.getValue(Optional.ofNullable(queryHotelAuthExtensionResponseType)
            .map(QueryHotelAuthExtensionResponseType::getOriginalOrderInfo)
            .map(OriginalOrderInfoType::getOriginalOrderId).orElse(null)) > 0 ?
            String.valueOf(queryHotelAuthExtensionResponseType.getOriginalOrderInfo().getOriginalOrderId()) : null;
        OrderCreateResponseType orderCreateResponseType = new OrderCreateResponseType();
        orderCreateResponseType.setCityInfo(
            Optional.ofNullable(cityBaseInfo).map(CityBaseInfo::getCityInfo).orElse(null));
        boolean continueAuth = Optional.ofNullable(queryHotelAuthExtensionResponseType)
            .map(QueryHotelAuthExtensionResponseType::getContinueAuth).orElse(false);
        // 行程勾选了智能沿用 实际不可沿用时需要给出提示
        if (accountInfo.isPackageEnabled() && BooleanUtil.parseStr(true).equalsIgnoreCase(
            Optional.ofNullable(orderCreateRequestType.getFollowApprovalInfoInput())
                .map(FollowApprovalInfoInput::getAiFollow).orElse(null)) && (StringUtil.isBlank(flowTripId)
            || !continueAuth)) {
            // 提示不可沿用 无智能推荐行程号简单提示 有智能推荐行程号提示当前匹配不上的具体原因
            FollowApprovalInfo followApprovalInfo = buildNoFollowApprovalInfo(flowTripId, originalOrderId,
                queryHotelAuthExtensionResponseType.getReasonList());
            orderCreateToken.addContinueTypes(ContinueTypeConst.FOLLOW_APPROVAL);
            orderCreateToken.setFollowApprovalResult(buildNoFollowApprovalResult(flowTripId, originalOrderId));
            orderCreateResponseType.setOrderCreateToken(
                TokenParseUtil.generateToken(orderCreateToken, OrderCreateToken.class));
            orderCreateResponseType.setFollowApprovalInfo(followApprovalInfo);
            return Tuple2.of(true, orderCreateResponseType);
        }
        // 单订单智能不可延用时特定原因无需提示给客户，直接走后续流程，非特定原因需要提示给客户
        if (buildNeedFollowFailRemindSingle(queryHotelAuthExtensionResponseType.getReasonList(), continueAuth)) {
            // 提示不可沿用 无智能推荐行程号简单提示 有智能推荐行程号提示当前匹配不上的具体原因
            FollowApprovalInfo followApprovalInfo = buildNoFollowApprovalInfo(flowTripId, originalOrderId,
                queryHotelAuthExtensionResponseType.getReasonList());
            orderCreateToken.addContinueTypes(ContinueTypeConst.FOLLOW_APPROVAL);
            orderCreateToken.setFollowApprovalResult(buildNoFollowApprovalResult(flowTripId, originalOrderId));
            orderCreateResponseType.setOrderCreateToken(
                TokenParseUtil.generateToken(orderCreateToken, OrderCreateToken.class));
            orderCreateResponseType.setFollowApprovalInfo(followApprovalInfo);
            return Tuple2.of(true, orderCreateResponseType);
        }
        if (!BooleanUtils.isTrue(queryHotelAuthExtensionResponseType.getContinueAuth())) {
            // 无审批延用执行后续流程
            return Tuple2.of(false, null);
        }
        if (Optional.ofNullable(queryHotelAuthExtensionResponseType.getOriginalOrderInfo())
            .map(OriginalOrderInfoType::getOriginalOrderId).orElse(0L) <= 0) {
            return Tuple2.of(false, null);
        }
        orderCreateToken.setFollowApprovalResult(buildCanFollowApprovalResult(flowTripId, originalOrderId));
        orderCreateToken.addContinueTypes(ContinueTypeConst.FOLLOW_APPROVAL);
        orderCreateResponseType.setOrderCreateToken(TokenParseUtil.generateToken(buildOrderCreateToken(true, flowTripId,
            String.valueOf(queryHotelAuthExtensionResponseType.getOriginalOrderInfo().getOriginalOrderId()),
            orderCreateToken), OrderCreateToken.class));
        orderCreateResponseType.setFollowApprovalInfo(buildCanFollowApprovalInfo(flowTripId, originalOrderId));
        return Tuple2.of(true, orderCreateResponseType);
    }

    /**
     * 单订单智能不可延用时是否需要提示
     *
     * @param reasonList
     * @return
     */
    private boolean buildNeedFollowFailRemindSingle(List<String> reasonList, Boolean continueAuth) {
        if (BooleanUtil.isTrue(continueAuth)) {
            return false;
        }
        if (CollectionUtil.isEmpty(reasonList)) {
            return false;
        }
        if (reasonList.stream().anyMatch(QconfigOfInitConfig::cannotFlowAndNoNeedEndReasonCodes)) {
            return false;
        }
        return true;
    }

    private CityInfo buildCityInfo(OrderCreateRequestType orderCreateRequestType,
        GetCityBaseInfoResponseType getCityBaseInfoResponseType) {
        if (CollectionUtil.isEmpty(
            Optional.ofNullable(getCityBaseInfoResponseType).map(GetCityBaseInfoResponseType::getCityBaseInfo)
                .orElse(null))) {
            return null;
        }
        CityBaseInfoEntity cityBaseInfoEntity =
            getCityBaseInfoResponseType.getCityBaseInfo().stream().filter(Objects::nonNull)
                .filter(cityinfo -> Objects.equals(cityinfo.getCityId(),
                    orderCreateRequestType.getCityInput().getCityId()))
                .collect(Collectors.toList()).stream().findFirst().orElse(null);
        if (cityBaseInfoEntity == null) {
            return null;
        }
        CityInfo cityInfo = new CityInfo();
        cityInfo.setLocaleCityName(cityBaseInfoEntity.getCityName());
        return cityInfo;
    }

    private Tuple2<Boolean, OrderCreateResponseType> buildOrderCreateResponseTypeByCheck(
        CheckHotelAuthExtensionResponseType checkHotelAuthExtensionResponseType,
        OrderCreateRequestType orderCreateRequestType, WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo,
        OrderCreateToken orderCreateToken, WrapperOfAccount.AccountInfo accountInfo) {
        OrderCreateResponseType orderCreateResponseType = new OrderCreateResponseType();
        boolean canFollow = BooleanUtils.isTrue(checkHotelAuthExtensionResponseType.getContinueAuth());
        // 延用行程号
        String tripId = TemplateNumberUtil.isNotZeroAndNull(
            Optional.ofNullable(checkHotelAuthExtensionResponseType.getTripId()).orElse(null)) ?
            TemplateNumberUtil.toString(checkHotelAuthExtensionResponseType.getTripId()) : null;
        // 延用订单号
        String followOrderId = TemplateNumberUtil.isNotZeroAndNull(
            Optional.ofNullable(checkHotelAuthExtensionResponseType.getOrderId()).orElse(null)) ?
            TemplateNumberUtil.toString(checkHotelAuthExtensionResponseType.getOrderId()) : null;
        // 重新预订原单号
        String followOrderIdInput = Optional.ofNullable(orderCreateRequestType.getFollowApprovalInfoInput())
            .map(FollowApprovalInfoInput::getFollowOrderId).orElse(null);
        orderCreateToken.addContinueTypes(ContinueTypeConst.FOLLOW_APPROVAL);
        if (canFollow) {
            // 校验可延用
            orderCreateResponseType.setFollowApprovalInfo(buildCanFollowApprovalInfo(tripId, followOrderId));
            orderCreateToken.setFollowApprovalResult(buildCanFollowApprovalResult(tripId, followOrderId));
        } else {
            // 校验不可延用
            orderCreateResponseType.setFollowApprovalInfo(buildNoFollowApprovalInfo(tripId, followOrderIdInput,
                checkHotelAuthExtensionResponseType.getReasonList()));
            orderCreateToken.setFollowApprovalResult(buildNoFollowApprovalResult(tripId, followOrderIdInput));
        }
        // 重新预订校验沿用时如果客户选择不沿用 行程需要弹行程选择列表 非行程才可以继续预订
        orderCreateResponseType.getFollowApprovalInfo()
            .setNeedTripModule(buildNeedTripModule(orderCreateRequestType.getTripInput(), accountInfo));
        orderCreateResponseType.setOrderCreateToken(TokenParseUtil.generateToken(
            buildOrderCreateToken(canFollow, tripId, followOrderIdInput, orderCreateToken),OrderCreateToken.class));
        orderCreateResponseType.setCityInfo(
            Optional.ofNullable(cityBaseInfo).map(CityBaseInfo::getCityInfo).orElse(null));
        return Tuple2.of(true, orderCreateResponseType);
    }

    /**
     * 为选择行程号时需要加载行程选择列表
     * 因私前置判断不会进入该流程 无需判断
     *
     * @param tripInput
     * @return
     */
    private String buildNeedTripModule(TripInput tripInput, WrapperOfAccount.AccountInfo accountInfo) {
        if (!accountInfo.isPackageEnabled()) {
            return BooleanUtil.parseStr(false);
        }
        if (StringUtil.isNotBlank(Optional.ofNullable(tripInput).map(TripInput::getTripId).orElse(null))) {
            return BooleanUtil.parseStr(false);
        }
        return BooleanUtil.parseStr(true);
    }

    private OrderCreateToken buildOrderCreateToken(boolean canFollow, String tripId, String followOrderNo,
        OrderCreateToken orderCreateToken) {
        FollowApprovalResult followApprovalResult = new FollowApprovalResult();
        followApprovalResult.setCanFollowApproval(canFollow ? FOLLOW_APPROVAL_RESULT_T : FOLLOW_APPROVAL_RESULT_F);
        if (canFollow) {
            followApprovalResult.setFollowOrderNo(followOrderNo);
            followApprovalResult.setTripId(tripId);
        }
        orderCreateToken.setFollowApprovalResult(followApprovalResult);
        return orderCreateToken;
    }

    private List<ReasonInfo> buildReasonInfos(List<String> reasonList) {
        List<ReasonInfo> reasonInfos = new ArrayList<>();
        if (CollectionUtil.isEmpty(reasonList)) {
            return reasonInfos;
        }
        if (CollectionUtil.isEmpty(reasonList)) {
            return reasonInfos;
        }
        reasonList.forEach(reason -> {
            ReasonInfo reasonInfo = new ReasonInfo();
            reasonInfo.setReasonType(reason);
            reasonInfo.setReasonDesc(BFFSharkUtil.getSharkValue(StringUtil.indexedFormat(FAIL_FOLLOW_REASON, reason)));
            reasonInfos.add(reasonInfo);
        });
        return reasonInfos;
    }
}
