package com.ctrip.corp.bff.hotel.book.handler.corpuserinfoservice4j;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.corpUserInfoService.CorpUserInfoService4jClient;
import corp.user.service.corpUserInfoService.GetPlatformRelationByUidRequestType;
import corp.user.service.corpUserInfoService.GetPlatformRelationByUidResponseType;
import org.springframework.stereotype.Component;

/**
 * @Author: chenchuang
 * @Date: 2024/8/22 16:26
 * @Description:查询散客uid或者商旅uid
 */
@Component
public class HandlerOfGetPlatformRelationByUid extends AbstractHandlerOfSOA<GetPlatformRelationByUidRequestType,
        GetPlatformRelationByUidResponseType, CorpUserInfoService4jClient> {

    @Override
    protected String getMethodName() {
        return "getPlatformRelationByUid";
    }
}
