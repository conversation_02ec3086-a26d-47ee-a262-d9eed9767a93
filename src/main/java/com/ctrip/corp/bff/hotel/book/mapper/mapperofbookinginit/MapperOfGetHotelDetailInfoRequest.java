package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.common.enums.BookingInitErrorEnum;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.foundation.common.util.CalendarUtils;
import com.ctrip.corp.foundation.common.util.Null;
import com.ctrip.corp.hotel.book.query.entity.GetHotelDetailInfoRequestType;
import com.ctrip.corp.hotel.book.query.entity.QueryBaseEntity;
import com.ctrip.corp.hotel.book.query.entity.ReturnDataTypeEnum;
import com.ctrip.corp.hotel.book.query.entity.UserInfoEntity;
import com.ctrip.framework.foundation.Foundation;
import org.springframework.stereotype.Component;

import java.util.Calendar;
import java.util.Collections;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/5/8
 */
@Component
public class MapperOfGetHotelDetailInfoRequest extends AbstractMapper<Tuple2<BookingInitRequestType, ResourceToken>, GetHotelDetailInfoRequestType> {

    private final static int ONLYLONGRENTAL = 30;

    private static final String API_NAME = "bookingInit";
    @Override
    protected GetHotelDetailInfoRequestType convert(Tuple2<BookingInitRequestType, ResourceToken> tuple) {
        IntegrationSoaRequestType integrationSoaRequestType = Optional.ofNullable(tuple)
            .map(Tuple2::getT1)
            .map(BookingInitRequestType::getIntegrationSoaRequestType)
            .orElse(new IntegrationSoaRequestType());
        HotelDateRangeInfo hotelDateRangeInfo = Optional.ofNullable(tuple)
            .map(Tuple2::getT1)
            .map(BookingInitRequestType::getHotelBookInput)
            .map(HotelBookInput::getHotelDateRangeInfo)
            .orElse(new HotelDateRangeInfo());
        Integer hotelId = Optional.ofNullable(tuple)
            .map(Tuple2::getT2)
            .map(ResourceToken::getHotelResourceToken)
            .map(HotelResourceToken::getMasterHotelId)
            .orElse(null);
        String checkInStr = hotelDateRangeInfo.getCheckIn();
        String checkOutStr = hotelDateRangeInfo.getCheckOut();
        GetHotelDetailInfoRequestType res = new GetHotelDetailInfoRequestType();
        res.setLocalCheckInDate(checkInStr);
        res.setLocalCheckOutDate(checkOutStr);
        res.setMasterHotelId(hotelId);
        res.setBaseInfo(buildBaseInfo(integrationSoaRequestType, API_NAME));
        res.setLongRental(CalendarUtils.getTimeDiff(CalendarUtils.parse(checkOutStr, CalendarUtils.YYYY_MM_DD),
                CalendarUtils.parse(checkInStr, CalendarUtils.YYYY_MM_DD), Calendar.DAY_OF_MONTH) > ONLYLONGRENTAL);
        res.setReturnDataTypes(Collections.singletonList(ReturnDataTypeEnum.HOTEL_COMMON));
        return res;
    }
    public static QueryBaseEntity buildBaseInfo(IntegrationSoaRequestType integrationSoaRequestType, String apiName) {
        if (integrationSoaRequestType == null) {
            return null;
        }
        QueryBaseEntity res = new QueryBaseEntity();
        res.setLocale(integrationSoaRequestType.getLanguage());
        res.setTraceId(integrationSoaRequestType.getRequestId());
        res.setRequestFrom(apiName);
        res.setUserInfo(buildUserInfo(integrationSoaRequestType.getUserInfo()));
        res.setOriginClientAppId(Foundation.app().getAppId());
        res.setBookingChannel(Null.or(integrationSoaRequestType.getSourceFrom(), SourceFrom.H5).name());
        return res;
    }
    protected static UserInfoEntity buildUserInfo(UserInfo userInfo) {
        if (userInfo == null) {
            return null;
        }
        UserInfoEntity res = new UserInfoEntity();
        res.setUid(userInfo.getUserId());
        res.setCorpId(userInfo.getCorpId());
        return res;
    }
    @Override
    protected ParamCheckResult check(Tuple2<BookingInitRequestType, ResourceToken> tuple) {
        IntegrationSoaRequestType integrationSoaRequestType = Optional.ofNullable(tuple)
            .map(Tuple2::getT1)
            .map(BookingInitRequestType::getIntegrationSoaRequestType)
            .orElse(null);
        HotelDateRangeInfo hotelDateRangeInfo = Optional.ofNullable(tuple)
            .map(Tuple2::getT1)
            .map(BookingInitRequestType::getHotelBookInput)
            .map(HotelBookInput::getHotelDateRangeInfo)
            .orElse(null);
        Integer hotelId = Optional.ofNullable(tuple)
            .map(Tuple2::getT2)
            .map(ResourceToken::getHotelResourceToken)
            .map(HotelResourceToken::getMasterHotelId)
            .orElse(null);
        if (integrationSoaRequestType == null || hotelDateRangeInfo == null || hotelId == null) {
            return new ParamCheckResult(false, BookingInitErrorEnum.HOTEL_DETAIL_REQUEST_MAPPER_PARAM_CHECK_ERROR, "");
        }
        return null;
    }
}
