package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.agg.hotel.expense.contract.model.ChargeAmountInfoType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookingRulesType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.CancelPolicyType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckAvailResponseType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.GuaranteeDetailType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelRatePlan;
import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelGeoInfoResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.utils.ResourceTokenUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ResourceTokenInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.hotel.book.common.enums.BookingInitErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.GuaranteeTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.BookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.foundation.common.plus.time.HotelTimeZoneUtil;
import com.ctrip.corp.hotel.book.query.entity.CityBaseInfoEntity;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType;
import com.ctrip.corp.hotel.book.query.entity.TimeZoneType;
import com.ctrip.corp.hotelbook.commonws.entity.CancelDeductDetailType;
import com.ctrip.corp.hotelbook.commonws.entity.DepositPolicyType;
import com.ctrip.corp.hotelbook.commonws.entity.GuaranteePolicyType;
import com.ctrip.corp.hotelbook.commonws.entity.GuaranteePriceType;
import com.ctrip.corp.hotelbook.commonws.entity.PriceType;
import com.ctrip.corp.hotelbook.commonws.entity.RequestBaseInfoType;
import com.ctrip.corp.hotelbook.commonws.entity.RoomInfoType;
import com.ctrip.corp.hotelbook.commonws.entity.UserInfoType;
import com.ctrip.model.CalculateServiceChargeV2ResponseType;
import com.ctrip.model.GetCancelPolicyDescRequestType;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ctrip.corp.agg.hotel.roomavailable.entity.BalanceTypes.FG;

/**
 * <AUTHOR>
 * @Description 取消政策描述
 * @Date 2024/8/22 10:03
 * @Version 1.0
 */
@Component
public class MapperOfGetCancelPolicyDescRequest extends
    AbstractMapper<Tuple5<BookingInitRequestType, WrapperOfCheckAvail.CheckAvailInfo, CalculateServiceChargeV2ResponseType,
        HotelPayTypeEnum, HotelPayTypeEnum>, GetCancelPolicyDescRequestType> {

    public static final int CANCEL_SEQUENCE_ID = 0;
    private static final String GUARANTEETYPE_NONE = "NONE";
    private static final String CANCELTYPE_FREE = "FREE";

    @Override
    protected GetCancelPolicyDescRequestType convert(Tuple5<BookingInitRequestType, WrapperOfCheckAvail.CheckAvailInfo,
        CalculateServiceChargeV2ResponseType, HotelPayTypeEnum, HotelPayTypeEnum> tuple) {
        BookingInitRequestType bookingInitRequestType = tuple.getT1();
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = tuple.getT2();
        CalculateServiceChargeV2ResponseType calculateServiceChargeV2ResponseType = tuple.getT3();
        HotelPayTypeEnum roomPayType = tuple.getT4();
        HotelPayTypeEnum servicePayType = tuple.getT5();
        GetCancelPolicyDescRequestType result = new GetCancelPolicyDescRequestType();

        ResourceToken resourceToken = ResourceTokenUtil.tryParseToken(Optional.ofNullable(bookingInitRequestType
                .getResourceTokenInfo())
            .map(ResourceTokenInfo::getResourceToken)
            .orElse(null));
        Integer cityId = Optional.ofNullable(resourceToken)
            .map(ResourceToken::getHotelResourceToken)
            .map(HotelResourceToken::getHotelGeoInfoResourceToken)
            .map(HotelGeoInfoResourceToken::getCityId)
            .orElse(null);
        if (cityId != null) {
            result.setHotelTimeZoneId(HotelTimeZoneUtil.getInstance().tryGetTimeZoneId(cityId));
            result.setUserOffsetMinutes(Optional.ofNullable(bookingInitRequestType.getIntegrationSoaRequestType().getTimezoneOffsetMinutesNew())
                .map(Integer::valueOf).orElse(null));
        }

        String balanceType = Optional.ofNullable(resourceToken).map(ResourceToken::getRoomResourceToken)
            .map(RoomResourceToken::getBalanceType).orElse(null);
        List<RoomInfoType> roomInfoList = getRoomInfoList(checkAvailInfo,
            calculateServiceChargeV2ResponseType,
            StringUtil.equalsIgnoreCase(FG.toString(), balanceType),
            roomPayType,
            servicePayType);
        result.setRoomInfoList(roomInfoList);
        result.setScene("BOOK");

        RequestBaseInfoType baseInfoType = new RequestBaseInfoType();
        IntegrationSoaRequestType integrationSoaRequestType = bookingInitRequestType.getIntegrationSoaRequestType();
        if (integrationSoaRequestType != null) {
            baseInfoType.setBookingChannel(getChannel(integrationSoaRequestType.getSourceFrom()));
            baseInfoType.setRequestFrom(getChannel(integrationSoaRequestType.getSourceFrom()));
            baseInfoType.setLocale(integrationSoaRequestType.getLanguage());
            baseInfoType.setTraceId(integrationSoaRequestType.getRequestId());

            UserInfo userInfo = integrationSoaRequestType.getUserInfo();
            if (userInfo != null) {
                UserInfoType userInfoType = new UserInfoType();
                userInfoType.setUid(userInfo.getUserId());
                userInfoType.setCorpId(userInfo.getCorpId());
                baseInfoType.setUserInfo(userInfoType);
            }
        }
        result.setRequestBaseInfo(baseInfoType);

        return result;
    }

    @Override
    protected ParamCheckResult check(Tuple5<BookingInitRequestType, WrapperOfCheckAvail.CheckAvailInfo,
        CalculateServiceChargeV2ResponseType, HotelPayTypeEnum, HotelPayTypeEnum> tuple) {
        return null;
    }

    public List<RoomInfoType> getRoomInfoList(
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo,
        CalculateServiceChargeV2ResponseType calculateServiceChargeV2ResponseType,
        Boolean isFG,
        HotelPayTypeEnum roomPayType,
        HotelPayTypeEnum servicePayType) {
        List<RoomInfoType> cancelPolicyRoomInfoList = new ArrayList<>();
        // 取消政策
        RoomInfoType roomInfoType = new RoomInfoType();

        roomInfoType.setSequenceId(CANCEL_SEQUENCE_ID);
        roomInfoType.setReservationToken(checkAvailInfo.getReservationToken());
        ChargeAmountInfoType chargeAmountInfoType =
            BookingInitUtil.getChargeAmountInfoType(calculateServiceChargeV2ResponseType, servicePayType,
                roomPayType);
        roomInfoType.setHasServiceCharge(chargeAmountInfoType != null);
        CancelPolicyType cancelPolicyInfo = Optional.ofNullable(checkAvailInfo.getBookingRules())
            .map(BookingRulesType::getCancelPolicyInfo).orElse(null);

        roomInfoType.setCancelPolicyInfo(buildCancelPolicyType(cancelPolicyInfo, isFG));
        cancelPolicyRoomInfoList.add(roomInfoType);
        return cancelPolicyRoomInfoList;
    }

    private com.ctrip.corp.hotelbook.commonws.entity.CancelPolicyType buildCancelPolicyType(
        CancelPolicyType cancelPolicyType, boolean isFG) {
        if (cancelPolicyType == null) {
            return null;
        }
        com.ctrip.corp.hotelbook.commonws.entity.CancelPolicyType policyType =
            new com.ctrip.corp.hotelbook.commonws.entity.CancelPolicyType();
        policyType.setLastCancelTimeUTC(cancelPolicyType.getLastCancelTimeUTC());
        policyType.setFreeCancelPolicySceneType(cancelPolicyType.getFreeCancelPolicySceneType());
        policyType.setGuaranteePolicyInfo(buildGuaranteePolicyInfo(
            cancelPolicyType.getGuaranteePolicyInfo(),
            cancelPolicyType.getCancelType(), isFG));
        policyType.setDepositPolicyList(buildDepositPolicyList(cancelPolicyType.getDepositPolicyList()));
        policyType.setXFreeCancelMinutes(cancelPolicyType.getXFreeCancelMinutes());
        policyType.setCancelType(cancelPolicyType.getCancelType());
        return policyType;
    }

    private GuaranteePolicyType buildGuaranteePolicyInfo(GuaranteeDetailType guaranteePolicyInfo,
        String cancelType, boolean isFG) {
        GuaranteePolicyType result = new GuaranteePolicyType();
        if (guaranteePolicyInfo == null) {
            result.setGuaranteeType(GUARANTEETYPE_NONE);
            return result;
        }
        result.setGuaranteeType(GuaranteeTypeEnum.getGuaranteeType(
            guaranteePolicyInfo.getGuaranteeType(), CANCELTYPE_FREE.equalsIgnoreCase(cancelType), isFG));
        result.setGuaranteePriceInfo(buildGuaranteePriceInfo(guaranteePolicyInfo.getGuaranteePriceInfo()));

        if (CollectionUtil.isNotEmpty(guaranteePolicyInfo.getCancelDeductDetailInfo())) {
            result.setCancelDeductDetailList(guaranteePolicyInfo.getCancelDeductDetailInfo().stream()
                .map(cancelDeductPolicyType -> {
                    CancelDeductDetailType item = new CancelDeductDetailType();
                    item.setDeductionStartTimeUTC(cancelDeductPolicyType.getDeductionStartTimeUTC());
                    item.setDeductionEndTimeUTC(cancelDeductPolicyType.getDeductionEndTimeUTC());
                    item.setDeductionType(cancelDeductPolicyType.getDeductionType());
                    item.setDeductionRatio(cancelDeductPolicyType.getDeductionRatio());
                    item.setCustomDeductionPrice(toPriceType(cancelDeductPolicyType.getCustomDeductionPrice()));
                    item.setOriginDeductionPrice(toPriceType(cancelDeductPolicyType.getOriginDeductionPrice()));
                    return item;
                }).collect(Collectors.toList()));
        }

        return result;
    }

    private GuaranteePriceType buildGuaranteePriceInfo(com.ctrip.corp.agg.hotel.roomavailable.entity.GuaranteePriceType guaranteePriceInfo) {
        if (guaranteePriceInfo == null) {
            return null;
        }
        GuaranteePriceType result = new GuaranteePriceType();
        result.setOriginGuaranteePrice(toPriceType(guaranteePriceInfo.getOriginGuaranteePrice()));
        result.setCustomGuaranteePrice(toPriceType(guaranteePriceInfo.getCustomGuaranteePrice()));
        return result;
    }

    private PriceType toPriceType(com.ctrip.corp.agg.hotel.roomavailable.entity.PriceType priceEntity) {
        if (priceEntity == null) {
            return null;
        }
        PriceType priceType = new PriceType();
        priceType.setPrice(priceEntity.getPrice());
        priceType.setCurrency(priceEntity.getCurrency());
        return priceType;
    }

    private List<DepositPolicyType> buildDepositPolicyList(List<com.ctrip.corp.agg.hotel.roomavailable.entity.DepositPolicyType> depositPolicyList) {
        if (CollectionUtil.isEmpty(depositPolicyList)) {
            return null;
        }
        List<DepositPolicyType> result = new ArrayList<>();
        depositPolicyList.forEach(depositPolicyType -> {
            DepositPolicyType item = new DepositPolicyType();
            item.setDepositTimeUTC(depositPolicyType.getDepositTimeUTC());
            item.setCustomDepositPrice(convertPriceType(depositPolicyType.getCustomDepositPrice()));
            item.setOriginDepositPrice(convertPriceType(depositPolicyType.getOriginDepositPrice()));
            result.add(item);
        });

        return result;
    }

    private PriceType convertPriceType(com.ctrip.corp.agg.hotel.roomavailable.entity.PriceType checkAvailPriceType) {
        if (checkAvailPriceType == null) {
            return null;
        }
        PriceType priceType = new PriceType();
        priceType.setCurrency(checkAvailPriceType.getCurrency());
        priceType.setPrice(checkAvailPriceType.getPrice());
        return priceType;
    }

    private String getChannel(SourceFrom sourceFrom) {
        if (sourceFrom == null) {
            return "APP";
        }
        if (sourceFrom == SourceFrom.Online) {
            return "ONLINE";
        }
        if (sourceFrom == SourceFrom.Offline) {
            return "OFFLINE";
        }
        return "APP";
    }
}
