package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.agg.hotel.roomavailable.entity.AmountDetailEntity;
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookHotelInfoEntity;
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookRoomInfoEntity;
import com.ctrip.corp.agg.hotel.roomavailable.entity.PriceType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomDiscountInfo;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckItemsResultType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckOverStandardRcInfoType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType;
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextResponseType;
import com.ctrip.corp.agg.hotel.tmc.entity.FinalPolicyType;
import com.ctrip.corp.agg.hotel.tmc.entity.FinalTravelPolicyType;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum;
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.date.DateUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.RoomTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.MapperOfQueryHotelAuthExtensionRequestType;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.soa._20184.ClientInfoType;
import com.ctrip.soa._20184.HotelProductInfoType;
import com.ctrip.soa._20184.TravelControlInfoType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/16 20:15
 */
public class HotelAuthExtensionUtil {
    public static String ORDER_MODE_TRIP = "TR";
    public static String ORDER_MODE_SINGLE = "OR";
    private static final String CHARGE_PAY_NONE = "NONE";
    private static final String CHARGE_PAY_PERSONAL = "PERSONAL_PAY_SERVICE_CHARGE";
    private static final String CHARGE_PAY_CORP = "CORP_PAY_SERVICE_CHARGE";

    public static String getOrderMode(WrapperOfAccount.AccountInfo accountInfo) {
        return accountInfo.isPackageEnabled() ? ORDER_MODE_TRIP : ORDER_MODE_SINGLE;
    }

    public static String getPolicyUid(OrderCreateRequestType orderCreateRequestType) {
        if (StringUtil.isNotBlank(
            Optional.ofNullable(orderCreateRequestType.getHotelPolicyInput()).map(HotelPolicyInput::getPolicyInput)
                .map(PolicyInput::getPolicyUid).orElse(null))) {
            return orderCreateRequestType.getHotelPolicyInput().getPolicyInput().getPolicyUid();
        }
        return orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId();
    }

    public static BigDecimal buildSettlementAmount(
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
        BigDecimal customPromotionAmount = Optional.ofNullable(queryCheckAvailContextResponseType.getRoomInfo())
            .map(BookRoomInfoEntity::getRoomDiscountInfo).map(RoomDiscountInfo::getCustomPromotionAmount)
            .map(PriceType::getPrice).orElse(BigDecimal.ZERO);
        return Optional.ofNullable(queryCheckAvailContextResponseType.getRoomInfo())
            .map(BookRoomInfoEntity::getCustomAmountInfo).map(AmountDetailEntity::getAmount).orElse(BigDecimal.ZERO)
            .subtract(customPromotionAmount);
    }

    public static List<ClientInfoType> buildClientInfos(OrderCreateRequestType orderCreateRequestType,
        WrapperOfCheckAvail.BaseCheckAvailInfo baseCheckAvailInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig, Map<String, StrategyInfo> strategyInfoMap) {
        if (CollectionUtils.isEmpty(orderCreateRequestType.getHotelBookPassengerInputs())) {
            return new ArrayList<>();
        }
        List<ClientInfoType> clientList = new ArrayList<>();
        orderCreateRequestType.getHotelBookPassengerInputs().forEach(hotelPassengerInfo -> {
            if (hotelPassengerInfo == null || hotelPassengerInfo.getHotelPassengerInput() == null) {
                return;
            }
            ClientInfoType clientInfo = new ClientInfoType();
            clientInfo.setName(OrderCreateProcessorOfUtil.getUseName(hotelPassengerInfo,
                Optional.ofNullable(orderCreateRequestType.getCityInput()).map(CityInput::getCityId).orElse(0),
                baseCheckAvailInfo, qconfigOfCertificateInitConfig, strategyInfoMap));
            if ("T".equalsIgnoreCase(hotelPassengerInfo.getHotelPassengerInput().getEmployee())) {
                clientInfo.setUid(hotelPassengerInfo.getHotelPassengerInput().getUid());
            }
            clientList.add(clientInfo);
        });
        return clientList;
    }

    public static HotelProductInfoType buildHotelProductInfo(OrderCreateRequestType orderCreateRequestType,
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType,
        WrapperOfCheckAvail.BaseCheckAvailInfo baseCheckAvailInfo) {
        HotelProductInfoType hotelProductInfo = new HotelProductInfoType();
        hotelProductInfo.setRoomQuantity(orderCreateRequestType.getHotelBookInput().getRoomQuantity());
        hotelProductInfo.setStar(Optional.ofNullable(queryCheckAvailContextResponseType)
            .map(QueryCheckAvailContextResponseType::getHotelInfo).map(BookHotelInfoEntity::getStar).orElse(0));
        hotelProductInfo.setCityId(orderCreateRequestType.getCityInput().getCityId());
        hotelProductInfo.setMasterHotelId(Optional.ofNullable(queryCheckAvailContextResponseType)
            .map(QueryCheckAvailContextResponseType::getHotelInfo).map(BookHotelInfoEntity::getMasterHotelId)
            .orElse(0));
        hotelProductInfo.setHotelId(Optional.ofNullable(queryCheckAvailContextResponseType)
            .map(QueryCheckAvailContextResponseType::getHotelInfo).map(BookHotelInfoEntity::getSubHotelId).orElse(0));
        hotelProductInfo.setHotelType(
            Optional.ofNullable(baseCheckAvailInfo.getRoomTypeEnum()).orElse(RoomTypeEnum.M).getValue());
        // todo:钟点房延用了非钟点房的单子的审批 是否合理？？？
        try {
            hotelProductInfo.setCheckInTime(
                DateUtil.dateFormat(orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo().getCheckIn(),
                    DateUtil.YYYY_MM_DD, DateUtil.YYYY_MM_DD_HH_mm_ss));
            hotelProductInfo.setCheckOutTime(
                DateUtil.dateFormat(orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo().getCheckOut(),
                    DateUtil.YYYY_MM_DD, DateUtil.YYYY_MM_DD_HH_mm_ss));
        } catch (Exception e) {
            LogUtil.loggingClogOnly(LogLevelEnum.Error, MapperOfQueryHotelAuthExtensionRequestType.class,
                "getHotelProductInfo", "failed to parse checkIn or checkOut!", null);
        }
        return hotelProductInfo;
    }

    public static TravelControlInfoType buildTravelControlInfo(
        GetTravelPolicyContextResponseType getTravelPolicyContextResponseType,
        CheckTravelPolicyResponseType checkTravelPolicyResponseType, OrderCreateRequestType orderCreateRequestType, ResourceToken resourceToken) {
        FinalTravelPolicyType finalTravelPolicyType = Optional.ofNullable(getTravelPolicyContextResponseType)
            .map(GetTravelPolicyContextResponseType::getFinalPolicy).map(FinalPolicyType::getFinalTravelPolicy)
            .orElse(new FinalTravelPolicyType());
        TravelControlInfoType travelControlInfo = new TravelControlInfoType();
        travelControlInfo.setPolicyHighPrice(
            Optional.ofNullable(finalTravelPolicyType.getMaxSettlementPrice()).orElse(BigDecimal.ZERO));
        travelControlInfo.setPolicyHighStar(Optional.ofNullable(finalTravelPolicyType.getMaxStar()).orElse(0));
        travelControlInfo.setPolicyLowPrice(
            Optional.ofNullable(finalTravelPolicyType.getMinSettlementPrice()).orElse(BigDecimal.ZERO));
        travelControlInfo.setPolicyLowStar(Optional.ofNullable(finalTravelPolicyType.getMinStar()).orElse(0));
        travelControlInfo.setPriceMatchPolicy(
            buildPriceMatchPolicy(checkTravelPolicyResponseType, orderCreateRequestType.getHotelPayTypeInput(), resourceToken));
        return travelControlInfo;
    }

    public static boolean buildPriceMatchPolicy(CheckTravelPolicyResponseType checkTravelPolicyResponseType,
        List<HotelPayTypeInput> hotelPayTypeInputList, ResourceToken resourceToken) {
        if (checkTravelPolicyResponseType == null) {
            return false;
        }
        if (Optional.ofNullable(checkTravelPolicyResponseType.getCheckItemsResult())
            .map(CheckItemsResultType::getPriceCheckResult).orElse(null) != null) {
            return BooleanUtils.isTrue(
                checkTravelPolicyResponseType.getCheckItemsResult().getPriceCheckResult().isInControl());
        }
        if (checkTravelPolicyResponseType.getCheckRcResult() == null) {
            return false;
        }
        CheckOverStandardRcInfoType checkOverStandardRcInfoType =
            OrderCreateProcessorOfUtil.getCheckOverStandardRcInfoType(checkTravelPolicyResponseType,
                hotelPayTypeInputList, resourceToken);
        if (checkOverStandardRcInfoType == null) {
            return false;
        }
        return BooleanUtils.isTrue(checkOverStandardRcInfoType.isPriceInControl());
    }

}
