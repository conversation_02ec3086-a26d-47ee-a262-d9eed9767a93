package com.ctrip.corp.bff.hotel.book.common.wrapper;

import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryResponseType;
import com.ctrip.corp.bff.specific.contract.ApprovalTextInfoResponseType;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType;
import corp.user.service.costcenterService.matchCostCenter.MatchCostCenterResponseType;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/12/27 9:19
 */
public class WrapperOfSaveCostCenter {
    private OrderCreateRequestType orderCreateRequestType;
    private WrapperOfAccount.AccountInfo accountInfo;
    private WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo;
    private OrderCreateToken orderCreateToken;
    private CreateOrderResponseType createOrderResponseType;
    private MatchCostCenterResponseType matchCostCenterResponseType;
    private ApprovalTextInfoResponseType approvalTextInfoResponseType;
    private SSOInfoQueryResponseType ssoInfoQueryResponseType;
    private ResourceToken resourceToken;
    private QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig;
    Map<String, StrategyInfo> strategyInfoMap;

    public Map<String, StrategyInfo> getStrategyInfoMap() {
        return strategyInfoMap;
    }

    public OrderCreateRequestType getOrderCreateRequestType() {
        return orderCreateRequestType;
    }

    public WrapperOfAccount.AccountInfo getAccountInfo() {
        return accountInfo;
    }

    public WrapperOfCheckAvail.BaseCheckAvailInfo getCheckAvailInfo() {
        return checkAvailInfo;
    }

    public OrderCreateToken getOrderCreateToken() {
        return orderCreateToken;
    }

    public CreateOrderResponseType getCreateOrderResponseType() {
        return createOrderResponseType;
    }

    public MatchCostCenterResponseType getMatchCostCenterResponseType() {
        return matchCostCenterResponseType;
    }

    public ApprovalTextInfoResponseType getApprovalTextInfoResponseType() {
        return approvalTextInfoResponseType;
    }

    public SSOInfoQueryResponseType getSsoInfoQueryResponseType() {
        return ssoInfoQueryResponseType;
    }

    public ResourceToken getResourceToken() {
        return resourceToken;
    }

    public QconfigOfCertificateInitConfig getQconfigOfCertificateInitConfig() {
        return qconfigOfCertificateInitConfig;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private OrderCreateRequestType orderCreateRequestType;
        private WrapperOfAccount.AccountInfo accountInfo;
        private WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo;
        private OrderCreateToken orderCreateToken;
        private CreateOrderResponseType createOrderResponseType;
        private MatchCostCenterResponseType matchCostCenterResponseType;
        private ApprovalTextInfoResponseType approvalTextInfoResponseType;
        private SSOInfoQueryResponseType ssoInfoQueryResponseType;
        private ResourceToken resourceToken;

        private QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig;
        private Map<String, StrategyInfo> strategyInfoMap;

        public Builder setStrategyInfoMap(Map<String, StrategyInfo> strategyInfoMap) {
            this.strategyInfoMap = strategyInfoMap;
            return this;
        }
        public Builder setOrderCreateRequestType(OrderCreateRequestType orderCreateRequestType) {
            this.orderCreateRequestType = orderCreateRequestType;
            return this;
        }

        public Builder setAccountInfo(WrapperOfAccount.AccountInfo accountInfo) {
            this.accountInfo = accountInfo;
            return this;
        }

        public Builder setCheckAvailInfo(WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
            this.checkAvailInfo = checkAvailInfo;
            return this;
        }

        public Builder setOrderCreateToken(OrderCreateToken orderCreateToken) {
            this.orderCreateToken = orderCreateToken;
            return this;
        }

        public Builder setCreateOrderResponseType(CreateOrderResponseType createOrderResponseType) {
            this.createOrderResponseType = createOrderResponseType;
            return this;
        }

        public Builder setMatchCostCenterResponseType(MatchCostCenterResponseType matchCostCenterResponseType) {
            this.matchCostCenterResponseType = matchCostCenterResponseType;
            return this;
        }

        public Builder setApprovalTextInfoResponseType(ApprovalTextInfoResponseType approvalTextInfoResponseType) {
            this.approvalTextInfoResponseType = approvalTextInfoResponseType;
            return this;
        }

        public Builder setSsoInfoQueryResponseType(SSOInfoQueryResponseType ssoInfoQueryResponseType) {
            this.ssoInfoQueryResponseType = ssoInfoQueryResponseType;
            return this;
        }

        public Builder setResourceToken(ResourceToken resourceToken) {
            this.resourceToken = resourceToken;
            return this;
        }

        public Builder setQconfigOfCertificateInitConfig(QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig) {
            this.qconfigOfCertificateInitConfig = qconfigOfCertificateInitConfig;
            return this;
        }

        public WrapperOfSaveCostCenter build() {
            WrapperOfSaveCostCenter wrapper = new WrapperOfSaveCostCenter();
            wrapper.orderCreateRequestType = this.orderCreateRequestType;
            wrapper.accountInfo = this.accountInfo;
            wrapper.checkAvailInfo = this.checkAvailInfo;
            wrapper.orderCreateToken = this.orderCreateToken;
            wrapper.createOrderResponseType = this.createOrderResponseType;
            wrapper.matchCostCenterResponseType = this.matchCostCenterResponseType;
            wrapper.approvalTextInfoResponseType = this.approvalTextInfoResponseType;
            wrapper.ssoInfoQueryResponseType = this.ssoInfoQueryResponseType;
            wrapper.resourceToken = this.resourceToken;
            wrapper.qconfigOfCertificateInitConfig = this.qconfigOfCertificateInitConfig;
            wrapper.strategyInfoMap = this.strategyInfoMap;
            return wrapper;
        }
    }
}
