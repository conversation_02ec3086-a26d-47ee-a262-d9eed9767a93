package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.agg.hotel.roomavailable.entity.NationalityRestrictionType;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.tools.contract.CountryQueryRequestType;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/10
 */
@Component
public class MapperOfCountryQueryRequestType extends AbstractMapper<Tuple2<IntegrationSoaRequestType, NationalityRestrictionType>, CountryQueryRequestType> {
    @Override
    protected CountryQueryRequestType convert(Tuple2<IntegrationSoaRequestType, NationalityRestrictionType> para) {
        if (para == null) {
            return null;
        }
        IntegrationSoaRequestType integrationSoaRequestType = para.getT1();
        NationalityRestrictionType nationalityRestrictionInfo = para.getT2();
        if (integrationSoaRequestType == null || nationalityRestrictionInfo == null) {
            return null;
        }
        List<String> countryCodeList = buildCountryCodeList(nationalityRestrictionInfo.getBlockCountryCodeList(), nationalityRestrictionInfo.getAllowCountryCodeList());
        if (CollectionUtil.isEmpty(countryCodeList)) {
            return null;
        }
        CountryQueryRequestType res = new CountryQueryRequestType();
        res.setIntegrationSoaRequestType(integrationSoaRequestType);
        res.setCountryCodeList(countryCodeList);
        return res;
    }

    protected List<String> buildCountryCodeList(List<String> blockCountryCodeList, List<String> allowCountryCodeList) {
        List<String> res = new ArrayList<>();
        if (CollectionUtil.isNotEmpty(blockCountryCodeList)) {
            res.addAll(blockCountryCodeList.stream().filter(StringUtil::isNotBlank).toList());
        }
        if (CollectionUtil.isNotEmpty(allowCountryCodeList)) {
            res.addAll(allowCountryCodeList.stream().filter(StringUtil::isNotBlank).toList());
        }
        return res;
    }

    @Override
    protected ParamCheckResult check(Tuple2<IntegrationSoaRequestType, NationalityRestrictionType> integrationSoaRequestTypeNationalityRestrictionInfoTuple2) {
        return null;
    }
}
