package com.ctrip.corp.bff.hotel.book.handler.corphotelbookcommonws;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.hotelbook.commonws.CorpHotelBookCommonWSClient;
import com.ctrip.corp.hotelbook.commonws.entity.DistributePaymentAmountRequestType;
import com.ctrip.corp.hotelbook.commonws.entity.DistributePaymentAmountResponseType;
import com.ctrip.model.CalculateAddPriceAmountDetailRequestType;
import com.ctrip.model.CalculateAddPriceAmountDetailResponseType;
import org.springframework.stereotype.Component;

/**
 * @Author: chenchuang
 * @Date: 2024/8/22 16:26
 * @Description:agg费用详情
 */
@Component
public class HandlerOfDistributePaymentAmount extends AbstractHandlerOfSOA<DistributePaymentAmountRequestType,
        DistributePaymentAmountResponseType, CorpHotelBookCommonWSClient> {

    @Override
    protected String getMethodName() {
        return "distributePaymentAmount";
    }
}
