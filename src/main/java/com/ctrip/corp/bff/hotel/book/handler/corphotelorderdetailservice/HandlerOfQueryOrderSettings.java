package com.ctrip.corp.bff.hotel.book.handler.corphotelorderdetailservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.CorpHotelOrderDetailServiceClient;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.QueryOrderSettingsRequestType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.QueryOrderSettingsResponseType;
import org.springframework.stereotype.Component;

/**
 * @Author: chenchuang
 * @Date: 2024/9/20 16:12
 */
@Component
public class HandlerOfQueryOrderSettings extends AbstractHandlerOfSOA<QueryOrderSettingsRequestType, QueryOrderSettingsResponseType, CorpHotelOrderDetailServiceClient> {
    @Override
    protected String getMethodName() {
        return "queryOrderSettings";
    }
}
