package com.ctrip.corp.bff.hotel.book.handler.corpbffmicebasicauthservice;

import com.ctrip.corp.bff.framework.template.entity.IntegrationResponse;
import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.bff.mice.basic.auth.contract.CorpBffMiceBasicAuthServiceClient;
import com.ctrip.corp.bff.mice.basic.auth.contract.TmsCreateOrderVerifyRequestType;
import com.ctrip.corp.bff.mice.basic.auth.contract.TmsCreateOrderVerifyResponseType;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2024/6/28 22:13
 */
@Component
public class HandlerOfTmsCreateOrderVerify extends
    AbstractHandlerOfSOA<TmsCreateOrderVerifyRequestType, TmsCreateOrderVerifyResponseType, CorpBffMiceBasicAuthServiceClient> {
    @Override protected String getMethodName() {
        return "tmsCreateOrderVerify";
    }

    @Override protected String getLogErrorCode(TmsCreateOrderVerifyResponseType response) {
        return Optional.ofNullable(response).map(TmsCreateOrderVerifyResponseType::getIntegrationResponse).map(IntegrationResponse::getErrorCode).orElse(null);
    }

}
