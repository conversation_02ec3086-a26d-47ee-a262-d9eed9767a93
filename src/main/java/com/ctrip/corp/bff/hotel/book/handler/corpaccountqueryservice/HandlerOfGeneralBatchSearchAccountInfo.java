package com.ctrip.corp.bff.hotel.book.handler.corpaccountqueryservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.CorpAccountQueryService.CorpAccountQueryServiceClient;
import corp.user.service.CorpAccountQueryService.GeneralBatchSearchAccountInfoRequestType;
import corp.user.service.CorpAccountQueryService.GeneralBatchSearchAccountInfoResponseType;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoRequestType;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 批量查询账户信息
 * @Date 2025/5/6 15:38
 * @Version 1.0
 */
@Component
public class HandlerOfGeneralBatchSearchAccountInfo extends
        AbstractHandlerOfSOA<GeneralBatchSearchAccountInfoRequestType, GeneralBatchSearchAccountInfoResponseType, CorpAccountQueryServiceClient> {

    @Override
    protected String getMethodName() {
        return "generalBatchSearchAccountInfo";
    }
}
