package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import com.ctrip.corp.agg.hotel.roomavailable.entity.AddPriceRuleType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookingRulesType;
import com.ctrip.corp.bff.framework.hotel.common.util.HotelDateRangeUtil;
import com.ctrip.corp.bff.framework.hotel.entity.contract.AddPriceInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.foundation.common.util.NumberUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount.AccountInfo;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple6;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail.CheckAvailInfo;
import org.springframework.stereotype.Component;

import com.ctrip.corp.agg.hotel.expense.contract.model.BaseChargeAmount;
import com.ctrip.corp.agg.hotel.expense.contract.model.ServiceChargePriceType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomCouponInfoType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomItem;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfHotelTravelPolicy;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.hotel.book.common.util.BookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.util.CorpHotelBookCommonWSUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodRequestType;
import com.ctrip.corp.hotelbook.commonws.entity.OriOrderInfoType;
import com.ctrip.corp.hotelbook.commonws.entity.PriceType;
import com.ctrip.corp.hotelbook.commonws.entity.ServiceChargeDetailType;
import com.ctrip.corp.hotelbook.commonws.entity.ServiceChargeInfoType;
import com.ctrip.corp.order.data.aggregation.query.contract.CancelInfoType;
import com.ctrip.corp.order.data.aggregation.query.contract.HotelInfoType;
import com.ctrip.corp.order.data.aggregation.query.contract.HotelOrderType;
import com.ctrip.corp.order.data.aggregation.query.contract.OrderGenericInfoType;
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataResponseType;
import com.ctrip.corp.order.data.aggregation.query.contract.ServiceFeeConfigType;
import com.ctrip.model.CalculateServiceChargeV2ResponseType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.QueryOrderSettingsResponseType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.ServiceFeeSettingType;

/**
 * <AUTHOR>
 * @Description 获取支持的支付方式
 * @Date 2024/8/22 10:04
 * @Version 1.0
 */
@Component
public class MapperOfGetSupportedPaymentMethodRequestType extends AbstractMapper<Tuple6<BookingInitRequestType
        , WrapperOfCheckAvail.CheckAvailInfo, WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo
        , WrapperOfAccount.AccountInfo
        , QueryHotelOrderDataResponseType
        , QueryOrderSettingsResponseType>
        , GetSupportedPaymentMethodRequestType> {


    public static final String FILL_ORDER = "FILL_ORDER";

    public static final String ACCOUNT_PAY = "ACCOUNT_PAY";
    public static final String INDIVIDUAL_PAY = "INDIVIDUAL_PAY";

    private static final String CNY = "CNY";
    @Override
    protected GetSupportedPaymentMethodRequestType convert(Tuple6<BookingInitRequestType
                , CheckAvailInfo, HotelTravelPolicyInfo
                , AccountInfo
                , QueryHotelOrderDataResponseType
                , QueryOrderSettingsResponseType> param) {
        BookingInitRequestType bookingInitRequestType = param.getT1();
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = param.getT2();
        WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo hotelTravelPolicyInfo = param.getT3();
        GetSupportedPaymentMethodRequestType request = new GetSupportedPaymentMethodRequestType();
        WrapperOfAccount.AccountInfo accountInfo = param.getT4();
        QueryHotelOrderDataResponseType queryHotelOrderDataResponseType = param.getT5();
        QueryOrderSettingsResponseType queryOrderSettingsResponseType = param.getT6();
        request.setRequestBaseInfo(CorpHotelBookCommonWSUtil.buildRequestBaseInfoTypeWithPolicyUidNotNull(bookingInitRequestType));
        request.setScene(FILL_ORDER);
        request.setCorpPayType(CorpPayInfoUtil.isPublic(bookingInitRequestType.getCorpPayInfo()) ? "C" : "P");
        request.setCheckAvailId(checkAvailInfo.getWsId());
        request.setPolicyToken(Optional.ofNullable(hotelTravelPolicyInfo).map(WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo::getPolicyToken).orElse(null));
        request.setCouponAmount(buildPriceType(Optional.ofNullable(checkAvailInfo.getRoomItem())
                        .map(RoomItem::getRoomCouponInfo)
                        .map(RoomCouponInfoType::getMultiCouponTotalCustomAmount).orElse(null)
                , CNY));
        request.setAddPriceAmount(buildAddPriceAmount(checkAvailInfo.getCustomCurrency(),
                 Optional.ofNullable(bookingInitRequestType).map(BookingInitRequestType::getAddPriceInput).orElse(null),
                Optional.ofNullable(checkAvailInfo.getBookingRules()).map(BookingRulesType::getAddPriceRule).map(AddPriceRuleType::isCanAddPrice).orElse(false),
                bookingInitRequestType.getHotelBookInput()));
        request.setCorpXProductList(CorpHotelBookCommonWSUtil.buildCorpXProductInfoType(bookingInitRequestType));
        request.setServiceChargeInfo(buildServiceChargeInfo(accountInfo, queryHotelOrderDataResponseType, queryOrderSettingsResponseType, bookingInitRequestType));
        return request;
    }

    private PriceType buildAddPriceAmount(String customCurrency, AddPriceInput addPriceInput, Boolean canAddPrice, HotelBookInput hotelBookInput) {
        if (BooleanUtil.isNotTrue(canAddPrice)) {
            return null;
        }
        if (addPriceInput == null || addPriceInput.getAmountInfo() == null || StringUtil.isBlank(addPriceInput.getAmountInfo().getAmount())) {
            return null;
        }
        Integer amount = NumberUtil.parseInt(addPriceInput.getAmountInfo().getAmount());
        if (amount <= 0) {
            return null;
        }
        int roomNights = HotelDateRangeUtil.getRoomNights(hotelBookInput);
        if (roomNights <= 0) {
            return null;
        }
        PriceType priceType = new PriceType();
        priceType.setPrice(BigDecimal.valueOf(amount * roomNights));
        priceType.setCurrency(customCurrency);
        return priceType;
    }


    protected ServiceChargeInfoType buildServiceChargeInfo(WrapperOfAccount.AccountInfo accountInfo
            , QueryHotelOrderDataResponseType queryHotelOrderDataResponseType
            , QueryOrderSettingsResponseType queryOrderSettingsResponseType
            , BookingInitRequestType bookingInitRequestType) {
        ServiceChargeInfoType serviceChargeInfo = new ServiceChargeInfoType();
        serviceChargeInfo.setAmsFeeConfigVersion(buildAmsFeeConfigVersion(accountInfo, queryHotelOrderDataResponseType, bookingInitRequestType));
        serviceChargeInfo.setServiceChargeToken(buildServiceChargeToken(queryOrderSettingsResponseType, bookingInitRequestType));
        serviceChargeInfo.setOperationScenario(buildOperationScenario(bookingInitRequestType));
        serviceChargeInfo.setOriOrderInfo(buildOriOrderInfoType(queryHotelOrderDataResponseType, bookingInitRequestType));
        return serviceChargeInfo;
    }

    private static OriOrderInfoType buildOriOrderInfoType(QueryHotelOrderDataResponseType queryHotelOrderDataResponseType
            , BookingInitRequestType bookingInitRequestType) {
        if (BookingInitUtil.buildModifyServiceFeeOrder(bookingInitRequestType)) {
            OriOrderInfoType oriOrderInfo = new OriOrderInfoType();
            oriOrderInfo.setOrderId(Optional.ofNullable(queryHotelOrderDataResponseType)
                    .map(QueryHotelOrderDataResponseType::getOrderBasicInfo)
                    .map(o -> String.valueOf(o.getOrderId())).orElse(null));
            oriOrderInfo.setLastModifyTimeUTC(Optional.ofNullable(queryHotelOrderDataResponseType).map(QueryHotelOrderDataResponseType::getOrderGenericInfo)
                .map(OrderGenericInfoType::getCancelInfo).map(CancelInfoType::getCancelDeadlineUTC).orElse(null));
            return oriOrderInfo;
        }
        return null;
    }


    private static String buildOperationScenario(BookingInitRequestType bookingInitRequestType) {
        if (BookingInitUtil.buildModifyServiceFeeOrder(bookingInitRequestType)) {
            return "ModifyOrder";
        }
        return "BookingOrder";
    }


    private static String buildServiceChargeToken(QueryOrderSettingsResponseType queryOrderSettingsResponseType, BookingInitRequestType bookingInitRequestType) {
        if (BookingInitUtil.buildModifyServiceFeeOrder(bookingInitRequestType)) {
            return Optional.ofNullable(queryOrderSettingsResponseType)
                    .map(QueryOrderSettingsResponseType::getServiceFeeSetting)
                    .map(ServiceFeeSettingType::getServiceChargeToken).orElse(null);
        }
        return null;
    }


    protected static String buildAmsFeeConfigVersion(WrapperOfAccount.AccountInfo accountInfo,
                                                   QueryHotelOrderDataResponseType queryHotelOrderDataResponseType, BookingInitRequestType bookingInitRequestType) {
        return BookingInitUtil.buildModifyServiceFeeOrder(bookingInitRequestType) ?
                Optional.ofNullable(queryHotelOrderDataResponseType)
                        .map(QueryHotelOrderDataResponseType::getHotelInfo).map(HotelInfoType::getHotelOrder)
                .map(HotelOrderType::getServiceFeeConfig).map(ServiceFeeConfigType::getAmsFeeConfigVersion).orElse(null)
                : accountInfo.getServiceFeeVersion();
    }


    public PriceType buildPriceType(BigDecimal price, String currency) {
        if (price == null) {
            return null;
        }
        PriceType priceType = new PriceType();
        priceType.setPrice(price);
        priceType.setCurrency(currency);

        return priceType;
    }

    @Override
    protected ParamCheckResult check(Tuple6<BookingInitRequestType
            , WrapperOfCheckAvail.CheckAvailInfo, WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo
            , WrapperOfAccount.AccountInfo
            , QueryHotelOrderDataResponseType
            , QueryOrderSettingsResponseType> stringStringTuple2) {
        return null;
    }
}
