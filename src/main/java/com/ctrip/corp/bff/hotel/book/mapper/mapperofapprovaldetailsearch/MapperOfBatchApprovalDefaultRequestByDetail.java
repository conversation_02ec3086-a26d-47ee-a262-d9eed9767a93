package com.ctrip.corp.bff.hotel.book.mapper.mapperofapprovaldetailsearch;

import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.MiceInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.util.ApprovalDetailSearchUtil;
import com.ctrip.corp.bff.hotel.book.contract.ApprovalDetailSearchRequestType;
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryResponseType;
import com.ctrip.corp.bff.specific.contract.BatchApprovalDefaultRequestType;
import com.ctrip.corp.bff.specific.contract.ExtRelationItem;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/6/26 20:37
 */
@Component
public class MapperOfBatchApprovalDefaultRequestByDetail extends AbstractMapper<Tuple3<ApprovalDetailSearchRequestType, WrapperOfAccount.AccountInfo,
        SSOInfoQueryResponseType>, BatchApprovalDefaultRequestType> {
    /**
     * 普通预定
     */
    private static final String NORMAL = "NORMAL";

    /**
     * mice类型
     */
    private static final String MICE_ACTIVITY_ID_TYPE = "MICE_ACTIVITY_ID";

    @Override
    protected BatchApprovalDefaultRequestType convert(Tuple3<ApprovalDetailSearchRequestType, WrapperOfAccount.AccountInfo, SSOInfoQueryResponseType> param) {
        ApprovalDetailSearchRequestType approvalDetailSearchRequest = param.getT1();
        WrapperOfAccount.AccountInfo accountInfoUtil = param.getT2();
        SSOInfoQueryResponseType ssoInfoQueryResponseType = param.getT3();

        BatchApprovalDefaultRequestType approvalDefaultRequestType = new BatchApprovalDefaultRequestType();
        approvalDefaultRequestType.setIntegrationSoaRequestType(approvalDetailSearchRequest.getIntegrationSoaRequestType());
        approvalDefaultRequestType.setScene(convertScene(accountInfoUtil));
        String subApprovalNo = Optional.ofNullable(approvalDetailSearchRequest.getApprovalInput()).map(ApprovalInput::getSubApprovalNo).orElse("");
        if (StringUtils.isNotEmpty(subApprovalNo)) {
            approvalDefaultRequestType.setSubApprovalNos(Collections.singletonList(subApprovalNo));
        } else {
            approvalDefaultRequestType.setSsoInput(approvalDetailSearchRequest.getSsoInput());
        }
        approvalDefaultRequestType.setProductTypes(ApprovalDetailSearchUtil.buildProductType(approvalDetailSearchRequest, ssoInfoQueryResponseType));
        approvalDefaultRequestType.setBookingType(NORMAL);
        approvalDefaultRequestType.setCorpPayInfo(approvalDetailSearchRequest.getCorpPayInfo());
        String miceActivityId = Optional.ofNullable(approvalDetailSearchRequest.getMiceInput()).map(MiceInput::getMiceActivityId).orElse(null);
        if (!StringUtil.isEmpty(miceActivityId)) {
            ExtRelationItem extRelationInfo = new ExtRelationItem();
            extRelationInfo.setRelationId(miceActivityId);
            extRelationInfo.setType(MICE_ACTIVITY_ID_TYPE);
            approvalDefaultRequestType.setExtRelationItems(Collections.singletonList(extRelationInfo));
        }
        return approvalDefaultRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple3<ApprovalDetailSearchRequestType, WrapperOfAccount.AccountInfo, SSOInfoQueryResponseType> param) {
        return null;
    }

    private String convertScene(WrapperOfAccount.AccountInfo accountInfo) {
        if (Boolean.TRUE.equals(accountInfo.isOaApprovalHead())) {
            return CommonConstant.PRE_POSITION;
        } else {
            return CommonConstant.POST_POSITION;
        }
    }
}
