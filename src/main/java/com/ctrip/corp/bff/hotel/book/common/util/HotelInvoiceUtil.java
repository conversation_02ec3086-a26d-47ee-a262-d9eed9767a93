package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.hotel.book.contract.HotelInvoiceInfo;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/27 13:42
 * 获取客户选中的房费 服务费支付方式
 */
public class HotelInvoiceUtil {
    private static final String INSURANCE = "INSURANCE";

    public static HotelInvoiceInfo getHotelInvoiceInfoInsurance(List<HotelInvoiceInfo> hotelInvoiceInfos) {
        if (CollectionUtil.isEmpty(hotelInvoiceInfos)) {
            return null;
        }
        return hotelInvoiceInfos.stream().filter(Objects::nonNull)
            .filter(hotelInvoiceInfo -> StringUtil.equalsIgnoreCase(hotelInvoiceInfo.getHotelInvoiceType(), INSURANCE))
            .collect(Collectors.toList()).stream().findFirst().orElse(null);
    }
}
