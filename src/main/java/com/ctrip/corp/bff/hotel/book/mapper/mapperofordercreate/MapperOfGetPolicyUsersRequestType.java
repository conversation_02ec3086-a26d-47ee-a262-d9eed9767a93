package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.framework.foundation.Foundation;
import corp.user.service.corpUserInfoService.GetPolicyUsersRequestType;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/5/16
 */
@Component
public class MapperOfGetPolicyUsersRequestType extends AbstractMapper<Tuple3<PolicyInput, IntegrationSoaRequestType,
        WrapperOfAccount.AccountInfo>, GetPolicyUsersRequestType> {
    @Override
    protected GetPolicyUsersRequestType convert(Tuple3<PolicyInput, IntegrationSoaRequestType, WrapperOfAccount.AccountInfo> para) {
        if (para == null) {
            return null;
        }
        PolicyInput policyInput = para.getT1();
        IntegrationSoaRequestType integrationSoaRequestType = para.getT2();
        WrapperOfAccount.AccountInfo accountInfo = para.getT3();
        if (policyInput == null || StringUtil.isBlank(policyInput.getPolicyUid()) || accountInfo == null) {
            return null;
        }
        // 无登录卡信息
        if (integrationSoaRequestType == null || integrationSoaRequestType.getUserInfo() == null || StringUtil.isBlank(integrationSoaRequestType.getUserInfo().getUserId())) {
            return null;
        }
        // 非 行程+政策执行人
        if (!accountInfo.isPackageEnabled() || !accountInfo.isPolicyModel()) {
            return null;
        }
        // 政策执行人是自己
        if (StringUtil.equalsIgnoreCase(integrationSoaRequestType.getUserInfo().getUserId(), policyInput.getPolicyUid())) {
            return null;
        }
        GetPolicyUsersRequestType getPolicyUsersRequestType = new GetPolicyUsersRequestType();
        getPolicyUsersRequestType.setPolicyUID(policyInput.getPolicyUid());
        getPolicyUsersRequestType.setUid(Optional.ofNullable(integrationSoaRequestType.getUserInfo()).map(UserInfo::getUserId).orElse(null));
        getPolicyUsersRequestType.setAppId(Foundation.app().getAppId());
        return getPolicyUsersRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple3<PolicyInput, IntegrationSoaRequestType, WrapperOfAccount.AccountInfo>
                                                 policyInputIntegrationSoaRequestTypeAccountInfoCorpPayInfoCityInputTuple5) {
        return null;
    }
}
