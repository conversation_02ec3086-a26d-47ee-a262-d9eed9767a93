package com.ctrip.corp.bff.hotel.book.handler.corpsettlementinvoiceservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.settlement.service.invoice.CorpSettlementInvoiceServiceClient;
import corp.settlement.service.invoice.corpinvoice.CorpOrderInvoiceDetailInfoQueryRequest;
import corp.settlement.service.invoice.corpinvoice.CorpOrderInvoiceDetailInfoQueryResponse;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/1/3 19:40
 */
@Component
public class HandlerOfCorpOrderInvoiceInfo extends
    AbstractHandlerOfSOA<CorpOrderInvoiceDetailInfoQueryRequest, CorpOrderInvoiceDetailInfoQueryResponse, CorpSettlementInvoiceServiceClient> {

    @Override
    protected String getMethodName() {
        return "corpOrderInvoiceInfo";
    }
}
