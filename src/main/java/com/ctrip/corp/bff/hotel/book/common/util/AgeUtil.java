package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum;
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil;
import com.ctrip.corp.bff.framework.template.common.utils.CalendarUtil;
import com.ctrip.corp.bff.framework.template.common.utils.ExceptionUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;

import java.util.Calendar;
import java.util.Locale;

/**
 * <AUTHOR>
 * @date 2024/7/9 15:24
 */
public class AgeUtil {

    public static final int IDCARDNUMBER_LENGTH = 18;
    public static final int IDCARDNUMBER_LENGTH_SPECIAL = 15;
    private static final int INSURE_MAXAGE = 85;
    private static final int INSURE_MINAGE = 18;
    private static final int IDCARDNUMBER_YEAR_START = 6;
    private static final int IDCARDNUMBER_YEAR_END = 10;
    private static final int IDCARDNUMBER_MONTH_START = 10;
    private static final int IDCARDNUMBER_MONTH_END = 12;
    private static final int IDCARDNUMBER_DAY_START = 12;
    private static final int IDCARDNUMBER_DAY_END = 14;

    private static final int SPECIAL_IDCARDNUMBER_YEAR_START = 6;
    private static final int SPECIAL_IDCARDNUMBER_YEAR_END = 8;
    private static final int SPECIAL_IDCARDNUMBER_MONTH_START = 8;
    private static final int SPECIAL_IDCARDNUMBER_MONTH_END = 10;
    private static final int SPECIAL_IDCARDNUMBER_DAY_START = 10;
    private static final int SPECIAL_IDCARDNUMBER_DAY_END = 12;
    private static final int SPECIAL_IDCARD_YEAR_START = 19;

    private static final int BIRTH_YEAR_END = 4;
    private static final int BIRTH_MONTH_LENGTH = 2;
    private static final int BIRTH_MONTH_START = 4;
    private static final int BIRTH_MONTH_END = 6;
    private static final int BIRTH_DAY_LENGTH = 2;
    private static final int BIRTH_DAY_START = 6;
    private static final int BIRTH_DAY_END = 8;

    private static final int DIFFERENCE = 1;
    private static final int DEFAULT_AGE = 0;

    public static boolean ageInRange(Integer age) {
        if (age == null) {
            return false;
        }
        return INSURE_MINAGE <= age && INSURE_MAXAGE >= age;
    }

    /**
     * 根据身份证号获取年龄
     *
     * @param idCardNumber 身份证号
     * @return 根据身份证计算得到的年龄 =0既可能是获取失败兜底例如身份证不合法等 也有可能是刚出生0岁 酌情使用
     *
     * 目前适用场景：保险出行人除非是成人，否则不允许购买保险 兜底可用0
     */
    public static int getAgeByCardNo(String idCardNumber) {
        if (StringUtil.isBlank(idCardNumber)) {
            return DEFAULT_AGE;
        }
        if (idCardNumber.length() != IDCARDNUMBER_LENGTH && idCardNumber.length() != IDCARDNUMBER_LENGTH_SPECIAL) {
            LogUtil.loggingClogOnly(LogLevelEnum.Error, AgeUtil.class, "getAgeByCardNo.error", idCardNumber, null);
            return DEFAULT_AGE;
        }
        String birth = null;
        try {
            String year;
            String month;
            String day;
            if (idCardNumber.length() == IDCARDNUMBER_LENGTH) {
                year = idCardNumber.substring(IDCARDNUMBER_YEAR_START, IDCARDNUMBER_YEAR_END);
                month = idCardNumber.substring(IDCARDNUMBER_MONTH_START, IDCARDNUMBER_MONTH_END);
                day = idCardNumber.substring(IDCARDNUMBER_DAY_START, IDCARDNUMBER_DAY_END);
            } else {
                year = SPECIAL_IDCARD_YEAR_START + idCardNumber.substring(SPECIAL_IDCARDNUMBER_YEAR_START, SPECIAL_IDCARDNUMBER_YEAR_END);
                month = idCardNumber.substring(SPECIAL_IDCARDNUMBER_MONTH_START, SPECIAL_IDCARDNUMBER_MONTH_END);
                day = idCardNumber.substring(SPECIAL_IDCARDNUMBER_DAY_START, SPECIAL_IDCARDNUMBER_DAY_END);
            }
            birth = year + "-" + month + "-" + day;
        } catch (Exception e) {
            LogUtil.loggingClogOnly(LogLevelEnum.Error, AgeUtil.class, "getAgeByCardNo.error",
                ExceptionUtil.getException(e), null);
        }
        if (StringUtil.isBlank(birth)) {
            LogUtil.loggingClogOnly(LogLevelEnum.Error, AgeUtil.class, "getAgeByCardNo.error", idCardNumber, null);
            return DEFAULT_AGE;
        }
        return getAgeByBirth(birth);
    }

    /**
     * 根据生日获取年龄
     * @param birth 出生日期 yyyy-MM-dd 格式
     * @return 根据出生日期得到的年龄 =0既可能是获取失败兜底例如入参格式不符合 也有可能是刚出生0岁 酌情使用
     *
     * 目前适用场景：保险出行人除非是成人，否则不允许购买保险 兜底可用0
     */
    public static int getAgeByBirth(String birth) {
        if (StringUtil.isBlank(birth)) {
            return 0;
        }
        try {
            Calendar birthCalendar = CalendarUtil.parseCalendar(birth, "yyyy-MM-dd");
            String birthStr = CalendarUtil.format(birthCalendar, "yyyyMMdd");
            return getAge(birthStr.substring(0, BIRTH_YEAR_END),
                birthStr.substring(BIRTH_MONTH_START, BIRTH_MONTH_END).substring(0, BIRTH_MONTH_LENGTH),
                birthStr.substring(BIRTH_DAY_START, BIRTH_DAY_END).substring(0, BIRTH_DAY_LENGTH));
        } catch (Exception e) {
            LogUtil.loggingClogOnly(LogLevelEnum.Error, AgeUtil.class, "getAgeByBirth.error",
                ExceptionUtil.getException(e), null);
            return DEFAULT_AGE;
        }
    }

    private static int getAge(String birthYear, String birthMonth, String birthDay) {
        if (StringUtil.isBlank(birthYear) || StringUtil.isBlank(birthMonth) || StringUtil.isBlank(birthDay)) {
            return DEFAULT_AGE;
        }
        try {
            // 身份证上都是北京时间 所以需要获取当前北京时间来对比
            Calendar now = Calendar.getInstance(Locale.CHINA);
            int nowYear = now.get(Calendar.YEAR);
            int nowMonth = now.get(Calendar.MONTH) + DIFFERENCE;
            int nowDay = now.get(Calendar.DATE);
            // 用当前年月日减去生日年月日
            int yearMinus = nowYear - Integer.parseInt(birthYear);
            int monthMinus = nowMonth - Integer.parseInt(birthMonth);
            int dayMinus = nowDay - Integer.parseInt(birthDay);

            int age = yearMinus;

            // 出生年份为当前年份
            if (yearMinus == 0) {
                age = 0;
            } else {
                // 出生年份大于当前年份
                if (monthMinus < 0) {
                    // 出生月份小于当前月份时，还没满周岁
                    age = age - DIFFERENCE;
                }
                if (monthMinus == 0) {
                    // 当前月份为出生月份时，判断日期
                    if (dayMinus < 0) {
                        // 出生日期小于当前月份时，没满周岁
                        age = age - DIFFERENCE;
                    }
                }
            }
            return age;
        } catch (Exception e) {
            LogUtil.loggingClogOnly(LogLevelEnum.Error, AgeUtil.class, "getAge.error", ExceptionUtil.getException(e),
                null);
            return DEFAULT_AGE;
        }
    }
}
