package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.foundation.common.util.NumberUtil;

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/11
 */
public class CityInfoValidateUtil {
    public static boolean validCity(String cityId, String cityName) {
        Integer parsedCityId = NumberUtil.parseInt(cityId);
        return parsedCityId != null && StringUtil.equals(String.valueOf(parsedCityId), cityId)
            && parsedCityId > 0 && !"InvalidCity".equalsIgnoreCase(cityName);
    }
}
