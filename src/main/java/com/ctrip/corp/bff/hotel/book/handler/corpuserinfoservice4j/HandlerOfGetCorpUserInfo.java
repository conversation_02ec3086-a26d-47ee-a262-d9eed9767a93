package com.ctrip.corp.bff.hotel.book.handler.corpuserinfoservice4j;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.corpUserInfoService.CorpUserInfoService4jClient;
import corp.user.service.corpUserInfoService.GetCorpUserInfoRequestType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2024/4/9 18:59
 * @Version 1.0
 */
@Component
public class HandlerOfGetCorpUserInfo extends
        AbstractHandlerOfSOA<GetCorpUserInfoRequestType, GetCorpUserInfoResponseType, CorpUserInfoService4jClient> {

    @Override
    protected String getMethodName() {
        return "getCorpUserInfo";
    }
}
