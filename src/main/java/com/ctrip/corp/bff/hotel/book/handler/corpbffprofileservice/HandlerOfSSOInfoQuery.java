package com.ctrip.corp.bff.hotel.book.handler.corpbffprofileservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.bff.profile.contract.CorpBffProfileServiceClient;
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryRequestType;
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 单点登录信息查询
 * @Date 2024/8/13 15:27
 * @Version 1.0
 */
@Component
public class HandlerOfSSOInfoQuery extends AbstractHandlerOfSOA<SSOInfoQueryRequestType
        , SSOInfoQueryResponseType, CorpBffProfileServiceClient> {
    @Override
    protected String getMethodName() {
        return "ssoInfoQuery";
    }
}
