package com.ctrip.corp.bff.hotel.book.common.wrapper;

import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType;
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextResponseType;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount.AccountInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.RepeatOrderInfo;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.CustomizedSharkConfig;
import corp.user.service.corp4jservice.GetReasoncodesResponseType;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/9/14 16:43
 */
public class WrapperOfOrderRcInfo {
    private CheckTravelPolicyResponseType checkTravelPolicyResponseType;
    private RepeatOrderInfo repeatOrderInfo;
    private OrderCreateRequestType orderCreateRequestType;
    private GetReasoncodesResponseType getReasoncodesResponseType;
    private List<CustomizedSharkConfig> customizedSharkConfigList;
    private GetTravelPolicyContextResponseType getTravelPolicyContextResponseType;
    private WrapperOfAccount.AccountInfo accountInfo;
    private OrderCreateToken orderCreateToken;
    private ResourceToken resourceToken;

    public CheckTravelPolicyResponseType getCheckTravelPolicyResponseType() {
        return checkTravelPolicyResponseType;
    }

    public RepeatOrderInfo getRepeatOrderInfo() {
        return repeatOrderInfo;
    }

    public OrderCreateRequestType getOrderCreateRequestType() {
        return orderCreateRequestType;
    }

    public GetReasoncodesResponseType getGetReasoncodesResponseType() {
        return getReasoncodesResponseType;
    }

    public List<CustomizedSharkConfig> getCustomizedSharkConfigList() {
        return customizedSharkConfigList;
    }

    public GetTravelPolicyContextResponseType getGetTravelPolicyContextResponseType() {
        return getTravelPolicyContextResponseType;
    }

    public AccountInfo getAccountInfo() {
        return accountInfo;
    }

    public OrderCreateToken getOrderCreateToken() {
        return orderCreateToken;
    }

    public ResourceToken getResourceToken() {
        return resourceToken;
    }

    public static WrapperOfOrderRcInfo.Builder builder() {
        return new WrapperOfOrderRcInfo.Builder();
    }

    public static class Builder {

        private CheckTravelPolicyResponseType checkTravelPolicyResponseType;
        private RepeatOrderInfo repeatOrderInfo;
        private OrderCreateRequestType orderCreateRequestType;
        private GetReasoncodesResponseType getReasoncodesResponseType;
        private List<CustomizedSharkConfig> customizedSharkConfigList;
        private GetTravelPolicyContextResponseType getTravelPolicyContextResponseType;

        private WrapperOfAccount.AccountInfo accountInfo;
        private OrderCreateToken orderCreateToken;

        private ResourceToken resourceToken;

        public Builder() {
        }

        public Builder setCheckTravelPolicyResponseType(CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
            this.checkTravelPolicyResponseType = checkTravelPolicyResponseType;
            return this;
        }

        public Builder setRepeatOrderInfo(RepeatOrderInfo repeatOrderInfo) {
            this.repeatOrderInfo = repeatOrderInfo;
            return this;
        }

        public Builder setOrderCreateRequestType(OrderCreateRequestType orderCreateRequestType) {
            this.orderCreateRequestType = orderCreateRequestType;
            return this;
        }

        public Builder setGetReasoncodesResponseType(GetReasoncodesResponseType getReasoncodesResponseType) {
            this.getReasoncodesResponseType = getReasoncodesResponseType;
            return this;
        }

        public Builder setCustomizedSharkConfigList(List<CustomizedSharkConfig> customizedSharkConfigList) {
            this.customizedSharkConfigList = customizedSharkConfigList;
            return this;
        }

        public Builder setGetTravelPolicyContextResponseType(
            GetTravelPolicyContextResponseType getTravelPolicyContextResponseType) {
            this.getTravelPolicyContextResponseType = getTravelPolicyContextResponseType;
            return this;
        }

        public Builder setAccountInfo(WrapperOfAccount.AccountInfo accountInfo) {
            this.accountInfo = accountInfo;
            return this;
        }

        public Builder setOrderCreateToken(OrderCreateToken orderCreateToken) {
            this.orderCreateToken = orderCreateToken;
            return this;
        }

        public Builder setResourceToken(ResourceToken resourceToken) {
            this.resourceToken = resourceToken;
            return this;
        }

        public WrapperOfOrderRcInfo build() {
            WrapperOfOrderRcInfo wrapperOfOrderRcInfo = new WrapperOfOrderRcInfo();
            wrapperOfOrderRcInfo.checkTravelPolicyResponseType = this.checkTravelPolicyResponseType;
            wrapperOfOrderRcInfo.repeatOrderInfo = this.repeatOrderInfo;
            wrapperOfOrderRcInfo.orderCreateRequestType = this.orderCreateRequestType;
            wrapperOfOrderRcInfo.getReasoncodesResponseType = this.getReasoncodesResponseType;
            wrapperOfOrderRcInfo.customizedSharkConfigList = this.customizedSharkConfigList;
            wrapperOfOrderRcInfo.getTravelPolicyContextResponseType = this.getTravelPolicyContextResponseType;
            wrapperOfOrderRcInfo.accountInfo = this.accountInfo;
            wrapperOfOrderRcInfo.orderCreateToken = this.orderCreateToken;
            wrapperOfOrderRcInfo.resourceToken = this.resourceToken;
            return wrapperOfOrderRcInfo;
        }
    }

}
