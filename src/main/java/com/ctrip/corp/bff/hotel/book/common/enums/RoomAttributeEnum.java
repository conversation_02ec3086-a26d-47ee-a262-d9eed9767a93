package com.ctrip.corp.bff.hotel.book.common.enums;

/**
 * @Author: chenchuang
 * @Date: 2024/8/30 19:47
 * @Description: 担保类型枚举
 */
public enum RoomAttributeEnum {

    /**
     * 入住人数
     */
    MAX_GUEST_NUM("MAX_GUEST_NUM"),

    /**
     * 床型
     */
    BED_DESC("BED_DESC"),

    /**
     * 床型描述
     */
    BED_DETAIL_DESC("BED_DETAIL_DESC"),

    /**
     * 餐食
     */
    MEAL_DESC("MEAL_DESC"),

    /**
     * 最早到店时间
     */
    ARRIVAL_TIME("ARRIVAL_TIME"),

    /**
     * 最晚离店时间
     */
    DEPARTURE_TIME("DEPARTURE_TIME"),

    /**
     * 房型变化信息
     */
    ROOM_CHANGE("ROOM_CHANGE"),

    /**
     * 城市id
     */
    CITY_ID("CITY_ID"),

    /**
     * 是否海外
     */
    OVER_SEA("OVER_SEA"),

    /**
     * 房型是否可积携程等级分
     */
    IS_TRIP_LEVEL_POINT_ROOM("IS_TRIP_LEVEL_POINT_ROOM"),


    /**
     * 房型id
     */
    ROOM_ID("ROOM_ID"),

    /**
     * 房型名称
     */
    ROOM_NAME("ROOM_NAME"),

    /**
     * 窗型类型
     */
    WINDOW_TYPE("WINDOW_TYPE"),

    /**
     * 窗型名称
     */
    WINDOW_NAME("WINDOW_NAME"),


    /**
     * 床名
     */
    BED_NAME("BED_NAME"),
    /**
     * AMADEUS
     */
    AMADEUS("AMADEUS"),

    /**
     * AMADEUS_USER_VCC
     */
    AMADUES_USER_VCC("AMADUES_USER_VCC"),

    /**
     * 两方
     */
    TMC_ROOM("TMC_ROOM"),

    /**
     * 福利房
     */
    WELFARE_ROOM("WELFARE_ROOM"),

    /**
     * 支付类型
     */
    BALANCE_TYPE("BALANCE_TYPE"),

    /**
     * 餐食备注
     */
    MEAL_REMARK("MEAL_REMARK"),

    /**
     * 供应商id
     */
    VENDOR_ID("VENDOR_ID"),

    /**
     * 钟点房提示话术
     */
    HOUR_ROOM_TIP("HOUR_ROOM_TIP"),

    /**
     * 餐食类型
     */
    MEAL_TYPE("MEAL_TYPE"),

    /**
     * 无餐食
     */
    NONE_MEAL("NONE_MEAL"),

    /**
     * 适用人群标题 对应可订 ApplicativeAreaTitle
     */
    APPLICATIVE_AREA_TITLE("APPLICATIVE_AREA_TITLE"),

    /**
     * 适用人群描述 对应可订 ApplicativeAreaDesc
     */
    APPLICATIVE_AREA_DESC("APPLICATIVE_AREA_DESC"),

    /**
     * 房型描述 对应可订 RoomDescription
     */
    ROOM_DESCRIPTION("ROOM_DESCRIPTION"),

    /**
     * 特别提示 对应可订 SpecialNoticeList
     */
    SPECIAL_NOTICE_VALUE_LIST("SPECIAL_NOTICE_VALUE_LIST"),

    /**
     * 房型类型C：协议 M：会员
     */
    ROOM_TYPE("ROOM_TYPE"),

    /**
     * 城市所在区域：MAIN_LAND 大陆；HK_MO_TW 港澳台；OVER_SEAS 海外
     */
    CITY_AREA("CITY_AREA"),

    /**
     * 入住即享
     */
    ACCOMMODATION_INFO("ACCOMMODATION_INFO"),

    /**
     * 供应商房型id
     */
    GDS_TYPE("GDS_TYPE"),

    /**
     * 钟点房连住时间
     */
    DURATION_OF_HOUR_ROOM("DURATION_OF_HOUR_ROOM"),

    /**
     * 随机房类型  hotel:酒店随机房 platform:平台随机房
     */
    RANDOM_ROOM_TYPE("RANDOM_ROOM_TYPE"),
    ;

    private String value;

    RoomAttributeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
