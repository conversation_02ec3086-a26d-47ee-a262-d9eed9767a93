package com.ctrip.corp.bff.hotel.book.common.mapper;

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.SSOInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryRequestType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 单点登录信息查询请求参数
 * @Date 2024/8/13 15:29
 * @Version 1.0
 */
@Component
public class MapperOfSSOInfoQueryRequestType extends
        AbstractMapper<Tuple2<IntegrationSoaRequestType, SSOInput>, SSOInfoQueryRequestType> {

    @Override
    protected SSOInfoQueryRequestType convert(Tuple2<IntegrationSoaRequestType, SSOInput> inputTuple2) {
        SSOInfoQueryRequestType ssoInfoQueryRequestType = new SSOInfoQueryRequestType();
        ssoInfoQueryRequestType.setIntegrationSoaRequestType(inputTuple2.getT1());
        ssoInfoQueryRequestType.setSsoInput(inputTuple2.getT2());
        return ssoInfoQueryRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple2<IntegrationSoaRequestType, SSOInput> integrationSoaRequestTypeSSOInputTuple2) {
        return null;
    }
}
