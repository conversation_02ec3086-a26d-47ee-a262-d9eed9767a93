package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.soa._24373.CheckBasicInfoType;
import com.ctrip.soa._24373.CreateOrderCheckRequestType;
import org.springframework.stereotype.Component;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2025/1/3 19:43
 */
@Component public class MapperOfCreateOrderCheckRequestType
    extends AbstractMapper<Tuple1<OrderCreateRequestType>, CreateOrderCheckRequestType> {
    private static final String FLASH_ORDER = "FlashOrder";

    @Override protected CreateOrderCheckRequestType convert(Tuple1<OrderCreateRequestType> tuple) {
        OrderCreateRequestType orderCreateRequestType = tuple.getT1();
        CreateOrderCheckRequestType createOrderCheckRequestType = new CreateOrderCheckRequestType();
        CheckBasicInfoType checkBasicInfoType = new CheckBasicInfoType();
        checkBasicInfoType.setUid(orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId());
        checkBasicInfoType.setScenarioList(Arrays.asList(FLASH_ORDER));
        createOrderCheckRequestType.setBasicInfo(checkBasicInfoType);
        return createOrderCheckRequestType;
    }

    @Override protected ParamCheckResult check(Tuple1<OrderCreateRequestType> tuple) {
        return null;
    }

}
