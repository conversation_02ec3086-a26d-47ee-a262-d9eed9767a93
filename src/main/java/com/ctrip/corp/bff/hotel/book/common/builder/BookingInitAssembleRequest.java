package com.ctrip.corp.bff.hotel.book.common.builder;

import com.ctrip.basebiz.geolocation.service.GetAllNationalityResponseType;
import com.ctrip.corp.agg.commonws.entity.GetPackageRoomListResponseType;
import com.ctrip.corp.agg.commonws.pkg.room.snapshot.entity.GetPackageRoomSnapshotResponseType;
import com.ctrip.corp.agg.hotel.salestrategy.entity.CalculateTravelRewardsResponseType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.entity.PersonalAccountConfig;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.entity.RoomNumberInfoConfig;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfHotelTravelPolicy;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfSearchApproval;
import com.ctrip.corp.bff.framework.hotel.entity.contract.ApprovalOutput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.BookInitToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.bff.hotel.book.qconfig.QConfigOfContactConfig;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfRegisterConfig;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.CustomizedSharkConfig;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.MemberBonusRuleEntry;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.QconfigEntityOfGroupHotelMemberCardRule;
import com.ctrip.corp.bff.specific.contract.ApprovalDefaultResponseType;
import com.ctrip.corp.bff.specific.contract.ApprovalTextInfoResponseType;
import com.ctrip.corp.bff.specific.contract.BatchApprovalDefaultResponseType;
import com.ctrip.corp.bff.tools.contract.CountryQueryResponseType;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType;
import com.ctrip.corp.hotel.book.query.entity.GetHotelDetailInfoResponseType;
import com.ctrip.corp.hotelbook.commonws.entity.CancelPolicyDescType;
import com.ctrip.corp.hotelbook.commonws.entity.DistributePaymentAmountResponseType;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedInvoiceTypeResponseType;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodResponseType;
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataResponseType;
import com.ctrip.corp.order.managementcenter.hotel.service.contract.XProductEnquireResponseType;
import com.ctrip.hotel.order.rewardservice.contract.CtripMemberUserInfoResponse;
import com.ctrip.hotel.wireless.htlgetgroupvippackagefunction.soa.contract.GetGroupVipPackageResponseType;
import com.ctrip.ibu.member.coupon.platform.client.GetMultilingualCouponInfoResponseType;
import com.ctrip.model.CalculateServiceChargeV2ResponseType;
import com.ctrip.model.GetRoomChargePolicyListResponseType;
import com.ctrip.model.SearchRegistrationFieldsResponseType;
import com.ctrip.order.reimbursement.ReimbursementDetailInfoType;
import com.ctrip.soa._21234.GetTripBookingInfosResponseType;
import com.ctrip.soa._21234.SearchTripBasicInfoResponseType;
import com.ctrip.soa._21234.SearchTripDetailResponseType;
import com.ctrip.soa._21685.PayConfigResponseType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderDetailResponseType;
import com.ctrip.soa.platform.sps.InvoiceService.v1.GetInvoiceTitlesResponse;
import corp.settlement.service.invoice.corpinvoice.CorpOrderInvoiceDetailInfoQueryResponse;
import corp.user.service.CorpAccountQueryService.GetAuthDelayResponseType;
import corp.user.service.CorpAccountQueryService.QueryIndividualAccountResponseType;
import corp.user.service.UserInfoQueryService.BatchSearchClientsInfoResponseType;
import corp.user.service.authorityManage.QueryAuthResponseType;
import corp.user.service.corp4jservice.CheckCorpSwitchByCorpIDResponseType;
import corp.user.service.corp4jservice.GetCorpInfoResponseType;
import corp.user.service.corp4jservice.GetReasoncodesResponseType;
import corp.user.service.corpUserInfoService.*;
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType;

import java.util.List;
import java.util.Map;

/**
 * @Author: chenchuang
 * @Date: 2024/9/10 19:46
 * @Description: 填写页最终response的组装入参request
 */
public class BookingInitAssembleRequest {

    private BookingInitRequestType bookingInitRequest;
    private CancelPolicyDescType cancelPolicyDesc;
    private GetAuthDelayResponseType getAuthDelayResponse;
    private WrapperOfAccount.AccountInfo accountInfo;
    private QueryHotelOrderDataResponseType queryHotelOrderDataResponse;
    private GetSupportedPaymentMethodResponseType getSupportedPaymentMethodResponse;
    private GetGroupVipPackageResponseType getGroupVipPackageResponse;
    private CtripMemberUserInfoResponse ctripMemberUserInfoResponse;
    private CalculateTravelRewardsResponseType calculateTravelRewardsResponseType;
    private PersonalAccountConfig personalAccountConfig;
    private RoomNumberInfoConfig roomNumberInfoConfig;
    private QueryIndividualAccountResponseType queryIndividualAccountResponseType;
    private GetCorpUserHotelVipCardResponseType getCorpUserHotelVipCardResponse;
    private CalculateServiceChargeV2ResponseType calculateServiceChargeV2ResponseType;
    private ReimbursementDetailInfoType reimbursementDetailInfoType;
    private GetContactInvoiceDefaultInfoResponseType getContactInvoiceDefaultInfoResponseType;
    private GetInvoiceTitlesResponse getInvoiceTitlesResponse;
    private GetUserAddressInfoResponseType getUserAddressInfoResponseType;
    private GetSupportedInvoiceTypeResponseType getSupportedInvoiceTypeResponseType;
    private ResourceToken resourceToken;
    private HotelPayTypeEnum roomPayType;
    private HotelPayTypeEnum servicePayType;
    private GetCorpUserInfoResponseType getCorpUserInfoResponseType;
    private WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo hotelTravelPolicyInfo;
    private WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo;

    private WrapperOfSearchApproval.ApprovalInfo approvalInfo;

    private GetMultilingualCouponInfoResponseType getMultilingualCouponInfoResponseType;

    private DistributePaymentAmountResponseType distributePaymentAmountResponseType;

    private GetCorpUserInfoResponseType getGetCorpUserInfoByPolicyResponseType;
    private BatchApprovalDefaultResponseType approvalDefaultResponseType;
    private ApprovalTextInfoResponseType approvalTextInfoResponseType;
    private GetPackageRoomListResponseType getPackageRoomListResponseType;
    private GetPackageRoomSnapshotResponseType getPackageRoomSnapshotResponseType;
    private GetHotelDetailInfoResponseType getHotelDetailInfoResponseType;
    private SearchRegistrationFieldsResponseType searchRegistrationFieldsResponseType;
    private BatchSearchClientsInfoResponseType batchSearchClientsInfoResponseType;
    private QConfigOfContactConfig qConfigOfContactConfig;
    private CheckTravelPolicyResponseType checkTravelPolicyResponseType;
    private XProductEnquireResponseType xProductEnquireResponseType;
    private GetTripBookingInfosResponseType getTripBookingInfosResponseType;
    private QconfigEntityOfGroupHotelMemberCardRule qconfigEntityOfGroupHotelMemberCardRule;
    private BookInitToken bookInitToken;
    private CheckCorpSwitchByCorpIDResponseType checkCorpSwitchByCorpIDResponseType;
    private ApprovalOutput approvalOutput;
    private GetPlatformRelationByUidResponseType getPlatformRelationByUidResponseType;
    private SearchTripDetailResponseType searchTripDetailResponseType;
    private ApprovalInput approvalInput;
    private HotelPayTypeEnum selectedRoomPayType;

    private List<MemberBonusRuleEntry> memberBonusRuleEntries;

    private PayConfigResponseType payConfigResponseType;

    private GetRoomChargePolicyListResponseType getRoomChargePolicyListResponseType;

    private QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType;

    private GetReasoncodesResponseType getReasoncodesResponseType;

    private List<CustomizedSharkConfig> customizedSharkConfigList;
    private CorpOrderInvoiceDetailInfoQueryResponse corpOrderInvoiceDetailInfoQueryResponse;

    private QconfigOfRegisterConfig qconfigOfRegisterConfig;
    private Map<String, StrategyInfo> strategyInfoMap;

    private CountryQueryResponseType countryQueryResponseType;

    private GetAllNationalityResponseType getAllNationalityResponseType;

    private GetCityBaseInfoResponseType getCityBaseInfoResponseType;
    private QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig;
    private SearchTripBasicInfoResponseType searchTripBasicInfoResponseTypeOfOriginalOrder;
    private GetCorpInfoResponseType getCorpInfoResponseType;
    // USE_NEW 灰度开启  COMPARE 灰度关闭，对比开启  USE_OLD 灰度关闭，对比关闭
    private String costCenterNew;
    private QueryAuthResponseType queryAuthResponseType;
    private BookingInitAssembleRequest() {
    }

    public GetCorpInfoResponseType getGetCorpInfoResponseType() {
        return getCorpInfoResponseType;
    }
    public List<CustomizedSharkConfig> getCustomizedSharkConfigList() {
        return customizedSharkConfigList;
    }
    public GetReasoncodesResponseType getGetReasoncodesResponseType() {
        return getReasoncodesResponseType;
    }

    public BookInitToken getBookInitToken() {
        return bookInitToken;
    }
    public XProductEnquireResponseType getxProductEnquireResponseType() {
        return xProductEnquireResponseType;
    }
    public BookingInitRequestType getBookingInitRequest() {
        return bookingInitRequest;
    }

    public CancelPolicyDescType getCancelPolicyDesc() {
        return cancelPolicyDesc;
    }

    public GetAuthDelayResponseType getGetAuthDelayResponse() {
        return getAuthDelayResponse;
    }

    public WrapperOfAccount.AccountInfo getAccountInfo() {
        return accountInfo;
    }

    public QueryHotelOrderDataResponseType getQueryHotelOrderDataResponse() {
        return queryHotelOrderDataResponse;
    }

    public GetSupportedPaymentMethodResponseType getGetSupportedPaymentMethodResponse() {
        return getSupportedPaymentMethodResponse;
    }

    public GetGroupVipPackageResponseType getGetGroupVipPackageResponse() {
        return getGroupVipPackageResponse;
    }

    public CtripMemberUserInfoResponse getCtripMemberUserInfoResponse() {
        return ctripMemberUserInfoResponse;
    }

    public CalculateTravelRewardsResponseType getCalculateTravelRewardsResponseType() {
        return calculateTravelRewardsResponseType;
    }

    public PersonalAccountConfig getPersonalAccountConfig() {
        return personalAccountConfig;
    }

    public RoomNumberInfoConfig getRoomNumberInfoConfig() {
        return roomNumberInfoConfig;
    }

    public QueryIndividualAccountResponseType getIndividualAccountResponse() {
        return queryIndividualAccountResponseType;
    }

    public GetCorpUserHotelVipCardResponseType getGetCorpUserHotelVipCardResponse() {
        return getCorpUserHotelVipCardResponse;
    }
    public CalculateServiceChargeV2ResponseType getCalculateServiceChargeV2ResponseType() {
        return calculateServiceChargeV2ResponseType;
    }

    public ReimbursementDetailInfoType getReimbursementDetailInfoType() {
        return reimbursementDetailInfoType;
    }

    public GetInvoiceTitlesResponse getGetInvoiceTitlesResponse() {
        return getInvoiceTitlesResponse;
    }

    public GetContactInvoiceDefaultInfoResponseType getGetContactInvoiceDefaultInfoResponseType() {
        return getContactInvoiceDefaultInfoResponseType;
    }

    public GetUserAddressInfoResponseType getGetUserAddressInfoResponseType() {
        return getUserAddressInfoResponseType;
    }

    public GetSupportedInvoiceTypeResponseType getGetSupportedInvoiceTypeResponseType() {
        return getSupportedInvoiceTypeResponseType;
    }

    public ResourceToken getResourceToken() {
        return resourceToken;
    }

    public HotelPayTypeEnum getRoomPayType() {
        return roomPayType;
    }

    public HotelPayTypeEnum getServicePayType() {
        return servicePayType;
    }

    public GetCorpUserInfoResponseType getGetCorpUserInfoResponseType() {
        return getCorpUserInfoResponseType;
    }

    public GetCorpUserInfoResponseType getGetCorpUserInfoByPolicyResponseType() {
        return getGetCorpUserInfoByPolicyResponseType;
    }

    public WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo getHotelTravelPolicyInfo() {
        return hotelTravelPolicyInfo;
    }

    public WrapperOfCheckAvail.CheckAvailInfo getCheckAvailInfo() {
        return checkAvailInfo;
    }

    public WrapperOfSearchApproval.ApprovalInfo getApprovalInfo() {
        return approvalInfo;
    }

    public QueryIndividualAccountResponseType getQueryIndividualAccountResponseType() {
        return queryIndividualAccountResponseType;
    }

    public static BookingInitAssembleRequest.Builder builder() {
        return new BookingInitAssembleRequest.Builder();
    }

    public DistributePaymentAmountResponseType getDistributePaymentAmountResponseType() {
        return distributePaymentAmountResponseType;
    }

    public GetMultilingualCouponInfoResponseType getGetMultilingualCouponInfoResponseType() {
        return getMultilingualCouponInfoResponseType;
    }

    public ApprovalTextInfoResponseType getApprovalTextInfoResponseType() {
        return approvalTextInfoResponseType;
    }

    public BatchApprovalDefaultResponseType getApprovalDefaultResponseType() {
        return approvalDefaultResponseType;
    }

    public GetPackageRoomListResponseType getGetPackageRoomListResponseType() {
        return getPackageRoomListResponseType;
    }

    public GetPackageRoomSnapshotResponseType getGetPackageRoomSnapshotResponseType() {
        return getPackageRoomSnapshotResponseType;
    }

    public GetHotelDetailInfoResponseType getGetHotelDetailInfoResponseType() {
        return getHotelDetailInfoResponseType;
    }

    public SearchRegistrationFieldsResponseType getSearchRegistrationFieldsResponseType() {
        return searchRegistrationFieldsResponseType;
    }

    public BatchSearchClientsInfoResponseType getBatchSearchClientsInfoResponseType() {
        return batchSearchClientsInfoResponseType;
    }

    public GetCorpUserInfoResponseType getGetGetCorpUserInfoByPolicyResponseType() {
        return getGetCorpUserInfoByPolicyResponseType;
    }

    public QConfigOfContactConfig getqConfigOfContactConfig() {
        return qConfigOfContactConfig;
    }

    public CheckTravelPolicyResponseType getCheckTravelPolicyResponseType() {
        return checkTravelPolicyResponseType;
    }

    public GetTripBookingInfosResponseType getGetTripBookingInfosResponseType() {
        return getTripBookingInfosResponseType;
    }

    public QconfigEntityOfGroupHotelMemberCardRule getQconfigEntityOfGroupHotelMemberCardRule() {
        return qconfigEntityOfGroupHotelMemberCardRule;
    }

    public ApprovalOutput getApprovalOutput() {
        return approvalOutput;
    }

    public GetPlatformRelationByUidResponseType getGetPlatformRelationByUidResponseType() {
        return getPlatformRelationByUidResponseType;
    }

    public SearchTripDetailResponseType getSearchTripDetailResponseType() {
        return searchTripDetailResponseType;
    }

    public ApprovalInput getApprovalInput() {
        return approvalInput;
    }

    public List<MemberBonusRuleEntry> getMemberBonusRuleEntries() {
        return memberBonusRuleEntries;
    }

    public HotelPayTypeEnum getSelectedRoomPayType() {
        return selectedRoomPayType;
    }

    public PayConfigResponseType getPayConfigResponseType() {
        return payConfigResponseType;
    }

    public GetRoomChargePolicyListResponseType getGetRoomChargePolicyListResponseType() {
        return getRoomChargePolicyListResponseType;
    }

    public QueryBizModeBindRelationResponseType getQueryBizModeBindRelationResponseType() {
        return queryBizModeBindRelationResponseType;
    }

    public CorpOrderInvoiceDetailInfoQueryResponse getCorpOrderInvoiceDetailInfoQueryResponse() {
        return corpOrderInvoiceDetailInfoQueryResponse;
    }

    public QconfigOfRegisterConfig getQconfigOfRegisterConfig() {
        return qconfigOfRegisterConfig;
    }

    public Map<String, StrategyInfo> getStrategyInfoMap() {
        return strategyInfoMap;
    }

    public CountryQueryResponseType getCountryQueryResponseType() {
        return countryQueryResponseType;
    }

    public GetAllNationalityResponseType getGetAllNationalityResponseType() {
        return getAllNationalityResponseType;
    }

    public GetCityBaseInfoResponseType getGetCityBaseInfoResponseType() {
        return getCityBaseInfoResponseType;
    }

    public QconfigOfCertificateInitConfig getQconfigOfCertificateInitConfig() {
        return qconfigOfCertificateInitConfig;
    }
    public String getCostCenterNew() {
        return costCenterNew;
    }

    public QueryAuthResponseType getQueryAuthResponseType() {
        return queryAuthResponseType;
    }

    public SearchTripBasicInfoResponseType getSearchTripBasicInfoResponseTypeOfOriginalOrder() {
        return searchTripBasicInfoResponseTypeOfOriginalOrder;
    }


    public static class Builder {
        private final BookingInitAssembleRequest bookingInitAssembleRequest = new BookingInitAssembleRequest();

        public Builder withBookInitToken(BookInitToken bookInitToken) {
            this.bookingInitAssembleRequest.bookInitToken = bookInitToken;
            return this;
        }

        public Builder withQconfigEntityOfGroupHotelMemberCardRule(QconfigEntityOfGroupHotelMemberCardRule qconfigEntityOfGroupHotelMemberCardRule) {
            this.bookingInitAssembleRequest.qconfigEntityOfGroupHotelMemberCardRule = qconfigEntityOfGroupHotelMemberCardRule;
            return this;
        }
        public Builder withXProductEnquireResponseType(XProductEnquireResponseType xProductEnquireResponseType) {
            this.bookingInitAssembleRequest.xProductEnquireResponseType = xProductEnquireResponseType;
            return this;
        }
        public Builder withBatchSearchClientsInfoResponseType(BatchSearchClientsInfoResponseType batchSearchClientsInfoResponseType) {
            this.bookingInitAssembleRequest.batchSearchClientsInfoResponseType = batchSearchClientsInfoResponseType;
            return this;
        }
        public Builder withSearchRegistrationFieldsResponseType(SearchRegistrationFieldsResponseType searchRegistrationFieldsResponseType) {
            this.bookingInitAssembleRequest.searchRegistrationFieldsResponseType = searchRegistrationFieldsResponseType;
            return this;
        }
        public Builder withGetMultilingualCouponInfoResponseType(GetMultilingualCouponInfoResponseType getMultilingualCouponInfoResponseType) {
            this.bookingInitAssembleRequest.getMultilingualCouponInfoResponseType = getMultilingualCouponInfoResponseType;
            return this;
        }

        public Builder withDistributePaymentAmountResponse(DistributePaymentAmountResponseType distributePaymentAmountResponseType) {
            this.bookingInitAssembleRequest.distributePaymentAmountResponseType = distributePaymentAmountResponseType;
            return this;
        }

        public Builder withBookingInitRequest(BookingInitRequestType bookingInitRequest) {
            this.bookingInitAssembleRequest.bookingInitRequest = bookingInitRequest;
            return this;
        }

        public Builder withCancelPolicyDesc(CancelPolicyDescType cancelPolicyDesc) {
            this.bookingInitAssembleRequest.cancelPolicyDesc = cancelPolicyDesc;
            return this;
        }

        public Builder withGetAuthDelayResponse(GetAuthDelayResponseType getAuthDelayResponse) {
            this.bookingInitAssembleRequest.getAuthDelayResponse = getAuthDelayResponse;
            return this;
        }

        public Builder withGetAllNationalityResponseType(GetAllNationalityResponseType getAllNationalityResponseType) {
            this.bookingInitAssembleRequest.getAllNationalityResponseType = getAllNationalityResponseType;
            return this;
        }

        public Builder withAccountInfo(WrapperOfAccount.AccountInfo accountInfo) {
            this.bookingInitAssembleRequest.accountInfo = accountInfo;
            return this;
        }

        public Builder withQueryHotelOrderDataResponse(QueryHotelOrderDataResponseType queryHotelOrderDataResponse) {
            this.bookingInitAssembleRequest.queryHotelOrderDataResponse = queryHotelOrderDataResponse;
            return this;
        }

        public Builder withGetSupportedPaymentMethodResponse(GetSupportedPaymentMethodResponseType getSupportedPaymentMethodResponse) {
            this.bookingInitAssembleRequest.getSupportedPaymentMethodResponse = getSupportedPaymentMethodResponse;
            return this;
        }

        public Builder withGetGroupVipPackageResponse(GetGroupVipPackageResponseType getGroupVipPackageResponse) {
            this.bookingInitAssembleRequest.getGroupVipPackageResponse = getGroupVipPackageResponse;
            return this;
        }

        public Builder withCtripMemberUserInfoResponse(CtripMemberUserInfoResponse ctripMemberUserInfoResponse) {
            this.bookingInitAssembleRequest.ctripMemberUserInfoResponse = ctripMemberUserInfoResponse;
            return this;
        }

        public Builder withCalculateTravelRewardsResponse(CalculateTravelRewardsResponseType calculateTravelRewardsResponseType) {
            this.bookingInitAssembleRequest.calculateTravelRewardsResponseType = calculateTravelRewardsResponseType;
            return this;
        }

        public Builder withQConfigOfPersonalAccountConfig(PersonalAccountConfig personalAccountConfig) {
            this.bookingInitAssembleRequest.personalAccountConfig = personalAccountConfig;
            return this;
        }

        public Builder withRoomNumberInfoConfig(RoomNumberInfoConfig roomNumberInfoConfig) {
            this.bookingInitAssembleRequest.roomNumberInfoConfig = roomNumberInfoConfig;
            return this;
        }


        public Builder withGetCorpUserHotelVipCardResponse(GetCorpUserHotelVipCardResponseType getCorpUserHotelVipCardResponse) {
            this.bookingInitAssembleRequest.getCorpUserHotelVipCardResponse = getCorpUserHotelVipCardResponse;
            return this;
        }

        public Builder withQueryIndividualAccountResponse(QueryIndividualAccountResponseType queryIndividualAccountResponse) {
            this.bookingInitAssembleRequest.queryIndividualAccountResponseType = queryIndividualAccountResponse;
            return this;
        }

        public Builder withCalculateServiceChargeV2Response(CalculateServiceChargeV2ResponseType calculateServiceChargeV2ResponseType) {
            this.bookingInitAssembleRequest.calculateServiceChargeV2ResponseType = calculateServiceChargeV2ResponseType;
            return this;
        }

        public Builder withReimbursementDetailInfoType(ReimbursementDetailInfoType reimbursementDetailInfoType) {
            this.bookingInitAssembleRequest.reimbursementDetailInfoType = reimbursementDetailInfoType;
            return this;
        }

        public Builder withGetContactInvoiceDefaultInfoResponseType(GetContactInvoiceDefaultInfoResponseType getContactInvoiceDefaultInfoResponseType) {
            this.bookingInitAssembleRequest.getContactInvoiceDefaultInfoResponseType = getContactInvoiceDefaultInfoResponseType;
            return this;
        }

        public Builder withGetInvoiceTitlesResponse(GetInvoiceTitlesResponse getInvoiceTitlesResponse) {
            this.bookingInitAssembleRequest.getInvoiceTitlesResponse = getInvoiceTitlesResponse;
            return this;
        }

        public Builder withGetUserAddressInfoResponseType(GetUserAddressInfoResponseType getUserAddressInfoResponseType) {
            this.bookingInitAssembleRequest.getUserAddressInfoResponseType = getUserAddressInfoResponseType;
            return this;
        }

        public Builder withGetSupportedInvoiceTypeResponseType(GetSupportedInvoiceTypeResponseType getSupportedInvoiceTypeResponseType) {
            this.bookingInitAssembleRequest.getSupportedInvoiceTypeResponseType = getSupportedInvoiceTypeResponseType;
            return this;
        }

        public Builder withResourceToken(ResourceToken resourceToken) {
            this.bookingInitAssembleRequest.resourceToken = resourceToken;
            return this;
        }
        public Builder withRoomPayType(HotelPayTypeEnum roomPayType) {
            this.bookingInitAssembleRequest.roomPayType = roomPayType;
            return this;
        }
        public Builder withServicePayType(HotelPayTypeEnum servicePayType) {
            this.bookingInitAssembleRequest.servicePayType = servicePayType;
            return this;
        }
        public Builder withGetCorpUserInfoResponseType(GetCorpUserInfoResponseType getCorpUserInfoResponseType) {
            this.bookingInitAssembleRequest.getCorpUserInfoResponseType = getCorpUserInfoResponseType;
            return this;
        }
        public Builder withHotelTravelPolicyInfo(WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo hotelTravelPolicyInfo) {
            this.bookingInitAssembleRequest.hotelTravelPolicyInfo = hotelTravelPolicyInfo;
            return this;
        }

        public Builder withCheckAvailInfo(WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo) {
            this.bookingInitAssembleRequest.checkAvailInfo = checkAvailInfo;
            return this;
        }

        public Builder withApprovalInfo(WrapperOfSearchApproval.ApprovalInfo approvalInfo) {
            this.bookingInitAssembleRequest.approvalInfo = approvalInfo;
            return this;
        }

        public Builder withQueryIndividualAccountResponseType(QueryIndividualAccountResponseType queryIndividualAccountResponseType) {
            this.bookingInitAssembleRequest.queryIndividualAccountResponseType = queryIndividualAccountResponseType;
            return this;
        }

        public Builder withGetCorpUserInfoByPolicyResponseType(GetCorpUserInfoResponseType getCorpUserInfoByPolicyResponseType) {
            this.bookingInitAssembleRequest.getGetCorpUserInfoByPolicyResponseType = getCorpUserInfoByPolicyResponseType;
            return this;
        }

        public Builder withApprovalDefaultResponseType(BatchApprovalDefaultResponseType approvalDefaultResponseType) {
            this.bookingInitAssembleRequest.approvalDefaultResponseType = approvalDefaultResponseType;
            return this;
        }

        public Builder withApprovalTextInfoResponseType(ApprovalTextInfoResponseType approvalTextInfoResponseType) {
            this.bookingInitAssembleRequest.approvalTextInfoResponseType = approvalTextInfoResponseType;
            return this;
        }
        public Builder withGetPackageRoomListResponseType(GetPackageRoomListResponseType getPackageRoomListResponseType) {
            this.bookingInitAssembleRequest.getPackageRoomListResponseType = getPackageRoomListResponseType;
            return this;
        }

        public Builder withGetPackageRoomSnapshotResponseType(GetPackageRoomSnapshotResponseType getPackageRoomSnapshotResponseType) {
            this.bookingInitAssembleRequest.getPackageRoomSnapshotResponseType = getPackageRoomSnapshotResponseType;
            return this;
        }
        public Builder withGetHotelDetailInfoResponseType(GetHotelDetailInfoResponseType getHotelDetailInfoResponseType) {
            this.bookingInitAssembleRequest.getHotelDetailInfoResponseType = getHotelDetailInfoResponseType;
            return this;
        }
        public Builder withQConfigOfContactConfig(QConfigOfContactConfig qConfigOfContactConfig) {
            this.bookingInitAssembleRequest.qConfigOfContactConfig = qConfigOfContactConfig;
            return this;
        }
        public Builder withCheckTravelPolicyResponseType(CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
            this.bookingInitAssembleRequest.checkTravelPolicyResponseType = checkTravelPolicyResponseType;
            return this;
        }
        public Builder withGetTripBookingInfosResponseType(GetTripBookingInfosResponseType getTripBookingInfosResponseType) {
            this.bookingInitAssembleRequest.getTripBookingInfosResponseType = getTripBookingInfosResponseType;
            return this;
        }
        public Builder withApprovalOutPut(ApprovalOutput approvalOutput) {
            this.bookingInitAssembleRequest.approvalOutput = approvalOutput;
            return this;
        }
        public Builder withGetPlatformRelationByUidResponseType(GetPlatformRelationByUidResponseType getPlatformRelationByUidResponseType) {
            this.bookingInitAssembleRequest.getPlatformRelationByUidResponseType = getPlatformRelationByUidResponseType;
            return this;
        }
        public Builder withSearchTripDetailResponseType(SearchTripDetailResponseType searchTripDetailResponseType) {
            this.bookingInitAssembleRequest.searchTripDetailResponseType = searchTripDetailResponseType;
            return this;
        }
        public Builder withApprovalInput(ApprovalInput approvalInput) {
            this.bookingInitAssembleRequest.approvalInput = approvalInput;
            return this;
        }
        public Builder withMemberBonusRuleEntries(List<MemberBonusRuleEntry> memberBonusRuleEntries) {
            this.bookingInitAssembleRequest.memberBonusRuleEntries = memberBonusRuleEntries;
            return this;
        }

        public Builder withSelectedRoomPayType(HotelPayTypeEnum selectedRoomPayType) {
            this.bookingInitAssembleRequest.selectedRoomPayType = selectedRoomPayType;
            return this;
        }

        public Builder withPayConfigResponseType(PayConfigResponseType payConfigResponseType) {
            this.bookingInitAssembleRequest.payConfigResponseType = payConfigResponseType;
            return this;
        }

        public Builder withGetRoomChargePolicyListResponseType(GetRoomChargePolicyListResponseType getRoomChargePolicyListResponseType) {
            this.bookingInitAssembleRequest.getRoomChargePolicyListResponseType = getRoomChargePolicyListResponseType;
            return this;
        }

        public Builder withQueryBizModeBindRelationResponseType(QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType) {
            this.bookingInitAssembleRequest.queryBizModeBindRelationResponseType = queryBizModeBindRelationResponseType;
            return this;
        }

        public Builder withGetReasoncodesResponseType(GetReasoncodesResponseType getReasoncodesResponseType) {
            this.bookingInitAssembleRequest.getReasoncodesResponseType = getReasoncodesResponseType;
            return this;
        }

        public Builder withCustomizedSharkConfigList(List<CustomizedSharkConfig> customizedSharkConfigList) {
            this.bookingInitAssembleRequest.customizedSharkConfigList = customizedSharkConfigList;
            return this;
        }

        public Builder withCorpOrderInvoiceDetailInfoQueryResponse(
            CorpOrderInvoiceDetailInfoQueryResponse corpOrderInvoiceDetailInfoQueryResponse) {
            this.bookingInitAssembleRequest.corpOrderInvoiceDetailInfoQueryResponse =
                corpOrderInvoiceDetailInfoQueryResponse;
            return this;
        }

        public Builder withQconfigOfRegisterConfig(QconfigOfRegisterConfig qconfigOfRegisterConfig) {
            this.bookingInitAssembleRequest.qconfigOfRegisterConfig = qconfigOfRegisterConfig;
            return this;
        }

        public Builder withStrategyInfoMap(Map<String, StrategyInfo> strategyInfoMap) {
            this.bookingInitAssembleRequest.strategyInfoMap = strategyInfoMap;
            return this;
        }

        public Builder withCountryQueryResponseType(CountryQueryResponseType countryQueryResponseType) {
            this.bookingInitAssembleRequest.countryQueryResponseType = countryQueryResponseType;
            return this;
        }

        public Builder withGetCityBaseInfoResponseType(GetCityBaseInfoResponseType getCityBaseInfoResponseType) {
            this.bookingInitAssembleRequest.getCityBaseInfoResponseType = getCityBaseInfoResponseType;
            return this;
        }

        public Builder withQconfigOfCertificateInitConfig(
            QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig) {
            this.bookingInitAssembleRequest.qconfigOfCertificateInitConfig = qconfigOfCertificateInitConfig;
            return this;
        }

        public Builder withCostCenterNew(String costCenterNew) {
            this.bookingInitAssembleRequest.costCenterNew = costCenterNew;
            return this;
        }
        public Builder withQueryAuthResponseType(QueryAuthResponseType queryAuthResponseType) {
            this.bookingInitAssembleRequest.queryAuthResponseType = queryAuthResponseType;
            return this;
        }
        public Builder withSearchTripBasicInfoResponseTypeOfOriginalOrder(
            SearchTripBasicInfoResponseType searchTripBasicInfoResponseTypeOfOriginalOrder) {
            this.bookingInitAssembleRequest.searchTripBasicInfoResponseTypeOfOriginalOrder =
                searchTripBasicInfoResponseTypeOfOriginalOrder;
            return this;
        }
        public Builder withGetCorpInfoResponseType(
            GetCorpInfoResponseType getCorpInfoResponseType) {
            this.bookingInitAssembleRequest.getCorpInfoResponseType = getCorpInfoResponseType;
            return this;
        }
        public BookingInitAssembleRequest build() {
            return this.bookingInitAssembleRequest;
        }
    }


}
