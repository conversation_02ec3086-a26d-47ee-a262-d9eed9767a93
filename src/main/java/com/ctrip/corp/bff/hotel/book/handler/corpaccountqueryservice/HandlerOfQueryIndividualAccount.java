package com.ctrip.corp.bff.hotel.book.handler.corpaccountqueryservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.CorpAccountQueryService.CorpAccountQueryServiceClient;
import corp.user.service.CorpAccountQueryService.QueryIndividualAccountRequestType;
import corp.user.service.CorpAccountQueryService.QueryIndividualAccountResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/6/24 7:19
 */
@Component public class HandlerOfQueryIndividualAccount extends
    AbstractHandlerOfSOA<QueryIndividualAccountRequestType, QueryIndividualAccountResponseType, CorpAccountQueryServiceClient> {
    @Override protected String getMethodName() {
        return "queryIndividualAccount";
    }
}
