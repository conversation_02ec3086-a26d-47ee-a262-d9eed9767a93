package com.ctrip.corp.bff.hotel.book.handler.corpbfftoolsservice;

import com.ctrip.corp.bff.framework.template.entity.IntegrationResponse;
import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.bff.tools.contract.CorpBffToolsServiceClient;
import com.ctrip.corp.bff.tools.contract.CountryQueryRequestType;
import com.ctrip.corp.bff.tools.contract.CountryQueryResponseType;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/4/10
 */
@Component
public class HandlerOfCountryQuery extends AbstractHandlerOfSOA<CountryQueryRequestType, CountryQueryResponseType, CorpBffToolsServiceClient> {
    @Override
    protected String getMethodName() {
        return "countryQuery";
    }

    @Override
    protected String getLogErrorCode(CountryQueryResponseType response) {
        return Optional.ofNullable(response).map(CountryQueryResponseType::getIntegrationResponse).map(IntegrationResponse::getErrorCode).orElse(null);
    }
}
