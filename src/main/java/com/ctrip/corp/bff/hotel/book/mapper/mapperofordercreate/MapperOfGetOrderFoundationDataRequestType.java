package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.soa.corp.order.orderindexextservice.v1.GetOrderFoundationDataRequestType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/6/30 12:27
 */
@Component
public class MapperOfGetOrderFoundationDataRequestType
    extends AbstractMapper<Tuple1<OrderCreateRequestType>, GetOrderFoundationDataRequestType> {

    @Override protected GetOrderFoundationDataRequestType convert(Tuple1<OrderCreateRequestType> tuple1) {
        OrderCreateRequestType GetOrderFoundationDataRequestType = tuple1.getT1();
        GetOrderFoundationDataRequestType request = new GetOrderFoundationDataRequestType();
        request.setOrderId(TemplateNumberUtil.parseLong(
            GetOrderFoundationDataRequestType.getApprovalFlowReuseInput().getArtificialReuseNo()));
        return request;
    }

    @Override protected ParamCheckResult check(Tuple1<OrderCreateRequestType> tuple1) {
        return null;
    }
}
