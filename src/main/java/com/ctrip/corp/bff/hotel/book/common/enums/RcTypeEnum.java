package com.ctrip.corp.bff.hotel.book.common.enums;

import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.hotel.book.common.constant.SharkKeyConstant;

/**
 * <AUTHOR>
 * @date 2024/8/21 14:41
 * @description
 */
public enum RcTypeEnum {

    /**
     * 低价RC
     */
    LOW_PRICE("LOW_PRICE", 1),
    /**
     * 协议RC
     */
    AGREEMENT("AGREEMENT",2),
    /**
     * 提前预定RC
     */
    BOOK_AHEAD("BOOK_AHEAD",8),

    /**
     * 重复预订RC
     */
    CONFLICT_BOOK("CONFLICT_BOOK",128),
    /**
     * 修改RC
     */
    MODIFY("MODIFY",16),
    /**
     * offline自定义修改原因
     */
    OFFLINE_MODIFY("OFFLINE_MODIFY", null),
    /**
     * 取消RC
     */
    CANCEL("CANCEL",32),

    /**
     * 申请修改rc
     */
    APPLY_MODIFY("APPLY_MODIFY", 0);

    private final String code;
    /**
     * 酒店rc类型: "低价RC,协议RC,最低价RC,提前预定RC,修改RC,取消RC,未预定酒店RC,重复预订RC,打卡RC",
     * 酒店rc类型枚举: "1,2,4,8,16,32,64,128,256",
     */
    private final Integer rcType;

    RcTypeEnum(String code, Integer rcType) {
        this.code = code;
        this.rcType = rcType;
    }

    public Integer getRcType() {
        return rcType;
    }

    public static Integer getRcTypeByName(String  enumName) {
        RcTypeEnum rcTypeEnum = RcTypeEnum.valueOf(enumName);
        if (rcTypeEnum == null) {
            return null;
        }
        return rcTypeEnum.getRcType();
    }

    /**
     * rc类型
     * LOW_PRICE：低价RC
     * AGREEMENT：协议RC
     * BOOK_AHEAD：提前预定RC
     * CONFLICT_BOOK：重复预订RC
     * OFFLINE_MODIFY：修改原因RC
     * APPLY_MODIFY：申请修改rc
     *
     * @param code
     * @return
     */
    public static RcTypeEnum getType(String code) {
        for (RcTypeEnum type : RcTypeEnum.values()) {
            if (type.getCode().equalsIgnoreCase(code)){
                return type;
            }
        }
        return null;
    }

    public String getCode() {
        return code;
    }

    public String getRcDesc() {
        return BFFSharkUtil.getSharkValue(StringUtil.indexedFormat(SharkKeyConstant.RC_DESC, this.name()));
    }

    public String getRcTitle() {
        return BFFSharkUtil.getSharkValue(StringUtil.indexedFormat(SharkKeyConstant.RC_TITLE, this.name()));
    }
}
