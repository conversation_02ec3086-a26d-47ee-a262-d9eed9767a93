package com.ctrip.corp.bff.hotel.book.common.wrapper;

import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfCityBaseInfo.CityBaseInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType;
import com.ctrip.soa._20184.CheckHotelAuthExtensionResponseType;
import com.ctrip.soa._20184.QueryHotelAuthExtensionResponseType;
import com.ctrip.soa._21234.SearchTripBasicInfoResponseType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderDetailResponseType;
import com.ctrip.soa.corp.order.orderindexextservice.v1.GetOrderFoundationDataResponseType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/26 16:59
 */
public class WrapperOfApprovalFlowReuse {
    private QueryHotelAuthExtensionResponseType queryHotelAuthExtensionResponseType;
    private CheckHotelAuthExtensionResponseType checkHotelAuthExtensionResponseType;
    private OrderCreateRequestType orderCreateRequestType;
    private CityBaseInfo cityBaseInfo;
    private OrderCreateToken orderCreateToken;
    private WrapperOfAccount.AccountInfo accountInfo;
    private OrderDetailResponseType orderDetailResponseTypeOfApprovalFlowReuse;
    private Map<String, StrategyInfo> strategyInfoMap;
    private GetOrderFoundationDataResponseType getOrderFoundationDataResponseType;
    private SearchTripBasicInfoResponseType searchTripBasicInfoResponseTypeOfApprovalFlowReuse;
    private WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo;
    private GetCityBaseInfoResponseType getCityBaseInfoResponseType;
    private GetCityBaseInfoResponseType getCityBaseInfoResponseTypeOfApprovalFlowReuse;
    private QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig;
    private GetCorpUserInfoResponseType policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse;
    private GetCorpUserInfoResponseType policyGetCorpUserInfoResponseType;

    public static class Build {
        private final WrapperOfApprovalFlowReuse wrapperOfApprovalFlowReuse = new WrapperOfApprovalFlowReuse();

        public Build withQueryHotelAuthExtensionResponseType(
            QueryHotelAuthExtensionResponseType queryHotelAuthExtensionResponseType) {
            wrapperOfApprovalFlowReuse.queryHotelAuthExtensionResponseType = queryHotelAuthExtensionResponseType;
            return this;
        }

        public Build withCheckHotelAuthExtensionResponseType(
            CheckHotelAuthExtensionResponseType checkHotelAuthExtensionResponseType) {
            wrapperOfApprovalFlowReuse.checkHotelAuthExtensionResponseType = checkHotelAuthExtensionResponseType;
            return this;
        }

        public Build withOrderCreateRequestType(OrderCreateRequestType orderCreateRequestType) {
            wrapperOfApprovalFlowReuse.orderCreateRequestType = orderCreateRequestType;
            return this;
        }

        public Build withCityBaseInfo(CityBaseInfo cityBaseInfo) {
            wrapperOfApprovalFlowReuse.cityBaseInfo = cityBaseInfo;
            return this;
        }

        public Build withOrderCreateToken(OrderCreateToken orderCreateToken) {
            wrapperOfApprovalFlowReuse.orderCreateToken = orderCreateToken;
            return this;
        }

        public Build withAccountInfo(WrapperOfAccount.AccountInfo accountInfo) {
            wrapperOfApprovalFlowReuse.accountInfo = accountInfo;
            return this;
        }

        public Build withOrderDetailResponseTypeOfApprovalFlowReuse(OrderDetailResponseType orderDetailResponseType) {
            wrapperOfApprovalFlowReuse.orderDetailResponseTypeOfApprovalFlowReuse = orderDetailResponseType;
            return this;
        }

        public Build withStrategyInfoMap(Map<String, StrategyInfo> strategyInfoMap) {
            wrapperOfApprovalFlowReuse.strategyInfoMap = strategyInfoMap;
            return this;
        }

        public Build withGetOrderFoundationDataResponseType(
            GetOrderFoundationDataResponseType getOrderFoundationDataResponseType) {
            wrapperOfApprovalFlowReuse.getOrderFoundationDataResponseType = getOrderFoundationDataResponseType;
            return this;
        }

        public Build withSearchTripBasicInfoResponseTypeOfApprovalFlowReuse(
            SearchTripBasicInfoResponseType searchTripBasicInfoResponseType) {
            wrapperOfApprovalFlowReuse.searchTripBasicInfoResponseTypeOfApprovalFlowReuse =
                searchTripBasicInfoResponseType;
            return this;
        }

        public Build withCheckAvailInfo(WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
            wrapperOfApprovalFlowReuse.checkAvailInfo = checkAvailInfo;
            return this;
        }

        public Build withGetCityBaseInfoResponseType(GetCityBaseInfoResponseType getCityBaseInfoResponseType) {
            wrapperOfApprovalFlowReuse.getCityBaseInfoResponseType = getCityBaseInfoResponseType;
            return this;
        }

        public Build withGetCityBaseInfoResponseTypeOfApprovalFlowReuse(
            GetCityBaseInfoResponseType getCityBaseInfoResponseTypeOfApprovalFlowReuse) {
            wrapperOfApprovalFlowReuse.getCityBaseInfoResponseTypeOfApprovalFlowReuse =
                getCityBaseInfoResponseTypeOfApprovalFlowReuse;
            return this;
        }

        public Build withQconfigOfCertificateInitConfig(QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig) {
            wrapperOfApprovalFlowReuse.qconfigOfCertificateInitConfig = qconfigOfCertificateInitConfig;
            return this;
        }

        public Build withPolicyGetCorpUserInfoResponseTypeOfApprovalFlowReuse(
            GetCorpUserInfoResponseType policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse) {
            wrapperOfApprovalFlowReuse.policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse =
                policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse;
            return this;
        }

        public Build withPolicyGetCorpUserInfoResponseType(
            GetCorpUserInfoResponseType policyGetCorpUserInfoResponseType) {
            wrapperOfApprovalFlowReuse.policyGetCorpUserInfoResponseType = policyGetCorpUserInfoResponseType;
            return this;
        }
        public WrapperOfApprovalFlowReuse build() {
            return wrapperOfApprovalFlowReuse;
        }
    }

    public static WrapperOfApprovalFlowReuse.Build builder() {
        return new WrapperOfApprovalFlowReuse.Build();
    }

    public QueryHotelAuthExtensionResponseType getQueryHotelAuthExtensionResponseType() {
        return queryHotelAuthExtensionResponseType;
    }

    public CheckHotelAuthExtensionResponseType getCheckHotelAuthExtensionResponseType() {
        return checkHotelAuthExtensionResponseType;
    }

    public OrderCreateRequestType getOrderCreateRequestType() {
        return orderCreateRequestType;
    }

    public CityBaseInfo getCityBaseInfo() {
        return cityBaseInfo;
    }

    public OrderCreateToken getOrderCreateToken() {
        return orderCreateToken;
    }

    public WrapperOfAccount.AccountInfo getAccountInfo() {
        return accountInfo;
    }

    public OrderDetailResponseType getOrderDetailResponseTypeOfApprovalFlowReuse() {
        return orderDetailResponseTypeOfApprovalFlowReuse;
    }

    public Map<String, StrategyInfo> getStrategyInfoMap() {
        return strategyInfoMap;
    }

    public GetOrderFoundationDataResponseType getGetOrderFoundationDataResponseType() {
        return getOrderFoundationDataResponseType;
    }

    public SearchTripBasicInfoResponseType getSearchTripBasicInfoResponseTypeOfApprovalFlowReuse() {
        return searchTripBasicInfoResponseTypeOfApprovalFlowReuse;
    }

    public WrapperOfCheckAvail.BaseCheckAvailInfo getCheckAvailInfo() {
        return checkAvailInfo;
    }

    public GetCityBaseInfoResponseType getGetCityBaseInfoResponseType() {
        return getCityBaseInfoResponseType;
    }

    public GetCityBaseInfoResponseType getGetCityBaseInfoResponseTypeOfApprovalFlowReuse() {
        return getCityBaseInfoResponseTypeOfApprovalFlowReuse;
    }

    public QconfigOfCertificateInitConfig getQconfigOfCertificateInitConfig() {
        return qconfigOfCertificateInitConfig;
    }

    public GetCorpUserInfoResponseType getPolicyGetCorpUserInfoResponseTypeOfApprovalFlowReuse() {
        return policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse;
    }

    public GetCorpUserInfoResponseType getPolicyGetCorpUserInfoResponseType() {
        return policyGetCorpUserInfoResponseType;
    }
}
