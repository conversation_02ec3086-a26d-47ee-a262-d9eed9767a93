package com.ctrip.corp.bff.hotel.book.common.enums;

/**
 * @Author: chenchuang
 * @Date: 2024/9/13 16:34
 */
public enum InvoiceContentEnum {

    /**
     * 电话
     */
    TEL("tel"),

    /**
     * 电话掩码
     */
    MASK_TEL("maskTel"),

    /**
     * 地址
     */
    ADDRESS("address"),

    /**
     * 省份
     */
    PROVINCE("province"),

    /**
     * 城市
     */
    CITY("city"),

    /**
     * 地区
     */
    DISTRICT("district"),

    /**
     * 联系人姓名
     */
    CONTACT_NAME("contactName"),

    /**
     * 联系人电话
     */
    CONTACT_TEL("contactTel"),

    /**
     * 联系人地址
     */
    CONTACT_ADDRESS("contactAddress")
    ;


    private String key;

    InvoiceContentEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }

}
