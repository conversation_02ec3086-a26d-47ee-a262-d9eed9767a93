package com.ctrip.corp.bff.hotel.book.handler.corpuserinfoservice4j;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.corpUserInfoService.CorpUserInfoService4jClient;
import corp.user.service.corpUserInfoService.OperateCorpUserHotelVipCardRequestType;
import corp.user.service.corpUserInfoService.OperateCorpUserHotelVipCardResponseType;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/05/29
 */
@Component
public class HandlerOfOperateCorpUserHotelVipCard extends
        AbstractHandlerOfSOA<OperateCorpUserHotelVipCardRequestType, OperateCorpUserHotelVipCardResponseType, CorpUserInfoService4jClient> {
    @Override
    protected String getMethodName() {
        return "operateCorpUserHotelVipCard";
    }
    @Override
    protected String getLogErrorCode(OperateCorpUserHotelVipCardResponseType responseType) {
        return String.valueOf(Optional.ofNullable(responseType)
                .map(OperateCorpUserHotelVipCardResponseType::getRetCode).orElse(0));
    }
}
