package com.ctrip.corp.bff.hotel.book.common.enums.approvalinfo;

/**
 * <AUTHOR>
 * @date 2024-11-14
 **/
public enum ControlInfoTypeEnum {
    /**
     * 城市ID
     */
    CITY_ID("CHECKIN_CITY_CODE"),
    /**
     * 入店开始日期-YYYY-MM-DD
     */
    CHECKIN_DATE_START("CHECKIN_DATE_START"),
    /**
     * 入店结束日期-YYYY-MM-DD
     */
    CHECKIN_DATE_END("CHECKIN_DATE_END"),
    /**
     * 离店结束日期-YYYY-MM-DD
     */
    CHECKOUT_DATE_START("CHECKOUT_DATE_START"),
    /**
     * 离店结束日期-YYYY-MM-DD
     */
    CHECKOUT_DATE_END("CHECKOUT_DATE_END"),
    ;

    private String code;

    ControlInfoTypeEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}
