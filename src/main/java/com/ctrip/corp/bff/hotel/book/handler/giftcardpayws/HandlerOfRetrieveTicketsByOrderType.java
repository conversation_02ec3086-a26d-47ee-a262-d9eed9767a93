package com.ctrip.corp.bff.hotel.book.handler.giftcardpayws;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.microfinance.giftcardpay.ws.contract.GiftcardPayWSClient;
import com.ctrip.microfinance.giftcardpay.ws.contract.RetrieveTicketsByOrderTypeRequestType;
import com.ctrip.microfinance.giftcardpay.ws.contract.RetrieveTicketsByOrderTypeResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/03/24 21:28
 */
@Component public class HandlerOfRetrieveTicketsByOrderType extends
    AbstractHandlerOfSOA<RetrieveTicketsByOrderTypeRequestType, RetrieveTicketsByOrderTypeResponseType, GiftcardPayWSClient> {
    @Override protected String getMethodName() {
        return "retrieveTicketsByOrderType";
    }
}
