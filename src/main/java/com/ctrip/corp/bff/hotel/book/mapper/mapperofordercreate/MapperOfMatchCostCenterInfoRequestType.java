package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput;
import com.ctrip.corp.bff.framework.template.common.threadlocal.ThreadContextUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.framework.foundation.Foundation;
import corp.user.service.costcenterService.matchCostCenter.MatchCostCenterRequestType;
import org.springframework.stereotype.Service;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/17 11:12
 */
@Service public class MapperOfMatchCostCenterInfoRequestType
    extends AbstractMapper<Tuple1<OrderCreateRequestType>, MatchCostCenterRequestType> {

    @Override public MatchCostCenterRequestType convert(Tuple1<OrderCreateRequestType> tuple) {
        OrderCreateRequestType orderCreateRequestType = tuple.getT1();
        MatchCostCenterRequestType matchCostCenterRequestType = new MatchCostCenterRequestType();
        matchCostCenterRequestType.setAppid(Foundation.app().getAppId());
        matchCostCenterRequestType.setSourceFrom(ThreadContextUtil.getSourceFrom().toString());
        matchCostCenterRequestType.setRid(tuple.getT1().getIntegrationSoaRequestType().getRequestId());
        matchCostCenterRequestType.setUid(getCostCenterId(orderCreateRequestType));
        matchCostCenterRequestType.setFilter(1);
        return matchCostCenterRequestType;
    }

    @Override public ParamCheckResult check(Tuple1<OrderCreateRequestType> tuple) {
        return null;
    }

    private String getCostCenterId(OrderCreateRequestType requestType) {
        String policyUid = Optional.ofNullable(requestType.getHotelPolicyInput()).map(HotelPolicyInput::getPolicyInput)
            .map(PolicyInput::getPolicyUid).orElse(null);
        if (StringUtil.isNotBlank(policyUid)) {
            return policyUid;
        }
        return requestType.getIntegrationSoaRequestType().getUserInfo().getUserId();
    }
}
