package com.ctrip.corp.bff.hotel.book.common.enums;

import com.ctrip.corp.bff.hotel.book.common.constant.SharkKeyConstant;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/9/14 10:30
 */
public enum RcCustomizedSharkByCorpEnum {
    /**
     * 价格星级RC（根据贵公司差旅政策，{0}未预订符合价格和星级房型的原因是：）
     */
    LOW_PRICE_R_RC(SharkKeyConstant.LOW_PRICE_R_RC, "LOW_PRICE_ROOM"),

    /**
     * 价格星级RC（根据贵公司差旅政策，{0}未预订符合价格和星级房型的原因是：）
     */
    LOW_PRICE_R_RC_PRICE(SharkKeyConstant.LOW_PRICE_R_RC_PRICE, "LOW_PRICE_ROOM_PRICE"),

    /**
     * 价格星级RC（根据贵公司差旅政策，{0}未预订符合星级房型的原因是：）
     */
    LOW_PRICE_R_RC_STAR(SharkKeyConstant.LOW_PRICE_R_RC_STAR, "LOW_PRICE_ROOM_STAR"),

    /**
     * 低价RC
     */
    LOW_PRICE_RC(SharkKeyConstant.LOW_PRICE_RC, RcTypeEnum.LOW_PRICE.getCode()),

    /**
     * 低价RC
     */
    LOW_PRICE_RC_PRICE(SharkKeyConstant.LOW_PRICE_RC_PRICE, "LOW_PRICE_PRICE"),

    /**
     * 低价RC
     */
    LOW_PRICE_RC_STAR(SharkKeyConstant.LOW_PRICE_RC_STAR, "LOW_PRICE_STAR"),

    /**
     * 协议RC
     */
    C_PRICE_RC(SharkKeyConstant.C_PRICE_RC, RcTypeEnum.AGREEMENT.getCode()),

    /**
     * 提前预定RC
     */
    RESERVATION_RC(SharkKeyConstant.RESERVATION_RC, RcTypeEnum.BOOK_AHEAD.getCode());
    private final String shark;

    private final String rcCode;

    RcCustomizedSharkByCorpEnum(String shark, String rcCode) {
        this.shark = shark;
        this.rcCode = rcCode;
    }

    public String getShark() {
        return shark;
    }

    public String getRcCode() {
        return rcCode;
    }

    public String getScene() {
        return "ROOM_LIST_RC";
    }

    public static RcCustomizedSharkByCorpEnum getNowEnumByRcType(String rcCode) {
        return Arrays.stream(RcCustomizedSharkByCorpEnum.values())
            .filter(o -> o.getRcCode().equalsIgnoreCase(rcCode)).findFirst().orElse(null);
    }
}
