package com.ctrip.corp.bff.hotel.book.qconfig;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.ContactDisplayConfig;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.EmailCorpIdBlackListConfig;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.SourceDisplayConfig;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;

import static com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig.ONE_DOT;
import static com.ctrip.corp.foundation.common.constant.StringConstants.F;
import static com.ctrip.corp.foundation.common.constant.StringConstants.T;

/**
 * @Author: chenchuang
 * @Date: 2024/10/22 22:08
 */
@Component
public class QConfigOfContactConfig {

    public static final String NAME_MODULE = "name";

    public static final String EMAIL_MODULE = "email";

    public static final String PHONE_MODULE = "phone";

    @QConfig("ContactDisplayConfig.json")
    private List<ContactDisplayConfig> contactDisplayConfigs;

    @QConfig("EmailCorpIdBlackListConfig.json")
    private List<EmailCorpIdBlackListConfig> emailCorpIdBlackListConfigs;

    public boolean isShowContactName(String sourceFrom, String corpId, String uid) {
        return isShowContactModule(NAME_MODULE, sourceFrom, corpId, uid);
    }

    public boolean isShowContactEmail(String sourceFrom, String corpId, String uid) {
        return isShowContactModule(EMAIL_MODULE, sourceFrom, corpId, uid);

    }

    public boolean isShowContactPhone(String sourceFrom, String corpId, String uid) {
        return isShowContactModule(PHONE_MODULE, sourceFrom, corpId, uid);
    }

    public boolean isShowEmailByCorpBlackList(String corpId) {
        if (CollectionUtil.isEmpty(emailCorpIdBlackListConfigs)) {
            return true;
        }
        return emailCorpIdBlackListConfigs.stream()
                .noneMatch(o -> StringUtil.equalsIgnoreCase(o.getCorpId(), corpId));
    }

    protected boolean isShowContactModule(String moduleName, String sourceFrom, String corpId, String uid) {
        if (StringUtil.isBlank(sourceFrom)) {
            return false;
        }
        if (StringUtil.isBlank(moduleName)) {
            return false;
        }
        SourceDisplayConfig displayModel = getSourceDisplayConfig(moduleName, sourceFrom);
        if (displayModel == null) {
            return false;
        }
        return checkSupport(displayModel.getUnSupports(), displayModel.getSupports(), corpId, uid);
    }

    protected SourceDisplayConfig getSourceDisplayConfig(String moduleName, String sourceFrom) {
        ContactDisplayConfig contactDisplayConfig = contactDisplayConfigs.stream().filter(config -> Objects.equals(moduleName, config.getModuleName()))
                .findAny().orElse(null);
        if (contactDisplayConfig == null || CollectionUtils.isEmpty(contactDisplayConfig.getSourceDisplayConfigs())) {
            return null;
        }
        return contactDisplayConfig.getSourceDisplayConfigs().stream().filter(sourceDisplayConfig -> Objects.equals(sourceFrom, sourceDisplayConfig.getSourceFrom()))
                .findAny().orElse(null);
    }

    /**
     * value 是否在key对应的配置中,根据splitRegex 分割字符串
     *
     * @return
     */
    public boolean checkSupport(String not_supports, String supports, String corpId, String uid) {
        if (StringUtil.isNotBlank(not_supports)) {
            if (StringUtil.equalsIgnoreCase(not_supports, T)) {
                return false;
            }
            if (StringUtil.equalsIgnoreCase(not_supports, F)) {
                return true;
            }
            if (CollectionUtil.containsIgnoreCase(split(not_supports, ONE_DOT), corpId)
                    || CollectionUtil.containsIgnoreCase(split(not_supports, ONE_DOT), uid)) {
                return false;
            }
        }

        if (StringUtil.isNotBlank(supports)) {
            if (StringUtil.equalsIgnoreCase(supports, T)) {
                return true;
            }
            if (StringUtil.equalsIgnoreCase(supports, F)) {
                return false;
            }

            if (CollectionUtil.containsIgnoreCase(split(supports, ONE_DOT), corpId)
                    || CollectionUtil.containsIgnoreCase(split(supports, ONE_DOT), uid)) {
                return true;
            }
        }

        return false;
    }

    public static List<String> split(String strValue, String regex) {
        if (StringUtil.isBlank(strValue)) {
            return new ArrayList<>();
        }
        return Arrays.asList(strValue.split(regex));
    }

}
