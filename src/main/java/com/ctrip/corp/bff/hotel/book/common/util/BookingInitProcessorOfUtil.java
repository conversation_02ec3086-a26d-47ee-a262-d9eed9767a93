package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.agg.hotel.roomavailable.entity.*;
import com.ctrip.corp.bff.framework.hotel.common.util.CityInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.WaitFutureUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.OriginalOrderInput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelGeoInfoResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.enums.BooleanValueEnum;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.handler.WaitFuture;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.order.data.aggregation.query.contract.OrderBasicInfoType;
import com.ctrip.corp.order.data.aggregation.query.contract.ApprovalType;
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataRequestType;
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataResponseType;
import com.ctrip.corp.bff.hotel.book.contract.TripInfoInput;
import com.ctrip.corp.foundation.common.util.NumberUtil;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderDetailInfoType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderDetailResponseType;
import org.apache.commons.collections4.CollectionUtils;
import com.ctrip.corp.foundation.common.util.NumberUtil;
import com.ctrip.corp.order.data.aggregation.query.contract.TravelInfoType;
import corp.user.service.group4j.accounts.BizModeBindRelationData;
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: yfx
 * @Date: 2018/8/27
 */
public class BookingInitProcessorOfUtil {


    /**
     * 是否获取差标
     *
     * @return
     */
    public static boolean needGetCorpUserInfoOfPolicy(BookingInitRequestType bookingInitRequestType
            , WrapperOfAccount.AccountInfo accountInfo) {
        if (CorpPayInfoUtil.isPrivate(bookingInitRequestType.getCorpPayInfo())) {
            return false;
        }

        if (BooleanUtil.isFalse(accountInfo.isPolicyModel())){
            return false;
        }
        return true;
    }


    /**
     * 是否获取差标
     *
     * @return
     */
    public static boolean needGetHotelTravelPolicy(BookingInitRequestType bookingInitRequestType
            , WrapperOfAccount.AccountInfo accountInfo) {
        if (CorpPayInfoUtil.isPrivate(bookingInitRequestType.getCorpPayInfo())) {
            return false;
        }
        return true;
    }

    /**
     * 是否获取审批单信息
     *
     * @return
     */
    public static boolean needSearchApproval(BookingInitRequestType bookingInitRequestType,
                                             WaitFuture<QueryHotelOrderDataRequestType, QueryHotelOrderDataResponseType> queryHotelOrderDataResponseTypeWaitFuture,
                                             ResourceToken resourceToken,
                                             WrapperOfAccount.AccountInfo accountInfo) {
        if (CorpPayInfoUtil.isPrivate(bookingInitRequestType.getCorpPayInfo())) {
            return false;
        }
        String subApprovalNoRequest =
                Optional.ofNullable(bookingInitRequestType.getApprovalInput()).map(ApprovalInput::getSubApprovalNo)
                        .orElse(null);
        // 非提前审批不需要查询单据---应对单点场景，前端传入的审批单号是关联行程号，但是酒店压根没开提前审批的场景
        if (accountInfo.isPreApprovalRequired(CityInfoUtil.oversea(
                Optional.ofNullable(resourceToken).map(ResourceToken::getHotelResourceToken)
                        .map(HotelResourceToken::getHotelGeoInfoResourceToken).map(HotelGeoInfoResourceToken::getCityId)
                        .orElse(null)), bookingInitRequestType.getCorpPayInfo()) && StringUtil.isNotBlank(subApprovalNoRequest)) {
            return true;
        }
        String subApprovalNo = Optional.ofNullable(WaitFutureUtil.safeGetFuture(queryHotelOrderDataResponseTypeWaitFuture))
                .map(QueryHotelOrderDataResponseType::getTravelInfo)
                .map(TravelInfoType::getApproval)
                .map(ApprovalType::getSubPreApprovalNo).orElse(null);
        return StringUtil.isNotBlank(subApprovalNo);
    }

    public static boolean needGetCorpUserHotelVipCard(IntegrationSoaRequestType integrationSoaRequestType,
                                                      QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType,
                                                      List<StrategyInfo> strategyInfos,
                                                      List<HotelBookPassengerInput> hotelBookPassengerInputs) {
        HotelBookPassengerInput hotelPassengerInput = CollectionUtil.findFirst(hotelBookPassengerInputs,
                Objects::nonNull);
        if ((hotelPassengerInput != null && hotelPassengerInput.getHotelPassengerInput() != null && StringUtil.isNotBlank(hotelPassengerInput.getHotelPassengerInput().getUid()))
                && StrategyOfBookingInitUtil.needFirstPassengerMembershipCard(strategyInfos)) {
            return true;
        }
        if (StrategyOfBookingInitUtil.needFirstPassengerMembershipCard(strategyInfos)) {
            return false;
        }
        String userId = RequestHeaderUtil.getUserId(integrationSoaRequestType);
        if (queryBizModeBindRelationResponseType == null) {
            return false;
        }
        BizModeBindRelationData bizModeBindRelationData = CollectionUtil.findFirst(queryBizModeBindRelationResponseType.getBizModeBindRelationDataList(),
                t -> t != null && StringUtil.equalsIgnoreCase(userId, t.getDimensionId()));
        if (bizModeBindRelationData == null || StringUtil.isBlank(bizModeBindRelationData.getPrimaryDimensionId())) {
            return false;
        }
        return true;
    }

    /**
     * 是否获取默认审批单信息
     *
     * @return
     */
    public static boolean needBatchApprovalDefault(BookingInitRequestType bookingInitRequestType
            , WrapperOfAccount.AccountInfo accountInfo, ResourceToken resourceToken) {
        if (CorpPayInfoUtil.isPrivate(bookingInitRequestType.getCorpPayInfo())) {
            return false;
        }
        Integer cityId = Optional.ofNullable(resourceToken).map(ResourceToken::getHotelResourceToken)
            .map(HotelResourceToken::getHotelGeoInfoResourceToken).map(HotelGeoInfoResourceToken::getCityId)
            .orElse(null);
        if (accountInfo.isPreApprovalRequired(CityInfoUtil.oversea(cityId), bookingInitRequestType.getCorpPayInfo())) {
            return true;
        }
        if (accountInfo.isTravelApplyRequired(CityInfoUtil.oversea(cityId), bookingInitRequestType.getCorpPayInfo())) {
            return true;
        }
        return false;
    }

    /**
     * 是否订单配置
     *
     * @return
     */
    public static boolean needQueryHotelOrderData(Long orderId) {
        if (orderId == null) {
            return false;
        }
        return true;
    }

    /**
     * 是否获取服务费
     *
     * @return
     */
    public static boolean needCalculateServiceChargeV2(BookingInitRequestType bookingInitRequestType) {
        if (CorpPayInfoUtil.isPrivate(bookingInitRequestType.getCorpPayInfo())) {
            return false;
        }
        return true;
    }

    /**
     * 是否需要查询套餐快照
     *
     * @param resourceToken
     * @param checkAvailInfo
     * @return
     */
    public static boolean needGetPackageRoomSnapshot(ResourceToken resourceToken, WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo) {
        BooleanValueEnum booleanValueEnum = Optional.ofNullable(resourceToken)
            .map(ResourceToken::getRoomResourceToken)
            .map(RoomResourceToken::getSupportAccommodation)
            .orElse(null);
        List<String> tokens = Optional.ofNullable(checkAvailInfo)
            .map(WrapperOfCheckAvail.CheckAvailInfo::getRoomItem)
            .map(RoomItem::getXProductInfo)
            .map(XProductInfoType::getXProductEntity)
            .orElse(new ArrayList<>())
            .stream()
            .filter(entity -> entity.getXProductType() == 1)
            .map(XProductEntityType::getXProductToken).collect(Collectors.toList());
        return booleanValueEnum == BooleanValueEnum.T && !CollectionUtils.isEmpty(tokens);
    }

    /**
     * 是否需要查询房型套餐
     *
     * @param checkAvailInfo
     * @return
     */
    public static boolean needGetPackageRoomList(WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo) {
        if (Optional.ofNullable(checkAvailInfo)
            .map(WrapperOfCheckAvail.CheckAvailInfo::getRoomItem)
            .map(RoomItem::getPackageRoomInfo).map(PackageRoomInfoType::getPackageId).orElse(0) > 0) {
            return true;
        }
        if (StringUtil.isNotBlank(Optional.ofNullable(checkAvailInfo)
                .map(WrapperOfCheckAvail.CheckAvailInfo::getRoomItem)
                .map(RoomItem::getPackageRoomInfo).map(PackageRoomInfoType::getPackageRoomToken).orElse(null))) {
            return true;
        }
        return false;
    }

    /**
     * 是否需要查询会员注册信息
     * @return
     */
    public static boolean needSearchRegistrationFields(WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo) {
        return Objects.nonNull(Optional.ofNullable(checkAvailInfo)
                .map(WrapperOfCheckAvail.CheckAvailInfo::getHotelItem)
                .map(HotelItem::getHotelBrandInfo)
                .map(HotelBrandItem::getGroupId)
                .orElse(null)) && Optional.ofNullable(checkAvailInfo)
            .map(WrapperOfCheckAvail.CheckAvailInfo::getRoomItem)
            .map(RoomItem::getRoomAttributes)
            .map(RoomAttributesType::isCanRegister).orElse(false);
    }
    public static boolean needCheckTravelPolicy(BookingInitRequestType bookingInitRequestType) {
        return CorpPayInfoUtil.isPublic(bookingInitRequestType.getCorpPayInfo());
    }

    /**
     * 是否需要查询保单信息
     *
     * @return
     */
    public static boolean needXProductEnquire(Long orderId) {
        return TemplateNumberUtil.isNotZeroAndNull(orderId);
    }


    public static boolean needGetTripBookingInfos(BookingInitRequestType bookingInitRequestType) {
        Long tripId = NumberUtil.parseLong(Optional.ofNullable(bookingInitRequestType)
            .map(BookingInitRequestType::getTripInfoInput)
            .map(TripInfoInput::getTripId)
            .orElse(null));
        return tripId != null && tripId > 0;
    }

    public static boolean needSearchTripDetail(BookingInitRequestType bookingInitRequestType) {
        Long tripId = NumberUtil.parseLong(Optional.ofNullable(bookingInitRequestType)
                .map(BookingInitRequestType::getTripInfoInput)
                .map(TripInfoInput::getTripId)
                .orElse(null));
        return tripId != null && tripId > 0;
    }

    public static boolean needReimbursementQuery(Long orderId) {
        return orderId != null;
    }

    public static boolean needGetPlatformRelationByUid(IntegrationSoaRequestType integrationSoaRequestType,
                                                       QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType) {
        String uid = Optional.ofNullable(integrationSoaRequestType).map(IntegrationSoaRequestType::getUserInfo).map(UserInfo::getUserId).orElse(null);
        if (queryBizModeBindRelationResponseType == null || StringUtil.isBlank(uid)) {
            return false;
        }
        BizModeBindRelationData bizModeBindRelationData = CollectionUtil.findFirst(queryBizModeBindRelationResponseType.getBizModeBindRelationDataList(),
                t -> t != null && StringUtil.equalsIgnoreCase(uid, t.getDimensionId()));
        if (bizModeBindRelationData == null || StringUtil.isBlank(bizModeBindRelationData.getPrimaryDimensionId())) {
            return false;
        }
        return true;
    }


    /**
     * 不需要注册
     */
    public static final String NO_REGISTER = "NO_REGISTER";
    public static boolean needQueryMrgMemberUserInfo(WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo) {
        GroupMemberShipType groupMemberShipType = Optional.ofNullable(checkAvailInfo)
            .map(WrapperOfCheckAvail.CheckAvailInfo::getBookingRules)
            .map(BookingRulesType::getGroupMemberShipInfo)
            .orElse(null);
        RoomItem roomItem = Optional.ofNullable(checkAvailInfo)
            .map(WrapperOfCheckAvail.CheckAvailInfo::getRoomItem)
            .orElse(null);
        Integer groupId = Optional.ofNullable(checkAvailInfo)
            .map(WrapperOfCheckAvail.CheckAvailInfo::getHotelItem)
            .map(HotelItem::getHotelBrandInfo)
            .map(HotelBrandItem::getGroupId)
            .orElse(null);

        if (groupMemberShipType == null || roomItem == null) {
            return false;
        }
        if (!Optional.ofNullable(roomItem.getRoomAttributes()).map(RoomAttributesType::isGroupMemberShip).orElse(false)) {
            return false;
        }
        if (!BooleanUtils.isTrue(groupMemberShipType.isNeedRegister()) ||
            StringUtil.equalsIgnoreCase(groupMemberShipType.getGroupRegisterRule(), NO_REGISTER)) {
            return false;
        }
        if (Optional.ofNullable(groupId).orElse(0) <= 0) {
            return false;
        }
        return true;
    }

    public static boolean needCorpOrderInvoiceInfo(Map<String, StrategyInfo> strategyInfoMap) {
        return StrategyOfBookingInitUtil.needInvoice(strategyInfoMap);
    }

    public static boolean needGetRoomChargePolicyList(CorpPayInfo corpPayInfo) {
        return CorpPayInfoUtil.isPublic(corpPayInfo);
    }


    public static boolean needCountryQuery(List<StrategyInfo> strategyInfos,
                                           IntegrationSoaRequestType integrationSoaRequestType,
                                           NationalityRestrictionType nationalityRestrictionType) {
        if (BooleanUtils.isNotTrue(StrategyOfBookingInitUtil.needNationalityRestriction(strategyInfos))) {
            return false;
        }
        if (integrationSoaRequestType == null || nationalityRestrictionType == null) {
            return false;
        }
        if (CollectionUtil.isNotEmpty(nationalityRestrictionType.getBlockCountryCodeList())
                && nationalityRestrictionType.getBlockCountryCodeList().stream().anyMatch(StringUtil::isNotBlank)) {
            return true;
        }
        if (CollectionUtil.isNotEmpty(nationalityRestrictionType.getAllowCountryCodeList())
                && nationalityRestrictionType.getAllowCountryCodeList().stream().anyMatch(StringUtil::isNotBlank)) {
            return true;
        }
        return false;
    }

    public static boolean needGetAllNationality(List<StrategyInfo> strategyInfos) {
        return StrategyOfBookingInitUtil.needContractCountryCodeLimitInfo(strategyInfos);
    }

    public static boolean needGetReasoncodes(Map<String, StrategyInfo> strategyInfoMap) {
        return StrategyOfBookingInitUtil.hotelCheckAvail(strategyInfoMap) || StrategyOfBookingInitUtil.needRcInfo(strategyInfoMap);
    }

    public static boolean needGeneralSearchAccountInfoOfPolicy(PolicyInput policyInput) {
        return policyInput != null && StringUtil.isNotBlank(policyInput.getPolicyUid());
    }
    public static boolean needSearchTripBasicInfoOfOriginalOrderId(
        QueryHotelOrderDataResponseType queryHotelOrderDataResponseType) {
        if (TemplateNumberUtil.isNotZeroAndNull(
            Optional.ofNullable(queryHotelOrderDataResponseType).map(QueryHotelOrderDataResponseType::getOrderBasicInfo)
                .map(OrderBasicInfoType::getTripOrderId).orElse(null))) {
            return true;
        }
        return false;
    }
}
