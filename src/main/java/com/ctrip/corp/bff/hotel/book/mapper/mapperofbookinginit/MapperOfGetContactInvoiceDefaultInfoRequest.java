package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.hotel.book.common.enums.BookingInitErrorEnum;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.soa.platform.sps.InvoiceService.v1.GetInvoiceTitlesRequest;
import corp.user.service.corpUserInfoService.GetContactInvoiceDefaultInfoRequestType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 发票抬头响应
 * @Date 2024/8/22 10:01
 * @Version 1.0
 */
@Component
public class MapperOfGetContactInvoiceDefaultInfoRequest extends
    AbstractMapper<Tuple1<BookingInitRequestType>, GetContactInvoiceDefaultInfoRequestType> {

    private static final String HOTEL_PRODUCT = "H";
    @Override
    protected GetContactInvoiceDefaultInfoRequestType convert(Tuple1<BookingInitRequestType> tuple) {
        BookingInitRequestType bookingInitRequestType = tuple.getT1();
        GetContactInvoiceDefaultInfoRequestType result = new GetContactInvoiceDefaultInfoRequestType();

        String uid = RequestHeaderUtil.getUserId(bookingInitRequestType.getIntegrationSoaRequestType());
        result.setUid(uid);
        result.setDefaultType(HOTEL_PRODUCT);
        return result;
    }

    @Override
    protected ParamCheckResult check(Tuple1<BookingInitRequestType> tuple) {
        if (tuple == null || tuple.getT1() == null) {
            return new ParamCheckResult(false,
                BookingInitErrorEnum.MAPPER_PARAM_CHECK_ERROR,
                this.getClass().getName() + " error");
        }
        return null;
    }
}
