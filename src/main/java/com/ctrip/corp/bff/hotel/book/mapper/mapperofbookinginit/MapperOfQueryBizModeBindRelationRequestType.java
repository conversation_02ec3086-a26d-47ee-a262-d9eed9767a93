package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;

import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.contract.MembershipInfo;
import com.ctrip.framework.foundation.Foundation;
import corp.user.service.group4j.accounts.QueryBizModeBindRelationRequestType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/12/27
 */
@Component
public class MapperOfQueryBizModeBindRelationRequestType extends AbstractMapper<Tuple2<IntegrationSoaRequestType, MembershipInfo>, QueryBizModeBindRelationRequestType> {
    private static final String USER = "USER";

    private static final String ONLY_PRIMARY_ID = "ONLY_PRIMARY_ID";

    @Override
    protected QueryBizModeBindRelationRequestType convert(Tuple2<IntegrationSoaRequestType, MembershipInfo> para) {
        if (para == null) {
            return null;
        }
        IntegrationSoaRequestType integrationSoaRequestType = para.getT1();
        MembershipInfo membershipInfo = para.getT2();
        List<String> uids = buildUids(integrationSoaRequestType, membershipInfo);
        if (CollectionUtil.isEmpty(uids)) {
            return null;
        }
        QueryBizModeBindRelationRequestType res = new QueryBizModeBindRelationRequestType();
        res.setAppId(Foundation.app().getAppId());
        res.setOperator(Optional.ofNullable(integrationSoaRequestType).map(IntegrationSoaRequestType::getUserInfo).map(UserInfo::getUserId).orElse(null));
        res.setDimension(USER);
        res.setDimensionIds(uids);
        res.setQueryType(ONLY_PRIMARY_ID);
        return res;
    }

    protected List<String> buildUids(IntegrationSoaRequestType integrationSoaRequestType, MembershipInfo membershipInfo) {
        Set<String> res = new HashSet<>();
        String uid = Optional.ofNullable(integrationSoaRequestType).map(IntegrationSoaRequestType::getUserInfo).map(UserInfo::getUserId).orElse(null);
        if (StringUtils.isNotBlank(uid)) {
            res.add(uid);
        }
        String memberUid = Optional.ofNullable(membershipInfo).map(MembershipInfo::getMembershipUid).orElse(null);
        if (StringUtils.isNotBlank(memberUid)) {
            res.add(memberUid);
        }
        return res.stream().toList();

    }

    @Override
    protected ParamCheckResult check(Tuple2<IntegrationSoaRequestType, MembershipInfo> para) {
        return null;
    }
}
