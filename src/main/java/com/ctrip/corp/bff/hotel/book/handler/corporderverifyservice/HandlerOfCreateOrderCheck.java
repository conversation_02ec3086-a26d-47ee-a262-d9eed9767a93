package com.ctrip.corp.bff.hotel.book.handler.corporderverifyservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.soa._24373.CorpOrderVerifyServiceClient;
import com.ctrip.soa._24373.CreateOrderCheckRequestType;
import com.ctrip.soa._24373.CreateOrderCheckResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/1/3 19:40
 */
@Component
public class HandlerOfCreateOrderCheck extends
    AbstractHandlerOfSOA<CreateOrderCheckRequestType, CreateOrderCheckResponseType, CorpOrderVerifyServiceClient> {

    @Override
    protected String getMethodName() {
        return "createOrderCheck";
    }
}
