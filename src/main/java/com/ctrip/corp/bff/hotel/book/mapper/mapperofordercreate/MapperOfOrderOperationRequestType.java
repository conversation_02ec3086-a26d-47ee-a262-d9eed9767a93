package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.order.data.aggregation.query.contract.OrderOperationRequestType;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/7 16:53
 */
@Component public class MapperOfOrderOperationRequestType
    extends AbstractMapper<Tuple3<IntegrationSoaRequestType, List<String>, Long>, OrderOperationRequestType> {
    @Override protected OrderOperationRequestType convert(Tuple3<IntegrationSoaRequestType, List<String>, Long> tuple) {
        OrderOperationRequestType orderOperationRequestType = new OrderOperationRequestType();
        orderOperationRequestType.setOrderId(tuple.getT3());
        orderOperationRequestType.setRequestId(tuple.getT1().getRequestId());
        orderOperationRequestType.setOperationList(tuple.getT2());
        orderOperationRequestType.setUid(tuple.getT1().getUserInfo().getUserId());
        return orderOperationRequestType;
    }

    @Override protected ParamCheckResult check(Tuple3<IntegrationSoaRequestType, List<String>, Long> tuple) {
        return null;
    }
}
