package com.ctrip.corp.bff.hotel.book.handler.triporderservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.soa._21234.GetUserLiteInfoRequestType;
import com.ctrip.soa._21234.GetUserLiteInfoResponseType;
import com.ctrip.soa._21234.TripOrderServiceClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/10/22 11:04
 */

@Component
public class HandlerOfGetUserLiteInfo
    extends AbstractHandlerOfSOA<GetUserLiteInfoRequestType, GetUserLiteInfoResponseType, TripOrderServiceClient> {
    @Override protected String getMethodName() {
        return "getUserLiteInfo";
    }
}
