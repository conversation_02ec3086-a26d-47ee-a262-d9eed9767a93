package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response;

import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PassengerBasicInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.hotel.book.common.constant.SoaErrorSharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.ControlElementEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail.BaseCheckAvailInfo;
import com.ctrip.corp.bff.hotel.book.contract.SimpleApprovalOutput;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.ElementInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.VerifyFellowInfo;
import com.ctrip.corp.bff.hotel.book.contract.VerifyFellowPassengerInfo;
import com.ctrip.corp.bff.hotel.book.contract.VerifyPassengerInfo;
import com.ctrip.corp.bff.hotel.book.contract.VerifyResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.ResponseStatus;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.verifyfellowpassenger.VerifyFellowPassengerResponseType;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.verifyfellowpassenger.VerifyFellowResult;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.verifyfellowpassenger.VerifyFieldResult;
import com.ctrip.corp.foundation.common.constant.ResponseCode;
import org.apache.commons.lang3.math.NumberUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ctrip.corp.bff.hotel.book.common.enums.ControlElementEnum.TO_CITIES;

/**
 * <AUTHOR>
 * @Description 同行程校验
 * @Date 2024/7/13 12:10
 * @Version 1.0
 */
@Component
public class MapperOfVerifyFellowResultResponse
        extends AbstractMapper<Tuple5<VerifyFellowPassengerResponseType, OrderCreateRequestType, WrapperOfCheckAvail.BaseCheckAvailInfo,
    QconfigOfCertificateInitConfig,
    Map<String, StrategyInfo>>
        , Tuple2<Boolean, OrderCreateResponseType>> {
    private static final Integer VERIFY_PASS = 10303000;
    private static final Integer VERIFY_NONEED = 103110001;
    private static final Integer SHOW_PASSENGER_VERIFY_FAIL_TOAST = 10303016;
    private static final Integer SHOW_PASSENGER_VERIFY_FAIL = 103110002;
    private static final List<Integer> SHOW_PASSENGER_VERIFY_INFO = Arrays.asList(103110003, 103110004);

    private static final String FELLOWCONTROL_FIELDNAME_CITY = "cityID";
    private static final String FELLOWCONTROL_FIELDNAME_STARTDATE = "startDate";
    private static final String FELLOWCONTROL_FIELDNAME_ENDDATE = "endDate";

    public static final String SHARK_PREFIX = "PreApprovalServiceClient_verifyFellowPassenger_";

    @Override
    protected Tuple2<Boolean, OrderCreateResponseType> convert(
        Tuple5<VerifyFellowPassengerResponseType, OrderCreateRequestType, BaseCheckAvailInfo,
                    QconfigOfCertificateInitConfig,
                    Map<String, StrategyInfo>> verifyFellowPassengerResponseTypeTuple1) {
        OrderCreateResponseType orderCreateResponseType = new  OrderCreateResponseType();
        Tuple2<Boolean, OrderCreateResponseType> result = Tuple2.of(false, orderCreateResponseType);
        VerifyFellowPassengerResponseType verifyResponseType = verifyFellowPassengerResponseTypeTuple1.getT1();
        OrderCreateRequestType orderCreateRequestType = verifyFellowPassengerResponseTypeTuple1.getT2();
        WrapperOfCheckAvail.BaseCheckAvailInfo queryCheckAvailContextResponseType = verifyFellowPassengerResponseTypeTuple1.getT3();
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig = verifyFellowPassengerResponseTypeTuple1.getT4();
        Map<String, StrategyInfo> strategyInfoMap = verifyFellowPassengerResponseTypeTuple1.getT5();

        // 校验同行人接口响应数据有误
        int errorCode = Optional.ofNullable(verifyResponseType).map(VerifyFellowPassengerResponseType::getStatus).map(
            ResponseStatus::getErrorCode).orElse(NumberUtils.INTEGER_ZERO);
        if (errorCode == 0) {
            throw BusinessExceptionBuilder.createAlertException(
                    OrderCreateErrorEnum.VERIFY_FELLOW_PASSENGER_RESPONSE_TYPE_IS_NULL.getErrorCode(),
                    OrderCreateErrorEnum.VERIFY_FELLOW_PASSENGER_RESPONSE_TYPE_IS_NULL.getErrorMessage(),
                BFFSharkUtil.getSharkValue(SHARK_PREFIX + errorCode), String.valueOf(errorCode));
        }

        // 校验通过： 10303000 非校验场景： 103110001 这两种都属于通过的code，非这两个都是失败
        if (VERIFY_PASS.equals(errorCode) || VERIFY_NONEED.equals(errorCode)) {
            return result;
        }

        // 错误弹窗以Toast形式返回
        if (SHOW_PASSENGER_VERIFY_FAIL_TOAST.equals(errorCode)) {
            throw BusinessExceptionBuilder.createToastException(
                OrderCreateErrorEnum.VERIFY_FELLOW_PASSENGER_RESPONSE_TYPE_FAIL_TOAST.getErrorCode(),
                OrderCreateErrorEnum.VERIFY_FELLOW_PASSENGER_RESPONSE_TYPE_FAIL_TOAST.getErrorMessage(),
                BFFSharkUtil.getSharkValue(SHARK_PREFIX + errorCode), String.valueOf(errorCode));
        }

        // 失败弹框拼人名
        if (SHOW_PASSENGER_VERIFY_FAIL.equals(errorCode)) {
            List<String> passengerNames = Optional.ofNullable(orderCreateRequestType)
                .map(OrderCreateRequestType::getHotelBookPassengerInputs)
                .orElse(new ArrayList<>()).stream().filter(Objects::nonNull)
                .map(hotelBookPassengerInput -> OrderCreateProcessorOfUtil.getUseName(
                    hotelBookPassengerInput,
                    Optional.ofNullable(orderCreateRequestType.getCityInput())
                        .map(CityInput::getCityId).orElse(null),
                    queryCheckAvailContextResponseType, qconfigOfCertificateInitConfig, strategyInfoMap)).toList();
            String passengerNameStr = Optional.ofNullable(
                String.join(", ", passengerNames)).orElse(StringUtil.EMPTY);
            throw BusinessExceptionBuilder.createAlertException(
                OrderCreateErrorEnum.VERIFY_FELLOW_PASSENGER_RESPONSE_TYPE_FAIL.getErrorCode(),
                OrderCreateErrorEnum.VERIFY_FELLOW_PASSENGER_RESPONSE_TYPE_FAIL.getErrorMessage(),
                StringUtil.indexedFormat(
                    BFFSharkUtil.getSharkValue(SHARK_PREFIX + errorCode),
                    passengerNameStr),
                String.valueOf(errorCode));
        }

        if (SHOW_PASSENGER_VERIFY_INFO.contains(errorCode)) {
            List<VerifyFellowPassengerInfo> verifyFellowPassengerInfos = new ArrayList<>();
            Optional.ofNullable(orderCreateRequestType.getHotelBookPassengerInputs())
                .orElse(new ArrayList<>()).stream().filter(Objects::nonNull)
                .forEach(hotelBookPassengerInput -> {
                VerifyFellowPassengerInfo verifyFellowPassengerInfo = new VerifyFellowPassengerInfo();

                VerifyPassengerInfo verifyPassengerInfo = new VerifyPassengerInfo();
                VerifyFellowResult matchedVerifyFellowResult = getMatchedVerifyFellowResult(
                    hotelBookPassengerInput,
                    Optional.ofNullable(verifyResponseType)
                        .map(VerifyFellowPassengerResponseType::getVerifyResult).orElse(null));
                verifyPassengerInfo.setSimpleApprovalOutput(
                    buildApprovalOutput(matchedVerifyFellowResult, hotelBookPassengerInput));
                verifyPassengerInfo.setApprovalOutput(
                            buildApprovalOutput(matchedVerifyFellowResult, hotelBookPassengerInput));
                verifyPassengerInfo.setUseName(
                    OrderCreateProcessorOfUtil.getUseName(
                        hotelBookPassengerInput,
                        Optional.ofNullable(orderCreateRequestType.getCityInput())
                            .map(CityInput::getCityId).orElse(null),
                        queryCheckAvailContextResponseType, qconfigOfCertificateInitConfig, strategyInfoMap));

                verifyFellowPassengerInfo.setVerifyPassengerInfo(verifyPassengerInfo);
                verifyFellowPassengerInfo.setVerifyResults(getVerifyResults(
                    Optional.ofNullable(matchedVerifyFellowResult)
                        .map(VerifyFellowResult::getVerifyFieldResult).orElse(null)));
                verifyFellowPassengerInfos.add(verifyFellowPassengerInfo);
            });
            VerifyFellowInfo verifyFellowInfo = new VerifyFellowInfo();
            verifyFellowInfo.setVerifyFellowPassengerInfos(verifyFellowPassengerInfos);
            verifyFellowInfo.setVerifyCode(String.valueOf(errorCode));
            orderCreateResponseType.setVerifyFellowInfo(verifyFellowInfo);
            return Tuple2.of(true, orderCreateResponseType);
        }
        String logErrorCode = String.valueOf(
            Optional.ofNullable(verifyResponseType).map(VerifyFellowPassengerResponseType::getStatus)
                .map(ResponseStatus::getErrorCode)
                .orElse(OrderCreateErrorEnum.VERIFY_FELLOW_PASSENGER_RESPONSE_TYPE_OTHER_ERROR.getErrorCode()));
        String friendlyMessage = SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessage(
            SoaErrorSharkKeyConstant.SERVICE_NAME_PRE_APPROVAL_SERVICE,
            SoaErrorSharkKeyConstant.ACTION_NAME_VERIFY_FELLOW_PASSENGER, logErrorCode);
        throw BusinessExceptionBuilder.createAlertException(
            OrderCreateErrorEnum.VERIFY_FELLOW_PASSENGER_RESPONSE_TYPE_OTHER_ERROR.getErrorCode(),
            Optional.ofNullable(verifyResponseType).map(VerifyFellowPassengerResponseType::getStatus)
                .map(ResponseStatus::getErrorMessage).orElse(null), friendlyMessage, logErrorCode);
    }


    private VerifyFellowResult getMatchedVerifyFellowResult(
        HotelBookPassengerInput hotelBookPassengerInput, List<VerifyFellowResult> verifyFellowResults) {
        String inputPsgId = getInputPsgId(hotelBookPassengerInput);
        if (CollectionUtil.isEmpty(verifyFellowResults) || StringUtil.isBlank(inputPsgId)) {
            return null;
        }

        return verifyFellowResults.stream().filter(Objects::nonNull).filter(verifyFellowResult -> {
            String verifyPsgId = getVerifyPsgId(verifyFellowResult);
            return inputPsgId.equals(verifyPsgId);
        }).findFirst().orElse(null);
    }

    private SimpleApprovalOutput buildApprovalOutput(
        VerifyFellowResult matchedVerifyFellowResult,
        HotelBookPassengerInput hotelBookPassengerInput) {
        SimpleApprovalOutput result = new SimpleApprovalOutput();
        String subApprovalNo = Optional.ofNullable(matchedVerifyFellowResult)
            .map(VerifyFellowResult::getPassengerInfo)
            .map(com.ctrip.corp.corpsz.preapprovalws.common.contract.verifyfellowpassenger
                .VerifyFellowPassengerInfo::getApprovalNumber)
            .orElse(null);
        if (StringUtil.isNotBlank(subApprovalNo)) {
            result.setSubApprovalNo(subApprovalNo);
        } else {
            result.setSubApprovalNo(
                Optional.ofNullable(hotelBookPassengerInput.getHotelPassengerInput())
                    .map(HotelPassengerInput::getApprovalInput)
                    .map(ApprovalInput::getSubApprovalNo).orElse(null));
        }
        return result;
    }

    private String getVerifyPsgId(VerifyFellowResult verifyFellowResult) {
        String uid = Optional.ofNullable(verifyFellowResult).map(VerifyFellowResult::getPassengerInfo)
            .map(com.ctrip.corp.corpsz.preapprovalws.common.contract.verifyfellowpassenger.VerifyFellowPassengerInfo::getUID)
            .orElse(null);
        String nonEmployeeId = Optional.ofNullable(verifyFellowResult).map(VerifyFellowResult::getPassengerInfo)
            .map(com.ctrip.corp.corpsz.preapprovalws.common.contract.verifyfellowpassenger.VerifyFellowPassengerInfo::getNonEmployeeId)
            .orElse(null);
        return StringUtil.isNotBlank(uid) ? uid : nonEmployeeId;
    }

    private String getInputPsgId(HotelBookPassengerInput hotelBookPassengerInput) {
        String uid = Optional.ofNullable(hotelBookPassengerInput).map(HotelBookPassengerInput::getHotelPassengerInput)
            .map(HotelPassengerInput::getUid).orElse(null);
        String infoId = Optional.ofNullable(hotelBookPassengerInput).map(HotelBookPassengerInput::getHotelPassengerInput)
            .map(HotelPassengerInput::getInfoId).orElse(null);
        return StringUtil.isNotBlank(uid) ? uid : infoId;
    }


    @Override
    protected ParamCheckResult check(
        Tuple5<VerifyFellowPassengerResponseType, OrderCreateRequestType, WrapperOfCheckAvail.BaseCheckAvailInfo,
            QconfigOfCertificateInitConfig,
            Map<String, StrategyInfo>> verifyFellowPassengerResponseTypeTuple1) {
        return null;
    }

    /**
     * 该方法是从 com.ctrip.corp.frontend.hotelbook.service.service.ordercheck.processor.FellowControlProcessor 中重构而来,
     * 用于将同行人校验结果中的元素校验信息, 转换为本应用契约规定的返回结果. 相比原方法去掉了检测同行人校验返回结果的人是否和入参
     * 的人一致的逻辑
     * @param verifyFieldResults
     * @return
     */
    private List<VerifyResult> getVerifyResults(final List<VerifyFieldResult> verifyFieldResults) {
        List<VerifyResult> result = new ArrayList<>();
        if (CollectionUtil.isEmpty(verifyFieldResults)) {
            return result;
        }
        verifyFieldResults.forEach(verifyFieldResult -> {
            if (verifyFieldResult == null) {
                return;
            }
            VerifyResult verifyResult = new VerifyResult();
            verifyResult.setMatched(BooleanUtil.parseStr(verifyFieldResult.isMatched()));
            ElementInfo elementInfo = new ElementInfo();
            elementInfo.setType(getType(verifyFieldResult.getFieldName()));
            verifyResult.setElementInfo(elementInfo);
            verifyResult.setApprovalValue("cityID".equalsIgnoreCase(verifyFieldResult.getFieldName()) ?
                verifyFieldResult.getApprovalFieldLabel() : verifyFieldResult.getApprovalFieldValue());
            result.add(verifyResult);
        });
        return result;
    }

    private String getType(String fileName) {
        if (StringUtil.isBlank(fileName)) {
            return null;
        }
        switch (fileName) {
            case FELLOWCONTROL_FIELDNAME_CITY:
                return ControlElementEnum.TO_CITIES.name() ;
            case FELLOWCONTROL_FIELDNAME_STARTDATE:
                return ControlElementEnum.CHECK_IN.name();
            case FELLOWCONTROL_FIELDNAME_ENDDATE:
                return ControlElementEnum.CHECK_OUT.name();
            default:
                return null;
        }
    }

}
