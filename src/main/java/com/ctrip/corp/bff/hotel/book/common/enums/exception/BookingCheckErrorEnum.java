package com.ctrip.corp.bff.hotel.book.common.enums.exception;

import com.ctrip.corp.bff.framework.hotel.common.enums.check.IChecker;

/**
 * <AUTHOR>
 * @date 2024-11-07
 **/
public enum BookingCheckErrorEnum implements IChecker {
    /**
     * 你当前的审批单仅支持国内酒店预订
     */
    CHECK_APPROVAL_DOMESTIC_CITY_ERROR(600, "CHECK_APPROVAL_DOMESTIC_CITY_ERROR"),
    /**
     * 你当前的审批单仅支持海外酒店预订
     */
    CHECK_APPROVAL_OVERSEA_CITY_ERROR(601, "CHECK_APPROVAL_OVERSEA_CITY_ERROR"),
    /**
     * 审批单查询失败
     */
    SEARCH_APPROVAL_ERROR(602, "SEARCH_APPROVAL_ERROR"),
    /**
     * 城市不符合管控
     */
    APPROVAL_CITY_ERROR(603, "APPROVAL_CITY_ERROR"),
    /**
     * 入住时间管控
     */
    APPROVAL_START_DATE_ERROR(604, "APPROVAL_START_DATE_ERROR"),
    /**
     * 离店时间管控
     */
    APPROVAL_END_DATE_ERROR(605, "APPROVAL_START_DATE_ERROR"),
    /**
     * 因贵司差旅政策，不可预订酒店
     */
    CHECK_BOOKING_LIMITION_ERROR(606, "checkBookingLimition error"),
    ;

    private Integer errorCode;
    private String errorMessage;

    BookingCheckErrorEnum(Integer errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    @Override public String getKey() {
        return this.name();
    }
}
