package com.ctrip.corp.bff.hotel.book.common.enums;

import com.ctrip.corp.bff.framework.hotel.common.enums.check.IChecker;

/**
 * <AUTHOR>
 * @date 2024/6/24 6:14
 */
public enum OrderCreateErrorEnum implements IChecker {
    /**
     * tms校验接口响应为空
     */
    TMS_CREATE_ORDER_VERIFY_RESPONSE_IS_NULL(601, "tmsCreateOrderVerifyResponse is null"),

    /**
     * tms校验接口异常
     */
    TMS_CREATE_ORDER_VERIFY_RESPONSE_IS_ERROR(602, "tmsCreateOrderVerifyResponse is error"),
    /**
     * 心程贝入口提交订单时，个人账户开关已关闭
     */
    PARAM_VALID_XCB_CLOSE(610, "ams personAccount closed error"),
    /**
     * 入住人相同
     */
    PARAM_VALID_CLIENT_PSG_SAME(611, "clientPsg id same error"),
    /**
     * 功能定制公司 政策执行人必须等于入住人
     */
    PARAM_VALID_CLIENT_PSG_POLICY_PSG_MUST_SAME(618, "clientPsg policyPsg must same error"),
    /**
     * 福利房必须包含本人
     */
    PARAM_VALID_WELFARE_ROOM_MUST_HAS_SELF(619, "welfareRoom must hasSelf error"),
    /**
     * boss预订不可包含本人
     */
    PARAM_VALID_BOSS_FORBID_SELF(620, "boss forbid hasSelf error"),
    /**
     * 实时查询到账户为入住人模式但入住人缺失房间号
     */
    PARAM_VALID_CLIENT_PSG_ROOM_INDEX_EMPTY(624, "clientPsg roomIndex error"),
    /**
     * gds房型需要会员积分时要求入住人手机号包含联系人手机号
     */
    PARAM_VALID_POINT_PHONE_ERROR(631, "point phone error"),
    /**
     * 双拼已经关闭
     */
    PARAM_VALID_DUPLEX_MODEL_CLOSE_ERROR(633, "duplex model close error"),
    /**
     * 双拼同住入住人选择的入住人无本人/人数大于3不符合预期
     */
    PARAM_VALID_SHARE_ROOM_PSG_ERROR(634, "share room psg error"),
    /**
     * 员工-员工重名
     */
    PARAM_VALID_CLIENT_PSG_NAME_SAME_UID_TO_UID(638, "clientPsg uid-uid name same error"),
    /**
     * 员工-非员工重名
     */
    PARAM_VALID_CLIENT_PSG_NAME_SAME_INFOID_TO_UID(639, "clientPsg infoId-uid name same error"),
    /**
     * 非员工-非员工重名
     */
    PARAM_VALID_CLIENT_PSG_NAME_SAME_INFOID_TO_INFOID(640, "clientPsg infoId-infoId name same error"),
    /**
     * 校验同行人接口响应数据有误
     */
    VERIFY_FELLOW_PASSENGER_RESPONSE_TYPE_IS_NULL(641, "verifyFellowPassengerResponseType is null"),
    /**
     * 校验同行人接口响应数据返回了错误码, 需要弹窗提示
     */
    VERIFY_FELLOW_PASSENGER_RESPONSE_TYPE_FAIL_TOAST(642, "verifyFellowPassengerResponseType contains fail error code 10303016 "),
    /**
     * 校验同行人接口响应数据返回了错误码, 要弹框拼人名
     */
    VERIFY_FELLOW_PASSENGER_RESPONSE_TYPE_FAIL(643, "verifyFellowPassengerResponseType contains fail error code 103110002 "),
    /**
     * 因私三方协议酒店, 入住人没有本人
     */
    PARAM_VALID_THIRD_PARTY_AGREEMENT_PSG_ERROR(644, "thirdPartyAgreementPsg error"),
    /**
     * orderCreateToken解析失败
     */
    PARAM_VALID_ORDER_CREATE_TOKEN_ERROR(645, "orderCreateToken error"),
    /**
     * 可定反查缺失必要信息
     */
    VALID_QUERY_CHECK_AVAIL_CONTEXT_ERROR(647, "queryCheckAvailContex error"),
    /**
     * 保险token解析失败
     */
    INSURANCE_TOKEN_ERROR(648, "insuranceToken error"),
    /**
     * rc token解析失败
     */
    RC_TOKEN_ERROR(649, "insuranceToken error"),

    /**
     * 重复预订原因获取失败，请稍后再试
     */
    CONFLICT_RC_NOT_OBTAIN(650, "conflict rc is null"),

    /**
     * 因贵司差旅政策需填写重复预订原因，请联系差旅负责人维护
     */
    CONFLICT_RC_NOT_CONFIGURE(651, "conflict rc not configure"),
    /**
     * mapper入参校验错误
     */
    MAPPER_PARAM_CHECK_ERROR(652, "mapper param check error"),
    /**
     * RC没有维护
     */
    REASONCODE_EMPTY(653, "buildRCInfoView rc is null"),
    /**
     * resourceToken有误
     */
    PARAM_VALID_RESOURCE_TOKEN_ERROR(655, "resourceToken error"),
    /**
     * 同行程校验人下审批单和政策执行人下审批单是否一致
     */
    INVALID_TRAVEL_TOGETHER_PASSENGER(656, "invalid travelTogetherApprovalNo error"),

    /**
     * 管控接口返回异常
     */
    CHECK_TRAVEL_POLICY_RESPONSE_IS_ERROR(657, "checkTravelPolicy response error"),

    /**
     * 审批单列表查询未返回预期的审批单信息
     */
    SEARCH_APPROVAL_RESPONSE_IS_ERROR(658, "searchApproval response error"),

    /**
     * agg管控支付方式节点异常
     */
    APPROVAL_PAYMENT_TYPE_ERROR(659, "approvalPaymentType error"),

    /**
     * 审批流匹配异常
     */
    MATCH_APPROVAL_FLOW_ERROR(660, "MatchApprovalFlow error"),

    /**
     * 房费支付方式异常
     */
    ROOM_PAY_TYPE_ERROR(661, "RoomPayType error"),

    /**
     * 创单异常
     */
    CREATE_ORDER_ERROR(662, "createOrder error"),

    /**
     * 订单提交超时，请重新提交。
     */
    SUBMIT_ORDER_AGAIN(663, "submitOrder error"),

    /**
     * 订单已经预订成功，但未关联到行程，请致电商旅客服处理。
     */
    CONNECTED_TRIP_ERROR(664, "submitOrder error"),

    /**
     * 审批流校验失败
     */
    APPROVAL_FLOWCHECK_ERROR(665, "approvalFlowCheck error"),

    /**
     * 支付获取失败
     */
    PAYMENT_ORDERCREATE_ERROR(666, "paymentOrderCreate error"),

    /**
     * 创单个付金额异常
     */
    CREATE_ORDER_AMOUNT_ERROR(667, "createOrder amount error"),

    /**
     * 获取balanceType异常
     */
    GET_HOTEL_BALANCE_TYPE_ENUM_ERROR(668, "getHotelBalanceTypeEnum error"),
    /**
     * 行程详情查询失败
     */
    SEARCH_TRIP_DETAIL_ERROR(669, "searchTripDetail error"),
    /**
     * 行程详情查询失败, 其他情况errorCode
     */
    VERIFY_FELLOW_PASSENGER_RESPONSE_TYPE_OTHER_ERROR(670, "verifyFellowPassengerResponseType other error"),

    /**
     * 系统当前最大支持x个未提交行程，如需创建新行程，请先提交已有行程
     */
    UN_SUBMIT_TRIP_EXCEED(671, "un submit trip exceed"),

    /**
     * 丰享双拼支付url为空
     */
    DOUBLE_PAY_NON_URL(672, "double pay non url"),    
    /**
     * 集团会员卡号长度和字母校验失败
     */
    PARAM_VALID_HOTEL_GROUP_MEMBER_CARD_LENGTH_AND_LETTER_ERROR(673, "hotel group member card length and letter error"),
    /**
     * 集团会员卡号长度校验失败
     */
    PARAM_VALID_HOTEL_GROUP_MEMBER_CARD_LENGTH_ERROR(674, "hotel group member card length error"),
    /**
     * 审批流token获取异常
     */
    APPROVAL_FLOW_COMPUTE(675, "approvalFlowCompute error"),
    /**
     * 本次预订价格已超标
     */
    FORBID_BOOKING(676, "check Rc Forbid error"),

    /**
     * 当前房型仅集团会员可享，请填写会员卡号
     */
    MEMBERSHIPNO_IS_BLANK(677, "membershipNo is blank"),

    /**
     * 员工房管控失败
     */
    EMP_ROOM_NOT_IN_CONTROL(678, "check emp room not in control"),

    /**
     * 缺失rc信息
     */
    MISS_RC_LIST(679, "miss rc list"),

    /**
     * 成本中心配置信息查询失败
     */
    MATCH_COST_CENTER_ERROR(682, "matchCostCenter error"),

    /**
     * 成本中心保存失败
     */
    SAVE_ORDER_COST_CENTER_ERROR(683, "saveOrderCostCenter error"),

    /**
     * 闪住在途校验失败
     */
    FLASH_CHECK_ERROR(684, "flashCheck error"),

    /**
     * 万豪直连限定非员工不可入住
     */
    ONLY_EMPLOYEE_BOOKING(685, "onlyEmployeeBooking"),

    /**
     * 您的账户配置没有可用的支付方式，请联系贵公司的差旅负责人。
     */
    NO_SUPPORT_PAY(686, "no support pay"),
    /**
     * 保存订单信息失败
     */
    SAVE_COMMON_DATA_ERROR(687, "saveCommonData error"),

    /**
     * 成本中心校验失败
     */
    COST_CENTER_CHECK(687, "costCenterCheck error"),

    /**
     * 创建行程失败
     */
    CREATE_TRIP_ERROR(686, "createTrip error"),

    /**
     * 预定信息有误 前端历史bug----base的房费/服务费支付方式不同于ordercheck传入的
     */
    INVALID_BOOK_INFO(499, "book info error"),

    /**
     * 缺失审批单信息，订单创建失败，请退回后重新下单
     */
    MISS_SUBAPPROVALNO(498, "miss subApprovalNo"),
    /**
     * 入住人缺失审批单据信息
     */
    PSG_MISS_SUBAPPROVALNO(497, "psg miss subApprovalNo"),

    /**
     * 活动详情地址为空
     */
    ACTIVITY_DETAIL_URL_IS_NULL(496, "activityDetailUrl is null"),
    /**
     * 活动id为空
     */
    INVALID_MICE_ACTIVITY_ID(495, "invalid miceActivityId"),
    /**
     * miceToken为空
     */
    INVALID_MICE_TOKEN(494, "invalid miceToken"),
    /**
     * 入住人敏感信息校验失败
     */
    PARAM_VALID_CLIENT_PSG_SENSITIVE_INFORMATION_ERROR(493, "clientPsg sensitive information error"),
    /**
     * 联系人敏感信息校验失败
     */
    PARAM_VALID_CONTACT_PSG_SENSITIVE_INFORMATION_ERROR(492, "contactPsg sensitive information error"),
    /**
     * 保险出行人敏感信息校验失败
     */
    PARAM_VALID_INSURANCE_PSG_SENSITIVE_INFORMATION_ERROR(491, "insurancePsg sensitive information error"),
    /**
     * 发票邮箱验证不通过
     */
    PARAM_VALID_INVOICE_EMAIL_SENSITIVE_INFORMATION_ERROR(490, "invoice email sensitive information error"),
    /**
     * 入住人为空
     */
    PARAM_VALID_CLIENT_PSG_EMPTY(489, "clientPsg is error"),
    /**
     * 请求头用户信息缺失
     */
    PARAM_VALID_USERINFO_EMPTY(488, "userInfo is error"),
    /**
     * 用户行为回溯视频为空-保险必须
     */
    PARAM_VALID_USER_ACTION_TOKEN_EMPTY(487, "userActionToken is error"),
    /**
     * 预订节点缺失
     */
    PARAM_VALID_HOTEL_BOOK_INPUT_NULL(486, "hotelBookInput null error"),
    /**
     * 资源token缺失
     */
    PARAM_VALID_RESOURCE_TOKEN_NULL(485, "resourceToken null error"),
    /**
     * 入住人人名为空
     */
    PARAM_VALID_CLIENT_PSG_NAME_EMPTY(484, "clientPsg name is empty error"),
    /**
     * 分摊金额校验失败
     */
    PARAM_VALID_COST_ALLOCATION_AMOUNT(483, "costAllocation amount error"),
    /**
     * 保险缺失入住人信息
     */
    PARAM_VALID_INSURANCE_PSG_EMPTY(482, "insurancePsg empty error"),
    /**
     * 保险缺失入住人信息有误
     */
    PARAM_VALID_INSURANCE_PSG_INFO_ERROR(481, "insurancePsg info error"),
    /**
     * 前端提交订单的房间数可可定的房间数不一致
     */
    PARAM_VALID_ROOM_QUANTITY_ERROR(480, "room quantity error"),
    /**
     * 会员卡号有误
     */
    PARAM_VALID_MEMBERSHIP_NO_ERROR(479, "membershipNo error"),
    /**
     * 联系人信息缺失
     */
    PARAM_VALID_CONTACTOR_INFO_ERROR(478, "contactorInfo error"),
    /**
     * 单订单审批流信息异常
     */
    PARAM_VALID_SINGLE_APPROVAL_MISS_ERROR(477, "single approval miss error"),
    /**
     * 双拼同住入住人选择的间数不符合预期
     */
    PARAM_VALID_SHARE_ROOM_QUALITY_MISS_SELF_ERROR(476, "share room quality error"),
    /**
     * 入住人英文名为空
     */
    PARAM_VALID_CLIENT_PSG_ENAME_EMPTY(475, "clientPsg ename is empty error"),
    /**
     * 入住人人id空
     */
    PARAM_VALID_CLIENT_PSG_ID_EMPTY(474, "clientPsg id is empty error"),
    /**
     * 延用单号非long
     */
    PARAM_VALID_FOLLOW_ORDER_NO_ERROR(473, "order followOrderNo error"),
    /**
     * 房间数为空
     */
    PARAM_VALID_MISS_ROOM_QUANTITY_ERROR(472, "roomQuantity is null"),

    /**
     * 已超过该房型最多入住人数，请稍后再试或预订其他房型
     */
    GUEST_PERSON_SIZE_ERROR(471, "guestPersonSize error"),

    /**
     * 入住人数超过房间数
     */
    GUEST_PERSON_OR_ROOM_QUANTITY_SIZE_ERROR(470, "guestPersonSize or roomQuantity error"),

    /**
     * 存在入住人下的房间号>预订房间数的
     */
    GUEST_PERSON_ROOM_INDEX_ERROR(469, "roomIndex error"),
    /**
     * 缺失腾讯外部员工号
     */
    PSG_MISS_EXTERNAL_EMPLOYEE_ID(468, "psg miss externalEmployeeId"),

    /**
     * 腾讯外部员工号校验异常
     */
    PSG_EXTERNAL_EMPLOYEE_ID_ERROR(467, "psg externalEmployeeId error"),

    /**
     *  %1$s的企微号不存在，请确认并修改企微号
     */
    EXTERNAL_EMPLOYEE_ID_ERROR(466, "psg externalEmployeeId error"),
    /**
     * 缺失审批单信息，请重新填写
     */
    MISS_SUBAPPROVALNO_POST(301, "miss oaApprovalHead subApprovalNo"),
    /**
     * 行程政策执行人和订单政策执行人必须同一人
     */
    TRIPPOLICYID_MUST_SAME_ORDERPOLICYID(688, "trip policyUid and order policyId must same"),
    /**
     * 智能沿用二次提交校验未通过
     */
    APPROVAL_FLOW_REUSE_AI_CONTINUE_CHECK_ERROR(687, "approvalFlowReuse AI continue check error")
    ;

    private final Integer errorCode;
    private final String errorMessage;

    OrderCreateErrorEnum(Integer errorCode, String errorMessage) {
        this.errorCode = errorCode;
        this.errorMessage = errorMessage;
    }

    public Integer getErrorCode() {
        return errorCode;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    @Override public String getKey() {
        return this.name();
    }
}
