package com.ctrip.corp.bff.hotel.book.handler.corphotelbookservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.hotelbooking.CorpHotelBookServiceClient;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderRequestType;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/9/14 12:56
 */
@Component
public class HandlerOfCreateOrder
    extends AbstractHandlerOfSOA<CreateOrderRequestType, CreateOrderResponseType, CorpHotelBookServiceClient> {
    @Override protected String getMethodName() {
        return "createOrder";
    }
}
