package com.ctrip.corp.bff.hotel.book.handler.corphotelbookcommonws;

import com.ctrip.corp.agg.commonws.entity.GetPackageRoomListRequestType;
import com.ctrip.corp.agg.commonws.entity.GetPackageRoomListResponseType;
import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.hotelbook.commonws.CorpHotelBookCommonWSClient;
import org.springframework.stereotype.Component;

/**
 * @Author: junlongma
 * @Date: 2024-10-8
 * @Description: 酒店套餐
 */
@Component
public class HandlerOfGetPackageRoomList extends AbstractHandlerOfSOA<GetPackageRoomListRequestType,
    GetPackageRoomListResponseType, CorpHotelBookCommonWSClient> {
    @Override
    protected String getMethodName() {
        return "getPackageRoomList";
    }
}
