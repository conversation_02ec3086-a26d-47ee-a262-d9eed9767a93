package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount.AccountInfo;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.OrderResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum;
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.common.utils.date.DateUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple6;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail.BaseCheckAvailInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.corp.foundation.common.util.NumberUtil;
import com.ctrip.soa._21210.HotelClientInfoType;
import com.ctrip.soa._21210.HotelOrderRepeatOrderRequestType;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/23 18:31
 */
@Component
public class MapperOfHotelOrderRepeatOrderRequestType extends
    AbstractMapper<Tuple6<AccountInfo,OrderCreateRequestType,
            ResourceToken, WrapperOfCheckAvail.BaseCheckAvailInfo, QconfigOfCertificateInitConfig,
        Map<String, StrategyInfo>>, HotelOrderRepeatOrderRequestType> {

    private static final String ALL = "ALL";

    private static final String T = "T";

    @Override
    protected HotelOrderRepeatOrderRequestType convert(Tuple6<AccountInfo, OrderCreateRequestType,
            ResourceToken, BaseCheckAvailInfo, QconfigOfCertificateInitConfig,
            Map<String, StrategyInfo>> para) {
        if (para == null || para.getT2() == null) {
            return null;
        }
        OrderCreateRequestType orderCreateRequestType = para.getT2();
        HotelOrderRepeatOrderRequestType res = new HotelOrderRepeatOrderRequestType();
        WrapperOfCheckAvail.BaseCheckAvailInfo queryCheckAvailContextResponseType = para.getT4();
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig = para.getT5();
        Map<String, StrategyInfo> strategyInfoMap = para.getT6();
        res.setCorpPayType(ALL);
        try {
            res.setCheckInTime(DateUtil.dateFormat(Optional.ofNullable(para.getT2().getHotelBookInput())
                    .map(HotelBookInput::getHotelDateRangeInfo).map(HotelDateRangeInfo::getCheckIn).orElse(null), DateUtil.YYYY_MM_DD, DateUtil.YYYY_MM_DD_HH_mm_ss));
            res.setCheckOutTime(DateUtil.dateFormat(Optional.ofNullable(para.getT2().getHotelBookInput())
                    .map(HotelBookInput::getHotelDateRangeInfo).map(HotelDateRangeInfo::getCheckOut).orElse(null), DateUtil.YYYY_MM_DD, DateUtil.YYYY_MM_DD_HH_mm_ss));
        } catch (Exception e) {
            LogUtil.loggingClogOnly(LogLevelEnum.Error,  MapperOfHotelOrderRepeatOrderRequestType.class, "parse date fail", e, null);
        }
        res.setCorpId(Optional.ofNullable(para.getT2().getIntegrationSoaRequestType()).map(IntegrationSoaRequestType::getUserInfo).map(UserInfo::getCorpId).orElse(null));
        res.setSubAccountId(NumberUtil.parseInt(Optional.ofNullable(para.getT1()).map(WrapperOfAccount.AccountInfo::getSubAccountID).orElse(null)));
        res.setOriginalOrderId(buildOriginalOrderId(para.getT3(), para.getT2()));
        res.setLanguage("en-US".equalsIgnoreCase(
            Optional.ofNullable(para.getT2().getIntegrationSoaRequestType()).map(IntegrationSoaRequestType::getLanguage)
                .orElse(null)) ? "en-US" : "zh-CN");
        res.setClientList(convert2ClientList(
            Optional.ofNullable(para.getT2()).map(OrderCreateRequestType::getHotelBookPassengerInputs).orElse(null),
            orderCreateRequestType, queryCheckAvailContextResponseType, qconfigOfCertificateInitConfig, strategyInfoMap));
        return res;
    }

    private Long buildOriginalOrderId(ResourceToken resourceToken, OrderCreateRequestType orderCreateRequestType) {
        if (StrategyOfBookingInitUtil.modify(orderCreateRequestType.getStrategyInfos())
            || StrategyOfBookingInitUtil.applyModify(orderCreateRequestType.getStrategyInfos())
            || StrategyOfBookingInitUtil.copyOrder(orderCreateRequestType.getStrategyInfos())) {
            return Optional.ofNullable(resourceToken).map(ResourceToken::getOrderResourceToken)
                .map(OrderResourceToken::getOrderId).orElse(0L);
        }
        return 0L;
    }

    protected List<HotelClientInfoType> convert2ClientList(List<HotelBookPassengerInput> passengerInputs,
        OrderCreateRequestType orderCreateRequestType,
        WrapperOfCheckAvail.BaseCheckAvailInfo queryCheckAvailContextResponseType,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        Map<String, StrategyInfo> strategyInfoMap) {
        if (CollectionUtil.isEmpty(passengerInputs)) {
            return null;
        }
        return passengerInputs.stream().filter(Objects::nonNull).map(t -> {
            HotelClientInfoType hotelClientInfoType = new HotelClientInfoType();
            hotelClientInfoType.setUid(
                T.equalsIgnoreCase(t.getHotelPassengerInput().getEmployee()) ? t.getHotelPassengerInput().getUid() :
                    null);
            hotelClientInfoType.setName(
                OrderCreateProcessorOfUtil.getUseName(t, orderCreateRequestType.getCityInput().getCityId(),
                    queryCheckAvailContextResponseType, qconfigOfCertificateInitConfig, strategyInfoMap));
            return hotelClientInfoType;
        }).collect(Collectors.toList());
    }

    @Override
    protected ParamCheckResult check(Tuple6<AccountInfo, OrderCreateRequestType,
            ResourceToken, BaseCheckAvailInfo, QconfigOfCertificateInitConfig,
        Map<String, StrategyInfo>> accountInfoOrderCreateRequestTypeTuple2) {
        return null;
    }
}
