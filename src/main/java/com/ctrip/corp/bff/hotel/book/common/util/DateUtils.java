package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.bff.framework.template.common.utils.date.DateUtil;
import com.ctrip.framework.apollo.core.utils.StringUtils;

import java.text.*;
import java.time.Duration;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.format.DateTimeFormatter;
import java.util.Calendar;
import java.util.Date;

/**
 * @Author: chenchuang
 * @Date: 2024/9/4 17:14

 */
public class DateUtils {

    public static final String YYYY_MM_DD = "yyyy-MM-dd";
    public static final String UTC_FORMAT = "yyyy-MM-dd'T'HH:mm:ss'Z'";

    private static final DateTimeFormatter FORMATTER = DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD_HH_mm_ss);

    public static LocalDate toLocalDate(Date date) {
        if (null == date) {
            return null;
        }
        return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
    }

    public static String formatDate(Calendar calendar) {
        DateFormat dateFormat = new SimpleDateFormat(YYYY_MM_DD);
        return dateFormat.format(calendar.getTime());
    }

    public static LocalDate toLocalDate(String date) {
        if (StringUtils.isEmpty(date)) {
            return null;
        }
        try {
            return toLocalDate(org.apache.commons.lang3.time.DateUtils.parseDate(date, YYYY_MM_DD));
        } catch (Exception e) {
            return null;
        }
    }

    public static LocalDateTime toLocalDateTime(Date date) {
        if (date == null) {
            return null;
        }
        try {
            return date.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        } catch (Exception e) {
            return null;
        }
    }

    /**
     * 持续小时数
     * 规则：
     * (离店时间-入住时间)/60min 直接截取一位小数
     */
    public static String durationHour(LocalDateTime startTime, LocalDateTime endTime) {
        if (startTime == null || endTime == null || startTime.isAfter(endTime)) {
            return null;
        }
        DecimalFormat decimalFormat = new DecimalFormat("#.#");
        long durationSecond = Duration.between(startTime, endTime).getSeconds();
        return decimalFormat.format(Math.floor((double) durationSecond / 3600 * 10) / 10);
    }

    public static String formatLocalDate(LocalDateTime localDateTime) {
        return localDateTime.format(FORMATTER);
    }

    public static Calendar stringToCalendar(String convertString, String format) {
        Date date = stringToDate(convertString, format);
        if (date == null) {
            return null;
        } else {
            Calendar timeCalendar = Calendar.getInstance();
            timeCalendar.setTime(date);
            return timeCalendar;
        }
    }

    public static Date stringToDate(String convertString, String format) {
        SimpleDateFormat tFormat = new SimpleDateFormat(format);
        if (convertString == null) {
            return null;
        } else {
            Date timeDate = null;

            try {
                timeDate = tFormat.parse(convertString);
            } catch (ParseException var5) {
            }

            return timeDate;
        }
    }

    /**
     * 获取UTC时间字符串 格式："yyyy-MM-dd'T'HH:mm:ssX"
     */
    public static String getUTCTimeStr() {
        return LocalDateTime.now(ZoneOffset.UTC).format(DateTimeFormatter.ofPattern("yyyy-MM-dd'T'HH:mm:ss'Z'"));
    }

    /**
     * 字符串转为Calendar类型
     *
     * @param dateString 日期字符串
     * @return 日期
     */
    public static Calendar stringToCalender(String dateString, String dateFormat) throws ParseException {
        if (org.apache.commons.lang.StringUtils.isNotEmpty(dateString)) {
            return date2Calendar(org.apache.commons.lang3.time.DateUtils.parseDate(dateString, dateFormat));
        }
        return null;
    }

    /**
     * Date 转化Calendar
     *
     * @param date
     * @return
     */
    public static Calendar date2Calendar(Date date) {
        if (date != null) {
            Calendar calendar = Calendar.getInstance();
            calendar.setTime(date);
            return calendar;
        }
        return null;
    }
}
