package com.ctrip.corp.bff.hotel.book.common.enums;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;

import java.util.Arrays;
import java.util.Optional;

/**
 * 发票类型
 *
 * <AUTHOR>
 * @date 2021-05-27
 */
public enum InvoiceEnum {
    /**
     * 空值
     */
    NONE("NONE", "NONE"),

    /**
     * 电子普票
     */
    E_VAT("E_VAT", "VAT_ELECTRONIC_INVOICE"),

    /**
     * 纸质普票
     */
    VAT("VAT", "VAT_INVOICE"),

    /**
     * 专票
     */
    S_VAT("S_VAT", "VAT_SPECIAL_INVOICE"),

    /**
     * 电子专票
     */
    ES_VAT("ES_VAT", "VAT_ELECTRONIC_SPECIAL_INVOICE"),

    /**
     * 海外invoice消费凭证
     */
    INVOICE("INVOICE", "INVOICE"),
    /**
     * 数电普票
     */
    D_INVOICE("DInvoice", "DInvoice"),
    /**
     * 数电专票
     */
    D_VAT_INVOICE("DVatInvoice", "DVatInvoice")
    ;

    /**
     * code
     */
    private String code;
    /**
     * code
     */
    private String aggCode;

    InvoiceEnum(String code, String aggCode) {
        this.code = code;
        this.aggCode = aggCode;
    }

    public String getCode() {
        return code;
    }

    public String getAggCode() {
        return aggCode;
    }

    public static InvoiceEnum getValue(String value) {
        Optional<InvoiceEnum> result = Arrays.stream(InvoiceEnum.values())
                .filter(corpPayType -> corpPayType.getCode().equalsIgnoreCase(value)).findFirst();
        return result.orElse(NONE);
    }

    public static InvoiceEnum getValueForAgg(String value) {
        Optional<InvoiceEnum> result = Arrays.stream(InvoiceEnum.values())
                .filter(corpPayType -> StringUtil.equalsIgnoreCase(corpPayType.getAggCode(), value)).findFirst();
        return result.orElse(NONE);
    }

    public static InvoiceEnum convertInvoiceType(Integer invoiceType) {
        if (invoiceType == null) {
            return InvoiceEnum.NONE;
        }
        // 发票类型对应的case
        final int INVOICE_TYPE_E_VAT = 102, INVOICE_TYPE_VAT = 101, INVOICE_TYPE_S_VAT = 200, INVOICE_D_INVOICE = 300, INVOICE_D_VAT_INVOICE = 301;

        switch (invoiceType) {
            case INVOICE_TYPE_E_VAT:
                return InvoiceEnum.E_VAT;
            case INVOICE_TYPE_VAT:
                return InvoiceEnum.VAT;
            case INVOICE_TYPE_S_VAT:
                return InvoiceEnum.S_VAT;
            case INVOICE_D_INVOICE:
                return InvoiceEnum.D_INVOICE;
            case INVOICE_D_VAT_INVOICE:
                return InvoiceEnum.D_VAT_INVOICE;
            default:
                return InvoiceEnum.NONE;
        }
    }

    // 发票类型： EInvoice 增值税普票电子发票 PInvoice 增值税普票纸质发票 PVatInvoice 增值税专票纸质发票 DInvoice-数电普票 DVatInvoice-数电专票
    public static InvoiceEnum convertInvoiceEnum(String invoiceType) {
        if (StringUtil.isBlank(invoiceType)) {
            return InvoiceEnum.NONE;
        }
        switch (invoiceType) {
            case "EInvoice":
                return InvoiceEnum.E_VAT;
            case "PInvoice":
                return InvoiceEnum.VAT;
            case "PVatInvoice":
                return InvoiceEnum.S_VAT;
            case "DInvoice":
                return InvoiceEnum.D_INVOICE;
            case "DVatInvoice":
                return InvoiceEnum.D_VAT_INVOICE;
            default:
                return InvoiceEnum.NONE;
        }
    }
}
