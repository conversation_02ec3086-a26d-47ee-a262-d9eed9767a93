package com.ctrip.corp.bff.hotel.book.common.wrapper;

import com.ctrip.basebiz.members.core.contract.GetInboundParameterResponseType;
import com.ctrip.corp.agg.commonws.entity.GetPackageRoomListResponseType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType;
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextResponseType;
import com.ctrip.corp.approve.ws.contract.MatchApprovalFlowResponseType;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount.AccountInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.AllocationResultToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfEmailInfoConfig;
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryResponseType;
import com.ctrip.corp.bff.specific.contract.ApprovalFlowComputeResponseType;
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataResponseType;
import com.ctrip.model.RegisterResponseType;
import com.ctrip.soa._21234.CreateTripResponseType;
import com.ctrip.soa._21234.SearchTripDetailResponseType;
import com.ctrip.soa._21685.PayConfigResponseType;
import corp.user.service.CorpAccountQueryService.QueryIndividualAccountResponseType;
import corp.user.service.corpUserInfoService.GetPlatformRelationByUidResponseType;
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/22 8:59
 */
public class WrapperOfCreateOrder {
    private QueryCheckAvailContextResponseType queryCheckAvailContextResponseType;
    private CreateTripResponseType createTripResponseType;
    private OrderCreateRequestType orderCreateRequestType;
    private ResourceToken resourceToken;
    private GetTravelPolicyContextResponseType getTravelPolicyContextResponseType;
    private CheckTravelPolicyResponseType checkTravelPolicyResponseType;
    private WrapperOfAccount.AccountInfo accountInfo;
    private AllocationResultToken costAllocationToken;
    private SearchTripDetailResponseType searchTripDetailResponseType;
    private QueryHotelOrderDataResponseType queryHotelOrderDataResponseType;
    private OrderCreateToken orderCreateToken;
    private PayConfigResponseType payConfigResponseType;
    private GetPlatformRelationByUidResponseType getPlatformRelationByUidResponseType;
    private WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo;
    private SSOInfoQueryResponseType ssoInfoQueryResponseType;
    private ApprovalFlowComputeResponseType approvalFlowComputeResponseType;
    private MatchApprovalFlowResponseType matchApprovalFlowResponseType;
    private GetPackageRoomListResponseType getPackageRoomListResponseType;
    private GetInboundParameterResponseType getInboundParameterResponseType;
    private QconfigOfEmailInfoConfig qconfigOfEmailInfoConfig;

    private RegisterResponseType registerResponse;

    private QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType;
    private Map<String, StrategyInfo> strategyInfoMap;
    private QueryIndividualAccountResponseType queryIndividualAccountResponseType;
    private QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig;

    public QconfigOfCertificateInitConfig getQconfigOfCertificateInitConfig() {
        return qconfigOfCertificateInitConfig;
    }



    public Map<String, StrategyInfo> getStrategyInfoMap() {
        return strategyInfoMap;
    }


    public QueryIndividualAccountResponseType getQueryIndividualAccountResponseType() {
        return queryIndividualAccountResponseType;
    }

    public QconfigOfEmailInfoConfig getQconfigOfEmailInfoConfig() {
        return qconfigOfEmailInfoConfig;
    }
    public MatchApprovalFlowResponseType getMatchApprovalFlowResponseType() {
        return matchApprovalFlowResponseType;
    }

    public GetPackageRoomListResponseType getGetPackageRoomListResponseType() {
        return getPackageRoomListResponseType;
    }

    public GetInboundParameterResponseType getGetInboundParameterResponseType() {
        return getInboundParameterResponseType;
    }
    public ApprovalFlowComputeResponseType getApprovalFlowComputeResponseType() {
        return approvalFlowComputeResponseType;
    }

    public PayConfigResponseType getPayConfigResponseType() {
        return payConfigResponseType;
    }

    public QueryCheckAvailContextResponseType getQueryCheckAvailContextResponseType() {
        return queryCheckAvailContextResponseType;
    }

    public CreateTripResponseType getCreateTripResponseType() {
        return createTripResponseType;
    }

    public OrderCreateRequestType getOrderCreateRequestType() {
        return orderCreateRequestType;
    }

    public ResourceToken getResourceToken() {
        return resourceToken;
    }

    public GetTravelPolicyContextResponseType getGetTravelPolicyContextResponseType() {
        return getTravelPolicyContextResponseType;
    }

    public CheckTravelPolicyResponseType getCheckTravelPolicyResponseType() {
        return checkTravelPolicyResponseType;
    }

    public AccountInfo getAccountInfo() {
        return accountInfo;
    }

    public AllocationResultToken getCostAllocationToken() {
        return costAllocationToken;
    }

    public SearchTripDetailResponseType getSearchTripDetailResponseType() {
        return searchTripDetailResponseType;
    }

    public QueryHotelOrderDataResponseType getQueryHotelOrderDataResponseType() {
        return queryHotelOrderDataResponseType;
    }

    public GetPlatformRelationByUidResponseType getGetPlatformRelationByUidResponseType() {
        return getPlatformRelationByUidResponseType;
    }

    public OrderCreateToken getOrderCreateToken() {
        return orderCreateToken;
    }

    public WrapperOfCheckAvail.CheckAvailContextInfo getCheckAvailContextInfo() {
        return checkAvailContextInfo;
    }

    public SSOInfoQueryResponseType getSsoInfoQueryResponseType() {
        return ssoInfoQueryResponseType;
    }
    public static Builder builder() {
        return new Builder();
    }

    public QueryBizModeBindRelationResponseType getQueryBizModeBindRelationResponseType() {
        return queryBizModeBindRelationResponseType;
    }

    public RegisterResponseType getRegisterResponse() {
        return registerResponse;
    }

    public static class Builder {
        private QueryCheckAvailContextResponseType queryCheckAvailContextResponseType;
        private CreateTripResponseType createTripResponseType;
        private OrderCreateRequestType orderCreateRequestType;
        private ResourceToken resourceToken;
        private GetTravelPolicyContextResponseType getTravelPolicyContextResponseType;
        private CheckTravelPolicyResponseType checkTravelPolicyResponseType;
        private WrapperOfAccount.AccountInfo accountInfo;
        private AllocationResultToken costAllocationToken;
        private SearchTripDetailResponseType searchTripDetailResponseType;
        private QueryHotelOrderDataResponseType queryHotelOrderDataResponseType;
        private OrderCreateToken orderCreateToken;
        private PayConfigResponseType payConfigResponseType;
        private GetPlatformRelationByUidResponseType getPlatformRelationByUidResponseType;
        private WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo;

        private SSOInfoQueryResponseType ssoInfoQueryResponseType;
        private ApprovalFlowComputeResponseType approvalFlowComputeResponseType;
        private MatchApprovalFlowResponseType matchApprovalFlowResponseType;
        private GetPackageRoomListResponseType getPackageRoomListResponseType;
        private GetInboundParameterResponseType getInboundParameterResponseType;
        private QconfigOfEmailInfoConfig qconfigOfEmailInfoConfig;

        private RegisterResponseType registerResponse;

        private QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType;

        private Map<String, StrategyInfo> strategyInfoMap;
        private QueryIndividualAccountResponseType queryIndividualAccountResponseType;

        private QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig;

        public Builder setQconfigOfCertificateInitConfig(QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig) {
            this.qconfigOfCertificateInitConfig = qconfigOfCertificateInitConfig;
            return this;
        }

        public Builder setStrategyInfoMap(Map<String, StrategyInfo> strategyInfoMap) {
            this.strategyInfoMap = strategyInfoMap;
            return this;
        }

        public Builder setQueryIndividualAccountResponseType(
            QueryIndividualAccountResponseType queryIndividualAccountResponseType) {
            this.queryIndividualAccountResponseType = queryIndividualAccountResponseType;
            return this;
        }

        public Builder setRegisterResponse(RegisterResponseType registerResponse) {
            this.registerResponse = registerResponse;
            return this;
        }

        public Builder setQueryBizModeBindRelationResponseType(QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType) {
            this.queryBizModeBindRelationResponseType = queryBizModeBindRelationResponseType;
            return this;
        }

        public Builder setQconfigOfEmailInfoConfig(QconfigOfEmailInfoConfig qconfigOfEmailInfoConfig) {
            this.qconfigOfEmailInfoConfig = qconfigOfEmailInfoConfig;
            return this;
        }

        public Builder setPayConfigResponseType(PayConfigResponseType payConfigResponseType) {
            this.payConfigResponseType = payConfigResponseType;
            return this;
        }

        public Builder setQueryCheckAvailContextResponseType(QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
            this.queryCheckAvailContextResponseType = queryCheckAvailContextResponseType;
            return this;
        }

        public Builder setCreateTripResponseType(CreateTripResponseType createTripResponseType) {
            this.createTripResponseType = createTripResponseType;
            return this;
        }

        public Builder setOrderCreateRequestType(OrderCreateRequestType orderCreateRequestType) {
            this.orderCreateRequestType = orderCreateRequestType;
            return this;
        }

        public Builder setResourceToken(ResourceToken resourceToken) {
            this.resourceToken = resourceToken;
            return this;
        }

        public Builder setGetTravelPolicyContextResponseType(GetTravelPolicyContextResponseType getTravelPolicyContextResponseType) {
            this.getTravelPolicyContextResponseType = getTravelPolicyContextResponseType;
            return this;
        }

        public Builder setCheckTravelPolicyResponseType(CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
            this.checkTravelPolicyResponseType = checkTravelPolicyResponseType;
            return this;
        }

        public Builder setAccountInfo(WrapperOfAccount.AccountInfo accountInfo) {
            this.accountInfo = accountInfo;
            return this;
        }

        public Builder setCostAllocationToken(AllocationResultToken costAllocationToken) {
            this.costAllocationToken = costAllocationToken;
            return this;
        }

        public Builder setSearchTripDetailResponseType(SearchTripDetailResponseType searchTripDetailResponseType) {
            this.searchTripDetailResponseType = searchTripDetailResponseType;
            return this;
        }

        public Builder setQueryHotelOrderDataResponseType(QueryHotelOrderDataResponseType queryHotelOrderDataResponseType) {
            this.queryHotelOrderDataResponseType = queryHotelOrderDataResponseType;
            return this;
        }

        public Builder setOrderCreateToken(OrderCreateToken orderCreateToken) {
            this.orderCreateToken = orderCreateToken;
            return this;
        }

        public Builder setGetPlatformRelationByUidResponseType(
            GetPlatformRelationByUidResponseType getPlatformRelationByUidResponseType) {
            this.getPlatformRelationByUidResponseType = getPlatformRelationByUidResponseType;
            return this;
        }

        public Builder setCheckAvailContextInfo(WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo) {
            this.checkAvailContextInfo = checkAvailContextInfo;
            return this;
        }

        public Builder setSsoInfoQueryResponseType(SSOInfoQueryResponseType ssoInfoQueryResponseType) {
            this.ssoInfoQueryResponseType = ssoInfoQueryResponseType;
            return this;
        }

        public Builder setApprovalFlowComputeResponseType(
            ApprovalFlowComputeResponseType approvalFlowComputeResponseType) {
            this.approvalFlowComputeResponseType = approvalFlowComputeResponseType;
            return this;
        }

        public Builder setMatchApprovalFlowResponseType(MatchApprovalFlowResponseType matchApprovalFlowResponseType) {
            this.matchApprovalFlowResponseType = matchApprovalFlowResponseType;
            return this;
        }

        public Builder setGetPackageRoomListResponseType(GetPackageRoomListResponseType getPackageRoomListResponseType) {
            this.getPackageRoomListResponseType = getPackageRoomListResponseType;
            return this;
        }

        public Builder setGetInboundParameterResponseType(GetInboundParameterResponseType getInboundParameterResponseType) {
            this.getInboundParameterResponseType = getInboundParameterResponseType;
            return this;
        }

        public WrapperOfCreateOrder build() {
            WrapperOfCreateOrder order = new WrapperOfCreateOrder();
            order.queryCheckAvailContextResponseType = this.queryCheckAvailContextResponseType;
            order.createTripResponseType = this.createTripResponseType;
            order.orderCreateRequestType = this.orderCreateRequestType;
            order.resourceToken = this.resourceToken;
            order.getTravelPolicyContextResponseType = this.getTravelPolicyContextResponseType;
            order.checkTravelPolicyResponseType = this.checkTravelPolicyResponseType;
            order.accountInfo = this.accountInfo;
            order.costAllocationToken = this.costAllocationToken;
            order.searchTripDetailResponseType = this.searchTripDetailResponseType;
            order.queryHotelOrderDataResponseType = this.queryHotelOrderDataResponseType;
            order.orderCreateToken = this.orderCreateToken;
            order.payConfigResponseType = this.payConfigResponseType;
            order.getPlatformRelationByUidResponseType = this.getPlatformRelationByUidResponseType;
            order.checkAvailContextInfo = this.checkAvailContextInfo;
            order.ssoInfoQueryResponseType = this.ssoInfoQueryResponseType;
            order.approvalFlowComputeResponseType = this.approvalFlowComputeResponseType;
            order.matchApprovalFlowResponseType = this.matchApprovalFlowResponseType;
            order.getPackageRoomListResponseType = this.getPackageRoomListResponseType;
            order.getInboundParameterResponseType = this.getInboundParameterResponseType;
            order.qconfigOfEmailInfoConfig = this.qconfigOfEmailInfoConfig;
            order.queryBizModeBindRelationResponseType = this.queryBizModeBindRelationResponseType;
            order.registerResponse = registerResponse;
            order.strategyInfoMap = strategyInfoMap;
            order.queryIndividualAccountResponseType = queryIndividualAccountResponseType;
            order.qconfigOfCertificateInitConfig = this.qconfigOfCertificateInitConfig;
            return order;
        }
    }
}

