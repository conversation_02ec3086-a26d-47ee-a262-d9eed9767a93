package com.ctrip.corp.bff.hotel.book.service;

import com.ctrip.corp.bff.framework.hotel.common.enums.CertificateTypeEnum;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.ParamCheckUtil;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CertificateInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PassengerBasicInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ResourceTokenInfo;
import com.ctrip.corp.bff.framework.template.processor.Processor;
import com.ctrip.corp.bff.framework.template.service.AbstractSyncService;
import com.ctrip.corp.bff.framework.template.service.generate.WebService;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.util.AgeUtil;
import com.ctrip.corp.bff.hotel.book.common.util.HotelPayTypeUtil;
import com.ctrip.corp.bff.hotel.book.contract.FollowApprovalInfoInput;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType;
import com.ctrip.corp.bff.hotel.book.contract.UserActionInfo;
import com.ctrip.corp.bff.hotel.book.processor.ProcessorOfOrderCreate;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;

/**
 * <AUTHOR>
 * @description 创单服务
 * @date 2024/3/31
 */
@WebService(name = "orderCreate")
public class ServiceOfOrderCreate
        extends AbstractSyncService<OrderCreateRequestType, OrderCreateResponseType> {
    @Autowired
    private ProcessorOfOrderCreate processorOfOrderCreate;

    @Override
    public void validateRequest(OrderCreateRequestType orderCreateRequestType) throws BusinessException {
        // 校验-基本参数缺失 因公因私标识
        CorpPayInfoUtil.check(orderCreateRequestType.getCorpPayInfo());
        // 校验-基本参数缺失 城市信息为空
        ParamCheckUtil.validCity(orderCreateRequestType.getCityInput());
        // 校验-基本参数缺失 HotelBookInput预订信息节点未传入
        if (orderCreateRequestType.getHotelBookInput() == null) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.PARAM_VALID_HOTEL_BOOK_INPUT_NULL,
                OrderCreateErrorEnum.PARAM_VALID_HOTEL_BOOK_INPUT_NULL.getErrorCode().toString());
        }
        // 校验-入离店时间
        ParamCheckUtil.validDateRange(orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo(),
                orderCreateRequestType.getIntegrationSoaRequestType(), orderCreateRequestType.getCityInput());
        // 校验-缺失资源token 不可缺失
        if (StringUtil.isBlank(
                Optional.ofNullable(orderCreateRequestType.getResourceTokenInfo()).map(ResourceTokenInfo::getResourceToken)
                        .orElse(null))) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.PARAM_VALID_RESOURCE_TOKEN_NULL,
                OrderCreateErrorEnum.PARAM_VALID_RESOURCE_TOKEN_NULL.getErrorCode().toString());
        }
        // 校验-基本参数缺失 联系人信息缺失
        if (orderCreateRequestType.getHotelContactorInfo() == null) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.PARAM_VALID_CONTACTOR_INFO_ERROR,
                OrderCreateErrorEnum.PARAM_VALID_CONTACTOR_INFO_ERROR.getErrorCode().toString());
        }
        // 校验-基本参数缺失 联系人节点有 联系人手机号必填
        if (orderCreateRequestType.getHotelContactorInfo().getPhoneInfo() == null) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.PARAM_VALID_CONTACTOR_INFO_ERROR,
                OrderCreateErrorEnum.PARAM_VALID_CONTACTOR_INFO_ERROR.getErrorCode().toString());
        }
        // 校验-基本参数缺失 入住人人信息缺失
        if (CollectionUtil.isEmpty(orderCreateRequestType.getHotelBookPassengerInputs())) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_EMPTY,
                OrderCreateErrorEnum.PARAM_VALID_CLIENT_PSG_EMPTY.getErrorCode().toString());
        }
        // 校验-保险必须有保险入住人 且年龄需要满足18-85
        checkCorpInsuranceInfo(orderCreateRequestType);
        // 校验-沿用单号不符合规范
        if (StringUtil.isNotBlank(Optional.ofNullable(orderCreateRequestType.getFollowApprovalInfoInput())
            .map(FollowApprovalInfoInput::getFollowOrderId).orElse(null))) {
            if (TemplateNumberUtil.isZeroOrNull(
                TemplateNumberUtil.parseLong(orderCreateRequestType.getFollowApprovalInfoInput().getFollowOrderId()))) {
                throw BusinessExceptionBuilder.createAlertException(
                    OrderCreateErrorEnum.PARAM_VALID_FOLLOW_ORDER_NO_ERROR,
                    OrderCreateErrorEnum.PARAM_VALID_FOLLOW_ORDER_NO_ERROR.getErrorCode().toString());
            }
        }
        if (TemplateNumberUtil.isZeroOrNull(
            Optional.ofNullable(orderCreateRequestType.getHotelBookInput()).map(HotelBookInput::getRoomQuantity)
                .orElse(null))) {
            throw BusinessExceptionBuilder.createAlertException(
                OrderCreateErrorEnum.PARAM_VALID_MISS_ROOM_QUANTITY_ERROR,
                OrderCreateErrorEnum.PARAM_VALID_MISS_ROOM_QUANTITY_ERROR.getErrorCode().toString());
        }
        checkHotelPayType(orderCreateRequestType);
    }

    @Override
    protected Processor<OrderCreateRequestType, OrderCreateResponseType> getProcessor(
            OrderCreateRequestType orderCreateRequestType) {
        return processorOfOrderCreate;
    }

    protected boolean checkHotelPayType(OrderCreateRequestType orderCreateRequestType) {
        HotelPayTypeEnum hotelPayTypeEnum =
            HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        if (hotelPayTypeEnum == null || hotelPayTypeEnum == HotelPayTypeEnum.NONE) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.ROOM_PAY_TYPE_ERROR,
                OrderCreateErrorEnum.ROOM_PAY_TYPE_ERROR.getErrorCode().toString());
        }
        return true;
    }

    protected boolean checkCorpInsuranceInfo(OrderCreateRequestType orderCreateRequestType) {
        if (orderCreateRequestType.getHotelInsuranceInput() == null) {
            return true;
        }
        if (CollectionUtil.isEmpty(orderCreateRequestType.getHotelInsuranceInput().getHotelInsuranceDetailInputs())) {
            return true;
        }
        if (StringUtil.isBlank(
                Optional.ofNullable(orderCreateRequestType.getUserActionInfo()).map(UserActionInfo::getUserActionToken)
                        .orElse(null))) {
            throw BusinessExceptionBuilder.createAlertException(
                    OrderCreateErrorEnum.PARAM_VALID_USER_ACTION_TOKEN_EMPTY,
                OrderCreateErrorEnum.PARAM_VALID_USER_ACTION_TOKEN_EMPTY.getErrorCode().toString());
        }
        orderCreateRequestType.getHotelInsuranceInput().getHotelInsuranceDetailInputs().forEach(insurance -> {
            if (insurance == null) {
                return;
            }
            if (CollectionUtil.isEmpty(insurance.getInsuranceHotelBookPassengerInputs())) {
                throw BusinessExceptionBuilder.createAlertException(
                        OrderCreateErrorEnum.PARAM_VALID_INSURANCE_PSG_EMPTY,
                    OrderCreateErrorEnum.PARAM_VALID_INSURANCE_PSG_EMPTY.getErrorCode().toString());
            }
            insurance.getInsuranceHotelBookPassengerInputs().forEach(p -> {
                if (p == null) {
                    return;
                }
                if (checkInsurancePassenger(p)) {
                    return;
                }
                throw BusinessExceptionBuilder.createAlertException(
                        OrderCreateErrorEnum.PARAM_VALID_INSURANCE_PSG_INFO_ERROR,
                    OrderCreateErrorEnum.PARAM_VALID_INSURANCE_PSG_INFO_ERROR.getErrorCode().toString());
            });
        });
        return true;
    }

    protected boolean checkInsurancePassenger(HotelBookPassengerInput insurancePsg) {
        if (insurancePsg == null) {
            return false;
        }
        if (StringUtil.isBlank(insurancePsg.getName()) && StringUtil.isBlank(
                getEname(insurancePsg.getPassengerBasicInfo()))) {
            return false;
        }
        String certificateType =
                Optional.ofNullable(insurancePsg.getCertificateInfo()).map(CertificateInfo::getCertificateType)
                        .orElse(null);
        String certificateNo =
                Optional.ofNullable(insurancePsg.getCertificateInfo()).map(CertificateInfo::getCertificateNo).orElse(null);
        if (StringUtil.isBlank(certificateType) || StringUtil.isBlank(certificateNo)) {
            return false;
        }
        return AgeUtil.ageInRange(getAge(insurancePsg));
    }

    protected int getAge(HotelBookPassengerInput insurancePsg) {
        return AgeUtil.getAgeByBirth(
            Optional.ofNullable(insurancePsg.getPassengerBasicInfo()).map(PassengerBasicInfo::getBirth).orElse(null));
    }

    private String getEname(PassengerBasicInfo inputPsg) {
        if (inputPsg == null) {
            return "";
        }
        if (StringUtil.isNotBlank(inputPsg.getPreferLastName()) && StringUtil.isNotBlank(
                inputPsg.getPreferFirstName())) {
            return inputPsg.getPreferLastName() + "/" + inputPsg.getPreferFirstName();
        }
        return "";
    }
}
