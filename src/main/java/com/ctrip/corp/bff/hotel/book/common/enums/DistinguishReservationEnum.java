package com.ctrip.corp.bff.hotel.book.common.enums;

/**
 * <AUTHOR>
 * @date 2024/12/5 23:41
 */
public enum DistinguishReservationEnum {
    /**
     * 无需操作
     */
    NONE("none"),
    /**
     * 客户选择
     */
    OPTION("option"),
    /**
     * 员工差旅
     */
    EMPLOYEE_TRAVEL("EmployeeTravel");

    private String code;

    DistinguishReservationEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}
