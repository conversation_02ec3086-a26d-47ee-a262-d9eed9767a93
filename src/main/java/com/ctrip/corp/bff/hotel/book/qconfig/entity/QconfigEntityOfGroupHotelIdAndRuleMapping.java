package com.ctrip.corp.bff.hotel.book.qconfig.entity;

/**
 * 会员卡规则
 *
 * @Author: Lorenzo
 * @Date: 2023/9/5
 */
public class QconfigEntityOfGroupHotelIdAndRuleMapping {
    private String hotelName;
    private Integer hotelGroupId;
    private Boolean cardIdRequired;
    private Integer cardIdLength;
    private Boolean letterRequired;

    public String getHotelName() {
        return hotelName;
    }

    public void setHotelName(String hotelName) {
        this.hotelName = hotelName;
    }

    public Integer getHotelGroupId() {
        return hotelGroupId;
    }

    public void setHotelGroupId(Integer hotelGroupId) {
        this.hotelGroupId = hotelGroupId;
    }

    public Boolean getCardIdRequired() {
        return cardIdRequired;
    }

    public void setCardIdRequired(Boolean cardIdRequired) {
        this.cardIdRequired = cardIdRequired;
    }

    public Integer getCardIdLength() {
        return cardIdLength;
    }

    public void setCardIdLength(Integer cardIdLength) {
        this.cardIdLength = cardIdLength;
    }

    public Boolean getLetterRequired() {
        return letterRequired;
    }

    public void setLetterRequired(Boolean letterRequired) {
        this.letterRequired = letterRequired;
    }
}
