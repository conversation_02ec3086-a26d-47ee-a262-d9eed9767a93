package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response;

import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.CreateOrderResult;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.InsuranceRoutAmountResult;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.PayAmountResult;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.PaymentItemTypeResult;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.AmountInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.HotelPayTypeUtil;
import com.ctrip.corp.bff.hotel.book.common.util.MathUtils;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType;
import com.ctrip.corp.bff.hotel.book.contract.PriceChangeInfo;
import com.ctrip.corp.hotelbooking.hotelws.entity.AmountDifferenceType;
import com.ctrip.corp.hotelbooking.hotelws.entity.AmountInfoType;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType;
import com.ctrip.corp.hotelbooking.hotelws.entity.OrderAmountType;
import com.ctrip.corp.hotelbooking.hotelws.entity.OrderPaymentType;
import com.ctrip.corp.hotelbooking.hotelws.entity.SubTransactionInfo;
import com.ctrip.corp.hotelbooking.hotelws.entity.TransactionInfo;
import com.ctrip.soa._21210.HotelOrderRepeatOrderResponseType;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 变价响应
 * @Date 2024/7/15 14:20
 * @Version 1.0
 */
@Component public class MapperOfPriceChangeInfoResponse extends
    AbstractMapper<Tuple3<CreateOrderResponseType, OrderCreateToken, OrderCreateRequestType>, Tuple2<Boolean, OrderCreateResponseType>> {
    private static final String ORDER_CHANGE_HIGHER = "ORDER_CHANGE_HIGHER";

    @Override protected Tuple2<Boolean, OrderCreateResponseType> convert(
        Tuple3<CreateOrderResponseType, OrderCreateToken, OrderCreateRequestType> tuple) {
        CreateOrderResponseType createOrderResponseType = tuple.getT1();
        OrderCreateToken orderCreateToken = tuple.getT2();
        OrderCreateRequestType orderCreateRequestType = tuple.getT3();
        OrderCreateResponseType orderCreateResponseType = new OrderCreateResponseType();
        orderCreateResponseType.setPriceChangeInfo(buildPriceChangeInfo(createOrderResponseType));
        orderCreateToken.addContinueTypes(ContinueTypeConst.PRICE_CHANGE);
        orderCreateToken.setCreateOrderResult(
            OrderCreateProcessorOfUtil.buildCreateOrderResult(createOrderResponseType, orderCreateRequestType,
                orderCreateToken));
        orderCreateResponseType.setOrderCreateToken(
            TokenParseUtil.generateToken(orderCreateToken, OrderCreateToken.class));
        return Tuple2.of(true, orderCreateResponseType);
    }

    @Override
    protected ParamCheckResult check(Tuple3<CreateOrderResponseType, OrderCreateToken, OrderCreateRequestType> tuple) {
        return null;
    }

    private PriceChangeInfo buildPriceChangeInfo(CreateOrderResponseType createOrderResponseType) {
        PriceChangeInfo priceChangeInfo = new PriceChangeInfo();
        priceChangeInfo.setAfterPayType(createOrderResponseType.getOrderPaymentInfo().getPayType());
        AmountInfo amountInfo = new AmountInfo();
        amountInfo.setAmount(
            createOrderResponseType.getOrderPaymentInfo().getAmountDifferenceInfo().getAmount().toString());
        amountInfo.setCurrency(createOrderResponseType.getOrderPaymentInfo().getAmountDifferenceInfo().getCurrency());
        priceChangeInfo.setPriceChangeAmountInfo(amountInfo);
        priceChangeInfo.setPriceChangeType(ORDER_CHANGE_HIGHER);
        return priceChangeInfo;
    }
}
