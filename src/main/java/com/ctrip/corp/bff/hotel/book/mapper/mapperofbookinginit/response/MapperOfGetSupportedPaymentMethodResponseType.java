package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.response;

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.SoaErrorSharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.BookingInitErrorEnum;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodResponseType;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/7/30
 */
@Component
public class MapperOfGetSupportedPaymentMethodResponseType extends AbstractMapper<Tuple1<GetSupportedPaymentMethodResponseType>, Void> {
    @Override
    protected Void convert(Tuple1<GetSupportedPaymentMethodResponseType> getSupportedPaymentMethodResponseTypeTuple1) {
        return null;
    }

    @Override
    protected ParamCheckResult check(Tuple1<GetSupportedPaymentMethodResponseType> para) {
        GetSupportedPaymentMethodResponseType getSupportedPaymentMethodResponseType = para.getT1();
        if (getSupportedPaymentMethodResponseType != null && TemplateNumberUtil.equals(
                getSupportedPaymentMethodResponseType.getResponseCode(), CommonConstant.SUCCESS_20000)) {
            return null;
        }
        Integer logErrorCode = Optional.ofNullable(getSupportedPaymentMethodResponseType)
                .map(GetSupportedPaymentMethodResponseType::getResponseCode)
                .orElse(BookingInitErrorEnum.ACTION_NAME_GET_SUPPORTED_PAYMENT_METHOD.getErrorCode());
        String friendlyMessage = SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessage(
                SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_HOTEL_BOOK_COMMON_WS,
                SoaErrorSharkKeyConstant.ACTION_NAME_GET_SUPPORTED_PAYMENT_METHOD, String.valueOf(logErrorCode));
        if (StringUtil.isBlank(friendlyMessage)) {
            friendlyMessage = SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessageDefault(
                    SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_HOTEL_BOOK_COMMON_WS,
                    SoaErrorSharkKeyConstant.ACTION_NAME_GET_SUPPORTED_PAYMENT_METHOD);
        }
        return new ParamCheckResult(false,
                BookingInitErrorEnum.ACTION_NAME_GET_SUPPORTED_PAYMENT_METHOD.getErrorCode(),
                logErrorCode.toString(),
                Optional.ofNullable(getSupportedPaymentMethodResponseType)
                .map(GetSupportedPaymentMethodResponseType::getResponseDesc).orElse(null),
                friendlyMessage);
    }
}
