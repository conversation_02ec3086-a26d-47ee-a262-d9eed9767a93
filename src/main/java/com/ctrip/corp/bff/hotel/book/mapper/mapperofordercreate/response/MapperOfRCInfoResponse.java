package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response;

import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.*;
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextResponseType;
import com.ctrip.corp.agg.hotel.tmc.entity.FinalFloatingAmountSettingType;
import com.ctrip.corp.agg.hotel.tmc.entity.FinalPolicyType;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelRcInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.common.RcToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.RepeatOrderResult;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.RCContent;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.RCInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.OverStandardTypeConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.SharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.SoaErrorSharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.RcCustomizedSharkByCorpEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.RcTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfOrderRcInfo;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.CustomizedSharkConfig;
import com.ctrip.corp.bff.hotel.book.common.util.HotelPayTypeUtil;
import com.ctrip.corp.bff.hotel.book.common.util.HotelRCUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType;
import com.ctrip.corp.bff.hotel.book.contract.RepeatOrderInfo;
import com.ctrip.corp.foundation.common.enums.LanguageLocaleEnum;
import com.ctrip.corp.foundation.common.util.StringUtilsExt;
import com.google.common.collect.Lists;
import corp.user.service.corp4jservice.GetReasoncodesResponseType;
import corp.user.service.corp4jservice.ReasoncodeInfo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.function.Predicate;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description RC信息响应
 * @Date 2024/7/13 12:10
 * @Version 1.0
 * 差标RC信息响应
 * 重复预订RC信息响应
 */
@Component public class MapperOfRCInfoResponse
    extends AbstractMapper<Tuple1<WrapperOfOrderRcInfo>, Tuple2<Boolean, OrderCreateResponseType>> {

    private static final String EDIT_RC_CODE = "VV";

    private static final String FORBID_BOOKING = "FORBID_BOOKING";

    @Override protected Tuple2<Boolean, OrderCreateResponseType> convert(Tuple1<WrapperOfOrderRcInfo> tuple) {
        WrapperOfOrderRcInfo wrapperOfOrderRcInfo = tuple.getT1();
        CheckTravelPolicyResponseType checkTravelPolicyResponseType =
            wrapperOfOrderRcInfo.getCheckTravelPolicyResponseType();
        RepeatOrderInfo repeatOrderInfo = wrapperOfOrderRcInfo.getRepeatOrderInfo();
        OrderCreateToken orderCreateToken = wrapperOfOrderRcInfo.getOrderCreateToken();
        OrderCreateRequestType orderCreateRequestType = wrapperOfOrderRcInfo.getOrderCreateRequestType();
        GetReasoncodesResponseType getReasoncodesResponseType = wrapperOfOrderRcInfo.getGetReasoncodesResponseType();
        List<CustomizedSharkConfig> customizedSharkConfigs = wrapperOfOrderRcInfo.getCustomizedSharkConfigList();
        GetTravelPolicyContextResponseType getTravelPolicyContextResponseType =
            wrapperOfOrderRcInfo.getGetTravelPolicyContextResponseType();
        WrapperOfAccount.AccountInfo accountInfo = wrapperOfOrderRcInfo.getAccountInfo();

        OrderCreateResponseType orderCreateResponseType = new OrderCreateResponseType();
        orderCreateResponseType.setRepeatOrderInfo(repeatOrderInfo);
        Tuple2<Boolean, OrderCreateResponseType> tupleResult = Tuple2.of(false, null);
        // 用户创单已经选完RC后 1.RC校验 + 2.有效RC赋值---按道理此mapper无需有剔除无效rc的逻辑了，应该在使用方例如match审批流、创单传参mapper那里处理
        if (orderCreateToken.containsContinueType(ContinueTypeConst.RC_CONTROL)
            || orderCreateToken.containsContinueType(ContinueTypeConst.CONFLICT_ORDER)) {
            return tupleResult;
        }
        // 重复订单+rc时 对重复订单赋值
        if (CollectionUtil.isNotEmpty(
            Optional.ofNullable(repeatOrderInfo).map(RepeatOrderInfo::getRepeatOrderDetails).orElse(null))) {
            orderCreateToken.addContinueTypes(ContinueTypeConst.CONFLICT_ORDER);
            orderCreateToken.setRepeatOrderResults(
                orderCreateResponseType.getRepeatOrderInfo().getRepeatOrderDetails().stream().map(repeatOrderDetail -> {
                    RepeatOrderResult repeatOrderResult = new RepeatOrderResult();
                    repeatOrderResult.setOrderId(repeatOrderDetail.getOrderId());
                    return repeatOrderResult;
                }).collect(Collectors.toList()));
            orderCreateResponseType.setOrderCreateToken(
                TokenParseUtil.generateToken(orderCreateToken, OrderCreateToken.class));
            tupleResult = Tuple2.of(true, orderCreateResponseType);
        }
        // 获取需要用户需要选择的RC
        List<HotelRcInfo> hotelRcInfo =
            buildRCInfo(checkTravelPolicyResponseType, repeatOrderInfo, orderCreateRequestType,
                getReasoncodesResponseType, customizedSharkConfigs, getTravelPolicyContextResponseType, accountInfo,
                wrapperOfOrderRcInfo.getResourceToken());
        if (CollectionUtil.isNotEmpty(hotelRcInfo)) {
            orderCreateResponseType.setHotelRcInfo(hotelRcInfo);
            orderCreateToken.addContinueTypes(ContinueTypeConst.RC_CONTROL);
            orderCreateResponseType.setOrderCreateToken(
                TokenParseUtil.generateToken(orderCreateToken, OrderCreateToken.class));
            tupleResult = Tuple2.of(true, orderCreateResponseType);
        }
        return tupleResult;
    }

    @Override protected ParamCheckResult check(Tuple1<WrapperOfOrderRcInfo> tuple) {
        WrapperOfOrderRcInfo wrapperOfOrderRcInfo = tuple.getT1();
        CheckTravelPolicyResponseType checkTravelPolicyResponseType =
            wrapperOfOrderRcInfo.getCheckTravelPolicyResponseType();
        if (!OrderCreateProcessorOfUtil.requireCheckTravelPolicyControl(
            wrapperOfOrderRcInfo.getOrderCreateRequestType())) {
            return null;
        }
        int logErrorCode =
            Optional.ofNullable(checkTravelPolicyResponseType).map(CheckTravelPolicyResponseType::getResponseCode)
                .orElse(OrderCreateErrorEnum.CHECK_TRAVEL_POLICY_RESPONSE_IS_ERROR.getErrorCode());
        if (logErrorCode != CommonConstant.SUCCESS_20000) {
            String friendlyMessage = SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessage(
                SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_AGG_HOTEL_TMC_SERVICE,
                SoaErrorSharkKeyConstant.ACTION_NAME_CHECK_TRAVEL_POLICY, String.valueOf(logErrorCode));
            return new ParamCheckResult(false,
                OrderCreateErrorEnum.CHECK_TRAVEL_POLICY_RESPONSE_IS_ERROR.getErrorCode(), String.valueOf(logErrorCode),
                Optional.ofNullable(checkTravelPolicyResponseType).map(CheckTravelPolicyResponseType::getResponseDesc)
                    .orElse(null), friendlyMessage);
        }
        // 员工房管控, 默认管控通过
        boolean empRoomInControl = Optional.ofNullable(checkTravelPolicyResponseType).map(CheckTravelPolicyResponseType::getCheckItemsResult)
                .map(CheckItemsResultType::getEmployeeHotelCheckResult).map(EmployeeHotelCheckResultType::isInControl).orElse(true);
        if (!empRoomInControl) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.EMP_ROOM_NOT_IN_CONTROL);
        }
        if (checkTravelPolicyResponseType.getCheckRcResult() == null) {
            return null;
        }
        if (CollectionUtil.isEmpty(checkTravelPolicyResponseType.getCheckRcResult().getCheckResultList())) {
            return null;
        }
        if (checkTravelPolicyResponseType.getCheckRcResult().getCheckResultList().stream()
            .anyMatch(item -> StringUtil.compareIgnoreCase(item.getCheckResult(), FORBID_BOOKING))) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.FORBID_BOOKING);
        }
        return null;
    }

    /**
     * 获取可定管控需要弹的rc + 重复订单rc
     *
     * @return
     */
    private List<HotelRcInfo> buildRCInfo(CheckTravelPolicyResponseType checkTravelPolicyResponseType,
        RepeatOrderInfo repeatOrderInfo, OrderCreateRequestType orderCreateRequestType,
        GetReasoncodesResponseType getReasoncodesResponseType, List<CustomizedSharkConfig> customizedSharkConfigs,
        GetTravelPolicyContextResponseType getTravelPolicyContextResponseType,
        WrapperOfAccount.AccountInfo accountInfo, ResourceToken resourceToken) {
        List<HotelRcInfo> hotelRcInfo = new ArrayList<>();
        // 差标RC
        List<HotelRcInfo> checkTravelPolicyRc =
            getRcInfos(checkTravelPolicyResponseType, orderCreateRequestType, getReasoncodesResponseType,
                customizedSharkConfigs, getTravelPolicyContextResponseType, resourceToken);
        if (CollectionUtil.isNotEmpty(checkTravelPolicyRc)) {
            hotelRcInfo.addAll(checkTravelPolicyRc);
        }
        // 重复订单RC
        HotelRcInfo conflictRc = buildConflictRCContent(getReasoncodesResponseType, repeatOrderInfo, accountInfo,
            orderCreateRequestType.getIntegrationSoaRequestType());
        if (conflictRc != null) {
            hotelRcInfo.add(conflictRc);
        }
        return hotelRcInfo;
    }

    public List<HotelRcInfo> getRcInfos(CheckTravelPolicyResponseType checkTravelPolicyResponseType,
        OrderCreateRequestType orderCreateRequestType, GetReasoncodesResponseType getReasoncodesResponseType,
        List<CustomizedSharkConfig> customizedSharkConfigs,
        GetTravelPolicyContextResponseType getTravelPolicyContextResponseType, ResourceToken resourceToken) {
        // 因私
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())) {
            return null;
        }
        CheckRcResultType checkRcResult =
            Optional.ofNullable(checkTravelPolicyResponseType).map(CheckTravelPolicyResponseType::getCheckRcResult)
                .orElse(null);
        if (checkRcResult == null) {
            return null;
        }
        List<HotelRcInfo> hotelRcInfos = new ArrayList<>();
        CheckOverStandardRcInfoType checkOverStandardRcInfoType =
            OrderCreateProcessorOfUtil.getCheckOverStandardRcInfoType(checkTravelPolicyResponseType,
                orderCreateRequestType.getHotelPayTypeInput(), resourceToken);
        // 低价
        hotelRcInfos.add(
            buildRCContentOverStand(getReasoncodesResponseType, orderCreateRequestType.getIntegrationSoaRequestType(),
                checkOverStandardRcInfoType, customizedSharkConfigs, getTravelPolicyContextResponseType));
        // 协议
        hotelRcInfos.add(buildRCContentAgreement(checkRcResult.getAgreementRc(), getReasoncodesResponseType,
            orderCreateRequestType.getIntegrationSoaRequestType(), customizedSharkConfigs));
        // 提前预订
        if (checkRcResult.getBookAheadRc() != null) {
            hotelRcInfos.add(buildRCContentBookAhead(checkRcResult.getBookAheadRc(), getReasoncodesResponseType,
                orderCreateRequestType.getIntegrationSoaRequestType(), customizedSharkConfigs));
        }
        return hotelRcInfos.stream().filter(Objects::nonNull).collect(Collectors.toList());
    }

    private HotelRcInfo buildRCContentOverStand(GetReasoncodesResponseType getReasoncodesResponseType,
        IntegrationSoaRequestType integrationSoaRequestType, CheckOverStandardRcInfoType checkOverStandardRcInfoType,
        List<CustomizedSharkConfig> customizedSharkConfigs,
        GetTravelPolicyContextResponseType getTravelPolicyContextResponseType) {
        if (checkOverStandardRcInfoType == null) {
            return null;
        }
        if (!BooleanUtils.isTrue(Boolean.TRUE.equals(checkOverStandardRcInfoType.isRequired()) && !Boolean.TRUE.equals(
            checkOverStandardRcInfoType.isSelected()))) {
            return null;
        }
        List<ReasoncodeInfo> reasonCodeInfos = getPriceReasons(getReasoncodesResponseType, RcTypeEnum.LOW_PRICE);
        HotelRcInfo hotelRcInfo = new HotelRcInfo();
        hotelRcInfo.setRcType(RcTypeEnum.LOW_PRICE.name());
        hotelRcInfo.setRcTitle(
            buildRcTitleOverStand(checkOverStandardRcInfoType, integrationSoaRequestType, customizedSharkConfigs,
                getTravelPolicyContextResponseType));
        hotelRcInfo.setRcDesc(RcTypeEnum.LOW_PRICE.getRcDesc());
        reasonCodeInfos.stream().filter(o -> EDIT_RC_CODE.equals(o.getReasonCode())).collect(Collectors.toList())
            .stream().findFirst().ifPresent(reasoncodeInfoVV -> hotelRcInfo.setRcTip(
                getRcValueByLanguage(reasoncodeInfoVV.getRcTip(), reasoncodeInfoVV.getRcTipEn(),
                    integrationSoaRequestType)));
        RCInfo rcInfo = new RCInfo();
        rcInfo.setRcContents(buildRcContents(reasonCodeInfos, RcTypeEnum.LOW_PRICE, integrationSoaRequestType));
        hotelRcInfo.setRcInfo(rcInfo);
        return hotelRcInfo;
    }

    private String buildRcTitleOverStand(CheckOverStandardRcInfoType checkOverStandardRcInfoType,
        IntegrationSoaRequestType integrationSoaRequestType, List<CustomizedSharkConfig> customizedSharkConfigs,
        GetTravelPolicyContextResponseType getTravelPolicyContextResponseType) {
        List<Integer> overStandardRoomIndex = getOverStandardRoomIndex(checkOverStandardRcInfoType);
        String title = null;
        String overStandardType = HotelRCUtil.overStandardType(checkOverStandardRcInfoType);
        if (CollectionUtils.isNotEmpty(overStandardRoomIndex) && isShowRoomTitle(checkOverStandardRcInfoType)) {
            // 按房间管控 出行人模式低价RC按房间管控返回Title
            StringBuilder stringBuilder = new StringBuilder();
            for (int i = 0; i < overStandardRoomIndex.size(); i++) {
                if (i == overStandardRoomIndex.size() - 1) {
                    stringBuilder.append(StringUtil.indexedFormat(BFFSharkUtil.getSharkValue(SharkKeyConstant.RC_ROOM),
                        overStandardRoomIndex.get(i)));
                } else {
                    stringBuilder.append(StringUtil.indexedFormat(BFFSharkUtil.getSharkValue(SharkKeyConstant.RC_ROOM),
                        overStandardRoomIndex.get(i))).append("、");
                }
            }
            // 获取按公司定制rc：若该公司且输入的keyPre支持定制,则获取到的为定制文案，否则为keyPre对应的兜底文案
            RcCustomizedSharkByCorpEnum rcCustomizedSharkByCorpEnumHasSuffix =
                RcCustomizedSharkByCorpEnum.getNowEnumByRcType(
                    RcCustomizedSharkByCorpEnum.LOW_PRICE_R_RC.getRcCode() + getRcTypeSuffix(RcTypeEnum.LOW_PRICE,
                        overStandardType));
            String customizedRc = HotelRCUtil.getValueByCorpId(
                rcCustomizedSharkByCorpEnumHasSuffix != null ? rcCustomizedSharkByCorpEnumHasSuffix.getShark() :
                    RcCustomizedSharkByCorpEnum.LOW_PRICE_R_RC.getShark(),
                rcCustomizedSharkByCorpEnumHasSuffix != null ? rcCustomizedSharkByCorpEnumHasSuffix.getShark() : null,
                integrationSoaRequestType.getUserInfo().getCorpId(),
                RcCustomizedSharkByCorpEnum.LOW_PRICE_R_RC.getScene(), customizedSharkConfigs);
            if (StringUtil.isNotBlank(customizedRc) && customizedRc.contains("{0}")) {
                // 按房间管控话术
                title = StringUtil.indexedFormat(customizedRc, stringBuilder.toString());
            } else {
                // 只有定制话术才有不存在按房间管控的可能
                title = customizedRc;
            }
        } else {
            // 按单管控 按公司定制rc
            RcCustomizedSharkByCorpEnum rcCustomizedSharkByCorpEnum =
                RcCustomizedSharkByCorpEnum.getNowEnumByRcType(RcTypeEnum.LOW_PRICE.getCode());
            RcCustomizedSharkByCorpEnum rcCustomizedSharkByCorpEnumHasSuffix =
                RcCustomizedSharkByCorpEnum.getNowEnumByRcType(
                    RcTypeEnum.LOW_PRICE.getCode() + getRcTypeSuffix(RcTypeEnum.LOW_PRICE, overStandardType));
            String customizedRc = HotelRCUtil.getValueByCorpId(
                rcCustomizedSharkByCorpEnumHasSuffix != null ? rcCustomizedSharkByCorpEnumHasSuffix.getShark() :
                    rcCustomizedSharkByCorpEnum.getShark(),
                rcCustomizedSharkByCorpEnumHasSuffix != null ? rcCustomizedSharkByCorpEnumHasSuffix.getShark() : null,
                integrationSoaRequestType.getUserInfo().getCorpId(),
                RcCustomizedSharkByCorpEnum.getNowEnumByRcType(RcTypeEnum.LOW_PRICE.getCode()).getScene(),
                customizedSharkConfigs);
            if (StringUtil.isNotBlank(customizedRc)) {
                title = customizedRc;
            } else {
                if (OverStandardTypeConstant.OVER_PRICE.equals(overStandardType)) {
                    title = BFFSharkUtil.getSharkValue(RcCustomizedSharkByCorpEnum.LOW_PRICE_RC_PRICE.getShark());
                }
                if (OverStandardTypeConstant.OVER_STAR.equals(overStandardType)) {
                    title = BFFSharkUtil.getSharkValue(RcCustomizedSharkByCorpEnum.LOW_PRICE_RC_STAR.getShark());
                }
                if (StringUtil.isEmpty(title)) {
                    title = RcTypeEnum.LOW_PRICE.getRcTitle();
                }
            }
        }
        if (OverStandardTypeConstant.OVER_PRICE.equalsIgnoreCase(overStandardType)
                && Optional.ofNullable(getOpenFloatAmount(getTravelPolicyContextResponseType)).orElse(false)) {
            title += BFFSharkUtil.getSharkValue(StringUtilsExt.format(SharkKeyConstant.FLOAT_OVER_PRICE, getFloatAmountType(getTravelPolicyContextResponseType)));
        }
        return title;
    }

    private HotelRcInfo buildRCContentBookAhead(CheckBookAheadRcInfoType checkBookAheadRcInfoType,
        GetReasoncodesResponseType getReasoncodesResponseType, IntegrationSoaRequestType integrationSoaRequestType,
        List<CustomizedSharkConfig> customizedSharkConfigs) {
        if (checkBookAheadRcInfoType == null) {
            return null;
        }
        if (!BooleanUtils.isTrue(Boolean.TRUE.equals(checkBookAheadRcInfoType.isRequired()) && !Boolean.TRUE.equals(
            checkBookAheadRcInfoType.isSelected()))) {
            return null;
        }
        List<ReasoncodeInfo> reasonCodeInfos = getPriceReasons(getReasoncodesResponseType, RcTypeEnum.BOOK_AHEAD);
        HotelRcInfo hotelRcInfo = new HotelRcInfo();
        hotelRcInfo.setRcType(RcTypeEnum.BOOK_AHEAD.name());
        hotelRcInfo.setRcTitle(buildRcTitleBookAhead(integrationSoaRequestType, customizedSharkConfigs,
            checkBookAheadRcInfoType.getBookAheadDay()));
        hotelRcInfo.setRcDesc(RcTypeEnum.BOOK_AHEAD.getRcDesc());
        reasonCodeInfos.stream().filter(o -> EDIT_RC_CODE.equals(o.getReasonCode())).collect(Collectors.toList())
            .stream().findFirst().ifPresent(reasoncodeInfoVV -> hotelRcInfo.setRcTip(
                getRcValueByLanguage(reasoncodeInfoVV.getRcTip(), reasoncodeInfoVV.getRcTipEn(),
                    integrationSoaRequestType)));
        RCInfo rcInfo = new RCInfo();
        rcInfo.setRcContents(buildRcContents(reasonCodeInfos, RcTypeEnum.BOOK_AHEAD, integrationSoaRequestType));
        hotelRcInfo.setRcInfo(rcInfo);
        return hotelRcInfo;
    }

    private HotelRcInfo buildRCContentAgreement(CheckAgreementRcInfoType checkAgreementRcInfoType,
        GetReasoncodesResponseType getReasoncodesResponseType, IntegrationSoaRequestType integrationSoaRequestType,
        List<CustomizedSharkConfig> customizedSharkConfigs) {
        if (checkAgreementRcInfoType == null) {
            return null;
        }
        if (!BooleanUtils.isTrue(Boolean.TRUE.equals(checkAgreementRcInfoType.isRequired()) && !Boolean.TRUE.equals(
            checkAgreementRcInfoType.isSelected()))) {
            return null;
        }
        List<ReasoncodeInfo> reasonCodeInfos = getPriceReasons(getReasoncodesResponseType, RcTypeEnum.AGREEMENT);
        HotelRcInfo hotelRcInfo = new HotelRcInfo();
        hotelRcInfo.setRcType(RcTypeEnum.AGREEMENT.name());
        hotelRcInfo.setRcTitle(buildRcTitleAgreement(integrationSoaRequestType, customizedSharkConfigs));
        hotelRcInfo.setRcDesc(RcTypeEnum.AGREEMENT.getRcDesc());
        reasonCodeInfos.stream().filter(o -> EDIT_RC_CODE.equals(o.getReasonCode())).collect(Collectors.toList())
            .stream().findFirst().ifPresent(reasoncodeInfoVV -> hotelRcInfo.setRcTip(
                getRcValueByLanguage(reasoncodeInfoVV.getRcTip(), reasoncodeInfoVV.getRcTipEn(),
                    integrationSoaRequestType)));
        RCInfo rcInfo = new RCInfo();
        rcInfo.setRcContents(buildRcContents(reasonCodeInfos, RcTypeEnum.AGREEMENT, integrationSoaRequestType));
        hotelRcInfo.setRcInfo(rcInfo);
        return hotelRcInfo;
    }

    private List<RCContent> buildRcContents(List<ReasoncodeInfo> reasonCodeInfos, RcTypeEnum rcTypeEnum,
        IntegrationSoaRequestType integrationSoaRequestType) {
        if (CollectionUtil.isEmpty(reasonCodeInfos)) {
            return null;
        }
        List<RCContent> rcContents = new ArrayList<>();
        reasonCodeInfos.stream().forEach(reasonCodeInfo -> {
            if (StringUtil.isEmpty(reasonCodeInfo.getReasonInfo())) {
                return;
            }
            RCContent rcContent = new RCContent();
            rcContent.setType(rcTypeEnum.name());
            rcContent.setCode(reasonCodeInfo.getReasonCode());
            String rcValue = getRcValueByLanguage(reasonCodeInfo.getReasonInfo(), reasonCodeInfo.getReasonInfoEn(),
                integrationSoaRequestType);
            String canInput = EDIT_RC_CODE.equals(reasonCodeInfo.getReasonCode()) ? BooleanUtil.parseStr(true) :
                BooleanUtil.parseStr(false);
            rcContent.setValue(rcValue);
            rcContent.setCanInput(canInput);
            RcToken rcToken = new RcToken();
            rcToken.setType(rcTypeEnum.name());
            rcToken.setCode(reasonCodeInfo.getReasonCode());
            rcToken.setCanInput(canInput);
            rcToken.setValue(rcContent.getValue());
            rcContent.setRcToken(TokenParseUtil.generateToken(rcToken, RcToken.class));
            rcContents.add(rcContent);
        });
        return rcContents;
    }

    private String buildRcTitleAgreement(IntegrationSoaRequestType integrationSoaRequestType,
        List<CustomizedSharkConfig> customizedSharkConfigs) {
        // 按公司定制rc
        String customizedRc = HotelRCUtil.getValueByCorpId(
            RcCustomizedSharkByCorpEnum.getNowEnumByRcType(RcTypeEnum.AGREEMENT.getCode()).getShark(),
            RcCustomizedSharkByCorpEnum.getNowEnumByRcType(RcTypeEnum.AGREEMENT.getCode()).getShark(),
            integrationSoaRequestType.getUserInfo().getCorpId(),
            RcCustomizedSharkByCorpEnum.getNowEnumByRcType(RcTypeEnum.AGREEMENT.getCode()).getScene(),
            customizedSharkConfigs);
        if (StringUtil.isNotBlank(customizedRc)) {
            return customizedRc;
        }
        return RcTypeEnum.AGREEMENT.getRcTitle();
    }

    private String buildRcTitleBookAhead(IntegrationSoaRequestType integrationSoaRequestType,
        List<CustomizedSharkConfig> customizedSharkConfigs, Integer daysInAdvance) {
        String title = null;
        // 按公司定制rc
        String customizedRc = HotelRCUtil.getValueByCorpId(
            RcCustomizedSharkByCorpEnum.getNowEnumByRcType(RcTypeEnum.BOOK_AHEAD.getCode()).getShark(),
            RcCustomizedSharkByCorpEnum.getNowEnumByRcType(RcTypeEnum.BOOK_AHEAD.getCode()).getShark(),
            integrationSoaRequestType.getUserInfo().getCorpId(),
            RcCustomizedSharkByCorpEnum.getNowEnumByRcType(RcTypeEnum.BOOK_AHEAD.getCode()).getScene(),
            customizedSharkConfigs);
        if (StringUtil.isNotBlank(customizedRc)) {
            title = customizedRc;
        } else {
            title = RcTypeEnum.BOOK_AHEAD.getRcTitle();
        }
        if (title.contains("{0}")) {
            return title.replace("{0}", daysInAdvance == null ? "1" : daysInAdvance.toString());
        } else {
            return title;
        }
    }

    private static Boolean getOpenFloatAmount(GetTravelPolicyContextResponseType getTravelPolicyContextResponseType) {
        return Optional.ofNullable(getTravelPolicyContextResponseType)
            .map(GetTravelPolicyContextResponseType::getFinalPolicy).map(FinalPolicyType::getFinalFloatingAmountSetting)
            .map(setting -> setting.getFloatingAmount() != null
                && setting.getFloatingAmount().compareTo(BigDecimal.ZERO) > 0).orElse(false);
    }

    private static String getFloatAmountType(GetTravelPolicyContextResponseType getTravelPolicyContextResponseType) {
        return Optional.ofNullable(getTravelPolicyContextResponseType)
                .map(GetTravelPolicyContextResponseType::getFinalPolicy).map(FinalPolicyType::getFinalFloatingAmountSetting)
                .map(FinalFloatingAmountSettingType::getFloatAmountType).orElse("");
    }

    private String getRcTypeSuffix(RcTypeEnum rcTypeEnum, String overStandardType) {
        if (rcTypeEnum != RcTypeEnum.LOW_PRICE) {
            return "";
        }
        if (OverStandardTypeConstant.OVER_PRICE.equals(overStandardType)) {
            return "_PRICE";
        }
        if (OverStandardTypeConstant.OVER_STAR.equals(overStandardType)) {
            return "_STAR";
        }
        return "";
    }

    /**
     * 低价RC是否显示Title
     */
    private static boolean isShowRoomTitle(CheckOverStandardRcInfoType checkOverStandardRcInfoType) {
        if (CollectionUtil.isEmpty(checkOverStandardRcInfoType.getRoomModeInfoList())) {
            return false;
        }
        List<Integer> overStandardRoomIndex = getOverStandardRoomIndex(checkOverStandardRcInfoType);
        if (CollectionUtil.isEmpty(overStandardRoomIndex)) {
            return false;
        }
        return overStandardRoomIndex.size() != checkOverStandardRcInfoType.getRoomModeInfoList().size();
    }

    /**
     * 获取超标房间列表
     */
    private static List<Integer> getOverStandardRoomIndex(CheckOverStandardRcInfoType checkOverStandardRcInfoType) {
        if (CollectionUtil.isEmpty(checkOverStandardRcInfoType.getRoomModeInfoList())) {
            return Lists.newArrayList();
        }
        Predicate<RoomModeInfoType> controlPredicate =
            r -> BooleanUtils.isFalse(r.isPriceInControl()) || BooleanUtils.isFalse(r.isStarInControl());
        return checkOverStandardRcInfoType.getRoomModeInfoList().stream().filter(Objects::nonNull)
            .filter(controlPredicate).map(RoomModeInfoType::getRoomIndex).sorted().collect(Collectors.toList());
    }

    private String getRcValueByLanguage(String rcValue, String defaultEnRcValue,
        IntegrationSoaRequestType requestType) {
        return LanguageLocaleEnum.ZH_CN.getLanguageLocaleString().equalsIgnoreCase(requestType.getLanguage()) ?
            rcValue : defaultEnRcValue;
    }

    private List<ReasoncodeInfo> getPriceReasons(GetReasoncodesResponseType getReasoncodesResponseType,
        RcTypeEnum rcTypeEnum) {
        if (getReasoncodesResponseType == null) {
            return null;
        }
        if (CollectionUtil.isEmpty(getReasoncodesResponseType.getReasonCodes())) {
            return null;
        }
        List<ReasoncodeInfo> reasonCodeInfos =
            getReasoncodesResponseType.getReasonCodes().stream().filter(Objects::nonNull).filter(
                    reasonCode -> reasonCode.getRcType() != null
                        && (reasonCode.getRcType() & rcTypeEnum.getRcType()) == rcTypeEnum.getRcType())
                .collect(Collectors.toList());
        if (CollectionUtil.isEmpty(reasonCodeInfos)) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.REASONCODE_EMPTY,
                "getRcInfos error");
        }
        Collections.sort(reasonCodeInfos, ((o1, o2) -> {
            if (EDIT_RC_CODE.equals(o1.getReasonCode())) {
                return 1;
            }
            if (EDIT_RC_CODE.equals(o2.getReasonCode())) {
                return -1;
            }
            return 0;
        }));
        return reasonCodeInfos;
    }

    protected HotelRcInfo buildConflictRCContent(GetReasoncodesResponseType getReasoncodesResponseType,
        RepeatOrderInfo repeatOrderInfo, WrapperOfAccount.AccountInfo accountInfo,
        IntegrationSoaRequestType integrationSoaRequestType) {
        if (accountInfo != null && !StringUtils.equalsIgnoreCase(CommonConstant.OPEN,
            accountInfo.getRepeatBookingReason())) {
            return null;
        }
        if (repeatOrderInfo == null || CollectionUtil.isEmpty(repeatOrderInfo.getRepeatOrderDetails())) {
            return null;
        }
        if (CommonConstant.FORBID_BOOKING.equalsIgnoreCase(repeatOrderInfo.getRepeatOrderRule())) {
            return null;
        }
        List<ReasoncodeInfo> reasonCodeInfos = getPriceReasons(getReasoncodesResponseType, RcTypeEnum.CONFLICT_BOOK);
        HotelRcInfo rcInfo = new HotelRcInfo();
        rcInfo.setRcType(RcTypeEnum.CONFLICT_BOOK.name());
        rcInfo.setRcTitle(RcTypeEnum.CONFLICT_BOOK.getRcTitle());
        rcInfo.setRcDesc(RcTypeEnum.CONFLICT_BOOK.getRcDesc());
        RCInfo rcContent = new RCInfo();
        rcContent.setRcContents(buildRcContents(reasonCodeInfos, RcTypeEnum.CONFLICT_BOOK, integrationSoaRequestType));
        rcInfo.setRcInfo(rcContent);
        return rcInfo;
    }
}
