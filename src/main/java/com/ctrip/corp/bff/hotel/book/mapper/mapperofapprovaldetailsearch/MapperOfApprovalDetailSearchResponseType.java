package com.ctrip.corp.bff.hotel.book.mapper.mapperofapprovaldetailsearch;

import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QconfigOfInitConfig;
import com.ctrip.corp.bff.framework.hotel.common.util.CityInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.TimeUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfSearchApproval;
import com.ctrip.corp.bff.framework.hotel.entity.contract.*;
import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum;
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.ExceptionUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple6;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple7;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.approvalinfo.ControlInfoTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.ApprovalDetailSearchUtil;
import com.ctrip.corp.bff.hotel.book.common.util.CityInfoValidateUtil;
import com.ctrip.corp.bff.hotel.book.contract.ApprovalBaseInfoInput;
import com.ctrip.corp.bff.hotel.book.contract.ApprovalDetailSearchRequestType;
import com.ctrip.corp.bff.hotel.book.contract.ApprovalDetailSearchResponseType;
import com.ctrip.corp.bff.hotel.book.contract.ApprovalSwitchInfo;
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryResponseType;
import com.ctrip.corp.bff.specific.contract.*;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.CityInfo;
import com.ctrip.corp.foundation.common.exception.TimeZoneNotFoundException;
import com.ctrip.corp.foundation.common.plus.time.HotelTimeZoneUtil;
import com.ctrip.corp.foundation.common.util.DateUtils;
import com.ctrip.corp.foundation.common.util.NumberUtil;
import com.ctrip.corp.foundation.common.util.StringUtilsExt;
import com.ctrip.corp.hotel.book.query.entity.CityBaseInfoEntity;
import com.ctrip.corp.hotel.book.query.entity.TimeZoneType;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.sql.Time;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/23 18:31
 * @desc 审批单详情，主要是实现一些产线定制化的逻辑
 */
@Component
public class MapperOfApprovalDetailSearchResponseType extends AbstractMapper<Tuple7<ApprovalDetailSearchRequestType,
    BatchApprovalDefaultResponseType, WrapperOfAccount.AccountInfo, WrapperOfSearchApproval.ApprovalInfo,
    ApprovalTextInfoResponseType, SSOInfoQueryResponseType, Map<String, String>>, ApprovalDetailSearchResponseType> {

    /**
     * 单点登录场景下，是否可以更改审批单，目前只有国网定制
     */
    private final static String CAN_MODIFY_APPROVAL_BY_SSO = "canModifyApprovalBySSO";
    /**
     * 默认时间特殊处理
     */
    private final static String BOOKING_DATE_SPECIAL = "booingDateSpecial";

    private final static String BOOKING_DATE_FOR_APPROVAL = "booingDateForApproval";
    /**
     * 审批单管控结束时间需要+1，目前是国网定制
     */
    private final static String APPROVAL_CONTROL_END_TIME_AFTER = "approvalControlEndTimeAfter";
    /**
     * 前选人页面
     */
    private final static String PASSAGE = "PASSAGE";
    /**
     * 不需要单点登录带入的审批单
     */
    private final static String NO_NEED_SSO_ORDER_APPROVAL = "noNeedSSoOrderApprovalNo";
    /**
     * 紧急预定标题
     */
    private final static String EMERGENCY_TITLE = "EMERGENCY_TITLE";

    private final static String CHECK_IN_DATE = "checkInDate";

    private final static String CHECK_OUT_DATE = "checkOutDate";

    private final static String APPROVAL_DETAIL_SEARCH_ERROR = "ctrip.com.hotel.approvalDetail.search.error.{0}";

    private final static String CITY_ID = "cityId";

    @Override
    protected ApprovalDetailSearchResponseType convert(Tuple7<ApprovalDetailSearchRequestType,
        BatchApprovalDefaultResponseType, WrapperOfAccount.AccountInfo, WrapperOfSearchApproval.ApprovalInfo,
        ApprovalTextInfoResponseType, SSOInfoQueryResponseType, Map<String, String>> param) {
        ApprovalDetailSearchRequestType ApprovalDetailSearchRequestType = param.getT1();
        BatchApprovalDefaultResponseType approvalListResponseType = param.getT2();
        WrapperOfAccount.AccountInfo accountInfo = param.getT3();
        WrapperOfSearchApproval.ApprovalInfo approvalInfo = param.getT4();
        ApprovalTextInfoResponseType approvalTextInfoResponseType = param.getT5();
        SSOInfoQueryResponseType ssoInfoQueryResponseType = param.getT6();
        Map<String, String> cityIdToOffsetMap = param.getT7();

        String corpId = ApprovalDetailSearchRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId();
        boolean isPrivate = CorpPayInfoUtil.isPrivate(ApprovalDetailSearchRequestType.getCorpPayInfo());
        ApprovalDetailSearchResponseType approvalDetailSearchResponseType = new ApprovalDetailSearchResponseType();
        // 酒店审批后置+因私直接返回空
        if (BooleanUtils.isTrue(accountInfo.isOaApprovalHead()) && isPrivate) {
            return approvalDetailSearchResponseType;
        }
        // 紧急预定没有管控信息，直接返回
        if (CommonConstant.OPEN.equalsIgnoreCase(
                Optional.ofNullable(ApprovalDetailSearchRequestType.getApprovalInput()).map(ApprovalInput::getEmergency).orElse(null))) {
            approvalDetailSearchResponseType.setApprovalOutput(buildApprovalOutPutByEmergency(approvalTextInfoResponseType, accountInfo));
            return approvalDetailSearchResponseType;
        }
        ApprovalInfo defaultApprovalInfo = ApprovalDetailSearchUtil.buildDefaultApprovalInfo(approvalListResponseType);
        if (isIgnoreApprovalNo(ApprovalDetailSearchRequestType, accountInfo, ssoInfoQueryResponseType, defaultApprovalInfo)) {
            return approvalDetailSearchResponseType;
        }
        approvalDetailSearchResponseType.setApprovalOutput(buildApprovalOutPut(defaultApprovalInfo, corpId,
                approvalInfo, approvalTextInfoResponseType ,accountInfo, cityIdToOffsetMap));
        ApprovalCityInfo approvalCityInfo = buildDefaultApprovalCityInfo(approvalInfo, ssoInfoQueryResponseType, cityIdToOffsetMap);
        approvalDetailSearchResponseType.setDefaultCityInfo(approvalCityInfo);
        approvalDetailSearchResponseType.setDateRange(buildDefaultApprovalDateRange(defaultApprovalInfo, corpId, ssoInfoQueryResponseType,
                Optional.ofNullable(approvalCityInfo).map(ApprovalCityInfo::getCityId).map(Integer::parseInt).orElse(null)));
        approvalDetailSearchResponseType.setSwitchInfo(buildApprovalSwitchInfo(defaultApprovalInfo, ApprovalDetailSearchRequestType, ssoInfoQueryResponseType));
        return approvalDetailSearchResponseType;
    }

    @Override
    protected ParamCheckResult check(Tuple7<ApprovalDetailSearchRequestType,
        BatchApprovalDefaultResponseType, WrapperOfAccount.AccountInfo, WrapperOfSearchApproval.ApprovalInfo,
        ApprovalTextInfoResponseType, SSOInfoQueryResponseType, Map<String, String>> param) {
        BatchApprovalDefaultResponseType batchApprovalDefaultResponseType = param.getT2();
        if (batchApprovalDefaultResponseType != null && batchApprovalDefaultResponseType.getIntegrationResponse() != null
                && StringUtil.isNotEmpty(batchApprovalDefaultResponseType.getIntegrationResponse().getErrorCode())
                && !Objects.equals(batchApprovalDefaultResponseType.getIntegrationResponse().getErrorCode(), "0")) {
            return new ParamCheckResult(false, Integer.parseInt(batchApprovalDefaultResponseType.getIntegrationResponse().getErrorCode()),
                    batchApprovalDefaultResponseType.getIntegrationResponse().getErrorMessage(),
                    BFFSharkUtil.getSharkValue(StringUtilsExt.format(APPROVAL_DETAIL_SEARCH_ERROR, batchApprovalDefaultResponseType.getIntegrationResponse().getErrorCode())));
        }
        return null;
    }

    /**
     * 因公+单点登录+非提前审批+合住模式 忽略单点登录带入的订单维度审批单号
     */
    private Boolean isIgnoreApprovalNo(ApprovalDetailSearchRequestType ApprovalDetailSearchRequestType,
                                       WrapperOfAccount.AccountInfo accountInfo,
                                       SSOInfoQueryResponseType ssoInfoQueryResponseType,
                                       ApprovalInfo approvalInfo) {
        if (CorpPayInfoUtil.isPrivate(ApprovalDetailSearchRequestType.getCorpPayInfo())) {
            return false;
        }
        Boolean overSea = CommonConstant.OFF.equalsIgnoreCase(Optional.ofNullable(approvalInfo).map(ApprovalInfo::getDomesticFlag).orElse(CommonConstant.OPEN));
        if (BooleanUtils.isNotTrue(isSSO(ApprovalDetailSearchRequestType, ssoInfoQueryResponseType))) {
            return false;
        }
        // 单点登录场景下如果没开启单据模式，忽略
        if (BooleanUtils.isNotTrue(accountInfo.isPreApprovalRequired(overSea, ApprovalDetailSearchRequestType.getCorpPayInfo()))
                && BooleanUtils.isNotTrue(accountInfo.isTravelApplyRequired(overSea, ApprovalDetailSearchRequestType.getCorpPayInfo()))) {
            return true;
        }
        // 单点登录场景下开启了按订单选审批单，不忽略
        if (BooleanUtils.isTrue(accountInfo.isPreApprovalRequired(overSea, ApprovalDetailSearchRequestType.getCorpPayInfo()))) {
            return false;
        }
        if (!PASSAGE.equals(Optional.ofNullable(ApprovalDetailSearchRequestType.getApprovalBaseInfoInput())
                .map(ApprovalBaseInfoInput::getPageType).orElse(null))) {
            return false;
        }
        if (QConfigOfCustomConfig.isSupport(NO_NEED_SSO_ORDER_APPROVAL, ApprovalDetailSearchRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId())) {
            return true;
        }
        return accountInfo.bookTravelersMustSameTripApprove(overSea);
    }

    /**
     * 审批单出参
     * @param approvalInfo
     * @param corpId
     * @return
     */
    private ApprovalOutput buildApprovalOutPut(ApprovalInfo approvalInfo, String corpId,
        WrapperOfSearchApproval.ApprovalInfo searchApprovalInfo,
        ApprovalTextInfoResponseType approvalTextInfoResponseType,
        WrapperOfAccount.AccountInfo accountInfo, Map<String, String> cityIdToOffsetMap) {
        ApprovalOutput approvalOutput = new ApprovalOutput();
        if (approvalInfo != null && approvalInfo.getApprovalBaseInfo() != null) {
            approvalOutput.setApprovalMainNo(approvalInfo.getApprovalBaseInfo().getMasterApprovalNo());
            approvalOutput.setApprovalSubNo(approvalInfo.getApprovalBaseInfo().getSubApprovalNo());
            approvalOutput.setEmergencyBook(CommonConstant.OFF);
            approvalOutput.setApprovalControl(buildApprovalControl(corpId, searchApprovalInfo, cityIdToOffsetMap));
        }
        if (approvalTextInfoResponseType != null) {
            approvalOutput.setApprovalTitle(Optional.ofNullable(approvalTextInfoResponseType.getTextInfoItems()).orElse(new ArrayList<>())
                    .stream().filter(item -> EMERGENCY_TITLE.equals(item.getItemType())).map(TextInfoItem::getItemValue).findFirst().orElse(null));
            approvalOutput.setEmergencyName(approvalTextInfoResponseType.getEmergencyName());
            approvalOutput.setOnlyQueryPrice(buildCanOnlyQueryPrice(accountInfo));
            approvalOutput.setCanEmergency(canEmergencyBook(accountInfo));
        }
        return approvalOutput;
    }

    /**
     * 审批单出参
     * @param approvalTextInfoResponseType
     * @param accountInfo
     * @return
     */
    private ApprovalOutput buildApprovalOutPutByEmergency(ApprovalTextInfoResponseType approvalTextInfoResponseType, WrapperOfAccount.AccountInfo accountInfo) {
        ApprovalOutput approvalOutput = new ApprovalOutput();
        approvalOutput.setEmergencyBook(CommonConstant.OPEN);
        if (approvalTextInfoResponseType != null) {
            approvalOutput.setApprovalTitle(Optional.ofNullable(approvalTextInfoResponseType.getTextInfoItems()).orElse(new ArrayList<>())
                    .stream().filter(item -> EMERGENCY_TITLE.equals(item.getItemType())).map(TextInfoItem::getItemValue).findFirst().orElse(null));
            approvalOutput.setEmergencyName(approvalTextInfoResponseType.getEmergencyName());
            approvalOutput.setOnlyQueryPrice(buildCanOnlyQueryPrice(accountInfo));
            approvalOutput.setCanEmergency(canEmergencyBook(accountInfo));
        }
        return approvalOutput;
    }

    /**
     * 默认的审批单管控信息
     * @param searchApprovalInfo
     * @oaram corpId
     * @return
     */
    private ApprovalControl buildApprovalControl(String corpId,
        WrapperOfSearchApproval.ApprovalInfo searchApprovalInfo, Map<String, String> cityIdToOffsetMap) {
        ApprovalControl approvalControl = new ApprovalControl();
        approvalControl.setCheckIn(buildCheckInApprovalDateRange(searchApprovalInfo));
        approvalControl.setCheckOut(buildCheckOutApprovalDateRange(searchApprovalInfo, corpId));
        if (searchApprovalInfo != null && CollectionUtil.isNotEmpty(searchApprovalInfo.getCheckInCityInfos())
            && BooleanUtil.isTrue(searchApprovalInfo.isControlCheckInCityCode())) {
            List<ApprovalCityInfo> approvalCityInfos = searchApprovalInfo.getCheckInCityInfos()
                .stream().map(cityInfo -> buildApprovalCityInfo(cityInfo, cityIdToOffsetMap))
                .collect(Collectors.toList());
            approvalControl.setCityInfo(approvalCityInfos);
        }
        if (searchApprovalInfo != null && CollectionUtil.isNotEmpty(searchApprovalInfo.getHotelExtInfoList())) {
            List<StrongControlInfo> strongControlInfoList = searchApprovalInfo.getHotelExtInfoList().stream().map(extInfo -> {
                StrongControlInfo strongControlInfo = new StrongControlInfo();
                strongControlInfo.setHotelId(extInfo.getHotelIDs());
                strongControlInfo.setCityId(extInfo.getCityID());
                return strongControlInfo;
            }).collect(Collectors.toList());
            approvalControl.setStrongControlInfo(strongControlInfoList);
        }
        return approvalControl;
    }

    /**
     * 审批单管控入住时间范围
     * @param approvalInfo
     * @return
     */
    private ApprovalDateRange buildCheckInApprovalDateRange(WrapperOfSearchApproval.ApprovalInfo approvalInfo) {
        if (approvalInfo == null) {
            return null;
        }
        ApprovalDateRange approvalDateRange = new ApprovalDateRange();
        if (StringUtil.isNotEmpty(approvalInfo.checkInBeginDateAfterFloatDay())) {
            approvalDateRange.setBeginDate(approvalInfo.checkInBeginDateAfterFloatDay());
        }
        if (StringUtil.isNotEmpty(approvalInfo.checkInEndDateAfterFloatDay())) {
            approvalDateRange.setEndDate(approvalInfo.checkInEndDateAfterFloatDay());
        }
        return approvalDateRange;
    }

    /**
     * 审批单管控离店时间范围
     * @param approvalInfo
     * @param corpId
     * @return
     */
    private ApprovalDateRange buildCheckOutApprovalDateRange(WrapperOfSearchApproval.ApprovalInfo approvalInfo, String corpId) {
        if (approvalInfo == null) {
            return null;
        }
        ApprovalDateRange approvalDateRange = new ApprovalDateRange();
        if (StringUtil.isNotEmpty(approvalInfo.checkOutBeginDateAfterFloatDay())) {
            approvalDateRange.setBeginDate(approvalInfo.checkOutBeginDateAfterFloatDay());
        }
        if (StringUtil.isNotEmpty(approvalInfo.checkOutEndDateAfterFloatDay())) {
            if (QConfigOfCustomConfig.isSupport(APPROVAL_CONTROL_END_TIME_AFTER, corpId)) {
                approvalDateRange.setEndDate(DateUtils.parseLocalDate(DateUtils.FORMATTER_DATE, approvalInfo.checkOutEndDateAfterFloatDay()).plusDays(1)
                        .format(DateTimeFormatter.ofPattern(DateUtils.FORMATTER_DATE)));
            } else {
                approvalDateRange.setEndDate(approvalInfo.checkOutEndDateAfterFloatDay());
            }
        }
        return approvalDateRange;
    }

    /**
     * 审批单默认的入离时间
     * @param approvalInfo
     * @param corpId
     * @return
     */
    private ApprovalDateRange buildDefaultApprovalDateRange(ApprovalInfo approvalInfo, String corpId, SSOInfoQueryResponseType ssoInfoQueryResponseType,
                                                            Integer defaultCityId) {
        if (approvalInfo == null) {
            return null;
        }
        String overFlag = CommonConstant.OPEN.equalsIgnoreCase(approvalInfo.getDomesticFlag()) ? CommonConstant.OFF : CommonConstant.OPEN;
        ApprovalDateRange approvalDateRange = null;
        // 特殊定制审批单默认时间
        if (QConfigOfCustomConfig.isSupport(BOOKING_DATE_SPECIAL, corpId)) {
            approvalDateRange = getBooingDateSpecial(approvalInfo, defaultCityId);
        } else if (QConfigOfCustomConfig.isSupport(BOOKING_DATE_FOR_APPROVAL, corpId)) {
            // 直接使用审批单的时间
            approvalDateRange = getBooingDateForApproval(approvalInfo);
        } else {
            approvalDateRange = getBooingDateCommon(approvalInfo, defaultCityId);
        }
        // 顺序一定不能动，先判断审批单交集，再判断时间有效性，再判断月租房
        approvalDateRange = buildApprovalDateRangeBySSO(approvalDateRange, ssoInfoQueryResponseType);
        approvalDateRange = buildDateRangeLimit(approvalDateRange, defaultCityId);
        approvalDateRange = buildLongRent(approvalDateRange, corpId, overFlag);
        return approvalDateRange;
    }

    private ApprovalDateRange getBooingDateSpecial(ApprovalInfo approvalInfo, Integer defaultCityId) {
        String checkInStartDate = Optional.ofNullable(ApprovalDetailSearchUtil.buildControlInfo(approvalInfo.getControlInfos(),
                        ControlInfoTypeEnum.CHECKIN_DATE_START.getCode())).map(ControlInfo::getContent).orElse(null);
        String checkOutStartDate = Optional.ofNullable(ApprovalDetailSearchUtil.buildControlInfo(approvalInfo.getControlInfos(),
                        ControlInfoTypeEnum.CHECKOUT_DATE_START.getCode())).map(ControlInfo::getContent).orElse(null);
        String checkOutEndDate = Optional.ofNullable(ApprovalDetailSearchUtil.buildControlInfo(approvalInfo.getControlInfos(),
                        ControlInfoTypeEnum.CHECKOUT_DATE_END.getCode())).map(ControlInfo::getContent).orElse(null);
        ApprovalDateRange approvalDateRange = new ApprovalDateRange();
        LocalDate startDate = getStartDate(checkInStartDate, defaultCityId);
        approvalDateRange.setBeginDate(startDate.format(DateTimeFormatter.ofPattern(DateUtils.FORMATTER_DATE)));
        // 开始时间+1d
        LocalDate startAfterDate = startDate.plusDays(1);
        LocalDate endDate = startAfterDate;
        if (StringUtils.isBlank(checkOutStartDate) && StringUtils.isNotBlank(checkOutEndDate)){
            LocalDate checkOutEnd = DateUtils.parseLocalDate(DateUtils.FORMATTER_DATE, checkOutEndDate);
            endDate = startAfterDate.isBefore(checkOutEnd) ? startAfterDate : checkOutEnd;
        } else if (StringUtils.isBlank(checkOutEndDate) && StringUtils.isNotBlank(checkOutStartDate)){
            LocalDate checkOutStart = DateUtils.parseLocalDate(DateUtils.FORMATTER_DATE, checkOutStartDate);
            endDate = startAfterDate.isAfter(checkOutStart) ? startAfterDate : checkOutStart;
        } else if (StringUtils.isNotBlank(checkOutStartDate) && StringUtils.isNotBlank(checkOutEndDate)){
            LocalDate checkOutStart = DateUtils.parseLocalDate(DateUtils.FORMATTER_DATE, checkOutStartDate);
            LocalDate checkOutEnd = DateUtils.parseLocalDate(DateUtils.FORMATTER_DATE, checkOutEndDate);
            if (startAfterDate.isBefore(checkOutStart) || startAfterDate.isAfter(checkOutEnd)){
                endDate = checkOutStart;
            }
        }
        approvalDateRange.setEndDate(endDate.format(DateTimeFormatter.ofPattern(DateUtils.FORMATTER_DATE)));
        return approvalDateRange;
    }

    /**
     * 如果日期不在管控范围内，直接返回当前时间，不给前端默认时间
     * @param checkInStartDate
     * @return
     */
    private LocalDate getStartDate(String checkInStartDate, Integer defaultCityId) {
        if (StringUtils.isBlank(checkInStartDate)) {
            LocalDateTime nowLocalTime = TimeUtil.getNowLocalTime(defaultCityId);
            if (nowLocalTime == null) {
                return LocalDate.now();
            }
            if (nowLocalTime.getHour() < 6) {
                return nowLocalTime.toLocalDate().minusDays(1);
            }
            return nowLocalTime.toLocalDate();
        }
        return DateUtils.parseLocalDate(DateUtils.FORMATTER_DATE, checkInStartDate);
    }

    private ApprovalDateRange getBooingDateForApproval(ApprovalInfo approvalInfo) {
        ApprovalDateRange approvalDateRange = new ApprovalDateRange();
        approvalDateRange.setBeginDate(Optional.ofNullable(
                ApprovalDetailSearchUtil.buildControlInfo(approvalInfo.getControlInfos(), ControlInfoTypeEnum.CHECKIN_DATE_START.getCode()))
                .map(ControlInfo::getContent).orElse(null));
        approvalDateRange.setEndDate(Optional.ofNullable(
                ApprovalDetailSearchUtil.buildControlInfo(approvalInfo.getControlInfos(), ControlInfoTypeEnum.CHECKOUT_DATE_END.getCode()))
                .map(ControlInfo::getContent).orElse(null));
        return approvalDateRange;
    }

    private ApprovalDateRange getBooingDateCommon(ApprovalInfo approvalInfo, Integer cityId) {
        String checkInStartDate = Optional.ofNullable(
                ApprovalDetailSearchUtil.buildControlInfo(approvalInfo.getControlInfos(), ControlInfoTypeEnum.CHECKIN_DATE_START.getCode()))
                .map(ControlInfo::getContent).orElse(null);
        String checkInEndDate = Optional.ofNullable(
                ApprovalDetailSearchUtil.buildControlInfo(approvalInfo.getControlInfos(), ControlInfoTypeEnum.CHECKIN_DATE_END.getCode()))
                .map(ControlInfo::getContent).orElse(null);
        String checkOutStartDate = Optional.ofNullable(
                ApprovalDetailSearchUtil.buildControlInfo(approvalInfo.getControlInfos(), ControlInfoTypeEnum.CHECKOUT_DATE_START.getCode()))
                .map(ControlInfo::getContent).orElse(null);
        String checkOutEndDate = Optional.ofNullable(
                ApprovalDetailSearchUtil.buildControlInfo(approvalInfo.getControlInfos(), ControlInfoTypeEnum.CHECKOUT_DATE_END.getCode()))
                .map(ControlInfo::getContent).orElse(null);
        ApprovalDateRange approvalDateRange = new ApprovalDateRange();
        LocalDate startDate = getStartDate(checkInStartDate, cityId);
        if (startDate != null) {
            approvalDateRange.setBeginDate(startDate.format(DateTimeFormatter.ofPattern(DateUtils.FORMATTER_DATE)));
        }
        LocalDate endDate = getEndDate(startDate, checkInEndDate, checkOutStartDate, checkOutEndDate);
        approvalDateRange.setEndDate(endDate.format(DateTimeFormatter.ofPattern(DateUtils.FORMATTER_DATE)));
        return approvalDateRange;
    }

    private LocalDate getEndDate(LocalDate startDate, String checkInEndDate, String checkOutStartDate, String checkOutEndDate) {
        if (StringUtils.isNotBlank(checkOutEndDate)) {
            return parseToEndTime(startDate, checkOutEndDate);
        }
        if (StringUtils.isNotBlank(checkOutStartDate)) {
            return parseToEndTime(startDate, checkOutStartDate);
        }
        if (StringUtils.isNotBlank(checkInEndDate)) {
            return parseToEndTime(startDate, checkInEndDate);
        }
        return startDate.plusDays(1);
    }

    /**
     * 如果审批单结束时间小于开始时间，则默认开始时间+1
     * 如果审批单结束时间小于当前时间，则默认当前时间+1
     * @param startDate
     * @param checkEndTime
     * @return
     */
    private LocalDate parseToEndTime(LocalDate startDate, String checkEndTime) {
        LocalDate dateTime = DateUtils.parseLocalDate(DateUtils.FORMATTER_DATE, checkEndTime);
        if (dateTime.isBefore(startDate) || dateTime.isEqual(startDate)) {
            return startDate.plusDays(1);
        }
        return dateTime;
    }

    /**
     * 处理审批单时间和单点登录时间的交集
     * @param approvalDateRange
     * @param ssoInfoQueryResponseType
     * @return
     */
    private ApprovalDateRange buildApprovalDateRangeBySSO(ApprovalDateRange approvalDateRange, SSOInfoQueryResponseType ssoInfoQueryResponseType) {
        if (approvalDateRange == null) {
            return null;
        }
        LocalDate ssoCheckInDate = null;
        LocalDate ssoCheckOutDate = null;
        try {
            String checkInDate = ApprovalDetailSearchUtil.getExtendInfoValue(ssoInfoQueryResponseType, CHECK_IN_DATE);
            String checkOutDate = ApprovalDetailSearchUtil.getExtendInfoValue(ssoInfoQueryResponseType, CHECK_OUT_DATE);
            ssoCheckInDate = StringUtil.isNotEmpty(checkInDate) ? com.ctrip.corp.bff.hotel.book.common.util.DateUtils.toLocalDate(checkInDate) : null;
            ssoCheckOutDate = StringUtil.isNotEmpty(checkOutDate) ? com.ctrip.corp.bff.hotel.book.common.util.DateUtils.toLocalDate(checkOutDate) : null;
        } catch (Exception e) {
            LogUtil.loggingClogOnly(LogLevelEnum.Error, MapperOfApprovalDetailSearchResponseType.class, "date format error",
                    ExceptionUtil.getFullException(e), null);
            return approvalDateRange;
        }
        LocalDate approvalStartDate = StringUtil.isNotEmpty(approvalDateRange.getBeginDate()) ?
                DateUtils.parseLocalDate(DateUtils.FORMATTER_DATE, approvalDateRange.getBeginDate()) : null;
        LocalDate approvalEndDate = StringUtil.isNotEmpty(approvalDateRange.getEndDate()) ?
                DateUtils.parseLocalDate(DateUtils.FORMATTER_DATE, approvalDateRange.getEndDate()) : null;
        Boolean hasDateIntersection = hasDateIntersection(ssoCheckInDate, ssoCheckOutDate, approvalStartDate, approvalEndDate);
        // 如果没有交集，直接返回审批单的信息
        if (BooleanUtils.isNotTrue(hasDateIntersection)) {
            return approvalDateRange;
        }
        if (ssoCheckInDate != null) {
            if (approvalStartDate == null) {
                approvalDateRange.setBeginDate(ssoCheckInDate.format(DateTimeFormatter.ofPattern(DateUtils.FORMATTER_DATE)));
            } else {
                approvalDateRange.setBeginDate(approvalStartDate.isBefore(ssoCheckInDate) ?
                        ssoCheckInDate.format(DateTimeFormatter.ofPattern(DateUtils.FORMATTER_DATE)) : approvalDateRange.getBeginDate());
            }
        }
        if (ssoCheckOutDate != null) {
            if (approvalEndDate == null) {
                approvalDateRange.setEndDate(ssoCheckOutDate.format(DateTimeFormatter.ofPattern(DateUtils.FORMATTER_DATE)));
            } else {
                approvalDateRange.setEndDate(approvalEndDate.isBefore(ssoCheckOutDate) ?
                        approvalDateRange.getEndDate() : ssoCheckOutDate.format(DateTimeFormatter.ofPattern(DateUtils.FORMATTER_DATE)));
            }
        }
        return approvalDateRange;
    }

    /**
     * 判断2个时间范围是否有交集，兼容单边场景
     * @param startDate1
     * @param endDate1
     * @param startDate2
     * @param endDate2
     * @return
     */
    private Boolean hasDateIntersection(LocalDate startDate1, LocalDate endDate1, LocalDate startDate2, LocalDate endDate2) {
        // 如果第一个时间范围没有结束时间，假设它是无限的
        if (endDate1 == null) {
            endDate1 = LocalDate.MAX;
        }
        // 如果第二个时间范围没有结束时间，假设它是无限的
        if (endDate2 == null) {
            endDate2 = LocalDate.MAX;
        }
        // 如果第一个时间范围没有开始时间，假设它是从最早开始
        if (startDate1 == null) {
            startDate1 = LocalDate.MIN;
        }
        // 如果第二个时间范围没有开始时间，假设它是从最早开始
        if (startDate2 == null) {
            startDate2 = LocalDate.MIN;
        }
        // 判断两个时间范围是否有交集
        return !startDate1.isAfter(endDate2) && !startDate2.isAfter(endDate1);
    }

    /**
     * 判断月租房,如果默认时间大于月租房的最大时间，则取月租房的最大时间
     * @param approvalDateRange
     * @param corpId
     * @param oversea
     * @return
     */
    private ApprovalDateRange buildLongRent(ApprovalDateRange approvalDateRange, String corpId, String oversea) {
        String beginDate = approvalDateRange.getBeginDate();
        String endDate = approvalDateRange.getEndDate();
        if (StringUtil.isNotEmpty(beginDate) && StringUtil.isNotEmpty(endDate)) {
            String longRentDay = QconfigOfInitConfig.buildLongRentDay(corpId, oversea);
            LocalDate startDate = DateUtils.parseLocalDate(DateUtils.FORMATTER_DATE, beginDate);
            LocalDate endLocalDate = DateUtils.parseLocalDate(DateUtils.FORMATTER_DATE, endDate);
            LocalDate maxDate = startDate.plusDays(Optional.ofNullable(longRentDay).map(Integer::parseInt).orElse(30));
            if (endLocalDate.isAfter(maxDate)) {
                approvalDateRange.setEndDate(maxDate.format(DateTimeFormatter.ofPattern(DateUtils.FORMATTER_DATE)));
            }
        }
        return approvalDateRange;
    }

    /**
     * 判断时间是否合法
     * 如果开始时间小于当前时间，则使用当前时间
     * 如果结束时间小于当前时间+1，则使用当前时间+1
     * @param approvalDateRange
     * @return
     */
    private ApprovalDateRange buildDateRangeLimit(ApprovalDateRange approvalDateRange, Integer defaultCityId) {
        String beginDate = approvalDateRange.getBeginDate();
        String endDate = approvalDateRange.getEndDate();
        LocalDate localDate = getStartDate(null, defaultCityId);
        if (StringUtil.isNotEmpty(beginDate)) {
            LocalDate startDate = DateUtils.parseLocalDate(DateUtils.FORMATTER_DATE, beginDate);
            if (startDate.isBefore(localDate)) {
                approvalDateRange.setBeginDate(localDate.format(DateTimeFormatter.ofPattern(DateUtils.FORMATTER_DATE)));
            }
        }
        if (StringUtil.isNotEmpty(endDate)) {
            LocalDate endLocalDate = DateUtils.parseLocalDate(DateUtils.FORMATTER_DATE, endDate);
            if (endLocalDate.isBefore(localDate.plusDays(1))) {
                approvalDateRange.setEndDate(localDate.plusDays(1).format(DateTimeFormatter.ofPattern(DateUtils.FORMATTER_DATE)));
            }
        }
        return approvalDateRange;
    }

    /**
     * 开关信息
     * @param approvalInfo
     * @param ApprovalDetailSearchRequestType
     * @param ssoInfoQueryResponseType
     * @return
     */
    private ApprovalSwitchInfo buildApprovalSwitchInfo(ApprovalInfo approvalInfo, ApprovalDetailSearchRequestType ApprovalDetailSearchRequestType,
                                                       SSOInfoQueryResponseType ssoInfoQueryResponseType) {
        ApprovalSwitchInfo approvalSwitchInfo = new ApprovalSwitchInfo();
        approvalSwitchInfo.setCanModify(BooleanUtil.parseStr(!(isSSO(ApprovalDetailSearchRequestType, ssoInfoQueryResponseType)
                && canModifyApproval(ApprovalDetailSearchRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId()))));
        if (approvalInfo != null) {
            approvalSwitchInfo.setSupportType(CommonConstant.OPEN.equalsIgnoreCase(approvalInfo.getDomesticFlag()) ? CommonConstant.DOMESTIC : CommonConstant.OVERSEA);
        } else if (ApprovalDetailSearchRequestType.getApprovalBaseInfoInput() != null
                && StringUtil.isNotEmpty(ApprovalDetailSearchRequestType.getApprovalBaseInfoInput().getCityType())) {
            approvalSwitchInfo.setSupportType(ApprovalDetailSearchRequestType.getApprovalBaseInfoInput().getCityType());
        }
        return approvalSwitchInfo;
    }

    /**
     * 城市信息
     * @param cityInfo
     * @param
     * @return
     */
    private ApprovalCityInfo buildApprovalCityInfo(CityInfo cityInfo, Map<String, String> cityIdToOffsetMap) {
        if (cityInfo == null || NumberUtil.parseInt(cityInfo.getCityId()) == null
            || NumberUtil.parseInt(cityInfo.getCityId()) <= 0) {
            return null;
        }
        ApprovalCityInfo approvalCityInfo = new ApprovalCityInfo();
        approvalCityInfo.setCityId(Optional.ofNullable(cityInfo.getCityId()).orElse(StringUtil.EMPTY));
        approvalCityInfo.setCityName(cityInfo.getCityName());
        approvalCityInfo.setCityType(CityInfoUtil.oversea(NumberUtil.parseInt(cityInfo.getCityId()))
            ? CommonConstant.OVERSEA : CommonConstant.DOMESTIC);
        String offsetMinute = cityIdToOffsetMap == null ? null : cityIdToOffsetMap.get(cityInfo.getCityId());
        approvalCityInfo.setOffset(StringUtil.isNotBlank(offsetMinute) ? offsetMinute : null);
        return approvalCityInfo;
    }

    /**
     * 默认城市信息，如果单点登录带入了城市，则默认使用单点登录带入的城市，如果没有，则使用审批单管控的第一个城市
     * @param approvalInfo
     * @param ssoInfoQueryResponseType
     * @return
     */
    private ApprovalCityInfo buildDefaultApprovalCityInfo(
        WrapperOfSearchApproval.ApprovalInfo approvalInfo,
        SSOInfoQueryResponseType ssoInfoQueryResponseType,
        Map<String, String> cityIdToOffsetMap) {
        if (approvalInfo == null) {
            return null;
        }
        if (CollectionUtil.isEmpty(approvalInfo.getCheckInCityInfos())
            && CollectionUtil.isEmpty(approvalInfo.getCheckInCityInfoMap())) {
            return null;
        }
        CityInfo cityInfo = null;
        String ssoCityId = ApprovalDetailSearchUtil.getExtendInfoValue(ssoInfoQueryResponseType, CITY_ID);
        if (StringUtils.isNotEmpty(ssoCityId) && NumberUtils.isNumber(ssoCityId)) {
            cityInfo = approvalInfo.getCheckInCityInfoMap().get(ssoCityId);
        }
        if (cityInfo == null) {
            cityInfo = Optional.ofNullable(approvalInfo.getCheckInCityInfos())
                .orElse(new ArrayList<>()).stream()
                .filter(currentCityInfo -> CityInfoValidateUtil.validCity(
                    currentCityInfo.getCityId(), currentCityInfo.getCityName()))
                .findFirst().orElse(null);
        }
        if (cityInfo == null) {
            return null;
        }
        ApprovalCityInfo approvalCityInfo = new ApprovalCityInfo();
        approvalCityInfo.setCityId(Optional.ofNullable(cityInfo.getCityId()).map(String::valueOf).orElse(""));
        approvalCityInfo.setCityName(cityInfo.getCityName());
        approvalCityInfo.setCityType(CityInfoUtil.oversea(NumberUtil.parseInt(cityInfo.getCityId()))
            ? CommonConstant.OVERSEA : CommonConstant.DOMESTIC);
        String offsetMinute = cityIdToOffsetMap == null ? null : cityIdToOffsetMap.get(cityInfo.getCityId());
        approvalCityInfo.setOffset(StringUtil.isNotBlank(offsetMinute) ? offsetMinute : null);
        return approvalCityInfo;
    }

    /**
     * 是否是单点登录
     * @param request
     * @return
     */
    private Boolean isSSO(ApprovalDetailSearchRequestType request, SSOInfoQueryResponseType ssoInfoQueryResponseType) {
        return request.getSsoInput() != null && StringUtils.isNotEmpty(request.getSsoInput().getSsoKey()) && ssoInfoQueryResponseType != null;
    }

    /**
     * 是否是国网
     * @return
     */
    private Boolean canModifyApproval(String corpId) {
        return QConfigOfCustomConfig.isSupport(CAN_MODIFY_APPROVAL_BY_SSO, corpId);
    }

    /**
     * 是否支持仅查价
     *
     * @param accountInfo
     * @return
     */
    private String buildCanOnlyQueryPrice(WrapperOfAccount.AccountInfo accountInfo) {
        if (!accountInfo.isOaApprovalHead()) {
            return CommonConstant.OFF;
        }
        return BooleanUtil.parseStr(accountInfo.canOnlyQueryPrice());
    }

    /**
     * 是否支持紧急预定
     *
     * @param accountInfo
     * @return
     */
    private String canEmergencyBook(WrapperOfAccount.AccountInfo accountInfo) {
        if (accountInfo == null) {
            return CommonConstant.OFF;
        }
        return BooleanUtil.parseStr(accountInfo.canEmergencyBook());
    }

}
