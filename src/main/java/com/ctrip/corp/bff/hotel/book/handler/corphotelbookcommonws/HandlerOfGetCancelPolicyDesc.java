package com.ctrip.corp.bff.hotel.book.handler.corphotelbookcommonws;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.hotelbook.commonws.CorpHotelBookCommonWSClient;
import com.ctrip.model.GetCancelPolicyDescRequestType;
import com.ctrip.model.GetCancelPolicyDescResponseType;
import org.springframework.stereotype.Component;

/**
 * @Author: chenchuang
 * @Date: 2024/9/20 16:36
 * @Description: 获取 取消政策
 */
@Component
public class HandlerOfGetCancelPolicyDesc extends AbstractHandlerOfSOA<GetCancelPolicyDescRequestType,
        GetCancelPolicyDescResponseType, CorpHotelBookCommonWSClient> {
    @Override
    protected String getMethodName() {
        return "getCancelPolicyDesc";
    }
}
