package com.ctrip.corp.bff.hotel.book.common.enums;

import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken;
import com.ctrip.corp.bff.framework.template.common.defalut.LogDefaultUtil;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import org.apache.commons.lang.StringUtils;

import java.util.Optional;

/**
 * @Author: chenchuang
 * @Date: 2024/09/02 21:33
 * @Description: 填写页初始化返回
 */
public enum HotelBalanceTypeEnum {

    /**
     * 现付
     */
    FG("FG"),

    /**
     * 预付
     */
    PP("PP"),

    /**
     * 现转预
     */
    USEFG("UseFG"),

    /**
     * 预付到酒店（注意，只有海外直连酒店才应赋值为PH）
     */
    PH("PH");

    HotelBalanceTypeEnum(String desc) {
        this.desc = desc;
    }

    private String desc;

    public String getDesc() {
        return desc;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }

    public boolean isFG(){
        return this == FG;
    }

    public boolean isPP(){
        return this == PP;
    }

    public boolean isPPOrUseFG(){
        return isUseFG() || isPP();
    }


    public boolean isPH(){
        return this == PH;
    }

    public boolean isUseFG(){
        return this == USEFG;
    }

    public static HotelBalanceTypeEnum getHotelBalanceTypeEnum(String desc){
        if (StringUtil.isBlank(desc)) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.PARAM_VALID_RESOURCE_TOKEN_NULL,
                "getHotelBalanceTypeEnum error");
        }
        for (HotelBalanceTypeEnum hotelBalanceTypeEnum : HotelBalanceTypeEnum.values()) {
            if (hotelBalanceTypeEnum.getDesc().equalsIgnoreCase(desc)) {
                return hotelBalanceTypeEnum;
            }
        }
        throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.PARAM_VALID_RESOURCE_TOKEN_NULL,
            "getHotelBalanceTypeEnum error");
    }


    public static HotelBalanceTypeEnum getBalanceTypeEnum(String balance) {
        String balanceType =
                Optional.ofNullable(balance)
                        .orElse(StringUtil.EMPTY);
        switch (StringUtils.upperCase(balanceType)) {
            case "FG":
            case "0":
                return HotelBalanceTypeEnum.FG;
            case "PP":
            case "1":
                return HotelBalanceTypeEnum.PP;
            case "PH":
            case "3":
                return HotelBalanceTypeEnum.PH;
            case "USEFG":
            case "2":
                return HotelBalanceTypeEnum.USEFG;
            default:
                LogDefaultUtil.bizDefaultVal(HotelBalanceTypeEnum.FG, "BookingBaseInfoRequestBO.getBalanceTypeEnum",
                        "balanceType is " + balanceType);
                return HotelBalanceTypeEnum.FG;
        }
    }

    public static HotelBalanceTypeEnum getBalanceTypeEnumEnum(ResourceToken resourceToken) {
        String balanceType = Optional.ofNullable(resourceToken)
                .map(ResourceToken::getRoomResourceToken).map(RoomResourceToken::getBalanceType).orElse("");
        switch (StringUtils.upperCase(balanceType)) {
            case "FG":
            case "0":
                return HotelBalanceTypeEnum.FG;
            case "PP":
            case "1":
                return HotelBalanceTypeEnum.PP;
            case "PH":
            case "3":
                return HotelBalanceTypeEnum.PH;
            case "USEFG":
            case "2":
                return HotelBalanceTypeEnum.USEFG;
            default:
                return HotelBalanceTypeEnum.FG;
        }
    }
}
