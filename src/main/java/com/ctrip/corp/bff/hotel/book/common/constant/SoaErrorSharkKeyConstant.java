package com.ctrip.corp.bff.hotel.book.common.constant;

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.ActionInfo;
import com.ctrip.corp.bff.framework.template.entity.IntegrationResponse;
import com.ctrip.corp.bff.framework.template.entity.MapString;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.qconfig.QConfigOfCodeMappingConfig;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.CodeMappingConfig;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/10/11 14:38
 */
public class SoaErrorSharkKeyConstant {
    // eg:key.corp.hotel.errorMsg.CorpBffSpecific.approvalFlowCheck.602
    public static final String SOA_ERROR = "key.corp.hotel.{0}.{1}.errorMsg.{2}";
    public static final String SOA_ERROR_DEFAULT = "key.corp.hotel.{0}.{1}.errorMsg";
    public static final String SERVICE_NAME_CORP_BFF_SPECIFIC = "CorpBffSpecific";
    public static final String ACTION_NAME_APPROVAL_FLOW_CHECK = "approvalFlowCheck";
    public static final String ACTION_NAME_COST_CENTER_CHECK = "costCenterCheck";
    public static final String ACTION_NAME_APPROVAL_FLOW_COMPUTE = "approvalFlowCompute";
    public static final String SERVICE_NAME_CORP_BFF_PAYMENT_SERVICE = "CorpBffPaymentService";
    public static final String ACTION_NAME_PAYMENT_ORDER_CREATE = "paymentOrderCreate";
    public static final String SERVICE_NAME_CORP_HOTEL_BOOK_SERVICE = "CorpHotelBookService";
    public static final String ACTION_NAME_CREATE_ORDER = "createOrder";
    public static final String ACTION_NAME_SUBMIT_ORDER = "submitOrder";
    public static final String SERVICE_NAME_CORP_AGG_HOTEL_TMC_SERVICE = "CorpAggHotelTMCService";
    public static final String ACTION_NAME_CHECK_TRAVEL_POLICY = "checkTravelPolicy";
    public static final String SERVICE_NAME_CORP_APPROVE_SERVICE = "CorpApproveService";
    public static final String ACTION_NAME_MATCH_APPROVAL_FLOW = "matchApprovalFlow";
    public static final String SERVICE_NAME_ORDER_PAYMENT_CENTER_TRANSACTION_SERVICE =
        "OrderPaymentCenterTransactionService";
    public static final String ACTION_NAME_TRANSACTION_PAY_URL = "transactionPayUrl";
    public static final String SERVICE_NAME_PRE_APPROVAL_SERVICE = "PreApprovalService";
    public static final String ACTION_NAME_VERIFY_FELLOW_PASSENGER = "verifyFellowPassenger";

    public static final String SERVICE_NAME_CORP_HOTEL_BOOK_COMMON_WS = "CorpHotelBookCommonWS";
    public static final String ACTION_NAME_DISTRIBUTE_PAYMENT_AMOUNT = "distributePaymentAmount";
    public static final String ACTION_NAME_GET_SUPPORTED_PAYMENT_METHOD = "getSupportedPaymentMethod";

    public static final String SERVICE_NAME_CORP_AGG_HOTEL_EXPENSE_SERVICE_CLIENT = "CorpAggHotelExpenseServiceClient";
    public static final String ACTION_NAME_CALCULATE_SERVICE_CHARGE = "calculateServiceChargeV2";

    public static final String SERVICE_NAME_CORP_HOTEL_AGG_ROOM_AVAIL_SERVICE = "CorpHotelAggRoomAvailService";
    public static final String ACTION_NAME_CHECK_AVAIL = "checkAvail";
    public static final String SERVICE_NAME_CORP_AGG_HOTEL_SALESTRATEGY_SERVICE = "CorpAggHotelSaleStrategyService";
    public static final String ACTION_NAME_CHECK_BOOKING_LIMITION = "checkBookingLimition";
    public static final String SERVICE_NAME_ORDER_FOUNDATION_CENTER_DATA_SYNC_SERVICE =
        "OrderFoundationCenterDataSyncService";
    public static final String ACTION_NAME_SAVE_COMMON_DATA = "saveCommonData";

    public static final String SERVICE_NAME_TRIP_ORDER_SERVICE = "TripOrderService";
    public static final String ACTION_NAME_CREATE_TRIP = "createTrip";
    public static String buildSoaErrorFriendlyMessage(String serviceName, String actionName, String errorCode) {
        return BFFSharkUtil.getSharkValue(StringUtil.indexedFormat(SOA_ERROR, serviceName, actionName, errorCode));
    }

    public static String buildSoaErrorFriendlyMessageDefault(String serviceName, String actionName) {
        return BFFSharkUtil.getSharkValue(StringUtil.indexedFormat(SOA_ERROR_DEFAULT, serviceName, actionName));
    }

    public static String buildSoaErrorFriendlyMessageHasDefault(String serviceName, String actionName,
        String errorCode) {
        String friendlyMessage = buildSoaErrorFriendlyMessage(serviceName, actionName, errorCode);
        if (StringUtil.isBlank(friendlyMessage)) {
            return buildSoaErrorFriendlyMessageDefault(serviceName, actionName);
        }
        return friendlyMessage;
    }

    public static ActionInfo buildActionInfo(Integer responseCode,
        QConfigOfCodeMappingConfig qConfigOfCodeMappingConfig, String codeMappingConfigKey,
        List<MapString> dataExtInfoList) {
        if (responseCode == null) {
            return null;
        }
        if (responseCode.equals(CommonConstant.SUCCESS_20000)) {
            return null;
        }
        if (qConfigOfCodeMappingConfig == null) {
            return null;
        }
        CodeMappingConfig codeMappingConfig = qConfigOfCodeMappingConfig.getCodeMappingConfig(codeMappingConfigKey);
        if (codeMappingConfig == null) {
            return null;
        }
        String errorCodeMappingActionType = codeMappingConfig.getMappingActionType(responseCode.toString());
        if (StringUtil.isEmpty(errorCodeMappingActionType)) {
            return null;
        }
        ActionInfo actionInfo = new ActionInfo();
        actionInfo.setActionType(errorCodeMappingActionType);
        actionInfo.setDataExtInfo(dataExtInfoList);
        return actionInfo;
    }

}
