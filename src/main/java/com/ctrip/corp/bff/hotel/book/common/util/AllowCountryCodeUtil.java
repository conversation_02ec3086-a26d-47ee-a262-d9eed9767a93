package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

/**
 * @Author: z.c. wang
 * @Date: 2025/5/20 14:33
 * @Version 1.0
 */
public class AllowCountryCodeUtil {

    // 仅内宾的code列表
    private static final Set<String> ONLY_FOR_CN_CODE_LIST = Set.of("CN");
    // 仅中宾的code列表
    private static final Set<String> ONLY_FOR_CN_GAT_CODE_LIST = Set.of("CN", "TW", "HK", "MO");

    /**
     * 是否只接待内宾
     *
     * @return
     */
    public static boolean isOnlyForCnGuest(List<String> allowCountryCodeList) {
        if (CollectionUtil.isEmpty(allowCountryCodeList)) {
            return false;
        }
        Set<String> listSet = new HashSet<>(allowCountryCodeList);
        return ONLY_FOR_CN_CODE_LIST.equals(listSet);
    }

    /**
     * 是否只接待中宾
     *
     * @return
     */
    public static boolean isOnlyForCnGatGuest(List<String> allowCountryCodeList) {
        if (CollectionUtil.isEmpty(allowCountryCodeList)) {
            return false;
        }
        Set<String> listSet = new HashSet<>(allowCountryCodeList);
        return !ONLY_FOR_CN_CODE_LIST.equals(listSet) && ONLY_FOR_CN_GAT_CODE_LIST.containsAll(listSet);
    }


}
