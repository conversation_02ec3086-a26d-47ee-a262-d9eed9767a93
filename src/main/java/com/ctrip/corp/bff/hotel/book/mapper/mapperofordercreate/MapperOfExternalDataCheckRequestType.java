package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.corpsz.corpcustomrequirementservice.common.contract.DataType;
import com.ctrip.corp.corpsz.corpcustomrequirementservice.common.contract.ExternalDataCheckRequestType;
import org.springframework.stereotype.Component;

import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/05/29
 */
@Component public class MapperOfExternalDataCheckRequestType
    extends AbstractMapper<Tuple1<OrderCreateRequestType>, ExternalDataCheckRequestType> {

    @Override protected ExternalDataCheckRequestType convert(Tuple1<OrderCreateRequestType> param) {
        ExternalDataCheckRequestType externalDataCheckRequestType = new ExternalDataCheckRequestType();
        OrderCreateRequestType orderCreateRequestType = param.getT1();
        externalDataCheckRequestType.setDataType(DataType.TencentEID);
        externalDataCheckRequestType.setDataList(orderCreateRequestType.getHotelBookPassengerInputs().stream()
            .map(HotelBookPassengerInput::getHotelPassengerInput).map(HotelPassengerInput::getExternalEmployeeId)
            .collect(Collectors.toList()));
        externalDataCheckRequestType.setCorpId(
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId());
        return externalDataCheckRequestType;
    }

    @Override protected ParamCheckResult check(Tuple1<OrderCreateRequestType> param) {
        OrderCreateRequestType orderCreateRequestType = param.getT1();
        if (orderCreateRequestType.getHotelBookPassengerInputs().stream().anyMatch(
            hotelPassenger -> hotelPassenger == null || hotelPassenger.getHotelPassengerInput() == null
                || StringUtil.isBlank(hotelPassenger.getHotelPassengerInput().getExternalEmployeeId()))) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.PSG_MISS_EXTERNAL_EMPLOYEE_ID,
                OrderCreateErrorEnum.PSG_MISS_EXTERNAL_EMPLOYEE_ID.getErrorMessage());
        }
        return null;
    }
}
