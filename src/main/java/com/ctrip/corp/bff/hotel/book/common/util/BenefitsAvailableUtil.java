package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.agg.hotel.roomavailable.entity.DeductionConditionInfoType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.MergedRightInfoType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.RightBaseInfoType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.RightRuleInfoType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomDailyInfo;
import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum;
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.ExceptionUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.hotel.book.common.constant.SharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.DeductionStrategyTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.EarnPointsPatternEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.RightTypeEnum;
import com.ctrip.corp.bff.hotel.book.contract.RightsDetail;
import com.ctrip.corp.bff.hotel.book.contract.RightsOption;
import com.ctrip.framework.apollo.core.utils.StringUtils;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.MessageFormat;
import java.text.ParseException;
import java.util.Arrays;
import java.util.Calendar;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import static com.ctrip.corp.bff.hotel.book.common.constant.SharkKeyConstant.SHARK_STRATEGY_TYPE;
import static com.ctrip.corp.bff.hotel.book.common.util.DateUtils.YYYY_MM_DD;

/**
 * @Author: chenchuang
 * @Date: 2024/9/27 20:13
 * @Description:
 */
public class BenefitsAvailableUtil {

    public static final String EN_SENTENCE_PATTERN = "^[a-zA-Z0-9\\p{Punct}\\s]+$";

    /**
     * 会员卡积分
     */
    private static final String HYKMS = "HYKMS";

    /**
     * 手机号积分
     */
    private static final String SJHMS = "SJHMS";

    /**
     * 线下积分
     */
    private static final String XXMS = "XXMS";

    public static RightsDetail getRightsDetail(MergedRightInfoType mergedRightInfoType, int roomQuantity, int guestNumber,
                                              List<RoomDailyInfo> roomDailyInfoBOS) {
        if (mergedRightInfoType == null) {
            return null;
        }
        RightBaseInfoType rightBaseInfoType =
                Optional.ofNullable(mergedRightInfoType.getBaseInfo()).orElse(new RightBaseInfoType());
        RightTypeEnum rightTypeEnum = RightTypeEnum.findByType(rightBaseInfoType.getRightType());
        if (rightTypeEnum == null) {
            return null;
        }
        int totalBalance = Optional.ofNullable(rightBaseInfoType.getBalance()).orElse(0);
        if (totalBalance == 0) {
            return null;
        }
        RightsDetail rightsDetail = new RightsDetail();
        rightsDetail.setRightCode(rightBaseInfoType.getRightCode());
        rightsDetail.setRightType(rightTypeEnum.getCode());

        String timeDesc =
                Optional.ofNullable(mergedRightInfoType.getRuleInfo()).map(RightRuleInfoType::getAdvancedRuleValue)
                        .orElse(null);
        if (!StringUtil.isEmpty(timeDesc) && timeDesc.lastIndexOf(":") > 0 && timeDesc.length() > 5) {
            timeDesc = timeDesc.substring(0, timeDesc.lastIndexOf(":"));
        }

        switch (rightTypeEnum) {
            case MEAL:
                // 早餐
                rightsDetail.setRightName(BFFSharkUtil.getSharkValue(SharkKeyConstant.RIGHTS_MEAL_NAME));
                // 免费兑早餐
                rightsDetail.setContent(BFFSharkUtil.getSharkValue(SharkKeyConstant.RIGHTS_MEAL_CONTENT));
                // 超值不花钱
                rightsDetail.setDescription(BFFSharkUtil.getSharkValue(SharkKeyConstant.RIGHTS_MEAL_DESC));
                if (CollectionUtil.isEmpty(roomDailyInfoBOS)) {
                    return null;
                }
                int totalDefault = roomQuantity * guestNumber * roomDailyInfoBOS.size();
                if (totalBalance >= totalDefault) {
                    rightsDetail.setExchangeable(totalBalance - totalDefault);
                    rightsDetail.setRightsOptions(
                            buildMealOptions(roomDailyInfoBOS, roomQuantity * guestNumber, roomQuantity * guestNumber));
                } else {
                    rightsDetail.setExchangeable(totalBalance);
                    rightsDetail.setRightsOptions(buildMealOptions(roomDailyInfoBOS, roomQuantity * guestNumber, 0));
                }

                break;
            case FREE_CANCEL:
                // 免费兑取消
                rightsDetail.setRightName(BFFSharkUtil.getSharkValue(SharkKeyConstant.RIGHTS_CANCEL_NAME));
                // 免费兑取消, 房型升级
                rightsDetail.setContent(BFFSharkUtil.getSharkValue(SharkKeyConstant.RIGHTS_CANCEL_CONTENT));
                // 入住日20:00前
                rightsDetail.setDescription(
                    messageFormat(BFFSharkUtil.getSharkValue(SharkKeyConstant.RIGHTS_CANCEL_DESC), timeDesc));
                RightsOption option1 = new RightsOption();
                // 免费兑换
                option1.setOption(BFFSharkUtil.getSharkValue(SharkKeyConstant.RIGHTS_OPTION));
                option1.setOptionType("checkBox");
                // 免费取消默认1份 最大1份
                option1.setExchangeable(1);
                option1.setExchangeableDefault(1);
                rightsDetail.setExchangeable(totalBalance - 1);
                rightsDetail.setRightsOptions(Arrays.asList(option1));
                break;
            case DELAYED_CHECK_OUT:
                // 延迟退房
                rightsDetail.setRightName(BFFSharkUtil.getSharkValue(SharkKeyConstant.RIGHTS_DELAYED_NAME));
                // 延迟退房至14：00
                rightsDetail.setContent(
                    messageFormat(BFFSharkUtil.getSharkValue(SharkKeyConstant.RIGHTS_DELAYED_CONTENT),
                                timeDesc));
                // 安心多睡几小时
                rightsDetail.setDescription(BFFSharkUtil.getSharkValue(SharkKeyConstant.RIGHTS_DELAYED_DESC));
                RightsOption option2 = new RightsOption();
                // 免费兑换
                option2.setOption(BFFSharkUtil.getSharkValue(SharkKeyConstant.RIGHTS_OPTION));
                option2.setOptionType("checkBox");
                if (totalBalance >= roomQuantity) {
                    rightsDetail.setExchangeable(totalBalance - roomQuantity);
                    option2.setExchangeable(roomQuantity);
                    option2.setExchangeableDefault(roomQuantity);
                } else {
                    rightsDetail.setExchangeable(0);
                    option2.setExchangeable(totalBalance);
                    option2.setExchangeableDefault(totalBalance);
                }
                option2.setExchangeable(roomQuantity);
                rightsDetail.setRightsOptions(Arrays.asList(option2));
                break;
            case FREE_UPGRADE_ROOM:
                // 房型升级
                rightsDetail.setRightName(BFFSharkUtil.getSharkValue(SharkKeyConstant.RIGHTS_UPGRADE_NAME));
                // 房型升级
                rightsDetail.setContent(BFFSharkUtil.getSharkValue(SharkKeyConstant.RIGHTS_UPGRADE_CONTENT));
                // 视房态安排
                rightsDetail.setDescription(BFFSharkUtil.getSharkValue(SharkKeyConstant.RIGHTS_UPGRADE_DESC));
                RightsOption option3 = new RightsOption();
                // 免费兑换
                option3.setOption(BFFSharkUtil.getSharkValue(SharkKeyConstant.RIGHTS_OPTION));
                option3.setOptionType("checkBox");
                option3.setExchangeable(roomQuantity);
                if (totalBalance >= roomQuantity) {
                    rightsDetail.setExchangeable(totalBalance - roomQuantity);
                    option3.setExchangeable(roomQuantity);
                    option3.setExchangeableDefault(roomQuantity);
                } else {
                    rightsDetail.setExchangeable(0);
                    option3.setExchangeable(totalBalance);
                    option3.setExchangeableDefault(totalBalance);
                }
                rightsDetail.setRightsOptions(Arrays.asList(option3));
                break;
            default:

        }
        return rightsDetail;
    }

    private static String messageFormat(String value, String param) {
        if (StringUtil.isEmpty(value) || StringUtil.isEmpty(param)) {
            return StringUtil.EMPTY;
        }
        return MessageFormat.format(value, param);
    }
    public static List<RightsOption> buildMealOptions(List<RoomDailyInfo> roomDailyInfoBOS, int exchangeable,
                                                    int exchangeableDefault) {
        if (CollectionUtil.isEmpty(roomDailyInfoBOS)) {
            return null;
        }
        return roomDailyInfoBOS.stream().map(o -> {
            RightsOption option = new RightsOption();
            option.setOptionType("multiple");
            option.setExchangeable(exchangeable);
            option.setExchangeableDefault(exchangeableDefault);
            try {
                Calendar nextDay = DateUtils.stringToCalender(o.getLocalEffectDate(), YYYY_MM_DD);
                nextDay.add(Calendar.DAY_OF_MONTH, 1);
                option.setOption(DateUtils.formatDate(nextDay));
            } catch (ParseException e) {
                LogUtil.loggingClogOnly(LogLevelEnum.Error, BenefitsAvailableUtil.class, "getMealOptions",
                    ExceptionUtil.getFullException(e), null);
            }
            return option;
        }).collect(Collectors.toList());
    }

    public static EarnPointsPatternEnum convert2EarnPointsPatternEnum(String pointsMode) {
        if (StringUtil.isBlank(pointsMode)) {
            return EarnPointsPatternEnum.UNSUPPORTED;
        }
        if (StringUtil.equalsIgnoreCase(HYKMS, pointsMode)) {
            return EarnPointsPatternEnum.BY_VIP_CARD;
        }
        if (StringUtil.equalsIgnoreCase(SJHMS, pointsMode)) {
            return EarnPointsPatternEnum.BY_PHONE;
        }
        if (StringUtil.equalsIgnoreCase(XXMS, pointsMode)) {
            return EarnPointsPatternEnum.TO_HOTEL;
        }
        return EarnPointsPatternEnum.UNSUPPORTED;
    }

    /**
     * 优惠券描述
     *
     * @param
     * @return
     */
    public static String getCouponDesc(Integer deductionStrategyTypeId,
                                       List<DeductionConditionInfoType> deductionConditionInfoTypes, BigDecimal totalPrice) {
        if (deductionStrategyTypeId == null
                || CollectionUtil.isEmpty(deductionConditionInfoTypes)) {
            return StringUtils.EMPTY;
        }
        DeductionStrategyTypeEnum deductionStrategyType = DeductionStrategyTypeEnum.getDeductionStrategyTypeById(deductionStrategyTypeId);
        if (deductionStrategyType == DeductionStrategyTypeEnum.FIXED_REDUCTION
                || deductionStrategyType == DeductionStrategyTypeEnum.PERCENTAGE_REDUCTION) {
            // 立减、折扣
            return MessageFormat.format(
                    BFFSharkUtil.getSharkValue(MessageFormat.format(SHARK_STRATEGY_TYPE, deductionStrategyType.getId())),
                    formatAmountStr(Optional.ofNullable(totalPrice).orElse(BigDecimal.ZERO)));
        }
        if (deductionStrategyType == DeductionStrategyTypeEnum.LADDER_REDUCTION) {
            // 满减
            DeductionConditionInfoType deductionConditionInfoType = getDeductionConditionInfo(deductionConditionInfoTypes, totalPrice);
            return MessageFormat.format(
                    BFFSharkUtil.getSharkValue(MessageFormat.format(SHARK_STRATEGY_TYPE, deductionStrategyType.getId())),
                    formatAmountStr(deductionConditionInfoType.getLadderStartAmount()),
                    formatAmountStr(deductionConditionInfoType.getDeductionAmount()));
        }

        return StringUtils.EMPTY;
    }

    /**
     * 价格字符串
     * <p>
     * 2.00 -> 2
     *
     * @param amount
     * @return
     */
    public static String formatAmountStr(BigDecimal amount) {
        if (amount == null) {
            return "";
        }
        return new DecimalFormat("#.##").format(amount.setScale(2, BigDecimal.ROUND_HALF_UP));
    }

    private static DeductionConditionInfoType getDeductionConditionInfo(List<DeductionConditionInfoType> list, BigDecimal totalPrice) {
        return list.stream()
                .filter(x -> {
                    if (totalPrice == null || x.getDeductionAmount() == null) {
                        return false;
                    }
                    return totalPrice.intValue() == x.getDeductionAmount().intValue();
                })
                .findFirst()
                .orElse(list.get(0));
    }

}
