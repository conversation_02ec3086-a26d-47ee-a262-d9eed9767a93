package com.ctrip.corp.bff.hotel.book.common.enums;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;

/**
 * <AUTHOR>
 * @date 2024/12/12
 */
public enum RoomPropertyEnum {
    /** 证件房型 */
    CERTIFICATE_ROOM("CERTIFICATE_ROOM"),
    ;

    /**
     * 房型类型，
     */
    private String propertyType;

    RoomPropertyEnum(String propertyType) {
        this.propertyType = propertyType;
    }

    public String getPropertyType() {
        return propertyType;
    }

    public static RoomPropertyEnum getCertificateRoomFromPropertyType(String propertyType) {
        for (RoomPropertyEnum roomPropertyEnum : RoomPropertyEnum.values()) {
            if (StringUtil.equalsIgnoreCase(roomPropertyEnum.getPropertyType(), propertyType)) {
                return roomPropertyEnum;
            }
        }

        return null;
    }
}
