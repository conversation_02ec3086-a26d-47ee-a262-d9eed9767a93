package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.agg.hotel.expense.contract.model.BaseInfoType;
import com.ctrip.corp.agg.hotel.expense.contract.model.BookingPolicyType;
import com.ctrip.corp.agg.hotel.expense.contract.model.ConditionInfoType;
import com.ctrip.corp.agg.hotel.expense.contract.model.HotelInfoType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckAvailResponseType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelItem;
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelRatePlan;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken;
import com.ctrip.corp.bff.framework.template.common.utils.HostUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple6;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple7;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple8;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.BookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.util.HotelPayTypeUtil;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodResponseType;
import com.ctrip.corp.order.data.aggregation.query.contract.HotelOrderType;
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataResponseType;
import com.ctrip.corp.order.data.aggregation.query.contract.ServiceFeeConfigType;
import com.ctrip.model.GetRoomChargePolicyListRequestType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.QueryOrderSettingsResponseType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.ServiceFeeSettingType;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description 房费政策列表请求
 * @Date 2024/8/22 10:04
 * @Version 1.0
 */
@Component
public class MapperOfGetRoomChargePolicyListRequestType extends AbstractMapper<Tuple8<WrapperOfAccount.AccountInfo
        , QueryHotelOrderDataResponseType, QueryOrderSettingsResponseType, WrapperOfCheckAvail.CheckAvailInfo
        , BookingInitRequestType, ResourceToken, GetSupportedPaymentMethodResponseType, HotelPayTypeEnum>, GetRoomChargePolicyListRequestType> {
    @Override
    protected GetRoomChargePolicyListRequestType convert(Tuple8<WrapperOfAccount.AccountInfo
            , QueryHotelOrderDataResponseType, QueryOrderSettingsResponseType, WrapperOfCheckAvail.CheckAvailInfo
            , BookingInitRequestType, ResourceToken, GetSupportedPaymentMethodResponseType, HotelPayTypeEnum> param) {
        WrapperOfAccount.AccountInfo accountInfo = param.getT1();
        QueryHotelOrderDataResponseType queryHotelOrderDataResponseType = param.getT2();
        QueryOrderSettingsResponseType queryOrderSettingsResponseType = param.getT3();
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = param.getT4();
        BookingInitRequestType bookingInitRequestType = param.getT5();
        ResourceToken resourceToken = param.getT6();
        GetSupportedPaymentMethodResponseType getSupportedPaymentMethodResponse = param.getT7();
        HotelPayTypeEnum selectedRoomPayType = param.getT8();
        GetRoomChargePolicyListRequestType requestType = new GetRoomChargePolicyListRequestType();
        requestType.setServiceChargeToken(buildServiceChargeToken(queryOrderSettingsResponseType, bookingInitRequestType));
        requestType.setAmsChargeVersion(buildAmsFeeConfigVersion(accountInfo, queryHotelOrderDataResponseType, bookingInitRequestType));
        requestType.setBaseInfo(buildBaseInfoType(accountInfo, bookingInitRequestType));
        requestType.setBookingInfo(getBookingInfoType(bookingInitRequestType));
        requestType.setHotelInfo(buildHotelInfoType(checkAvailInfo));
        requestType.setConditionFilter(conditionFilter(bookingInitRequestType, getSupportedPaymentMethodResponse, accountInfo, resourceToken, selectedRoomPayType));
        requestType.setAmsFeeConfigVersion(buildOrderAmsFeeConfigVersion(queryHotelOrderDataResponseType, bookingInitRequestType));
        return requestType;
    }
    protected String buildOrderAmsFeeConfigVersion(QueryHotelOrderDataResponseType queryHotelOrderDataResponseType, BookingInitRequestType bookingInitRequestType){
        if (StrategyOfBookingInitUtil.modify(bookingInitRequestType.getStrategyInfos()) ||
            StrategyOfBookingInitUtil.onlyApplyModify(bookingInitRequestType.getStrategyInfos())) {
            return Optional.ofNullable(queryHotelOrderDataResponseType).map(QueryHotelOrderDataResponseType::getHotelInfo)
                .map(com.ctrip.corp.order.data.aggregation.query.contract.HotelInfoType::getHotelOrder).map(HotelOrderType::getServiceFeeConfig)
                .map(ServiceFeeConfigType::getAmsFeeConfigVersion).orElse(null);
        }
        return null;
    }

    private List<ConditionInfoType> conditionFilter(BookingInitRequestType bookingInitRequestType
            , GetSupportedPaymentMethodResponseType getSupportedPaymentMethodResponse, WrapperOfAccount.AccountInfo accountInfo
            , ResourceToken resourceToken, HotelPayTypeEnum selectedRoomPayType) {
        List<ConditionInfoType> conditionInfoTypes = new ArrayList<>();
        HotelPayTypeEnum hotelPayTypeEnum =
                BookingInitUtil.getRoomPayType(selectedRoomPayType, getSupportedPaymentMethodResponse, null,
                        accountInfo);
        if (hotelPayTypeEnum == null) {
            return conditionInfoTypes;
        }
        if (hotelPayTypeEnum.isCorpPay() || hotelPayTypeEnum.isMixPay() || hotelPayTypeEnum.isFlashPay()) {
            conditionInfoTypes.add(new ConditionInfoType("PaymentMethod", "AccountPay"));
        } else {
            conditionInfoTypes.add(new ConditionInfoType("PaymentMethod", "PersonalPay"));
        }
        RoomResourceToken roomResourceToken = Optional.ofNullable(resourceToken)
                .map(ResourceToken::getRoomResourceToken).orElse(new RoomResourceToken());
        if ("C".equalsIgnoreCase(roomResourceToken.getRoomType())) {
            conditionInfoTypes.add(new ConditionInfoType("RoomType", "C"));
        } else {
            conditionInfoTypes.add(new ConditionInfoType("RoomType", "M"));
        }
        conditionInfoTypes.add(buildConditionInfoTypeSourceFrom(bookingInitRequestType.getIntegrationSoaRequestType()));
        return conditionInfoTypes;
    }

    public static ConditionInfoType buildConditionInfoTypeSourceFrom(
        IntegrationSoaRequestType integrationSoaRequestType) {
        ConditionInfoType conditionInfoType = new ConditionInfoType();
        conditionInfoType.setFieldName("OperationChannel");
        if (integrationSoaRequestType.getSourceFrom() == SourceFrom.Offline) {
            conditionInfoType.setFieldValue("OFFLINE");
        } else if (integrationSoaRequestType.getSourceFrom() == SourceFrom.Online) {
            conditionInfoType.setFieldValue("ONLINE");
        } else {
            conditionInfoType.setFieldValue("APP");
        }
        return conditionInfoType;
    }

    public static BookingPolicyType getBookingInfoType(BookingInitRequestType bookingInitRequestType) {
        BookingPolicyType bookingInfoType = new BookingPolicyType();
        bookingInfoType.setFeeType(CorpPayInfoUtil.isPrivate(bookingInitRequestType.getCorpPayInfo()) ? "P" : "C");
        return bookingInfoType;
    }

    private static String buildServiceChargeToken(QueryOrderSettingsResponseType queryOrderSettingsResponseType, BookingInitRequestType bookingInitRequestType) {
        if (BookingInitUtil.buildModifyServiceFeeOrder(bookingInitRequestType)) {
            return Optional.ofNullable(queryOrderSettingsResponseType)
                    .map(QueryOrderSettingsResponseType::getServiceFeeSetting)
                    .map(ServiceFeeSettingType::getServiceChargeToken).orElse(null);
        }
        return null;
    }

    private static String buildAmsFeeConfigVersion(WrapperOfAccount.AccountInfo accountInfo,
                                                   QueryHotelOrderDataResponseType queryHotelOrderDataResponseType, BookingInitRequestType bookingInitRequestType) {

        return BookingInitUtil.buildModifyServiceFeeOrder(bookingInitRequestType) ?
                Optional.ofNullable(queryHotelOrderDataResponseType)
                        .map(QueryHotelOrderDataResponseType::getHotelInfo)
                        .map(com.ctrip.corp.order.data.aggregation.query.contract.HotelInfoType::getHotelOrder)
                        .map(HotelOrderType::getServiceFeeConfig).map(ServiceFeeConfigType::getServiceFeeVersion).orElse(null)
                : accountInfo.getServiceFeeVersion();
    }

    public static BaseInfoType buildBaseInfoType(WrapperOfAccount.AccountInfo accountInfo,
        BookingInitRequestType bookingInitRequestType) {
        BaseInfoType baseInfoType = new BaseInfoType();
        baseInfoType.setTraceId(Optional.ofNullable(bookingInitRequestType.getIntegrationSoaRequestType())
            .map(IntegrationSoaRequestType::getRequestId).orElse(null));
        baseInfoType.setUid(RequestHeaderUtil.getUserId(bookingInitRequestType.getIntegrationSoaRequestType()));
        String policyUid = Optional.ofNullable(bookingInitRequestType.getPolicyInput()).map(PolicyInput::getPolicyUid).orElse(null);
        if (StringUtil.isEmpty(policyUid)) {
            policyUid = RequestHeaderUtil.getUserId(bookingInitRequestType.getIntegrationSoaRequestType());
        }
        baseInfoType.setPolicyUid(policyUid);
        baseInfoType.setCorpId(RequestHeaderUtil.getCorpId(bookingInitRequestType.getIntegrationSoaRequestType()));
        baseInfoType.setPOS(
            HostUtil.mapToAccountPos(bookingInitRequestType.getIntegrationSoaRequestType().getUserInfo().getPos()));
        baseInfoType.setCustomCurrency(accountInfo.getCurrency());
        return baseInfoType;
    }

    public static HotelInfoType buildHotelInfoType(WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo) {
        HotelInfoType hotelInfoType = new HotelInfoType();
        HotelItem hotelItem = Optional.ofNullable(checkAvailInfo.getHotelItem()).orElse(new HotelItem());
        hotelInfoType.setCountryId(hotelItem.getCountry());
        hotelInfoType.setProvinceId(hotelItem.getProvince());
        hotelInfoType.setCityId(hotelItem.getCity());
        return hotelInfoType;
    }

    @Override
    protected ParamCheckResult check(Tuple8<WrapperOfAccount.AccountInfo
            , QueryHotelOrderDataResponseType, QueryOrderSettingsResponseType, WrapperOfCheckAvail.CheckAvailInfo
            , BookingInitRequestType, ResourceToken, GetSupportedPaymentMethodResponseType, HotelPayTypeEnum> tuple) {
        return null;
    }
}
