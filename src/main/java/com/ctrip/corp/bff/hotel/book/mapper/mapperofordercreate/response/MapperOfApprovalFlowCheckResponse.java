package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.common.constant.SoaErrorSharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType;
import com.ctrip.corp.bff.specific.contract.ApprovalFlowCheckResponseType;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Description 同行程校验
 * @Date 2024/7/15 14:20
 * @Version 1.0
 */
@Component public class MapperOfApprovalFlowCheckResponse
    extends AbstractMapper<Tuple1<ApprovalFlowCheckResponseType>, Tuple2<Boolean, OrderCreateResponseType>> {

    @Override protected Tuple2<Boolean, OrderCreateResponseType> convert(Tuple1<ApprovalFlowCheckResponseType> tuple) {
        return null;
    }

    @Override protected ParamCheckResult check(Tuple1<ApprovalFlowCheckResponseType> tuple) {
        ApprovalFlowCheckResponseType approvalFlowCheckResponseType = tuple.getT1();
        if (!checkApprovalFlowCheckResponseType(approvalFlowCheckResponseType)) {
            return OrderCreateProcessorOfUtil.buildParamCheckResult(Optional.ofNullable(approvalFlowCheckResponseType)
                    .map(ApprovalFlowCheckResponseType::getIntegrationResponse).orElse(null),
                OrderCreateErrorEnum.APPROVAL_FLOWCHECK_ERROR, SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_BFF_SPECIFIC,
                SoaErrorSharkKeyConstant.ACTION_NAME_APPROVAL_FLOW_CHECK);
        }
        return null;
    }

    protected boolean checkApprovalFlowCheckResponseType(ApprovalFlowCheckResponseType approvalFlowCheckResponseType) {
        return StringUtil.equalsIgnoreCase(
            Optional.ofNullable(approvalFlowCheckResponseType).map(ApprovalFlowCheckResponseType::getCheckResult)
                .orElse(null), "T");
    }
}
