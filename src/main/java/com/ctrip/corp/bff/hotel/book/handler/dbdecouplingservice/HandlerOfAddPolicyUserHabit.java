package com.ctrip.corp.bff.hotel.book.handler.dbdecouplingservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.dbdecouplingService.DBDecouplingServiceClient;
import corp.user.service.dbdecouplingService.contract.AddPolicyUserHabitRequestType;
import corp.user.service.dbdecouplingService.contract.AddPolicyUserHabitResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/12/12 21:28
 */
@Component
public class HandlerOfAddPolicyUserHabit  extends AbstractHandlerOfSOA<AddPolicyUserHabitRequestType, AddPolicyUserHabitResponseType, DBDecouplingServiceClient> {
    @Override
    protected String getMethodName() {
        return "addPolicyUserHabit";
    }
}
