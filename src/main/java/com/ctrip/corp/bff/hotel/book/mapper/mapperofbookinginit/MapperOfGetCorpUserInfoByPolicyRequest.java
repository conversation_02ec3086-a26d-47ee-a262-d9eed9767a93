package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoRequestType;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * @Author: yfx
 * @Date: 2019/3/29 14:47
 */
@Component
public class MapperOfGetCorpUserInfoByPolicyRequest extends AbstractMapper<Tuple1<BookingInitRequestType>, GetCorpUserInfoRequestType> {
    public MapperOfGetCorpUserInfoByPolicyRequest() {
    }

    protected GetCorpUserInfoRequestType convert(Tuple1<BookingInitRequestType> param) {
        if (param == null) {
            return null;
        }
        BookingInitRequestType bookingInitRequestType = param.getT1();
        String policyUid = Optional.ofNullable(bookingInitRequestType.getPolicyInput()).map(PolicyInput::getPolicyUid).orElse(null);
        if (StringUtil.isEmpty(policyUid)) {
            policyUid = Optional.ofNullable(bookingInitRequestType.getIntegrationSoaRequestType())
                .map(IntegrationSoaRequestType::getUserInfo)
                .map(UserInfo::getUserId)
                .orElse(null);
        }
        GetCorpUserInfoRequestType getCorpUserInfoRequestType = new GetCorpUserInfoRequestType();
        getCorpUserInfoRequestType.setUid(policyUid);
        return getCorpUserInfoRequestType;
    }

    protected ParamCheckResult check(Tuple1<BookingInitRequestType> uidTuple1) {
        return null;
    }
}
