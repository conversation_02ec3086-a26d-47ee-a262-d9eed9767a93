package com.ctrip.corp.bff.hotel.book.handler.triporderservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.soa._21234.CreateTripRequestType;
import com.ctrip.soa._21234.CreateTripResponseType;
import com.ctrip.soa._21234.GetTripBookingInfosRequestType;
import com.ctrip.soa._21234.GetTripBookingInfosResponseType;
import com.ctrip.soa._21234.TripOrderServiceClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024-10-28 17:32:19
 */
@Component
public class HandlerOfGetTripBookingInfos extends AbstractHandlerOfSOA<GetTripBookingInfosRequestType
        , GetTripBookingInfosResponseType, TripOrderServiceClient> {
    @Override
    protected String getMethodName() {
        return "getTripBookingInfos";
    }
}
