package com.ctrip.corp.bff.hotel.book.qconfig;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.RegisterConfigEntry;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/14
 */
@Component
public class QconfigOfRegisterConfig {

    /**
     * 默认同步
     */
    public static final String SYNC = "SYNC";

    /**
     * 异步
     */
    public static final String ASYNC = "ASYNC";

    @QConfig("registerConfig.json")
    private List<RegisterConfigEntry> registerConfigEntryList;

    public String getExecutiveTypeByGroupId(Integer groupId) {
        if (CollectionUtil.isEmpty(registerConfigEntryList)) {
            return SYNC;
        }
        RegisterConfigEntry registerConfigEntry = CollectionUtil.findFirst(registerConfigEntryList,
                t -> t != null && String.valueOf(groupId).equalsIgnoreCase(t.getGroupId()));
        if (registerConfigEntry == null || StringUtil.isBlank(registerConfigEntry.getExecuteType())) {
            return SYNC;
        }
        return registerConfigEntry.getExecuteType();
    }

    public List<String> getCountryCodeLimitsByGroupId(Integer groupId) {
        if (CollectionUtil.isEmpty(registerConfigEntryList)) {
            return null;
        }
        RegisterConfigEntry registerConfigEntry = CollectionUtil.findFirst(registerConfigEntryList,
                t -> t != null && String.valueOf(groupId).equalsIgnoreCase(t.getGroupId()));
        if (registerConfigEntry == null || StringUtil.isBlank(registerConfigEntry.getCountryCodeLimits())) {
            return null;
        }
        return Arrays.stream(registerConfigEntry.getCountryCodeLimits().split(",")).collect(Collectors.toList());
    }
}
