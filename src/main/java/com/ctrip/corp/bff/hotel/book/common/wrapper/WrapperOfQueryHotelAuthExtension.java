package com.ctrip.corp.bff.hotel.book.common.wrapper;

import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType;
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextResponseType;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/7/24 14:35
 */
public class WrapperOfQueryHotelAuthExtension {

    private WrapperOfAccount.AccountInfo accountInfo;
    private GetTravelPolicyContextResponseType getTravelPolicyContextResponseType;
    private CheckTravelPolicyResponseType checkTravelPolicyResponseType;
    private QueryCheckAvailContextResponseType queryCheckAvailContextResponseType;
    private OrderCreateRequestType orderCreateRequestType;
    private ResourceToken resourceToken;
    private WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo;
    private QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig;
    private Map<String, StrategyInfo> strategyInfoMap;

    public static class Build {
        private final WrapperOfQueryHotelAuthExtension wrapperOfQueryHotelAuthExtension =
            new WrapperOfQueryHotelAuthExtension();

        public Build withStrategyInfoMap(Map<String, StrategyInfo> strategyInfoMap) {
            wrapperOfQueryHotelAuthExtension.strategyInfoMap = strategyInfoMap;
            return this;
        }

        public Build withAccountInfo(WrapperOfAccount.AccountInfo accountInfo) {
            wrapperOfQueryHotelAuthExtension.accountInfo = accountInfo;
            return this;
        }

        public Build withGetTravelPolicyContextResponseType(
            GetTravelPolicyContextResponseType getTravelPolicyContextResponseType) {
            wrapperOfQueryHotelAuthExtension.getTravelPolicyContextResponseType = getTravelPolicyContextResponseType;
            return this;
        }

        public Build withCheckTravelPolicyResponseType(CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
            wrapperOfQueryHotelAuthExtension.checkTravelPolicyResponseType = checkTravelPolicyResponseType;
            return this;
        }

        public Build withQueryCheckAvailContextResponseType(
            QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
            wrapperOfQueryHotelAuthExtension.queryCheckAvailContextResponseType = queryCheckAvailContextResponseType;
            return this;
        }

        public Build withOrderCreateRequestType(OrderCreateRequestType orderCreateRequestType) {
            wrapperOfQueryHotelAuthExtension.orderCreateRequestType = orderCreateRequestType;
            return this;
        }

        public Build withResourceToken(ResourceToken resourceToken) {
            wrapperOfQueryHotelAuthExtension.resourceToken = resourceToken;
            return this;
        }

        public Build withCheckAvailInfo(WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
            wrapperOfQueryHotelAuthExtension.checkAvailInfo = checkAvailInfo;
            return this;
        }

        public Build withQconfigOfCertificateInitConfig(QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig) {
            wrapperOfQueryHotelAuthExtension.qconfigOfCertificateInitConfig = qconfigOfCertificateInitConfig;
            return this;
        }

        public WrapperOfQueryHotelAuthExtension build() {
            return wrapperOfQueryHotelAuthExtension;
        }
    }

    public static Build builder() {
        return new WrapperOfQueryHotelAuthExtension.Build();
    }

    public WrapperOfAccount.AccountInfo getAccountInfo() {
        return accountInfo;
    }

    public GetTravelPolicyContextResponseType getGetTravelPolicyContextResponseType() {
        return getTravelPolicyContextResponseType;
    }

    public CheckTravelPolicyResponseType getCheckTravelPolicyResponseType() {
        return checkTravelPolicyResponseType;
    }

    public QueryCheckAvailContextResponseType getQueryCheckAvailContextResponseType() {
        return queryCheckAvailContextResponseType;
    }

    public OrderCreateRequestType getOrderCreateRequestType() {
        return orderCreateRequestType;
    }

    public ResourceToken getResourceToken() {
        return resourceToken;
    }


    public WrapperOfCheckAvail.BaseCheckAvailInfo getCheckAvailInfo() {
        return checkAvailInfo;
    }

    public QconfigOfCertificateInitConfig getQconfigOfCertificateInitConfig() {
        return qconfigOfCertificateInitConfig;
    }

    public Map<String, StrategyInfo> getStrategyInfoMap() {
        return strategyInfoMap;
    }
}
