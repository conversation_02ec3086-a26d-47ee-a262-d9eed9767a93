package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.agg.commonws.entity.GetPackageRoomListRequestType;
import com.ctrip.corp.agg.commonws.entity.PkgPersonInfoType;
import com.ctrip.corp.agg.commonws.pkg.room.snapshot.entity.GetPackageRoomSnapshotRequestType;
import com.ctrip.corp.agg.commonws.pkg.room.snapshot.entity.PackageRoomSnapshotType;
import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.enums.BooleanValueEnum;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.hotelbook.commonws.entity.RequestBaseInfoType;
import com.ctrip.corp.hotelbook.commonws.entity.UserInfoType;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2024/10/8 21:33
 */
@Component
public class MapperOfGetPackageRoomListRequest
    extends AbstractMapper<Tuple3<OrderCreateRequestType,
        ResourceToken,
    WrapperOfCheckAvail.CheckAvailContextInfo>, GetPackageRoomListRequestType> {
    @Override protected GetPackageRoomListRequestType convert(
        Tuple3<OrderCreateRequestType, ResourceToken, WrapperOfCheckAvail.CheckAvailContextInfo> tuple) {
        OrderCreateRequestType orderCreateRequestType = tuple.getT1();
        ResourceToken resourceToken = tuple.getT2();
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo = tuple.getT3();
        Integer packageId = checkAvailInfo.getPackageId();
        String aggPackageToken = checkAvailInfo.getAggPackageToken();

        int adultNumber = Optional.ofNullable(resourceToken)
            .map(ResourceToken::getRoomResourceToken)
            .map(RoomResourceToken::getAdult)
            .orElse(0);
        GetPackageRoomListRequestType requestType = new GetPackageRoomListRequestType();
        requestType.setRequestBaseInfo(buildRequestBaseInfoType(orderCreateRequestType));
        requestType.setPackageId(packageId != null ? Collections.singletonList(packageId) : null);
        requestType.setPackageTokenList(StringUtil.isNotBlank(aggPackageToken) ? Collections.singletonList(aggPackageToken) : null);
        if (adultNumber > 0) {
            PkgPersonInfoType pkgPersonInfoType = new PkgPersonInfoType();
            pkgPersonInfoType.setPkgId(packageId);
            pkgPersonInfoType.setAdultQty(adultNumber);
            requestType.setPkgPersonInfos(Collections.singletonList(pkgPersonInfoType));
        }
        return requestType;
    }
    private static final String REQUEST_FROM_APP = "APP";
    private static final String REQUEST_FROM_ONLINE = "ONLINE";
    private static final String REQUEST_FROM_OFFLINE = "OFFLINE";

    public static String getChannel(SourceFrom sourceFrom) {
        if (sourceFrom == null) {
            return REQUEST_FROM_APP;
        }
        if (sourceFrom == SourceFrom.Online) {
            return REQUEST_FROM_ONLINE;
        }
        if (sourceFrom == SourceFrom.Offline) {
            return REQUEST_FROM_OFFLINE;
        }
        return REQUEST_FROM_APP;
    }
    public static RequestBaseInfoType buildRequestBaseInfoType(OrderCreateRequestType orderCreateRequestType) {
        IntegrationSoaRequestType integrationSoaRequestType = orderCreateRequestType.getIntegrationSoaRequestType();
        RequestBaseInfoType baseInfo = new RequestBaseInfoType();
        baseInfo.setTraceId(orderCreateRequestType.getIntegrationSoaRequestType().getRequestId());
        UserInfoType userInfo = new UserInfoType();
        userInfo.setUid(RequestHeaderUtil.getUserId(integrationSoaRequestType));
        userInfo.setPolicyUid(
            Optional.ofNullable(orderCreateRequestType.getHotelPolicyInput()).map(HotelPolicyInput::getPolicyInput)
                .map(PolicyInput::getPolicyUid)
                .orElse(orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId()));
        userInfo.setCorpId(RequestHeaderUtil.getCorpId(integrationSoaRequestType));
        baseInfo.setUserInfo(userInfo);
        baseInfo.setBookingChannel(getChannel(integrationSoaRequestType.getSourceFrom()));
        baseInfo.setLocale(integrationSoaRequestType.getLanguage());

        return baseInfo;
    }
    @Override
    protected ParamCheckResult check(Tuple3<OrderCreateRequestType, ResourceToken, WrapperOfCheckAvail.CheckAvailContextInfo> tuple) {
        return null;
    }
}
