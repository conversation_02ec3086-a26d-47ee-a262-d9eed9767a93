package com.ctrip.corp.bff.hotel.book.common.wrapper;

import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfCityBaseInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfSaveCommonData.Builder;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.SearchApprovalResponseType;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/9/18 22:40
 */
public class WrapperOfVerifyApproval {
    private CheckTravelPolicyResponseType checkTravelPolicyResponseType;
    private WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo;
    private WrapperOfAccount.AccountInfo accountInfo;
    private OrderCreateToken orderCreateToken;
    private OrderCreateRequestType orderCreateRequestType;
    private SearchApprovalResponseType searchApprovalResponseType;
    private WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo;
    private Map<String, StrategyInfo> strategyInfoMap;
    public CheckTravelPolicyResponseType getCheckTravelPolicyResponseType() {
        return checkTravelPolicyResponseType;
    }

    public WrapperOfCityBaseInfo.CityBaseInfo getCityBaseInfo() {
        return cityBaseInfo;
    }

    public WrapperOfAccount.AccountInfo getAccountInfo() {
        return accountInfo;
    }

    public OrderCreateToken getOrderCreateToken() {
        return orderCreateToken;
    }

    public OrderCreateRequestType getOrderCreateRequestType() {
        return orderCreateRequestType;
    }

    public SearchApprovalResponseType getSearchApprovalResponseType() {
        return searchApprovalResponseType;
    }

    public Map<String, StrategyInfo> getStrategyInfoMap() {
        return strategyInfoMap;
    }
    public WrapperOfCheckAvail.CheckAvailContextInfo getCheckAvailContextInfo() {
        return checkAvailContextInfo;
    }

    public static Builder builder() {
        return new Builder();
    }

    public static class Builder {
        private CheckTravelPolicyResponseType checkTravelPolicyResponseType;
        private WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo;
        private WrapperOfAccount.AccountInfo accountInfo;
        private OrderCreateToken orderCreateToken;
        private OrderCreateRequestType orderCreateRequestType;
        private SearchApprovalResponseType searchApprovalResponseType;
        private WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo;
        private Map<String, StrategyInfo> strategyInfoMap;

        public Builder setCheckTravelPolicyResponseType(CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
            this.checkTravelPolicyResponseType = checkTravelPolicyResponseType;
            return this;
        }

        public Builder setCityBaseInfo(WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo) {
            this.cityBaseInfo = cityBaseInfo;
            return this;
        }

        public Builder setAccountInfo(WrapperOfAccount.AccountInfo accountInfo) {
            this.accountInfo = accountInfo;
            return this;
        }

        public Builder setOrderCreateToken(OrderCreateToken orderCreateToken) {
            this.orderCreateToken = orderCreateToken;
            return this;
        }

        public Builder setOrderCreateRequestType(OrderCreateRequestType orderCreateRequestType) {
            this.orderCreateRequestType = orderCreateRequestType;
            return this;
        }

        public Builder setSearchApprovalResponseType(SearchApprovalResponseType searchApprovalResponseType) {
            this.searchApprovalResponseType = searchApprovalResponseType;
            return this;
        }

        public Builder setCheckAvailContextInfo(WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo) {
            this.checkAvailContextInfo = checkAvailContextInfo;
            return this;
        }

        public Builder setStrategyInfoMap(Map<String, StrategyInfo> strategyInfoMap) {
            this.strategyInfoMap = strategyInfoMap;
            return this;
        }

        public WrapperOfVerifyApproval build() {
            WrapperOfVerifyApproval approval = new WrapperOfVerifyApproval();
            approval.checkTravelPolicyResponseType = this.checkTravelPolicyResponseType;
            approval.cityBaseInfo = this.cityBaseInfo;
            approval.accountInfo = this.accountInfo;
            approval.orderCreateToken = this.orderCreateToken;
            approval.orderCreateRequestType = this.orderCreateRequestType;
            approval.searchApprovalResponseType = this.searchApprovalResponseType;
            approval.checkAvailContextInfo = this.checkAvailContextInfo;
            approval.strategyInfoMap = this.strategyInfoMap;
            return approval;
        }
    }
}
