package com.ctrip.corp.bff.hotel.book.common.enums;

/**
 * <AUTHOR>
 * @date 2024-09-27
 **/
public enum BookingInitArrivalInfoTypeEnum {
    /**
     * 超时担保15分钟
     */
    GUARANTEE_TIMEOUT_FIFTEEN("GUARANTEE_TIMEOUT_FIFTEEN"),
    /**
     * 非超时担保15分钟
     */
    NO_GUARANTEE_FIFTEEN("NO_GUARANTEE_FIFTEEN"),
    /**
     * 其他15分钟
     */
    OTHER_FIFTEEN("OTHER_FIFTEEN"),
    /**
     * 超时担保过最晚到店时间
     */
    GUARANTEE_TIMEOUT_BEFORE_LAST("GU<PERSON>ANTEE_TIMEOUT_BEFORE_LAST"),
    /**
     * 无需担保过最晚到店时间
     */
    NO_GUARANTEE_BEFORE_LAST("NO_GUARANTEE_BEFORE_LAST"),
    /**
     * 无需担保其他场景
     */
    NO_GUARANTEE_OTHER("NO_GUARANTEE_OTHER"),
    /**
     * 未过担保时间
     */
    GUARANTEE_BEFORE_GUARANTEE("GUARANTEE_BEFORE_GUARANTEE"),
    /**
     * 担保时间内
     */
    GUARANTEE_BETWEEN_GUARANTEE("GUARANTEE_BETWEEN_GUARANTEE")
    ;

    private String code;

    BookingInitArrivalInfoTypeEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}
