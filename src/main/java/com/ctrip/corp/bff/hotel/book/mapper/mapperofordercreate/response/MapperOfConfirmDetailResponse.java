package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response;

import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig;
import com.ctrip.corp.bff.framework.hotel.entity.contract.ConfirmDetailInfo;
import com.ctrip.corp.bff.framework.hotel.entity.contract.ConfirmInfo;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2024/7/15 14:20
 * @Version 1.0
 */
@Component public class MapperOfConfirmDetailResponse extends
    AbstractMapper<Tuple2<OrderCreateRequestType, CreateOrderResponseType>, Tuple2<Boolean, OrderCreateResponseType>> {
    private static final int REPEAT_ORDER_CODE = 99997;
    private static final String REPEAT_ORDER = "REPEAT_ORDER";

    @Override protected Tuple2<Boolean, OrderCreateResponseType> convert(
        Tuple2<OrderCreateRequestType, CreateOrderResponseType> tuple) {
        OrderCreateRequestType requestType = tuple.getT1();
        CreateOrderResponseType createOrderResponseType = tuple.getT2();
        OrderCreateResponseType orderCreateResponseType = new OrderCreateResponseType();
        if (!requireRepeatOrderCreateResponseType(createOrderResponseType, requestType)) {
            return Tuple2.of(false, orderCreateResponseType);
        }
        ConfirmInfo confirmInfo = new ConfirmInfo();
        List<ConfirmDetailInfo> confirmDetailInfos = new ArrayList<>();
        ConfirmDetailInfo confirmDetailInfo = new ConfirmDetailInfo();
        confirmDetailInfo.setCode(REPEAT_ORDER);
        confirmDetailInfos.add(confirmDetailInfo);
        confirmInfo.setConfirmDetailInfos(confirmDetailInfos);
        orderCreateResponseType.setConfirmInfo(confirmInfo);
        return Tuple2.of(true, orderCreateResponseType);
    }

    @Override protected ParamCheckResult check(Tuple2<OrderCreateRequestType, CreateOrderResponseType> tuple) {

        return null;
    }

    public static boolean requireRepeatOrderCreateResponseType(CreateOrderResponseType createOrderResponseType,
        OrderCreateRequestType orderCreateRequestType) {
        if (!QConfigOfCustomConfig.isSupport("supportLimitRepeatAction",
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId())) {
            return false;
        }
        return TemplateNumberUtil.getValue(
            Optional.ofNullable(createOrderResponseType).map(CreateOrderResponseType::getResponseCode).orElse(0))
            == REPEAT_ORDER_CODE;
    }
}
