package com.ctrip.corp.bff.hotel.book.common.signature;

import io.protostuff.Tag;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/23 18:34
 */
public class ApproveObjSignatureBO {
    /**
     * 成本中心
     */
    @Tag(1)
    public List<CostCenterSignatureBO> costCenterSignatureBOS;

    /**
     * 项目号名称
     */
    @Tag(2)
    public List<String> projectNames;

    /**
     * 出行人
     */
    @Tag(3)
    public List<String> travelers;

    /**
     * 出行人uid 数组
     */
    @Tag(4)
    public List<String> travelerUids;

    /**
     * 出行目的，后续版本请使用新字段travelPurposeNames
     */
    @Tag(5)
    public List<String> travelPurpose;

    /**
     * 出行目的名称
     */
    @Tag(6)
    public List<String> travelPurposeNames;
    @Tag(7)
    public String submitter;
    /**
     * 出行人信息
     */
    public List<TravelerInfoSignatureBO> travelerInfoSignatureBOS;

    public List<String> getProjectNames() {
        return projectNames;
    }

    public void setProjectNames(List<String> projectNames) {
        this.projectNames = projectNames;
    }

    public List<String> getTravelers() {
        return travelers;
    }

    public void setTravelers(List<String> travelers) {
        this.travelers = travelers;
    }

    public List<String> getTravelerUids() {
        return travelerUids;
    }

    public void setTravelerUids(List<String> travelerUids) {
        this.travelerUids = travelerUids;
    }

    public List<String> getTravelPurpose() {
        return travelPurpose;
    }

    public void setTravelPurposeNames(List<String> travelPurposeNames) {
        this.travelPurposeNames = travelPurposeNames;
    }

    public String getSubmitter() {
        return submitter;
    }

    public void setSubmitter(String submitter) {
        this.submitter = submitter;
    }

    public List<CostCenterSignatureBO> getCostCenterSignatureBOS() {
        return costCenterSignatureBOS;
    }

    public void setCostCenterSignatureBOS(List<CostCenterSignatureBO> costCenterSignatureBOS) {
        this.costCenterSignatureBOS = costCenterSignatureBOS;
    }

    public List<TravelerInfoSignatureBO> getTravelerInfoSignatureBOS() {
        return travelerInfoSignatureBOS;
    }

    public void setTravelerInfoSignatureBOS(List<TravelerInfoSignatureBO> travelerInfoSignatureBOS) {
        this.travelerInfoSignatureBOS = travelerInfoSignatureBOS;
    }
}
