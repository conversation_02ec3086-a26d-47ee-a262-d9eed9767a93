package com.ctrip.corp.bff.hotel.book.handler.corphotelbookcommonws;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.hotelbook.commonws.CorpHotelBookCommonWSClient;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedInvoiceTypeRequestType;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedInvoiceTypeResponseType;
import org.springframework.stereotype.Component;

/**
 * @Author: chenchuang
 * @Date: 2024/8/22 16:26
 * @Description:agg返回的发票信息
 */
@Component
public class HandlerOfGetSupportedInvoiceType extends AbstractHandlerOfSOA<GetSupportedInvoiceTypeRequestType,
        GetSupportedInvoiceTypeResponseType, CorpHotelBookCommonWSClient> {

    @Override
    protected String getMethodName() {
        return "getSupportedInvoiceType";
    }
}
