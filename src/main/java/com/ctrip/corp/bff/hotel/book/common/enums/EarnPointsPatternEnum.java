package com.ctrip.corp.bff.hotel.book.common.enums;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/9/20 22:26
 */
public enum EarnPointsPatternEnum {


    /**
     * 华住积分模式（手机号）
     */
    BY_PHONE("byPhone"),

    /**
     * 会员卡号积分模式
     */
    BY_VIP_CARD("byVipCard"),

    /**
     * 线下操作积分模式
     */
    TO_HOTEL("toHotel"),

    /**
     * 不支持积分
     */
    UNSUPPORTED("unsupported");


    private String pattern;

    EarnPointsPatternEnum(String pattern) {
        this.pattern = pattern;
    }

    public static EarnPointsPatternEnum getValue(String pattern) {
        if (pattern == null) {
            return null;
        }
        return Arrays.stream(EarnPointsPatternEnum.values())
            .filter(e -> StringUtil.equalsIgnoreCase(e.getPattern(), pattern))
            .findFirst()
            .orElse(null);
    }

    public String getPattern() {
        return pattern;
    }
}
