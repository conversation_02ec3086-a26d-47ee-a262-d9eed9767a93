package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.bff.framework.hotel.common.util.PbSerializerUtil;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.utils.ResourceTokenUtil;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum;
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil;
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ResourceTokenInfo;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import org.apache.commons.lang3.BooleanUtils;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2024/8/21 14:36
 * @Version 1.0
 */
public class TokenParseUtil {

    public static OrderCreateToken parseOrderCreateToken(String orderCreateTokenStr, Boolean useOrderCreate) {
        if (StringUtil.isBlank(orderCreateTokenStr)) {
            OrderCreateToken orderCreateToken = new OrderCreateToken();
            orderCreateToken.setUseOrderCreate(BooleanUtils.isTrue(useOrderCreate));
            return orderCreateToken;
        }
        OrderCreateToken orderCreateToken = parseToken(orderCreateTokenStr, OrderCreateToken.class);
        if (orderCreateToken == null) {
            throw BusinessExceptionBuilder.createAlertException(
                OrderCreateErrorEnum.PARAM_VALID_ORDER_CREATE_TOKEN_ERROR, "parseOrderCreateToken error");
        }
        orderCreateToken.setUseOrderCreate(BooleanUtils.isTrue(useOrderCreate));
        return orderCreateToken;
    }

    public static ResourceToken parseResourceToken(ResourceTokenInfo resourceTokenInfo) {
        // 资源token解析
        ResourceToken resourceToken = ResourceTokenUtil.tryParseToken(
                Optional.ofNullable(resourceTokenInfo).map(ResourceTokenInfo::getResourceToken)
                        .orElse(null));
        if (resourceToken == null) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.PARAM_VALID_RESOURCE_TOKEN_ERROR,
                "parseResourceToken error");
        }
        if (resourceToken.getReservationResourceToken() == null
            || StringUtil.isBlank(resourceToken.getReservationResourceToken().getWsId())) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.PARAM_VALID_RESOURCE_TOKEN_ERROR,
                "parseResourceToken error");
        }
        return resourceToken;
    }

    public static <T> T parseToken(String token, Class<T> clazz) {
        if (StringUtil.isBlank(token)) {
            return null;
        }
        T model = PbSerializerUtil.pbDeserialize(token, clazz);
        if (model == null) {
            LogUtil.loggingClogOnly(LogLevelEnum.Error, TokenParseUtil.class, "parseToken-" + clazz.getSimpleName(),
                "parseToken error", null);
        } else {
            LogUtil.loggingClogOnly(LogLevelEnum.Info, TokenParseUtil.class, "parseToken-" + clazz.getSimpleName(),
                JsonUtil.toJson(model), null);
        }
        return model;
    }

    public static <T> String generateToken(T pidEntity, Class<T> tClass) {
        return PbSerializerUtil.pbSerialize(pidEntity, tClass);
    }

}
