package com.ctrip.corp.bff.hotel.book.qconfig;

import com.ctrip.corp.bff.hotel.book.qconfig.entity.certificate.CertificateBaseOnCountry;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.certificate.CertificateInitConfig;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

/**
 * <AUTHOR>
 * @date 2025/6/23 15:44
 */
@Component
public class QconfigOfCertificateInitConfig {

    @QConfig("100045131#certificateInitConfig.json")
    private CertificateInitConfig certificateInitConfig;

    public CertificateInitConfig getCertificateInitConfig() {
        return certificateInitConfig;
    }

    public void setCertificateInitConfig(CertificateInitConfig certificateInitConfig) {
        this.certificateInitConfig = certificateInitConfig;
    }
}
