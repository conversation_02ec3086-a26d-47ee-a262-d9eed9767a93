package com.ctrip.corp.bff.hotel.book.common.constant;

/**
 * @Author: yfx
 * @Date: 2018/8/27
 */
public class ModuleConfigConstant {

    /**
     * 审批流
     */
    public static final String MODULE_APPROVAL_FLOW = "APPROVAL_FLOW";

    /**
     * 发票模块
     */
    public static final String MODULE_INVOICE = "INVOICE_MODULE";

    /**
     * 发票话术模块
     */
    public static final String MODULE_INVOICE_TIP = "INVOICE_TIP";

    /**
     * 到达时间模块
     */
    public static final String MODULE_ARRIVE_TIME = "ARRIVE_TIME_MODULE";

    /**
     * 自定义床型模块
     */
    public static final String MODULE_CUSTOM_BED = "CUSTOM_BED_MODULE";

    /**
     * 福利可用模块
     */
    public static final String MODULE_BENEFITS_AVAILABLE = "BENEFITS_AVAILABLE_MODULE";

    /**
     * 成本分摊模块
     */
    public static final String MODULE_COST_ALLOCATION = "COST_ALLOCATION_MODULE";

    /**
     * 成本中心模块
     */
    public static final String MODULE_COST_CENTER = "COST_CENTER_MODULE";

    /**
     * 团队VIP信息模块
     */
    public static final String MODULE_GROUP_VIP_INFO = "GROUP_VIP_INFO_MODULE";

    /**
     * 钟点房模块
     */
    public static final String MODULE_HOUR_ROOM = "HOUR_ROOM_MODULE";

    /**
     * 保险
     */
    public static final String MODULE_INSURANCE = "INSURANCE_MODULE";

    /**
     * 担保信息模块
     */
    public static final String MODULE_GUARANTEE_INFO = "GUARANTEE_INFO_MODULE";

    /**
     * 行程模块
     */
    public static final String MODULE_TRIP = "TRIP_MODULE";

    /**
     * 会员积分
     */
    public static final String MODULE_MEMBERSHIP = "MEMBERSHIP_MODULE";

    /**
     * 代订类别
     */
    public static final String DISTINGUISH_RESERVATION = "DISTINGUISH_RESERVATION_MODULE";
}