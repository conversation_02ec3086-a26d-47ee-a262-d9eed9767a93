package com.ctrip.corp.bff.hotel.book.common.enums;

import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.Arrays;

/**
 * @Author: zyp
 * @Date: 2018/8/27
 */
public enum BonusPointEnum {
    /**
     * 会员卡积分
     */
    VIP_CARD("HYKMS"),

    /**
     * 手机号积分
     */
    PHONE("SJHMS"),

    /**
     * 到店积分
     */
    AT_HOTEL("XXMS"),

    /**
     * 会员注册&会员卡积分
     */
    REGISTER_VIP_CARD(null)
    ;

    private final String aggCode;

    BonusPointEnum(String aggCode) {
        this.aggCode = aggCode;
    }

    public static BonusPointEnum valueByAgg(String aggCode, Boolean bonusPointRoom, Boolean isCanRegister, Boolean onlyGroupMemberCanBook) {
        if (onlyGroupMemberCanBook) {
            return BonusPointEnum.VIP_CARD;
        }
        if (BooleanUtil.isNotTrue(bonusPointRoom)) {
            return null;
        }
        BonusPointEnum bonusPointEnum = Arrays.stream(BonusPointEnum.values()).filter(t -> StringUtils.equalsIgnoreCase(aggCode, t.aggCode)).findFirst().orElse(null);
        if (BooleanUtil.isTrue(isCanRegister) && bonusPointEnum == VIP_CARD) {
            return REGISTER_VIP_CARD;
        }
        if (bonusPointEnum == null) {
            return null;
        }
        switch (bonusPointEnum) {
            case VIP_CARD:
                return VIP_CARD;
            case PHONE:
                return PHONE;
            case AT_HOTEL:
                return AT_HOTEL;
            default:
                return null;
        }
    }
}
