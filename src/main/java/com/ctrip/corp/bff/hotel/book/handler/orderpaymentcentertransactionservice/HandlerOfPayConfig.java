package com.ctrip.corp.bff.hotel.book.handler.orderpaymentcentertransactionservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.soa._21685.OrderPaymentCenterTransactionServiceClient;
import com.ctrip.soa._21685.PayConfigRequestType;
import com.ctrip.soa._21685.PayConfigResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 双付丰享支付配置查询
 * @Date 2024/8/20 17:24
 * @Version 1.0
 */
@Component
public class HandlerOfPayConfig extends
        AbstractHandlerOfSOA<PayConfigRequestType, PayConfigResponseType, OrderPaymentCenterTransactionServiceClient> {

    @Override
    protected String getMethodName() {
        return "payConfig";
    }
}
