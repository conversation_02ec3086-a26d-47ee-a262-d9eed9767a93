package com.ctrip.corp.bff.hotel.book.common.constant;
/**
 * <AUTHOR>
 * @date 2024/7/9 16:34
 */
public class SharkKeyConstant {
    /**
     * {0}会员积分需要保证联系人手机号与入住人手机号一致，请更换联系人手机号
     * {0}==eg:HZ
     */
    public static final String EARN_POINTS_PHONE_ERROR = "key.corp.hotel.ordercreate.points.phone.error.{0}";
    /**
     * 选择器兜底描述 eg:请选择未预订差标内房型的原因
     * {0}==OVER_STANDARD/AGREEMENT/BOOK_AHEAD
     */
    public static final String RC_DESC = "key.corp.hotel.ordercreate.rcinfo.rcdesc.{0}";
    /**
     * 选择器兜底描述 eg：根据贵公司差旅政策，房间1、2未预订符合价格和星级房型，故请您选择原因
     * {0}==OVER_STANDARD/AGREEMENT/BOOK_AHEAD
     */
    public static final String RC_TITLE = "key.corp.hotel.ordercreate.rcinfo.rctitle.{0}";
    /**
     * 房间{0}
     */
    public static final String RC_ROOM = "key.corp.hotel.ordercreate.rcinfo.rcroom";
    /**
     * 价格星级RC（根据贵公司差旅政策，{0}未预订符合价格和星级房型的原因是：）
     */
    public static final String LOW_PRICE_R_RC = "key.corp.app.hotel.rc.LowPriceR";

    /**
     * 价格星级RC（根据贵公司差旅政策，{0}未预订符合价格房型的原因是：）
     */
    public static final String LOW_PRICE_R_RC_PRICE = "key.corp.hotel.booking.biz.text.lowprice.room.price";

    /**
     * 价格星级RC（根据贵公司差旅政策，{0}未预订符合星级房型的原因是：）
     */
    public static final String LOW_PRICE_R_RC_STAR = "key.corp.hotel.booking.biz.text.lowprice.room.star";

    /**
     * 低价RC 根据贵公司差旅政策规定，继续预订您需要提供未预订差标内房型的原因:
     */
    public final static String LOW_PRICE_RC = "key.corp.app.hotel.rc.LowPrice";
    /**
     * 根据贵公司差旅政策，因您未预订符合价格房型，故请您选择原因
     */
    public final static String LOW_PRICE_RC_PRICE = "key.corp.hotel.booking.biz.text.lowprice.price";
    /**
     * 根据贵公司差旅政策，因您未预订符合星级房型，故请您选择原因
     */
    public final static String LOW_PRICE_RC_STAR = "key.corp.hotel.booking.biz.text.lowprice.star";
    /**
     * 协议RC
     */
    public final static String C_PRICE_RC = "key.corp.app.hotel.rc.CPrice";
    /**
     * 提前预定RC
     */
    public final static String RESERVATION_RC = "key.corp.app.hotel.rc.Reservation";

    /**
     * （若继续预订，差补额度将加入差标，后续报销则相应扣减）
     */
    public final static String FLOAT_OVER_PRICE = "ctrip.com.hotel.booking.biz.text.policy.float.over.price.{0}";

    // 管控元素名称
    public static final String CONTROL_ELEMENT_NAME = "ctrip.com.hotel.booking.biz.text.control.element.name.{0}";
    /**
     * ({0})
     */
    public static final String BRACKETS = "ctrip.com.hotel.common.punctuation.brackets";
    // 不限
    public static final String CONTROL_VALUE_BILL_ANY = "ctrip.com.hotel.booking.biz.text.control.value.any";
    // {0} (全部城市) eg：日本（全部城市）
    public static final String CONTROL_TITLE_COUNTRY = "ctrip.com.hotel.booking.biz.text.control.value.country";
    // 国内酒店
    public static final String CONTROL_VALUE_DHOTEL = "ctrip.com.hotel.booking.biz.text.control.value.dhotel";
    // 国际酒店
    public static final String CONTROL_VALUE_IHOTEL = "ctrip.com.hotel.booking.biz.text.control.value.ihotel";
    // 不限-{0}
    public static final String CONTROL_VALUE_BILL_FROM_ANY = "ctrip.com.hotel.booking.biz.text.control.value.fromany";
    // 0-{0}
    public static final String CONTROL_VALUE_BILL_FROM_ZERO = "ctrip.com.hotel.booking.biz.text.control.value.fromzero";
    // {0}-不限
    public static final String CONTROL_VALUE_BILL_TO_ANY = "ctrip.com.hotel.booking.biz.text.control.value.toany";
    // {0}-{1}
    public static final String CONTROL_VALUE_BILL_NOT_ANY = "ctrip.com.hotel.booking.biz.text.control.value.noany";
    // 订单状态
    public static final String REPEAT_ORDER_ORDERSTATUS_DESCRIPTION =
        "key.corp.hotel.ordercreate.repeatorder.orderstatus.description.{0}";
    // 商旅客户
    public static final String CONTACT_PSG_NAME = "key.corp.hotel.ordercreate.createorder.contactorpsg.defaultname";
    // 商旅会员酒店订单
    public static final String PAYMENT_ORDER_TITLE_M = "key.corp.hotel.ordercreate.paymentorder.orderTitle.M";
    // 商旅协议酒店订单
    public static final String PAYMENT_ORDER_TITLE_C = "key.corp.hotel.ordercreate.paymentorder.orderTitle.C";

    /**
     * {0}张{1}
     */
    public static final String SHARK_KEY_BED_DESC = "key.corp.hotel.bookingInit.roomInfo.bedDesc";

    /**
     * 宽{0}米
     */
    public static final String SHARK_KEY_BED_WIDTH = "key.corp.hotel.bookingInit.roomInfo.bedWidth";

    /**
     * {0}米-{1}米
     */
    public static final String SHARK_KEY_BED_WIDTH_RANGE = "key.corp.hotel.bookingInit.roomInfo.bedWidthRange";
    /**
     * >={0}米
     */
    public static final String SHARK_KEY_BED_WIDTH_RANGE_MIN = "key.corp.hotel.bookingInit.roomInfo.bedWidthRangeMin";

    /**
     * 应酒店方要求，需填写英文（尽量转达酒店为您安排，但无法保证一定满足）
     */
    public static final String ROOM_CUSTOM_REMARK_AMADEUS_TIP =
        "key.corp.hotel.bookingInit.roomCustomRemark.amadeusTip";

    /**
     * 尽量转达酒店为您安排，但无法保证一定满足
     */
    public static final String ROOM_CUSTOM_REMARK_TIP = "key.corp.hotel.bookingInit.roomCustomRemark.tip.other";

    /********************************************发票*********************************************************/

    /**
     * 发票类型
     */
    public static final String INVOICE_TITLE = "key.corp.hotel.bookingInit.invoice.{0}.title";
    /**
     * 发票类型描述
     */
    public static final String INVOICE_TITLE_TIPS = "key.corp.hotel.bookingInit.invoice.{0}.title.tips";
    /**
     * 保险出保后由保险公司开具
     */
    public static final String INVOICE_INSURANCE_TITLE_TIPS = "key.corp.hotel.bookingInit.invoice.Insurance.tips";

    /**
     * 发票明细话术
     */
    public static final String INVOICE_DETAIL = "key.corp.hotel.bookingInit.invoice.invoiceDetailDisplayValue.{0}";

    /********************************************发票*********************************************************/

    /**
     * 免费兑换
     */
    public static final String RIGHTS_OPTION = "ctrip.com.hotel.bookingInit.rights.option";

    /**
     * 早餐
     */
    public static final String RIGHTS_MEAL_NAME = "ctrip.com.hotel.bookingInit.rights.mealName";

    /**
     * 免费兑早餐
     */
    public static final String RIGHTS_MEAL_CONTENT = "ctrip.com.hotel.bookingInit.rights.mealContent";

    /**
     * 超值不花钱
     */
    public static final String RIGHTS_MEAL_DESC = "ctrip.com.hotel.bookingInit.rights.mealDesc";

    /**
     * 免费兑取消
     */
    public static final String RIGHTS_CANCEL_NAME = "ctrip.com.hotel.bookingInit.rights.cancelName";

    /**
     * 免费兑取消, 房型升级
     */
    public static final String RIGHTS_CANCEL_CONTENT = "ctrip.com.hotel.bookingInit.rights.cancelContent";

    /**
     * 入住日{0}前
     */
    public static final String RIGHTS_CANCEL_DESC = "ctrip.com.hotel.bookingInit.rights.cancelDesc";

    /**
     * 延迟退房
     */
    public static final String RIGHTS_DELAYED_NAME = "ctrip.com.hotel.bookingInit.rights.delayedName";

    /**
     * 延迟退房至{0}
     */
    public static final String RIGHTS_DELAYED_CONTENT = "ctrip.com.hotel.bookingInit.rights.delayedContent";

    /**
     * 安心多睡几小时
     */
    public static final String RIGHTS_DELAYED_DESC = "ctrip.com.hotel.bookingInit.rights.delayedDesc";

    /**
     * 房型升级
     */
    public static final String RIGHTS_UPGRADE_NAME = "ctrip.com.hotel.bookingInit.rights.upgradeName";

    /**
     * 房型升级
     */
    public static final String RIGHTS_UPGRADE_CONTENT = "ctrip.com.hotel.bookingInit.rights.upgradeContent";

    /**
     * 视房态安排
     */
    public static final String RIGHTS_UPGRADE_DESC = "ctrip.com.hotel.bookingInit.rights.upgradeDesc";

    /**
     * 办理酒店入住时，请提供酒店入住登记人姓名
     */
    public static final String REGISTRANT_LIMIT_TIP = "key.corp.hotel.bookingInit.registrant.limit.tip";
    /**
     * 根据当地法规要求，非马来西亚籍客人入住马来西亚酒店需要缴纳旅游税 RM {10} 每间每晚。
     */
    public static final String NATIONALITY_LIMIT_TIP = "key.corp.hotel.bookingInit.nationalityLimit.tip";
    /**
     * 旅游税说明
     */
    public static final String NATIONALITY_LIMIT_TITLE = "key.corp.hotel.bookingInit.nationalityLimit.title";
    /**
     * 自2023年1月1日起，通过平台预订酒店并线上支付房费时，将改由线上预订服务提供方代收代缴旅游税，到店出示缴付凭证，酒店不再收取（到店付款的订单仍需到酒店前台缴纳旅游税）。为判断您是否属于征收范围，请填写您的国籍。
     */
    public static final String NATIONALITY_LIMIT_CONTENT = "key.corp.hotel.bookingInit.nationalityLimit.content";
    /**
     * 自2023年1月1日起，通过平台预订酒店并线上支付房费时，将改由线上预订服务提供方代收代缴旅游税，到店出示缴付凭证，酒店不再收取（到店付款的订单仍需到酒店前台缴纳旅游税）。
     */
    public static final String NATIONALITY_LIMIT_AFTER_CONTENT =
        "key.corp.hotel.bookingInit.nationalityLimit.afterContent";
    public static final String ID_CARD_TYPE_NAME = "key.corp.hotel.bookingInit.idCardType.{0}";

    /**
     * 优惠券描述信息
     */
    public static final String SHARK_STRATEGY_TYPE = "key.corp.hotel.bookingInit.strategyType.{0}";

    /********************************************支付方式*********************************************************/
    public static final String SHARK_PAY_REMARK = "key.corp.hotel.bookingInit.payType.{0}.remark";
    public static final String SHARK_PAYMENT_REMARK = "key.corp.hotel.bookingInit.payment.{0}";
    // 您当前已选择保险，更换为该支付方式将为您删除已选择的保险
    public static final String SHARK_UNSUPPORTED_X_TIP = "key.corp.hotel.bookingInit.payment.unSupportedX.tip";
    public static final String SHARK_SUBREMIND_DESC_FLASH_PAY = "key.corp.hotel.bookingInit.subRemindDesc.flashPay";
    public static final String SHARK_SUBREMIND_DESC_DOCKING_PAY = "key.corp.hotel.bookingInit.subRemindDesc.dockingPay";
    public static final String SHARK_SUBREMIND_DESC_INSURANCE_ONLY_PAY_TYPE =
        "key.corp.hotel.bookingInit.subRemindDesc.insuranceOnlyPayType";
    // vcc个公帐现转预 特别提醒：此房型公司信用卡支付，请勿个人前台垫资
    public static final String VCC_USE_FG_CORP = "key.corp.hotel.bookingInit.payTypeDesc.vccUseFgCorp";
    /********************************************支付方式*********************************************************/

    /**
     * 发票话术
     * ROOM_FEE.CorpSettlement：发票由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司
     * ROOM_FEE.CorpOrder：下单后在订单详情页申请开具发票，由{0}，该房型可开具增值税普票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱地址，发票明细为{1}
     * ROOM_FEE.CorpOrder.Vat：下单后在订单详情页申请开具发票，由{0}，该房型可开具增值税普票、专票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱地址，发票明细为{1}
     * ROOM_FEE.MIX：个人支付部分下单后在订单详情页申请开具发票，由{0}，该房型可开具增值税普票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱，发票明细为{1}。公账支付部分发票由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司
     * ROOM_FEE.MIX.Vat：个人支付部分下单后在订单详情页申请开具发票，由{0}，该房型可开具增值税普票、专票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱，发票明细为{1}。公账支付部分发票由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司
     * ROOM_FEE.HOTEL.MIX：个人支付部分发票由酒店开具，请到前台索取。公账支付发票由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司
     * ROOM_FEE.HOTEL：票据由酒店开具，请到前台索取
     * ROOM_FEE.PRBAL： 携程不开票，发票由支付服务商开具
     * SERVICE_FEE.CorpSettlement：携程预订商旅管理服务费发票会统一寄送到公司
     * SERVICE_FEE.CorpOrder：下单后在订单详情页申请开具发票，由{0}，可开具增值税普票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱地址，发票明细为{1}
     * SERVICE_FEE.CorpOrder.Vat：下单后在订单详情页申请开具发票，由{0}，可开具增值税普票、专票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱地址，发票明细为{1}
     * INSURANCE.CorpSettlement： 开具普票并统一寄送到公司
     * INSURANCE.CorpOrder：下单后在订单详情页申请开具普票
     * PERSON_ACCOUNT_PAY： 若使用心程贝支付，心程贝支付部分统一寄送到公司
     * INSURANCE.INVOICE.CorpSettlement:票据由供应商开具并统一寄送到公司
     * SERVICE_FEE.INVOICE.CorpSettlement：携程预订商旅管理服务费消费凭证会统一寄送到公司
     * ROOM_FEE.INVOICE.CorpSettlement：消费凭证由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司
     * ROOM_FEE.MIX.INVOICE：个人支付部分下单后在订单详情页申请开具发票，由%1$s，该房型可开具增值税普票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱，发票明细为%2$s。公账支付部分的消费凭证会统一寄送到公司
     * ROOM_FEE.MIX.INVOICE.Vat：个人支付部分下单后在订单详情页申请开具发票，由%1$s，该房型可开具增值税普票、专票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱，发票明细为%2$s。公账支付部分的消费凭证会统一寄送到公司
     */
    public static final String INVOICE_TIP_FEE_DESC = "corp.bff.hotel.bookinginit.invoiceTip.feeDesc.{0}";
    // 若使用{0}支付，{0}支付部分统一寄送到公司
    public static final String INVOICE_TIP_PERSON_ACCOUNT_PAY = "corp.bff.hotel.bookinginit.invoiceTip.feeDesc.PERSON_ACCOUNT_PAY";
    // 开票方 HONGRUI:上海携程宏睿国旅旅行社有限公司开具 NANTONG:上海携程宏睿国旅旅行社有限公司南通分公司开具HUASHENG:西安华圣商旅服务有限公司开具
    public static final String INVOICE_TIP_COMPANY = "corp.bff.hotel.bookinginit.invoiceTip.company.{0}";
    // 可专票
    public static final String SUPPORT_D_VAT_INVOICE =
        "corp.bff.hotel.bookinginit.invoiceTip.detail.brief.DVatInvoice";
    // 普票
    public static final String SUPPORT_D_INVOICE =
        "corp.bff.hotel.bookinginit.invoiceTip.detail.brief.DInvoice";
    // 个付
    public static final String ROOM_FEE_PAY_TYPE_SELF_PAY =
        "corp.bff.hotel.bookinginit.invoiceTip.detail.ROOM_FEE.PAY_TYPE.SELF_PAY";
    // 公账支付部分发票由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司
    public static final String INVOICE_TIP_ROOM_FEE_CORP_PAY =
        "corp.bff.hotel.bookinginit.invoiceTip.detail.ROOM_FEE.CORP_PAY";
    // 发票明细 BOOKINGACCOMMODATIONFEE:经济代理*代订住宿费VATAGENCYSERVICEFEE:经济代理*代理服务费
    public static final String INVOICE_TIP_INVOICEDETAIL = "corp.bff.hotel.bookinginit.invoiceTip.invoiceDetail.{0}";
    // 下单后在订单详情页发票信息中查看开票信息
    public static final String INVOICE_TIP_DEFAULT = "corp.bff.hotel.bookinginit.invoiceTip.feeDesc.default";
    // 通过三峡数字商旅预订的酒店请勿自行获取住宿发票，通过其它渠道自购产品仍需自行取得报销凭证。
    public static final String INVOICE_TIP_SANXIA = "corp.bff.hotel.bookinginit.invoiceTip.feeDesc.ROOM_FEE.SANXIA";




    /********************************************审批沿用*********************************************************/
    /**
     * 如果您预订与此订单相同的行程，允许跳过审批过程。
     */
    public static final String REUSE_STATUS_TIP_REUSE = "key.corp.hotel.ordercreate.approvalFlowReuseInfo.reuseStatusTip.reuse";
    /**
     * 部分条件不符合但修改信息可以支持沿用：订单部分条件与原单不一致，无法沿用审批结果。您可返回修改订单。如直接下单将重新审批。
     */
    public static final String REUSE_STATUS_TIP_MODIFY_REUSE = "key.corp.hotel.ordercreate.approvalFlowReuseInfo.reuseStatusTip.modify";
    /**
     * 由于以下原因无法沿用审批结果，如直接下单将重新审批。
     */
    public static final String REUSE_STATUS_TIP_NOT_REUSE = "key.corp.hotel.ordercreate.approvalFlowReuseInfo.reuseStatusTip.notReuse";
    public static final String REUSE_STATUS_TIP_MODIFY_CHECK_RESULT_TYPE = "key.corp.hotel.ordercreate.approvalFlowReuseInfo.checkResultType.{0}";
    public static final String REUSE_STATUS_TIP_REUSE_RESULT_DETAIL = "key.corp.hotel.ordercreate.approvalFlowReuseInfo.reuseResultDetail.{0}";
}