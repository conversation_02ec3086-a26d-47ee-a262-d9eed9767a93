package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataRequestType;
import com.ctrip.corp.order.managementcenter.hotel.service.contract.XProductEnquireRequestType;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * @Author: chenchuang
 * @Date: 2024/8/22 16:26
 * @Description:
 */
@Component
public class MapperOfXProductEnquireRequestType extends AbstractMapper<Tuple2<BookingInitRequestType, Long>, XProductEnquireRequestType> {

    @Override
    protected XProductEnquireRequestType convert(Tuple2<BookingInitRequestType, Long> param) {
        Long orderId = param.getT2();
        XProductEnquireRequestType xProductEnquireRequestType = new XProductEnquireRequestType();
        xProductEnquireRequestType.setOrderId(orderId);

        return xProductEnquireRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple2<BookingInitRequestType, Long> bookingInitRequestTypeBookingInitResponseTypeTuple2) {
        return null;
    }
}
