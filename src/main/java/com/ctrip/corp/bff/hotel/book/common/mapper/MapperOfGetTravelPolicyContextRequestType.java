package com.ctrip.corp.bff.hotel.book.common.mapper;

import com.ctrip.corp.agg.hotel.tmc.context.entity.BaseInfoType;
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextRequestType;
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextResponseType;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CertificateInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.EmailInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.InvoiceInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PhoneInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.common.util.CheckDataUtil;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.tools.contract.CheckDataRequestType;
import com.ctrip.corp.bff.tools.contract.DataInfo;
import com.ctrip.framework.foundation.Foundation;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/6/23 20:37
 */
@Component
public class MapperOfGetTravelPolicyContextRequestType
        extends AbstractMapper<Tuple2<IntegrationSoaRequestType, String>, GetTravelPolicyContextRequestType> {

    @Override
    protected GetTravelPolicyContextRequestType convert(Tuple2<IntegrationSoaRequestType, String> integrationSoaRequestTypeStringTuple2) {
        IntegrationSoaRequestType integrationSoaRequestType = integrationSoaRequestTypeStringTuple2.getT1();
        String policyToken = integrationSoaRequestTypeStringTuple2.getT2();
        GetTravelPolicyContextRequestType request = new GetTravelPolicyContextRequestType();
        BaseInfoType baseInfo = new BaseInfoType();
        baseInfo.setCorpId(integrationSoaRequestType.getUserInfo().getCorpId());
        baseInfo.setUid(integrationSoaRequestType.getUserInfo().getUserId());
        request.setBaseInfo(baseInfo);
        request.setPolicyToken(policyToken);
        request.setTraceId(integrationSoaRequestType.getRequestId());
        request.setOriginClientAppId(Foundation.app().getAppId());
        return request;
    }

    @Override
    protected ParamCheckResult check(Tuple2<IntegrationSoaRequestType, String> integrationSoaRequestTypeStringTuple2) {
        return null;
    }
}
