package com.ctrip.corp.bff.hotel.book.processor;

import java.util.Collections;
import java.util.Map;
import java.util.Optional;

import com.corp.order.service.orderfoundationcentercostinfoservice.SaveOrderCostCenterRequestType;
import com.corp.order.service.orderfoundationcentercostinfoservice.SaveOrderCostCenterResponseType;
import com.ctrip.basebiz.members.core.contract.GetInboundParameterRequestType;
import com.ctrip.basebiz.members.core.contract.GetInboundParameterResponseType;
import com.ctrip.corp.agg.hotel.salestrategy.entity.CalculateTravelRewardsRequestType;
import com.ctrip.corp.agg.hotel.salestrategy.entity.CalculateTravelRewardsResponseType;
import com.ctrip.corp.bff.framework.hotel.common.enums.TrackingBookModeEnum;
import com.ctrip.corp.bff.framework.hotel.common.enums.TrackingEnum;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfAccountInfoConfig;
import com.ctrip.corp.bff.framework.hotel.common.util.CityInfoUtil;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfPolicyUser;
import com.ctrip.corp.bff.framework.template.entity.IntegrationResponse;
import com.ctrip.corp.bff.framework.template.entity.PosEnum;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.*;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple6;
import com.ctrip.corp.bff.hotel.book.common.constant.CustomConfigKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.SoaErrorSharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TrackingUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfApprovalFlowReuse;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckHotelAuthExtension;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfMatchApprovalFlow;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfParamValid;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfQueryHotelAuthExtension;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfSaveCostCenter;
import com.ctrip.corp.bff.hotel.book.contract.CostCenterInfo;
import com.ctrip.corp.bff.hotel.book.contract.OfflineInfo;
import com.ctrip.corp.bff.hotel.book.handler.corpagghotelsalestrategyservice.HandlerOfCalculateTravelRewards;
import com.ctrip.corp.bff.hotel.book.handler.corpaccountqueryservice.HandlerOfGeneralBatchSearchAccountInfo;
import com.ctrip.corp.bff.hotel.book.handler.corpbffspecificservice.HandlerOfApprovalTextInfo;
import com.ctrip.corp.bff.hotel.book.handler.corpbffspecificservice.HandlerOfCostCenterCheck;
import com.ctrip.corp.bff.hotel.book.handler.corpconfigurationservice.HandlerOfCustomConfigSearch;
import com.ctrip.corp.bff.hotel.book.handler.corpcostcenterservice.HandlerOfMatchCostCenter;
import com.ctrip.corp.bff.hotel.book.handler.corpcustomrequirementservice.HandlerOfExternalDataCheck;
import com.ctrip.corp.bff.hotel.book.handler.corphotelmemberservice.HandlerOfRegister;
import com.ctrip.corp.bff.hotel.book.handler.corphotelorderdetailservice.HandlerOfGetCorpHotelOrderDetail;
import com.ctrip.corp.bff.hotel.book.handler.corpuserinfoservice4j.*;
import com.ctrip.corp.bff.hotel.book.handler.giftcardpayws.HandlerOfRetrieveTicketsByOrderType;
import com.ctrip.corp.bff.hotel.book.handler.handlerofgroup4jservice.HandlerOfQueryBizModeBindRelation;
import com.ctrip.corp.bff.hotel.book.handler.corporderverifyservice.HandlerOfCreateOrderCheck;
import com.ctrip.corp.bff.hotel.book.handler.inboundservice.HandlerOfGetInboundParameter;
import com.ctrip.corp.bff.hotel.book.handler.orderdataaggregationqueryservice.HandlerOfOrderOperation;
import com.ctrip.corp.bff.hotel.book.handler.orderfoundationcentercostinfoservice.HandlerOfSaveCostCenter;
import com.ctrip.corp.bff.hotel.book.handler.orderindexextservice.HandlerOfGetOrderFoundationData;
import com.ctrip.corp.bff.hotel.book.handler.triporderservice.HandlerOfSearchTripBasicInfo;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfApprovalTextInfoRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetCorpUserHotelVipCardRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfQueryBizModeBindRelationRequestType;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.*;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response.*;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfEmailInfoConfig;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfRegisterConfig;
import com.ctrip.corp.bff.specific.contract.ApprovalTextInfoRequestType;
import com.ctrip.corp.bff.specific.contract.ApprovalTextInfoResponseType;
import com.ctrip.corp.bff.specific.contract.CostCenterCheckRequestType;
import com.ctrip.corp.bff.specific.contract.CostCenterCheckResponseType;
import com.ctrip.corp.corpsz.configuration.common.contract.CustomConfigSearchRequestType;
import com.ctrip.corp.corpsz.configuration.common.contract.CustomConfigSearchResponseType;
import com.ctrip.corp.corpsz.corpcustomrequirementservice.common.contract.ExternalDataCheckRequestType;
import com.ctrip.corp.corpsz.corpcustomrequirementservice.common.contract.ExternalDataCheckResponseType;
import com.ctrip.corp.order.data.aggregation.query.contract.OrderOperationRequestType;
import com.ctrip.corp.order.data.aggregation.query.contract.OrderOperationResponseType;
import com.ctrip.model.RegisterRequestType;
import com.ctrip.model.RegisterResponseType;
import com.ctrip.microfinance.giftcardpay.ws.contract.RetrieveTicketsByOrderTypeRequestType;
import com.ctrip.microfinance.giftcardpay.ws.contract.RetrieveTicketsByOrderTypeResponseType;
import com.ctrip.soa._20183.CostCenterInfoType;
import com.ctrip.soa._20183.PassengerCostCenterInfoType;
import com.ctrip.soa._21234.GetUserLiteInfoRequestType;
import com.ctrip.soa._21234.GetUserLiteInfoResponseType;
import com.ctrip.soa._21234.SearchTripBasicInfoRequestType;
import com.ctrip.soa._21234.SearchTripBasicInfoResponseType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderDetailInfoType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderDetailRequestType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderDetailResponseType;
import com.ctrip.soa.corp.order.orderindexextservice.v1.GetOrderFoundationDataRequestType;
import com.ctrip.soa.corp.order.orderindexextservice.v1.GetOrderFoundationDataResponseType;
import corp.user.service.CorpAccountQueryService.GeneralBatchSearchAccountInfoRequestType;
import corp.user.service.CorpAccountQueryService.GeneralBatchSearchAccountInfoResponseType;
import corp.user.service.corpUserInfoService.*;
import corp.user.service.group4j.accounts.QueryBizModeBindRelationRequestType;
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType;
import com.ctrip.soa._24373.CreateOrderCheckRequestType;
import com.ctrip.soa._24373.CreateOrderCheckResponseType;
import corp.user.service.costcenterService.matchCostCenter.MatchCostCenterRequestType;
import corp.user.service.costcenterService.matchCostCenter.MatchCostCenterResponseType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import com.ctrip.corp.agg.commonws.entity.GetPackageRoomListRequestType;
import com.ctrip.corp.agg.commonws.entity.GetPackageRoomListResponseType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextRequestType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyRequestType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType;
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextRequestType;
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextResponseType;
import com.ctrip.corp.approve.ws.contract.MatchApprovalFlowRequestType;
import com.ctrip.corp.approve.ws.contract.MatchApprovalFlowResponseType;
import com.ctrip.corp.bff.framework.hotel.common.builder.SearchApprovalRequest;
import com.ctrip.corp.bff.framework.hotel.common.mapper.MapperOfGeneralSearchAccountInfoRequest;
import com.ctrip.corp.bff.framework.hotel.common.mapper.MapperOfGetCityBaseInfoRequestType;
import com.ctrip.corp.bff.framework.hotel.common.mapper.MapperOfGetCorpUserInfoRequest;
import com.ctrip.corp.bff.framework.hotel.common.mapper.MapperOfGetSubAccountConfigRequest;
import com.ctrip.corp.bff.framework.hotel.common.mapper.MapperOfPolicyGeneralSearchAccountInfoRequest;
import com.ctrip.corp.bff.framework.hotel.common.mapper.MapperOfSearchApprovalRequest;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig;
import com.ctrip.corp.bff.framework.hotel.common.util.WaitFutureUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfCityBaseInfo;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.AllocationResultToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ReservationResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil;
import com.ctrip.corp.bff.framework.template.common.qconfig.QConfigUtil;
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.handler.WaitFuture;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.*;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.mapper.MapperOfCheckTravelPolicyRequestType;
import com.ctrip.corp.bff.hotel.book.common.mapper.MapperOfGetPlatformRelationByUidRequestType;
import com.ctrip.corp.bff.hotel.book.common.mapper.MapperOfGetTravelPolicyContextRequestType;
import com.ctrip.corp.bff.hotel.book.common.mapper.MapperOfPayConfigRequestType;
import com.ctrip.corp.bff.hotel.book.common.mapper.MapperOfQueryCheckAvailContextRequestType;
import com.ctrip.corp.bff.hotel.book.common.mapper.MapperOfQueryIndividualAccountRequestType;
import com.ctrip.corp.bff.hotel.book.common.mapper.MapperOfSSOInfoQueryRequestType;
import com.ctrip.corp.bff.hotel.book.common.util.HotelPayTypeUtil;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckTravelPolicy;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCreateOrder;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfFinishResponse;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfOrderRcInfo;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfSaveCommonData;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfVerifyApproval;
import com.ctrip.corp.bff.hotel.book.contract.CostAllocationInfo;
import com.ctrip.corp.bff.hotel.book.contract.FinishInfoOutput;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType;
import com.ctrip.corp.bff.hotel.book.handler.bbzmbrcommonpassenger.HandlerOfGetCommonPassenger;
import com.ctrip.corp.bff.hotel.book.handler.corp4jservice.HandlerOfGetReasoncodes;
import com.ctrip.corp.bff.hotel.book.handler.corpaccountqueryservice.HandlerOfGeneralSearchAccountInfo;
import com.ctrip.corp.bff.hotel.book.handler.corpaccountqueryservice.HandlerOfGetAuthDelayConfig;
import com.ctrip.corp.bff.hotel.book.handler.corpaccountqueryservice.HandlerOfQueryIndividualAccount;
import com.ctrip.corp.bff.hotel.book.handler.corpagghoteltmcservice.HandlerOfCheckTravelPolicy;
import com.ctrip.corp.bff.hotel.book.handler.corpagghoteltmcservice.HandlerOfGetTravelPolicyContext;
import com.ctrip.corp.bff.hotel.book.handler.corpapproveservice.HandlerOfMatchApprovalFlow;
import com.ctrip.corp.bff.hotel.book.handler.corpbffmicebasicauthservice.HandlerOfTmsCreateOrderVerify;
import com.ctrip.corp.bff.hotel.book.handler.corpbffpaymentservice.HandlerOfPaymentOrderCreate;
import com.ctrip.corp.bff.hotel.book.handler.corpbffprofileservice.HandlerOfSSOInfoQuery;
import com.ctrip.corp.bff.hotel.book.handler.corpbffspecificservice.HandlerOfApprovalFlowCheck;
import com.ctrip.corp.bff.hotel.book.handler.corpbffspecificservice.HandlerOfApprovalFlowCompute;
import com.ctrip.corp.bff.hotel.book.handler.corpbfftoolsservice.HandlerOfCheckData;
import com.ctrip.corp.bff.hotel.book.handler.corpconfigurationservice.HandlerOfGetSubAccountConfig;
import com.ctrip.corp.bff.hotel.book.handler.corpendorsementinfoservice.HandlerOfSyncTrip;
import com.ctrip.corp.bff.hotel.book.handler.corphotelbookcommonws.HandlerOfGetPackageRoomList;
import com.ctrip.corp.bff.hotel.book.handler.corphotelbookqueryservice.HandlerOfGetCityBaseInfo;
import com.ctrip.corp.bff.hotel.book.handler.corphotelbookservice.HandlerOfCreateOrder;
import com.ctrip.corp.bff.hotel.book.handler.corphotelbookservice.HandlerOfSubmitOrder;
import com.ctrip.corp.bff.hotel.book.handler.corphotelorderprimeinfojvservice.HandlerOfReimburseSettlementSync;
import com.ctrip.corp.bff.hotel.book.handler.corphotelroomavailableservice.HandlerOfQueryCheckAvailContext;
import com.ctrip.corp.bff.hotel.book.handler.dbdecouplingservice.HandlerOfAddPolicyUserHabit;
import com.ctrip.corp.bff.hotel.book.handler.orderdataaggregationqueryservice.HandlerOfQueryHotelOrderData;
import com.ctrip.corp.bff.hotel.book.handler.orderfoundationcenterauthorizationservice.HandlerOfCheckHotelAuthExtension;
import com.ctrip.corp.bff.hotel.book.handler.orderfoundationcenterauthorizationservice.HandlerOfQueryHotelAuthExtension;
import com.ctrip.corp.bff.hotel.book.handler.orderfoundationcenterdatasyncservice.HandlerOfSaveCommonData;
import com.ctrip.corp.bff.hotel.book.handler.ordergenericsearchservice.HandlerOfHotelOrderRepeatOrder;
import com.ctrip.corp.bff.hotel.book.handler.orderpaymentcenterbillservice.HandlerOfQueryPaymentBillConfig;
import com.ctrip.corp.bff.hotel.book.handler.orderpaymentcentertransactionservice.HandlerOfPayConfig;
import com.ctrip.corp.bff.hotel.book.handler.orderpaymentcentertransactionservice.HandlerOfTransactionPayUrl;
import com.ctrip.corp.bff.hotel.book.handler.preapprovalservice.HandlerOfSearchApproval;
import com.ctrip.corp.bff.hotel.book.handler.preapprovalservice.HandlerOfVerifyFellowPassenger;
import com.ctrip.corp.bff.hotel.book.handler.triporderservice.HandlerOfCreateTrip;
import com.ctrip.corp.bff.hotel.book.handler.triporderservice.HandlerOfGetUserLiteInfo;
import com.ctrip.corp.bff.hotel.book.handler.triporderservice.HandlerOfSearchTripDetail;
import com.ctrip.corp.bff.hotel.book.qconfig.QConfigOfCodeMappingConfig;
import com.ctrip.corp.bff.hotel.book.qconfig.QConfigOfCustomizedSharkConfig;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.MemberBonusRuleEntry;
import com.ctrip.corp.bff.mice.basic.auth.contract.TmsCreateOrderVerifyRequestType;
import com.ctrip.corp.bff.mice.basic.auth.contract.TmsCreateOrderVerifyResponseType;
import com.ctrip.corp.bff.payment.contract.PaymentOrderCreateRequestType;
import com.ctrip.corp.bff.payment.contract.PaymentOrderCreateResponseType;
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryRequestType;
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryResponseType;
import com.ctrip.corp.bff.specific.contract.ApprovalFlowCheckRequestType;
import com.ctrip.corp.bff.specific.contract.ApprovalFlowCheckResponseType;
import com.ctrip.corp.bff.specific.contract.ApprovalFlowComputeRequestType;
import com.ctrip.corp.bff.specific.contract.ApprovalFlowComputeResponseType;
import com.ctrip.corp.bff.tools.contract.CheckDataRequestType;
import com.ctrip.corp.bff.tools.contract.CheckDataResponseType;
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigRequestType;
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.SearchApprovalRequestType;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.SearchApprovalResponseType;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.verifyfellowpassenger.VerifyFellowPassengerRequestType;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.verifyfellowpassenger.VerifyFellowPassengerResponseType;
import com.ctrip.corp.foundation.common.util.BeanCopyUtils;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoRequestType;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderRequestType;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType;
import com.ctrip.corp.hotelbooking.hotelws.entity.SubmitOrderRequestType;
import com.ctrip.corp.hotelbooking.hotelws.entity.SubmitOrderResponseType;
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataRequestType;
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataResponseType;
import com.ctrip.corp.order.paymentcenter.bill.contract.QueryPaymentBillConfigRequestType;
import com.ctrip.corp.order.paymentcenter.bill.contract.QueryPaymentBillConfigResponseType;
import com.ctrip.order.reimbursement.ReimburseSettlementSyncRequestType;
import com.ctrip.soa._20183.SaveCommonDataRequestType;
import com.ctrip.soa._20183.SaveCommonDataResponseType;
import com.ctrip.soa._20184.CheckHotelAuthExtensionRequestType;
import com.ctrip.soa._20184.CheckHotelAuthExtensionResponseType;
import com.ctrip.soa._20184.QueryHotelAuthExtensionRequestType;
import com.ctrip.soa._20184.QueryHotelAuthExtensionResponseType;
import com.ctrip.soa._21210.HotelOrderRepeatOrderRequestType;
import com.ctrip.soa._21210.HotelOrderRepeatOrderResponseType;
import com.ctrip.soa._21234.CreateTripRequestType;
import com.ctrip.soa._21234.CreateTripResponseType;
import com.ctrip.soa._21234.SearchTripDetailRequestType;
import com.ctrip.soa._21234.SearchTripDetailResponseType;
import com.ctrip.soa._21685.PayConfigRequestType;
import com.ctrip.soa._21685.PayConfigResponseType;
import com.ctrip.soa._21685.TransactionPayUrlRequestType;
import com.ctrip.soa._21685.TransactionPayUrlResponseType;
import com.google.common.collect.Lists;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoRequestType;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType;
import corp.user.service.CorpAccountQueryService.GetAuthDelayRequestType;
import corp.user.service.CorpAccountQueryService.GetAuthDelayResponseType;
import corp.user.service.CorpAccountQueryService.QueryIndividualAccountRequestType;
import corp.user.service.CorpAccountQueryService.QueryIndividualAccountResponseType;
import corp.user.service.corp4jservice.GetReasoncodesRequestType;
import corp.user.service.corp4jservice.GetReasoncodesResponseType;
import ctrip.BBZ.members.bbzmbrCommonPassenger.GetCommonPassengerRequestType;
import ctrip.BBZ.members.bbzmbrCommonPassenger.GetCommonPassengerResponseType;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/20 0:11
 */
@Component
public class ProcessorOfOrderCreate
        extends AbstractProcessor<OrderCreateRequestType, OrderCreateResponseType> {
    // region Autowired
    @Autowired
    private MapperOfCheckDataRequestType mapperOfCheckDataRequestType;
    @Autowired
    private HandlerOfCheckData handlerOfCheckData;
    @Autowired
    private MapperOfQueryIndividualAccountRequestType mapperOfQueryIndividualAccountRequestType;
    @Autowired
    private HandlerOfQueryIndividualAccount handlerOfQueryIndividualAccount;
    @Autowired
    private MapperOfTmsCreateOrderVerifyRequestType mapperOfTmsCreateOrderVerifyRequestType;
    @Autowired
    private HandlerOfTmsCreateOrderVerify handlerOfTmsCreateOrderVerify;
    @Autowired
    private MapperOfFinishInfoResponse mapperOfFinishInfoResponse;
    @Autowired
    private MapperOfMiceControlResponse mapperOfMiceControlResponse;
    @Autowired
    private MapperOfCheckDataParamValidResponse mapperOfParamValidSensitiveInformation;
    @Autowired
    private HandlerOfSearchTripDetail handlerOfSearchTripDetail;
    @Autowired
    private MapperOfSearchTripDetailRequestType mapperOfSearchTripDetailRequestType;
    @Autowired
    private MapperOfQueryCheckAvailContextRequestType mapperOfQueryCheckAvailContextRequestType;
    @Autowired
    private HandlerOfQueryCheckAvailContext handlerOfQueryCheckAvailContext;
    @Autowired
    private HandlerOfGeneralSearchAccountInfo handlerOfGeneralSearchAccountInfo;
    @Autowired
    private MapperOfGeneralSearchAccountInfoRequest mapperOfAccountInfoRequest;
    @Autowired
    private HandlerOfGetCorpUserInfo handlerOfGetCorpUserInfo;
    @Autowired
    private MapperOfGetCorpUserInfoRequest mapperOfGetCorpUserInfoRequest;
    @Autowired
    private MapperOfPolicyGeneralSearchAccountInfoRequest mapperOfPolicyAccountInfoRequest;
    @Autowired
    private HandlerOfGetSubAccountConfig handlerOfGetSubAccountConfig;
    @Autowired
    private MapperOfGetSubAccountConfigRequest mapperOfGetSubAccountConfigRequest;
    @Autowired
    private MapperOfParamValidResponse mapperOfParamValidResponse;
    @Autowired
    private MapperOfApprovalFlowOutputInfoResponse mapperOfApprovalFlowOutputInfoResponse;
    @Autowired
    private MapperOfApprovalFlowComputeRequestType mapperOfApprovalFlowComputeRequestType;
    @Autowired
    private MapperOfVerifyFellowPassengerRequestType mapperOfVerifyFellowPassengerRequestType;
    @Autowired
    private MapperOfVerifyFellowResultResponse mapperOfVerifyFellowResultResponse;
    @Autowired
    private MapperOfCheckTravelPolicyRequestType mapperOfCheckTravelPolicyRequestType;
    @Autowired
    private MapperOfSearchApprovalRequest mapperOfSearchApprovalRequest;
    @Autowired
    private HandlerOfSearchApproval handlerOfSearchApproval;
    @Autowired
    private HandlerOfCheckTravelPolicy handlerOfCheckTravelPolicy;
    @Autowired
    private MapperOfRCInfoResponse mapperOfRCInfoResponse;
    @Autowired
    private MapperOfFollowApprovalResponse mapperOfFollowApprovalResponse;
    @Autowired
    private MapperOfRepeatOrderInfoResponse mapperOfRepeatOrderInfoResponse;
    @Autowired
    private MapperOfPriceChangeInfoResponse mapperOfPriceChangeInfoResponse;
    @Autowired
    private MapperOfVerifyApprovalResultResponse mapperOfVerifyApprovalResultResponse;
    @Autowired
    private MapperOfHotelOrderRepeatOrderRequestType mapperOfHotelOrderRepeatOrderRequestType;
    @Autowired
    private HandlerOfHotelOrderRepeatOrder handlerOfHotelOrderRepeatOrder;
    @Autowired
    private MapperOfQueryHotelAuthExtensionRequestType mapperOfQueryHotelAuthExtensionRequestType;
    @Autowired
    private MapperOfCheckHotelAuthExtensionRequestType mapperOfCheckHotelAuthExtensionRequestType;
    @Autowired
    private HandlerOfQueryHotelAuthExtension handlerOfQueryHotelAuthExtension;
    @Autowired
    private HandlerOfCheckHotelAuthExtension handlerOfCheckHotelAuthExtension;
    @Autowired
    private HandlerOfGetTravelPolicyContext handlerOfGetTravelPolicyContext;
    @Autowired
    private MapperOfGetTravelPolicyContextRequestType mapperOfGetTravelPolicyContextRequestType;
    @Autowired
    private MapperOfMatchApprovalFlowRequestType mapperOfMatchApprovalFlowRequestType;
    @Autowired
    private HandlerOfMatchApprovalFlow handlerOfMatchApprovalFlow;
    @Autowired
    private HandlerOfApprovalFlowCompute handlerOfApprovalFlowCompute;
    @Autowired
    private HandlerOfApprovalFlowCheck handlerOfApprovalFlowCheck;
    @Autowired
    private MapperOfApprovalFlowCheckRequestType mapperOfApprovalFlowCheckRequestType;
    @Autowired
    private MapperOfApprovalFlowCheckResponse mapperOfApprovalFlowCheckResponse;
    @Autowired
    private MapperOfCreateOrderRequestType mapperOfCreateOrderRequestType;
    @Autowired
    private MapperOfCreateTripRequestType mapperOfCreateTripRequestType;
    @Autowired
    private HandlerOfCreateTrip handlerOfCreateTrip;
    @Autowired
    private HandlerOfGetUserLiteInfo handlerOfGetUserLiteInfo;
    @Autowired
    private MapperOfGetUserLiteInfoRequestType mapperOfGetUserLiteInfoRequestType;
    @Autowired
    private MapperOfSaveCommonDataRequestType mapperOfSaveCommonDataRequestType;
    @Autowired
    private MapperOfSubmitOrderRequestType mapperOfSubmitOrderRequestType;
    @Autowired
    private MapperOfPaymentOrderCreateRequestType mapperOfPaymentOrderCreateRequestType;
    @Autowired
    private MapperOfSSOInfoQueryRequestType mapperOfSSOInfoQueryRequestType;
    @Autowired
    private HandlerOfSSOInfoQuery handlerOfSSOInfoQuery;
    @Autowired
    private HandlerOfGetAuthDelayConfig handlerOfGetAuthDelayConfig;
    @Autowired
    private MapperOfGetAuthDelayRequestType mapperOfGetAuthDelayRequestType;
    @Autowired
    private MapperOfGetCityBaseInfoRequestType mapperOfGetCityBaseInfoRequestType;
    @Autowired
    private HandlerOfGetCityBaseInfo handlerOfGetCityBaseInfo;
    @Autowired
    private HandlerOfGetReasoncodes handlerOfGetReasoncodes;
    @Autowired
    private MapperOfGetReasonCodesRequestType mapperOfGetReasonCodesRequestType;
    @Autowired
    private HandlerOfQueryHotelOrderData handlerOfQueryHotelOrderData;
    @Autowired
    private HandlerOfVerifyFellowPassenger handlerOfVerifyFellowPassenger;
    @Autowired
    private MapperOfQueryHotelOrderDataRequestType mapperOfQueryHotelOrderDataRequestType;
    @Autowired
    private QConfigOfCustomizedSharkConfig qConfigOfCustomizedSharkConfig;
    @Autowired
    private HandlerOfCreateOrder handlerOfCreateOrder;
    @Autowired
    private HandlerOfPaymentOrderCreate handlerOfPaymentOrderCreate;
    @Autowired
    private HandlerOfSubmitOrder handlerOfSubmitOrder;
    @Autowired
    private HandlerOfSaveCommonData handlerOfSaveCommonData;
    @Autowired
    private HandlerOfReimburseSettlementSync handlerOfReimburseSettlementSync;
    @Autowired
    private MapperOfReimburseSettlementSyncRequestType mapperOfReimburseSettlementSyncRequestType;
    @Autowired
    private HandlerOfPayConfig handlerOfPayConfig;
    @Autowired
    private MapperOfPayConfigRequestType mapperOfPayConfigRequestType;
    @Autowired
    private MapperOfQueryPaymentBillConfigRequestType mapperOfQueryPaymentBillConfigRequestType;
    @Autowired
    private HandlerOfQueryPaymentBillConfig handlerOfQueryPaymentBillConfig;
    @Autowired
    private QConfigOfCodeMappingConfig qConfigOfCodeMappingConfig;
    @Autowired
    private MapperOfTeamRoomConfirmInfoResponse mapperOfTeamRoomConfirmInfoResponse;
    @Autowired
    private MapperOfConfirmOrderInfoResponse mapperOfConfirmOrderInfoResponse;
    @Autowired
    private MapperOfGetPlatformRelationByUidRequestType mapperOfGetPlatformRelationByUidRequestType;
    @Autowired
    private HandlerOfGetPlatformRelationByUid handlerOfGetPlatformRelationByUid;
    @Autowired
    private HandlerOfTransactionPayUrl handlerOfTransactionPayUrl;
    @Autowired
    private MapperOfTransactionPayUrlRequestType mapperOfTransactionPayUrlRequestType;
    @Autowired
    private HandlerOfApprovalTextInfo handlerOfApprovalTextInfo;
    @Autowired
    private MapperOfApprovalTextInfoRequestType mapperOfApprovalTextInfoRequestType;
    @Autowired
    private HandlerOfOrderOperation handlerOfOrderOperation;
    @Autowired
    private MapperOfOrderOperationRequestType mapperOfOrderOperationRequestType;
    @Autowired
    private MapperOfOrderPriceChangeInfoResponse mapperOfOrderPriceChangeInfoResponse;
    @Autowired
    private HandlerOfSaveContactInvoiceDefaultInfo handlerOfSaveContactInvoiceDefaultInfo;
    @Autowired
    private MapperOfSaveContactInvoiceDefaultInfoRequestType mapperOfSaveContactInvoiceDefaultInfoRequestType;
    @Autowired
    private MapperOfGetPackageRoomListRequest mapperOfGetPackageRoomListRequest;
    @Autowired
    private HandlerOfGetPackageRoomList handlerOfGetPackageRoomList;
    @Autowired
    private MapperOfSyncTripRequestType mapperOfSyncTripRequestType;
    @Autowired
    private HandlerOfSyncTrip handlerOfSyncTrip;
    @Autowired
    private HandlerOfAddPolicyUserHabit handlerOfAddPolicyUserHabit;
    @Autowired
    private MapperOfAddPolicyUserHabitRequestType mapperOfAddPolicyUserHabitRequestType;
    @Autowired
    private MapperOfGetCorpUserInfoRequestPolicy mapperOfGetCorpUserInfoRequestPolicy;
    @Autowired
    private MapperOfGetInboundParameterRequestType mapperOfGetInboundParameterRequestType;
    @Autowired
    private HandlerOfGetInboundParameter handlerOfGetInboundParameter;
    @Autowired
    private HandlerOfGetCommonPassenger handlerOfGetCommonPassenger;
    @Autowired
    private MapperOfGetCommonPassengerRequestType mapperOfGetCommonPassengerRequestType;
    @Autowired
    private MapperOfConfirmInfoResponse mapperOfConfirmInfoResponse;
    @Autowired
    private HandlerOfQueryBizModeBindRelation handlerOfQueryBizModeBindRelation;
    @Autowired
    private MapperOfQueryBizModeBindRelationRequestType mapperOfQueryBizModeBindRelationRequestType;
    @Autowired
    private MapperOfSaveOrderCostCenterRequestType mapperOfSaveOrderCostCenterRequestType;
    @Autowired
    private HandlerOfSaveCostCenter handlerOfSaveCostCenter;
    @Autowired
    private MapperOfMatchCostCenterInfoRequestType mapperOfMatchCostCenterInfoRequestType;
    @Autowired
    private HandlerOfMatchCostCenter handlerOfMatchCostCenter;
    @Autowired
    private QconfigOfEmailInfoConfig qconfigOfEmailInfoConfig;
    @Autowired
    private MapperOfCreateOrderCheckRequestType mapperOfCreateOrderCheckRequestType;
    @Autowired
    private MapperOfCreateOrderCheckResponse mapperOfCreateOrderCheckResponse;
    @Autowired
    private HandlerOfCreateOrderCheck handlerOfCreateOrderCheck;
    @Autowired
    private HandlerOfGetCorpUserHotelVipCard handlerOfGetCorpUserHotelVipCard;
    @Autowired
    private MapperOfGetCorpUserHotelVipCardRequestType mapperOfGetCorpUserHotelVipCardRequestType;
    @Autowired
    private HandlerOfRegister handlerOfRegister;
    @Autowired
    private MapperOfRegisterRequestType mapperOfRegisterRequestType;
    @Autowired
    private QconfigOfRegisterConfig qconfigOfRegisterConfig;
    @Autowired
    private MapperOfRegisterInfoResponse mapperOfRegisterInfoResponse;
    @Autowired
    private HandlerOfRetrieveTicketsByOrderType handlerOfRetrieveTicketsByOrderType;
    @Autowired
    private MapperOfRetrieveTicketsByOrderTypeRequestType mapperOfRetrieveTicketsByOrderTypeRequestType;
    @Autowired
    private MapperOfConfirmDetailResponse mapperOfConfirmDetailResponse;
    @Autowired
    private MapperOfPersonalAccountInfoResponse mapperOfPersonalAccountInfoResponse;
    @Autowired
    private HandlerOfCalculateTravelRewards handlerOfCalculateTravelRewards;
    @Autowired
    private MapperOfCalculateTravelRewardsRequestType mapperOfCalculateTravelRewardsRequestType;
    @Autowired
    private MapperOfCheckMembershipPhoneResponse mapperOfCheckMembershipPhoneResponse;
    @Autowired
    private HandlerOfOperateCorpUserHotelVipCard handlerOfOperateCorpUserHotelVipCard;
    @Autowired
    private MapperOfOperateCorpUserHotelVipCardRequestType mapperOfOperateCorpUserHotelVipCardRequestType;
    @Autowired
    private MapperOfGeneralBatchSearchAccountInfoRequestType mapperOfGeneralBatchSearchAccountInfoRequestType;
    @Autowired
    private HandlerOfGeneralBatchSearchAccountInfo handlerOfGeneralBatchSearchAccountInfo;
    @Autowired
    private HandlerOfGetPolicyUsers handlerOfGetPolicyUsers;
    @Autowired
    private MapperOfGetPolicyUsersRequestType mapperOfGetPolicyUsersRequestType;
    @Autowired
    private HandlerOfGetUserServedCardInfo handlerOfGetUserServedCardInfo;
    @Autowired
    private MapperOfGetUserServedCardInfoRequestType mapperOfGetUserServedCardInfoRequestType;
    @Autowired
    private MapperOfPayByUserConfirmInfoResponse mapperOfPayByUserConfirmInfoResponse;
    @Autowired
    private MapperOfCostCenterCheckRequestType mapperOfCostCenterCheckRequestType;
    @Autowired
    private HandlerOfCostCenterCheck handlerOfCostCenterCheck;
    @Autowired
    private MapperOfExternalDataCheckRequestType mapperOfExternalDataCheckRequestType;
    @Autowired
    private MapperOfCustomConfigSearchRequestType mapperOfCustomConfigSearchRequestType;
    @Autowired
    private HandlerOfCustomConfigSearch handlerOfCustomConfigSearch;
    @Autowired
    private HandlerOfExternalDataCheck handlerOfExternalDataCheck;
    @Autowired
    private QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig;
    @Autowired
    private QConfigOfAccountInfoConfig qConfigOfAccountInfoConfig;
    @Autowired
    private MapperOfGetOrderFoundationDataRequestType mapperOfGetOrderFoundationDataRequestType;
    @Autowired
    private HandlerOfGetOrderFoundationData handlerOfGetOrderFoundationData;
    @Autowired
    private MapperOfApprovalFlowReuseResponse mapperOfApprovalFlowReuseResponse;
    @Autowired
    private MapperOfSearchTripBasicInfoRequestType mapperOfSearchTripBasicInfoRequestType;
    @Autowired
    private HandlerOfSearchTripBasicInfo handlerOfSearchTripBasicInfo;
    @Autowired
    private HandlerOfGetCorpHotelOrderDetail handlerOfGetCorpHotelOrderDetail;
    @Autowired
    private MapperOfOrderDetailRequestType mapperOfOrderDetailRequestType;
    @Autowired
    private MapperOfGetCorpUserInfoRequestTypeByPolicyId mapperOfGetCorpUserInfoRequestTypeByPolicyId;
    @Autowired
    private MapperOfGetCityBaseInfoRequestTypeByCityId mapperOfGetCityBaseInfoRequestTypeByCityId;
    // endregion

    // 会员卡号规则
    private static final String QCONFIG_FILE_MEMBER_BONUS_RULE = "memberBonusRule.json";
    private static final int LIMIT_SIZE = 49;

    @Override
    public OrderCreateResponseType execute(OrderCreateRequestType orderCreateRequestType) throws Exception {
        boolean useNewOrderCreate =
            BooleanUtil.parseStr(true).equalsIgnoreCase(orderCreateRequestType.getUseNewOrderCreate());

        // region 解析token
        ResourceToken resourceToken = TokenParseUtil.parseResourceToken(orderCreateRequestType.getResourceTokenInfo());
        // 分摊token解析
        AllocationResultToken allocationResultToken =
            Optional.ofNullable(orderCreateRequestType.getCostAllocationInfo())
                .map(CostAllocationInfo::getCostAllocationToken)
                .map(x -> TokenParseUtil.parseToken(x, AllocationResultToken.class)).orElse(null);
        // 二次提交token解析
        OrderCreateToken orderCreateToken =
            TokenParseUtil.parseOrderCreateToken(orderCreateRequestType.getOrderCreateToken(), useNewOrderCreate);
        Map<String, StrategyInfo> strategyInfoMap =
            StrategyOfBookingInitUtil.buildStrategyInfoMap(orderCreateRequestType.getStrategyInfos());
        // endregion

        // 腾讯模式查询
        WaitFuture<CustomConfigSearchRequestType, CustomConfigSearchResponseType>
            customConfigSearchResponseTypeWaitFuture = null;
        if (OrderCreateProcessorOfUtil.needCustomConfigSearch(orderCreateRequestType.getCorpPayInfo(),
            orderCreateRequestType.getIntegrationSoaRequestType())) {
            customConfigSearchResponseTypeWaitFuture = handlerOfCustomConfigSearch.handleAsync(
                mapperOfCustomConfigSearchRequestType.map(
                    Tuple1.of(orderCreateRequestType.getIntegrationSoaRequestType())));
        }
        // 敏感信息校验
        WaitFuture<CheckDataRequestType, CheckDataResponseType> checkDataWaitFuture =
                handlerOfCheckData.handleAsync(mapperOfCheckDataRequestType.map(Tuple1.of(orderCreateRequestType)));
        // 腾讯员工号校验
        WaitFuture<ExternalDataCheckRequestType, ExternalDataCheckResponseType>
            externalDataCheckResponseTypeWaitFuture = null;
        if (OrderCreateProcessorOfUtil.needExternalEmployeeId(
            WaitFutureUtil.safeGetFuture(customConfigSearchResponseTypeWaitFuture),
            orderCreateRequestType.getIntegrationSoaRequestType())) {
            externalDataCheckResponseTypeWaitFuture = handlerOfExternalDataCheck.handleAsync(
                mapperOfExternalDataCheckRequestType.map(Tuple1.of(orderCreateRequestType)));
        }        mapperOfParamValidSensitiveInformation.map(
                Tuple2.of(orderCreateRequestType, WaitFutureUtil.safeGetFutureWithoutError(checkDataWaitFuture)));

        // 查询账户信息
        WaitFuture<GeneralSearchAccountInfoRequestType, GeneralSearchAccountInfoResponseType>
                accountAccountInfoResponseTypeWaitFuture = handlerOfGeneralSearchAccountInfo.handleAsync(
                mapperOfAccountInfoRequest.map(Tuple1.of(orderCreateRequestType.getIntegrationSoaRequestType())));
        // 查询用户信息
        WaitFuture<GetCorpUserInfoRequestType, GetCorpUserInfoResponseType> getCorpUserInfoResponseTypeWaitFuture =
            handlerOfGetCorpUserInfo.handleAsync(
                mapperOfGetCorpUserInfoRequest.map(Tuple1.of(orderCreateRequestType.getIntegrationSoaRequestType())));
        // 查询礼品卡配置
        WaitFuture<RetrieveTicketsByOrderTypeRequestType, RetrieveTicketsByOrderTypeResponseType>
            retrieveTicketsByOrderTypeResponseTypeWaitFuture = null;
        if (OrderCreateProcessorOfUtil.needRetrieveTicketsByOrder(orderCreateRequestType.getCorpPayInfo(),
            strategyInfoMap)) {
            retrieveTicketsByOrderTypeResponseTypeWaitFuture = handlerOfRetrieveTicketsByOrderType.handleAsync(
                mapperOfRetrieveTicketsByOrderTypeRequestType.map(
                    Tuple1.of(orderCreateRequestType.getIntegrationSoaRequestType())));
        }
        // 查询政策执行人账户信息
        WaitFuture<GeneralSearchAccountInfoRequestType, GeneralSearchAccountInfoResponseType>
            policyAccountInfoResponseTypeWaitFuture = null;
        WaitFuture<GetCorpUserInfoRequestType, GetCorpUserInfoResponseType> policyCorpUserInfoResponseTypeWaitFuture = null;
        if (OrderCreateProcessorOfUtil.needPolicyUidUserInfo(orderCreateRequestType.getHotelPolicyInput())) {
            // 查询政策执行人账户信息
            policyAccountInfoResponseTypeWaitFuture = handlerOfGeneralSearchAccountInfo.handleAsync(
                mapperOfPolicyAccountInfoRequest.map(Tuple2.of(orderCreateRequestType.getIntegrationSoaRequestType(),
                    orderCreateRequestType.getHotelPolicyInput().getPolicyInput())));
            policyCorpUserInfoResponseTypeWaitFuture = handlerOfGetCorpUserInfo.handleAsync(
                mapperOfGetCorpUserInfoRequestPolicy.map(Tuple1.of(orderCreateRequestType.getHotelPolicyInput())));
        }
        // 查查询原单信息
        WaitFuture<QueryHotelOrderDataRequestType, QueryHotelOrderDataResponseType>
            queryHotelOrderDataResponseTypeWaitFuture = null;
        if (OrderCreateProcessorOfUtil.needQueryHotelOrderData(resourceToken)) {
            queryHotelOrderDataResponseTypeWaitFuture = handlerOfQueryHotelOrderData.handleAsync(
                mapperOfQueryHotelOrderDataRequestType.map(
                    Tuple2.of(resourceToken, orderCreateRequestType.getIntegrationSoaRequestType())));
        }
        // 查询城市信息
        WaitFuture<GetCityBaseInfoRequestType, GetCityBaseInfoResponseType> cityInfoResponseTypeWaitFuture =
            handlerOfGetCityBaseInfo.handleAsync(mapperOfGetCityBaseInfoRequestType.map(
                Tuple2.of(orderCreateRequestType.getIntegrationSoaRequestType(),
                    orderCreateRequestType.getCityInput())));
        // 查询子账户信息
        WaitFuture<GetSubAccountConfigRequestType, GetSubAccountConfigResponseType>
            getSubAccountConfigResponseTypeWaitFuture = handlerOfGetSubAccountConfig.handleAsync(
            mapperOfGetSubAccountConfigRequest.map(Tuple1.of(getCorpUserInfoResponseTypeWaitFuture.get())));
        // 个人账户开关
        WaitFuture<QueryIndividualAccountRequestType, QueryIndividualAccountResponseType>
                queryIndividualAccountWaitFuture = handlerOfQueryIndividualAccount.handleAsync(
                mapperOfQueryIndividualAccountRequestType.map(Tuple1.of(orderCreateRequestType.getIntegrationSoaRequestType())));
        // 查询丰享配置
        WaitFuture<PayConfigRequestType, PayConfigResponseType> payConfigResponseTypeWaitFuture =
            handlerOfPayConfig.handleAsync(
                mapperOfPayConfigRequestType.map(Tuple1.of(orderCreateRequestType.getIntegrationSoaRequestType())));
        // 延用审批账户接口
        WaitFuture<GetAuthDelayRequestType, GetAuthDelayResponseType> getAuthDelayResponseTypeWaitFuture =
            handlerOfGetAuthDelayConfig.handleAsync(
                mapperOfGetAuthDelayRequestType.map(Tuple1.of(orderCreateRequestType.getIntegrationSoaRequestType())));
        // 查询RC信息
        WaitFuture<GetReasoncodesRequestType, GetReasoncodesResponseType> getReasoncodesResponseTypeWaitFuture =
            handlerOfGetReasoncodes.handleAsync(
                mapperOfGetReasonCodesRequestType.map(Tuple1.of(orderCreateRequestType.getIntegrationSoaRequestType())));
        // 行程详情查询
        WaitFuture<SearchTripDetailRequestType, SearchTripDetailResponseType> tripDetailWaitFuture = null;
        // 行程关联单据查询
        if (OrderCreateProcessorOfUtil.needTripDetail(orderCreateRequestType.getTripInput())) {
            tripDetailWaitFuture = handlerOfSearchTripDetail.handleAsync(mapperOfSearchTripDetailRequestType.map(
                Tuple3.of(orderCreateRequestType.getIntegrationSoaRequestType(), null,
                    orderCreateRequestType.getTripInput())));
        }
        // 延用行程查询
        WaitFuture<SearchTripDetailRequestType, SearchTripDetailResponseType> tripDetailFolowWaitFuture = null;
        if (OrderCreateProcessorOfUtil.needTripDetailFollow(orderCreateToken)) {
            tripDetailFolowWaitFuture = handlerOfSearchTripDetail.handleAsync(mapperOfSearchTripDetailRequestType.map(
                Tuple3.of(orderCreateRequestType.getIntegrationSoaRequestType(),
                    orderCreateToken.getFollowApprovalResult().getTripId(), null)));
        }

        // 获取主营卡uid
        WaitFuture<QueryBizModeBindRelationRequestType, QueryBizModeBindRelationResponseType>
            queryBizModeBindRelationResponseTypeWaitFuture = null;
        if (OrderCreateProcessorOfUtil.needQueryBizModeBindRelation(
            orderCreateRequestType.getIntegrationSoaRequestType(), orderCreateRequestType.getMembershipInfo())) {
            queryBizModeBindRelationResponseTypeWaitFuture = handlerOfQueryBizModeBindRelation.handleAsync(
                mapperOfQueryBizModeBindRelationRequestType.map(
                    Tuple2.of(orderCreateRequestType.getIntegrationSoaRequestType(),
                        orderCreateRequestType.getMembershipInfo())));
        }
        // 散客uid获取,获取会员绑定
        WaitFuture<GetPlatformRelationByUidRequestType, GetPlatformRelationByUidResponseType>
            getPlatformRelationByUidResponseTypeWaitFuture = null;
        if (OrderCreateProcessorOfUtil.needGetPlatformRelationByUid(
            orderCreateRequestType.getIntegrationSoaRequestType(),
            WaitFutureUtil.safeGetFutureWithoutError(queryBizModeBindRelationResponseTypeWaitFuture))) {
            GetPlatformRelationByUidRequestType getPlatformRelationByUidRequestType =
                mapperOfGetPlatformRelationByUidRequestType.map(
                    Tuple2.of(orderCreateRequestType.getIntegrationSoaRequestType(),
                        WaitFutureUtil.safeGetFutureWithoutError(queryBizModeBindRelationResponseTypeWaitFuture)));
            getPlatformRelationByUidResponseTypeWaitFuture =
                handlerOfGetPlatformRelationByUid.handleAsync(getPlatformRelationByUidRequestType);
        }
        // 可定反查
        WaitFuture<QueryCheckAvailContextRequestType, QueryCheckAvailContextResponseType>
                queryCheckAvailContextWaitFuture = handlerOfQueryCheckAvailContext.handleAsync(
                mapperOfQueryCheckAvailContextRequestType.map(Tuple2.of(orderCreateRequestType.getIntegrationSoaRequestType()
                        , Optional.ofNullable(resourceToken.getReservationResourceToken()).map(
                        ReservationResourceToken::getWsId).orElse(null))));
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(accountAccountInfoResponseTypeWaitFuture.get())
                .policyAccountInfo(WaitFutureUtil.safeGetFuture(policyAccountInfoResponseTypeWaitFuture))
                .corpUserInfo(WaitFutureUtil.safeGetFuture(getCorpUserInfoResponseTypeWaitFuture))
                .subAccountConfig(WaitFutureUtil.safeGetFuture(getSubAccountConfigResponseTypeWaitFuture))
                .build();
        // 政策执行人鉴权
        WaitFuture<GetPolicyUsersRequestType, GetPolicyUsersResponseType> getPolicyUsersResponseTypeWaitFuture = null;
        if (OrderCreateProcessorOfUtil.needGetPolicyUsers(
            Optional.ofNullable(orderCreateRequestType.getHotelPolicyInput()).map(HotelPolicyInput::getPolicyInput)
                .orElse(null), orderCreateRequestType.getIntegrationSoaRequestType(), accountInfo)) {
            GetPolicyUsersRequestType getPolicyUsersRequestType = mapperOfGetPolicyUsersRequestType.map(Tuple3.of(
                Optional.ofNullable(orderCreateRequestType.getHotelPolicyInput()).map(HotelPolicyInput::getPolicyInput)
                    .orElse(null), orderCreateRequestType.getIntegrationSoaRequestType(), accountInfo));
            getPolicyUsersResponseTypeWaitFuture = handlerOfGetPolicyUsers.handleAsync(getPolicyUsersRequestType);
        }
        WaitFuture<GetUserServedCardInfoRequestType, GetUserServedCardInfoResponseType>
            getUserServedCardInfoResponseTypeWaitFuture = null;
        if (OrderCreateProcessorOfUtil.needGetUserServedCardInfo(
            Optional.ofNullable(orderCreateRequestType.getHotelPolicyInput()).map(HotelPolicyInput::getPolicyInput)
                .orElse(null), orderCreateRequestType.getIntegrationSoaRequestType(), accountInfo,
            orderCreateRequestType.getCorpPayInfo(), orderCreateRequestType.getCityInput())) {
            GetUserServedCardInfoRequestType getUserServedCardInfoRequestType =
                mapperOfGetUserServedCardInfoRequestType.map(Tuple5.of(
                    Optional.ofNullable(orderCreateRequestType.getHotelPolicyInput())
                        .map(HotelPolicyInput::getPolicyInput).orElse(null),
                    orderCreateRequestType.getIntegrationSoaRequestType(), accountInfo,
                    orderCreateRequestType.getCorpPayInfo(), orderCreateRequestType.getCityInput()));
            getUserServedCardInfoResponseTypeWaitFuture =
                handlerOfGetUserServedCardInfo.handleAsync(getUserServedCardInfoRequestType);
        }
        // 校验政策执行人信息
        WrapperOfPolicyUser.PolicyUserInfo policyUserInfo = new WrapperOfPolicyUser.Builder()
                .policyInput(Optional.ofNullable(orderCreateRequestType.getHotelPolicyInput()).map(HotelPolicyInput::getPolicyInput).orElse(null))
                .getUserServedCardInfoResponseType(WaitFutureUtil.safeGetFuture(getUserServedCardInfoResponseTypeWaitFuture))
                .getPolicyUsersResponseType(WaitFutureUtil.safeGetFuture(getPolicyUsersResponseTypeWaitFuture))
                .check(accountInfo, orderCreateRequestType.getCorpPayInfo(), CityInfoUtil.oversea(
                        Optional.ofNullable(orderCreateRequestType.getCityInput()).map(CityInput::getCityId).orElse(null)),
                        orderCreateRequestType.getIntegrationSoaRequestType(), orderCreateRequestType.getHotelBookPassengerInputs())
                .build();
        // 查询所有入住人的账户信息
        WaitFuture<GeneralBatchSearchAccountInfoRequestType, GeneralBatchSearchAccountInfoResponseType>
            generalBatchSearchAccountInfoResponseTypeWaitFuture = null;
        if (OrderCreateProcessorOfUtil.requirePsgAccountInfo(accountInfo, orderCreateRequestType)) {
            handlerOfGeneralBatchSearchAccountInfo.handleAsync(mapperOfGeneralBatchSearchAccountInfoRequestType.map(
                Tuple2.of(orderCreateRequestType.getHotelBookPassengerInputs(),
                    orderCreateRequestType.getIntegrationSoaRequestType())));
        }

        // 成本中心配置收集
        boolean saveCommonDataCostCenter =
            QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.SAVE_COMMON_DATA_COST_CENTER,
                orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId());
        WaitFuture<MatchCostCenterRequestType, MatchCostCenterResponseType> matchCostCenterResponseTypeWaitFuture =
            null;
        if (OrderCreateProcessorOfUtil.requireMatchCostCenter(orderCreateRequestType, accountInfo,
            saveCommonDataCostCenter, resourceToken, strategyInfoMap)) {
            matchCostCenterResponseTypeWaitFuture = handlerOfMatchCostCenter.handleAsync(
                mapperOfMatchCostCenterInfoRequestType.map(Tuple1.of(orderCreateRequestType)));
        }

        // amadesu酒店集团会员信息校验，读取qconfig配置规则
        List<MemberBonusRuleEntry> memberBonusRuleEntries = QConfigUtil.getFile(QCONFIG_FILE_MEMBER_BONUS_RULE)
                .asJsonArray(MemberBonusRuleEntry.class, null);

        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo =
            WrapperOfCheckAvail.checkAvailContextBuilder()
                .setQueryCheckAvailContextResponseType(WaitFutureUtil.safeGetFuture(queryCheckAvailContextWaitFuture))
                .setResourceToken(resourceToken)
                .check(qConfigOfCodeMappingConfig)
                .build().getCheckAvailContextInfo();

        // 心程贝计算
        WaitFuture<CalculateTravelRewardsRequestType, CalculateTravelRewardsResponseType> calculateTravelRewardsResponseTypeWaitFuture =
                handlerOfCalculateTravelRewards.handleAsync(mapperOfCalculateTravelRewardsRequestType.map(
                        Tuple4.of(orderCreateRequestType, checkAvailInfo, resourceToken, accountInfo)));

        // 闪住校验
        if (OrderCreateProcessorOfUtil.requireCreateOrderCheck(orderCreateRequestType)) {
            WaitFuture<CreateOrderCheckRequestType, CreateOrderCheckResponseType> createOrderCheckWaitFuture =
                handlerOfCreateOrderCheck.handleAsync(
                    mapperOfCreateOrderCheckRequestType.map(Tuple1.of(orderCreateRequestType)));
            mapperOfCreateOrderCheckResponse.map(
                Tuple2.of(WaitFutureUtil.safeGetFuture(createOrderCheckWaitFuture), orderCreateRequestType));
        }
        // 依赖外部接口/token/qconfig 等 参数合理性校验
        WrapperOfParamValid wrapperOfParamValid = WrapperOfParamValid.builder()
            .withOrderCreateRequestType(orderCreateRequestType)
            .withResourceToken(resourceToken)
            .withAllocationResultToken(allocationResultToken)
            .withOrderCreateToken(orderCreateToken)
            .withAccountInfo(accountInfo)
            .withQueryCheckAvailContextResponseType(WaitFutureUtil.safeGetFuture(queryCheckAvailContextWaitFuture))
            .withSearchTripDetailResponseType(WaitFutureUtil.safeGetFuture(tripDetailWaitFuture))
            .withQueryIndividualAccountResponseType(WaitFutureUtil.safeGetFuture(queryIndividualAccountWaitFuture))
            .withBaseCheckAvailInfo(checkAvailInfo)
            .withMemberBonusRuleEntries(memberBonusRuleEntries)
            .withGeneralBatchSearchAccountInfoResponseType(WaitFutureUtil.safeGetFuture(generalBatchSearchAccountInfoResponseTypeWaitFuture))
            .withQconfigOfCertificateInitConfig(qconfigOfCertificateInitConfig)
            .withExternalDataCheckResponseType(WaitFutureUtil.safeGetFutureWithoutError(externalDataCheckResponseTypeWaitFuture))
            .withCustomConfigSearchResponseType(WaitFutureUtil.safeGetFutureWithoutError(customConfigSearchResponseTypeWaitFuture))
            .withStrategyInfoMap(strategyInfoMap)
            .build();
        mapperOfParamValidResponse.map(Tuple1.of(wrapperOfParamValid));
        WaitFuture<GetPackageRoomListRequestType, GetPackageRoomListResponseType> getPackageRoomListWaitFuture = null;
        if (OrderCreateProcessorOfUtil.needGetPackageRoomList(checkAvailInfo)) {
            getPackageRoomListWaitFuture =
                handlerOfGetPackageRoomList.handleAsync(mapperOfGetPackageRoomListRequest.map(Tuple3.of(orderCreateRequestType, resourceToken, checkAvailInfo)));
        }

        // 成本中心校验
        if (OrderCreateProcessorOfUtil.needCheckCostCenterNew(orderCreateRequestType, resourceToken, accountInfo,
            strategyInfoMap)) {
            WaitFuture<CostCenterCheckRequestType, CostCenterCheckResponseType> costCenterCheckResponseTypeWaitFuture =
                handlerOfCostCenterCheck.handleAsync(mapperOfCostCenterCheckRequestType.map(
                    Tuple6.of(orderCreateRequestType, resourceToken, checkAvailInfo, qconfigOfCertificateInitConfig, accountInfo, strategyInfoMap)));
            OrderCreateProcessorOfUtil.checkCostCenterCheckRequestType(costCenterCheckResponseTypeWaitFuture.get());
        }


        // offline绑定信息
        WaitFuture<GetInboundParameterRequestType, GetInboundParameterResponseType> getInboundParameterWaitFuture =
            null;
        if (OrderCreateProcessorOfUtil.needGetInboundParameter(orderCreateRequestType)) {
            getInboundParameterWaitFuture = handlerOfGetInboundParameter.handleAsync(
                mapperOfGetInboundParameterRequestType.map(Tuple1.of(orderCreateRequestType)));
        }


        TmsCreateOrderVerifyResponseType tmsCreateOrderVerifyResponseType = null;
        if (OrderCreateProcessorOfUtil.needTmsCreateOrderVerify(orderCreateRequestType)) {
            // mice可订请求
            TmsCreateOrderVerifyRequestType tmsCreateOrderVerifyRequestType = mapperOfTmsCreateOrderVerifyRequestType.map(Tuple2.of(orderCreateRequestType, resourceToken));
            // 调mice可订接口
            tmsCreateOrderVerifyResponseType = handlerOfTmsCreateOrderVerify.handleAsync(tmsCreateOrderVerifyRequestType).get();
            // mice校验
            mapperOfMiceControlResponse.map(Tuple1.of(tmsCreateOrderVerifyResponseType));
        }


        // 城市响应信息获取
        WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo = WrapperOfCityBaseInfo.builder()
            .getCityBaseInfoResponseType(WaitFutureUtil.safeGetFutureWithoutError(cityInfoResponseTypeWaitFuture))
            .cityInput(orderCreateRequestType.getCityInput()).build();

        // 手机号积分-会员手机号校验
        Tuple2<Boolean, OrderCreateResponseType> checkMembershipPhoneResponseInfo =
                mapperOfCheckMembershipPhoneResponse.map(Tuple3.of(orderCreateRequestType, checkAvailInfo, orderCreateToken));
        if (OrderCreateProcessorOfUtil.endOfProcess(checkMembershipPhoneResponseInfo)) {
            return OrderCreateProcessorOfUtil.getResponse(checkMembershipPhoneResponseInfo, cityBaseInfo);
        }

        // 心程贝提示
        Tuple2<Boolean, OrderCreateResponseType> personalAccountResponse = mapperOfPersonalAccountInfoResponse.map(Tuple4.of(
                orderCreateRequestType,
                WaitFutureUtil.safeGetFutureWithoutError(calculateTravelRewardsResponseTypeWaitFuture),
                WaitFutureUtil.safeGetFutureWithoutError(queryIndividualAccountWaitFuture), orderCreateToken));
        if (OrderCreateProcessorOfUtil.endOfProcess(personalAccountResponse)) {
            return OrderCreateProcessorOfUtil.getResponse(personalAccountResponse, cityBaseInfo);
        }

        // 蓝色空间offline客户自行支付确认框
        Tuple2<Boolean, OrderCreateResponseType> payByUserConfirmInfoResponse =
            mapperOfPayByUserConfirmInfoResponse.map(
                Tuple4.of(orderCreateToken, orderCreateRequestType, checkAvailInfo, accountInfo));
        if (OrderCreateProcessorOfUtil.endOfProcess(payByUserConfirmInfoResponse)) {
            return OrderCreateProcessorOfUtil.getResponse(payByUserConfirmInfoResponse, cityBaseInfo);
        }

        // 会员卡号
        WaitFuture<GetCorpUserHotelVipCardRequestType, GetCorpUserHotelVipCardResponseType> getCorpUserHotelVipCardResponseTypeWaitFuture = null;
        GetCorpUserHotelVipCardRequestType getCorpUserHotelVipCardRequestType = mapperOfGetCorpUserHotelVipCardRequestType.map(
                Tuple5.of(orderCreateRequestType.getIntegrationSoaRequestType(), checkAvailInfo,
                        WaitFutureUtil.safeGetFutureWithoutError(queryBizModeBindRelationResponseTypeWaitFuture), null, null));
        if (getCorpUserHotelVipCardRequestType != null) {
            getCorpUserHotelVipCardResponseTypeWaitFuture = handlerOfGetCorpUserHotelVipCard.handleAsync(getCorpUserHotelVipCardRequestType);
        }
        // 会员注册
        WaitFuture<RegisterRequestType, RegisterResponseType> registerResponseTypeWaitFuture = null;
        if (OrderCreateProcessorOfUtil.requireRegister(orderCreateToken, orderCreateRequestType.getRegisterInputInfo(),
                WaitFutureUtil.safeGetFutureWithoutError(getCorpUserHotelVipCardResponseTypeWaitFuture), orderCreateRequestType.getIntegrationSoaRequestType(),
                checkAvailInfo, WaitFutureUtil.safeGetFutureWithoutError(queryBizModeBindRelationResponseTypeWaitFuture))) {
            RegisterRequestType registerRequestType = mapperOfRegisterRequestType.map(
                    Tuple4.of(orderCreateRequestType.getIntegrationSoaRequestType(), orderCreateRequestType.getRegisterInputInfo(), checkAvailInfo, qconfigOfRegisterConfig));
            if (registerRequestType != null) {
                registerResponseTypeWaitFuture = handlerOfRegister.handleAsync(registerRequestType);
            }
            Tuple2<Boolean, OrderCreateResponseType> registerResponseInfo = mapperOfRegisterInfoResponse.map(
                    Tuple8.of(orderCreateRequestType.getRegisterInputInfo(), WaitFutureUtil.safeGetFutureWithoutError(getCorpUserHotelVipCardResponseTypeWaitFuture),
                            WaitFutureUtil.safeGetFuture(registerResponseTypeWaitFuture), qconfigOfRegisterConfig,
                            orderCreateToken, orderCreateRequestType.getIntegrationSoaRequestType(),
                            checkAvailInfo, WaitFutureUtil.safeGetFutureWithoutError(queryBizModeBindRelationResponseTypeWaitFuture)));
            if (OrderCreateProcessorOfUtil.endOfProcess(registerResponseInfo)) {
                return OrderCreateProcessorOfUtil.getResponse(registerResponseInfo, cityBaseInfo);
            }
        }

        // 非员工信息校验
        if (OrderCreateProcessorOfUtil.needGetCommonPassenger(orderCreateRequestType, orderCreateToken)) {
            List<String> infoids = orderCreateRequestType.getHotelBookPassengerInputs().stream()
                .map(HotelBookPassengerInput::getHotelPassengerInput).map(HotelPassengerInput::getInfoId)
                .collect(Collectors.toList());
            List<GetCommonPassengerResponseType> getCommonPassengerResponseTypes = new ArrayList<>();
            if (infoids.size() <= LIMIT_SIZE) {
                GetCommonPassengerRequestType getCommonPassengerRequestType =
                    mapperOfGetCommonPassengerRequestType.map(Tuple2.of(orderCreateRequestType, infoids));
                getCommonPassengerResponseTypes.add(
                    handlerOfGetCommonPassenger.handleAsync(getCommonPassengerRequestType).getWithoutError());
            }
            if (infoids.size() > LIMIT_SIZE) {
                List<List<String>> partition = Lists.partition(infoids, LIMIT_SIZE);
                for (List<String> infoIds : partition) {
                    GetCommonPassengerRequestType getCommonPassengerRequestType =
                        mapperOfGetCommonPassengerRequestType.map(Tuple2.of(orderCreateRequestType, infoIds));
                    getCommonPassengerResponseTypes.add(
                        handlerOfGetCommonPassenger.handleAsync(getCommonPassengerRequestType).getWithoutError());
                }
            }
            Tuple2<Boolean, OrderCreateResponseType> confirmInfoResponse = mapperOfConfirmInfoResponse.map(
                Tuple6.of(getCommonPassengerResponseTypes, orderCreateToken, orderCreateRequestType, checkAvailInfo,
                    qconfigOfCertificateInitConfig, strategyInfoMap));
            if (OrderCreateProcessorOfUtil.endOfProcess(confirmInfoResponse)) {
                return OrderCreateProcessorOfUtil.getResponse(confirmInfoResponse, cityBaseInfo);
            }
        }

        // 原单变价
        if (OrderCreateProcessorOfUtil.requireOrderPriceChange(orderCreateRequestType, orderCreateToken,
            strategyInfoMap)) {
            Tuple2<Boolean, OrderCreateResponseType> orderPriceChangeInfoResponse =
                mapperOfOrderPriceChangeInfoResponse.map(
                    Tuple5.of(orderCreateToken, orderCreateRequestType, checkAvailInfo,
                        WaitFutureUtil.safeGetFuture(queryHotelOrderDataResponseTypeWaitFuture), resourceToken));
            if (OrderCreateProcessorOfUtil.endOfProcess(orderPriceChangeInfoResponse)) {
                return OrderCreateProcessorOfUtil.getResponse(orderPriceChangeInfoResponse, cityBaseInfo);
            }
        }

        // 单点登录信息查询
        WaitFuture<SSOInfoQueryRequestType, SSOInfoQueryResponseType> ssoInfoQueryResponseTypeWaitFuture = null;
        if (OrderCreateProcessorOfUtil.requireSSOInfoQuery(orderCreateRequestType, accountInfo, orderCreateToken)) {
            SSOInfoQueryRequestType ssoInfoQueryRequestType = mapperOfSSOInfoQueryRequestType.map(
                Tuple2.of(orderCreateRequestType.getIntegrationSoaRequestType(), orderCreateRequestType.getSsoInput()));
            ssoInfoQueryResponseTypeWaitFuture = handlerOfSSOInfoQuery.handleAsync(ssoInfoQueryRequestType);
        }


        // 同行程管控
        if (OrderCreateProcessorOfUtil.requireVerifyFellowControl(orderCreateRequestType, accountInfo, orderCreateToken)) {
            VerifyFellowPassengerRequestType verifyFellowPassengerRequestType =
                mapperOfVerifyFellowPassengerRequestType.map(
                    Tuple5.of(orderCreateRequestType, accountInfo, checkAvailInfo, qconfigOfCertificateInitConfig, strategyInfoMap));
            WaitFuture<VerifyFellowPassengerRequestType, VerifyFellowPassengerResponseType>
                verifyFellowPassengerResponseTypeWaitFuture =
                handlerOfVerifyFellowPassenger.handleAsync(verifyFellowPassengerRequestType);
            Tuple2<Boolean, OrderCreateResponseType> verifyApprovalResultResponse =
                mapperOfVerifyFellowResultResponse.map(
                    Tuple5.of(WaitFutureUtil.safeGetFuture(verifyFellowPassengerResponseTypeWaitFuture),
                        orderCreateRequestType, checkAvailInfo, qconfigOfCertificateInitConfig, strategyInfoMap));
            if (OrderCreateProcessorOfUtil.endOfProcess(verifyApprovalResultResponse)) {
                return OrderCreateProcessorOfUtil.getResponse(verifyApprovalResultResponse, cityBaseInfo);
            }
        }
        WaitFuture<GetTravelPolicyContextRequestType, GetTravelPolicyContextResponseType> getTravelPolicyContextWaitFuture = null;
        if (OrderCreateProcessorOfUtil.requireGetTravelPolicyContext(orderCreateRequestType, accountInfo, orderCreateToken)) {
            GetTravelPolicyContextRequestType getTravelPolicyContextRequestType = mapperOfGetTravelPolicyContextRequestType.map(
                    Tuple2.of(orderCreateRequestType.getIntegrationSoaRequestType(), resourceToken.getReservationResourceToken().getPolicyToken()));
            getTravelPolicyContextWaitFuture = handlerOfGetTravelPolicyContext.handleAsync(getTravelPolicyContextRequestType);
        }

        // 查询提前审批 紧急预订
        WaitFuture<ApprovalTextInfoRequestType, ApprovalTextInfoResponseType> approvalTextInfoResponseTypeWaitFuture =
            handlerOfApprovalTextInfo.handleAsync(mapperOfApprovalTextInfoRequestType.map(
                Tuple2.of(orderCreateRequestType.getIntegrationSoaRequestType(),
                        strategyInfoMap)));

        // 查询CheckTravelPolicy 和 审批单信息
        WaitFuture<CheckTravelPolicyRequestType, CheckTravelPolicyResponseType> checkTravelPolicyResponseTypeWaitFuture = null;
        if (OrderCreateProcessorOfUtil.requireCheckTravelPolicyControl(orderCreateRequestType)) {
            WrapperOfCheckTravelPolicy checkTravelPolicy = WrapperOfCheckTravelPolicy.builder()
                .setResourceToken(resourceToken)
                .setCheckAvailInfo(checkAvailInfo)
                .setAccountInfo(accountInfo)
                .setHotelPolicyInput(orderCreateRequestType.getHotelPolicyInput())
                .setIntegrationSoaRequestType(orderCreateRequestType.getIntegrationSoaRequestType())
                .setApprovalInput(orderCreateRequestType.getApprovalInput())
                .setAddPriceInput(orderCreateRequestType.getAddPriceInput())
                .setHotelBookInput(orderCreateRequestType.getHotelBookInput())
                .setScene(MapperOfCheckTravelPolicyRequestType.SCENE_PAY_TYPE_FROM_INPUT)
                .setHotelInsuranceInput(orderCreateRequestType.getHotelInsuranceInput())
                .setHotelPayTypeInputs(orderCreateRequestType.getHotelPayTypeInput())
                .setGetSupportedPaymentMethodResponseType(null)
                .setFlashStayInput(orderCreateRequestType.getFlashStayInput())
                .setCalculateServiceChargeV2ResponseType(null)
                .setRcInfos(orderCreateRequestType.getRcInfos())
                .setAllocationResultToken(allocationResultToken)
                .setHotelBookPassengerInputs(orderCreateRequestType.getHotelBookPassengerInputs())
                .setCityInput(orderCreateRequestType.getCityInput())
                .setCorpPayInfo(orderCreateRequestType.getCorpPayInfo())
                .setCostCenterStr(
                    Optional.ofNullable(orderCreateRequestType.getCostCenterInfo())
                        .map(CostCenterInfo::getCostCenterJsonString).orElse(null))
                .setRoomPayType(HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput()))
                .setServicePayType(HotelPayTypeUtil.getServicePayType(orderCreateRequestType.getHotelPayTypeInput(), resourceToken))
                .setStrategyInfoMap(strategyInfoMap)
                .setQconfigOfCertificateInitConfig(qconfigOfCertificateInitConfig)
                .setCostCenterInfo(orderCreateRequestType.getCostCenterInfoNew())
                .build();
            CheckTravelPolicyRequestType checkTravelPolicyRequestType =
                mapperOfCheckTravelPolicyRequestType.map(Tuple1.of(checkTravelPolicy));
            checkTravelPolicyResponseTypeWaitFuture = handlerOfCheckTravelPolicy.handleAsync(checkTravelPolicyRequestType);
        }
        WaitFuture<SearchApprovalRequestType, SearchApprovalResponseType> searchApprovalResponseTypeWaitFuture = null;
        if (OrderCreateProcessorOfUtil.requireSearchApproval(orderCreateRequestType, accountInfo, orderCreateToken)) {
            SearchApprovalRequest adapterOfSearchApprovalRequest = SearchApprovalRequest.builder()
                    .approvalInput(orderCreateRequestType.getApprovalInput())
                    .cityInput(orderCreateRequestType.getCityInput())
                    .integrationSoaRequestType(orderCreateRequestType.getIntegrationSoaRequestType())
                    .returnRegionControlCityInfo(true)
                    .compatibleCitySplit(true)
                    .approvalInputs(
                    OrderCreateProcessorOfUtil.buildApprovalInputs(orderCreateRequestType,
                        WaitFutureUtil.safeGetFuture(checkTravelPolicyResponseTypeWaitFuture)))
                    .searchRange(OrderCreateProcessorOfUtil.buildSearchRange(
                    WaitFutureUtil.safeGetFuture(checkTravelPolicyResponseTypeWaitFuture)))
                    .build();
            SearchApprovalRequestType searchApprovalRequestType = mapperOfSearchApprovalRequest.map(
                    Tuple1.of(adapterOfSearchApprovalRequest));
            searchApprovalResponseTypeWaitFuture = handlerOfSearchApproval.handleAsync(searchApprovalRequestType);
        }
        if (OrderCreateProcessorOfUtil.requireVerifyApproval(orderCreateRequestType, accountInfo, orderCreateToken)) {
            // 审批单管控
            WrapperOfVerifyApproval wrapperOfVerifyApproval = WrapperOfVerifyApproval.builder()
                    .setOrderCreateRequestType(orderCreateRequestType)
                    .setAccountInfo(accountInfo)
                    .setCityBaseInfo(cityBaseInfo)
                    .setSearchApprovalResponseType(WaitFutureUtil.safeGetFuture(searchApprovalResponseTypeWaitFuture))
                    .setCheckTravelPolicyResponseType(WaitFutureUtil.safeGetFuture(checkTravelPolicyResponseTypeWaitFuture))
                    .setOrderCreateToken(orderCreateToken)
                    .setCheckAvailContextInfo(checkAvailInfo)
                    .setStrategyInfoMap(strategyInfoMap)
                    .build();
            Tuple2<Boolean, OrderCreateResponseType> verifyApprovalResultResponse = mapperOfVerifyApprovalResultResponse.map(
                    Tuple1.of(wrapperOfVerifyApproval));
            if (OrderCreateProcessorOfUtil.endOfProcess(verifyApprovalResultResponse)) {
                return OrderCreateProcessorOfUtil.getResponse(verifyApprovalResultResponse, cityBaseInfo);
            }
        }

        // 重复订单管控
        WaitFuture<HotelOrderRepeatOrderRequestType, HotelOrderRepeatOrderResponseType> hotelOrderRepeatOrderResponseTypeWaitFuture = null;
        if (OrderCreateProcessorOfUtil.requireRepeatOrderControl(accountInfo, orderCreateToken)) {
            HotelOrderRepeatOrderRequestType hotelOrderRepeatOrderRequestType =
                mapperOfHotelOrderRepeatOrderRequestType.map(
                    Tuple6.of(accountInfo, orderCreateRequestType, resourceToken,
                        checkAvailInfo, qconfigOfCertificateInitConfig, strategyInfoMap));
            hotelOrderRepeatOrderResponseTypeWaitFuture =
                    handlerOfHotelOrderRepeatOrder.handleAsync(hotelOrderRepeatOrderRequestType);
        }
        Tuple2<Boolean, OrderCreateResponseType> repeatOrderResponse = mapperOfRepeatOrderInfoResponse.map(
            Tuple7.of(WaitFutureUtil.safeGetFuture(hotelOrderRepeatOrderResponseTypeWaitFuture),
                WaitFutureUtil.safeGetFuture(getReasoncodesResponseTypeWaitFuture), accountInfo,
                orderCreateRequestType.getCorpPayInfo(), orderCreateRequestType.getIntegrationSoaRequestType(),
                orderCreateRequestType.getHotelPayTypeInput(), resourceToken));
        // RC及重复订单管控
        if (OrderCreateProcessorOfUtil.requireRCorRepeatOrder(orderCreateRequestType, accountInfo, orderCreateToken)) {
            WrapperOfOrderRcInfo wrapperOfOrderRcInfo = WrapperOfOrderRcInfo.builder()
                .setCheckTravelPolicyResponseType(WaitFutureUtil.safeGetFuture(checkTravelPolicyResponseTypeWaitFuture))
                .setRepeatOrderInfo(Optional.ofNullable(repeatOrderResponse.getT2()).map(OrderCreateResponseType::getRepeatOrderInfo)
                    .orElse(null))
                .setOrderCreateRequestType(orderCreateRequestType)
                .setGetReasoncodesResponseType(WaitFutureUtil.safeGetFuture(getReasoncodesResponseTypeWaitFuture))
                .setCustomizedSharkConfigList(qConfigOfCustomizedSharkConfig.getCustomizedSharkConfigs())
                .setGetTravelPolicyContextResponseType(WaitFutureUtil.safeGetFuture(getTravelPolicyContextWaitFuture))
                .setAccountInfo(accountInfo)
                .setOrderCreateToken(orderCreateToken)
                .setResourceToken(resourceToken)
                .build();
            Tuple2<Boolean, OrderCreateResponseType> rcInfoResponse =
                mapperOfRCInfoResponse.map(Tuple1.of(wrapperOfOrderRcInfo));
            if (OrderCreateProcessorOfUtil.endOfProcess(rcInfoResponse)) {
                return OrderCreateProcessorOfUtil.getResponse(rcInfoResponse, cityBaseInfo);
            }
        }

        // 灰度审批沿用新版流程
        boolean approvalFlowReuseNew =
            OrderCreateProcessorOfUtil.approvalFlowReuseNew(orderCreateRequestType, strategyInfoMap, orderCreateToken);
        if (approvalFlowReuseNew) {
            WaitFuture<GetOrderFoundationDataRequestType, GetOrderFoundationDataResponseType>
                getOrderFoundationDataResponseTypeWaitFuture = null;
            if (OrderCreateProcessorOfUtil.needGetOrderFoundationData(orderCreateRequestType)) {
                getOrderFoundationDataResponseTypeWaitFuture = handlerOfGetOrderFoundationData.handleAsync(
                    mapperOfGetOrderFoundationDataRequestType.map(Tuple1.of(orderCreateRequestType)));
            }
            GetOrderFoundationDataResponseType getOrderFoundationDataResponseType =
                WaitFutureUtil.safeGetFuture(getOrderFoundationDataResponseTypeWaitFuture);
            // 沿用审批校验
            WaitFuture<CheckHotelAuthExtensionRequestType, CheckHotelAuthExtensionResponseType>
                checkHotelAuthExtensionResponseTypeWaitFuture = null;
            if (OrderCreateProcessorOfUtil.needCheckHotelAuthExtension(orderCreateRequestType, strategyInfoMap,
                getOrderFoundationDataResponseType, orderCreateToken)) {
                WrapperOfCheckHotelAuthExtension wrapperOfCheckHotelAuthExtension =
                    WrapperOfCheckHotelAuthExtension.builder().withAccountInfo(accountInfo)
                        .withGetTravelPolicyContextResponseType(
                            WaitFutureUtil.safeGetFuture(getTravelPolicyContextWaitFuture))
                        .withCheckTravelPolicyResponseType(
                            WaitFutureUtil.safeGetFuture(checkTravelPolicyResponseTypeWaitFuture))
                        .withQueryCheckAvailContextResponseType(
                            WaitFutureUtil.safeGetFuture(queryCheckAvailContextWaitFuture))
                        .withOrderCreateRequestType(orderCreateRequestType).withResourceToken(resourceToken)
                        .withCheckAvailInfo(checkAvailInfo)
                        .withQconfigOfCertificateInitConfig(qconfigOfCertificateInitConfig)
                        .withGetOrderFoundationDataResponseType(getOrderFoundationDataResponseType)
                        .withStrategyInfoMap(strategyInfoMap).withOrderCreateToken(orderCreateToken).build();
                CheckHotelAuthExtensionRequestType checkHotelAuthExtensionRequestType =
                    mapperOfCheckHotelAuthExtensionRequestType.map(Tuple1.of(wrapperOfCheckHotelAuthExtension));
                checkHotelAuthExtensionResponseTypeWaitFuture =
                    handlerOfCheckHotelAuthExtension.handleAsync(checkHotelAuthExtensionRequestType);
            }
            // 审批沿用推荐
            WaitFuture<QueryHotelAuthExtensionRequestType, QueryHotelAuthExtensionResponseType>
                queryHotelAuthExtensionResponseTypeWaitFuture = null;
            if (OrderCreateProcessorOfUtil.needQueryHotelAuthExtension(orderCreateRequestType,
                WaitFutureUtil.safeGetFuture(getAuthDelayResponseTypeWaitFuture), orderCreateToken, strategyInfoMap)) {
                WrapperOfQueryHotelAuthExtension wrapperOfQueryHotelAuthExtension =
                    WrapperOfQueryHotelAuthExtension.builder().withAccountInfo(accountInfo)
                        .withGetTravelPolicyContextResponseType(
                            WaitFutureUtil.safeGetFuture(getTravelPolicyContextWaitFuture))
                        .withCheckTravelPolicyResponseType(
                            WaitFutureUtil.safeGetFuture(checkTravelPolicyResponseTypeWaitFuture))
                        .withQueryCheckAvailContextResponseType(
                            WaitFutureUtil.safeGetFuture(queryCheckAvailContextWaitFuture))
                        .withOrderCreateRequestType(orderCreateRequestType).withResourceToken(resourceToken)
                        .withCheckAvailInfo(checkAvailInfo)
                        .withQconfigOfCertificateInitConfig(qconfigOfCertificateInitConfig)
                        .withStrategyInfoMap(strategyInfoMap).build();
                QueryHotelAuthExtensionRequestType queryHotelAuthExtensionRequestType =
                    mapperOfQueryHotelAuthExtensionRequestType.map(Tuple1.of(wrapperOfQueryHotelAuthExtension));
                queryHotelAuthExtensionResponseTypeWaitFuture =
                    handlerOfQueryHotelAuthExtension.handleAsync(queryHotelAuthExtensionRequestType);
            }
            // 沿用单行程信息
            QueryHotelAuthExtensionResponseType queryHotelAuthExtensionResponseType =
                WaitFutureUtil.safeGetFutureWithoutError(queryHotelAuthExtensionResponseTypeWaitFuture);
            CheckHotelAuthExtensionResponseType checkHotelAuthExtensionResponseType =
                WaitFutureUtil.safeGetFuture(checkHotelAuthExtensionResponseTypeWaitFuture);
            WaitFuture<SearchTripBasicInfoRequestType, SearchTripBasicInfoResponseType>
                searchTripBasicInfoResponseTypeWaitFutureOfApprovalFlowReuse = null;
            if (OrderCreateProcessorOfUtil.needSearchTripBasicInfoOfApprovalFlowReuse(
                checkHotelAuthExtensionResponseType, queryHotelAuthExtensionResponseType)) {
                SearchTripBasicInfoRequestType searchTripBasicInfoRequestType =
                    mapperOfSearchTripBasicInfoRequestType.map(Tuple2.of(
                        OrderCreateProcessorOfUtil.searchTripIdOfApprovalFlowReuse(checkHotelAuthExtensionResponseType,
                            queryHotelAuthExtensionResponseType),
                        orderCreateRequestType.getIntegrationSoaRequestType()));
                searchTripBasicInfoResponseTypeWaitFutureOfApprovalFlowReuse =
                    handlerOfSearchTripBasicInfo.handleAsync(searchTripBasicInfoRequestType);
            }
            // 沿用单订单信息
            WaitFuture<OrderDetailRequestType, OrderDetailResponseType>
                orderDetailResponseTypeWaitFutureOfApprovalFlowReuse = null;
            if (OrderCreateProcessorOfUtil.needOrderDetailOfApprovalFlowReuse(orderCreateRequestType, strategyInfoMap,
                getOrderFoundationDataResponseType, queryHotelAuthExtensionResponseType)) {
                OrderDetailRequestType orderDetailRequestType = mapperOfOrderDetailRequestType.map(Tuple2.of(
                    OrderCreateProcessorOfUtil.orderIdOfApprovalFlowReuse(orderCreateRequestType, strategyInfoMap,
                        getOrderFoundationDataResponseType, queryHotelAuthExtensionResponseType),
                    orderCreateRequestType.getIntegrationSoaRequestType()));
                orderDetailResponseTypeWaitFutureOfApprovalFlowReuse =
                    handlerOfGetCorpHotelOrderDetail.handleAsync(orderDetailRequestType);
            }
            OrderDetailInfoType orderDetailInfoType =
                OrderCreateProcessorOfUtil.buildOrderDetailInfoType(orderCreateRequestType, strategyInfoMap,
                    getOrderFoundationDataResponseType, checkHotelAuthExtensionResponseType,
                    WaitFutureUtil.safeGetFuture(orderDetailResponseTypeWaitFutureOfApprovalFlowReuse),
                    queryHotelAuthExtensionResponseType);
            // 沿用单政策执行人信息
            WaitFuture<GetCorpUserInfoRequestType, GetCorpUserInfoResponseType>
                policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse = null;
            if (OrderCreateProcessorOfUtil.needPolicyGetCorpUserInfoResponseTypeOfApprovalFlowReuse(
                orderDetailInfoType)) {
                policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse = handlerOfGetCorpUserInfo.handleAsync(
                    mapperOfGetCorpUserInfoRequestTypeByPolicyId.map(
                        Tuple1.of(orderDetailInfoType.getOrderBasicInfo().getPolicyUid())));
            }
            // 沿用单城市信息
            WaitFuture<GetCityBaseInfoRequestType, GetCityBaseInfoResponseType>
                getCityBaseInfoResponseTypeOfApprovalFlowReuse = null;
            if (OrderCreateProcessorOfUtil.needGetCityBaseInfoResponseTypeOfApprovalFlowReuse(orderDetailInfoType)) {
                CityInput cityInputOfApprovalFlowReuse = new CityInput();
                getCityBaseInfoResponseTypeOfApprovalFlowReuse = handlerOfGetCityBaseInfo.handleAsync(
                    mapperOfGetCityBaseInfoRequestTypeByCityId.map(
                        Tuple2.of(orderCreateRequestType.getIntegrationSoaRequestType(),
                            orderDetailInfoType.getHotelInfo().getHotelAreaInfo().getCityId())));
            }
            WrapperOfApprovalFlowReuse wrapperOfApprovalFlowReuse = WrapperOfApprovalFlowReuse.builder()
                .withGetCityBaseInfoResponseType(
                    WaitFutureUtil.safeGetFutureWithoutError(cityInfoResponseTypeWaitFuture))
                .withCheckAvailInfo(checkAvailInfo).withGetCityBaseInfoResponseTypeOfApprovalFlowReuse(
                    WaitFutureUtil.safeGetFuture(getCityBaseInfoResponseTypeOfApprovalFlowReuse))
                .withQconfigOfCertificateInitConfig(qconfigOfCertificateInitConfig)
                .withCheckHotelAuthExtensionResponseType(checkHotelAuthExtensionResponseType)
                .withAccountInfo(accountInfo).withGetOrderFoundationDataResponseType(getOrderFoundationDataResponseType)
                .withCityBaseInfo(cityBaseInfo).withOrderCreateRequestType(orderCreateRequestType)
                .withOrderCreateToken(orderCreateToken).withStrategyInfoMap(strategyInfoMap)
                .withOrderDetailResponseTypeOfApprovalFlowReuse(
                    WaitFutureUtil.safeGetFuture(orderDetailResponseTypeWaitFutureOfApprovalFlowReuse))
                .withSearchTripBasicInfoResponseTypeOfApprovalFlowReuse(
                    WaitFutureUtil.safeGetFuture(searchTripBasicInfoResponseTypeWaitFutureOfApprovalFlowReuse))
                .withQueryHotelAuthExtensionResponseType(queryHotelAuthExtensionResponseType)
                .withPolicyGetCorpUserInfoResponseType(
                    WaitFutureUtil.safeGetFuture(policyCorpUserInfoResponseTypeWaitFuture))
                .withPolicyGetCorpUserInfoResponseTypeOfApprovalFlowReuse(
                    WaitFutureUtil.safeGetFuture(policyGetCorpUserInfoResponseTypeOfApprovalFlowReuse)).build();
            Tuple2<Boolean, OrderCreateResponseType> followApprovalResponse =
                mapperOfApprovalFlowReuseResponse.map(Tuple1.of(wrapperOfApprovalFlowReuse));
            if (OrderCreateProcessorOfUtil.endOfProcess(followApprovalResponse)) {
                return OrderCreateProcessorOfUtil.getResponse(followApprovalResponse, cityBaseInfo);
            }
        } else {
            // 沿用审批
            WaitFuture<CheckHotelAuthExtensionRequestType, CheckHotelAuthExtensionResponseType>
                checkHotelAuthExtensionResponseTypeWaitFuture = null;
            if (OrderCreateProcessorOfUtil.requireFollowApproval(orderCreateRequestType, orderCreateToken,
                WaitFutureUtil.safeGetFuture(getAuthDelayResponseTypeWaitFuture), accountInfo)) {
                WrapperOfCheckHotelAuthExtension wrapperOfCheckHotelAuthExtension = WrapperOfCheckHotelAuthExtension.builder()
                    .withAccountInfo(accountInfo)
                    .withGetTravelPolicyContextResponseType(WaitFutureUtil.safeGetFuture(getTravelPolicyContextWaitFuture))
                    .withCheckTravelPolicyResponseType(WaitFutureUtil.safeGetFuture(checkTravelPolicyResponseTypeWaitFuture))
                    .withQueryCheckAvailContextResponseType(WaitFutureUtil.safeGetFuture(queryCheckAvailContextWaitFuture))
                    .withOrderCreateRequestType(orderCreateRequestType)
                    .withResourceToken(resourceToken)
                    .withCheckAvailInfo(checkAvailInfo)
                    .withQconfigOfCertificateInitConfig(qconfigOfCertificateInitConfig)
                    .withGetOrderFoundationDataResponseType(null)
                    .withStrategyInfoMap(strategyInfoMap)
                    .withOrderCreateToken(orderCreateToken)
                    .build();
                CheckHotelAuthExtensionRequestType checkHotelAuthExtensionRequestType =
                    mapperOfCheckHotelAuthExtensionRequestType.map(Tuple1.of(wrapperOfCheckHotelAuthExtension));
                checkHotelAuthExtensionResponseTypeWaitFuture =
                    handlerOfCheckHotelAuthExtension.handleAsync(checkHotelAuthExtensionRequestType);

            }
            WaitFuture<QueryHotelAuthExtensionRequestType, QueryHotelAuthExtensionResponseType> queryHotelAuthExtensionResponseTypeWaitFuture = null;

            // 人工延用校验行程状态
            WaitFuture<SearchTripDetailRequestType, SearchTripDetailResponseType> artificialTripDetailWaitFuture = null;
            if (OrderCreateProcessorOfUtil.requireArtificialFollowApproval(orderCreateRequestType, orderCreateToken)) {
                // 人工延用输入的行程号详情查询
                artificialTripDetailWaitFuture = handlerOfSearchTripDetail.handleAsync(
                    mapperOfSearchTripDetailRequestType.map(Tuple3.of(orderCreateRequestType.getIntegrationSoaRequestType(),
                        orderCreateRequestType.getFollowApprovalInfoInput().getFollowTripId(), null)));
            }

            // 智能沿用审批
            if (OrderCreateProcessorOfUtil.requireSmartFollowApproval(orderCreateRequestType, accountInfo, orderCreateToken,
                WaitFutureUtil.safeGetFuture(getAuthDelayResponseTypeWaitFuture))) {
                WrapperOfQueryHotelAuthExtension wrapperOfQueryHotelAuthExtension =
                    WrapperOfQueryHotelAuthExtension.builder().withAccountInfo(accountInfo)
                        .withGetTravelPolicyContextResponseType(
                            WaitFutureUtil.safeGetFuture(getTravelPolicyContextWaitFuture))
                        .withCheckTravelPolicyResponseType(
                            WaitFutureUtil.safeGetFuture(checkTravelPolicyResponseTypeWaitFuture))
                        .withQueryCheckAvailContextResponseType(
                            WaitFutureUtil.safeGetFuture(queryCheckAvailContextWaitFuture))
                        .withOrderCreateRequestType(orderCreateRequestType).withResourceToken(resourceToken)
                        .withCheckAvailInfo(checkAvailInfo)
                        .withQconfigOfCertificateInitConfig(qconfigOfCertificateInitConfig)
                        .withStrategyInfoMap(strategyInfoMap).build();
                QueryHotelAuthExtensionRequestType queryHotelAuthExtensionRequestType =
                    mapperOfQueryHotelAuthExtensionRequestType.map(Tuple1.of(wrapperOfQueryHotelAuthExtension));
                queryHotelAuthExtensionResponseTypeWaitFuture =
                    handlerOfQueryHotelAuthExtension.handleAsync(queryHotelAuthExtensionRequestType);
            }
            Tuple2<Boolean, OrderCreateResponseType> followApprovalResponse = mapperOfFollowApprovalResponse.map(
                Tuple7.of(WaitFutureUtil.safeGetFuture(queryHotelAuthExtensionResponseTypeWaitFuture),
                    WaitFutureUtil.safeGetFuture(checkHotelAuthExtensionResponseTypeWaitFuture), orderCreateRequestType,
                    cityBaseInfo, orderCreateToken, WaitFutureUtil.safeGetFuture(artificialTripDetailWaitFuture), accountInfo));
            if (OrderCreateProcessorOfUtil.endOfProcess(followApprovalResponse)) {
                return OrderCreateProcessorOfUtil.getResponse(followApprovalResponse, cityBaseInfo);
            }
        }

        // 审批流
        MatchApprovalFlowResponseType matchApprovalFlowResponseType = null;
        MatchApprovalFlowRequestType matchApprovalFlowRequestType = null;
        WaitFuture<ApprovalFlowComputeRequestType, ApprovalFlowComputeResponseType> approvalFlowComputeResponseTypeWaitFuture = null;
        WrapperOfMatchApprovalFlow wrapperOfMatchApprovalFlow = WrapperOfMatchApprovalFlow.builder()
            .withGetTravelPolicyContextResponseType(WaitFutureUtil.safeGetFuture(getTravelPolicyContextWaitFuture))
            .withCheckTravelPolicyResponseType(WaitFutureUtil.safeGetFuture(checkTravelPolicyResponseTypeWaitFuture))
            .withQueryCheckAvailContextResponseType(WaitFutureUtil.safeGetFuture(queryCheckAvailContextWaitFuture))
            .withOrderCreateRequestType(orderCreateRequestType)
            .withResourceToken(resourceToken)
            .withBaseCheckAvailInfo(checkAvailInfo)
            .withAccountInfo(accountInfo)
            .withSSOInfoQueryResponseType(WaitFutureUtil.safeGetFuture(ssoInfoQueryResponseTypeWaitFuture))
            .withOrderCreateToken(orderCreateToken)
            .withQconfigOfCertificateInitConfig(qconfigOfCertificateInitConfig)
            .withGetCityBaseInfoResponseType(WaitFutureUtil.safeGetFutureWithoutError(cityInfoResponseTypeWaitFuture))
            .withQConfigOfAccountInfoConfig(qConfigOfAccountInfoConfig)
            .withStrategyInfoMap(strategyInfoMap)
            .build();
        matchApprovalFlowRequestType = mapperOfMatchApprovalFlowRequestType.map(
            Tuple1.of(wrapperOfMatchApprovalFlow));
        if (OrderCreateProcessorOfUtil.requireApprovalFlowMatch(orderCreateRequestType, accountInfo, orderCreateToken,
            strategyInfoMap, WaitFutureUtil.safeGetFuture(tripDetailWaitFuture),
            WaitFutureUtil.safeGetFuture(tripDetailFolowWaitFuture))) {
            matchApprovalFlowResponseType = handlerOfMatchApprovalFlow.handleAsync(matchApprovalFlowRequestType).get();
            ApprovalFlowComputeRequestType approvalFlowComputeRequestType = mapperOfApprovalFlowComputeRequestType.map(
                Tuple7.of(matchApprovalFlowRequestType, matchApprovalFlowResponseType, orderCreateRequestType,
                    WaitFutureUtil.safeGetFuture(queryCheckAvailContextWaitFuture), checkAvailInfo, qconfigOfCertificateInitConfig,
                    strategyInfoMap));
            approvalFlowComputeResponseTypeWaitFuture = handlerOfApprovalFlowCompute.handleAsync(approvalFlowComputeRequestType);
            Tuple2<Boolean, OrderCreateResponseType> approvalFlowResponse = mapperOfApprovalFlowOutputInfoResponse.map(
                Tuple2.of(WaitFutureUtil.safeGetFuture(approvalFlowComputeResponseTypeWaitFuture), orderCreateToken)
            );
            if (OrderCreateProcessorOfUtil.endOfProcess(approvalFlowResponse)) {
                return OrderCreateProcessorOfUtil.getResponse(approvalFlowResponse, cityBaseInfo);
            }
        }
        // 审批流提交校验
        WaitFuture<ApprovalFlowCheckRequestType, ApprovalFlowCheckResponseType> approvalFlowCheckResponseTypeWaitFuture = null;
        if (OrderCreateProcessorOfUtil.requireApprovalFlowCheck(orderCreateRequestType)) {
            ApprovalFlowCheckRequestType approvalFlowCheckRequestType = mapperOfApprovalFlowCheckRequestType.map(
                    Tuple2.of(orderCreateRequestType, matchApprovalFlowRequestType));
            if (useNewOrderCreate) {
                approvalFlowCheckResponseTypeWaitFuture = handlerOfApprovalFlowCheck.handleAsync(approvalFlowCheckRequestType);
                mapperOfApprovalFlowCheckResponse.map(Tuple1.of(WaitFutureUtil.safeGetFuture(approvalFlowCheckResponseTypeWaitFuture)));
            }
        }

        // 创建无感行程
        WaitFuture<CreateTripRequestType, CreateTripResponseType> createTripWaitFuture = null;
        if (OrderCreateProcessorOfUtil.requireCreateTrip(orderCreateRequestType, accountInfo, orderCreateToken)) {
            // 创建行程需要校验客户未提交行程是否达到上限
            GetUserLiteInfoRequestType getUserLiteInfoRequestType =
                mapperOfGetUserLiteInfoRequestType.map(Tuple1.of(orderCreateRequestType));
            WaitFuture<GetUserLiteInfoRequestType, GetUserLiteInfoResponseType> getUserLiteInfoResponseTypeWaitFuture =
                handlerOfGetUserLiteInfo.handleAsync(getUserLiteInfoRequestType);

            CreateTripRequestType createTripRequestType = mapperOfCreateTripRequestType.map(
                Tuple3.of(orderCreateRequestType, WaitFutureUtil.safeGetFuture(getUserLiteInfoResponseTypeWaitFuture),
                    strategyInfoMap));
            if (useNewOrderCreate) {
                createTripWaitFuture = handlerOfCreateTrip.handleAsync(createTripRequestType);
            }
            // 如果政策执行人和登录卡不一致加到常用政策执行人中
            if (OrderCreateProcessorOfUtil.requireSaveHabitPolicyUser(orderCreateRequestType, accountInfo,
                orderCreateToken)) {
                handlerOfAddPolicyUserHabit.handleAsync(
                    mapperOfAddPolicyUserHabitRequestType.map(Tuple1.of(orderCreateRequestType)));
            }
        }

        // 行程出差申请关联
        if (OrderCreateProcessorOfUtil.requireSyncTrip(orderCreateRequestType, accountInfo,
            WaitFutureUtil.safeGetFuture(createTripWaitFuture))) {
            if (useNewOrderCreate) {
                handlerOfSyncTrip.handleAsync(mapperOfSyncTripRequestType.map(
                    Tuple4.of(WaitFutureUtil.safeGetFuture(createTripWaitFuture), orderCreateRequestType, accountInfo,
                        orderCreateToken)));
            }
        }

        // 团队房
        mapperOfTeamRoomConfirmInfoResponse.map(Tuple5.of(orderCreateToken, orderCreateRequestType,
            checkAvailInfo, qconfigOfCertificateInitConfig, strategyInfoMap));

        // 创单
        CreateOrderResponseType createOrderResponseType = null;
        WaitFuture<CreateOrderRequestType, CreateOrderResponseType> createOrderResponseTypeWaitFuture = null;
        CreateOrderRequestType createOrderRequestType = null;
        if (OrderCreateProcessorOfUtil.requireCreateOrder(orderCreateToken)) {
            WrapperOfCreateOrder wrapperOfCreateOrder = WrapperOfCreateOrder.builder()
                .setQueryCheckAvailContextResponseType(WaitFutureUtil.safeGetFuture(queryCheckAvailContextWaitFuture))
                .setCreateTripResponseType(WaitFutureUtil.safeGetFuture(createTripWaitFuture))
                .setOrderCreateRequestType(orderCreateRequestType)
                .setResourceToken(resourceToken)
                .setGetTravelPolicyContextResponseType(WaitFutureUtil.safeGetFuture(getTravelPolicyContextWaitFuture))
                .setCheckTravelPolicyResponseType(WaitFutureUtil.safeGetFuture(checkTravelPolicyResponseTypeWaitFuture))
                .setAccountInfo(accountInfo)
                .setCostAllocationToken(allocationResultToken)
                .setSearchTripDetailResponseType(WaitFutureUtil.safeGetFuture(tripDetailWaitFuture))
                .setQueryHotelOrderDataResponseType(WaitFutureUtil.safeGetFuture(queryHotelOrderDataResponseTypeWaitFuture))
                .setOrderCreateToken(orderCreateToken)
                .setPayConfigResponseType(WaitFutureUtil.safeGetFuture(payConfigResponseTypeWaitFuture))
                .setGetPlatformRelationByUidResponseType(
                    WaitFutureUtil.safeGetFutureWithoutError(getPlatformRelationByUidResponseTypeWaitFuture))
                .setCheckAvailContextInfo(checkAvailInfo)
                .setSsoInfoQueryResponseType(WaitFutureUtil.safeGetFuture(ssoInfoQueryResponseTypeWaitFuture))
                .setMatchApprovalFlowResponseType(matchApprovalFlowResponseType)
                .setApprovalFlowComputeResponseType(WaitFutureUtil.safeGetFuture(approvalFlowComputeResponseTypeWaitFuture))
                .setGetPackageRoomListResponseType(WaitFutureUtil.safeGetFutureWithoutError(getPackageRoomListWaitFuture))
                .setGetInboundParameterResponseType(WaitFutureUtil.safeGetFutureWithoutError(getInboundParameterWaitFuture))
                .setQconfigOfEmailInfoConfig(qconfigOfEmailInfoConfig)
                .setQueryBizModeBindRelationResponseType(WaitFutureUtil.safeGetFutureWithoutError(queryBizModeBindRelationResponseTypeWaitFuture))
                .setRegisterResponse(WaitFutureUtil.safeGetFutureWithoutError(registerResponseTypeWaitFuture))
                .setStrategyInfoMap(strategyInfoMap)
                .setQueryIndividualAccountResponseType(
                    WaitFutureUtil.safeGetFutureWithoutError(queryIndividualAccountWaitFuture))
                .setQconfigOfCertificateInitConfig(qconfigOfCertificateInitConfig)
                .build();
            createOrderRequestType =
                mapperOfCreateOrderRequestType.map(Tuple1.of(wrapperOfCreateOrder));
            if (useNewOrderCreate) {
                createOrderResponseTypeWaitFuture = handlerOfCreateOrder.handleAsync(createOrderRequestType);
            } else {
                String createOrderRequestTypeStr = JsonUtil.toJson(createOrderRequestType);
                // 对比日志记录
                LogUtil.logCompare("BFF-Merge-Hotel-orderCheck-CreateOrderRequestType",  createOrderRequestTypeStr,
                    createOrderRequestTypeStr);
            }
        }

        // 订单创建结果
        Tuple2<Boolean, OrderCreateResponseType> confirmDetailResponse = mapperOfConfirmDetailResponse.map(
            Tuple2.of(orderCreateRequestType, WaitFutureUtil.safeGetFuture(createOrderResponseTypeWaitFuture)));
        if (OrderCreateProcessorOfUtil.endOfProcess(confirmDetailResponse)) {
            return OrderCreateProcessorOfUtil.getResponse(confirmDetailResponse, cityBaseInfo);
        }

        // offline客户自行支付权限校验
        WaitFuture<OrderOperationRequestType, OrderOperationResponseType> orderOperationResponseTypeWaitFuture = null;
        if (OrderCreateProcessorOfUtil.requireOrderOperation(orderCreateRequestType,
            WaitFutureUtil.safeGetFuture(createOrderResponseTypeWaitFuture), orderCreateToken)) {
            OrderOperationRequestType orderOperationRequestType = mapperOfOrderOperationRequestType.map(
                Tuple3.of(orderCreateRequestType.getIntegrationSoaRequestType(),
                    Collections.singletonList(OrderCreateProcessorOfUtil.BOOK_PAY),
                    OrderCreateProcessorOfUtil.buildCreateOrderResult(
                        WaitFutureUtil.safeGetFuture(createOrderResponseTypeWaitFuture), orderCreateRequestType,
                        orderCreateToken).getOrderID()));
            if (useNewOrderCreate) {
                orderOperationResponseTypeWaitFuture = handlerOfOrderOperation.handleAsync(orderOperationRequestType);
            }
        }
        // 保存成本中心
        WaitFuture<SaveOrderCostCenterRequestType, SaveOrderCostCenterResponseType>
            saveOrderCostCenterResponseTypeWaitFuture = null;
        if (OrderCreateProcessorOfUtil.saveOrderCostCenter(orderCreateRequestType, accountInfo,
            saveCommonDataCostCenter, orderCreateToken, resourceToken, strategyInfoMap,
            WaitFutureUtil.safeGetFuture(createOrderResponseTypeWaitFuture))) {
            WrapperOfSaveCostCenter wrapperOfSaveCostCenter =
                WrapperOfSaveCostCenter.builder().setOrderCreateRequestType(orderCreateRequestType)
                    .setAccountInfo(accountInfo).setCheckAvailInfo(checkAvailInfo).setOrderCreateToken(orderCreateToken)
                    .setCreateOrderResponseType(WaitFutureUtil.safeGetFuture(createOrderResponseTypeWaitFuture))
                    .setApprovalTextInfoResponseType(
                        WaitFutureUtil.safeGetFuture(approvalTextInfoResponseTypeWaitFuture))
                    .setSsoInfoQueryResponseType(WaitFutureUtil.safeGetFuture(ssoInfoQueryResponseTypeWaitFuture))
                    .setMatchCostCenterResponseType(WaitFutureUtil.safeGetFuture(matchCostCenterResponseTypeWaitFuture))
                    .setResourceToken(resourceToken)
                    .setQconfigOfCertificateInitConfig(qconfigOfCertificateInitConfig)
                    .setStrategyInfoMap(strategyInfoMap)
                    .build();
            SaveOrderCostCenterRequestType saveOrderCostCenterRequestType =
                mapperOfSaveOrderCostCenterRequestType.map(Tuple1.of(wrapperOfSaveCostCenter));
            if (useNewOrderCreate) {
                saveOrderCostCenterResponseTypeWaitFuture =
                    handlerOfSaveCostCenter.handleAsync(saveOrderCostCenterRequestType);
            }
        }

        // 保存会员卡号 - 不关心结果
        OperateCorpUserHotelVipCardRequestType operateCorpUserHotelVipCardRequestType = mapperOfOperateCorpUserHotelVipCardRequestType.map(
                Tuple3.of(orderCreateRequestType, WaitFutureUtil.safeGetFutureWithoutError(queryBizModeBindRelationResponseTypeWaitFuture), checkAvailInfo));
        if (operateCorpUserHotelVipCardRequestType != null) {
            WaitFuture<OperateCorpUserHotelVipCardRequestType, OperateCorpUserHotelVipCardResponseType> operateCorpUserHotelVipCardResponseTypeWaitFuture =
                    handlerOfOperateCorpUserHotelVipCard.handleAsync(operateCorpUserHotelVipCardRequestType);
        }

        // saveCommonData
        WaitFuture<SaveCommonDataRequestType, SaveCommonDataResponseType> saveCommonDataResponseTypeWaitFuture = null;
        if (OrderCreateProcessorOfUtil.requireSaveCommonData(orderCreateToken)) {
            WrapperOfSaveCommonData wrapperOfSaveCommonData = WrapperOfSaveCommonData.builder()
                .setCreateOrderResponseType(WaitFutureUtil.safeGetFuture(createOrderResponseTypeWaitFuture))
                .setQueryCheckAvailContextResponseType(WaitFutureUtil.safeGetFuture(queryCheckAvailContextWaitFuture))
                .setCheckTravelPolicyResponseType(WaitFutureUtil.safeGetFuture(checkTravelPolicyResponseTypeWaitFuture))
                .setCreateTripResponseType(WaitFutureUtil.safeGetFuture(createTripWaitFuture))
                .setGetCorpUserInfoResponseTypePolicy(WaitFutureUtil.safeGetFuture(policyCorpUserInfoResponseTypeWaitFuture))
                .setSsoInfoQueryResponseType(WaitFutureUtil.safeGetFuture(ssoInfoQueryResponseTypeWaitFuture))
                .setOrderCreateRequestType(orderCreateRequestType)
                .setMatchApprovalFlowResponseType(matchApprovalFlowResponseType).setAccountInfo(accountInfo)
                .setApprovalFlowComputeResponseType(
                    WaitFutureUtil.safeGetFuture(approvalFlowComputeResponseTypeWaitFuture))
                .setResourceToken(resourceToken)
                .setSearchTripDetailResponseType(WaitFutureUtil.safeGetFuture(tripDetailWaitFuture))
                .setSearchTripDetailResponseTypeFolow(WaitFutureUtil.safeGetFuture(tripDetailFolowWaitFuture))
                .setOrderCreateToken(orderCreateToken).setCheckAvailInfo(checkAvailInfo)
                .setApprovalTextInfoResponseType(WaitFutureUtil.safeGetFuture(approvalTextInfoResponseTypeWaitFuture))
                .setQConfigOfCodeMappingConfig(qConfigOfCodeMappingConfig)
                .setSaveOrderCostCenterResponseType(WaitFutureUtil.safeGetFuture(saveOrderCostCenterResponseTypeWaitFuture))
                .setQconfigOfCertificateInitConfig(qconfigOfCertificateInitConfig)
                .setStrategyInfoMap(strategyInfoMap)
                .build();
            SaveCommonDataRequestType saveCommonDataRequestType =
                mapperOfSaveCommonDataRequestType.map(Tuple1.of(wrapperOfSaveCommonData));
            OrderCreateProcessorOfUtil.savePassengerName(saveCommonDataRequestType, createOrderRequestType, resourceToken,
                orderCreateRequestType, accountInfo, strategyInfoMap);
            if (useNewOrderCreate) {
                saveCommonDataResponseTypeWaitFuture = handlerOfSaveCommonData.handleAsync(saveCommonDataRequestType);
                // check saveCommonData核心接口
                OrderCreateProcessorOfUtil.checkSaveCommonData(
                    WaitFutureUtil.safeGetFuture(saveCommonDataResponseTypeWaitFuture));
            } else {
                String saveCommonDataRequestTypeStr = JsonUtil.toJson(saveCommonDataRequestType);
                // 对比日志记录
                LogUtil.logCompare("BFF-Merge-Hotel-orderCheck-SaveCommonDataRequestType",  saveCommonDataRequestTypeStr,
                    saveCommonDataRequestTypeStr);
            }
        }
        if (OrderCreateProcessorOfUtil.requireCorpVatInvoiceProcess(orderCreateRequestType)) {
            ReimburseSettlementSyncRequestType reimburseSettlementSyncRequestType =
                mapperOfReimburseSettlementSyncRequestType.map(
                    Tuple4.of(orderCreateRequestType, WaitFutureUtil.safeGetFuture(createOrderResponseTypeWaitFuture),
                            WaitFutureUtil.safeGetFuture(queryCheckAvailContextWaitFuture), orderCreateToken));
            if (useNewOrderCreate) {
                handlerOfReimburseSettlementSync.handleAsync(reimburseSettlementSyncRequestType);
            }
        }
        if (OrderCreateProcessorOfUtil.requireSaveContactInvoiceDefaultInfo(
            orderCreateRequestType.getHotelInvoiceInfos())) {
            SaveContactInvoiceDefaultInfoRequestType saveContactInvoiceDefaultInfoRequestType =
                mapperOfSaveContactInvoiceDefaultInfoRequestType.map(Tuple1.of(orderCreateRequestType));
            if (useNewOrderCreate) {
                handlerOfSaveContactInvoiceDefaultInfo.handleAsync(saveContactInvoiceDefaultInfoRequestType);
            }
        }
        if (OrderCreateProcessorOfUtil.requirePriceChange(
            WaitFutureUtil.safeGetFuture(createOrderResponseTypeWaitFuture), orderCreateToken)) {
            // 变价弹窗
            Tuple2<Boolean, OrderCreateResponseType> priceChangeInfoResponse = mapperOfPriceChangeInfoResponse.map(
                Tuple3.of(WaitFutureUtil.safeGetFuture(createOrderResponseTypeWaitFuture), orderCreateToken,
                    orderCreateRequestType));
            if (OrderCreateProcessorOfUtil.endOfProcess(priceChangeInfoResponse)) {
                return OrderCreateProcessorOfUtil.getResponse(priceChangeInfoResponse, cityBaseInfo);
            }
        }
        // 确认订单
        if (OrderCreateProcessorOfUtil.requireConfirmOrder(orderCreateToken, orderCreateRequestType)
            && !StrategyOfBookingInitUtil.notRequiredConfirmOrder(strategyInfoMap)) {
            Tuple2<Boolean, OrderCreateResponseType> confirmOrderInfoResponse = mapperOfConfirmOrderInfoResponse.map(
                Tuple6.of(orderCreateToken, orderCreateRequestType,
                    WaitFutureUtil.safeGetFuture(queryCheckAvailContextWaitFuture),
                    WaitFutureUtil.safeGetFuture(createOrderResponseTypeWaitFuture),
                    WaitFutureUtil.safeGetFuture(orderOperationResponseTypeWaitFuture),
                    qConfigOfCodeMappingConfig));
            if (OrderCreateProcessorOfUtil.endOfProcess(confirmOrderInfoResponse)) {
                return OrderCreateProcessorOfUtil.getResponse(confirmOrderInfoResponse, cityBaseInfo);
            }
        }
        // offline新个付全流程用一个灰度开关 防止qconfig中途变更 灰度全开后移到requirePaymentOrderCreate方法内部并去掉表单逻辑的判断
        boolean offlineNewPay =
            OrderCreateProcessorOfUtil.requireOfflineNewPay(orderCreateRequestType, resourceToken, orderCreateToken);
        WaitFuture<QueryPaymentBillConfigRequestType, QueryPaymentBillConfigResponseType>
            queryPaymentBillConfigResponseTypeWaitFuture = null;
        if (OrderCreateProcessorOfUtil.requireQueryPaymentBill(orderCreateRequestType, resourceToken, orderCreateToken)) {
            QueryPaymentBillConfigRequestType queryPaymentBillConfigRequestType =
                mapperOfQueryPaymentBillConfigRequestType.map(
                    Tuple5.of(resourceToken, WaitFutureUtil.safeGetFuture(createOrderResponseTypeWaitFuture),
                        orderCreateRequestType, orderCreateToken, offlineNewPay));
            if (useNewOrderCreate) {
                queryPaymentBillConfigResponseTypeWaitFuture =
                    handlerOfQueryPaymentBillConfig.handleAsync(queryPaymentBillConfigRequestType);
            } else {
                String queryPaymentBillConfigRequestTypeStr = JsonUtil.toJson(queryPaymentBillConfigRequestType);
                // 对比日志记录
                LogUtil.logCompare("BFF-Merge-Hotel-orderCheck-QueryPaymentBillConfigRequestType",
                    queryPaymentBillConfigRequestTypeStr, queryPaymentBillConfigRequestTypeStr);
            }
        }
        // 个人支付
        WaitFuture<PaymentOrderCreateRequestType, PaymentOrderCreateResponseType>
            paymentOrderCreateResponseTypeWaitFuture = null;
        if (OrderCreateProcessorOfUtil.requirePaymentOrderCreate(orderCreateRequestType, resourceToken,
            orderCreateToken) || offlineNewPay) {
            PaymentOrderCreateRequestType paymentOrderCreateRequestType = mapperOfPaymentOrderCreateRequestType.map(
                Tuple12.of(WaitFutureUtil.safeGetFuture(createOrderResponseTypeWaitFuture), checkAvailInfo,
                    orderCreateRequestType, accountInfo,
                    WaitFutureUtil.safeGetFuture(queryPaymentBillConfigResponseTypeWaitFuture), createOrderRequestType,
                    orderCreateToken, resourceToken, tmsCreateOrderVerifyResponseType, offlineNewPay,
                    WaitFutureUtil.safeGetFuture(createTripWaitFuture), strategyInfoMap));
            if (useNewOrderCreate) {
                paymentOrderCreateResponseTypeWaitFuture =
                    handlerOfPaymentOrderCreate.handleAsync(paymentOrderCreateRequestType);
            } else {
                String paymentOrderCreateRequestTypeStr = JsonUtil.toJson(paymentOrderCreateRequestType);
                // 对比日志记录
                LogUtil.logCompare("BFF-Merge-Hotel-orderCheck-PaymentOrderCreateRequestType",  paymentOrderCreateRequestTypeStr,
                    paymentOrderCreateRequestTypeStr);
            }
        }
        // 提交订单
        WaitFuture<SubmitOrderRequestType, SubmitOrderResponseType> submitOrderResponseTypeWaitFuture = null;
        if (OrderCreateProcessorOfUtil.requireSubmitOrder(orderCreateRequestType, resourceToken, orderCreateToken)) {
            SubmitOrderRequestType submitOrderRequestType = mapperOfSubmitOrderRequestType.map(
                    Tuple2.of(WaitFutureUtil.safeGetFuture(createOrderResponseTypeWaitFuture), orderCreateToken));
            if (useNewOrderCreate) {
                submitOrderResponseTypeWaitFuture = handlerOfSubmitOrder.handleAsync(submitOrderRequestType);
            }
        }

        // 丰享支付
        WaitFuture<TransactionPayUrlRequestType, TransactionPayUrlResponseType>
            typeTransactionPayUrlResponseTypeWaitFuture = null;
        if (OrderCreateProcessorOfUtil.requireDoublePay(orderCreateRequestType,
            WaitFutureUtil.safeGetFutureWithoutError(payConfigResponseTypeWaitFuture))) {
            TransactionPayUrlRequestType transactionPayUrlRequestType = mapperOfTransactionPayUrlRequestType.map(
                Tuple4.of(orderCreateRequestType, WaitFutureUtil.safeGetFuture(createOrderResponseTypeWaitFuture),
                    orderCreateToken, checkAvailInfo));
            if (useNewOrderCreate) {
                typeTransactionPayUrlResponseTypeWaitFuture =
                    handlerOfTransactionPayUrl.handleAsync(transactionPayUrlRequestType);
            }
        }

        // 赋值完成信息
        WrapperOfFinishResponse wrapperOfFinishResponse =
            WrapperOfFinishResponse.builder().setTmsCreateOrderVerifyResponseType(tmsCreateOrderVerifyResponseType)
                .setPaymentOrderCreateResponseType(
                    WaitFutureUtil.safeGetFuture(paymentOrderCreateResponseTypeWaitFuture))
                .setCreateOrderResponseType(WaitFutureUtil.safeGetFuture(createOrderResponseTypeWaitFuture))
                .setCreateTripResponseType(WaitFutureUtil.safeGetFuture(createTripWaitFuture))
                .setOrderCreateRequestType(orderCreateRequestType).setResourceToken(resourceToken)
                .setOrderCreateToken(orderCreateToken).setQueryPaymentBillConfigResponseType(
                    WaitFutureUtil.safeGetFuture(queryPaymentBillConfigResponseTypeWaitFuture))
                .setCheckAvailContextInfo(checkAvailInfo).setTransactionPayUrlResponseType(
                    WaitFutureUtil.safeGetFuture(typeTransactionPayUrlResponseTypeWaitFuture))
                .setPayConfigResponseType(WaitFutureUtil.safeGetFuture(payConfigResponseTypeWaitFuture))
                .setAccountInfo(accountInfo)
                .setOfflineNewPay(offlineNewPay)
                .setRetrieveTicketsByOrderTypeResponseType(
                    WaitFutureUtil.safeGetFuture(retrieveTicketsByOrderTypeResponseTypeWaitFuture));
        Tuple2<Boolean, OrderCreateResponseType> finishInfoResponse =
            mapperOfFinishInfoResponse.map(Tuple1.of(wrapperOfFinishResponse));
        return OrderCreateProcessorOfUtil.getResponse(finishInfoResponse, cityBaseInfo);
    }


    @Override public Map<String, String> tracking(OrderCreateRequestType orderCreateRequestType,
        OrderCreateResponseType orderCreateResponseType) {
        Map<String, String> trackingMap = TrackingUtil.buildBaseTrackingMap(orderCreateRequestType.getHotelBookInput());
        trackingMap.put(TrackingEnum.CITY_ID.getCode(),
            Optional.ofNullable(orderCreateResponseType).map(OrderCreateResponseType::getCityInfo)
                .map(CityInfo::getCityId).orElse(0).toString());
        trackingMap.put(TrackingEnum.CITY_REGION.getCode(),
            Optional.ofNullable(orderCreateResponseType).map(OrderCreateResponseType::getCityInfo)
                .map(CityInfo::getCityArea).orElse(StringUtil.EMPTY));
        /*todo：trackingMap.put("roomType", StringUtil.EMPTY); trackingMap.put("hotelId", StringUtil.EMPTY);
        trackingMap.put("wsId", StringUtil.EMPTY);  trackingMap.put("gdsType", "");*/
        trackingMap.put("orderId",
            Optional.ofNullable(orderCreateResponseType).map(OrderCreateResponseType::getFinishInfo)
                .map(FinishInfoOutput::getOrderId).orElse(StringUtil.EMPTY));
        if (orderCreateRequestType.getHotelPayTypeInput() != null) {
            HotelPayTypeEnum roomPayType =
                HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
            trackingMap.put("roomPayType",
                roomPayType != null ? roomPayType.getCode() : HotelPayTypeEnum.NONE.getCode());
        }
        // 二次提交场景---管控、rc、重复预定、审批流曲线无大的变化
        if (StringUtil.isNotBlank(orderCreateRequestType.getOrderCreateToken())) {
            OrderCreateToken orderCreateToken =
                TokenParseUtil.parseOrderCreateToken(orderCreateRequestType.getOrderCreateToken(),
                    "T".equalsIgnoreCase(orderCreateRequestType.getUseNewOrderCreate()));
            trackingMap.put("continueTypes", JsonUtil.toJson(orderCreateToken.getContinueTypes()));
        }
        if (orderCreateResponseType != null) {
            // 管控规则 FORBID_BOOKING：禁止预订  ALLOW_BOOKING：继续提交
            if (orderCreateResponseType.getVerifyApprovalInfo() != null) {
                trackingMap.put("failRule", orderCreateResponseType.getVerifyApprovalInfo().getFailRule());
            }
        }
        String bookMode = TrackingBookModeEnum.NORMAL.name();
        if (orderCreateRequestType.getApprovalInput() != null && (
            StringUtil.isNotEmpty(orderCreateRequestType.getApprovalInput().getSubApprovalNo()) || "T".equalsIgnoreCase(
                orderCreateRequestType.getApprovalInput().getEmergency()))) {
            bookMode = TrackingBookModeEnum.APPROVAL.name();
        }
        trackingMap.put(TrackingEnum.BOOK_MODE.getCode(), bookMode);

        trackingMap.put(TrackingEnum.CORP_PAY_TYPE.getCode(),
            Optional.ofNullable(orderCreateRequestType).map(OrderCreateRequestType::getCorpPayInfo)
                .map(CorpPayInfo::getCorpPayType).orElse(StringUtil.EMPTY));
        return trackingMap;
    }
}
