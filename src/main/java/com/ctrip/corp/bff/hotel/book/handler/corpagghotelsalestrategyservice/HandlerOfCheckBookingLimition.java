package com.ctrip.corp.bff.hotel.book.handler.corpagghotelsalestrategyservice;

import com.ctrip.corp.agg.hotel.checkbookinglimition.entity.CheckBookingLimitionRequestType;
import com.ctrip.corp.agg.hotel.checkbookinglimition.entity.CheckBookingLimitionResponseType;
import com.ctrip.corp.agg.hotel.salestrategy.CorpAggHotelSaleStrategyServiceClient;
import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/11/13 16:37
 */
@Component public class HandlerOfCheckBookingLimition extends
    AbstractHandlerOfSOA<CheckBookingLimitionRequestType, CheckBookingLimitionResponseType, CorpAggHotelSaleStrategyServiceClient> {

    @Override protected String getMethodName() {
        return "checkBookingLimition";
    }
}