package com.ctrip.corp.bff.hotel.book.handler.corpuserinfoservice4j;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.corpUserInfoService.CorpUserInfoService4jClient;
import corp.user.service.corpUserInfoService.GetPolicyUsersRequestType;
import corp.user.service.corpUserInfoService.GetPolicyUsersResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/5/16
 */
@Component
public class HandlerOfGetPolicyUsers extends AbstractHandlerOfSOA<GetPolicyUsersRequestType, GetPolicyUsersResponseType, CorpUserInfoService4jClient> {
    @Override
    protected String getMethodName() {
        return "getPolicyUsers";
    }
}
