package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType;
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextResponseType;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount.AccountInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple6;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple7;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple8;
import com.ctrip.corp.bff.hotel.book.common.util.HotelAuthExtensionUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail.BaseCheckAvailInfo;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfQueryHotelAuthExtension;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.soa._20184.QueryHotelAuthExtensionRequestType;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/6/23 18:31
 */
@Component
public class MapperOfQueryHotelAuthExtensionRequestType extends
    AbstractMapper<Tuple1<WrapperOfQueryHotelAuthExtension>, QueryHotelAuthExtensionRequestType> {

    @Override
    protected QueryHotelAuthExtensionRequestType convert(
        Tuple1<WrapperOfQueryHotelAuthExtension> tuple) {
        WrapperOfQueryHotelAuthExtension wrapperOfQueryHotelAuthExtension = tuple.getT1();
        WrapperOfAccount.AccountInfo accountInfo = wrapperOfQueryHotelAuthExtension.getAccountInfo();
        GetTravelPolicyContextResponseType getTravelPolicyContextResponseType = wrapperOfQueryHotelAuthExtension
            .getGetTravelPolicyContextResponseType();
        CheckTravelPolicyResponseType checkTravelPolicyResponseType = wrapperOfQueryHotelAuthExtension
            .getCheckTravelPolicyResponseType();
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType = wrapperOfQueryHotelAuthExtension
            .getQueryCheckAvailContextResponseType();
        OrderCreateRequestType orderCreateRequestType = wrapperOfQueryHotelAuthExtension.getOrderCreateRequestType();
        ResourceToken resourceToken = wrapperOfQueryHotelAuthExtension.getResourceToken();
        WrapperOfCheckAvail.BaseCheckAvailInfo baseCheckAvailInfo = wrapperOfQueryHotelAuthExtension
            .getCheckAvailInfo();
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig = wrapperOfQueryHotelAuthExtension
            .getQconfigOfCertificateInitConfig();
        Map<String, StrategyInfo> strategyInfoMap = wrapperOfQueryHotelAuthExtension.getStrategyInfoMap();
        QueryHotelAuthExtensionRequestType queryHotelAuthExtensionRequestType =
            new QueryHotelAuthExtensionRequestType();
        queryHotelAuthExtensionRequestType.setUid(
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId());
        queryHotelAuthExtensionRequestType.setOrderMode(HotelAuthExtensionUtil.getOrderMode(accountInfo));
        queryHotelAuthExtensionRequestType.setPolicyUid(HotelAuthExtensionUtil.getPolicyUid(orderCreateRequestType));
        queryHotelAuthExtensionRequestType.setSettlementAmount(
            HotelAuthExtensionUtil.buildSettlementAmount(queryCheckAvailContextResponseType));
        queryHotelAuthExtensionRequestType.setHotelProductInfo(
            HotelAuthExtensionUtil.buildHotelProductInfo(orderCreateRequestType, queryCheckAvailContextResponseType,
                baseCheckAvailInfo));
        queryHotelAuthExtensionRequestType.setTravelControlInfo(
            HotelAuthExtensionUtil.buildTravelControlInfo(getTravelPolicyContextResponseType,
                checkTravelPolicyResponseType, orderCreateRequestType, resourceToken));
        queryHotelAuthExtensionRequestType.setClientList(
            HotelAuthExtensionUtil.buildClientInfos(orderCreateRequestType, baseCheckAvailInfo,
                qconfigOfCertificateInitConfig, strategyInfoMap));
        return queryHotelAuthExtensionRequestType;
    }

    @Override
    protected ParamCheckResult check(
        Tuple1<WrapperOfQueryHotelAuthExtension> tuple) {
        return null;
    }
}
