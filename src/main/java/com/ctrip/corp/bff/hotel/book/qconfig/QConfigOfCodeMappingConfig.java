package com.ctrip.corp.bff.hotel.book.qconfig;

import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.CodeMappingConfig;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2021/10/11 14:38
 */
@Component
public class QConfigOfCodeMappingConfig {
    @QConfig("CodeMappingConfig.json")
    private List<CodeMappingConfig> codeMappingEntities;

    public List<CodeMappingConfig> getCodeMappingEntities() {
        return codeMappingEntities;
    }

    public void setCodeMappingEntities(List<CodeMappingConfig> codeMappingEntities) {
        this.codeMappingEntities = codeMappingEntities;
    }

    public CodeMappingConfig getCodeMappingConfig(String actionName) {
        if (CollectionUtil.isEmpty(codeMappingEntities)) {
            return null;
        }
        Map<String, CodeMappingConfig> codeMappingConfigMap = codeMappingEntities.stream()
                .filter(Objects::nonNull)
                .collect(Collectors.toMap(CodeMappingConfig::getActionName, v2 -> v2, (v1, v2) -> v1));
        if (codeMappingConfigMap.containsKey(actionName)) {
            return codeMappingConfigMap.get(actionName);
        }
        return null;
    }
}
