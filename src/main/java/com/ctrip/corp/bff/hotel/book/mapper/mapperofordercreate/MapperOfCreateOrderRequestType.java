package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.threadlocal.TraceContextUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationResponse;
import com.ctrip.corp.bff.framework.template.entity.PosEnum;
import com.ctrip.corp.bff.hotel.book.common.constant.SoaErrorSharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.corp.bff.specific.contract.CostCenterCheckResponseType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.collections.MapUtils;
import org.apache.commons.lang.math.NumberUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import com.ctrip.arch.coreinfo.entity.InfoKey;
import com.ctrip.arch.coreinfo.enums.KeyType;
import com.ctrip.basebiz.members.core.contract.CallEntity;
import com.ctrip.basebiz.members.core.contract.GetInboundParameterResponseType;
import com.ctrip.basebiz.members.core.contract.MembersEntity;
import com.ctrip.corp.agg.commonws.entity.GetPackageRoomListResponseType;
import com.ctrip.corp.agg.commonws.entity.PackageExtendInfoType;
import com.ctrip.corp.agg.commonws.entity.PackageRoomInfoType;
import com.ctrip.corp.agg.commonws.entity.XProductStaticInfoType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.*;
import com.ctrip.corp.agg.hotel.roomavailable.entity.AmountDetailEntity;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.VerifyApprovalBillResultType;
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextResponseType;
import com.ctrip.corp.approve.ws.contract.MatchApprovalFlowResponseType;
import com.ctrip.corp.bff.framework.hotel.common.constant.BooleanConstant;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig;
import com.ctrip.corp.bff.framework.hotel.common.util.*;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.entity.contract.*;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.*;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.common.RcToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.RepeatOrderResult;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.enums.ModifyPolicyTypeEnum;
import com.ctrip.corp.bff.framework.template.common.log.enumeration.DateUnitEnum;
import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum;
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil;
import com.ctrip.corp.bff.framework.template.common.qconfig.QConfigFile;
import com.ctrip.corp.bff.framework.template.common.qconfig.QConfigUtil;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.threadlocal.ThreadContextUtil;
import com.ctrip.corp.bff.framework.template.common.utils.*;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.common.utils.date.DateUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.MapString;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.*;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.CustomConfigKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.SharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.*;
import com.ctrip.corp.bff.hotel.book.common.token.RoomRemarkToken;
import com.ctrip.corp.bff.hotel.book.common.util.*;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCreateOrder;
import com.ctrip.corp.bff.hotel.book.contract.*;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfEmailInfoConfig;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.CorpEmail;
import com.ctrip.corp.bff.profile.contract.SSOBaseInfo;
import com.ctrip.corp.bff.profile.contract.SSOCostCenterInfo;
import com.ctrip.corp.bff.profile.contract.SSOExtendInfo;
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryResponseType;
import com.ctrip.corp.bff.specific.contract.ApprovalFlowComputeResponseType;
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType;
import com.ctrip.corp.foundation.common.enums.LanguageLocaleEnum;
import com.ctrip.corp.foundation.common.exception.TimeZoneNotFoundException;
import com.ctrip.corp.foundation.common.plus.time.HotelTimeZoneUtil;
import com.ctrip.corp.foundation.common.util.DateUTCUtils;
import com.ctrip.corp.foundation.common.util.NumberUtil;
import com.ctrip.corp.hotelbooking.hotelws.entity.*;
import com.ctrip.corp.hotelbooking.hotelws.entity.CertificateTypeEnum;
import com.ctrip.corp.hotelbooking.hotelws.entity.CorpPayInfo;
import com.ctrip.corp.hotelbooking.hotelws.entity.CustomerPropertyType;
import com.ctrip.corp.hotelbooking.hotelws.entity.InvoiceTitleTypeEnum;
import com.ctrip.corp.hotelbooking.hotelws.entity.RCInfo;
import com.ctrip.corp.hotelbooking.hotelws.entity.insuranceInfo.InsuranceDetailInfo;
import com.ctrip.corp.hotelbooking.hotelws.entity.insuranceInfo.InsuranceInfo;
import com.ctrip.corp.hotelbooking.hotelws.entity.insuranceInfo.InsuredInfo;
import com.ctrip.corp.hotelbooking.hotelws.modify.RCInfoType;
import com.ctrip.corp.order.data.aggregation.query.contract.OrderGenericInfoType;
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataResponseType;
import com.ctrip.model.RegisterResponseType;
import com.ctrip.soa._20183.ApprovalInfoType;
import com.ctrip.soa._21234.CreateTripResponseType;
import com.ctrip.soa._21234.SearchTripDetailResponseType;
import com.ctrip.soa._21685.PayConfigResponseType;

import corp.user.service.CorpAccountQueryService.QueryIndividualAccountResponseType;
import corp.user.service.corpUserInfoService.GetPlatformRelationByUidResponseType;
import corp.user.service.group4j.accounts.BizModeBindRelationData;
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType;

/**
 * <AUTHOR>
 * @description TODO 汇总
 * 按最新契约对接创单下线 根据文档定义可下线：http://conf.ctripcorp.com/pages/viewpage.action?pageId=**********
 * 1.需要外部返回结果: CalculateAddPriceAmountDetailResponseType, GetPlatformRelationByUidResponseTyp,
 * CalculateServiceChargeResponseType, DistributePaymentAmountResponseType,
 * GetSubAccountConfigResponseType, QuerySSOInfoResponseType,
 * GetInboundParameterResponseType
 * 2.契约缺入参 入住人信息缺temporaryId需要再调研, 担保到期时间lastGuaranteeTime, 用于判断是否需要发票注明入离店时间RTInvoice,
 * XProductId, 新需求增加的积分相关MembershipPhoneInfo和PhoneInfoType
 * 4.accountInfo缺少 policyUserCTLScopeH, htlShareControlEdit, htlFeeAllocationType, htlPrintTicketAfterConfirm,
 * multiCurrency
 * 5.可订反查缺 GroupRegisterRule
 *
 * todo：少接口：打包房、费用明细
 *
 * @date 2024/6/23 20:37
 *
 */
@Component
public class MapperOfCreateOrderRequestType
        extends AbstractMapper<Tuple1<WrapperOfCreateOrder>, CreateOrderRequestType> {
    private static final String SERVER_FORM_H5_CN = "m.ct/html5";
    private static final String SERVER_FORM_H5_EN = "m.ct/html5/en";
    private static final String SERVER_FORM_ONLINE_CN = "ct.ctrip.com";
    private static final String SERVER_FORM_ONLINE_EN = "ct.ctrip.com/en";
    private static final String SERVER_FORM_OFFLINE = "corpint.ctripcorp.com";

    private static final String FILE_NAME = "config.properties";
    private static final String OFFLINE_SERVER_FROM_QCONFIG_KEY = "offlineServerFrom";

    private static final Integer BOOK_TYPE_APPLY_MODIFY = 3;

    private static final String BALANCE_TYPE_PP = "PP";
    private static final String BALANCE_TYPE_USE_FG = "UseFG";
    private static final String BALANCE_TYPE_FG = "FG";

    private static final String ROOM_TYPE_M = "M";
    private static final String ROOM_TYPE_C = "C";
    private static final String GDS_TYPE_AMADEUS = "AMADEUS";

    /**
     * 人民币符号CNY
     */
    public static final String CNY = "CNY";
    /**
     * 人民币符号RMB
     */
    public static final String RMB = "RMB";
    public static final String TRAVEL_TOGETHER = "TRAVEL_TOGETHER";
    public static final String ROOM_TOGETHER = "ROOM_TOGETHER";
    public static final String NORMAL = "NORMAL";
    public static final String SHARE_ROOM = "SHARE_ROOM";
    private static final String REPEAT_BOOKING = "RepeatBooking";
    private static final String MODIFY_APPLY_FROM = "ModifyApplyFrom";


    private static final String COUNTRY_CODE_86 = "86";
    private static final String YYYY_MM_DD_T_HH_MM_SS_SSX = "yyyy-MM-dd'T'HH:mm:ssX";
    /**
     * 预订政策使用政策执行人政策
     */
    public static final String ORDER_CHECK_BOOK_CARD = "C";
    /**
     * 出行人按房间管控
     */
    public static final String HOTEL_PASSENGERS_CONTROL_FLAG = "R";

    /**
     * 商旅uid注册
     */
    public static final String BUSINESS_TRAVEL_REGISTER = "BUSINESS_TRAVEL_REGISTER";

    /**
     *  携程uid注册
     */
    public static final String TRIP_REGISTER = "TRIP_REGISTER";

    /**
     * 不需要注册
     */
    public static final String NO_REGISTER = "NO_REGISTER";
    public static final List<String> SSO_COST_CENTER_KEY =
        Arrays.asList("CostCenter1", "CostCenter2", "CostCenter3", "CostCenter4", "CostCenter5", "CostCenter6");

    public static final String BIZC = "BIZC";

    private static final int FIRST_NAME_INDEX = 1;

    @Override
    protected CreateOrderRequestType convert(Tuple1<WrapperOfCreateOrder> tuple) {
        WrapperOfCreateOrder wrapperOfCreateOrder = tuple.getT1();
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType =
                wrapperOfCreateOrder.getQueryCheckAvailContextResponseType();
        CreateTripResponseType createTripResponseType = wrapperOfCreateOrder.getCreateTripResponseType();
        OrderCreateRequestType orderCreateRequestType = wrapperOfCreateOrder.getOrderCreateRequestType();
        ResourceToken resourceToken = wrapperOfCreateOrder.getResourceToken();
        GetTravelPolicyContextResponseType getTravelPolicyContextResponseType =
                wrapperOfCreateOrder.getGetTravelPolicyContextResponseType();
        CheckTravelPolicyResponseType checkTravelPolicyResponseType =
                wrapperOfCreateOrder.getCheckTravelPolicyResponseType();
        WrapperOfAccount.AccountInfo accountInfo = wrapperOfCreateOrder.getAccountInfo();
        AllocationResultToken costAllocationToken = wrapperOfCreateOrder.getCostAllocationToken();
        SearchTripDetailResponseType searchTripDetailResponseType =
                wrapperOfCreateOrder.getSearchTripDetailResponseType();
        QueryHotelOrderDataResponseType queryHotelOrderDataResponseType =
                wrapperOfCreateOrder.getQueryHotelOrderDataResponseType();
        PayConfigResponseType payConfigResponseType = wrapperOfCreateOrder.getPayConfigResponseType();
        OrderCreateToken orderCreateToken = wrapperOfCreateOrder.getOrderCreateToken();
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo =
            wrapperOfCreateOrder.getCheckAvailContextInfo();
        GetPlatformRelationByUidResponseType getPlatformRelationByUidResponseType =
            wrapperOfCreateOrder.getGetPlatformRelationByUidResponseType();
        QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType
                = wrapperOfCreateOrder.getQueryBizModeBindRelationResponseType();
        SSOInfoQueryResponseType ssoInfoQueryResponseType = wrapperOfCreateOrder.getSsoInfoQueryResponseType();
        ApprovalFlowComputeResponseType approvalFlowComputeResponseType =
            wrapperOfCreateOrder.getApprovalFlowComputeResponseType();
        MatchApprovalFlowResponseType matchApprovalFlowResponseType =
            wrapperOfCreateOrder.getMatchApprovalFlowResponseType();
        GetInboundParameterResponseType getInboundParameterResponseType =
            wrapperOfCreateOrder.getGetInboundParameterResponseType();
        QconfigOfEmailInfoConfig qconfigOfEmailInfoConfig = wrapperOfCreateOrder.getQconfigOfEmailInfoConfig();
        RegisterResponseType registerResponseType = wrapperOfCreateOrder.getRegisterResponse();
        QueryIndividualAccountResponseType queryIndividualAccountResponseType =
            wrapperOfCreateOrder.getQueryIndividualAccountResponseType();
        Map<String, StrategyInfo> strategyInfoMap = wrapperOfCreateOrder.getStrategyInfoMap();
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig =
            wrapperOfCreateOrder.getQconfigOfCertificateInitConfig();


        CreateOrderRequestType result = new CreateOrderRequestType();

        result.setBaseInfo(buildBaseInfo(orderCreateRequestType, resourceToken, accountInfo));
        result.setRoomInfo(buildRoomInfo(orderCreateRequestType, queryCheckAvailContextResponseType, resourceToken,
            checkAvailContextInfo));
        result.setClientList(getClientListInfo(orderCreateRequestType, costAllocationToken, checkAvailContextInfo,
            qconfigOfCertificateInitConfig, strategyInfoMap));
        result.setContactorInfo(getContactorInfo(orderCreateRequestType, qconfigOfEmailInfoConfig));
        result.setPriceInfo(getPriceInfo(queryCheckAvailContextResponseType.getRoomInfo()));
        result.setGuaranteeInfo(
                getGuaranteeInfo(orderCreateRequestType, queryCheckAvailContextResponseType, resourceToken));
        result.setInvoiceInfo(getInvoiceInfo(orderCreateRequestType));
        result.setCorpOrderInfo(
            getCorpOrderInfo(checkTravelPolicyResponseType, orderCreateRequestType, getTravelPolicyContextResponseType,
                accountInfo, costAllocationToken, resourceToken, orderCreateToken, checkAvailContextInfo,
                getPlatformRelationByUidResponseType, queryBizModeBindRelationResponseType));
        result.setCouponList(getCouponList(orderCreateRequestType.getCouponInfoInput()));
        result.setRemarkInfo(getRemarkInfo(orderCreateRequestType.getRoomInfoInput()));
        result.setTripInfo(
            getTripInfo(accountInfo, orderCreateRequestType, createTripResponseType, searchTripDetailResponseType,
                orderCreateToken));
        result.setPaymentInfo(getPaymentInfo(orderCreateRequestType,
                queryCheckAvailContextResponseType, payConfigResponseType, resourceToken));

        // 根据文档定义可下线：result.setBookingScenarioInfo(buildBookingScenarioInfo(orderCreateRequestType, accountInfo, costAllocationToken, getSubAccountConfigResponseType));
        result.setExtInfo(
            getExtInfo(orderCreateRequestType, accountInfo, queryCheckAvailContextResponseType, resourceToken,
                ssoInfoQueryResponseType, approvalFlowComputeResponseType, matchApprovalFlowResponseType,
                orderCreateToken, checkAvailContextInfo, getPlatformRelationByUidResponseType,
                wrapperOfCreateOrder.getGetPackageRoomListResponseType(), queryBizModeBindRelationResponseType,
                registerResponseType, queryIndividualAccountResponseType, strategyInfoMap));
        result.setDeviceInfo(getDeviceInfo(orderCreateRequestType));
        result.setOfflineExtInfo(buildCreateOfflineExtEntity(orderCreateRequestType, checkAvailContextInfo,
            getInboundParameterResponseType));
        // 老X产品节点
        List<StrategyInfo> strategyInfos = Optional.ofNullable(wrapperOfCreateOrder.getOrderCreateRequestType())
            .map(OrderCreateRequestType::getStrategyInfos)
            .orElse(null);
        String corpId = Optional.ofNullable(wrapperOfCreateOrder.getOrderCreateRequestType()).map(OrderCreateRequestType::getIntegrationSoaRequestType)
            .map(IntegrationSoaRequestType::getUserInfo).map(UserInfo::getCorpId).orElse(null);
        result.setXProductInfoList(getXProductInfoList(wrapperOfCreateOrder.getGetPackageRoomListResponseType(), strategyInfos, corpId));
        // 新X产品节点
        result.setXProductList(buildXProductList(
                orderCreateRequestType.getAvailableRights(),
                Optional.ofNullable(queryCheckAvailContextResponseType.getRoomInfo().getXProductInfo())
                        .map(XProductInfoType::getXProductEntity).orElse(null),
            wrapperOfCreateOrder.getGetPackageRoomListResponseType(),
            strategyInfos,
            corpId));
        result.setOriOrderInfo(
                buildCreateOriOrderEntity(
                        queryCheckAvailContextResponseType,
                        queryHotelOrderDataResponseType,
                        orderCreateRequestType, resourceToken));
        result.setInsuranceInfo(
            getInsuranceInfo(orderCreateRequestType, checkAvailContextInfo, qconfigOfCertificateInitConfig,
                strategyInfoMap));
        // 根据文档定义可下线：http://conf.ctripcorp.com/pages/viewpage.action?pageId=********** result.setCompatibilityInfo();
        return result;
    }

    protected boolean needNewPackageProduct(List<StrategyInfo> strategyInfos, String corpId) {
        if (StrategyOfBookingInitUtil.needOldPkgProduct(strategyInfos)) {
            return false;
        }
        return QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.USE_NEW_PACKAGE, corpId);
    }

    protected List<TokensEntity> buildXProductList(List<AvailableRight> availableRights,
        List<XProductEntityType> xProductEntity,
        GetPackageRoomListResponseType getPackageRoomListResponseType,
        List<StrategyInfo> strategyInfos,
        String corpId) {
        List<TokensEntity> xProductList = new ArrayList<>();
        List<TokensEntity> rightXProductList = getRights(availableRights);
        if (!CollectionUtil.isEmpty(rightXProductList)) {
            xProductList.addAll(rightXProductList);
        }
        List<TokensEntity> tokensEntityList = getXProductList(xProductEntity);
        if (!CollectionUtil.isEmpty(tokensEntityList)) {
            xProductList.addAll(tokensEntityList);
        }
        List<TokensEntity> packageList = buildPackageList(Optional.ofNullable(getPackageRoomListResponseType)
            .map(GetPackageRoomListResponseType::getProductStaticInfoList)
            .orElse(null), strategyInfos, corpId);
        if (!CollectionUtil.isEmpty(packageList)) {
            xProductList.addAll(packageList);
        }
        return CollectionUtil.isEmpty(xProductList) ? null : xProductList;
    }

    private List<TokensEntity> buildPackageList(List<XProductStaticInfoType> productStaticInfoList, List<StrategyInfo> strategyInfos, String corpId) {
        if (!needNewPackageProduct(strategyInfos, corpId)) {
            return null;
        }
        if (CollectionUtil.isEmpty(productStaticInfoList)) {
            return null;
        }
        XProductStaticInfoType xProductStaticInfoType = CollectionUtil.findFirst(productStaticInfoList, Objects::nonNull);
        if (xProductStaticInfoType == null || CollectionUtil.isEmpty(xProductStaticInfoType.getProductSpuInfoList())) {
            return null;
        }
        if (xProductStaticInfoType.getPackageId() == null && StringUtil.isBlank(xProductStaticInfoType.getPackageToken())) {
            return null;
        }
        return xProductStaticInfoType.getProductSpuInfoList().stream().filter(Objects::nonNull)
            .filter(z -> CollectionUtil.isNotEmpty(z.getProductSkuInfoList()))
            .flatMap(x -> x.getProductSkuInfoList().stream())
            .filter(Objects::nonNull)
            .map(t -> {
                TokensEntity xProductInfo = new TokensEntity();
                xProductInfo.setProductType(COMMON_PRODUCT_TYPE);
                xProductInfo.setQuantum(t.getQuantity());
                xProductInfo.setProductId(t.getProductId());
                return xProductInfo;
            }).collect(Collectors.toList());


    }

    @Override
    protected ParamCheckResult check(Tuple1<WrapperOfCreateOrder> tuple) {
        WrapperOfCreateOrder wrapperOfCreateOrder = tuple.getT1();
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType =
                wrapperOfCreateOrder.getQueryCheckAvailContextResponseType();
        CreateTripResponseType createTripResponseType = wrapperOfCreateOrder.getCreateTripResponseType();
        OrderCreateRequestType orderCreateRequestType = wrapperOfCreateOrder.getOrderCreateRequestType();
        ResourceToken resourceToken = wrapperOfCreateOrder.getResourceToken();
        GetTravelPolicyContextResponseType getTravelPolicyContextResponseType =
                wrapperOfCreateOrder.getGetTravelPolicyContextResponseType();
        CheckTravelPolicyResponseType checkTravelPolicyResponseType =
                wrapperOfCreateOrder.getCheckTravelPolicyResponseType();
        WrapperOfAccount.AccountInfo accountInfo = wrapperOfCreateOrder.getAccountInfo();
        AllocationResultToken costAllocationToken = wrapperOfCreateOrder.getCostAllocationToken();
        SearchTripDetailResponseType searchTripDetailResponseType =
                wrapperOfCreateOrder.getSearchTripDetailResponseType();
        QueryHotelOrderDataResponseType queryHotelOrderDataResponseType =
                wrapperOfCreateOrder.getQueryHotelOrderDataResponseType();
        OrderCreateToken orderCreateToken = wrapperOfCreateOrder.getOrderCreateToken();
        checkCreateTripResponseType(createTripResponseType, orderCreateRequestType, accountInfo, orderCreateToken);
        return null;
    }

    protected void checkCreateTripResponseType(CreateTripResponseType createTripResponseType,
        OrderCreateRequestType orderCreateRequestType, WrapperOfAccount.AccountInfo accountInfo,
        OrderCreateToken orderCreateToken) {
        if (!OrderCreateProcessorOfUtil.requireCreateTrip(orderCreateRequestType, accountInfo, orderCreateToken)) {
            return;
        }
        if (createTripResponseType != null && TemplateNumberUtil.isNotZeroAndNull(createTripResponseType.getTripId())) {
            return;
        }
        Integer logErrorCode = Optional.ofNullable(createTripResponseType).map(CreateTripResponseType::getResponseCode)
            .orElse(OrderCreateErrorEnum.CREATE_TRIP_ERROR.getErrorCode());
        String friendlyMessage = SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessageHasDefault(
            SoaErrorSharkKeyConstant.SERVICE_NAME_TRIP_ORDER_SERVICE, SoaErrorSharkKeyConstant.ACTION_NAME_CREATE_TRIP,
            String.valueOf(logErrorCode));
        if (StringUtil.isBlank(friendlyMessage)) {
            friendlyMessage =
                Optional.ofNullable(createTripResponseType).map(CreateTripResponseType::getResponseDesc).orElse(null);
        }
        throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.CREATE_TRIP_ERROR.getErrorCode(),
            friendlyMessage, friendlyMessage, String.valueOf(logErrorCode));
    }

    public CreateBaseEntity buildBaseInfo(OrderCreateRequestType orderCreateRequestType, ResourceToken resourceToken, WrapperOfAccount.AccountInfo accountInfo) {
        if (orderCreateRequestType == null) {
            return null;
        }
        CreateBaseEntity result = new CreateBaseEntity();
        result.setChannel(buildChannel(orderCreateRequestType.getIntegrationSoaRequestType()));
        result.setSecondaryChannel(
            Optional.ofNullable(orderCreateRequestType.getClientInfo()).map(ClientInfo::getSecondaryChannel)
                .orElse(null));
        result.setLocale(buildLocale(orderCreateRequestType));
        result.setServerFrom(buildServerFrom(orderCreateRequestType.getIntegrationSoaRequestType()));
        result.setWsId(resourceToken.getReservationResourceToken().getWsId());
        result.setTeamRoom(
                BooleanUtil.parseStr(Boolean.TRUE).equals(Optional.ofNullable(orderCreateRequestType.getTeamRoomInfo()).map(
                        TeamRoomInfo::getTeamRoom).orElse(null)));
        result.setBookType(buildBookType(orderCreateRequestType.getStrategyInfos()));
        result.setPos(
                HostUtil.mapToAccountPos(orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getPos()));
        result.setPlatformType(Optional.ofNullable(orderCreateRequestType.getFlashStayInput()).map(FlashStayInput::getFlashPlatFrom).orElse(null));
        result.setUserSettings(buildCreateUserSettingInfo(accountInfo, orderCreateRequestType));
        return result;
    }

    /**
     * offline订单语言
     */
    private static final String ORDER_LANGUAGE = "ORDER_LANGUAGE";

    private static String buildLocale(OrderCreateRequestType orderCreateRequestType) {
        if (orderCreateRequestType == null) {
            return null;
        }
        StrategyInfo strategyInfo = CollectionUtil.findFirst(orderCreateRequestType.getStrategyInfos(),
                t -> t != null && StringUtil.equalsIgnoreCase(ORDER_LANGUAGE, t.getStrategyKey()));
        if (strategyInfo != null && StringUtil.isNotBlank(strategyInfo.getStrategyValue())) {
            return strategyInfo.getStrategyValue();
        }
        return Optional.ofNullable(orderCreateRequestType.getIntegrationSoaRequestType()).map(IntegrationSoaRequestType::getLanguage).orElse(null);
    }

    private static final String CHANNEL_OFFLINE = "Offline";
    public String buildChannel(IntegrationSoaRequestType integrationSoaRequestType) {
        if (integrationSoaRequestType.getSourceFrom() == SourceFrom.Offline) {
            return CHANNEL_OFFLINE;
        }
        return RequestHeaderUtil.getChannel(integrationSoaRequestType);
    }

    private CreateUserSettingEntity buildCreateUserSettingInfo(WrapperOfAccount.AccountInfo accountInfo,
                                                             OrderCreateRequestType orderCreateRequestType) {
        CreateUserSettingEntity createUserSettingEntityTO = new CreateUserSettingEntity();
        if (!CommonConstant.CNY.equalsIgnoreCase(accountInfo.getCurrency()) && !CommonConstant.RMB.equalsIgnoreCase(
                accountInfo.getCurrency())) {
            createUserSettingEntityTO.setCustomCurrency(accountInfo.getCurrency());
        }
        createUserSettingEntityTO.setTravelPolicyControlMode(
            accountInfo.getTravelPolicyControlMode(orderCreateRequestType.getCorpPayInfo()));
        createUserSettingEntityTO.setBookRoomMode(buildBookRoomMode(accountInfo, orderCreateRequestType));
        createUserSettingEntityTO.setBookMode(buildBookMode(orderCreateRequestType));
        return createUserSettingEntityTO;
    }

    protected String buildBookMode(OrderCreateRequestType orderCreateRequestType) {
        if (SHARE_ROOM.equalsIgnoreCase(
                Optional.ofNullable(orderCreateRequestType.getBookModeInfo()).map(BookModeInfo::getBookingType)
                        .orElse(null))) {
            return SHARE_ROOM;
        }
        return null;
    }

    private String buildBookRoomMode(WrapperOfAccount.AccountInfo accountInfo,
                                         OrderCreateRequestType orderCreateRequestType) {
        String policyUidCanBeNull =
                Optional.ofNullable(orderCreateRequestType.getHotelPolicyInput()).map(HotelPolicyInput::getPolicyInput)
                        .map(PolicyInput::getPolicyUid).orElse(null);
        if (accountInfo.isPolicyModel() && accountInfo.isTravelStandPolicy()
            && StringUtils.isNotBlank(policyUidCanBeNull)
            && buildTravelPolicyOrAllPolicy(accountInfo.getHotelPolicyScope())) {
            return TRAVEL_TOGETHER;
        }
        if (CollectionUtil.isNotEmpty(orderCreateRequestType.getHotelBookPassengerInputs())) {
            Map<Integer, List<HotelBookPassengerInput>> roomPassenger =
                    orderCreateRequestType.getHotelBookPassengerInputs().stream().filter(Objects::nonNull).filter(
                                    hotelBookPassengerInput -> null != hotelBookPassengerInput.getHotelPassengerInput().getRoomIndex())
                            .collect(Collectors.groupingBy(
                                    hotelBookPassengerInput -> hotelBookPassengerInput.getHotelPassengerInput().getRoomIndex()));
            boolean roomTogether = roomPassenger.values().stream().anyMatch(r -> r.size() > NumberUtils.INTEGER_ONE);
            if (roomTogether) {
                return ROOM_TOGETHER;
            }
        }
        return NORMAL;
    }

    private static boolean buildTravelPolicyOrAllPolicy(String hotelPolicyScope) {
        if (StringUtil.isBlank(hotelPolicyScope)) {
            return false;
        }
        String[] scopes = hotelPolicyScope.split(",");
        boolean travelScope = Arrays.stream(scopes).anyMatch("A"::equalsIgnoreCase);
        boolean otherScope = Arrays.stream(scopes).anyMatch("B"::equalsIgnoreCase);
        if (travelScope && otherScope) {
            return true;
        } else if (travelScope) {
            return true;
        }
        return false;
    }

    private Integer buildBookType(List<StrategyInfo> strategyInfos) {
        if (CollectionUtil.isEmpty(strategyInfos)) {
            return null;
        }
        return StrategyOfBookingInitUtil.onlyApplyModify(strategyInfos) ? BOOK_TYPE_APPLY_MODIFY : null;
    }

    private String buildServerFrom(IntegrationSoaRequestType integrationSoaRequestType) {
        if (integrationSoaRequestType == null || integrationSoaRequestType.getSourceFrom() == null) {
            return SERVER_FORM_H5_CN;
        }

        SourceFrom sourceFrom = integrationSoaRequestType.getSourceFrom();
        String language = integrationSoaRequestType.getLanguage();

        switch (sourceFrom) {
            case H5:
            case Native:
            case CRN:
                return buildValByLanguage(SERVER_FORM_H5_CN, SERVER_FORM_H5_EN, SERVER_FORM_H5_CN, language);
            case Online:
                return buildValByLanguage(SERVER_FORM_ONLINE_CN, SERVER_FORM_ONLINE_EN, SERVER_FORM_ONLINE_CN, language);
            case Offline:
                QConfigFile configFile = QConfigUtil.getFile(FILE_NAME);
                return configFile.getConfigValue(OFFLINE_SERVER_FROM_QCONFIG_KEY, SERVER_FORM_OFFLINE);
            default:
                return SERVER_FORM_H5_CN;
        }
    }

    private String buildValByLanguage(String zhCN, String mu, String defaultStr, String language) {
        LanguageLocaleEnum localeEnum = Optional.ofNullable(LanguageLocaleEnum.getByLanguageLocaleIgnoreCase(language))
                .orElse(LanguageLocaleEnum.ZH_CN);
        String resultStr = localeEnum == LanguageLocaleEnum.ZH_CN ? zhCN : mu;
        return StringUtils.isBlank(resultStr) ? defaultStr : resultStr;
    }

    private CreateRoomEntity buildRoomInfo(
            OrderCreateRequestType orderCreateRequestType,
            QueryCheckAvailContextResponseType queryCheckAvailContextResponseType,
            ResourceToken resourceToken, WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo) {
        BookRoomInfoEntity roomInfo = queryCheckAvailContextResponseType.getRoomInfo();
        QueryLimitArrivalDateTimeType limitArrivalDateTimeType =
                Optional.ofNullable(queryCheckAvailContextResponseType.getBookingRules())
                        .map(QueryBookingRulesType::getLimitArrivalDateTime).orElse(null);
        if (roomInfo == null || limitArrivalDateTimeType == null) {
            return null;
        }
        CreateRoomEntity result = new CreateRoomEntity();
        String checkIn = Optional.ofNullable(orderCreateRequestType.getHotelBookInput())
                .map(HotelBookInput::getHotelDateRangeInfo)
                .map(HotelDateRangeInfo::getCheckIn).orElse(null);
        LocalDate checkInLocalDate = LocalDate.parse(checkIn, DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD));
        result.setDailyRoomMarkUpInfoList(buildDailyAddPriceInfo(orderCreateRequestType, resourceToken));
        result.setMarkUpFlag(CollectionUtil.isNotEmpty(result.getDailyRoomMarkUpInfoList()));
        result.setEarliestTimeUTC(buildArrivalTimeUtc(null, limitArrivalDateTimeType.getArrivalFromUTC()));
        String arriveTimeTokenStr = Optional.ofNullable(orderCreateRequestType.getArriveTimeInput())
                .map(ArriveTimeInput::getArriveTimeToken).orElse(null);
        ArriveTimeToken arriveTimeToken = TokenParseUtil.parseToken(arriveTimeTokenStr, ArriveTimeToken.class);
        result.setLatestArrivalTimeUTC(buildLatestArrivalTimeUTC(
            Optional.ofNullable(arriveTimeToken).map(ArriveTimeToken::getArriveTimeUTC).orElse(null),
            limitArrivalDateTimeType.getArrivalEndUTC(), orderCreateRequestType));

        result.setEarliestCheckInLocalTime(getArrivalLocalTime(
                null,
                limitArrivalDateTimeType.getLocalEarlyArrivalTime(),
                checkInLocalDate));
        result.setLatestCheckInLocalTime(buildLatestCheckInLocalTime(
            Optional.ofNullable(arriveTimeToken).map(ArriveTimeToken::getArriveTime).orElse(null),
            Optional.ofNullable(queryCheckAvailContextResponseType.getBookingRules())
                .map(QueryBookingRulesType::getLimitArrivalDateTime)
                .map(QueryLimitArrivalDateTimeType::getLocalLastArrivalTime).orElse(null), checkInLocalDate,
            orderCreateRequestType));
        HotelBookInput hotelBookInput = orderCreateRequestType.getHotelBookInput();
        if (hotelBookInput != null) {
            result.setRoomQuantity(hotelBookInput.getRoomQuantity());
            result.setGuestQuantity(orderCreateRequestType.getHotelBookPassengerInputs().size());
        }
        String hourRoomTokenStr =
            Optional.ofNullable(orderCreateRequestType.getHourRoomInput()).map(HourRoomInput::getHourRoomToken)
                .orElse(null);
        HourRoomToken hourRoomToken = TokenParseUtil.parseToken(hourRoomTokenStr, HourRoomToken.class);
        if (Boolean.TRUE.equals(roomInfo.isHourlyRoom()) && hourRoomToken != null) {
            HourlyRoomEntity hourlyRoomEntity = new HourlyRoomEntity();
            hourlyRoomEntity.setCheckInLocalTime(
                    CalendarUtil.format(hourRoomToken.getStartTime(), DateUtil.YYYY_MM_DD_HH_mm_ss));
            hourlyRoomEntity.setCheckOutLocalTime(
                    CalendarUtil.format(hourRoomToken.getEndTime(), DateUtil.YYYY_MM_DD_HH_mm_ss));
            result.setEarliestCheckInLocalTime(
                    CalendarUtil.format(hourRoomToken.getStartTime(), DateUtil.YYYY_MM_DD_HH_mm_ss));
            result.setLatestCheckInLocalTime(
                    CalendarUtil.format(hourRoomToken.getStartTime(), DateUtil.YYYY_MM_DD_HH_mm_ss));
            result.setHourlyRoomInfo(hourlyRoomEntity);
            // 钟点房 转化成UTC时间
            String timeZoneId = buildZoneId(orderCreateRequestType.getCityInput());
            if (StringUtils.isNotEmpty(timeZoneId)) {
                result.setEarliestTimeUTC(
                    buildArrivalTimeUtc(null, limitArrivalDateTimeType.getArrivalFromUTC(), roomInfo, hourRoomToken,
                        orderCreateRequestType));
                result.setLatestArrivalTimeUTC(
                    buildArrivalTimeUtc(null, limitArrivalDateTimeType.getArrivalFromUTC(), roomInfo, hourRoomToken,
                        orderCreateRequestType));
            }
        }

        List<ComplicatedBedToken> complicatedBedList = Optional.ofNullable(orderCreateRequestType.getCustomBedInput())
                .map(CustomBedInput::getBedToken).map(x -> TokenParseUtil.parseToken(x, BedInfoToken.class))
                .map(BedInfoToken::getComplicatedBedList).orElse(null);
        if (CollectionUtil.isNotEmpty(complicatedBedList)) {
            result.setComplicatedBedInfoList(complicatedBedList.stream().map(o -> {
                ComplicatedBedInfoType complicatedBedInfoType = new ComplicatedBedInfoType();
                complicatedBedInfoType.setBedQuantity(o.getBedQuantity());
                complicatedBedInfoType.setBedTypeId(o.getBedTypeId());
                return complicatedBedInfoType;
            }).collect(Collectors.toList()));
        }
        // 选用闪住支付方式则为闪住房型
        result.setFlashOrder(getFlashOrder(orderCreateRequestType.getHotelPayTypeInput()));
        // 最晚取消时间（当地）（格式：yyyy-MM-dd HH:mm:ss）
        result.setLatestCancelLocalTime(checkAvailContextInfo.getLocalLastCancelTime());
        return result;
    }

    protected String buildZoneId(CityInput cityInput) {
        if (cityInput == null) {
            return null;
        }
        try {
            return HotelTimeZoneUtil.getInstance().getTimeZoneId(cityInput.getCityId());
        } catch (TimeZoneNotFoundException e) {
            LogUtil.loggingClogOnly(LogLevelEnum.Error, MapperOfCreateOrderRequestType.class, "buildZoneId-error", e,
                null);
        }
        return null;
    }

    protected int buildOffsetMinute(String timeZoneId, LocalDateTime earliestLocalDateTime) {
        if (StringUtils.isBlank(timeZoneId) || earliestLocalDateTime == null) {
            return 0;
        }
        return HotelTimeZoneUtil.getInstance().getOffsetMinute(ZoneId.of(timeZoneId), earliestLocalDateTime);
    }

    protected String buildArrivalTimeUtc(String inputArrivalTime, String roomArrivalTime, BookRoomInfoEntity roomInfo,
        HourRoomToken hourRoomToken, OrderCreateRequestType orderCreateRequestType) {
        if (Boolean.TRUE.equals(roomInfo.isHourlyRoom()) && hourRoomToken != null) {
            // 钟点房 转化成UTC时间
            String timeZoneId = buildZoneId(orderCreateRequestType.getCityInput());
            if (StringUtils.isNotEmpty(timeZoneId) && hourRoomToken.getStartTime() != null) {
                LocalDateTime earliestLocalDateTime =
                    LocalDateTime.parse(CalendarUtil.format(hourRoomToken.getStartTime(), DateUtil.YYYY_MM_DD_HH_mm_ss),
                        DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD_HH_mm_ss));
                int offsetMinute = buildOffsetMinute(timeZoneId, earliestLocalDateTime);
                LocalDateTime earliestLocalDateTimeUTC = earliestLocalDateTime.minusMinutes(offsetMinute);
                return DateUTCUtils.convert2UTC(earliestLocalDateTimeUTC);
            }
        }
        return buildArrivalTimeUtc(inputArrivalTime, roomArrivalTime);
    }

    /**
     * 蓝色空间降噪，bagashi资源客户数选中时不传入,不传其实是有常规bug的 后续技改统一 todo：跟进文档 http://conf.ctripcorp.com/pages/viewpage.action?pageId=4153740932
     * 非bagashi，现有逻辑不动
     *
     * @param inputArrivalTime
     * @param roomArrivalTime
     * @return
     */
    private String buildLatestArrivalTimeUTC(String inputArrivalTime, String roomArrivalTime,
        OrderCreateRequestType orderCreateRequestType) {
        if (StringUtil.isNotBlank(orderCreateRequestType.getChooseLocalArriveTime())) {
            return null;
        }
        return buildArrivalTimeUtc(inputArrivalTime, roomArrivalTime);
    }

    private String buildArrivalTimeUtc(String inputArrivalTime, String roomArrivalTime) {
        // 定义时间常量
        return Optional.ofNullable(inputArrivalTime).orElse(roomArrivalTime);
    }

    public static Calendar localDateTime2Calendar(LocalDateTime localDateTime) {
        if (localDateTime != null) {
            ZonedDateTime zonedDateTime = localDateTime.atZone(ZoneId.systemDefault());
            return GregorianCalendar.from(zonedDateTime);
        }
        return null;
    }

    /**
     * 蓝色空间降噪，bagashi资源以客户数选中的为准 但是此方案其实可以参考国内站到店时间的设计，后续技改统一 todo：跟进文档 http://conf.ctripcorp.com/pages/viewpage.action?pageId=4153740932
     * 非bagashi，现有逻辑不动
     *
     * @param inputArrivalTime
     * @param roomArrivalTime
     * @param checkIn
     * @return
     */
    protected String buildLatestCheckInLocalTime(Calendar inputArrivalTime, String roomArrivalTime, LocalDate checkIn,
        OrderCreateRequestType orderCreateRequestType) {
        // 蓝色空间降噪，bagashi资源以客户数选中的为准
        if (StringUtil.isNotBlank(orderCreateRequestType.getChooseLocalArriveTime())) {
            return orderCreateRequestType.getChooseLocalArriveTime();
        }
        return getArrivalLocalTime(inputArrivalTime, roomArrivalTime, checkIn);
    }

    private String getArrivalLocalTime(Calendar inputArrivalTime,
                                       String roomArrivalTime, LocalDate checkIn) {
        if (inputArrivalTime != null) {
            return CalendarUtil.format(inputArrivalTime, DateUtil.YYYY_MM_DD_HH_mm_ss);
        }
        if (StringUtil.isNotBlank(roomArrivalTime)) {
            return roomArrivalTime;
        }
        return checkIn.plusDays(1).atTime(6, 0)
                .format(DateTimeFormatter.ofPattern(DateUtil.YYYY_MM_DD_HH_mm_ss));
    }

    private String convertTime(String formPattern, String toPattern, String time) {
        try {
            return CalendarUtil.format(CalendarUtil.parseCalendar(time, formPattern), toPattern);
        } catch (ParseException e) {
            LogUtil.loggingClogOnly(LogLevelEnum.Error, MapperOfCreateOrderRequestType.class,
                    "MapperOfCreateOrderRequestType.getTime", time, null);
            return null;
        }
    }

    private static final String PAYER_CORP_PAY = "CORP_PAY";
    private static final String PAYMENT_TYPE_FLASH_STAY_PAY = "FLASH_STAY_PAY";

    private boolean getFlashOrder(List<HotelPayTypeInput> hotelPayTypeInputs) {
        return HotelPayTypeUtil.getRoomPayType(hotelPayTypeInputs) == HotelPayTypeEnum.FLASH_STAY_PAY;
    }

    private List<DailyRoomMarkUpInfo> buildDailyAddPriceInfo(OrderCreateRequestType orderCreateRequestType,
                                                             ResourceToken resourceToken) {
        String checkIn = orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo().getCheckIn();
        String checkOut = orderCreateRequestType.getHotelBookInput().getHotelDateRangeInfo().getCheckOut();

        RoomPriceResourceToken roomPriceResourceToken = Optional.ofNullable(resourceToken.getBookInitResourceToken())
                .map(BookInitResourceToken::getRoomPriceResourceToken).orElse(null);
        if (roomPriceResourceToken == null || roomPriceResourceToken.getCustomRoomAdditionalAmount() == null) {
            return null;
        }
        List<String> detailDays = getDetailDays(checkIn, checkOut, DateUtil.YYYY_MM_DD, DateUtil.YYYY_MM_DD);
        if (CollectionUtil.isEmpty(detailDays)) {
            return null;
        }

        MarkUpAmountInfo addPriceInfo = new MarkUpAmountInfo();
        BigDecimal payMarkUpAmount = roomPriceResourceToken.getCustomRoomAdditionalAmount();
        addPriceInfo.setPayMarkUpAmount(payMarkUpAmount);
        addPriceInfo.setOriginalMarkUpAmount(roomPriceResourceToken.getOriginRoomAdditionalAmount());
        addPriceInfo.setCnyMarkUpAmount(roomPriceResourceToken.getCnyRoomAdditionalAmount());
        addPriceInfo.setSettlementMakeUpAmount(payMarkUpAmount);

        return detailDays.stream().map(m -> {
            DailyRoomMarkUpInfo info = new DailyRoomMarkUpInfo();
            info.setRoomDate(m);
            info.setMarkUpAmountInfo(addPriceInfo);
            return info;
        }).collect(Collectors.toList());
    }

    public static List<String> getDetailDays(String startDate, String endDate, String format, String outPutFormat) {
        Date start = DateUtil.parseDate(startDate, format);
        Date end = DateUtil.parseDate(endDate, format);
        int days = (int) DateUtil.between(start, end, DateUnitEnum.DAY);
        days = days > 0 ? days : (int) DateUtil.between(end, start, DateUnitEnum.DAY);
        if (days == 0) {
            return Collections.emptyList();
        }
        List<String> result = new ArrayList<>();
        Date newStart = start.compareTo(end) > 0 ? end : start;
        for (int i = 0; i < days; i++) {
            result.add(DateUtil.formatDate(DateUtil.addMinutes(newStart, i * 1440), outPutFormat));
        }
        return result;
    }

    public List<ClientEntity> getClientListInfo(OrderCreateRequestType orderCreateRequestType,
                                                AllocationResultToken costAllocationToken,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,  Map<String, StrategyInfo> strategyInfoMap) {
        List<HotelBookPassengerInput> passengerInputList = orderCreateRequestType.getHotelBookPassengerInputs();
        if (CollectionUtil.isEmpty(passengerInputList)) {
            return Collections.emptyList();
        }
        return passengerInputList.stream().map(passengerInput -> {
            if (passengerInput == null || passengerInput.getHotelPassengerInput() == null) {
                return null;
            }
            HotelPassengerInput hotelPassengerInput = passengerInput.getHotelPassengerInput();
            ClientEntity clientEntity = new ClientEntity();
            clientEntity.setUid(Optional.ofNullable(hotelPassengerInput.getUid()).orElse(StringUtil.EMPTY));
            clientEntity.setRoomIndex(hotelPassengerInput.getRoomIndex());
            clientEntity.setEmployeeID(
                Optional.ofNullable(hotelPassengerInput.getEmployeeId()).orElse(StringUtil.EMPTY));
            clientEntity.setInfoId(StringUtil.isBlank(hotelPassengerInput.getInfoId()) ? 0 :
                    NumberUtil.parseInt(hotelPassengerInput.getInfoId()));
            clientEntity.setName(
                    OrderCreateProcessorOfUtil.getUseName(passengerInput, orderCreateRequestType.getCityInput().getCityId(),
                            checkAvailInfo, qconfigOfCertificateInitConfig, strategyInfoMap));
            if (null != costAllocationToken) {
                clientEntity.setAllocationSettlementAmount(
                        buildShareAmount(costAllocationToken.getSettleAllocationAmount(),
                            buildUseId(hotelPassengerInput), hotelPassengerInput.getTemporaryId()));
                clientEntity.setAllocationOriginAmount(
                        buildShareAmount(costAllocationToken.getOriginAllocationAmount(),
                            buildUseId(hotelPassengerInput), hotelPassengerInput.getTemporaryId()));
                clientEntity.setShareOrderAmount(
                        buildShareAmount(costAllocationToken.getCnyAllocationAmount(),
                            buildUseId(hotelPassengerInput), hotelPassengerInput.getTemporaryId()));
            }
            clientEntity.setEmployee
                    (BooleanUtil.parseStr(Boolean.TRUE).equals(hotelPassengerInput.getEmployee()));
            clientEntity.setExternalTraveller(
                    BooleanUtil.parseStr(Boolean.TRUE).equals(hotelPassengerInput.getExternal()));
            clientEntity.setEarnPoints(
                buildEarnPoints(orderCreateRequestType, strategyInfoMap, passengerInputList, passengerInput));
            if (passengerInput.getPhoneInfo() != null
                    && StringUtil.isNotBlank(passengerInput.getPhoneInfo().getTransferPhoneNo())
                    && StringUtil.isNotBlank(passengerInput.getPhoneInfo().getCountryCode())) {
                clientEntity.setCountryCode(passengerInput.getPhoneInfo().getCountryCode());
                clientEntity.setMobilePhone(
                    encryptData(KeyType.Phone, passengerInput.getPhoneInfo().getTransferPhoneNo(),
                        orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId()));
            }
            if (passengerInput.getEmailInfo() != null
                    && StringUtil.isNotBlank(passengerInput.getEmailInfo().getTransferEmail())) {
                clientEntity.setEmail(encryptData(KeyType.Mail, passengerInput.getEmailInfo().getTransferEmail(),
                        orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId()));
            }
            clientEntity.setClientId(getClientId(passengerInput));
            clientEntity.setNationality(buildNationalityCode(passengerInput, checkAvailInfo));
            clientEntity.setMainGuest(
                    BooleanUtil.parseStr(Boolean.TRUE).equals(passengerInput.getIsRegistrant()));
            clientEntity.setGender(buildGender(
                Optional.ofNullable(passengerInput.getPassengerBasicInfo()).map(PassengerBasicInfo::getGender)
                    .orElse(null)));
            String eName = OrderCreateProcessorOfUtil.getEname(passengerInput, checkAvailInfo,
                qconfigOfCertificateInitConfig, strategyInfoMap);
            List<String> eNames = Optional.ofNullable(eName).map(name -> name.split("/"))
                    .map(Arrays::asList).orElse(null);
            if (!CollectionUtils.isEmpty(eNames)) {
                clientEntity.setFullName(eName);
                clientEntity.setLastName(eNames.get(0));
                if (eNames.size() > FIRST_NAME_INDEX) {
                    clientEntity.setFirstName(eNames.get(FIRST_NAME_INDEX));
                }
            }
            toCertificate(clientEntity, passengerInput, checkAvailInfo);
            return clientEntity;
        }).collect(Collectors.toList());
    }

    protected String buildGender(String gender) {
        if (StringUtil.isBlank(gender)) {
            return UN_KNOWN_STR;
        }
        switch (gender) {
            case MALE_STR:
                return MALE_STR;
            case FEMALE_STR:
                return FEMALE_STR;
            default:
                return UN_KNOWN_STR;
        }
    }

    protected String buildNationalityCode(HotelBookPassengerInput hotelBookPassengerInput,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
        return Optional.ofNullable(
                OrderCreateProcessorOfUtil.buildNationalityInfo(hotelBookPassengerInput, checkAvailInfo))
            .map(NationalityInfo::getNationalityCode).orElse(null);
    }

    protected boolean buildEarnPoints(OrderCreateRequestType orderCreateRequestType,
        Map<String, StrategyInfo> strategyInfoMap, List<HotelBookPassengerInput> passengerInputList,
        HotelBookPassengerInput passengerInput) {
        if (StrategyOfBookingInitUtil.earnPointUseFirstPassenger(strategyInfoMap)) {
            return passengerInputList.indexOf(passengerInput) == 0;
        }
        return BooleanUtil.parseStr(Boolean.TRUE).equals(
            Optional.ofNullable(orderCreateRequestType.getPointsInfo()).map(PointsInfo::getNeedPoints).orElse(null));
    }

    protected String encryptData(KeyType keyType, String needEncryptData, String corpId) {
        if (StringUtil.isBlank(needEncryptData)) {
            return needEncryptData;
        }
        if (!QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.NEED_ENCRYPT_SENSITIVE_INFORMATION, corpId)) {
            return needEncryptData;
        }
        return CoreInfoUtil.encrypt(new InfoKey(keyType, needEncryptData));
    }


    private String buildUseId(HotelPassengerInput hotelPassengerInput) {
        if (StringUtil.isNotBlank(hotelPassengerInput.getUid())) {
            return hotelPassengerInput.getUid();
        }
        return hotelPassengerInput.getInfoId();
    }

    private BigDecimal buildShareAmount(Map<String, BigDecimal> shareAmount, String useId, String temporaryId) {
        if (MapUtils.isEmpty(shareAmount) || StringUtils.isEmpty(useId)) {
            return null;
        }
        if (StringUtil.isNotBlank(temporaryId)
                && shareAmount.containsKey(temporaryId)
                && MathUtils.isGreaterThanZero(shareAmount.get(temporaryId))) {
            return shareAmount.get(temporaryId);
        }
        return shareAmount.get(useId);
    }

    public String getClientId(HotelBookPassengerInput passengerInput) {
        if (passengerInput == null) {
            return null;
        }
        if (!StringUtil.isBlank(passengerInput.getHotelPassengerInput().getUid())) {
            return passengerInput.getHotelPassengerInput().getUid();
        }

        return passengerInput.getHotelPassengerInput().getInfoId();
    }

    private static final String IDENTITY_CARD = "IDENTITY_CARD";
    private static final String PASSPORT = "PASSPORT";
    private static final String STUDENT_ID_CARD = "STUDENT_ID_CARD";
    private static final String MILITARY_CARD = "MILITARY_CARD";
    private static final String DRIVING_LICENSE = "DRIVING_LICENSE";
    private static final String HOMEPERMIT = "HOMEPERMIT";
    private static final String MTP = "MTP";
    private static final String OTHERDOCUMENT = "OTHERDOCUMENT";
    private static final String HKMACPASS = "HKMACPASS";
    private static final String SEAMAN_CARD = "SEAMAN_CARD";
    private static final String FOREIGNER_PERMANENT_RESIDENCE_CARD = "FOREIGNER_PERMANENT_RESIDENCE_CARD";
    private static final String TAIWANPASS = "TAIWANPASS";
    private static final String TRAVELDOCUMENT = "TRAVELDOCUMENT";
    private static final String FOREIGNER_PERMANENT_RESIDENT_ID_CARD = "FOREIGNER_PERMANENT_RESIDENT_ID_CARD";
    private static final String RESIDENCE_PERMIT_HKT = "RESIDENCEPERMITHKT";
    private static final String OVERSEA_AND_LOCAL_TRAVEL_CARD = "OVERSEA_AND_LOCAL_TRAVEL_CARD";
    private static final String RUSSIA_DOMESTIC_PASSPORT = "RUSSIA_DOMESTIC_PASSPORT";

    protected static void toCertificate(ClientEntity client, HotelBookPassengerInput orderCreateHotelPassengerInput,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
        CertificateInfo useCertificateInfo =
            OrderCreateProcessorOfUtil.buildUseCertificateInfo(orderCreateHotelPassengerInput, checkAvailInfo);
        if (useCertificateInfo == null || StringUtils.isBlank(useCertificateInfo.getTransferCertificateNo())) {
            return;
        }
        String transferCertificateNo = useCertificateInfo.getTransferCertificateNo();
        switch (useCertificateInfo.getCertificateType()) {
            case IDENTITY_CARD:
                client.setCertificateType(CertificateTypeEnum.IdentityCard);
                client.setCertificateNumber(Optional.ofNullable(
                                CoreInfoUtil.encrypt(new InfoKey(KeyType.Identity_Card, transferCertificateNo)))
                        .orElse(transferCertificateNo));
                break;
            case PASSPORT:
                client.setCertificateType(CertificateTypeEnum.Passport);
                client.setCertificateNumber(Optional.ofNullable(
                                CoreInfoUtil.encrypt(new InfoKey(KeyType.Passport, transferCertificateNo)))
                        .orElse(transferCertificateNo));
                break;
            case MTP:
                client.setCertificateType(CertificateTypeEnum.TaiwaneseCertificate);
                client.setCertificateNumber(Optional.ofNullable(
                                CoreInfoUtil.encrypt(new InfoKey(KeyType.MTP, transferCertificateNo)))
                        .orElse(transferCertificateNo));
                break;
            case TAIWANPASS:
                client.setCertificateType(CertificateTypeEnum.TaiwanPass);
                client.setCertificateNumber(Optional.ofNullable(
                                CoreInfoUtil.encrypt(new InfoKey(KeyType.TaiwanPass, transferCertificateNo)))
                        .orElse(transferCertificateNo));
                break;
            case HKMACPASS:
                client.setCertificateType(CertificateTypeEnum.HKAndMacauPass);
                client.setCertificateNumber(Optional.ofNullable(
                                CoreInfoUtil.encrypt(new InfoKey(KeyType.HKMacPass, transferCertificateNo)))
                        .orElse(transferCertificateNo));
                break;
            case HOMEPERMIT:
                client.setCertificateType(CertificateTypeEnum.HometownPermit);
                client.setCertificateNumber(Optional.ofNullable(
                                CoreInfoUtil.encrypt(new InfoKey(KeyType.HomePermit, transferCertificateNo)))
                        .orElse(transferCertificateNo));
                break;
            case RUSSIA_DOMESTIC_PASSPORT:
                client.setCertificateType(CertificateTypeEnum.RussianDomesticPassport);
                client.setCertificateNumber(Optional.ofNullable(
                                CoreInfoUtil.encrypt(new InfoKey(KeyType.OtherDocument, transferCertificateNo)))
                        .orElse(transferCertificateNo));
                break;
            case OVERSEA_AND_LOCAL_TRAVEL_CARD:
                client.setCertificateType(CertificateTypeEnum.OverseaTravelCertificate);
                client.setCertificateNumber(Optional.ofNullable(
                                CoreInfoUtil.encrypt(new InfoKey(KeyType.OtherDocument, transferCertificateNo)))
                        .orElse(transferCertificateNo));
                break;
            case SEAMAN_CARD:
                client.setCertificateType(CertificateTypeEnum.InternationalSeamanPassport);
                client.setCertificateNumber(Optional.ofNullable(
                                CoreInfoUtil.encrypt(new InfoKey(KeyType.Seafarer_Passport, transferCertificateNo)))
                        .orElse(transferCertificateNo));
                break;
            case FOREIGNER_PERMANENT_RESIDENT_ID_CARD:
                client.setCertificateNumber(Optional.ofNullable(
                                CoreInfoUtil.encrypt(
                                        new InfoKey(KeyType.Foreigner_Permanent_Resident_ID_Card, transferCertificateNo)))
                        .orElse(transferCertificateNo));
                break;
            case RESIDENCE_PERMIT_HKT:
                client.setCertificateType(CertificateTypeEnum.HKAndMacaoResidenceCertificate);
                client.setCertificateNumber(Optional.ofNullable(
                                CoreInfoUtil.encrypt(new InfoKey(KeyType.OtherDocument, transferCertificateNo)))
                        .orElse(transferCertificateNo));
                break;
            default:
                break;
        }
    }

    protected ContactorEntity getContactorInfo(OrderCreateRequestType orderCreateRequestType,
        QconfigOfEmailInfoConfig qconfigOfEmailInfoConfig) {
        HotelContactorInfo hotelContactorInfo = orderCreateRequestType.getHotelContactorInfo();
        if (hotelContactorInfo == null) {
            return null;
        }
        ContactorEntity result = new ContactorEntity();
        result.setConfirmType(getConfirmType(hotelContactorInfo.getEmailInfo()));
        String transferEmail =
            buildContactorEmail(orderCreateRequestType, qconfigOfEmailInfoConfig);
        if (StringUtil.isNotBlank(transferEmail)) {
            result.setEmail(Optional.ofNullable(encryptData(KeyType.Mail, transferEmail,
                    orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId()))
                .orElse(transferEmail));
        }
        result.setMobilePhoneCountryCode(hotelContactorInfo.getPhoneInfo().getCountryCode());
        result.setName(hotelContactorInfo.getName());
        String transferPhoneNo = Optional.ofNullable(hotelContactorInfo.getPhoneInfo())
                .map(PhoneInfo::getTransferPhoneNo).orElse(null);
        if (StringUtil.isNotBlank(transferPhoneNo)) {
            result.setMobilePhone(Optional.ofNullable(encryptData(KeyType.Phone, transferPhoneNo,
                    orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId()))
                .orElse(transferPhoneNo));
        }
        result.setName(StringUtil.isNotEmpty(hotelContactorInfo.getName()) ? hotelContactorInfo.getName() :
                BFFSharkUtil.getSharkValue(SharkKeyConstant.CONTACT_PSG_NAME));
        return result;
    }

    protected String buildContactorEmail(OrderCreateRequestType orderCreateRequestType,
        QconfigOfEmailInfoConfig qconfigOfEmailInfoConfig) {
        // 海外一律Email确认，前端不传email则读后台配置
        if (CityInfoUtil.oversea(orderCreateRequestType.getCityInput().getCityId())) {
            return buildOverseaEmail(orderCreateRequestType.getHotelContactorInfo());
        }
        String qconfigEmail =
            buildQConfigEmail(qconfigOfEmailInfoConfig, orderCreateRequestType.getIntegrationSoaRequestType());
        if (qconfigEmail != null) {
            return qconfigEmail;
        }
        return Optional.ofNullable(orderCreateRequestType.getHotelContactorInfo().getEmailInfo())
            .map(EmailInfo::getTransferEmail).orElse(null);
    }

    protected String buildOverseaEmail(HotelContactorInfo hotelContactorInfo) {
        return Optional.ofNullable(hotelContactorInfo.getEmailInfo()).map(EmailInfo::getTransferEmail).orElse(null);
    }

    protected String buildQConfigEmail(QconfigOfEmailInfoConfig qconfigOfEmailInfoConfig,
        IntegrationSoaRequestType integrationSoaRequestType) {
        if (qconfigOfEmailInfoConfig == null || qconfigOfEmailInfoConfig.getEmailInfoConfig() == null
            || CollectionUtil.isEmpty(qconfigOfEmailInfoConfig.getEmailInfoConfig().getCorpEmails())) {
            return null;
        }
        CorpEmail corpEmail = qconfigOfEmailInfoConfig.getEmailInfoConfig().getCorpEmails().stream()
            .filter(o -> integrationSoaRequestType.getUserInfo().getCorpId().equals(o.getCorpID())).findFirst()
            .orElse(null);
        if (corpEmail == null) {
            return null;
        }
        if (StringUtil.isNotBlank(corpEmail.getEmail())) {
            return corpEmail.getEmail();
        }
        if (StringUtil.isNotBlank(qconfigOfEmailInfoConfig.getEmailInfoConfig().getDefaultEmail())) {
            return qconfigOfEmailInfoConfig.getEmailInfoConfig().getDefaultEmail();
        }
        return null;
    }

    private ConfirmTypes getConfirmType(EmailInfo emailInfo) {
        if (StringUtil.isNotBlank(Optional.ofNullable(emailInfo).map(EmailInfo::getTransferEmail).orElse(null))) {
            return ConfirmTypes.Email;
        }
        return ConfirmTypes.MobileMSG;
    }

    private PriceEntity getPriceInfo(BookRoomInfoEntity bookRoomInfo) {
        if (bookRoomInfo == null) {
            return null;
        }

        PriceEntity result = new PriceEntity();
        result.setCNYAmount(bookRoomInfo.getCnyAmount());
        AmountDetailEntity customAmountInfo = bookRoomInfo.getCustomAmountInfo();
        if (customAmountInfo == null) {
            return result;
        }
        result.setCustomAmount(customAmountInfo.getAmount());
        result.setCustomCurrency(customAmountInfo.getCurrency());
        result.setCustomExchange(customAmountInfo.getExchange());
        AmountDetailEntity originAmountInfo = bookRoomInfo.getOriginAmountInfo();
        if (originAmountInfo == null) {
            return result;
        }
        result.setOriginAmount(originAmountInfo.getAmount());
        result.setOriginCurrency(originAmountInfo.getCurrency());
        result.setOriginExchange(originAmountInfo.getExchange());
        return result;
    }

    private GuaranteeEntity getGuaranteeInfo(
            OrderCreateRequestType orderCreateRequestType,
            QueryCheckAvailContextResponseType queryCheckAvailContextResponseType,
            ResourceToken resourceToken) {
        if (orderCreateRequestType == null || queryCheckAvailContextResponseType == null) {
            return null;
        }
        SourceFrom sourceFrom = Optional.ofNullable(orderCreateRequestType.getIntegrationSoaRequestType())
                .map(IntegrationSoaRequestType::getSourceFrom).orElse(null);
        if (SourceFrom.Offline.equals(sourceFrom)) {
            return buildOrderGuaranteeByOffline(orderCreateRequestType, queryCheckAvailContextResponseType,
                    resourceToken);
        }
        return getGuaranteeInfo(queryCheckAvailContextResponseType, orderCreateRequestType);
    }

    private GuaranteeEntity getGuaranteeInfo(QueryCheckAvailContextResponseType queryCheckAvailContextResponseType,
        OrderCreateRequestType orderCreateRequestType) {
        BookRoomInfoEntity roomInfo = queryCheckAvailContextResponseType.getRoomInfo();
        QueryCancelPolicyType cancelPolicyInfo = Optional.ofNullable(queryCheckAvailContextResponseType.getBookingRules())
                .map(QueryBookingRulesType::getCancelPolicyInfo).orElse(null);
        if (roomInfo == null || cancelPolicyInfo == null) {
            return null;
        }
        QueryGuaranteeDetailType guaranteePolicyInfo = cancelPolicyInfo.getGuaranteePolicyInfo();
        if (!BALANCE_TYPE_FG.equals(roomInfo.getBalanceType()) || guaranteePolicyInfo == null) {
            return null;
        }
        GuaranteeEntity result = new GuaranteeEntity();

        if (Boolean.TRUE.equals(
            isNeedGuarantee(cancelPolicyInfo))) {
            // 进入这里肯定是需要担保了, 已经判断过了
            result.setGuaranteeNeeded(true);
            // 已经没有手机担保了, 恒为false
            result.setMobileGuarantee(false);
            result.setForceGuarantee(true);
            // 担保金额(原币种)
            result.setGuaranteeContext(Optional.ofNullable(guaranteePolicyInfo.getGuaranteePriceInfo())
                    .map(QueryGuaranteePriceType::getOriginGuaranteePrice)
                    .map(PriceType::getPrice)
                    .map(BigDecimal::toString).orElse(""));
            result.setGuaranteeWay(getGuaranteeWay(orderCreateRequestType));
            return result;
        }
        return null;
    }

    public boolean isNeedGuarantee(QueryCancelPolicyType cancelPolicyInfo) {
        return Optional.ofNullable(cancelPolicyInfo).map(QueryCancelPolicyType::isNeedGuarantee).orElse(false);
    }

    private GuaranteeWays getGuaranteeWay(OrderCreateRequestType orderCreateRequestType) {
        HotelPayTypeEnum roomPayType = HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        if (HotelPayTypeEnum.GUARANTEE_CORP_PAY == roomPayType) {
            return GuaranteeWays.Account;
        }
        if (HotelPayTypeEnum.GUARANTEE_SELF_PAY == roomPayType) {
            return GuaranteeWays.Credit;
        }
        if (HotelPayTypeEnum.CORP_CREDIT_CARD_GUARANTEE.equals(roomPayType)) {
            return GuaranteeWays.BIZC;
        }
        return null;
    }

    private GuaranteeEntity buildOrderGuaranteeByOffline(
            OrderCreateRequestType orderCreateRequestType,
            QueryCheckAvailContextResponseType queryCheckAvailContextResponseType,
            ResourceToken resourceToken) {
        GuaranteeEntity result = new GuaranteeEntity();
        result.setGuaranteeNeeded(false);

        BookRoomInfoEntity roomInfo = queryCheckAvailContextResponseType.getRoomInfo();
        QueryCancelPolicyType cancelPolicyInfo =
                queryCheckAvailContextResponseType.getBookingRules().getCancelPolicyInfo();
        QueryGuaranteeDetailType guaranteePolicyInfo = cancelPolicyInfo.getGuaranteePolicyInfo();
        if (roomInfo == null || guaranteePolicyInfo == null) {
            return null;
        }

        String especialAuth = Optional.ofNullable(orderCreateRequestType.getOfflineInfo())
                .map(OfflineInfo::getEspecialAuth).orElse(null);
        boolean isSpecialAuth = BooleanUtil.parseStr(Boolean.TRUE).equals(especialAuth);
        if (BALANCE_TYPE_PP.equals(roomInfo.getBalanceType())) {
            // 只有会员酒店的特别授权+预付，按现付处理，Added By SF,事件号：INC000004472170
            if (!isSpecialAuthAndMembershipHotel(isSpecialAuth, roomInfo.getRoomType())) {
                return result;
            }
            result.setGuaranteeNeeded(true);
            result.setGuaranteeContext(Optional.ofNullable(guaranteePolicyInfo.getGuaranteePriceInfo())
                    .map(QueryGuaranteePriceType::getOriginGuaranteePrice)
                    .map(PriceType::getPrice)
                    .map(BigDecimal::toString).orElse(StringUtil.EMPTY));
        }

        if (!BALANCE_TYPE_FG.equals(roomInfo.getBalanceType())) {
            return null;
        }

        // 【COR03SLJD-4371】特别授权可以取消担保
        String forciblyAssure = Optional.ofNullable(orderCreateRequestType.getOfflineInfo())
                .map(OfflineInfo::getForciblyAssure).orElse(null);
        boolean isForce = BooleanUtil.parseStr(Boolean.TRUE).equals(forciblyAssure);
        if (canCancelGuarantee(isSpecialAuth, isForce)) {
            return result;
        }

        if (!needGuarantee(isForce, roomInfo, cancelPolicyInfo,
            orderCreateRequestType.getIntegrationSoaRequestType())) {
            return result;
        }

        result.setGuaranteeNeeded(true);
        if (ROOM_TYPE_C.equals(roomInfo.getRoomType())) {
            if (GDS_TYPE_AMADEUS.equals(roomInfo.getGdsType())) {
                return result;
            }
            /*// 创单下线节点
            if (HotelGuaranteeTypeEnum.CORP_GUARANTEE.getCode().equals(guaranteePolicyInfo.getGuaranteeType())) {
               result.setCHotelCorGuaranteeInfo(buildCGuarantee(guaranteePolicyInfo.getGuaranteePriceInfo()));
            }*/
        }

        RoomResourceToken roomResourceToken = resourceToken.getRoomResourceToken();
        /*// 创单下线节点
        if (Boolean.TRUE.equals(roomResourceToken.getTmcPrice())
            && HotelGuaranteeTypeEnum.CORP_GUARANTEE.getCode().equals(guaranteePolicyInfo.getGuaranteeType())) {
            result.setCHotelCorGuaranteeInfo(buildCGuarantee(guaranteePolicyInfo.getGuaranteePriceInfo()));
        }*/

        result.setMobileGuarantee(false);
        result.setGuaranteeContext(Optional.ofNullable(guaranteePolicyInfo.getGuaranteePriceInfo())
                .map(QueryGuaranteePriceType::getOriginGuaranteePrice)
                .map(PriceType::getPrice)
                .map(BigDecimal::toString).orElse(StringUtil.EMPTY));
        result.setForceGuarantee(isForce);
        result.setGuaranteeWay(getGuaranteeWay(orderCreateRequestType));

        return result;
    }

    private boolean isSpecialAuthAndMembershipHotel(boolean isSpecialAuth, String roomType) {
        return isSpecialAuth && ROOM_TYPE_M.equals(roomType);
    }

    private boolean canCancelGuarantee(boolean isSpecialAuth, boolean isForce) {
        return isSpecialAuth && !isForce;
    }

    private boolean needGuarantee(boolean isForce, BookRoomInfoEntity bookRoomInfoEntity,
        QueryCancelPolicyType queryCancelPolicyType, IntegrationSoaRequestType integrationSoaRequestType) {
        return isForce || isNeedGuarantee(queryCancelPolicyType);
    }

    private static final String HOTEL_INVOICE_TYPE_ROOM = "ROOM";
    private static final String INVOICE_TITLE_TYPE_PERSONAL = "I";
    private static final String INVOICE_TITLE_TYPE_ENTERPRISE = "C";
    private static final String INVOICE_TITLE_TYPE_GOVERNMENT = "P";

    private InvoiceEntity getInvoiceInfo(OrderCreateRequestType orderCreateRequestType) {
        List<HotelInvoiceInfo> hotelInvoiceInfos = orderCreateRequestType.getHotelInvoiceInfos();
        String corpId = RequestHeaderUtil.getCorpId(orderCreateRequestType.getIntegrationSoaRequestType());
        if (CollectionUtil.isEmpty(hotelInvoiceInfos)) {
            return null;
        }
        // 取房费的发票信息
        InvoiceInfo invoiceInfo = hotelInvoiceInfos.stream()
                .filter(Objects::nonNull)
                .filter(hotelInvoiceInfo -> HOTEL_INVOICE_TYPE_ROOM.equals(hotelInvoiceInfo.getHotelInvoiceType()))
                .findFirst().map(HotelInvoiceInfo::getInvoiceInfo).orElse(null);

        // 取房费的发票信息
        HotelInvoiceInfo hotelInvoiceInfo = hotelInvoiceInfos.stream().filter(Objects::nonNull)
            .filter(hotelInvoice -> HOTEL_INVOICE_TYPE_ROOM.equals(hotelInvoice.getHotelInvoiceType()))
            .collect(Collectors.toList()).stream().findFirst().orElse(null);
        if (invoiceInfo == null || invoiceInfo.getInvoiceType() == null) {
            return null;
        }
        return getInvoiceInfoByType(invoiceInfo, corpId, hotelInvoiceInfo);
    }

    /**
     * 根据发票类型创建发票
     *
     * @param invoiceInfo
     * @return
     */
    public InvoiceEntity getInvoiceInfoByType(InvoiceInfo invoiceInfo, String corpId,
        HotelInvoiceInfo hotelInvoiceInfo) {
        if (invoiceInfo == null) {
            return null;
        }
        OrderInvoiceTargetType orderInvoiceTargetType = getOrderInvoiceTargetType(invoiceInfo.getInvoiceType());
        if (orderInvoiceTargetType == null) {
            return null;
        }
        InvoiceEntity result = new InvoiceEntity();
        InvoiceScenarioEntity scenarioEntityTO = new InvoiceScenarioEntity();
        // 数电普票和数电专票 总是需要发票
        scenarioEntityTO.setNeedInvoice(true);
        result.setInvoiceScenarioInfo(scenarioEntityTO);
        OrderInvoiceEntity orderInvoiceEntity = new OrderInvoiceEntity();
        orderInvoiceEntity.setOrderInvoiceTargetType(getOrderInvoiceTargetType(invoiceInfo.getInvoiceType()));
        orderInvoiceEntity.setBaseInvoiceInfo(buildBaseInvoice(invoiceInfo, hotelInvoiceInfo));
        buildInvoiceInfo(orderInvoiceEntity, invoiceInfo, corpId);
        result.setOrderInvoiceInfo(orderInvoiceEntity);
        return result;
    }

    OrderInvoiceTargetType getOrderInvoiceTargetType(String invoiceType) {
        InvoiceEnum invoiceEnum = InvoiceEnum.getValueForAgg(invoiceType);
        if (invoiceEnum == null) {
            return null;
        }
        switch (invoiceEnum) {
            case D_INVOICE:
                return OrderInvoiceTargetType.DInvoice;
            case D_VAT_INVOICE:
                return OrderInvoiceTargetType.DVatInvoice;
            case VAT:
                return OrderInvoiceTargetType.VAT;
            case E_VAT:
                return OrderInvoiceTargetType.EVAT;
            case S_VAT:
                return OrderInvoiceTargetType.VATSpecial;
            default:
                return null;
        }
    }

    protected BaseInvoiceEntity buildBaseInvoice(InvoiceInfo invoiceInfo, HotelInvoiceInfo hotelInvoiceInfo) {
        BaseInvoiceEntity baseInvoiceEntity = new BaseInvoiceEntity();
        baseInvoiceEntity.setInvoiceTitle(invoiceInfo.getInvoiceTitle());
        baseInvoiceEntity.setContainOrderDetail(BooleanUtil.parseStr(true)
            .equalsIgnoreCase(Optional.ofNullable(hotelInvoiceInfo.getCheckInAndOutTime()).orElse(null)));
        baseInvoiceEntity.setTaxpayerNumber(invoiceInfo.getTaxNumber());
        return baseInvoiceEntity;
    }

    void buildInvoiceInfo(OrderInvoiceEntity invoiceEntity, InvoiceInfo invoiceInfo, String corpId) {
        if (invoiceEntity == null || invoiceInfo == null) {
            return;
        }
        String transferEmail = Optional.ofNullable(invoiceInfo.getEmailInfo())
                .map(EmailInfo::getTransferEmail).orElse(null);
        InvoiceEnum invoiceEnum = InvoiceEnum.getValueForAgg(invoiceInfo.getInvoiceType());

        if (OrderInvoiceTargetType.DVatInvoice.equals(invoiceEntity.getOrderInvoiceTargetType())) {
            SEVatInvoiceEntity seVatInvoiceEntity = new SEVatInvoiceEntity();
            seVatInvoiceEntity.setEmail(Optional.ofNullable(CoreInfoUtil.encrypt(
                    new InfoKey(KeyType.Mail, transferEmail))).orElse(transferEmail));
            SVatInvoiceSpecialEntity sVatInvoiceSpecialEntity = new SVatInvoiceSpecialEntity();
            InvoiceCompanyInfo invoiceCompanyInfo = invoiceInfo.getInvoiceCompanyInfo();
            if (invoiceCompanyInfo != null) {
                sVatInvoiceSpecialEntity.setCompanyAddress(invoiceCompanyInfo.getCompanyAddress());
                sVatInvoiceSpecialEntity.setCompanyBankAccount(invoiceCompanyInfo.getCompanyBankAccount());
                sVatInvoiceSpecialEntity.setCompanyBankName(invoiceCompanyInfo.getCompanyBank());
            }
            sVatInvoiceSpecialEntity.setCompanyName(invoiceInfo.getInvoiceTitle());
            String companyTel = Optional.ofNullable(invoiceInfo.getInvoiceCompanyInfo())
                    .map(InvoiceCompanyInfo::getCompanyTel).orElse(null);
            if (isNeedEncryptSensitiveInformation(corpId)) {
                companyTel = Optional.ofNullable(CoreInfoUtil.encrypt(
                        new InfoKey(KeyType.Phone, companyTel))).orElse(companyTel);
            }
            sVatInvoiceSpecialEntity.setCompanyPhone(companyTel);
            seVatInvoiceEntity.setSVatInvoiceSpecialInfo(sVatInvoiceSpecialEntity);
            invoiceEntity.setSEVatInvoiceInfo(seVatInvoiceEntity);
            // 数电专票需要传入发票抬头，默认为企业
            BaseInvoiceEntity baseInvoiceInfo = invoiceEntity.getBaseInvoiceInfo();
            baseInvoiceInfo.setInvoiceTitleType(InvoiceTitleTypeEnum.E);
        } else if (OrderInvoiceTargetType.DInvoice.equals(invoiceEntity.getOrderInvoiceTargetType())
            || invoiceEnum == InvoiceEnum.E_VAT) {
            EVatInvoiceEntity eVatInvoiceEntity = new EVatInvoiceEntity();
            eVatInvoiceEntity.setEmail(Optional.ofNullable(CoreInfoUtil.encrypt(
                    new InfoKey(KeyType.Mail, transferEmail))).orElse(transferEmail));
            eVatInvoiceEntity.setVatInvoiceSpecialInfo(buildVatInvoiceSpecial(invoiceInfo));
            invoiceEntity.setEVatInvoiceInfo(eVatInvoiceEntity);
        }
    }

    private boolean isNeedEncryptSensitiveInformation(String corpId) {
        // 目前仅开了testnet的配置不考虑 100023321 HotelSwitchConfig.properties isNeedEncryptSensitiveInformation
        return false;
    }

    protected VatInvoiceSpecialEntity buildVatInvoiceSpecial(InvoiceInfo invoiceInfo) {
        VatInvoiceSpecialEntity vatInvoiceSpecialEntity = new VatInvoiceSpecialEntity();
        vatInvoiceSpecialEntity.setInvoiceTitleType(buildInvoiceTitleType(invoiceInfo.getInvoiceTitleType()));
        if (invoiceInfo.getInvoiceDetailInfo() != null) {
            vatInvoiceSpecialEntity.setInvoiceBody(invoiceInfo.getInvoiceDetailInfo().getInvoiceDetailDisplayValue());
        }
        return vatInvoiceSpecialEntity;
    }

    private InvoiceTitleTypeEnum buildInvoiceTitleType(String invoiceTitleType) {
        switch (invoiceTitleType) {
            case INVOICE_TITLE_TYPE_PERSONAL:
                return InvoiceTitleTypeEnum.P;
            case INVOICE_TITLE_TYPE_ENTERPRISE:
                return InvoiceTitleTypeEnum.E;
            case INVOICE_TITLE_TYPE_GOVERNMENT:
                return InvoiceTitleTypeEnum.G;
            default:
                return null;
        }
    }

    public CorpOrderInfoEntity getCorpOrderInfo(CheckTravelPolicyResponseType checkTravelPolicyResponseType,
                                                OrderCreateRequestType orderCreateRequestType,
                                                GetTravelPolicyContextResponseType getTravelPolicyContextResponseType, WrapperOfAccount.AccountInfo accountInfo,
                                                AllocationResultToken costAllocationToken, ResourceToken resourceToken, OrderCreateToken orderCreateToken,
                                                WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailContextInfo,
                                                GetPlatformRelationByUidResponseType getPlatformRelationByUidResponseType,
                                                QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType) {
        CorpOrderInfoEntity result = new CorpOrderInfoEntity();
        result.setApprovalBudgetInfo(buildApprovalBudgetInfo(checkTravelPolicyResponseType));
        result.setTravelPolicyInfo(getTravelPolicyInfo(getTravelPolicyContextResponseType, accountInfo));
        String policyToken = Optional.ofNullable(resourceToken)
                .map(ResourceToken::getReservationResourceToken)
                .map(ReservationResourceToken::getPolicyToken).orElse(null);
        result.setPolicyToken(policyToken);
        result.setBookRCInfo(getRcInfo(OrderCreateProcessorOfUtil.buildValidRcList(orderCreateRequestType.getRcInfos(),
            Optional.ofNullable(checkTravelPolicyResponseType).map(CheckTravelPolicyResponseType::getCheckRcResult)
                .orElse(null))));
        // 费用分摊信息节点
        if (costAllocationToken != null && CollectionUtil.isNotEmpty(costAllocationToken.getSettleAllocationAmount())) {
            result.setAllocationInfo(getAllocationInfoType(accountInfo, costAllocationToken)); // TODO
        }
        result.setRcInfoList(getRCInfoList(
            OrderCreateProcessorOfUtil.buildValidRcList(orderCreateRequestType.getRcInfos(),
                Optional.ofNullable(checkTravelPolicyResponseType).map(CheckTravelPolicyResponseType::getCheckRcResult)
                    .orElse(null)), orderCreateRequestType));

        result.setSendMsg(Optional.ofNullable(orderCreateRequestType.getSendInfo()).map(SendInfo::getSendSMS).map(BooleanUtil::toBoolean).orElse(Boolean.FALSE));
        result.setSendNotificationEmailToGuest(
            Optional.ofNullable(orderCreateRequestType.getSendInfo()).map(SendInfo::getSendEmail).map(BooleanUtil::toBoolean).orElse(Boolean.FALSE));
        // 海外电子invoice接收人邮箱
        List<HotelInvoiceInfo> hotelInvoiceInfos = orderCreateRequestType.getHotelInvoiceInfos();
        InvoiceInfo invoiceInfo = null;
        if (CollectionUtil.isNotEmpty(hotelInvoiceInfos)) {
            invoiceInfo = hotelInvoiceInfos.stream().filter(Objects::nonNull)
                    .filter(hotelInvoiceInfo -> HOTEL_INVOICE_TYPE_ROOM.equals(hotelInvoiceInfo.getHotelInvoiceType()))
                    .map(HotelInvoiceInfo::getInvoiceInfo).findFirst().orElse(null);
        }
        if (InvoiceEnum.INVOICE.getCode()
            .equalsIgnoreCase(Optional.ofNullable(invoiceInfo).map(InvoiceInfo::getInvoiceType).orElse(null))) {
            result.setInvoiceEmail(Optional.ofNullable(invoiceInfo).map(InvoiceInfo::getEmailInfo)
                .map(EmailInfo::getTransferEmail).orElse(null));
        }
        if (orderCreateRequestType.getMiceInput() != null && StringUtil.isNotBlank(orderCreateRequestType.getMiceInput().getMiceActivityId())) {
            // Mice活动ID
            result.setMiceId(Integer.parseInt(orderCreateRequestType.getMiceInput().getMiceActivityId()));
        }
        // 扣款uid
        String policyUid = Optional.ofNullable(orderCreateRequestType.getHotelPolicyInput())
                .map(HotelPolicyInput::getPolicyInput)
                .map(PolicyInput::getPolicyUid).orElse(null);
        result.setPayUid(buildPayUid(accountInfo, policyUid));
        result.setRegisteredMemberUid(buildRegisteredMemberUid(checkAvailContextInfo, orderCreateRequestType,
            getPlatformRelationByUidResponseType, queryBizModeBindRelationResponseType));
        result.setRepeatBookingOrderList(buildRepeatBookingOrderList(orderCreateToken));
        result.setPartnerEid(TraceContextUtil.getPartnerEid());
        result.setPartnerId(TraceContextUtil.getPartnerId());
        return result;
    }

    private List<Long> buildRepeatBookingOrderList(OrderCreateToken orderCreateToken) {
        if (orderCreateToken == null) {
            return null;
        }
        if (CollectionUtil.isEmpty(orderCreateToken.getRepeatOrderResults())) {
            return null;
        }
        return orderCreateToken.getRepeatOrderResults().stream().map(RepeatOrderResult::getOrderId)
                .map(NumberUtil::parseLong).collect(Collectors.toList());
    }

    private ApprovalBudgetInfoType buildApprovalBudgetInfo(
        CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
        ApprovalBudgetInfoType result = new ApprovalBudgetInfoType();
        result.setApprovalBudgetVerifyId(StringUtil.EMPTY);
        result.setApprovalTimesVerifyId(StringUtil.EMPTY);
        if (checkTravelPolicyResponseType == null) {
            return result;
        }
        if (checkTravelPolicyResponseType.getVerifyApprovalBillResult() != null) {
            result.setApprovalBudgetVerifyId(
                Optional.ofNullable(checkTravelPolicyResponseType.getVerifyApprovalBillResult().getControlBudgetID())
                    .orElse(StringUtil.EMPTY));
            result.setApprovalTimesVerifyId(Optional.ofNullable(
                    checkTravelPolicyResponseType.getVerifyApprovalBillResult().getControlEffectivenessCountID())
                .orElse(StringUtil.EMPTY));
        }
        if (checkTravelPolicyResponseType.getVerifyPassengerResult() != null) {
            result.setApprovalBudgetVerifyId(
                Optional.ofNullable(checkTravelPolicyResponseType.getVerifyPassengerResult().getControlBudgetID())
                    .orElse(StringUtil.EMPTY));
            result.setApprovalTimesVerifyId(Optional.ofNullable(
                    checkTravelPolicyResponseType.getVerifyPassengerResult().getControlEffectivenessCountID())
                .orElse(StringUtil.EMPTY));
        }
        return result;
    }

    private TravelApplyEntity getTravelApplyInfo(
            QueryCheckAvailContextResponseType queryCheckAvailContextResponseType,
            CheckTravelPolicyResponseType checkTravelPolicyResponseType,
            ApprovalInput approvalInput) {
        TravelApplyEntity result = new TravelApplyEntity();

        if (approvalInput == null) {
            return null;
        }
        result.setEndorsementId(Long.parseLong(approvalInput.getMasterApprovalNo()));
        if (StringUtil.isNotEmpty(approvalInput.getSubApprovalNo())) {
            result.setTravelPlanId(Long.parseLong(approvalInput.getSubApprovalNo()));
        }
        // result.setMatchEndorsement(ControlResultHelper.isMatchEndorsement(controlResult)); //没有老出差申请了，不需要考虑了
        boolean needAuthorize = Optional.ofNullable(checkTravelPolicyResponseType.getVerifyApprovalBillResult())
                .map(VerifyApprovalBillResultType::isNeedAuthorize).orElse(false);
        result.setNeedAuthorize(needAuthorize);
        result.setUrgentBooking(
                BooleanUtil.parseStr(Boolean.TRUE).equals(approvalInput.getEmergency()));
        return result;
    }

    private TravelPolicyEntity getTravelPolicyInfo(
            GetTravelPolicyContextResponseType getTravelPolicyContextResponseType,
            WrapperOfAccount.AccountInfo accountInfo) {
        if (getTravelPolicyContextResponseType == null) {
            return null;
        }
        TravelPolicyEntity result = new TravelPolicyEntity();
        boolean isMustMatchBookTP = accountInfo.matchPolicyDefaultCheckedUnEditable();
        if (!Boolean.TRUE.equals(getTravelPolicyContextResponseType.getFinalPolicy().isCanBookWhenNotMatch())) {
            isMustMatchBookTP = true;
        }
        result.setMustMatchBookTP(isMustMatchBookTP);
        return result;
    }

    public RCInfo getRcInfo(List<RCInput> validRcInputList) {
        RCInfo rcInfo = new RCInfo();
        if (CollectionUtil.isEmpty(validRcInputList)) {
            return rcInfo;
        }
        validRcInputList.forEach(rcInput -> {
            RcToken rcToken = TokenParseUtil.parseToken(rcInput.getRcToken(), RcToken.class);
            RcTypeEnum rcTypeEnum = RcTypeEnum.getType(rcToken.getType());
            if (rcTypeEnum == RcTypeEnum.LOW_PRICE) {
                rcInfo.setLowPriceRC(rcToken.getCode());
                if (isCustomRC(rcToken.getCode())) {
                    rcInfo.setLowPriceRCVV(rcToken.getValue());
                }
            }
            if (rcTypeEnum == RcTypeEnum.AGREEMENT) {
                rcInfo.setContractRC(rcToken.getCode());
                if (isCustomRC(rcToken.getCode())) {
                    rcInfo.setContractRCVV(rcToken.getValue());
                }
            }
            if (rcTypeEnum == RcTypeEnum.BOOK_AHEAD) {
                rcInfo.setBookAheadRC(rcToken.getCode());
                if (isCustomRC(rcToken.getCode())) {
                    rcInfo.setBookAheadRCVV(rcToken.getValue());
                }
            }
            if (rcTypeEnum == RcTypeEnum.MODIFY) {
                rcInfo.setModifyRCCode(rcToken.getCode());
                rcInfo.setModifyRCContent(rcToken.getValue());
            }
        });
        return rcInfo;
    }

    public boolean isCustomRC(String rcCode) {
        return Optional.ofNullable(rcCode).map("VV"::equalsIgnoreCase).orElse(false);
    }

    public AuthEntity getAuthInfo(FollowApprovalInfoInput followApprovalInfoInput, SourceFrom sourceFrom) {
        AuthEntity result = new AuthEntity();
        result.setFollowAuthType(FollowAuthTypeEnum.INTELLIGENT_AUTH);
        String artificialFollow = Optional.ofNullable(followApprovalInfoInput)
                .map(FollowApprovalInfoInput::getArtificialFollow).orElse(null);
        if (BooleanUtil.parseStr(Boolean.TRUE).equals(artificialFollow)) {
            result.setFollowAuthType(FollowAuthTypeEnum.MANUAL_AUTH);
        }

        long authFromOrderId = Optional.ofNullable(followApprovalInfoInput)
                .map(FollowApprovalInfoInput::getFollowOrderId)
                .map(NumberUtil::parseLong).orElse(0L);
        if (SourceFrom.Offline.equals(sourceFrom) && authFromOrderId <= 0) {
            result.setFollowAuthType(FollowAuthTypeEnum.NONE);
        }
        result.setAuthFromOrderId(authFromOrderId);
        return result;
    }

    private static final String VERIFY_RESULT_PASS = "PASS";

    private OAAuthEntity getOAAuthInfo(
            CheckTravelPolicyResponseType checkTravelPolicyResponseType,
            OrderCreateRequestType orderCreateRequestType) {
        if (checkTravelPolicyResponseType == null || orderCreateRequestType == null) {
            return null;
        }

        boolean inControl = Optional.ofNullable(checkTravelPolicyResponseType.getVerifyApprovalBillResult())
            .map(VerifyApprovalBillResultType::isInControl).orElse(false);
        boolean isEmergency = BooleanUtil.parseStr(Boolean.TRUE).equals(
                Optional.ofNullable(orderCreateRequestType.getApprovalInput())
                        .map(ApprovalInput::getEmergency)
                        .orElse(null));
        String approvalNo = Optional.ofNullable(orderCreateRequestType.getApprovalInput())
                .map(ApprovalInput::getSubApprovalNo)
                .orElse(null);

        OAAuthEntity result = new OAAuthEntity();
        result.setMatchOAAuth(inControl);
        result.setOAAuthNo(approvalNo);
        result.setUrgentBooking(isEmergency);
        return result;
    }

    private AllocationInfoType getAllocationInfoType(WrapperOfAccount.AccountInfo accountInfo,
                                                     AllocationResultToken costAllocationToken) {
        AllocationInfoType result = new AllocationInfoType();
        // 分摊项是否可修改
        result.setAllocationFlag(accountInfo.htlShareControlEdit());
        // 分摊模式
        result.setAllocationMode(buildAllocationMode(accountInfo.htlFeeAllocationType()));

        // 分摊总金额
        Map<String, BigDecimal> shareAmounts = costAllocationToken.getSettleAllocationAmount();
        BigDecimal totalAmount = BigDecimal.ZERO;
        if (MapUtils.isNotEmpty(shareAmounts)) {
            for (BigDecimal amount : shareAmounts.values()) {
                if (null == amount) {
                    continue;
                }
                totalAmount = totalAmount.add(amount);
            }
        }
        result.setAllocationTotalAmount(totalAmount);
        return result;
    }

    public String buildAllocationMode(String htlFeeAllocationType) {
        if ("A".equalsIgnoreCase(htlFeeAllocationType)) {
            return "ORDER";
        }
        if ("B".equalsIgnoreCase(htlFeeAllocationType)) {
            return "ROOM";
        }
        if ("C".equalsIgnoreCase(htlFeeAllocationType)) {
            return "TRAVEL_CONTROL";
        }
        return "NONE";
    }

    private List<RCInfoType> getRCInfoList(List<RCInput> validRcInputs, OrderCreateRequestType orderCreateRequestType) {
        List<RCInfoType> result = new ArrayList<>();
        if (CollectionUtil.isEmpty(validRcInputs)) {
            return null;
        }
        List<RcToken> rcTokens = validRcInputs.stream().filter(Objects::nonNull)
                .map(rcInput -> TokenParseUtil.parseToken(rcInput.getRcToken(), RcToken.class))
                .filter(Objects::nonNull).collect(Collectors.toList());
        // 修改创单RC
        if (StrategyOfBookingInitUtil.onlyApplyModify(orderCreateRequestType.getStrategyInfos())) {
            for (RcToken rcToken : rcTokens) {
                if (!RcTypeEnum.APPLY_MODIFY.getCode().equalsIgnoreCase(rcToken.getType())) {
                    continue;
                }
                RCInfoType applyModifyRC = new RCInfoType();
                applyModifyRC.setType(MODIFY_APPLY_FROM);
                applyModifyRC.setContent(rcToken.getValue());
                applyModifyRC.setCode(rcToken.getCode());
                result.add(applyModifyRC);
            }
        }

        // 重复预订RC
        for (RcToken rcToken : rcTokens) {
            if (!RcTypeEnum.CONFLICT_BOOK.getCode().equalsIgnoreCase(rcToken.getType())) {
                continue;
            }
            RCInfoType repeatBookRC = new RCInfoType();
            repeatBookRC.setType(REPEAT_BOOKING);
            repeatBookRC.setCode(rcToken.getCode());
            repeatBookRC.setContent(rcToken.getValue());
            result.add(repeatBookRC);
        }
        return CollectionUtil.isEmpty(result) ? null : result;
    }

    private String buildPayUid(WrapperOfAccount.AccountInfo accountInfo, String policyUid) {
        if (accountInfo == null) {
            return null;
        }
        if (!accountInfo.usePolicyCurrency()) {
            return null;
        }
        return StringUtil.isNotEmpty(policyUid) ? policyUid : ThreadContextUtil.getUid();
    }

    public List<CreateCouponEntity> getCouponList(CouponInfoInput couponInfoInput) {
        if (CollectionUtil.isEmpty(
                Optional.ofNullable(couponInfoInput).map(CouponInfoInput::getCouponDetailInputList).orElse(null))) {
            return null;
        }
        return couponInfoInput.getCouponDetailInputList().stream().map(couponTokenInfo -> {
            if (couponTokenInfo == null) {
                return null;
            }
            CouponToken couponToken = TokenParseUtil.parseToken(
                    couponTokenInfo.getCouponToken(), CouponToken.class);
            if (couponToken == null) {
                return null;
            }
            CreateCouponEntity createCouponEntity = new CreateCouponEntity();
            createCouponEntity.setCouponCode(couponToken.getCouponCode());
            createCouponEntity.setCouponAmount(couponToken.getCouponAmount());
            createCouponEntity.setPromotionID(couponToken.getPromotionID());
            createCouponEntity.setVirtualCode(couponToken.getVirtualCode());
            createCouponEntity.setCouponType(CouponType.Y);
            return createCouponEntity;
        }).filter(Objects::nonNull).collect(
                Collectors.toList());
    }

    private RemarkEntity getRemarkInfo(RoomInfoInput roomInfoInput) {
        if (roomInfoInput == null) {
            return null;
        }
        RemarkEntity result = new RemarkEntity();

        result.setCustomRemark(roomInfoInput.getRoomCustomRemark());
        List<RoomRemarkToken> roomRemarkTokens = getRoomRemarkTokens(roomInfoInput.getRoomRemark());
        if (CollectionUtil.isEmpty(roomRemarkTokens)) {
            result.setOptionalRemarkList(Collections.emptyList());
            return result;
        }
        result.setOptionalRemarkList(roomRemarkTokens.stream().map(roomRemarkToken -> {
            OptionalRemarkEntity optionalRemarkEntity = new OptionalRemarkEntity();
            optionalRemarkEntity.setId(roomRemarkToken.getId());
            optionalRemarkEntity.setKey(roomRemarkToken.getKey());
            optionalRemarkEntity.setTitle(roomRemarkToken.getTitle());
            optionalRemarkEntity.setValue(roomRemarkToken.getValue());
            return optionalRemarkEntity;
        }).collect(Collectors.toList()));
        return result;
    }

    public static List<RoomRemarkToken> getRoomRemarkTokens(String roomRemarkToken) {
        if (StringUtil.isBlank(roomRemarkToken)) {
            return Collections.emptyList();
        }
        return Arrays.stream(roomRemarkToken.split(","))
                .map(remark -> PbSerializerUtil.pbDeserialize(remark, RoomRemarkToken.class))
                .filter(Objects::nonNull).collect(Collectors.toList());
    }

    private CreateTripEntity getTripInfo(WrapperOfAccount.AccountInfo accountInfo,
        OrderCreateRequestType orderCreateRequestType, CreateTripResponseType createTripResponseType,
        SearchTripDetailResponseType searchTripDetailResponseType, OrderCreateToken orderCreateToken) {

        CreateTripEntity result = new CreateTripEntity();
        // 先用沿用审批里取
        long tripId = OrderCreateProcessorOfUtil.getTripId(accountInfo, orderCreateRequestType, createTripResponseType,
            orderCreateToken);
        if (tripId <= 0) {
            return null;
        }
        result.setTripId(tripId);

        SourceFrom sourceFrom = Optional.ofNullable(orderCreateRequestType.getIntegrationSoaRequestType())
                .map(IntegrationSoaRequestType::getSourceFrom).orElse(null);
        result.setTripAdditionalOrder(isTripAdditionalOrder(tripId, sourceFrom, searchTripDetailResponseType));
        // 沿用授权设置
        FollowApprovalInfoInput followApprovalInfoInput = orderCreateRequestType.getFollowApprovalInfoInput();
        if (StringUtil.isBlank(
            Optional.ofNullable(followApprovalInfoInput).map(FollowApprovalInfoInput::getFollowOrderId).orElse(null))) {
            return result;
        }
        long followOrderNo = Long.parseLong(Optional.ofNullable(followApprovalInfoInput)
                .map(FollowApprovalInfoInput::getFollowOrderId).orElse("0"));
        if (followOrderNo > 0) {
            result.setAuthFromTripId(true);
            result.setAuthFromTripOriOrderId(followOrderNo);
            result.setTripAdditionalOrder(true);
        }
        return result;
    }

    /**
     * 是否是行程补单
     *
     * @param tripId
     * @return
     */
    protected boolean isTripAdditionalOrder(Long tripId, SourceFrom sourceFrom,
                                            SearchTripDetailResponseType searchTripDetailResponseType) {
        if (SourceFrom.Offline.equals(sourceFrom)) {
            return false;
        }
        if (tripId == null || tripId <= 0) {
            return false;
        }

        if (searchTripDetailResponseType != null
                && searchTripDetailResponseType.getBasicInfo() != null) {
            return !StringUtil.equalsIgnoreCase(
                    searchTripDetailResponseType.getBasicInfo().getTripStatus(), "N");
        }

        return false;
    }

    private PaymentInfoEntity getPaymentInfo(
            OrderCreateRequestType orderCreateRequestType,
            QueryCheckAvailContextResponseType queryCheckAvailContextResponseType,
            PayConfigResponseType payConfigResponseType,
            ResourceToken resourceToken) {
        PaymentInfoEntity result = new PaymentInfoEntity();
        HotelPayTypeEnum roomPayType = HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        result.setPrepayType(getPrepayType(roomPayType));
        if (HotelPayTypeEnum.MIX_PAY == roomPayType) {
            buildMixPayWayInfo(roomPayType, result, resourceToken);
        }
        CorpPayInfo corpPayInfoTO = new CorpPayInfo();
        corpPayInfoTO.setServiceFeeInfo(
                getServiceFeeInfo(resourceToken, orderCreateRequestType.getHotelPayTypeInput()));
        result.setCorpPayInfo(corpPayInfoTO);
        result.setPayMode(
                getPayMode(roomPayType, orderCreateRequestType.getIntegrationSoaRequestType(), payConfigResponseType));
        return result;
    }

    public static String getPrepayType(HotelPayTypeEnum bffPayTypeEnum) {
        if (bffPayTypeEnum == null) {
            return null;
        }
        switch (bffPayTypeEnum) {
            case CORP_PAY:
            case ADVANCE_PAY:
            case FLASH_STAY_PAY:
                return "ACCNT";
            case MIX_PAY:
                return "MIX_PAY";
            case SELF_PAY:
            case UNION_PAY:
                return "SELF_PAY";
            case CORPORATE_CARD_PAY:
                return BIZC;
            case PRBAL:
                return bffPayTypeEnum.getCode();
            default:
                return null;
        }
    }

    /**
     * 获取房费下沉计算的价格
     * @param roomPayType
     * @param result
     */
    private void buildMixPayWayInfo(HotelPayTypeEnum roomPayType, PaymentInfoEntity result, ResourceToken resourceToken) {
        if (roomPayType != HotelPayTypeEnum.MIX_PAY) {
            return;
        }
        if (resourceToken.getBookInitResourceToken() == null
                || resourceToken.getBookInitResourceToken().getAggBookPriceResourceToken() == null) {
            return;
        }
        AggBookPriceResourceToken aggBookPriceResourceToken = resourceToken.getBookInitResourceToken().getAggBookPriceResourceToken();
        String payCurrency =
                Optional.ofNullable(
                                aggBookPriceResourceToken.getCurrency())
                        .map(currency -> currency.replace(CNY, RMB)).orElse(RMB);

        MixPaymentWayEntity account = new MixPaymentWayEntity();
        account.setMixPayWay(MixPayWayEnum.ACCNT);

        BigDecimal accountPaymentAmount = aggBookPriceResourceToken.getAccountPaymentAmount();
        account.setCNYAmount(accountPaymentAmount);
        account.setPayAmount(accountPaymentAmount);

        MixPaymentWayEntity guest = new MixPaymentWayEntity();
        guest.setMixPayWay(MixPayWayEnum.GUEST);
        BigDecimal individualPaymentAmount = aggBookPriceResourceToken.getIndividualPaymentAmount();
        guest.setPayAmount(individualPaymentAmount);
        guest.setCNYAmount(individualPaymentAmount);

        result.setMixPayWayInfo(Arrays.asList(account, guest));
        BigDecimal mixPayAccountExtraRatio = aggBookPriceResourceToken.getMixPayAccountExtraRatio();
        if (mixPayAccountExtraRatio != null && mixPayAccountExtraRatio.compareTo(BigDecimal.ZERO) > 0) {
            result.setMixPayAccntExtraPaidRatio(mixPayAccountExtraRatio);
            BigDecimal mixPayExtraAmount = aggBookPriceResourceToken.getMixPayAccountExtraAmount();
            result.setMixPayAccntExtraPaidAmount(mixPayExtraAmount);
        }
    }

    private static final String PAY_TO_CTRIP = "Ctrip"; // 担保到携程

    public static ServiceFeeInfo getServiceFeeInfo(ResourceToken resourceToken,
        List<HotelPayTypeInput> hotelPayTypeInputs) {
        if (resourceToken.getBookInitResourceToken() == null
            || resourceToken.getBookInitResourceToken().getServiceChargeResourceToken() == null
            || MathUtils.isLessOrEqualsZero(
            resourceToken.getBookInitResourceToken().getServiceChargeResourceToken().getServiceChargeAmount())) {
            return null;
        }
        HotelPayTypeEnum servicePayType = HotelPayTypeUtil.getServicePayType(hotelPayTypeInputs, resourceToken);
        ServiceChargeResourceToken serviceChargeResourceToken =
            resourceToken.getBookInitResourceToken().getServiceChargeResourceToken();
        ServiceFeeInfo serviceFeeInfo = new ServiceFeeInfo();
        serviceFeeInfo.setPayType(getServicePrepayType(servicePayType));
        serviceFeeInfo.setServiceFee(serviceChargeResourceToken.getServiceChargeAmount());
        serviceFeeInfo.setServiceFeeByRoomNight(serviceChargeResourceToken.getServiceFeeByRoomNight());
        serviceFeeInfo.setServiceFeeItemList(
            getServiceFeeItemList(serviceChargeResourceToken.getServiceFeeItemResourceTokens()));
        return serviceFeeInfo;
    }

    public static List<ServiceFeeItemType> getServiceFeeItemList(
            List<ServiceFeeItemResourceToken> serviceFeeItemResourceTokens) {
        if (CollectionUtil.isEmpty(serviceFeeItemResourceTokens)) {
            return null;
        }

        List<ServiceFeeItemType> result = new ArrayList<>();
        for (ServiceFeeItemResourceToken item : serviceFeeItemResourceTokens) {
            if (item == null) {
                continue;
            }
            if (item.getChargeItemCode() == null) {
                continue;
            }
            ServiceFeeItemType serviceFeeItemType = new ServiceFeeItemType();
            serviceFeeItemType.setType(item.getChargeItemCode());
            serviceFeeItemType.setOriginalAmount(item.getOriginalAmount());
            serviceFeeItemType.setSettlementAmount(item.getSettlementAmount());
            result.add(serviceFeeItemType);
        }
        return result;
    }

    public static String getServicePrepayType(HotelPayTypeEnum bffPayTypeEnum) {
        switch (bffPayTypeEnum) {
            case CORP_PAY:
            case ADVANCE_PAY:
            case MIX_PAY:
            case FLASH_STAY_PAY:
                return "ACCNT";
            case SELF_PAY:
            case UNION_PAY:
                return "SELF_PAY";
            case CORPORATE_CARD_PAY:
                return BIZC;
            case PRBAL:
                return bffPayTypeEnum.getCode();
            default:
                return "";
        }
    }

    /**
     * 丰享模式/水发
     */
    private static final String PAYMODE_CUSTOMERPAYTRIGGERTOACCNT = "CustomerPayTriggerToAccnt";
    /**
     * 银联
     */
    private static final String PAYMODE_UNIONPAY = "UnionPay";

    private String getPayMode(HotelPayTypeEnum payType, IntegrationSoaRequestType integrationSoaRequestType,
                              PayConfigResponseType payConfigResponseType) {
        if (payType == HotelPayTypeEnum.UNION_PAY) {
            return PAYMODE_UNIONPAY;
        }
        if (getDoublePay(payType, integrationSoaRequestType, payConfigResponseType)) {
            return PAYMODE_CUSTOMERPAYTRIGGERTOACCNT;
        }
        return null;
    }

    protected boolean getDoublePay(HotelPayTypeEnum bffPayTypeEnum, IntegrationSoaRequestType integrationSoaRequestType,
                                   PayConfigResponseType payConfigResponseType) {
        if ((bffPayTypeEnum != HotelPayTypeEnum.CORP_PAY && bffPayTypeEnum != HotelPayTypeEnum.ADVANCE_PAY
                && bffPayTypeEnum != HotelPayTypeEnum.FLASH_STAY_PAY)
                || SourceFrom.H5 != integrationSoaRequestType.getSourceFrom()) {
            return false;
        }
        return "CustomerPayTriggerToAccnt".equalsIgnoreCase(
                Optional.ofNullable(payConfigResponseType).map(PayConfigResponseType::getPayMode).orElse(null));
    }
    /**
     * 简版套餐类型
     */
    public static final Integer SIMPLE_PRODUCT_TYPE = 3;
    /**
     * 正常套餐类型
     */
    public static final Integer COMMON_PRODUCT_TYPE = 4;
    public List<XProductInfo> getXProductInfoList(GetPackageRoomListResponseType getPackageRoomListResponseType, List<StrategyInfo> strategyInfos, String corpId) {
        if (needNewPackageProduct(strategyInfos, corpId)) {
            return null;
        }
        if (getPackageRoomListResponseType == null) {
            return null;
        }
        if (isUseNewPackageInfo(getPackageRoomListResponseType.getExtendInfo())) {
            return buildXProductInfoList(getPackageRoomListResponseType.getProductStaticInfoList());
        }
        PackageRoomInfoType packageRoomInfoType = Optional.ofNullable(getPackageRoomListResponseType.getPackageRoomInfo())
            .orElse(new ArrayList<>())
            .stream()
            .findFirst()
            .orElse(null);
        if (packageRoomInfoType == null) {
            return null;
        }
        if (packageRoomInfoType.getPackageId() == null || CollectionUtil.isEmpty(packageRoomInfoType.getXProductInfo())) {
            return null;
        }
        return packageRoomInfoType.getXProductInfo().stream().map(
                xProductEntityType -> {
                    XProductInfo xProductInfo = new XProductInfo();
                    xProductInfo.setPkgId(packageRoomInfoType.getPackageId());
                    xProductInfo.setXProductId(xProductEntityType.getXProductId());
                    if (isSimplePackageRoom(packageRoomInfoType)) {
                        xProductInfo.setProductType(SIMPLE_PRODUCT_TYPE);
                    } else {
                        xProductInfo.setProductType(COMMON_PRODUCT_TYPE);
                        xProductInfo.setQuantity(xProductEntityType.getQuantity());
                    }
                    return xProductInfo;
                }).collect(Collectors.toList());
    }

    private List<XProductInfo> buildXProductInfoList(List<XProductStaticInfoType> productStaticInfoList) {
        if (CollectionUtil.isEmpty(productStaticInfoList)) {
            return null;
        }
        XProductStaticInfoType xProductStaticInfoType = CollectionUtil.findFirst(productStaticInfoList, Objects::nonNull);
        if (xProductStaticInfoType == null || CollectionUtil.isEmpty(xProductStaticInfoType.getProductSpuInfoList())) {
            return null;
        }
        if (xProductStaticInfoType.getPackageId() == null && StringUtil.isBlank(xProductStaticInfoType.getPackageToken())) {
            return null;
        }
        return xProductStaticInfoType.getProductSpuInfoList().stream().filter(Objects::nonNull)
                .filter(z -> CollectionUtil.isNotEmpty(z.getProductSkuInfoList()))
                .flatMap(x -> x.getProductSkuInfoList().stream())
                .filter(Objects::nonNull)
                .map(t -> {
            XProductInfo xProductInfo = new XProductInfo();
            xProductInfo.setProductId(t.getProductId());
            xProductInfo.setProductType(COMMON_PRODUCT_TYPE);
            xProductInfo.setQuantity(t.getQuantity());
            return xProductInfo;
        }).collect(Collectors.toList());


    }

    private static final String NEW_XPRODUCT = "newXProduct";

    private boolean isUseNewPackageInfo(PackageExtendInfoType extendInfo) {
        if (extendInfo == null || CollectionUtil.isEmpty(extendInfo.getFlagInfoMap())) {
            return false;
        }
        return com.ctrip.corp.bff.framework.template.common.constant.BooleanConstant.TRUE.equalsIgnoreCase(extendInfo.getFlagInfoMap().get(NEW_XPRODUCT));
    }

    private final static Integer SIMPLE_PACKAGE_TYPE = 23;
    /**
     * 是否是简版套餐
     * @return
     */
    public Boolean isSimplePackageRoom(PackageRoomInfoType packageRoomInfoType) {
        return SIMPLE_PACKAGE_TYPE.equals(Optional.ofNullable(packageRoomInfoType).map(PackageRoomInfoType::getProductType).orElse(0));
    }

    private static final String PAYER_MIX = "MIX";

    /**
     * 构建创单场景信息
     *
     * @param
     * @param
     * @return
     */
    protected CreateBookingScenarioEntity buildBookingScenarioInfo(
            OrderCreateRequestType orderCreateRequestType, WrapperOfAccount.AccountInfo accountInfo,
            AllocationResultToken costAllocationToken, GetSubAccountConfigResponseType getSubAccountConfigResponseType) {
        CreateBookingScenarioEntity result = new CreateBookingScenarioEntity();
        // 新版出差总是false
        result.setTravelApplyOrder(false);
        // 新版出差总是true
        result.setOAAuthOrder(true);
        /*
        TODO 要去查 GetSubAccountConfigResponseType
        result.setOAAuthAheadOrder(Optional.ofNullable(controlResult.getAdvanceApprovalResult()).map(AdvanceApprovalResult::isAdvanceApprovalHead).orElse(false));
        Optional.ofNullable(orderCreateRequestType.getPaymentInfos())
            .orElse(new ArrayList<>()).stream()
            .filter(Objects::nonNull).findFirst()
            .map(PaymentInfo::getPayer).orElse(null)
        */
        result.setMixPay(
                HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput()) == HotelPayTypeEnum.MIX_PAY);
        if (costAllocationToken != null) {
            result.setNeedShareAmount(CollectionUtil.isNotEmpty(costAllocationToken.getSettleAllocationAmount()));
        }
        return result;
    }

    private static final String PWC_CORP_IDS = "PwcCorpIds";
    private static final String PWC = "PWC";
    private static final String CHECKOUT_DELAY = "CheckOutDelay";
    private static final String SERVICE_FEE_V2 = "SERVICE_FEE_V2";

    private static final String NEW_PKG_PRODUCT = "NEW_PKG_PRODUCT";

    /**
     * pwc 场景下，createOrder走agg的cropIds
     */
    private CreateOrderExtInfo getExtInfo(
            OrderCreateRequestType orderCreateRequestType,
            WrapperOfAccount.AccountInfo accountInfo,
            QueryCheckAvailContextResponseType queryCheckAvailContextResponseType,
            ResourceToken resourceToken, SSOInfoQueryResponseType ssoInfoQueryResponseType,
            ApprovalFlowComputeResponseType approvalFlowComputeResponseType,
            MatchApprovalFlowResponseType matchApprovalFlowResponseType,
            OrderCreateToken orderCreateToken, WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
            GetPlatformRelationByUidResponseType getPlatformRelationByUidResponseType,
            GetPackageRoomListResponseType getPackageRoomListResponseType,
            QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType,
            RegisterResponseType registerResponseType,
            QueryIndividualAccountResponseType queryIndividualAccountResponseType,
            Map<String, StrategyInfo> strategyInfoMap) {
        CreateOrderExtInfo createOrderExtInfo = new CreateOrderExtInfo();
        createOrderExtInfo.setMemberRegisterId(registerResponseType != null ? registerResponseType.getRegisterId() : orderCreateToken.getMembershipRegisterId());
        String membershipNo = Optional.ofNullable(orderCreateRequestType.getMembershipInfo())
                .map(MembershipInfo::getMembershipNo).orElse(null);
        createOrderExtInfo.setMembershipCardNum(membershipNo);
        createOrderExtInfo.setAid(buildAid(ssoInfoQueryResponseType));
        createOrderExtInfo.setSid(buildSid(ssoInfoQueryResponseType));
        String corpId = RequestHeaderUtil.getCorpId(orderCreateRequestType.getIntegrationSoaRequestType());
        boolean pwcSwitch = QConfigOfCustomConfig.isSupport(CustomConfigKeyConstant.PWC_CORP_IDS, corpId);
        boolean pwcCase = checkIsPwcCase(orderCreateRequestType, accountInfo, checkAvailInfo);
        if (pwcSwitch && pwcCase) {
            // pwc 支付场景tag
            addOrderTagInfo(createOrderExtInfo, PWC);
        }

        if (isApplyModifyOrExtend(orderCreateRequestType)) {
            // 修改申请单-创单传参
            createOrderExtInfo.setOrderModifyInfo(getOrderModifyInfo(orderCreateRequestType, resourceToken));
            if (StrategyOfBookingInitUtil.extend(orderCreateRequestType.getStrategyInfos())) {
                // 延住订单场景
                addOrderTagInfo(createOrderExtInfo, CHECKOUT_DELAY);
            }
        }
        addOrderTagInfo(createOrderExtInfo, "NeedNotSaveCommonData");
        // 心程贝
        if (supportCreateEntranceOfCowrie(strategyInfoMap, orderCreateRequestType, queryIndividualAccountResponseType)) {
            addOrderTagInfo(createOrderExtInfo, "CreateEntranceOfCowrie");
        }
        addOrderTagInfo(createOrderExtInfo, SERVICE_FEE_V2);
        // 新版套餐
        if (isUseNewPackageInfo(Optional.ofNullable(getPackageRoomListResponseType).map(GetPackageRoomListResponseType::getExtendInfo).orElse(null))) {
            addOrderTagInfo(createOrderExtInfo, NEW_PKG_PRODUCT);
        }
        createOrderExtInfo.setExternalId(
            buildExtendId(approvalFlowComputeResponseType, matchApprovalFlowResponseType, orderCreateToken,
                orderCreateRequestType, accountInfo));

        // 积分新节点
        Boolean supportOfflinePoint = supportOfflinePoint(queryCheckAvailContextResponseType);
        String pointMode = buildPointMode(checkAvailInfo);
        createOrderExtInfo.setPointInfo(buildPointInfo(supportOfflinePoint, orderCreateRequestType.getMembershipInfo(),
                orderCreateRequestType.getHotelBookPassengerInputs(),
                pointMode,
                orderCreateRequestType.getHotelContactorInfo(), queryBizModeBindRelationResponseType));

        createOrderExtInfo.setCustomerProperty(
            getCustomerProperty(orderCreateRequestType.getMembershipInfo(), checkAvailInfo, orderCreateRequestType,
                getPlatformRelationByUidResponseType, queryBizModeBindRelationResponseType));
        createOrderExtInfo.setSsoInfo(buildSsoEntity(orderCreateRequestType, ssoInfoQueryResponseType));
        return createOrderExtInfo;
    }

    /**
     * 是否支持心程贝
     * 国内站：特定入口进入/筛选了心程贝，均在transfer中-----vo作为策略传入
     * 蓝色空间：以ams配置为准---vo策略传入以ams配置为准时，判断后台开关
     *
     * @return
     */
    private boolean supportCreateEntranceOfCowrie(Map<String, StrategyInfo> strategyInfos,
        OrderCreateRequestType orderCreateRequestType,
        QueryIndividualAccountResponseType queryIndividualAccountResponseType) {
        if (StrategyOfBookingInitUtil.bookingWithPersonalAccount(strategyInfos)) {
            return true;
        }
        if (!StrategyOfBookingInitUtil.personalAccountByAms(strategyInfos)) {
            return false;
        }
        return PersonAccountUtil.supportPersonalAccount(queryIndividualAccountResponseType,
            orderCreateRequestType.getCorpPayInfo(), orderCreateRequestType.getIntegrationSoaRequestType());
    }

    /**
     * 会员卡积分
     */
    private static final String HYKMS = "HYKMS";

    /**
     * 手机号积分
     */
    private static final String SJHMS = "SJHMS";

    /**
     * 线下积分
     */
    private static final String XXMS = "XXMS";

    private static final String MEMBER_CARD = "MEMBER_CARD";

    private static final String PHONE = "PHONE";

    private static final String OFFLINE = "OFFLINE";

    private String buildPointMode(WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
        if (!checkAvailInfo.isOnlyGroupMemberCanBook() && !checkAvailInfo.isBonusPointRoom()) {
            return null;
        }
        if (StringUtil.isBlank(checkAvailInfo.getPointsMode())) {
            return null;
        }
        if (StringUtil.equalsIgnoreCase(HYKMS, checkAvailInfo.getPointsMode())) {
            return MEMBER_CARD;
        }
        if (StringUtil.equalsIgnoreCase(SJHMS, checkAvailInfo.getPointsMode())) {
            return PHONE;
        }
        if (StringUtil.equalsIgnoreCase(XXMS, checkAvailInfo.getPointsMode())) {
            return OFFLINE;
        }
        return null;
    }

    private SsoEntity buildSsoEntity(OrderCreateRequestType orderCreateRequestType,
        SSOInfoQueryResponseType ssoInfoQueryResponseType) {
        if (ssoInfoQueryResponseType == null) {
            return null;
        }
        SsoEntity ssoEntity = new SsoEntity();
        if (ssoInfoQueryResponseType.getSsoBaseInfo() != null && CollectionUtil.isNotEmpty(
            ssoInfoQueryResponseType.getSsoBaseInfo().getApprovers())) {
            String confirmPersonUid1 = ssoInfoQueryResponseType.getSsoBaseInfo().getApprovers().stream()
                .filter(t -> "1".equalsIgnoreCase(t.getLevel())).collect(Collectors.toList()).stream().findFirst()
                .orElse(new Approver()).getUid();
            String confirmPersonUid2 = ssoInfoQueryResponseType.getSsoBaseInfo().getApprovers().stream()
                .filter(t -> "2".equalsIgnoreCase(t.getLevel())).collect(Collectors.toList()).stream().findFirst()
                .orElse(new Approver()).getUid();
            if (StringUtil.isNotEmpty(confirmPersonUid1) || StringUtil.isNotEmpty(confirmPersonUid2)) {
                ssoEntity.setSsoKey(
                    Optional.ofNullable(orderCreateRequestType.getSsoInput()).map(SSOInput::getSsoKey).orElse(null));
            }
        }
        if (CollectionUtil.isNotEmpty(
            Optional.ofNullable(ssoInfoQueryResponseType.getSsoBaseInfo()).map(SSOBaseInfo::getCostCenterInfo)
                .map(SSOCostCenterInfo::getCostCenterInfo).orElse(null))) {

        }
        ssoEntity.setCostCenterFromSsoInfo(buildCostCenterFromSsoInfo(ssoInfoQueryResponseType));
        return ssoEntity;
    }

    private boolean buildCostCenterFromSsoInfo(SSOInfoQueryResponseType ssoInfoQueryResponseType) {
        if (CollectionUtil.isEmpty(
            Optional.ofNullable(ssoInfoQueryResponseType).map(SSOInfoQueryResponseType::getSsoBaseInfo)
                .map(SSOBaseInfo::getCostCenterInfo).map(SSOCostCenterInfo::getCostCenterInfo).orElse(null))) {
            return false;
        }
        Map<String, String> costCenterInfo =
            ssoInfoQueryResponseType.getSsoBaseInfo().getCostCenterInfo().getCostCenterInfo();
        return SSO_COST_CENTER_KEY.stream().anyMatch(key -> StringUtil.isNotBlank(costCenterInfo.get(key)));
    }



    private String buildSid(SSOInfoQueryResponseType ssoInfoQueryResponseType) {
        if (ssoInfoQueryResponseType == null || CollectionUtil.isEmpty(
            ssoInfoQueryResponseType.getSsoExtendInfoList())) {
            return StringUtil.EMPTY;
        }
        SSOExtendInfo commonSSOExtendInfo = ssoInfoQueryResponseType.getSsoExtendInfoList().stream()
            .filter(ssoExtendInfo -> StringUtil.equalsIgnoreCase(ssoExtendInfo.getProductType(), "Common"))
            .collect(Collectors.toList()).stream().findFirst().orElse(null);
        if (commonSSOExtendInfo == null || CollectionUtil.isEmpty(commonSSOExtendInfo.getExtendInfo())) {
            return StringUtil.EMPTY;
        }
        String aid = commonSSOExtendInfo.getExtendInfo().get("sid");
        return StringUtil.isBlank(aid) ? StringUtil.EMPTY : aid;
    }

    private String buildExtendId(ApprovalFlowComputeResponseType approvalFlowComputeResponseType,
        MatchApprovalFlowResponseType matchApprovalFlowResponseType, OrderCreateToken orderCreateToken,
        OrderCreateRequestType orderCreateRequestType, WrapperOfAccount.AccountInfo accountInfo) {
        ApprovalInfoType approvalInfoType =
            OrderCreateProcessorOfUtil.buildApprovalInfoType(approvalFlowComputeResponseType,
                matchApprovalFlowResponseType, orderCreateToken, orderCreateRequestType, accountInfo);
        return Optional.ofNullable(approvalInfoType).map(ApprovalInfoType::getExternalId).orElse(null);
    }

    private String buildAid(SSOInfoQueryResponseType ssoInfoQueryResponseType) {
        if (ssoInfoQueryResponseType == null || CollectionUtil.isEmpty(
            ssoInfoQueryResponseType.getSsoExtendInfoList())) {
            return StringUtil.EMPTY;
        }
        SSOExtendInfo commonSSOExtendInfo = ssoInfoQueryResponseType.getSsoExtendInfoList().stream()
            .filter(ssoExtendInfo -> StringUtil.equalsIgnoreCase(ssoExtendInfo.getProductType(), "Common"))
            .collect(Collectors.toList()).stream().findFirst().orElse(null);
        if (commonSSOExtendInfo == null || CollectionUtil.isEmpty(commonSSOExtendInfo.getExtendInfo())) {
            return StringUtil.EMPTY;
        }
        String aid = commonSSOExtendInfo.getExtendInfo().get("aid");
        return StringUtil.isBlank(aid) ? StringUtil.EMPTY : aid;
    }

    private boolean supportOfflinePoint(QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
        if (queryCheckAvailContextResponseType.getRoomInfo().getBonusPointInfo() == null) {
            return false;
        }
        if (!BooleanUtils.isTrue(
                queryCheckAvailContextResponseType.getRoomInfo().getBonusPointInfo().isBonusPointRoom())) {
            return false;
        }
        if (EarnPointsPatternEnum.TO_HOTEL.equals(BenefitsAvailableUtil.convert2EarnPointsPatternEnum(
                queryCheckAvailContextResponseType.getRoomInfo().getBonusPointInfo().getPointsMode()))) {
            return true;
        }
        return false;
    }


    /**
     * 提交订单,判断是否pwc
     * @param orderCreateRequestType
     * @param accountInfo
     * @param
     * @return
     */
    private boolean checkIsPwcCase(
            OrderCreateRequestType orderCreateRequestType,
            WrapperOfAccount.AccountInfo accountInfo,
            WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
        // sourceFrom ONLINE 判断
        SourceFrom sourceFrom = Optional.ofNullable(orderCreateRequestType.getIntegrationSoaRequestType())
                .map(IntegrationSoaRequestType::getSourceFrom).orElse(null);
        if (BooleanUtils.isFalse(Objects.equals(sourceFrom, SourceFrom.Online))) {
            return Boolean.FALSE;
        }
        // 因公 判断
        if (CorpPayInfoUtil.isPrivate(orderCreateRequestType.getCorpPayInfo())) {
            return Boolean.FALSE;
        }
        // 非协议房 判断
        if (checkAvailInfo.getRoomTypeEnum() == RoomTypeEnum.C) {
            return Boolean.FALSE;
        }
        if (BooleanUtil.isFalse(accountInfo.isHtlPrintTicketAfterConfirm())) {
            return Boolean.FALSE;
        }

        // 单订单 判断
        if (accountInfo.isPackageEnabled()) {
            return Boolean.FALSE;
        }

        if (isMultiCurrency(accountInfo)) {
            return Boolean.FALSE;
        }

        // 非海外 判断
        Integer cityId = Optional.ofNullable(orderCreateRequestType.getCityInput())
                .map(CityInput::getCityId).orElse(0);
        boolean isOverSea = CityInfoUtil.oversea(cityId);
        if (isOverSea) {
            return Boolean.FALSE;
        }
        if (checkAvailInfo.isTmcPrice()) {
            return Boolean.FALSE;
        }
        // 公账支付或担保支付 判断
        if (!isAccountOrGuarantee(orderCreateRequestType)) {
            return Boolean.FALSE;
        }
        return true;
    }

    private boolean isMultiCurrency(WrapperOfAccount.AccountInfo accountInfo) {
        return !(StringUtil.equalsIgnoreCase(accountInfo.getCurrency(), CommonConstant.RMB)
                || StringUtil.equalsIgnoreCase(accountInfo.getCurrency(), CommonConstant.CNY));
    }

    /**
     * 是否现付无担保或预付到携程
     * @param orderCreateRequestType
     * @param
     * @return
     */
    private boolean isAccountOrGuarantee(
            OrderCreateRequestType orderCreateRequestType) {
        HotelPayTypeEnum payType = HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput());
        boolean isAccnt = Boolean.FALSE;
        if (Objects.nonNull(payType)) {
            isAccnt = payType == HotelPayTypeEnum.CORP_PAY || payType == HotelPayTypeEnum.ADVANCE_PAY
                    || payType == HotelPayTypeEnum.FLASH_STAY_PAY;
        }
        if (isAccnt) {
            return Boolean.TRUE;
        }
        return HotelPayTypeEnum.GUARANTEE_CORP_PAY == payType;
    }

    private OrderModifyInfoType getOrderModifyInfo(
            OrderCreateRequestType orderCreateRequestType, ResourceToken resourceToken) {
        if (StrategyOfBookingInitUtil.onlyApplyModify(orderCreateRequestType.getStrategyInfos())) {
            OrderModifyInfoType orderModifyInfoType = new OrderModifyInfoType();
            // 申请修改场景传原单+新单入离日期
            HotelDateRangeInfo hotelDateRangeInfo = Optional.ofNullable(orderCreateRequestType.getHotelModifyBookInput())
                    .map(HotelModifyBookInput::getHotelDateRangeInfo).orElse(null);
            if (hotelDateRangeInfo != null) {
                try {
                    orderModifyInfoType.setCheckInTime(hotelDateRangeInfo.getCheckIn());
                    orderModifyInfoType.setCheckOutTime(hotelDateRangeInfo.getCheckOut());
                } catch (Exception e) {
                    LogUtil.loggingClogOnly(LogLevelEnum.Error, MapperOfCreateOrderRequestType.class,
                        "getOrderModifyInfo.error", ExceptionUtil.getException(e), null);
                }
            }
            ModifyPolicyTypeEnum modifyPolicyTypeEnum = Optional.ofNullable(resourceToken.getOrderResourceToken())
                    .map(OrderResourceToken::getModifyPolicyTypeEnum).orElse(ModifyPolicyTypeEnum.UN_KNOW);
            orderModifyInfoType.setPolicyType(modifyPolicyTypeEnum.getValue());
            return orderModifyInfoType;
        }
        return null;
    }

    /**
     * 积分新节点
     *
     * @param supportOfflinePointInfo
     * @return
     */
    public PointInfo buildPointInfo(Boolean supportOfflinePointInfo, MembershipInfo membershipInfo,
                                    List<HotelBookPassengerInput> hotelBookPassengerInputs,
                                    String pointMode,
                                    HotelContactorInfo contactInfo,
                                    QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType) {
        String membershipCardNum = Optional.ofNullable(membershipInfo).map(MembershipInfo::getMembershipNo)
                .orElse(null);
        String membershipUid = Optional.ofNullable(membershipInfo).map(MembershipInfo::getMembershipUid)
                .orElse(null);
        PhoneInfo phoneInfoType = Optional.ofNullable(membershipInfo).map(MembershipInfo::getPhoneInfo).orElse(null);
        String bizUid = buildBizUid(queryBizModeBindRelationResponseType, membershipUid, hotelBookPassengerInputs);
        if (BooleanUtils.isTrue(supportOfflinePointInfo) || StringUtil.equalsIgnoreCase(pointMode, OFFLINE)) {
            PointInfo pointInfoTO = new PointInfo();
            pointInfoTO.setPointType(PointTypeEnum.OFFLINE_POINTS.getCode());
            pointInfoTO.setPointSubType(pointMode);
            pointInfoTO.setPointCardHolder(bizUid);
            return pointInfoTO;
        }
        if (StringUtil.isNotBlank(bizUid) && StringUtil.isNotBlank(membershipCardNum)) {
            PointInfo pointInfo = new PointInfo();
            pointInfo.setPointCardNum(membershipCardNum);
            pointInfo.setPointCardHolder(bizUid);
            pointInfo.setPointType(PointTypeEnum.ONLINE_POINTS.getCode());
            pointInfo.setPointSubType(pointMode);
            return pointInfo;
        }
        if (StringUtil.isNotBlank(bizUid)
                && supportMembershipPhonePoint(phoneInfoType, contactInfo)) {
            PointInfo pointInfo = new PointInfo();
            pointInfo.setPointCardNum(phoneInfoType.getTransferPhoneNo());
            pointInfo.setPointCardHolder(bizUid);
            pointInfo.setPointType(PointTypeEnum.ONLINE_POINTS.getCode());
            pointInfo.setPointSubType(pointMode);
            return pointInfo;
        }
        return null;
    }

    private String buildBizUid(QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType,
                               String membershipUid,
                               List<HotelBookPassengerInput> hotelBookPassengerInputs) {
        if (CollectionUtil.isEmpty(hotelBookPassengerInputs)) {
            return null;
        }
        HotelBookPassengerInput hotelBookPassengerInput = CollectionUtil.findFirst(hotelBookPassengerInputs, t -> t != null
                && t.getHotelPassengerInput() != null && BooleanConstant.STR_F.equalsIgnoreCase(t.getHotelPassengerInput().getEmployee())
                && (StringUtil.equalsIgnoreCase(membershipUid, t.getHotelPassengerInput().getUid())
                || StringUtil.equalsIgnoreCase(membershipUid, t.getHotelPassengerInput().getInfoId())));
        if (hotelBookPassengerInput != null) {
            return membershipUid;
        }
        return buildBizUid(queryBizModeBindRelationResponseType, membershipUid);
    }

    protected static boolean supportMembershipPhonePoint(PhoneInfo phoneInfoType, HotelContactorInfo contactInfo) {
        if (phoneInfoType == null || contactInfo == null) {
            return false;
        }
        if (StringUtil.isBlank(phoneInfoType.getCountryCode()) || contactInfo.getPhoneInfo() == null) {
            return false;
        }
        if (!COUNTRY_CODE_86.equalsIgnoreCase(phoneInfoType.getCountryCode())) {
            return false;
        }
        if (!StringUtil.equalsIgnoreCase(phoneInfoType.getCountryCode(), contactInfo.getPhoneInfo().getCountryCode())) {
            return false;
        }
        return StringUtil.equalsIgnoreCase(phoneInfoType.getTransferPhoneNo(),
            contactInfo.getPhoneInfo().getTransferPhoneNo());
    }


    private void addOrderTagInfo(CreateOrderExtInfo createOrderExtInfo, String key) {
        OrderTagInfo orderTagInfo = new OrderTagInfo();
        orderTagInfo.setKey(key);
        orderTagInfo.setValue(BooleanUtil.parseStr(Boolean.TRUE));
        if (CollectionUtil.isEmpty(createOrderExtInfo.getOrderTagList())) {
            List<OrderTagInfo> orderTagList = new ArrayList<>();
            orderTagList.add(orderTagInfo);
            createOrderExtInfo.setOrderTagList(orderTagList);
        } else {
            createOrderExtInfo.getOrderTagList().add(orderTagInfo);
        }
    }

    protected CustomerPropertyType getCustomerProperty(MembershipInfo membershipInfo,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo, OrderCreateRequestType orderCreateRequestType,
        GetPlatformRelationByUidResponseType getPlatformRelationByUidResponseType,
                                                       QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType) {
        CustomerPropertyType result = new CustomerPropertyType();
        result.setRegisteredMemberUid(
            buildRegisteredMemberUid(checkAvailInfo, orderCreateRequestType, getPlatformRelationByUidResponseType, queryBizModeBindRelationResponseType));
        result.setGroupUserMembershipId(
            Optional.ofNullable(membershipInfo).map(MembershipInfo::getGroupUserMembershipId).orElse(null));
        result.setGroupMemberShipCode(Optional.ofNullable(membershipInfo).map(MembershipInfo::getGroupMemberShipCode).orElse(null));
        result.setGroupMemberShipLevel(Optional.ofNullable(membershipInfo).map(MembershipInfo::getGroupMemberShipLevel).orElse(null));
        return result;
    }

    protected String buildRegisteredMemberUid(WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        OrderCreateRequestType orderCreateRequestType,
        GetPlatformRelationByUidResponseType getPlatformRelationByUidResponseType,
                                              QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType) {
        String registerUid =
            buildRegisterUid(checkAvailInfo, orderCreateRequestType, getPlatformRelationByUidResponseType, queryBizModeBindRelationResponseType);
        if (StringUtil.isBlank(registerUid)) {
            return registerUid;
        }
        if (StringUtil.equalsIgnoreCase(checkAvailInfo.getGroupRegisterRule(), TRIP_REGISTER)
                || StringUtil.equalsIgnoreCase(checkAvailInfo.getGroupRegisterRule(), BUSINESS_TRAVEL_REGISTER)) {
            return registerUid;
        }
        return null;
    }

    private boolean buildNeedRegisterRoom(WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
        if (checkAvailInfo == null) {
            return false;
        }
        if (!checkAvailInfo.isGroupMemberShip()) {
            return false;
        }
        if (!checkAvailInfo.isNeedRegister()) {
            return false;
        }
        if (StringUtil.equalsIgnoreCase(checkAvailInfo.getGroupRegisterRule(), NO_REGISTER)) {
            return false;
        }
        if (TemplateNumberUtil.isZeroOrNull(checkAvailInfo.getGroupId())) {
            return false;
        }
        return true;
    }

    private String buildRegisterUid(WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        OrderCreateRequestType orderCreateRequestType,
        GetPlatformRelationByUidResponseType getPlatformRelationByUidResponseType,
                                    QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType) {
        String uid = Optional.ofNullable(orderCreateRequestType).map(OrderCreateRequestType::getIntegrationSoaRequestType)
                .map(IntegrationSoaRequestType::getUserInfo).map(UserInfo::getUserId).orElse(null);
        if (!buildNeedRegisterRoom(checkAvailInfo)) {
            return null;
        }
        if (StringUtil.isBlank(checkAvailInfo.getGroupRegisterRule())) {
            return buildBizUid(queryBizModeBindRelationResponseType, uid);
        }
        switch (checkAvailInfo.getGroupRegisterRule()) {
            case TRIP_REGISTER:
                return Optional.ofNullable(getPlatformRelationByUidResponseType)
                    .map(GetPlatformRelationByUidResponseType::getAccountId).orElse(null);
            case BUSINESS_TRAVEL_REGISTER:
            default:
                return buildBizUid(queryBizModeBindRelationResponseType, uid);
        }
    }

    private String buildBizUid(QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType,
                               String uid) {
        if (queryBizModeBindRelationResponseType == null || StringUtil.isBlank(uid)) {
            return null;
        }
        BizModeBindRelationData bizModeBindRelationData = CollectionUtil.findFirst(queryBizModeBindRelationResponseType.getBizModeBindRelationDataList(),
                t -> t != null && StringUtil.equalsIgnoreCase(uid, t.getDimensionId()));
        if (bizModeBindRelationData == null || StringUtil.isBlank(bizModeBindRelationData.getPrimaryDimensionId())) {
            return null;
        }
        return bizModeBindRelationData.getPrimaryDimensionId();
    }

    public static final String CID = "CID";
    public static final String VID = "VID";

    /**
     * 获取设备信息
     *
     * @param
     * @return
     */
    public DeviceInfo getDeviceInfo(OrderCreateRequestType orderCreateRequestType) {
        if (orderCreateRequestType == null) {
            return null;
        }
        DeviceInfo result = new DeviceInfo();
        result.setUserIP(ThreadContextUtil.getClientIp());
        String rmsToken = Optional.ofNullable(orderCreateRequestType.getClientInfo())
                .map(ClientInfo::getRmsToken).orElse(null);
        result.setRmsToken(rmsToken);
        List<MapString> mapStrings = Optional.ofNullable(orderCreateRequestType.getIntegrationSoaRequestType())
                .map(IntegrationSoaRequestType::getLogIndices)
                .orElse(Collections.emptyList())
                .stream()
                .filter(Objects::nonNull)
                .filter(mapString -> StringUtil.isNotBlank(mapString.getKey())
                        && StringUtil.isNotBlank(mapString.getValue())).collect(Collectors.toList());
        if (CollectionUtil.isEmpty(mapStrings)) {
            return result;
        }
        Map<String, String> logIndicesMap = mapStrings.stream()
                .collect(Collectors.toMap(MapString::getKey, MapString::getValue));
        result.setDeviceID(logIndicesMap.get(CID));
        result.setVid(logIndicesMap.get(VID));
        return result;
    }

    protected CreateOfflineExtEntity buildCreateOfflineExtEntity(OrderCreateRequestType orderCreateRequestType,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        GetInboundParameterResponseType getInboundParameterResponseType) {
        OfflineInfo offlineInfo = Optional.ofNullable(orderCreateRequestType)
                .map(OrderCreateRequestType::getOfflineInfo).orElse(null);
        if (offlineInfo == null) {
            return null;
        }
        SourceFrom sourceFrom = Optional.ofNullable(orderCreateRequestType.getIntegrationSoaRequestType())
                .map(IntegrationSoaRequestType::getSourceFrom).orElse(null);
        if (!SourceFrom.Offline.equals(sourceFrom)) {
            return null;
        }
        CreateOfflineExtEntity result = new CreateOfflineExtEntity();
        result.setSpecialAuth(BooleanUtil.parseStr(Boolean.TRUE).equals(offlineInfo.getEspecialAuth()));
        result.setExigentOrder(BooleanUtil.parseStr(Boolean.TRUE).equals(offlineInfo.getExigentOrder()));
        result.setAni(
            Optional.ofNullable(getInboundParameterResponseType).map(GetInboundParameterResponseType::getCallInfo)
                .map(CallEntity::getANI).orElse(StringUtil.EMPTY));
        result.setUcid(
            Optional.ofNullable(getInboundParameterResponseType).map(GetInboundParameterResponseType::getCallInfo)
                .map(CallEntity::getUCID).orElse(StringUtil.EMPTY));
        result.setUserName(
            Optional.ofNullable(getInboundParameterResponseType).map(GetInboundParameterResponseType::getMembersInfo)
                .map(MembersEntity::getUserName).orElse(StringUtil.EMPTY));
        result.setCtripRemarks(offlineInfo.getOperationRule());
        String transactionId = Optional.ofNullable(orderCreateRequestType.getPaymentInfoInput())
                .map(PayMentInfoInput::getPaymentTransactionId).orElse(null);
        result.setPaymentTransactionId(transactionId);
        result.setAutoConfirm(buildAutoConfirm(offlineInfo, checkAvailInfo));
        return result;
    }

    protected boolean buildAutoConfirm(OfflineInfo offlineInfo, WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
        boolean autoConfirm = BooleanUtil.parseStr(Boolean.TRUE).equals(offlineInfo.getAutoConfirm());
        if (autoConfirm) {
            return true;
        }
        if (checkAvailInfo.isAmadues()) {
            return true;
        }
        return checkAvailInfo.getRoomTypeEnum() == RoomTypeEnum.M && !checkAvailInfo.isTmcPrice();
    }

    public static final Integer RIGHTS_PRODUCT_TYPE = 5;
    public static final String TIME = " 00:00:00";

    public List<TokensEntity> getRights(List<AvailableRight> rights) {
        List<TokensEntity> result = new ArrayList<>();
        if (CollectionUtil.isEmpty(rights)) {
            return null;
        }
        Map<String, List<AvailableRight>> map = rights.stream()
                .filter(availableRight -> !StringUtil
                        .isEmpty(Optional.ofNullable(availableRight).map(AvailableRight::getRightType).orElse(null)))
                .filter(availableRight -> availableRight.getQuantity() != null)
                .collect(Collectors.groupingBy(AvailableRight::getRightType));
        for (RightTypeEnum rightTypeEnum : RightTypeEnum.values()) {
            List<AvailableRight> list = map.get(rightTypeEnum.getCode());
            if (CollectionUtil.isEmpty(list)) {
                continue;
            }
            AvailableRight item = list.get(0);
            if (rightTypeEnum == RightTypeEnum.MEAL) {
                int sum = list.stream().map(AvailableRight::getQuantity)
                        .filter(Objects::nonNull)
                        .map(NumberUtil::parseInt)
                        .reduce(Integer::sum).orElse(0);
                TokensEntity mealEntity = new TokensEntity();
                mealEntity.setProductType(RIGHTS_PRODUCT_TYPE);
                mealEntity.setProductToken(item.getRightCode());
                mealEntity.setQuantum(sum);
                mealEntity.setDetailList(list.stream().map(availableRight -> {
                    XProductDetailInfoType detail = new XProductDetailInfoType();
                    detail.setQuantity(NumberUtil.parseInt(availableRight.getQuantity()));
                    detail.setEffectTime(availableRight.getOption() + TIME);
                    return detail;
                }).collect(Collectors.toList()));
                result.add(mealEntity);
                continue;
            }
            TokensEntity tokensEntity = new TokensEntity();
            tokensEntity.setProductToken(item.getRightCode());
            tokensEntity.setQuantum(NumberUtil.parseInt(item.getQuantity()));
            tokensEntity.setProductType(RIGHTS_PRODUCT_TYPE);
            result.add(tokensEntity);
        }
        return result;
    }

    private static final int XPRODUCT_QUANTUM = 1;

    private List<TokensEntity> getXProductList(List<XProductEntityType> xProductEntityTypeList) {
        if (CollectionUtil.isEmpty(xProductEntityTypeList)) {
            return new ArrayList<>();
        }
        return xProductEntityTypeList.stream().map(xProductEntityType -> {
            TokensEntity tokensEntity = new TokensEntity();
            tokensEntity.setProductToken(xProductEntityType.getXProductToken());
            tokensEntity.setProductType(xProductEntityType.getXProductType());
            tokensEntity.setQuantum(XPRODUCT_QUANTUM);
            return tokensEntity;
        }).collect(Collectors.toList());
    }

    private CreateOriOrderEntity buildCreateOriOrderEntity(
            QueryCheckAvailContextResponseType queryCheckAvailContextResponseType,
            QueryHotelOrderDataResponseType queryHotelOrderDataResponseType,
            OrderCreateRequestType orderCreateRequestType, ResourceToken resourceToken) {

        CreateOriOrderEntity result = new CreateOriOrderEntity();
        if (isApplyModifyOrExtend(orderCreateRequestType)) {
            result.setOrderId(
                    queryCheckAvailContextResponseType.getBaseInfo().getOriginalOrderInfo().getOriginalOrderId());
            return result;
        }
        SourceFrom sourceFrom = orderCreateRequestType.getIntegrationSoaRequestType().getSourceFrom();
        if (!SourceFrom.Offline.equals(sourceFrom)) {
            return null;
        }
        if (resourceToken == null) {
            return null;
        }
        long orderId = Optional.ofNullable(resourceToken.getOrderResourceToken())
                .map(OrderResourceToken::getOrderId).orElse(0L);
        if (orderId <= 0) {
            return null;
        }
        String channelType = Optional.ofNullable(queryHotelOrderDataResponseType)
                .map(QueryHotelOrderDataResponseType::getOrderGenericInfo)
                .map(OrderGenericInfoType::getChannelType).orElse(null);
        result.setOriOrderOffline(
                StringUtil.containsIgnoreCase(channelType, "corpint")
                        || "Offline".equalsIgnoreCase(channelType));
        return result;
    }

    private boolean isApplyModifyOrExtend(OrderCreateRequestType orderCreateRequestType) {
        if (StrategyOfBookingInitUtil.applyModify(orderCreateRequestType.getStrategyInfos())) {
            return true;
        }
        return false;
    }

    private InsuranceInfo getInsuranceInfo(OrderCreateRequestType orderCreateRequestType,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        Map<String, StrategyInfo> strategyInfoMap) {
        if (orderCreateRequestType == null) {
            return null;
        }
        InsuranceInfo result = new InsuranceInfo();
        String userActionToken = Optional.ofNullable(orderCreateRequestType.getUserActionInfo())
                .map(UserActionInfo::getUserActionToken).orElse(null);
        result.setInsuranceBackToken(userActionToken);
        HotelInsuranceInput hotelInsuranceInput = orderCreateRequestType.getHotelInsuranceInput();
        if (hotelInsuranceInput == null || CollectionUtil.isEmpty(hotelInsuranceInput.getHotelInsuranceDetailInputs())) {
            return result;
        }
        result.setInsuranceList(new ArrayList<>());
        hotelInsuranceInput.getHotelInsuranceDetailInputs().forEach(insuranceInfoInput -> {
            if (insuranceInfoInput == null || StringUtil.isBlank(insuranceInfoInput.getInsuranceToken())) {
                return;
            }
            CorpXProductInfoToken insuranceToken = TokenParseUtil.parseToken(insuranceInfoInput.getInsuranceToken(),
                CorpXProductInfoToken.class);
            if (insuranceToken == null) {
                return;
            }
            InsuranceClassifyInfo insuranceClassifyInfo = new InsuranceClassifyInfo();
            insuranceClassifyInfo.setInsuranceToken(insuranceToken.getPriceMark());
            insuranceClassifyInfo.setBizType(insuranceToken.getBiztype());
            insuranceClassifyInfo.setInsuranceDetailList(
                getInsuranceDetailInfo(insuranceInfoInput.getInsuranceHotelBookPassengerInputs(), checkAvailInfo,
                    qconfigOfCertificateInitConfig, strategyInfoMap));
            result.getInsuranceList().add(insuranceClassifyInfo);
        });
        return result;
    }

    private List<InsuranceDetailInfo> getInsuranceDetailInfo(
        List<HotelBookPassengerInput> insuranceHotelBookPassengerInputs,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        Map<String, StrategyInfo> strategyInfoMap) {
        if (CollectionUtil.isEmpty(insuranceHotelBookPassengerInputs)) {
            return null;
        }
        List<InsuranceDetailInfo> result = new ArrayList<>();
        insuranceHotelBookPassengerInputs.forEach(hotelBookPassengerInput -> {
            if (hotelBookPassengerInput == null
                    || hotelBookPassengerInput.getHotelPassengerInput() == null) {
                return;
            }
            HotelPassengerInput hotelPassengerInput = hotelBookPassengerInput.getHotelPassengerInput();
            InsuranceDetailInfo insuranceDetailInfo = new InsuranceDetailInfo();
            String clientMatchId = hotelPassengerInput.getUid();
            if (StringUtil.isBlank(clientMatchId)) {
                clientMatchId = hotelPassengerInput.getInfoId();
            }
            insuranceDetailInfo.setClientMatchId(clientMatchId);
            // 被保人
            insuranceDetailInfo.setInsuredInfo(
                getInsuredInfo(hotelBookPassengerInput, checkAvailInfo, qconfigOfCertificateInitConfig,
                    strategyInfoMap));
            // 投保人
            insuranceDetailInfo.setInsurantInfo(
                getInsurantInfo(hotelBookPassengerInput, checkAvailInfo, qconfigOfCertificateInitConfig,
                    strategyInfoMap));
            result.add(insuranceDetailInfo);
        });
        return result;
    }

    private static final String RELATION_SELF = "00";

    private InsuredInfo getInsuredInfo(HotelBookPassengerInput hotelBookPassengerInput,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        Map<String, StrategyInfo> strategyInfoMap) {
        if (hotelBookPassengerInput == null) {
            return null;
        }
        InsuredInfo result = new InsuredInfo();
        result.setName(
            getInsuranceName(hotelBookPassengerInput, checkAvailInfo, qconfigOfCertificateInitConfig, strategyInfoMap));
        result.setCardType(getCardType(hotelBookPassengerInput.getCertificateInfo()));
        result.setCardNumber(encryptCardNumber(hotelBookPassengerInput.getCertificateInfo()));
        String gender = Optional.ofNullable(hotelBookPassengerInput.getPassengerBasicInfo())
                .map(PassengerBasicInfo::getGender).orElse(null);
        result.setGender(getGender(gender));
        PhoneInfo phoneInfo = hotelBookPassengerInput.getPhoneInfo();
        if (StringUtil.isNotBlank(Optional.ofNullable(phoneInfo).map(PhoneInfo::getTransferPhoneNo).orElse(null))) {
            result.setCountryCode(phoneInfo.getCountryCode());
            result.setPhone(CoreInfoUtil.encrypt(new InfoKey(KeyType.Phone, phoneInfo.getTransferPhoneNo())));
        }
        if (StringUtil.isNotBlank(
            Optional.ofNullable(hotelBookPassengerInput.getEmailInfo()).map(EmailInfo::getTransferEmail)
                .orElse(null))) {
            String transferEmail =
                Optional.of(hotelBookPassengerInput.getEmailInfo()).map(EmailInfo::getTransferEmail).orElse(null);
            result.setEmail(CoreInfoUtil.encrypt(new InfoKey(KeyType.Mail, transferEmail)));
        }
        String birth = Optional.ofNullable(hotelBookPassengerInput.getPassengerBasicInfo())
                .map(PassengerBasicInfo::getBirth).orElse(null);
        result.setBirthday(birth);
        result.setRelation(RELATION_SELF);
        return result;
    }

    private String getInsuranceName(HotelBookPassengerInput hotelBookPassengerInput,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        Map<String, StrategyInfo> strategyInfoMap) {
        String ename = OrderCreateProcessorOfUtil.getEname(hotelBookPassengerInput, checkAvailInfo,
            qconfigOfCertificateInitConfig, strategyInfoMap);
        if (OrderCreateCertificateTypeEnum.findByCertificateType(
            Optional.ofNullable(hotelBookPassengerInput.getCertificateInfo()).map(CertificateInfo::getCertificateType)
                .orElse(null)) == OrderCreateCertificateTypeEnum.IDENTITY_CARD) {
            return StringUtil.isBlank(hotelBookPassengerInput.getName()) ? ename : hotelBookPassengerInput.getName();
        }
        return StringUtil.isBlank(ename) ? hotelBookPassengerInput.getName() : ename;
    }

    private InsurantInfo getInsurantInfo(HotelBookPassengerInput hotelBookPassengerInput,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        Map<String, StrategyInfo> strategyInfoMap) {
        if (hotelBookPassengerInput == null) {
            return null;
        }
        InsurantInfo result = new InsurantInfo();
        result.setName(
            getInsuranceName(hotelBookPassengerInput, checkAvailInfo, qconfigOfCertificateInitConfig, strategyInfoMap));
        result.setCardType(getCardType(hotelBookPassengerInput.getCertificateInfo()));
        result.setCardNumber(encryptCardNumber(hotelBookPassengerInput.getCertificateInfo()));
        String gender = Optional.of(hotelBookPassengerInput.getPassengerBasicInfo())
                .map(PassengerBasicInfo::getGender).orElse(null);
        result.setGender(getGender(gender));
        PhoneInfo phoneInfo = hotelBookPassengerInput.getPhoneInfo();
        if (phoneInfo != null && StringUtil.isNotBlank(phoneInfo.getTransferPhoneNo())) {
            result.setCountryCode(phoneInfo.getCountryCode());
            result.setPhone(CoreInfoUtil.encrypt(new InfoKey(KeyType.Phone, phoneInfo.getTransferPhoneNo())));
        }
        if (hotelBookPassengerInput.getEmailInfo() != null && StringUtil.isNotBlank(
            hotelBookPassengerInput.getEmailInfo().getTransferEmail())) {
            result.setEmail(CoreInfoUtil.encrypt(
                new InfoKey(KeyType.Mail, hotelBookPassengerInput.getEmailInfo().getTransferEmail())));
        }
        String birth = Optional.ofNullable(hotelBookPassengerInput.getPassengerBasicInfo())
                .map(PassengerBasicInfo::getBirth).orElse(null);
        result.setBirthday(birth);
        return result;
    }

    private String getCardType(CertificateInfo certificateInfo) {
        String certificateType = Optional.ofNullable(certificateInfo)
                .map(CertificateInfo::getCertificateType).orElse(null);
        return OrderCreateCertificateTypeEnum.findByCertificateType(certificateType)
                .getInsuranceCreateOrder();
    }

    private String encryptCardNumber(CertificateInfo certificateInfo) {
        if (certificateInfo == null || StringUtil.isBlank(certificateInfo.getTransferCertificateNo())) {
            return null;
        }
        KeyType keyType = findKeyType(OrderCreateCertificateTypeEnum.findByCertificateType(
                certificateInfo.getCertificateType()));
        String transferCertificateNo = certificateInfo.getTransferCertificateNo();
        return Optional.ofNullable(
                        CoreInfoUtil.encrypt(new InfoKey(keyType, transferCertificateNo)))
                .orElse(transferCertificateNo);
    }

    private KeyType findKeyType(OrderCreateCertificateTypeEnum certificateTypeEnum) {
        if (certificateTypeEnum == null) {
            return KeyType.OtherDocument;
        }
        switch (certificateTypeEnum) {
            case IDENTITY_CARD:
                return KeyType.Identity_Card;
            case PASSPORT:
                return KeyType.Passport;
            case STUDENT_ID_CARD:
                return KeyType.Student_Id_Card;
            case MILITARY_CARD:
                return KeyType.MilitaryCard;
            case DRIVING_LICENSE:
                return KeyType.Driver_License;
            case HOMEPERMIT:
                return KeyType.HomePermit;
            case MTP:
                return KeyType.MTP;
            case HKMACPASS:
                return KeyType.HKMacPass;
            case SEAMAN_CARD:
                return KeyType.Seafarer_Passport;
            case FOREIGNER_PERMANENT_RESIDENCE_CARD:
                return KeyType.Foreigner_Permanent_Residence_Card;
            case TRAVELDOCUMENT:
                return KeyType.Travel_Document;
            case TAIWANPASS:
                return KeyType.TaiwanPass;
            case FOREIGNER_PERMANENT_RESIDENT_ID_CARD:
                return KeyType.Foreigner_Permanent_Resident_ID_Card;
            case OTHERDOCUMENT:
                return KeyType.OtherDocument;
            default:
                return KeyType.OtherDocument;
        }
    }

    private static final int MALE = 1;
    private static final int FEMALE = 2;
    private static final int UN_KNOWN = 0;
    private static final String MALE_STR = "M";
    private static final String FEMALE_STR = "F";
    private static final String UN_KNOWN_STR = "U";

    protected static int getGender(String gender) {
        if (gender == null) {
            return UN_KNOWN;
        }
        switch (gender) {
            case MALE_STR:
                return MALE;
            case FEMALE_STR:
                return FEMALE;
            default:
                return UN_KNOWN;
        }
    }

}
