package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookingcheck;

import com.ctrip.corp.agg.hotel.checkbookinglimition.entity.BaseInfoType;
import com.ctrip.corp.agg.hotel.checkbookinglimition.entity.BookingInfoType;
import com.ctrip.corp.agg.hotel.checkbookinglimition.entity.CheckBookingLimitionRequestType;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.SourceFromUtil;
import com.ctrip.corp.bff.framework.template.common.utils.HostUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.hotel.book.service.BookingCheckRequestType;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/13 18:20
 */
@Component public class MapperOfCheckBookingLimitionRequestType
    extends AbstractMapper<Tuple1<BookingCheckRequestType>, CheckBookingLimitionRequestType> {

    @Override protected CheckBookingLimitionRequestType convert(Tuple1<BookingCheckRequestType> tuple) {
        CheckBookingLimitionRequestType checkBookingLimitionRequestType = new CheckBookingLimitionRequestType();
        BookingCheckRequestType bookingCheckRequestType = tuple.getT1();
        checkBookingLimitionRequestType.setBookingInfo(buildBookingInfoType(bookingCheckRequestType));
        checkBookingLimitionRequestType.setBaseInfo(buildBaseInfoType(bookingCheckRequestType));
        return checkBookingLimitionRequestType;
    }

    @Override protected ParamCheckResult check(Tuple1<BookingCheckRequestType> tuple) {
        return null;
    }

    protected BaseInfoType buildBaseInfoType(BookingCheckRequestType bookingCheckRequestType) {
        BaseInfoType baseInfoType = new BaseInfoType();
        baseInfoType.setUid(bookingCheckRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId());
        baseInfoType.setPolicyUid(
            Optional.ofNullable(bookingCheckRequestType.getPolicyInfo()).map(PolicyInput::getPolicyUid).orElse(null));
        baseInfoType.setCorpId(bookingCheckRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId());
        baseInfoType.setPOS(
            HostUtil.mapToAccountPos(bookingCheckRequestType.getIntegrationSoaRequestType().getUserInfo().getPos()));
        baseInfoType.setBookingChannel(SourceFromUtil.getBffSourceFromCode(
            bookingCheckRequestType.getIntegrationSoaRequestType().getSourceFrom()));
        baseInfoType.setSubChannel(
            RequestHeaderUtil.getSubChannel(bookingCheckRequestType.getIntegrationSoaRequestType()));
        baseInfoType.setCorpPayType(CorpPayInfoUtil.isPublic(bookingCheckRequestType.getCorpPayInfo()) ? "C" : "P");
        baseInfoType.setLocale(bookingCheckRequestType.getIntegrationSoaRequestType().getLanguage());
        baseInfoType.setTraceId(bookingCheckRequestType.getIntegrationSoaRequestType().getRequestId());
        return baseInfoType;
    }

    protected BookingInfoType buildBookingInfoType(BookingCheckRequestType bookingCheckRequestType) {
        BookingInfoType bookingInfoType = new BookingInfoType();
        if (bookingCheckRequestType.getKeywordAutoComplete() != null) {
            bookingInfoType.setHotelIdList(
                buildHotelIdList(bookingCheckRequestType.getKeywordAutoComplete().getSourceId()));
        }
        bookingInfoType.setCityId(bookingCheckRequestType.getCityInput().getCityId());
        bookingInfoType.setCheckInDate(
            bookingCheckRequestType.getHotelBookInput().getHotelDateRangeInfo().getCheckIn());
        bookingInfoType.setCheckOutDate(
            bookingCheckRequestType.getHotelBookInput().getHotelDateRangeInfo().getCheckOut());
        return bookingInfoType;
    }

    protected List<Integer> buildHotelIdList(String sourceId) {
        if (StringUtil.isBlank(sourceId)) {
            return null;
        }
        if (sourceId.startsWith("HOTEL.HOTEL.")) {
            List<String> hotelIds =
                Arrays.stream(sourceId.split("\\.")).filter(StringUtil::isNumber).collect(Collectors.toList());
            return hotelIds.stream().map(TemplateNumberUtil::parseInt).collect(Collectors.toList());
        }
        return null;
    }
}
