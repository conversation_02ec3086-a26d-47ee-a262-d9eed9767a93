package com.ctrip.corp.bff.hotel.book.common.wrapper;

import com.ctrip.corp.agg.hotel.roomavailable.entity.*;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.RoomDailyPriceInfoType;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum;
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.common.utils.date.DateUtil;
import com.ctrip.corp.bff.framework.template.entity.ActionInfo;
import com.ctrip.corp.bff.framework.template.entity.MapString;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.SoaErrorSharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.BookingInitErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelBalanceTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.PaymentGuaranteePolyEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.RoomTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.MathUtils;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.qconfig.QConfigOfCodeMappingConfig;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.CodeMappingConfig;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description 可定检查查询
 * @Date 2024/8/14 10:01
 * @Version 1.0
 */
public class WrapperOfCheckAvail {
    private String wsId;

    private HotelItem hotelItem;

    private HotelInterfaceItem hotelInterfaceInfo;

    private RoomItem roomItem;

    private BookingRulesType bookingRulesType;

    private List<RoomDailyInfo> roomDailyInfos;
    private String reservationToken;

    private BigDecimal couponAmount;
    // 税后 优惠后 总房费（含税)
    private BigDecimal roomAmount;
    // 税前 优惠后 总房费（不含税)
    private BigDecimal roomAmountExcludeTax;
    private List<RoomDailyPriceInfoType> roomDailyPriceInfoTypeList;
    private BigDecimal extraPayTaxAmount;
    private boolean amadues;
    private boolean forceVccPay;
    private String couponAmountCurrency;
    private boolean premiumRoom;
    private boolean tmcPrice;
    private int guestPerson;
    private int minBookingRoomNum;
    private int maxBookingRoomNum;
    private static final String GDSTYPE_AMADUES = "Amadeus";
    private static final String TMC_PRICE_M = "M";
    private static final String TMC_PRICE_C = "C";
    private static final int DEFAULT_GUEST_PERSON = 1;
    private static final int DEFAULT_MIN_ROOM = 0;
    private static final int DEFAULT_MAX_ROOM = 0;
    private boolean isWelfareRoom;
    private String originCurrency;
    private String customCurrency;
    private PaymentGuaranteePolyEnum paymentGuaranteePolyEnum;
    private HotelBalanceTypeEnum hotelBalanceTypeEnum;
    private List<AcquirerInfoType> acquirerInfoList;

    private String roomType;
    private MultipleLanguageText hotelName;
    private MultipleLanguageText roomName;

    // 最晚取消时间:yyyy-MM-dd HH:mm:ss
    private String localLastCancelTime;

    /**
     * 套餐id
     */
    private Integer packageId;

    /**
     * 套餐agg token
     */
    private String aggPackageToken;

    /**
     * 预订集团会员互通资源时需要注册集团会员的方式,BUSINESS_TRAVEL_REGISTER（商旅uid注册），TRIP_REGISTER（携程uid注册）
     */
    private String groupRegisterRule;

    // 客人姓名语种
    private List<String> guestsNameLanguages;

    // 是否会员互通房型
    private boolean groupMemberShip;

    // 酒店所属集团id
    private Integer groupId;

    private boolean needRegister;

    private boolean onlyGroupMemberCanBook;

    private String pointsMode;

    private boolean bonusPointRoom;

    private RoomTypeEnum roomTypeEnum;

    private boolean onlyEmployeeBooking;
    private boolean canTravelMoneyPay;

    private BigDecimal guaranteeAmount;

    private BigDecimal customExchange;
    private List<String> supportCertificateType;

    private boolean needGuarantee;
    private int star;
    private static final String CODE_MAPPING_CONFIG = "corphotelroomavailableserviceclient.checkavail";
    private static final String CHECK_AVAIL_RESPONSE_CODE = "checkAvailResponseCode";

    private static BigDecimal getExtraPayTaxAmount(List<TaxDetailType> taxDetailTypes) {
        if (CollectionUtil.isEmpty(taxDetailTypes)) {
            return null;
        }
        return taxDetailTypes.stream().filter(Objects::nonNull)
                .filter(tax -> !BooleanUtil.isTrue(tax.isIncludeInTotalPrice())).map(TaxDetailType::getCustomAmount)
                .reduce(BigDecimal::add).filter(MathUtils::isGreaterThanZero).orElse(null);
    }

    private static List<RoomDailyPriceInfoType> getRoomDailyPriceInfoTypeList(List<RoomDailyInfo> roomDailyInfos) {
        if (CollectionUtil.isEmpty(roomDailyInfos)) {
            return null;
        }
        List<RoomDailyPriceInfoType> roomDailyPriceInfoTypeList = new ArrayList<>();
        roomDailyInfos.stream().filter(Objects::nonNull).forEach(roomDailyInfo -> {
            Calendar effectDate = null;
            if (!StringUtil.isEmpty(roomDailyInfo.getLocalEffectDate())) {
                try {
                    effectDate = DateUtil.fromStringToCalendar(roomDailyInfo.getLocalEffectDate(), DateUtil.YYYY_MM_DD);
                } catch (Exception e) {
                    LogUtil.loggingClogOnly(LogLevelEnum.Error, WrapperOfCheckAvail.class,
                            "getRoomDailyPriceInfoTypeList.error", e, null);
                }
            }
            if (effectDate == null) {
                return;
            }
            BigDecimal singleDailyPrice = getSingleDailyPrice(roomDailyInfo);
            if (singleDailyPrice == null) {
                return;
            }
            RoomDailyPriceInfoType roomDailyPriceInfoType = new RoomDailyPriceInfoType();
            roomDailyPriceInfoType.setEffectDate(DateUtil.fromCalendarToString(effectDate, DateUtil.YYYY_MM_DD));
            roomDailyPriceInfoType.setRoomAmount(singleDailyPrice);
            roomDailyPriceInfoType.setRoomAmountExcludeTax(getSingleDailyPriceExcludeTax(roomDailyInfo));
            roomDailyPriceInfoTypeList.add(roomDailyPriceInfoType);
        });
        return roomDailyPriceInfoTypeList;
    }

    private static BigDecimal getSingleDailyPriceExcludeTax(RoomDailyInfo roomDailyInfo) {
        if (Optional.ofNullable(roomDailyInfo).map(RoomDailyInfo::getCustomAmountExcludeTax).orElse(null) != null) {
            BigDecimal saleCustomAmount = Optional.ofNullable(roomDailyInfo.getSalePromotionInfo())
                    .map(RommDailySalePromotionEntity::getPromotCustomAmount).orElse(new BigDecimal(0));
            return roomDailyInfo.getCustomAmountExcludeTax().subtract(saleCustomAmount);
        }
        if (Optional.ofNullable(roomDailyInfo).map(RoomDailyInfo::getCNYAmountExcludeTax).orElse(null) != null) {
            BigDecimal saleCnyAmount = Optional.ofNullable(roomDailyInfo.getSalePromotionInfo())
                    .map(RommDailySalePromotionEntity::getPromotCNYAmount).orElse(new BigDecimal(0));
            return roomDailyInfo.getCNYAmountExcludeTax().subtract(saleCnyAmount);
        }
        return null;
    }

    private static BigDecimal getSingleDailyPrice(RoomDailyInfo roomDailyInfo) {
        if (Optional.ofNullable(roomDailyInfo).map(RoomDailyInfo::getSalePromotionInfo)
                .map(RommDailySalePromotionEntity::getAfterPromotCustomAmount).orElse(null) != null) {
            return roomDailyInfo.getSalePromotionInfo().getAfterPromotCustomAmount();
        }
        if (Optional.ofNullable(roomDailyInfo).map(RoomDailyInfo::getSalePromotionInfo)
                .map(RommDailySalePromotionEntity::getAfterPromotCNYAmount).orElse(null) != null) {
            return roomDailyInfo.getSalePromotionInfo().getAfterPromotCNYAmount();
        }
        if (Optional.ofNullable(roomDailyInfo).map(RoomDailyInfo::getCustomAmount).orElse(null) != null) {
            return roomDailyInfo.getCustomAmount();
        }
        if (Optional.ofNullable(roomDailyInfo).map(RoomDailyInfo::getCNYAmount).orElse(null) != null) {
            return roomDailyInfo.getCNYAmount();
        }
        return null;
    }

    public static WrapperOfCheckAvail.CheckAvailBuilder checkAvailBuilder() {
        return new WrapperOfCheckAvail.CheckAvailBuilder();
    }

    public static WrapperOfCheckAvail.CheckAvailContextBuilder checkAvailContextBuilder() {
        return new WrapperOfCheckAvail.CheckAvailContextBuilder();
    }

    public CheckAvailInfo getCheckAvailInfo() {
        return this.new CheckAvailInfo();
    }

    public CheckAvailContextInfo getCheckAvailContextInfo() {
        return this.new CheckAvailContextInfo();
    }

    public static class CheckAvailContextBuilder {
        ResourceToken resourceToken;

        QueryCheckAvailContextResponseType checkAvailContextResponseType;

        public CheckAvailContextBuilder setQueryCheckAvailContextResponseType(QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
            this.checkAvailContextResponseType = queryCheckAvailContextResponseType;
            return this;
        }


        public CheckAvailContextBuilder setResourceToken(ResourceToken resourceToken) {
            this.resourceToken = resourceToken;
            return this;
        }

        public CheckAvailContextBuilder check(QConfigOfCodeMappingConfig qConfigOfCodeMappingConfig) {
            if (checkAvailContextResponseType != null && checkAvailContextResponseType.getResponseCode() != null
                    && Objects.equals(checkAvailContextResponseType.getResponseCode(), 20000)) {
                return this;
            }
            String responseCode = checkAvailContextResponseType.getResponseCode().toString();
            String responseDesc = checkAvailContextResponseType.getResponseDesc();
            if (qConfigOfCodeMappingConfig == null) {
                throw BusinessExceptionBuilder.createAlertException(600, responseDesc, responseDesc, responseCode);
            }
            CodeMappingConfig codeMappingConfig = qConfigOfCodeMappingConfig.getCodeMappingConfig("");
            if (codeMappingConfig == null) {
                throw BusinessExceptionBuilder.createAlertException(600, responseDesc, responseDesc, responseCode);
            }
            throw BusinessExceptionBuilder.createAlertException(codeMappingConfig.getMappingCode(responseCode), responseDesc, responseDesc, responseCode);
        }


        public WrapperOfCheckAvail build() {
            WrapperOfCheckAvail wrapperOfCheckAvail = new WrapperOfCheckAvail();
            wrapperOfCheckAvail.wsId = resourceToken.getReservationResourceToken().getWsId();
            wrapperOfCheckAvail.couponAmount = Optional.ofNullable(checkAvailContextResponseType.getUserRightsInfo())
                .map(UserRightsInfoType::getMultiCouponTotalCustomAmount).orElse(BigDecimal.ZERO);
            BigDecimal customPromotionAmount = Optional.ofNullable(checkAvailContextResponseType.getRoomInfo())
                    .map(BookRoomInfoEntity::getRoomDiscountInfo).map(RoomDiscountInfo::getCustomPromotionAmount)
                    .map(PriceType::getPrice).orElse(BigDecimal.ZERO);
            wrapperOfCheckAvail.roomAmount = Optional.ofNullable(checkAvailContextResponseType.getRoomInfo())
                    .map(BookRoomInfoEntity::getCustomAmountInfo).map(AmountDetailEntity::getAmount).orElse(BigDecimal.ZERO)
                    .subtract(customPromotionAmount);
            wrapperOfCheckAvail.roomAmountExcludeTax = Optional.ofNullable(checkAvailContextResponseType.getRoomInfo())
                .map(BookRoomInfoEntity::getCustomAmountExcludeTaxInfo).map(AmountDetailEntity::getAmount)
                .orElse(BigDecimal.ZERO).subtract(Optional.ofNullable(checkAvailContextResponseType.getRoomInfo())
                    .map(BookRoomInfoEntity::getRoomDiscountInfo).map(RoomDiscountInfo::getCustomPromotionAmount)
                    .map(PriceType::getPrice).orElse(BigDecimal.ZERO));
            wrapperOfCheckAvail.amadues = StringUtil.equalsIgnoreCase(
                    Optional.ofNullable(checkAvailContextResponseType.getRoomInfo()).map(BookRoomInfoEntity::getGdsType)
                            .orElse(null), GDSTYPE_AMADUES);
            wrapperOfCheckAvail.forceVccPay = BooleanUtil.isTrue(
                    Optional.ofNullable(checkAvailContextResponseType.getRoomInfo().getPaymentRulesInfo())
                            .map(PaymentRules::isForceVccPay).orElse(false));
            wrapperOfCheckAvail.couponAmountCurrency = CommonConstant.CNY;
            wrapperOfCheckAvail.tmcPrice =
                TMC_PRICE_M.equalsIgnoreCase(checkAvailContextResponseType.getRoomInfo().getTmcPriceType())
                    || TMC_PRICE_C.equalsIgnoreCase(checkAvailContextResponseType.getRoomInfo().getTmcPriceType());
            wrapperOfCheckAvail.paymentGuaranteePolyEnum = PaymentGuaranteePolyEnum.getPaymentGuaranteePolyEnum(
                Optional.ofNullable(checkAvailContextResponseType.getBookingRules())
                    .map(QueryBookingRulesType::getCancelPolicyInfo).map(QueryCancelPolicyType::getGuaranteePolicyInfo)
                    .map(QueryGuaranteeDetailType::getPaymentGuaranteePoly).orElse(null));
            wrapperOfCheckAvail.hotelBalanceTypeEnum = HotelBalanceTypeEnum.getHotelBalanceTypeEnum(
                Optional.ofNullable(checkAvailContextResponseType.getRoomInfo().getBalanceType()).orElse(null));
            wrapperOfCheckAvail.acquirerInfoList = Optional.ofNullable(checkAvailContextResponseType.getBookingRules())
                .map(QueryBookingRulesType::getAcquirerInfoList).orElse(null);
            wrapperOfCheckAvail.roomType =
                TMC_PRICE_M.equalsIgnoreCase(checkAvailContextResponseType.getRoomInfo().getTmcPriceType())
                    && RoomTypeEnum.C.name()
                    .equalsIgnoreCase(checkAvailContextResponseType.getRoomInfo().getRoomType()) ?
                    RoomTypeEnum.M.name() : checkAvailContextResponseType.getRoomInfo().getRoomType();
            wrapperOfCheckAvail.hotelName =
                Optional.ofNullable(checkAvailContextResponseType.getHotelInfo()).map(BookHotelInfoEntity::getHotelName)
                    .orElse(null);
            wrapperOfCheckAvail.roomName =
                Optional.ofNullable(checkAvailContextResponseType.getRoomInfo()).map(BookRoomInfoEntity::getRoomName)
                    .orElse(null);
            wrapperOfCheckAvail.groupRegisterRule = Optional.ofNullable(checkAvailContextResponseType.getBookingRules())
                .map(QueryBookingRulesType::getGroupMemberShipInfo).map(QueryGroupMemberShipType::getGroupRegisterRule)
                .orElse(null);
            wrapperOfCheckAvail.extraPayTaxAmount = getExtraPayTaxAmount(checkAvailContextResponseType.getRoomInfo().getTaxDetails());
            wrapperOfCheckAvail.customCurrency =
                Optional.ofNullable(checkAvailContextResponseType.getRoomInfo().getCustomAmountInfo())
                    .map(AmountDetailEntity::getCurrency).orElse(null);
            wrapperOfCheckAvail.originCurrency =
                Optional.ofNullable(checkAvailContextResponseType.getRoomInfo().getOriginAmountInfo())
                    .map(AmountDetailEntity::getCurrency).orElse(null);
            wrapperOfCheckAvail.localLastCancelTime = Optional.ofNullable(checkAvailContextResponseType.getBookingRules())
                .map(QueryBookingRulesType::getCancelPolicyInfo).map(QueryCancelPolicyType::getLocalLastCancelTime).orElse(null);
            wrapperOfCheckAvail.packageId = Optional.ofNullable(checkAvailContextResponseType.getRoomInfo())
                .map(BookRoomInfoEntity::getPackageRoomInfo)
                .map(PackageRoomInfoType::getPackageId)
                .orElse(null);            
            wrapperOfCheckAvail.guestsNameLanguages =
                Optional.ofNullable(checkAvailContextResponseType.getBookingRules())
                    .map(QueryBookingRulesType::getBillingGuestInfo)
                    .map(QueryBillingGuestInfoType::getGuestsNameLanguages).orElse(null);
            wrapperOfCheckAvail.groupMemberShip =
                Optional.ofNullable(checkAvailContextResponseType.getRoomInfo().isGroupMemberShip()).orElse(false);
            wrapperOfCheckAvail.groupId = Optional.ofNullable(checkAvailContextResponseType.getHotelInfo())
                .map(BookHotelInfoEntity::getHotelGroupId).orElse(null);
            wrapperOfCheckAvail.needRegister = Optional.ofNullable(checkAvailContextResponseType.getBookingRules())
                .map(QueryBookingRulesType::getGroupMemberShipInfo).map(QueryGroupMemberShipType::isNeedRegister)
                .orElse(false);
            wrapperOfCheckAvail.onlyGroupMemberCanBook = Optional.ofNullable(checkAvailContextResponseType.getRoomInfo())
                .map(BookRoomInfoEntity::isOnlyGroupMemberCanBook).orElse(false);
            wrapperOfCheckAvail.pointsMode = Optional.ofNullable(checkAvailContextResponseType.getRoomInfo())
                .map(BookRoomInfoEntity::getBonusPointInfo).map(QueryBonusPointInfoType::getPointsMode).orElse(null);
            wrapperOfCheckAvail.bonusPointRoom = Optional.ofNullable(checkAvailContextResponseType.getRoomInfo())
                .map(BookRoomInfoEntity::getBonusPointInfo).map(QueryBonusPointInfoType::isBonusPointRoom).orElse(false);
            wrapperOfCheckAvail.roomTypeEnum = RoomTypeEnum.fromValue(
                TMC_PRICE_M.equalsIgnoreCase(checkAvailContextResponseType.getRoomInfo().getTmcPriceType())
                    && RoomTypeEnum.C.name()
                    .equalsIgnoreCase(checkAvailContextResponseType.getRoomInfo().getRoomType()) ?
                    RoomTypeEnum.M.name() : checkAvailContextResponseType.getRoomInfo().getRoomType());
            wrapperOfCheckAvail.guestPerson = Optional.ofNullable(checkAvailContextResponseType.getRoomInfo())
                .map(BookRoomInfoEntity::getMaxGuestCountPerRoom).orElse(DEFAULT_GUEST_PERSON);
            wrapperOfCheckAvail.onlyEmployeeBooking = buildOnlyEmployeeBooking(checkAvailContextResponseType);
            wrapperOfCheckAvail.aggPackageToken = Optional.ofNullable(checkAvailContextResponseType.getRoomInfo())
                    .map(BookRoomInfoEntity::getPackageRoomInfo).map(PackageRoomInfoType::getPackageRoomToken).orElse(null);
            wrapperOfCheckAvail.canTravelMoneyPay = BooleanUtil.isTrue(
                Optional.ofNullable(checkAvailContextResponseType.getRoomInfo())
                    .map(BookRoomInfoEntity::getPaymentRulesInfo).map(PaymentRules::isCanTravelMoneyPay).orElse(false));
            wrapperOfCheckAvail.customExchange = Optional.ofNullable(checkAvailContextResponseType.getRoomInfo())
                    .map(BookRoomInfoEntity::getCustomAmountInfo).map(AmountDetailEntity::getExchange).orElse(null);
            wrapperOfCheckAvail.guaranteeAmount = Optional.ofNullable(checkAvailContextResponseType.getBookingRules())
                .map(QueryBookingRulesType::getCancelPolicyInfo).map(QueryCancelPolicyType::getGuaranteePolicyInfo)
                .map(QueryGuaranteeDetailType::getGuaranteePriceInfo)
                .map(QueryGuaranteePriceType::getOriginGuaranteePrice).map(PriceType::getPrice).orElse(null);
            wrapperOfCheckAvail.supportCertificateType =
                Optional.ofNullable(checkAvailContextResponseType.getBookingRules())
                    .map(QueryBookingRulesType::getCertificateInfo)
                    .map(QueryCertificateType::getSupportCertificateType).orElse(null);
            wrapperOfCheckAvail.needGuarantee = BooleanUtil.isTrue(Optional.ofNullable(checkAvailContextResponseType.getBookingRules())
                    .map(QueryBookingRulesType::getCancelPolicyInfo)
                .map(QueryCancelPolicyType::isNeedGuarantee).orElse(false));
            wrapperOfCheckAvail.star = Optional.ofNullable(checkAvailContextResponseType.getHotelInfo())
                .map(BookHotelInfoEntity::getStar).orElse(0);
            return wrapperOfCheckAvail;
        }

        private static boolean buildOnlyEmployeeBooking(
            QueryCheckAvailContextResponseType checkAvailContextResponseType) {
            List<String> restrictRuleList = Optional.ofNullable(checkAvailContextResponseType)
                .map(QueryCheckAvailContextResponseType::getBookingRules).orElse(new QueryBookingRulesType())
                .getRestrictRuleList();
            if (CollectionUtil.isEmpty(restrictRuleList)) {
                return false;
            }
            return restrictRuleList.contains("EMPLOYEE_BOOKING");
        }
    }

    public static class CheckAvailBuilder {
        ResourceToken resourceToken;
        CheckAvailResponseType checkAvailResponseType;


        public CheckAvailBuilder setCheckAvailResponseType(CheckAvailResponseType checkAvailResponseType) {
            this.checkAvailResponseType = checkAvailResponseType;
            return this;
        }

        public CheckAvailBuilder setResourceToken(ResourceToken resourceToken) {
            this.resourceToken = resourceToken;
            return this;
        }

        private static final String BAGASHI_CHECK_FAIL_MESSAGE_KEY = "hotel.booking.err.alter.hotelcheckavail.bagashi_resource_error";

        public CheckAvailBuilder check(QConfigOfCodeMappingConfig qConfigOfCodeMappingConfig, List<StrategyInfo> strategyInfos) {
            if (checkAvailResponseType != null && checkAvailResponseType.getResponseCode() != null
                    && checkAvailResponseType.getResponseCode() == 20000) {
                return this;
            }
            if (StrategyOfBookingInitUtil.bagashiLocaleCheckAvailFailInfo(strategyInfos)) {
                throw BusinessExceptionBuilder.createAlertException(BookingInitErrorEnum.CHECK_AVAIL_ERROR.getErrorCode(),
                        BAGASHI_CHECK_FAIL_MESSAGE_KEY, BFFSharkUtil.getSharkValue(BAGASHI_CHECK_FAIL_MESSAGE_KEY),
                        BookingInitErrorEnum.CHECK_AVAIL_ERROR.getErrorCode().toString());
            }
            Integer logErrorCode =
                Optional.ofNullable(checkAvailResponseType).map(CheckAvailResponseType::getResponseCode)
                    .orElse(BookingInitErrorEnum.CHECK_AVAIL_ERROR.getErrorCode());
            String friendlyMessage = SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessage(
                SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_HOTEL_AGG_ROOM_AVAIL_SERVICE,
                SoaErrorSharkKeyConstant.ACTION_NAME_CHECK_AVAIL, checkAvailResponseType.getResponseCode().toString());
            if (StringUtil.isBlank(friendlyMessage)) {
                friendlyMessage = SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessageDefault(
                    SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_HOTEL_AGG_ROOM_AVAIL_SERVICE,
                    SoaErrorSharkKeyConstant.ACTION_NAME_CHECK_AVAIL);
            }
            // 有特殊定制时 使用qConfigOfCodeMappingConfig映射为699-》698-...
            Integer errorCode = BookingInitErrorEnum.CHECK_AVAIL_ERROR.getErrorCode();
            if (qConfigOfCodeMappingConfig != null
                && Optional.ofNullable(checkAvailResponseType).map(CheckAvailResponseType::getResponseCode).orElse(0)
                != 0) {
                CodeMappingConfig codeMappingConfig =
                    qConfigOfCodeMappingConfig.getCodeMappingConfig("corphotelroomavailableserviceclient.checkavail");
                if (codeMappingConfig != null) {
                    Integer errorCodeMappingCode =
                        codeMappingConfig.getMappingCode(checkAvailResponseType.getResponseCode().toString());
                    if (TemplateNumberUtil.isNotZeroAndNull(errorCodeMappingCode)) {
                        errorCode = errorCodeMappingCode;
                    }
                }
            }
            throw BusinessExceptionBuilder.createAlertException(errorCode,
                Optional.ofNullable(checkAvailResponseType).map(CheckAvailResponseType::getResponseDesc).orElse(null),
                friendlyMessage, logErrorCode.toString(), buildActionInfo(logErrorCode, qConfigOfCodeMappingConfig));
        }

        public ActionInfo buildActionInfo(Integer aggResponseCode,
            QConfigOfCodeMappingConfig qConfigOfCodeMappingConfig) {
            if (aggResponseCode == null) {
                return null;
            }
            if (aggResponseCode.equals(CommonConstant.SUCCESS_20000)) {
                return null;
            }
            if (qConfigOfCodeMappingConfig == null) {
                return null;
            }
            CodeMappingConfig codeMappingConfig = qConfigOfCodeMappingConfig.getCodeMappingConfig(CODE_MAPPING_CONFIG);
            if (codeMappingConfig == null) {
                return null;
            }
            String errorCodeMappingActionType = codeMappingConfig.getMappingActionType(aggResponseCode.toString());
            if (StringUtil.isEmpty(errorCodeMappingActionType)) {
                return null;
            }
            ActionInfo actionInfo = new ActionInfo();
            actionInfo.setActionType(errorCodeMappingActionType);
            List<MapString> dataExtInfoList = new ArrayList<>();
            MapString dataExtInfo = new MapString();
            dataExtInfo.setKey(CHECK_AVAIL_RESPONSE_CODE);
            dataExtInfo.setValue(aggResponseCode.toString());
            dataExtInfoList.add(dataExtInfo);
            actionInfo.setDataExtInfo(dataExtInfoList);
            return actionInfo;
        }

        public WrapperOfCheckAvail build() {
            WrapperOfCheckAvail wrapperOfCheckAvail = new WrapperOfCheckAvail();
            wrapperOfCheckAvail.wsId = checkAvailResponseType.getWsId();
            wrapperOfCheckAvail.hotelItem = Optional.ofNullable(checkAvailResponseType.getHotelRatePlan()).map(HotelRatePlan::getHotelInfo).orElse(null);
            wrapperOfCheckAvail.hotelInterfaceInfo = Optional.ofNullable(checkAvailResponseType.getHotelRatePlan()).map(HotelRatePlan::getHotelInterfaceInfo).orElse(null);
            wrapperOfCheckAvail.roomItem = Optional.ofNullable(checkAvailResponseType.getHotelRatePlan()).map(HotelRatePlan::getRoomInfo).orElse(null);
            wrapperOfCheckAvail.bookingRulesType = Optional.ofNullable(checkAvailResponseType.getHotelRatePlan()).map(HotelRatePlan::getBookingRules).orElse(null);
            wrapperOfCheckAvail.roomDailyInfos = Optional.ofNullable(checkAvailResponseType.getHotelRatePlan()).map(HotelRatePlan::getRoomDailyInfoList).orElse(null);
            wrapperOfCheckAvail.reservationToken = checkAvailResponseType.getReservationToken();
            wrapperOfCheckAvail.couponAmount =
                    Optional.ofNullable(checkAvailResponseType.getHotelRatePlan()).map(HotelRatePlan::getRoomInfo)
                        .map(RoomItem::getRoomCouponInfo)
                            .map(RoomCouponInfoType::getMultiCouponTotalCustomAmount).orElse(BigDecimal.ZERO);
            SalePromotionEntity salePromotionEntity =
                    Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getRoomInfo().getSalePromotionInfo())
                            .orElse(null);
            BigDecimal afterSalePromoCustomAmount;
            if (salePromotionEntity != null) {
                afterSalePromoCustomAmount = salePromotionEntity.getAfterPromotCustomAmount();
            } else {
                afterSalePromoCustomAmount = checkAvailResponseType.getHotelRatePlan().getRoomInfo().getCustomAmount();
            }
            wrapperOfCheckAvail.roomAmount = afterSalePromoCustomAmount;
            wrapperOfCheckAvail.roomAmountExcludeTax =
                Optional.ofNullable(checkAvailResponseType.getHotelRatePlan()).map(HotelRatePlan::getRoomInfo)
                    .map(RoomItem::getPriceItemInfo).map(PriceItemInfo::getCustomRoomAmount).orElse(BigDecimal.ZERO)
                    .subtract(Optional.ofNullable(salePromotionEntity).map(SalePromotionEntity::getPromotCustomAmount)
                        .orElse(BigDecimal.ZERO));
            wrapperOfCheckAvail.roomDailyPriceInfoTypeList =
                    getRoomDailyPriceInfoTypeList(checkAvailResponseType.getHotelRatePlan().getRoomDailyInfoList());
            wrapperOfCheckAvail.extraPayTaxAmount =
                    getExtraPayTaxAmount(checkAvailResponseType.getHotelRatePlan().getRoomInfo().getTaxDetails());
            wrapperOfCheckAvail.amadues = StringUtil.equalsIgnoreCase(
                    Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getRoomInfo()).map(RoomItem::getGDS)
                            .orElse(null), GDSTYPE_AMADUES);
            wrapperOfCheckAvail.forceVccPay = BooleanUtil.isTrue(
                    Optional.ofNullable(checkAvailResponseType.getHotelRatePlan()).map(HotelRatePlan::getRoomInfo)
                            .map(RoomItem::getRoomPaymentRules).map(RoomPaymentRulesEntity::isForceVccPay).orElse(false));
            wrapperOfCheckAvail.couponAmountCurrency = CommonConstant.CNY;
            wrapperOfCheckAvail.premiumRoom = BooleanUtil.isTrue(
                    Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getRoomInfo())
                            .map(RoomItem::getRoomAttributes).map(RoomAttributesType::isPremiumRoom).orElse(false));
            wrapperOfCheckAvail.tmcPrice = BooleanUtil.isTrue(resourceToken.getRoomResourceToken().getTmcPrice());
            wrapperOfCheckAvail.guestPerson =
                    Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getRoomInfo()).map(RoomItem::getGuestPerson)
                            .orElse(DEFAULT_GUEST_PERSON);
            wrapperOfCheckAvail.minBookingRoomNum =
                    Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getRoomInfo())
                            .map(RoomItem::getMinBookingRoomNum).orElse(DEFAULT_MIN_ROOM);
            wrapperOfCheckAvail.maxBookingRoomNum =
                    Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getRoomInfo())
                            .map(RoomItem::getMaxBookingRoomNum).orElse(DEFAULT_MAX_ROOM);
            wrapperOfCheckAvail.isWelfareRoom =
                Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getRoomInfo())
                    .map(RoomItem::getRoomAttributes).map(RoomAttributesType::isWelfareRoom).orElse(false);
            wrapperOfCheckAvail.originCurrency = checkAvailResponseType.getHotelRatePlan().getRoomInfo().getOriginCurrency();
            wrapperOfCheckAvail.paymentGuaranteePolyEnum = PaymentGuaranteePolyEnum.getPaymentGuaranteePolyEnum(
                Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getBookingRules())
                    .map(BookingRulesType::getCancelPolicyInfo).map(CancelPolicyType::getGuaranteePolicyInfo)
                    .map(GuaranteeDetailType::getPaymentGuaranteePoly).orElse(null));
            wrapperOfCheckAvail.hotelBalanceTypeEnum = HotelBalanceTypeEnum.getHotelBalanceTypeEnum(
                Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getRoomInfo().getBalanceType())
                    .orElse(null));
            wrapperOfCheckAvail.acquirerInfoList =
                Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getBookingRules())
                    .map(BookingRulesType::getAcquirerInfoList).orElse(null);
            wrapperOfCheckAvail.roomType = resourceToken.getRoomResourceToken().getRoomType();
            wrapperOfCheckAvail.hotelName =
                Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getHotelInfo())
                    .map(HotelItem::getHotelName).orElse(null);
            wrapperOfCheckAvail.roomName = Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getRoomInfo())
                .map(RoomItem::getRoomName).orElse(null);
            wrapperOfCheckAvail.groupRegisterRule =
                Optional.ofNullable(checkAvailResponseType.getHotelRatePlan()).map(HotelRatePlan::getBookingRules)
                    .map(BookingRulesType::getGroupMemberShipInfo).map(GroupMemberShipType::getGroupRegisterRule)
                    .orElse(null);
            wrapperOfCheckAvail.customCurrency =
                Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getRoomInfo())
                    .map(RoomItem::getCustomCurrency).orElse(null);
            wrapperOfCheckAvail.localLastCancelTime =
                Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getBookingRules())
                    .map(BookingRulesType::getCancelPolicyInfo).map(CancelPolicyType::getLocalLastCancelTime)
                    .orElse(null);
            wrapperOfCheckAvail.guestsNameLanguages =
                Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getBookingRules())
                    .map(BookingRulesType::getBillingGuestInfo).map(BillingGuestInfoType::getGuestsNameLanguages)
                    .orElse(null);
            wrapperOfCheckAvail.groupMemberShip =
                Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getRoomInfo())
                    .map(RoomItem::getRoomAttributes).map(RoomAttributesType::isGroupMemberShip).orElse(false);
            wrapperOfCheckAvail.groupId = Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getHotelInfo())
                .map(HotelItem::getHotelBrandInfo).map(HotelBrandItem::getGroupId).orElse(null);
            wrapperOfCheckAvail.needRegister =
                Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getBookingRules())
                    .map(BookingRulesType::getGroupMemberShipInfo).map(GroupMemberShipType::isNeedRegister)
                    .orElse(false);
            wrapperOfCheckAvail.onlyGroupMemberCanBook =
                Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getRoomInfo())
                    .map(RoomItem::getRoomAttributes).map(RoomAttributesType::isOnlyGroupMemberCanBook).orElse(false);
            wrapperOfCheckAvail.pointsMode =
                Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getRoomInfo())
                    .map(RoomItem::getBonusPointInfo).map(BonusPointInfoType::getPointsMode).orElse(null);
            wrapperOfCheckAvail.bonusPointRoom = Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getRoomInfo())
                .map(RoomItem::getBonusPointInfo).map(BonusPointInfoType::isBonusPointRoom).orElse(false);
            wrapperOfCheckAvail.roomTypeEnum = RoomTypeEnum.fromValue(Optional.ofNullable(resourceToken.getRoomResourceToken())
                .map(RoomResourceToken::getRoomType).orElse(null));
            wrapperOfCheckAvail.aggPackageToken = Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getRoomInfo())
                    .map(RoomItem::getPackageRoomInfo).map(PackageRoomInfoType::getPackageRoomToken).orElse(null);
            wrapperOfCheckAvail.canTravelMoneyPay = BooleanUtil.isTrue(
                Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getRoomInfo())
                    .map(RoomItem::getRoomPaymentRules).map(RoomPaymentRulesEntity::isAcceptTravelMoneyPay)
                    .orElse(false));
            wrapperOfCheckAvail.supportCertificateType =
                Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getBookingRules())
                    .map(BookingRulesType::getCertificateInfo).map(CertificateInfoType::getSupportCertificateType)
                    .orElse(null);
            wrapperOfCheckAvail.needGuarantee = BooleanUtil.isTrue(Optional.ofNullable(checkAvailResponseType.getHotelRatePlan()).map(HotelRatePlan::getBookingRules)
                .map(BookingRulesType::getCancelPolicyInfo).map(CancelPolicyType::isNeedGuarantee).orElse(false));
            wrapperOfCheckAvail.star = Optional.ofNullable(checkAvailResponseType.getHotelRatePlan().getHotelInfo())
                .map(HotelItem::getStar).orElse(0);
            return wrapperOfCheckAvail;
        }


    }

    public class BaseCheckAvailInfo {

        public String getWsId() {
            return wsId;
        }

        public BigDecimal getCouponAmount() {
            return couponAmount;
        }

        public BigDecimal getRoomAmount() {
            return roomAmount;
        }

        public BigDecimal getRoomAmountExcludeTax() {
            return roomAmountExcludeTax;
        }

        public List<RoomDailyPriceInfoType> getRoomDailyPriceInfoTypeList() {
            return roomDailyPriceInfoTypeList;
        }

        public BigDecimal getExtraPayTaxAmount() {
            return extraPayTaxAmount;
        }


        public boolean isAmadues() {
            return amadues;
        }

        public boolean isForceVccPay() {
            return forceVccPay;
        }

        public String getCouponAmountCurrency() {
            return couponAmountCurrency;
        }

        public boolean isPremiumRoom() {
            return premiumRoom;
        }

        public boolean isTmcPrice() {
            return tmcPrice;
        }

        /**
         * 原则上：可定反查是在创单使用的，下单接口入参目前不需要此节点，所以这里不做处理，就算用也只能做个入参校验，客户所见即所得，不可用反查房间数节点做业务逻辑
         * @return
         */
        public int getGuestPerson() {
            return guestPerson;
        }

        /**
         * 原则上：可定反查是在创单使用的，不需要此节点，反查wrapper未赋值
         * @return
         */
        public int getMinBookingRoomNum() {
            return minBookingRoomNum;
        }

        /**
         * 原则上：可定反查是在创单使用的，不需要此节点，反查wrapper未赋值
         * @return
         */
        public int getMaxBookingRoomNum() {
            return maxBookingRoomNum;
        }


        public boolean isWelfareRoom() {
            return isWelfareRoom;
        }

        public String getOriginCurrency() {
            return originCurrency;
        }

        public String getCustomCurrency() {
            return customCurrency;
        }

        public String getLocalLastCancelTime() {
            return localLastCancelTime;
        }

        public Integer getPackageId() {
            return packageId;
        }


        public PaymentGuaranteePolyEnum getPaymentGuaranteePolyEnum() {
            if (hotelBalanceTypeEnum.isPP() || hotelBalanceTypeEnum.isUseFG()) {
                return PaymentGuaranteePolyEnum.NONE;
            }
            return paymentGuaranteePolyEnum;
        }

        public HotelBalanceTypeEnum getHotelBalanceTypeEnum() {
            return hotelBalanceTypeEnum;
        }

        public List<AcquirerInfoType> getAcquirerInfoList() {
            return acquirerInfoList;
        }

        public String getRoomType() {
            return roomType;
        }

        public MultipleLanguageText getHotelName() {
            return Optional.ofNullable(hotelName).orElse(new MultipleLanguageText());
        }

        public MultipleLanguageText getRoomName() {
            return Optional.ofNullable(roomName).orElse(new MultipleLanguageText());
        }

        public String getGroupRegisterRule() {
            return groupRegisterRule;
        }

        public List<String> getGuestsNameLanguages() {
            return guestsNameLanguages;
        }

        public boolean isGroupMemberShip() {
            return groupMemberShip;
        }

        public int getGroupId() {
            return TemplateNumberUtil.getValue(groupId);
        }

        public boolean isNeedRegister() {
            return needRegister;
        }

        public boolean isOnlyGroupMemberCanBook() {
            return onlyGroupMemberCanBook;
        }

        public String getPointsMode() {
            return pointsMode;
        }

        public boolean isBonusPointRoom() {
            return bonusPointRoom;
        }

        public RoomTypeEnum getRoomTypeEnum() {
            return roomTypeEnum;
        }

        public boolean isOnlyEmployeeBooking() {
            return onlyEmployeeBooking;
        }

        public String getAggPackageToken() {
            return aggPackageToken;
        }

        public boolean isCanTravelMoneyPay() {
            return canTravelMoneyPay;
        }

        public BigDecimal getGuaranteeAmount() {
            return guaranteeAmount;
        }

        public List<String> getSupportCertificateType() {
            return supportCertificateType;
        }

        public boolean isNeedGuarantee() {
            return needGuarantee;
        }

        public int getStar() {
            return star;
        }
    }

    public class CheckAvailInfo extends BaseCheckAvailInfo {


        public HotelItem getHotelItem() {
            return hotelItem;
        }

        public HotelInterfaceItem getHotelInterfaceInfo() {
            return hotelInterfaceInfo;
        }

        public RoomItem getRoomItem() {
            return roomItem;
        }

        public BookingRulesType getBookingRules() {
            return bookingRulesType;
        }

        public List<RoomDailyInfo> getRoomDailyInfos() {
            return roomDailyInfos;
        }

        public String getReservationToken() {
            return reservationToken;
        }


    }

    public class CheckAvailContextInfo extends BaseCheckAvailInfo {

        public BigDecimal getCustomExchange() {
            return customExchange;
        }

    }
}
