package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.TripInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.bff.hotel.book.contract.TripInfoInput;
import com.ctrip.corp.foundation.common.util.NumberUtil;
import com.ctrip.soa._21234.GetTripBookingInfosRequestType;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description 查行程绑定的出差申请单号
 * @Date 2024-10-28 17:41:43
 * @Version 1.0
 */
@Component
public class MapperOfGetTripBookingInfosRequest extends
        AbstractMapper<Tuple2<TripInput, ResourceToken>, GetTripBookingInfosRequestType> {

    @Override
    protected GetTripBookingInfosRequestType convert(Tuple2<TripInput, ResourceToken> tuple) {
        TripInput tripInfoInput = tuple.getT1();
        ResourceToken resourceToken = tuple.getT2();
        GetTripBookingInfosRequestType requestType = new GetTripBookingInfosRequestType();
        requestType.setChannelType(1);
        Long tripId = NumberUtil.parseLong(Optional.ofNullable(tripInfoInput)
            .map(TripInput::getTripId).orElse(null));
        if (tripId == null || tripId <= 0) {
            return null;
        }
        requestType.setTripIdList(Collections.singletonList(tripId));
        String roomType = Optional.ofNullable(resourceToken)
            .map(ResourceToken::getRoomResourceToken)
            .map(RoomResourceToken::getRoomType)
            .orElse(null);
        requestType.setBusinessType(roomType);
       return requestType;
    }

    @Override
    protected ParamCheckResult check(Tuple2<TripInput, ResourceToken> tuple) {
        return null;
    }

}
