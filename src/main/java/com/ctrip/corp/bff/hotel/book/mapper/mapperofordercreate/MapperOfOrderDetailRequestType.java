package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderDetailRequestType;
import org.springframework.stereotype.Component;

import java.util.Collections;

/**
 * <AUTHOR>
 * @date 2025/6/30 15:00
 */
@Component
public class MapperOfOrderDetailRequestType
    extends AbstractMapper<Tuple2<Long, IntegrationSoaRequestType>, OrderDetailRequestType> {

    private static final String OFFLINE = "Offline";
    private static final String ONLINE = "Online";
    private static final String APP = "App";

    @Override protected OrderDetailRequestType convert(Tuple2<Long, IntegrationSoaRequestType> tuple) {
        Long orderId = tuple.getT1();
        IntegrationSoaRequestType integrationSoaRequestType = tuple.getT2();
        OrderDetailRequestType orderDetailRequestType = new OrderDetailRequestType();
        orderDetailRequestType.setOrderIdList(Collections.singletonList(orderId));
        orderDetailRequestType.setLanguage(integrationSoaRequestType.getLanguage());
        orderDetailRequestType.setOperationChannel(buildOperationChannel(integrationSoaRequestType.getSourceFrom()));
        orderDetailRequestType.setCacheable(true);
        orderDetailRequestType.setDeleteFlag(false);
        return orderDetailRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple2<Long, IntegrationSoaRequestType> longOrderCreateRequestTypeTuple2) {
        return null;
    }

    /**
     * 查询渠道：App，Online，Offline，Distribution，System
     *
     * @param sourceFrom
     * @return
     */
    private String buildOperationChannel(SourceFrom sourceFrom) {
        if (sourceFrom == null) {
            return APP;
        }
        switch (sourceFrom) {
            case Offline:
                return OFFLINE;
            case Online:
                return ONLINE;
            default:
                return APP;
        }
    }
}
