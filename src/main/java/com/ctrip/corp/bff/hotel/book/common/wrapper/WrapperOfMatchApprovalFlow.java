package com.ctrip.corp.bff.hotel.book.common.wrapper;

import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType;
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextResponseType;
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfAccountInfoConfig;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount.AccountInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail.BaseCheckAvailInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.corp.bff.mice.basic.auth.contract.TmsCreateOrderVerifyResponseType;
import com.ctrip.corp.bff.payment.contract.PaymentOrderCreateResponseType;
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryResponseType;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType;
import com.ctrip.corp.order.paymentcenter.bill.contract.QueryPaymentBillConfigResponseType;
import com.ctrip.microfinance.giftcardpay.ws.contract.RetrieveTicketsByOrderTypeResponseType;
import com.ctrip.soa._21234.CreateTripResponseType;
import com.ctrip.soa._21685.PayConfigResponseType;
import com.ctrip.soa._21685.TransactionPayUrlResponseType;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/18 10:57
 */
public class WrapperOfMatchApprovalFlow {
    private GetTravelPolicyContextResponseType  getTravelPolicyContextResponseType;
    private CheckTravelPolicyResponseType checkTravelPolicyResponseType;
    private QueryCheckAvailContextResponseType queryCheckAvailContextResponseType;
    private OrderCreateRequestType  orderCreateRequestType;
    private ResourceToken  resourceToken;
    private WrapperOfCheckAvail.BaseCheckAvailInfo baseCheckAvailInfo;
    private WrapperOfAccount.AccountInfo accountInfo;
    private SSOInfoQueryResponseType  ssoInfoQueryResponseType;
    private OrderCreateToken orderCreateToken;
    private QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig;
    private GetCityBaseInfoResponseType getCityBaseInfoResponseType;
    private QConfigOfAccountInfoConfig qConfigOfAccountInfoConfig;
    private Map<String, StrategyInfo> strategyInfoMap;

    public static class Build{
        private final WrapperOfMatchApprovalFlow wrapperOfMatchApprovalFlow = new WrapperOfMatchApprovalFlow();

        public Build withGetTravelPolicyContextResponseType(GetTravelPolicyContextResponseType getTravelPolicyContextResponseType) {
            wrapperOfMatchApprovalFlow.getTravelPolicyContextResponseType = getTravelPolicyContextResponseType;
            return this;
        }

        public Build withCheckTravelPolicyResponseType(CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
            wrapperOfMatchApprovalFlow.checkTravelPolicyResponseType = checkTravelPolicyResponseType;
            return this;
        }

        public Build withQueryCheckAvailContextResponseType(QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
            wrapperOfMatchApprovalFlow.queryCheckAvailContextResponseType = queryCheckAvailContextResponseType;
            return this;
        }

        public Build withOrderCreateRequestType(OrderCreateRequestType orderCreateRequestType) {
            wrapperOfMatchApprovalFlow.orderCreateRequestType = orderCreateRequestType;
            return this;
        }

        public Build withResourceToken(ResourceToken resourceToken) {
            wrapperOfMatchApprovalFlow.resourceToken = resourceToken;
            return this;
        }

        public Build withBaseCheckAvailInfo(WrapperOfCheckAvail.BaseCheckAvailInfo baseCheckAvailInfo) {
            wrapperOfMatchApprovalFlow.baseCheckAvailInfo = baseCheckAvailInfo;
            return this;
        }

        public Build withAccountInfo(WrapperOfAccount.AccountInfo accountInfo) {
            wrapperOfMatchApprovalFlow.accountInfo = accountInfo;
            return this;
        }

        public Build withSSOInfoQueryResponseType(SSOInfoQueryResponseType ssoInfoQueryResponseType) {
            wrapperOfMatchApprovalFlow.ssoInfoQueryResponseType = ssoInfoQueryResponseType;
            return this;
        }

        public Build withOrderCreateToken(OrderCreateToken orderCreateToken) {
            wrapperOfMatchApprovalFlow.orderCreateToken = orderCreateToken;
            return this;
        }

        public Build withQconfigOfCertificateInitConfig(QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig) {
            wrapperOfMatchApprovalFlow.qconfigOfCertificateInitConfig = qconfigOfCertificateInitConfig;
            return this;
        }

        public Build withGetCityBaseInfoResponseType(GetCityBaseInfoResponseType getCityBaseInfoResponseType) {
            wrapperOfMatchApprovalFlow.getCityBaseInfoResponseType = getCityBaseInfoResponseType;
            return this;
        }

        public Build withQConfigOfAccountInfoConfig(QConfigOfAccountInfoConfig qConfigOfAccountInfoConfig) {
            wrapperOfMatchApprovalFlow.qConfigOfAccountInfoConfig = qConfigOfAccountInfoConfig;
            return this;
        }

        public Build withStrategyInfoMap(Map<String, StrategyInfo> strategyInfoMap) {
            wrapperOfMatchApprovalFlow.strategyInfoMap = strategyInfoMap;
            return this;
        }

        public WrapperOfMatchApprovalFlow build() {
            return wrapperOfMatchApprovalFlow;
        }
    }

    public static Build builder() {
        return new WrapperOfMatchApprovalFlow.Build();
    }

    public GetTravelPolicyContextResponseType getGetTravelPolicyContextResponseType() {
        return getTravelPolicyContextResponseType;
    }

    public CheckTravelPolicyResponseType getCheckTravelPolicyResponseType() {
        return checkTravelPolicyResponseType;
    }

    public QueryCheckAvailContextResponseType getQueryCheckAvailContextResponseType() {
        return queryCheckAvailContextResponseType;
    }

    public OrderCreateRequestType getOrderCreateRequestType() {
        return orderCreateRequestType;
    }

    public ResourceToken getResourceToken() {
        return resourceToken;
    }

    public WrapperOfCheckAvail.BaseCheckAvailInfo getBaseCheckAvailInfo() {
        return baseCheckAvailInfo;
    }

    public WrapperOfAccount.AccountInfo getAccountInfo() {
        return accountInfo;
    }

    public SSOInfoQueryResponseType getSsoInfoQueryResponseType() {
        return ssoInfoQueryResponseType;
    }

    public OrderCreateToken getOrderCreateToken() {
        return orderCreateToken;
    }

    public QconfigOfCertificateInitConfig getQconfigOfCertificateInitConfig() {
        return qconfigOfCertificateInitConfig;
    }

    public GetCityBaseInfoResponseType getGetCityBaseInfoResponseType() {
        return getCityBaseInfoResponseType;
    }

    public QConfigOfAccountInfoConfig getQConfigOfAccountInfoConfig() {
        return qConfigOfAccountInfoConfig;
    }

    public Map<String, StrategyInfo> getStrategyInfoMap() {
        return strategyInfoMap;
    }
}
