package com.ctrip.corp.bff.hotel.book.common.signature.param;

import io.protostuff.Tag;

/**
 * <AUTHOR>
 * @date 2025/3/6 12:39
 * 注意！！！！！ 不要自己new，必须在OrderCreateProcessorOfUtil.buildBookInfoBO方法中new
 */
public class BookInfoBO {
    // 注意！！！！！ 若有新增或删除字段，需要修改BOOK_PARAMS_VERSION
    public static final String BOOK_VERSION = "2";
    @Tag(1)
    public String roomPayType;
    @Tag(2)
    public String servicePayType;

    public String getRoomPayType() {
        return roomPayType;
    }

    public void setRoomPayType(String roomPayType) {
        this.roomPayType = roomPayType;
    }

    public String getServicePayType() {
        return servicePayType;
    }

    public void setServicePayType(String servicePayType) {
        this.servicePayType = servicePayType;
    }
}
