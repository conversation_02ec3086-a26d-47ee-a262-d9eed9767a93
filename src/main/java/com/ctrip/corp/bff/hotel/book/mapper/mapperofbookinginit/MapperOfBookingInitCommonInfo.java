package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.agg.hotel.expense.contract.model.ChargeAmountInfoType;
import com.ctrip.corp.agg.hotel.expense.contract.model.ServiceChargePriceType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.AddPriceRuleType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookingRulesType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelRatePlan;
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomCouponInfoType;
import com.ctrip.corp.agg.hotel.salestrategy.entity.PriceType;
import com.ctrip.corp.bff.framework.hotel.common.util.HotelDateRangeUtil;
import com.ctrip.corp.bff.framework.hotel.entity.contract.AddPriceInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput;
import com.ctrip.corp.bff.framework.template.common.log.enumeration.DateUnitEnum;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.common.utils.date.DateUtil;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.AmountInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.Tuple;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.model.CalculateServiceChargeV2ResponseType;
import org.apache.commons.collections.CollectionUtils;
import org.apache.http.client.utils.DateUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * @Author: chenchuang
 * @Date: 2024/8/23 16:27
 * @Description: 酒店填写页通用逻辑
 */
@Component
public class MapperOfBookingInitCommonInfo<T extends Tuple, K> extends AbstractMapper<T, K> {

    public static final String CNY = "CNY";
    public static final String WECHAT = "WECHAT";
    public static final String PRIVATE = "P";
    public static final String PUBLIC = "C";

    public static final String FILL_ORDER = "FILL_ORDER";

    public static final String FLASH_STAY_PAY = "FLASH_STAY_PAY";

    public static final String ACCOUNT_PAY = "ACCOUNT_PAY";
    public static final String INDIVIDUAL_PAY = "INDIVIDUAL_PAY";
    public static final String MIX_PAY = "MIX_PAY";
    public static final String CASH_PAY = "CASH_PAY";
    public static final String UNION_PAY = "UNION_PAY";

    public static final String DISABLED = "DISABLED";

    @Override
    protected K convert(T t) {
        return null;
    }

    @Override
    protected ParamCheckResult check(T t) {
        return null;
    }


    // NOTE: 契约中之前有addPrice，现在从哪里取,addPrice是否可能不为整数--蓝色空间接入时做
    protected PriceType getAddPriceAmount(BookingInitRequestType bookingInitRequestType,
                                        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo, String customCurrency) {
        int addPrice = Optional.ofNullable(bookingInitRequestType.getAddPriceInput())
                .map(AddPriceInput::getAmountInfo).map(AmountInfo::getAmount).map(Integer::valueOf).orElse(0);
        Boolean canAddPrice = canAddPrice(checkAvailInfo.getBookingRules(),
                bookingInitRequestType.getIntegrationSoaRequestType().getSourceFrom());
        if (canAddPrice && addPrice > 0) {
            HotelBookInput hotelBookInput = bookingInitRequestType.getHotelBookInput();
            long totalAddPrice = addPrice * HotelDateRangeUtil.getRoomNights(hotelBookInput);
            PriceType addPriceAmount = new PriceType(BigDecimal.valueOf(totalAddPrice), customCurrency);
            return addPriceAmount;
        }

        return null;
    }

    protected boolean canAddPrice(BookingRulesType bookingRulesForAgg, SourceFrom sourceFrom) {
        if (SourceFrom.Offline != sourceFrom) {
            return false;
        }
        // NOTE:是否可以加价如何判断 --蓝色空间接入时做

        return Optional.ofNullable(bookingRulesForAgg).map(BookingRulesType::getAddPriceRule).map(AddPriceRuleType::isCanAddPrice).orElse(false);
    }



    /**
     * 叠加优惠券总金额
     * @param checkAvailInfo:房型价格下载，包括房型信息
     * @return
     */
    protected PriceType getCouponAmount(WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo) {
        if (checkAvailInfo == null) {
            return null;
        }

        // 叠加优惠券总金额
        BigDecimal amount = checkAvailInfo.getCouponAmount();
        if (amount == null || amount.intValue() == 0) {
            return null;
        }
        PriceType couponAmount = new PriceType(amount, CNY);
        return couponAmount;
    }

    /**
     * 政策执行人
     * @param policyInput
     * @return
     */
    protected String getPolicyUid(PolicyInput policyInput, UserInfo userInfo) {
        String policyUid = Optional.ofNullable(policyInput).map(PolicyInput::getPolicyUid).orElse(null);
        return StringUtil.isBlank(policyUid) ? userInfo.getUserId() : policyUid;
    }


    protected List<ChargeAmountInfoType> getChargeAmountInfoList(CalculateServiceChargeV2ResponseType responseType) {
        if (responseType == null || CollectionUtils.isEmpty(responseType.getChargeAmountInfoList())) {
            return null;
        }

        List<ChargeAmountInfoType> chargeAmountInfoTypes = responseType.getChargeAmountInfoList().stream().filter((s) -> {
            return s != null
                    && s.getChargeAmountPack() != null
                    && ((Number)Optional.ofNullable(s.getChargeAmountPack().getChargeAmountCustomCurrency())
                    .map(ServiceChargePriceType::getAmount).orElse(new BigDecimal(BigInteger.ZERO))).intValue() > 0;
        }).collect(Collectors.toList());
        return chargeAmountInfoTypes;
    }

    protected HotelPayTypeEnum getRoomPayType(HotelPayTypeEnum defaultPayment,
                                              String hotelBookingFilterByPolicy, List<HotelPayTypeEnum> payList) {

        boolean isDefaultPolicy = StringUtil.equalsIgnoreCase("S", hotelBookingFilterByPolicy);

        if (payList.contains(HotelPayTypeEnum.UNION_PAY)) {
            return HotelPayTypeEnum.UNION_PAY;
        }

        if (CollectionUtils.isEmpty(payList)) {
            return HotelPayTypeEnum.NONE;
        }

        if (payList.contains(defaultPayment)) {
            return defaultPayment;
        }

        // 有闪住默认闪住支付方式
        if (payList.contains(HotelPayTypeEnum.FLASH_STAY_PAY)) {
            return HotelPayTypeEnum.FLASH_STAY_PAY;
        }

        if (payList.contains(HotelPayTypeEnum.MIX_PAY) && isDefaultPolicy) {
            // 符合差标勾选不可改，默认混付
            return HotelPayTypeEnum.MIX_PAY;
        }

        if (payList.contains(HotelPayTypeEnum.CORP_PAY)) {
            return HotelPayTypeEnum.CORP_PAY;
        }

        if (payList.contains(HotelPayTypeEnum.SELF_PAY)) {
            return HotelPayTypeEnum.SELF_PAY;
        }

        return CollectionUtil.findFirst(payList, Objects::nonNull);
    }


}
