package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.bff.framework.hotel.common.constant.BooleanConstant;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.foundation.common.util.Null;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Date 2024/9/10 12:09
 * @Version 1.0
 * @Description 策略工具类 一个项目一个就可以了吧？？？
 */
public class StrategyOfBookingInitUtil {


    private static final String STRATEGY_VALUE_MODIFY = "MODIFY";

    private static final String STRATEGY_VALUE_COPY_ORDER_INIT = "CopyOrder";

    private static final String STRATEGY_VALUE_COPY_ORDER = "COPY_ORDER";

    private static final String STRATEGY_VALUE_MORE_ORDER = "MORE_ORDER";

    // 定后-审批沿用场景
    public static final String STRATEGY_VALUE_APPROVAL_CONTINUED_USE = "APPROVAL_CONTINUED_USE";

    private static final String STRATEGY_VALUE_T = "T";

    public static final String STRATEGY_KEY_BOOKING_SCENARIO = "BOOKING_SCENARIO";
    public static final String STRATEGY_KEY_BOOKING_WITH_PERSONAL_ACCOUNT = "BOOKING_WITH_PERSONAL_ACCOUNT";
    public static final String STRATEGY_KEY_WECHAT_MINI_PROGRAM = "WECHAT_MINI_PROGRAM";
    public static final String STRATEGY_KEY_BOOKING_TYPE_SHARE_ROOM = "BOOKING_TYPE_SHARE_ROOM";
    private static final String STRATEGY_KEY_BOOKING_PLATFORM = "BOOKING_PLATFORM";

    private static final String STRATEGY_VALUE_EXTEND = "EXTEND";
    private static final String STRATEGY_VALUE_APPLY_MODIFY = "APPLY_MODIFY";
    private static final String CONFIRM_ORDER = "CONFIRM_ORDER";
    // offline表单支付
    private static final String OFFLINE_FROM_PAY = "OFFLINE_FROM_PAY";
    // 第一次请求
    private static final String FIRST_REQUEST = "FIRST_REQUEST";

    private static final String SUPPORT_OFFLINE_FROM_PAY = "SUPPORT_OFFLINE_FROM_PAY";

    public static final String STRATEGY_KEY_OPEN_ROOM_STATUS_INPUT = "OPEN_ROOM_STATUS_INPUT";
    public static final String ONLY_QUERY_DEFAULT_APPROVE_INFO = "ONLY_QUERY_DEFAULT_APPROVE_INFO";
    private static final String CURRENTLY_ON_PAGE = "CURRENTLY_ON_PAGE";
    private static final String CHOSE_PSG_FRONT = "CHOSE_PSG_FRONT";
    private static final String HOME = "HOME";
    private static final String BOOKING_INIT = "BOOKING_INIT";
    private static final String NEED_ARRIVE_TIME_INFO = "NEED_ARRIVE_TIME_INFO";

    public static final String NEED_CARD_PAY_FEE = "NEED_CARD_PAY_FEE";
    public static final String STRATEGY_KEY_HOTEL_CHECK_AVAIL = "HOTEL_CHECK_AVAIL";
    // 是否支持心程贝以ams查到的配置为准
    public static final String PERSONAL_ACCOUNT_BY_AMS = "PERSONAL_ACCOUNT_BY_AMS";
    // 行程模式下，如果行程单是已审批通过&未完成的追加单场景，可以支持单订单审批流
    public static final String TRIP_PASS_NEED_SINGLE_APPROVAL = "TRIP_PASS_NEED_SINGLE_APPROVAL";
    // 不需要offline核对订单流程
    public static final String NOT_REQUIRED_CONFIRM_ORDER = "NOT_REQUIRED_CONFIRM_ORDER";
    // 审批单管控信息使用agg的详情做拼接
    public static final String VERIFY_APPROVAL_RESULT_USE_AGG_DETAIL = "VERIFY_APPROVAL_RESULT_USE_AGG_DETAIL";
    // 无感行程使用账户开关判断
    public static final String IS_SENITINERARY_TRIP_USE_AMS = "IS_SENITINERARY_TRIP_USE_AMS";
    // 蓝色空间相对回调地址
    public static final String RELATIVE_NOTIFY = "RELATIVE_NOTIFY";
    // 蓝色空间相对支付通知地址
    public static final String RELATIVE_RECALL = "RELATIVE_RECALL";
    // 管控是否需要海外服务费信息
    public static final String NEED_INTL_SERVICE_CHARGE_INFO = "NEED_INTL_SERVICE_CHARGE_INFO";
    // 蓝色空间无需发票信息
    public static final String NO_NEED_INVOICE = "NO_NEED_INVOICE";
    // 创建行程需要子产品线
    public static final String TRIP_NEED_SUB_PRODUCT_LINE = "TRIP_NEED_SUB_PRODUCT_LINE";
    // EARNPOINT_USE_FIRST_PASSENGER 蓝色空间，使用第一个人积分
    public static final String EARN_POINT_USE_FIRST_PASSENGER = "EARN_POINT_USE_FIRST_PASSENGER";

    // 使用主账户所属地区的匹配审批流产品-预订的酒店所在城市为所属地区则匹配国内审批流，否则匹配国际酒店审批流
    public static final String CITY_WITHIN_POINT_OF_SALE = "CITY_WITHIN_POINT_OF_SALE";

    // 蓝色空间不需要礼品卡
    public static final String BLOCK_RETRIEVE_TICKET = "BLOCK_RETRIEVE_TICKET";


    // 蓝色空间，行程场景，需要补单成本中心
    public static final String TRIP_NEED_COST_CENTER = "TRIP_NEED_COST_CENTER";

    // 禁用 按超标阶段匹配审批流的功能
    public static final String BLOCK_MATCH_OVER_STANDARD_STAGE_APPROVAL_FLOW =
        "BLOCK_MATCH_OVER_STANDARD_STAGE_APPROVAL_FLOW";

    // 提前审批前置，填写页支持切换审批单据
    public static final String PRE_APPROVAL_SUPPORT_SWITCHING_SUB_APPROVAL_NO =
        "PRE_APPROVAL_SUPPORT_SWITCHING_SUB_APPROVAL_NO";

    /**
     * 使用新人数逻辑调整-出行人：入住人数/间数取大 非出行人vo入参的人数/间数取大，VO不再依赖账户接口
     */
    public static final String HOTEL_BOOK_INPUT_NEW = "HOTEL_BOOK_INPUT_NEW";
    // 国内站灰度智能延用，开关由vo传入
    public static final String APPROVAL_FLOW_REUSE_NEW = "APPROVAL_FLOW_REUSE_NEW";
    // 智能沿用，不可沿用但有推荐单据时，支持与客户二次交互修改信息
    public static final String APPROVAL_FLOW_REUSE_AI_MODIFY = "APPROVAL_FLOW_REUSE_AI_MODIFY";

    // 是否非智能沿用
    public static boolean approvalReuseReBook(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueMatch(strategyInfoMap, STRATEGY_KEY_BOOKING_SCENARIO,
            STRATEGY_VALUE_APPROVAL_CONTINUED_USE);
    }

    // 国内站智能沿用灰度走新版
    public static boolean approvalFlowReuseNew(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueIsT(strategyInfoMap, APPROVAL_FLOW_REUSE_NEW);
    }

    // 智能沿用，不可沿用但有推荐单据时，支持与客户二次交互修改信息
    public static boolean approvalFlowReuseAiModify(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueIsT(strategyInfoMap, APPROVAL_FLOW_REUSE_AI_MODIFY);
    }

    private static boolean strategyValueMatch(Map<String, StrategyInfo> strategyInfos, String key, String value) {
        if (CollectionUtil.isEmpty(strategyInfos) || StringUtil.isEmpty(key) || StringUtil.isEmpty(value)) {
            return false;
        }
        if (strategyInfos.get(key) == null) {
            return false;
        }
        return value.equalsIgnoreCase(strategyInfos.get(key).getStrategyValue());
    }

    public static boolean useHotelBookInputNew(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueIsT(strategyInfoMap, HOTEL_BOOK_INPUT_NEW);
    }

    public static boolean preApprovalSupportSwitchingSubApprovalNo(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueIsT(strategyInfoMap, BLOCK_MATCH_OVER_STANDARD_STAGE_APPROVAL_FLOW);
    }

    public static boolean blockMatchOveStandardStageApprovalFlow(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueIsT(strategyInfoMap, BLOCK_MATCH_OVER_STANDARD_STAGE_APPROVAL_FLOW);
    }


    public static boolean tripNeedCostCenter(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueIsT(strategyInfoMap, TRIP_NEED_COST_CENTER);
    }

    public static boolean cityWithinPointOfSale(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueIsT(strategyInfoMap, CITY_WITHIN_POINT_OF_SALE);
    }

    public static boolean blockRetrieveTicket(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueIsT(strategyInfoMap, BLOCK_RETRIEVE_TICKET);
    }


    /**
     * 蓝色空间使用证件国籍
     */
    public static final String STRATEGY_KEY_USE_CERTIFICATE_NATIONALITY = "USE_CERTIFICATE_NATIONALITY";

    public static boolean useCertificateNationality(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
                info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_USE_CERTIFICATE_NATIONALITY) && (
                        BooleanConstant.STR_T.equalsIgnoreCase(info.getStrategyValue())));
    }
    public static boolean earnPointUseFirstPassenger(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueIsT(strategyInfoMap, EARN_POINT_USE_FIRST_PASSENGER);
    }

    public static boolean verifyIsSenitineraryTripUseAms(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueIsT(strategyInfoMap, IS_SENITINERARY_TRIP_USE_AMS);
    }
    public static boolean tripNeedSubProductLine(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueIsT(strategyInfoMap, TRIP_NEED_SUB_PRODUCT_LINE);
    }
    public static boolean relativeNotify(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueIsT(strategyInfoMap, RELATIVE_NOTIFY);
    }
    public static boolean relativeRecall(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueIsT(strategyInfoMap, RELATIVE_RECALL);
    }


    public static boolean verifyApprovalResultUseAggDetail(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueIsT(strategyInfoMap, VERIFY_APPROVAL_RESULT_USE_AGG_DETAIL);
    }

    public static boolean notRequiredConfirmOrder(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueIsT(strategyInfoMap, NOT_REQUIRED_CONFIRM_ORDER);
    }

    public static boolean tripPassNeedSingleApproval(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueIsT(strategyInfoMap, TRIP_PASS_NEED_SINGLE_APPROVAL);
    }

    public static boolean personalAccountByAms(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueIsT(strategyInfoMap, PERSONAL_ACCOUNT_BY_AMS);
    }

    public static boolean bookingWithPersonalAccount(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueIsT(strategyInfoMap, STRATEGY_KEY_BOOKING_WITH_PERSONAL_ACCOUNT);
    }
    public static boolean intlServiceChargeInfo(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueIsT(strategyInfoMap, NEED_INTL_SERVICE_CHARGE_INFO);
    }
    public static boolean needInvoice(Map<String, StrategyInfo> strategyInfoMap) {
        return !strategyValueIsT(strategyInfoMap, NO_NEED_INVOICE);
    }
    public static Map<String, StrategyInfo> buildStrategyInfoMap(List<StrategyInfo> strategyInfos) {
        if (CollectionUtil.isEmpty(strategyInfos)) {
            return null;
        }
        Map<String, StrategyInfo> strategyInfoMap = new HashMap<>();
        strategyInfos.forEach(strategyInfo -> {
            if (strategyInfo != null && StringUtil.isNotBlank(strategyInfo.getStrategyKey())) {
                strategyInfoMap.put(strategyInfo.getStrategyKey(), strategyInfo);
            }
        });
        return strategyInfoMap;
    }

    private static boolean strategyValueIsT(Map<String, StrategyInfo> strategyInfos, String key) {
        if (CollectionUtil.isEmpty(strategyInfos) || StringUtil.isEmpty(key)) {
            return false;
        }
        if (strategyInfos.get(key) == null) {
            return false;
        }
        return STRATEGY_VALUE_T.equalsIgnoreCase(strategyInfos.get(key).getStrategyValue());
    }

    private static final String STRATEGY_KEY_NEED_CITY_BASE_INFO = "NEED_CITY_BASE_INFO";

    public static boolean needCityBaseInfo(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
                info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_NEED_CITY_BASE_INFO) && (
                        BooleanConstant.STR_T.equalsIgnoreCase(info.getStrategyValue())));
    }

    public static boolean moreOrder(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
                info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_BOOKING_SCENARIO) && (
                        STRATEGY_VALUE_MORE_ORDER.equalsIgnoreCase(info.getStrategyValue())));
    }

    /**
     * 支付方式列表排序策略
     */
    public static final String STRATEGY_KEY_SORT_PAY_INFO_LIST = "SORT_PAY_INFO_LIST";

    /**
     * 联系人国家码限制信息
     */
    public static final String STRATEGY_KEY_CONTRACT_COUNTRY_CODE_LIMIT = "CONTRACT_COUNTRY_CODE_LIMIT";

    /**
     * 国籍限制信息
     */
    public static final String STRATEGY_KEY_NATIONALITY_RESTRICTION = "NATIONALITY_RESTRICTION";

    /**
     * 出行人姓名语种限制优先agg结果
     */
    public static final String STRATEGY_KEY_GUEST_NAME_LANGUAGE_AGG_RESULT = "GUEST_NAME_LANGUAGE_AGG_RESULT";


    /**
     * 价格星级是否超标信息
     */
    public static final String STRATEGY_KEY_PRICE_STAR_OVER_STANDARD = "PRICE_STAR_OVER_STANDARD";

    /**
     * 仅账户和英文环境邮箱必填
     */
    public static final String STRATEGY_KEY_EN_AND_ACCOUNT_REQUIRE_CONTACT_EMAIL = "EN_AND_ACCOUNT_REQUIRE_CONTACT_EMAIL";

    /**
     * 获取第一个入住人的会员卡号
     */
    public static final String STRATEGY_KEY_FIRST_PASSENGER_MEMBERSHIP_CARD = "FIRST_PASSENGER_MEMBERSHIP_CARD";

    /**
     * 老套餐
     */
    public static final String OLD_PKG_PRODUCT = "OLD_PKG_PRODUCT";

    /**
     * 蓝色空间支持多币种优惠券
     */
    public static final String STRATEGY_KEY_SUPPORT_MULTI_CURRENCY_COUPON = "SUPPORT_MULTI_CURRENCY_COUPON";

    public static boolean supportMultiCurrencyCoupon(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
                info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_SUPPORT_MULTI_CURRENCY_COUPON) && (
                        BooleanConstant.STR_T.equalsIgnoreCase(info.getStrategyValue())));
    }

    public static boolean needOldPkgProduct(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
            info -> info.getStrategyKey().equalsIgnoreCase(OLD_PKG_PRODUCT) && (
                BooleanConstant.STR_T.equalsIgnoreCase(info.getStrategyValue())));
    }

    /**
     * 蓝色空间服务费
     */
    public static final String STRATEGY_KEY_BLUE_SPACE_PRE_CHARGE_SERVICE = "BLUE_SPACE_PRE_CHARGE_SERVICE";

    /**
     * 校验下单政策执行人与行程政策执行人一致性
     */
    public static final String STRATEGY_KEY_TRIPPOLICYID_MUST_SAME_ORDERPOLICYID = "TRIPPOLICYID_MUST_SAME_ORDERPOLICYID";

    public static boolean isTripPolicyIdMustSameOrderPolicyId(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
                info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_TRIPPOLICYID_MUST_SAME_ORDERPOLICYID) && (
                        BooleanConstant.STR_T.equalsIgnoreCase(info.getStrategyValue())));
    }

    public static boolean isBlueSpacePreChargeService(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
                info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_BLUE_SPACE_PRE_CHARGE_SERVICE) && (
                        BooleanConstant.STR_T.equalsIgnoreCase(info.getStrategyValue())));
    }


    /**
     * 取消话术无需PaymentDesc节点描述
     */
    public static final String STRATEGY_KEY_CANCEL_DESC_WITHOUT_PAYMENT_DESC = "CANCEL_DESC_WITHOUT_PAYMENT_DESC";

    public static boolean cancelDescWithoutPaymentDesc(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
                info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_CANCEL_DESC_WITHOUT_PAYMENT_DESC) && (
                        BooleanConstant.STR_T.equalsIgnoreCase(info.getStrategyValue())));
    }

    /**
     * 是否选中马来西亚国籍
     */
    public static final String STRATEGY_KEY_CHECKED_MAS = "CHECKED_MAS";

    /**
     * 第一个出行人的国籍
     */
    public static final String STRATEGY_KEY_FIRST_NATION = "FIRST_NATION";

    /**
     * 需要修改RC信息
     */
    public static final String STRATEGY_KEY_NEED_MODIFY_RC_INFO = "NEED_MODIFY_RC_INFO";


    /**
     * bagashi定制 ，语言环境导致不可订
     */
    public static final String STRATEGY_KEY_BAGASHI_LOCALE_CHECKAVAIL_FAIL_INFO = "BAGASHI_LOCALE_CHECKAVAIL_FAIL_INFO";

    /**
     * 可订下发是bagashi场景需要定制
     */
    public static final String STRATEGY_KEY_BAGASHI_CUSTOM = "BAGASHI_CUSTOM";

    public static boolean isBagashiCustom(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
                info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_BAGASHI_CUSTOM) && (
                        BooleanConstant.STR_T.equalsIgnoreCase(info.getStrategyValue())));
    }

    public static boolean bagashiLocaleCheckAvailFailInfo(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
                info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_BAGASHI_LOCALE_CHECKAVAIL_FAIL_INFO) && (
                        BooleanConstant.STR_T.equalsIgnoreCase(info.getStrategyValue())));
    }

    public static boolean needModifyRcInfo(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
                info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_NEED_MODIFY_RC_INFO) && (
                        BooleanConstant.STR_T.equalsIgnoreCase(info.getStrategyValue())));
    }

    public static boolean useFirstNation(List<StrategyInfo> strategyInfos) {
        return Null.or(strategyInfos, new ArrayList<StrategyInfo>()).stream().filter(Objects::nonNull)
                .anyMatch(t -> STRATEGY_KEY_FIRST_NATION.equalsIgnoreCase(t.getStrategyKey()));
    }

    public static String getFirstNation(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).flatMap(o -> o.stream().filter(info -> info.getStrategyKey()
                .equalsIgnoreCase(STRATEGY_KEY_FIRST_NATION)).findFirst()).map(StrategyInfo::getStrategyValue).orElse(null);
    }

    public static boolean checkedMas(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
                info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_CHECKED_MAS) && (
                        BooleanConstant.STR_T.equalsIgnoreCase(info.getStrategyValue())));
    }

    public static boolean sortPayInfoList(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
                info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_SORT_PAY_INFO_LIST) && (
                        BooleanConstant.STR_T.equalsIgnoreCase(info.getStrategyValue())));
    }
    /**
     * 需要RC信息
     */
    public static final String STRATEGY_KEY_NEED_RC_INFO = "NEED_RC_INFO";

    public static boolean needRcInfo(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
                info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_NEED_RC_INFO) && (
                        BooleanConstant.STR_T.equalsIgnoreCase(info.getStrategyValue())));
    }

    public static boolean needFirstPassengerMembershipCard(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
                info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_FIRST_PASSENGER_MEMBERSHIP_CARD) && (
                        BooleanConstant.STR_T.equalsIgnoreCase(info.getStrategyValue())));
    }
    public static boolean enAndAccountRequireContactEmail(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
                info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_EN_AND_ACCOUNT_REQUIRE_CONTACT_EMAIL) && (
                        BooleanConstant.STR_T.equalsIgnoreCase(info.getStrategyValue())));
    }

    public static boolean needPriceStarOverStandard(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
                info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_PRICE_STAR_OVER_STANDARD) && (
                        BooleanConstant.STR_T.equalsIgnoreCase(info.getStrategyValue())));
    }

    public static boolean isGuestNameLanguageAggResult(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
                info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_GUEST_NAME_LANGUAGE_AGG_RESULT) && (
                        BooleanConstant.STR_T.equalsIgnoreCase(info.getStrategyValue())));
    }

    public static boolean needContractCountryCodeLimitInfo(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
                info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_CONTRACT_COUNTRY_CODE_LIMIT) && (
                        BooleanConstant.STR_T.equalsIgnoreCase(info.getStrategyValue())));
    }

    public static boolean needNationalityRestriction(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
                info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_NATIONALITY_RESTRICTION) && (
                        BooleanConstant.STR_T.equalsIgnoreCase(info.getStrategyValue())));
    }

    public static boolean modify(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
            info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_BOOKING_SCENARIO) && (
                STRATEGY_VALUE_MODIFY.equalsIgnoreCase(info.getStrategyValue())));
    }

    public static boolean modify(Map<String, StrategyInfo> strategyInfoMap) {
        return STRATEGY_VALUE_MODIFY.equalsIgnoreCase(Optional.ofNullable(strategyInfoMap)
                .orElse(new HashMap<String, StrategyInfo>())
                .getOrDefault(STRATEGY_KEY_BOOKING_SCENARIO, new StrategyInfo()).getStrategyValue());
    }

    public static boolean wechatMiniProgram(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream()
                .anyMatch(info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_WECHAT_MINI_PROGRAM)
                        && STRATEGY_VALUE_T.equalsIgnoreCase(info.getStrategyValue()));
    }

    public static boolean bookingWithPersonalAccount(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream()
                .anyMatch(info -> STRATEGY_KEY_BOOKING_WITH_PERSONAL_ACCOUNT.equalsIgnoreCase(info.getStrategyKey())
                        && STRATEGY_VALUE_T.equalsIgnoreCase(info.getStrategyValue()));
    }

    public static boolean copyOrder(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
            info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_BOOKING_SCENARIO) && (
                STRATEGY_VALUE_COPY_ORDER_INIT.equalsIgnoreCase(info.getStrategyValue())
                    || STRATEGY_VALUE_COPY_ORDER.equalsIgnoreCase(info.getStrategyValue())));
    }

    public static boolean choseShareRoom(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream()
                .anyMatch(info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_BOOKING_TYPE_SHARE_ROOM)
                        && STRATEGY_VALUE_T.equalsIgnoreCase(info.getStrategyValue()));
    }

    public static String getPlatForm(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).flatMap(o -> o.stream().filter(info -> info.getStrategyKey()
                .equalsIgnoreCase(STRATEGY_KEY_BOOKING_PLATFORM)).findFirst()).map(StrategyInfo::getStrategyValue).orElse(null);
    }

    public static boolean applyModify(List<StrategyInfo> strategyInfos) {
        return
            Optional.ofNullable(strategyInfos).orElse(new ArrayList<>())
                .stream().anyMatch(
                    info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_BOOKING_SCENARIO)
                        && STRATEGY_VALUE_EXTEND.equalsIgnoreCase(info.getStrategyValue()))
                ||
                Optional.ofNullable(strategyInfos).orElse(new ArrayList<>())
                    .stream().anyMatch(
                        info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_BOOKING_SCENARIO)
                && STRATEGY_VALUE_APPLY_MODIFY.equalsIgnoreCase(info.getStrategyValue()));
    }

    public static boolean applyModify(Map<String, StrategyInfo> strategyInfoMap) {
        if (CollectionUtil.isEmpty(strategyInfoMap) || StringUtil.isEmpty(STRATEGY_KEY_BOOKING_SCENARIO)) {
            return false;
        }
        if (strategyInfoMap.get(STRATEGY_KEY_BOOKING_SCENARIO) == null) {
            return false;
        }
        return STRATEGY_VALUE_EXTEND.equalsIgnoreCase(
            strategyInfoMap.get(STRATEGY_KEY_BOOKING_SCENARIO).getStrategyValue())
            || STRATEGY_VALUE_APPLY_MODIFY.equalsIgnoreCase(
            strategyInfoMap.get(STRATEGY_KEY_BOOKING_SCENARIO).getStrategyValue());
    }

    public static boolean applyModifyOnly(Map<String, StrategyInfo> strategyInfoMap) {
        if (CollectionUtil.isEmpty(strategyInfoMap) || StringUtil.isEmpty(STRATEGY_KEY_BOOKING_SCENARIO)) {
            return false;
        }
        if (strategyInfoMap.get(STRATEGY_KEY_BOOKING_SCENARIO) == null) {
            return false;
        }
        return STRATEGY_VALUE_APPLY_MODIFY.equalsIgnoreCase(
            strategyInfoMap.get(STRATEGY_KEY_BOOKING_SCENARIO).getStrategyValue());
    }


    public static boolean extend(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
            info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_BOOKING_SCENARIO)
                && STRATEGY_VALUE_EXTEND.equalsIgnoreCase(info.getStrategyValue()));
    }

    public static boolean onlyApplyModify(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
            info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_BOOKING_SCENARIO) && (
                STRATEGY_VALUE_APPLY_MODIFY.equalsIgnoreCase(info.getStrategyValue())));
    }

    public static boolean confirmOrder(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
            info -> info.getStrategyKey().equalsIgnoreCase(CONFIRM_ORDER) && STRATEGY_VALUE_T.equalsIgnoreCase(
                info.getStrategyValue()));
    }

    public static boolean offlineFromPay(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
            info -> info.getStrategyKey().equalsIgnoreCase(SUPPORT_OFFLINE_FROM_PAY) && STRATEGY_VALUE_T.equalsIgnoreCase(
                info.getStrategyValue()));
    }

    public static boolean firstRequest(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
                info -> info.getStrategyKey().equalsIgnoreCase(FIRST_REQUEST) && STRATEGY_VALUE_T.equalsIgnoreCase(
                        info.getStrategyValue()));
    }

    public static boolean openRoomStatus(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
            info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_OPEN_ROOM_STATUS_INPUT)
                && STRATEGY_VALUE_T.equalsIgnoreCase(info.getStrategyValue()));
    }

    /**
     * 是否只查询默认审批单信息
     *
     * @param strategyInfos
     * @return
     */
    public static boolean onlyQueryDefaultApproveInfo(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
            info -> ONLY_QUERY_DEFAULT_APPROVE_INFO.equalsIgnoreCase(info.getStrategyKey())
                && STRATEGY_VALUE_T.equalsIgnoreCase(info.getStrategyValue()));
    }

    public static boolean fromBookingInit(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
            info -> StringUtil.equalsIgnoreCase(info.getStrategyKey(), BOOKING_INIT)
                && StringUtil.equalsIgnoreCase(info.getStrategyValue(), STRATEGY_VALUE_T));
    }

    public static boolean needArriveTimeInfo(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
            info -> StringUtil.equalsIgnoreCase(info.getStrategyKey(), NEED_ARRIVE_TIME_INFO)
                && StringUtil.equalsIgnoreCase(info.getStrategyValue(), STRATEGY_VALUE_T));
    }

    public static boolean passengerPage(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
            info -> info.getStrategyKey().equalsIgnoreCase(CURRENTLY_ON_PAGE) && CHOSE_PSG_FRONT.equalsIgnoreCase(
                info.getStrategyValue()));
    }

    public static boolean homePage(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
            info -> info.getStrategyKey().equalsIgnoreCase(CURRENTLY_ON_PAGE) && HOME.equalsIgnoreCase(
                info.getStrategyValue()));
    }

    /**
     * 当前站点是否需要外卡手续费，蓝色空间需要 国内站不需要 不传入该策略默认不需要
     * @param strategyInfos
     * @return
     */
    public static boolean needCardPayFee(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
            info -> info.getStrategyKey().equalsIgnoreCase(NEED_CARD_PAY_FEE) && (
                STRATEGY_VALUE_T.equalsIgnoreCase(info.getStrategyValue())));
    }

    public static boolean hotelCheckAvail(List<StrategyInfo> strategyInfos) {
        return Optional.ofNullable(strategyInfos).orElse(new ArrayList<>()).stream().anyMatch(
            info -> info.getStrategyKey().equalsIgnoreCase(STRATEGY_KEY_HOTEL_CHECK_AVAIL) && (
                STRATEGY_VALUE_T.equalsIgnoreCase(info.getStrategyValue())));
    }

    public static boolean needRcInfo(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueIsT(strategyInfoMap, STRATEGY_KEY_NEED_RC_INFO);
    }

    public static boolean hotelCheckAvail(Map<String, StrategyInfo> strategyInfoMap) {
        return strategyValueIsT(strategyInfoMap, STRATEGY_KEY_HOTEL_CHECK_AVAIL);
    }

}
