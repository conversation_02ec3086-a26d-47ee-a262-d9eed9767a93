package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.hotel.common.constant.BooleanConstant;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.TemplateHeader;
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.MembershipInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.foundation.common.util.Null;
import com.ctrip.corp.foundation.common.util.NumberUtil;
import corp.user.service.corpUserInfoService.CorpUserHotelVipCard;
import corp.user.service.corpUserInfoService.OperateCorpUserHotelVipCardRequestType;
import corp.user.service.group4j.accounts.BizModeBindRelationData;
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.*;

/**
 * <AUTHOR>
 * @date 2024/05/29
 */
@Component
public class MapperOfOperateCorpUserHotelVipCardRequestType extends
        AbstractMapper<Tuple3<OrderCreateRequestType, QueryBizModeBindRelationResponseType, WrapperOfCheckAvail.BaseCheckAvailInfo>, OperateCorpUserHotelVipCardRequestType> {
    private static final String SAVE = "s";
    @Override
    protected OperateCorpUserHotelVipCardRequestType convert(Tuple3<OrderCreateRequestType, QueryBizModeBindRelationResponseType, WrapperOfCheckAvail.BaseCheckAvailInfo> para) {
        if (para == null) {
            return null;
        }
        OrderCreateRequestType orderCreateRequestType = para.getT1();
        QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType = para.getT2();
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = para.getT3();
        // 无会员卡或积分人
        if (orderCreateRequestType == null
                || orderCreateRequestType.getMembershipInfo() == null
                || StringUtils.isBlank(orderCreateRequestType.getMembershipInfo().getMembershipNo())
                || StringUtils.isBlank(orderCreateRequestType.getMembershipInfo().getMembershipUid())) {
            return null;
        }
        MembershipInfo membershipInfo = orderCreateRequestType.getMembershipInfo();
        if (checkAvailInfo == null) {
            return null;
        }
        List<HotelBookPassengerInput> hotelBookPassengerInputs = orderCreateRequestType.getHotelBookPassengerInputs();
        String bookingUid = Optional.ofNullable(orderCreateRequestType.getIntegrationSoaRequestType())
                .map(IntegrationSoaRequestType::getUserInfo).map(UserInfo::getUserId).orElse(null);
        // 出行人员工中不含积分人 或 非本人
        if (!StringUtils.equalsIgnoreCase(bookingUid, membershipInfo.getMembershipUid())
                && Null.or(hotelBookPassengerInputs, new ArrayList<HotelBookPassengerInput>()).stream().filter(Objects::nonNull)
                .filter(t -> t.getHotelPassengerInput() != null && BooleanConstant.STR_T.equalsIgnoreCase(t.getHotelPassengerInput().getEmployee()))
                .noneMatch(z -> membershipInfo.getMembershipUid().equalsIgnoreCase(z.getHotelPassengerInput().getUid()))) {
            return null;
        }
        // 积分人主营卡uid
        if (queryBizModeBindRelationResponseType == null) {
            return null;
        }
        BizModeBindRelationData bizModeBindRelationData = CollectionUtil.findFirst(queryBizModeBindRelationResponseType.getBizModeBindRelationDataList(),
                t -> t != null && StringUtil.equalsIgnoreCase(membershipInfo.getMembershipUid(), t.getDimensionId()));
        if (bizModeBindRelationData == null || StringUtil.isBlank(bizModeBindRelationData.getPrimaryDimensionId())) {
            return null;
        }
        String membershipPrimaryUid = bizModeBindRelationData.getPrimaryDimensionId();
        OperateCorpUserHotelVipCardRequestType res = new OperateCorpUserHotelVipCardRequestType();
        res.setOperateType(SAVE);
        res.setOperator(bookingUid);
        res.setCorpUserHotelVipCardList(convert2CorpUserHotelVipCardList(membershipPrimaryUid, checkAvailInfo.getGroupId(), membershipInfo.getMembershipNo()));
        return res;
    }

    protected List<CorpUserHotelVipCard> convert2CorpUserHotelVipCardList(String membershipPrimaryUid, Integer groupId, String membershipNo) {
        CorpUserHotelVipCard res = new CorpUserHotelVipCard();
        res.setUid(membershipPrimaryUid);
        res.setHotelGroupID(groupId);
        res.setHtlVipCardID(membershipNo);
        return Arrays.asList(res);
    }

    @Override
    protected ParamCheckResult check(Tuple3<OrderCreateRequestType, QueryBizModeBindRelationResponseType, WrapperOfCheckAvail.BaseCheckAvailInfo> para) {
        return null;
    }
}
