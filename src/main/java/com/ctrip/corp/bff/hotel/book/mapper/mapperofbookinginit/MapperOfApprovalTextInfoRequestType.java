package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.common.enums.BookInitStrategyEnum;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.bff.specific.contract.ApprovalTextInfoRequestType;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/6/26 20:37
 */
@Component
public class MapperOfApprovalTextInfoRequestType extends AbstractMapper<Tuple2<IntegrationSoaRequestType,
        Map<String, StrategyInfo>>, ApprovalTextInfoRequestType> {

    /**
     * 普通预定
     */
    private static final String NORMAL = "NORMAL";
    /**
     * 酒店修改
     */
    private static final String HOTEL_CHANGE = "HOTEL_CHANGE";

    @Override
    protected ApprovalTextInfoRequestType convert(Tuple2<IntegrationSoaRequestType, Map<String, StrategyInfo>> param) {

        ApprovalTextInfoRequestType approvalDefaultRequestType = new ApprovalTextInfoRequestType();
        approvalDefaultRequestType.setIntegrationSoaRequestType(param.getT1());
        approvalDefaultRequestType.setBookingType(buildBookingType(param.getT2()));
        return approvalDefaultRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple2<IntegrationSoaRequestType, Map<String, StrategyInfo>> param) {
        return null;
    }

    private String buildBookingType(Map<String, StrategyInfo> strategyInfos) {
        if (StrategyOfBookingInitUtil.modify(strategyInfos)) {
            return HOTEL_CHANGE;
        } else {
            return NORMAL;
        }
    }
}
