package com.ctrip.corp.bff.hotel.book.qconfig.entity.entityofpassengerform;

import java.util.List;

/**
 * @Author: z.c. wang
 * @Description 酒店出行人表单配置
 * @Date: 2025/3/14 14:37
 * @Version 1.0
 */
public class NameRuleItemEntityEntity {
    private String certificateDesc;
    private String certificateType;
    private List<String> nameTypes;
    private List<RuleInfoEntity> enNameRuleInfos;
    private List<RuleInfoEntity> localNameRuleInfos;

    public String getCertificateDesc() {
        return certificateDesc;
    }

    public void setCertificateDesc(String certificateDesc) {
        this.certificateDesc = certificateDesc;
    }

    public String getCertificateType() {
        return certificateType;
    }

    public void setCertificateType(String certificateType) {
        this.certificateType = certificateType;
    }

    public List<RuleInfoEntity> getEnNameRuleInfos() {
        return enNameRuleInfos;
    }

    public void setEnNameRuleInfos(List<RuleInfoEntity> enNameRuleInfos) {
        this.enNameRuleInfos = enNameRuleInfos;
    }

    public List<RuleInfoEntity> getLocalNameRuleInfos() {
        return localNameRuleInfos;
    }

    public void setLocalNameRuleInfos(List<RuleInfoEntity> localNameRuleInfos) {
        this.localNameRuleInfos = localNameRuleInfos;
    }

    public List<String> getNameTypes() {
        return nameTypes;
    }

    public void setNameTypes(List<String> nameTypes) {
        this.nameTypes = nameTypes;
    }

}
