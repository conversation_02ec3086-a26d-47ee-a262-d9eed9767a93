package com.ctrip.corp.bff.hotel.book.handler.corpagghotelexpenseservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.model.CorpAggHotelExpenseServiceClient;
import com.ctrip.model.GetRoomChargePolicyListRequestType;
import com.ctrip.model.GetRoomChargePolicyListResponseType;
import org.springframework.stereotype.Component;

/**
 * @Author: chenchuang
 * @Date: 2024/8/22 16:26
 * @Description:房费政策
 */
@Component
public class HandlerOfGetRoomChargePolicyList extends AbstractHandlerOfSOA<GetRoomChargePolicyListRequestType,
        GetRoomChargePolicyListResponseType, CorpAggHotelExpenseServiceClient> {

    @Override
    protected String getMethodName() {
        return "getRoomChargePolicyList";
    }
}
