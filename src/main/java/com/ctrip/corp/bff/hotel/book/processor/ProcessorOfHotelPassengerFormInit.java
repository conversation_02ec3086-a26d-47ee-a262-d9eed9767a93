package com.ctrip.corp.bff.hotel.book.processor;

import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextRequestType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ReservationResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.utils.ResourceTokenUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PassengerFormRuleInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ResourceTokenInfo;
import com.ctrip.corp.bff.framework.template.handler.WaitFuture;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import com.ctrip.corp.bff.hotel.book.common.mapper.MapperOfQueryCheckAvailContextRequestType;
import com.ctrip.corp.bff.hotel.book.contract.HotelPassengerFormInitRequestType;
import com.ctrip.corp.bff.hotel.book.contract.HotelPassengerFormInitResponseType;
import com.ctrip.corp.bff.hotel.book.handler.corphotelroomavailableservice.HandlerOfQueryCheckAvailContext;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofhotelpassengerforminit.MapperOfHotelPassengerFormInitResponse;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfPassengerFormConfig;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: z.c. wang
 * @Description 酒店出行人表单
 * @Date: 2025/3/14 14:37
 * @Version 1.0
 */
@Component
public class ProcessorOfHotelPassengerFormInit extends AbstractProcessor<HotelPassengerFormInitRequestType, HotelPassengerFormInitResponseType> {

    @Autowired
    private MapperOfQueryCheckAvailContextRequestType mapperOfQueryCheckAvailContextRequestType;
    @Autowired
    private HandlerOfQueryCheckAvailContext handlerOfQueryCheckAvailContext;
    @Autowired
    private QconfigOfPassengerFormConfig qconfigOfPassengerFormConfig;
    @Autowired
    private MapperOfHotelPassengerFormInitResponse mapperOfHotelPassengerFormInitResponse;

    @Override
    public HotelPassengerFormInitResponseType execute(HotelPassengerFormInitRequestType request) throws Exception {
        // resourceToken
        List<ResourceTokenInfo> resourceTokenInfoList = Optional.of(request)
                .map(HotelPassengerFormInitRequestType::getPassengerFormRuleInput)
                .map(PassengerFormRuleInput::getResourceTokenInfos)
                .orElseGet(ArrayList::new);
        ResourceToken resourceToken = ResourceTokenUtil.tryParseToken(resourceTokenInfoList.stream().findFirst().map(ResourceTokenInfo::getResourceToken).orElse(null));
        // 可定反查
        WaitFuture<QueryCheckAvailContextRequestType, QueryCheckAvailContextResponseType> queryCheckAvailContextWaitFuture = null;
        String wsId = Optional.ofNullable(resourceToken).map(ResourceToken::getReservationResourceToken).map(
                ReservationResourceToken::getWsId).orElse(null);
        if (StringUtil.isNotBlank(wsId)) {
            queryCheckAvailContextWaitFuture = handlerOfQueryCheckAvailContext.handleAsync(
                    mapperOfQueryCheckAvailContextRequestType.map(Tuple2.of(request.getIntegrationSoaRequestType(), wsId)));
        }
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType = Optional.ofNullable(queryCheckAvailContextWaitFuture)
                .map(WaitFuture::get).orElse(null);

        return mapperOfHotelPassengerFormInitResponse.map(Tuple3.of(request, queryCheckAvailContextResponseType, qconfigOfPassengerFormConfig.getPassengerFormConfigEntity()));
    }

    @Override
    public Map<String, String> tracking(HotelPassengerFormInitRequestType request, HotelPassengerFormInitResponseType response) {
        return null;
    }
}
