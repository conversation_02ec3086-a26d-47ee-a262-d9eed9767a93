package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response;

import com.ctrip.corp.bff.framework.hotel.entity.contract.ConfirmDetailInfo;
import com.ctrip.corp.bff.framework.hotel.entity.contract.ConfirmInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PhoneInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2025/4/9
 */
@Component
public class MapperOfCheckMembershipPhoneResponse extends
        AbstractMapper<Tuple3<OrderCreateRequestType, WrapperOfCheckAvail.BaseCheckAvailInfo, OrderCreateToken>,
        Tuple2<Boolean, OrderCreateResponseType>> {
    /**
     * 手机号积分agg code
     */
    private static final String SJHMS_CODE = "SJHMS";

    /**
     * 手机号校验不通过弹框code
     */
    private static final String PHONE_POINT_CHECK_FAIL = "PHONE_POINT_CHECK_FAIL";
    @Override
    protected Tuple2<Boolean, OrderCreateResponseType> convert(Tuple3<OrderCreateRequestType,
            WrapperOfCheckAvail.BaseCheckAvailInfo, OrderCreateToken> para) {
        if (para == null) {
            return null;
        }
        OrderCreateRequestType orderCreateRequestType = para.getT1();
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = para.getT2();
        OrderCreateToken orderCreateToken = para.getT3();
        // 非手机号积分场景
        if (checkAvailInfo == null || !SJHMS_CODE.equalsIgnoreCase(checkAvailInfo.getPointsMode())) {
            return null;
        }
        // 无会员手机号
        if (orderCreateRequestType == null
                || orderCreateRequestType.getMembershipInfo() == null
                || !isValidPhoneInfo(orderCreateRequestType.getMembershipInfo().getPhoneInfo())) {
            return null;
        }
        PhoneInfo memberPhoneInfo = orderCreateRequestType.getMembershipInfo().getPhoneInfo();
        // 无联系人手机号
        if (orderCreateRequestType.getHotelContactorInfo() == null
                || !isValidPhoneInfo(orderCreateRequestType.getHotelContactorInfo().getPhoneInfo())) {
            return null;
        }
        PhoneInfo contactPhoneInfo = orderCreateRequestType.getHotelContactorInfo().getPhoneInfo();
        // 手机号是否一样
        if (!StringUtil.equalsIgnoreCase(memberPhoneInfo.getCountryCode(), contactPhoneInfo.getCountryCode())
                || !StringUtil.equalsIgnoreCase(memberPhoneInfo.getTransferPhoneNo(), contactPhoneInfo.getTransferPhoneNo())) {
            return Tuple2.of(true, buildOrderCreateResponse(orderCreateToken));
        }
        return null;
    }

    private OrderCreateResponseType buildOrderCreateResponse(OrderCreateToken orderCreateToken) {
        OrderCreateResponseType orderCreateResponseType = new OrderCreateResponseType();
        orderCreateResponseType.setOrderCreateToken(TokenParseUtil.generateToken(orderCreateToken, OrderCreateToken.class));
        orderCreateResponseType.setConfirmInfo(buildConfirmInfo());
        return orderCreateResponseType;
    }

    protected ConfirmInfo buildConfirmInfo() {
        ConfirmDetailInfo confirmDetailInfo = new ConfirmDetailInfo();
        confirmDetailInfo.setCode(PHONE_POINT_CHECK_FAIL);
        ConfirmInfo confirmInfo = new ConfirmInfo();
        confirmInfo.setConfirmDetailInfos(Arrays.asList(confirmDetailInfo));
        return confirmInfo;
    }

    protected boolean isValidPhoneInfo(PhoneInfo phoneInfo) {
        if (phoneInfo == null) {
            return false;
        }
        if (StringUtil.isBlank(phoneInfo.getCountryCode())) {
            return false;
        }
        if (StringUtil.isBlank(phoneInfo.getTransferPhoneNo())) {
            return false;
        }
        return true;
    }


    @Override
    protected ParamCheckResult check(Tuple3<OrderCreateRequestType, WrapperOfCheckAvail.BaseCheckAvailInfo,
            OrderCreateToken> orderCreateRequestTypeBaseCheckAvailInfoTuple2) {
        return null;
    }
}
