package com.ctrip.corp.bff.hotel.book.handler.corphotelorderdetailservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.CorpHotelOrderDetailServiceClient;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderDetailRequestType;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderDetailResponseType;
import org.springframework.stereotype.Component;

/**
 * @Author: rtlv
 * @Date: 2025/6/26 16:12
 */
@Component
public class HandlerOfGetCorpHotelOrderDetail extends AbstractHandlerOfSOA<OrderDetailRequestType, OrderDetailResponseType, CorpHotelOrderDetailServiceClient> {
    @Override
    protected String getMethodName() {
        return "getCorpHotelOrderDetail";
    }
}
