package com.ctrip.corp.bff.hotel.book.handler.corp4jservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.CorpAccountQueryService.CorpAccountQueryServiceClient;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoRequestType;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType;
import corp.user.service.corp4jservice.Corp4jServiceClient;
import corp.user.service.corp4jservice.GetReasoncodesRequestType;
import corp.user.service.corp4jservice.GetReasoncodesResponseType;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/8/28
 */
@Component
public class HandlerOfGetReasoncodes extends AbstractHandlerOfSOA<GetReasoncodesRequestType, GetReasoncodesResponseType, Corp4jServiceClient> {

    @Override
    protected String getMethodName() {
        return "getReasoncodes";
    }

    @Override
    protected String getLogErrorCode(GetReasoncodesResponseType responseType) {
        return String.valueOf(Optional.ofNullable(responseType)
                .map(GetReasoncodesResponseType::getRetCode).orElse(0));
    }
}
