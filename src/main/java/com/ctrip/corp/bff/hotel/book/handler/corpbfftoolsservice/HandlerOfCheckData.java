package com.ctrip.corp.bff.hotel.book.handler.corpbfftoolsservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.bff.tools.contract.CheckDataRequestType;
import com.ctrip.corp.bff.tools.contract.CheckDataResponseType;
import com.ctrip.corp.bff.tools.contract.CorpBffToolsServiceClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/6/23 22:31
 */
@Component
public class HandlerOfCheckData extends AbstractHandlerOfSOA<CheckDataRequestType, CheckDataResponseType, CorpBffToolsServiceClient> {
    @Override
    protected String getMethodName() {
        return "checkData";
    }
}
