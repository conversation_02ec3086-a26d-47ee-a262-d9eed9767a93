package com.ctrip.corp.bff.hotel.book.common.enums;

/**
 * <AUTHOR>
 * @description
 * @date 2024/9/4
 */
public enum OrderCreateCertificateTypeEnum {
    /**
     * 身份证
     */
    IDENTITY_CARD(1,"0"),
    /**
     * 护照
     */
    PASSPORT(2,"1"),
    /**
     * 学生证
     */
    STUDENT_ID_CARD(3,"2"),
    /**
     * 军人证
     */
    MILITARY_CARD(4,"3"),
    /**
     * 驾驶证
     */
    DRIVING_LICENSE(6,"4"),
    /**
     * 回乡证
     */
    HOMEPERMIT(7,"5"),
    /**
     * 台胞证
     */
    MTP(8,"6"),
    /**
     * 其它
     */
    OTHERDOCUMENT(99,"99"),
    /**
     * 港澳通行证
     */
    HKMACPASS(10,"7"),
    /**
     * 国际海员证
     */
    SEAMAN_CARD(11,"8"),
    /**
     * 外国人永久居留证
     */
    FOREIGNER_PERMANENT_RESIDENCE_CARD(20,"9"),
    /**
     * 台湾通行证
     */
    TAIWANPASS(22,"11"),
    /**
     * 旅行证
     */
    TRAVELDOCUMENT(21, "10"),
    /**
     * 外国人永久居留身份证
     */
    FOREIGNER_PERMANENT_RESIDENT_ID_CARD(28, "99"),
    /**
     * 港澳台居民居住证
     */
    RESIDENCEPERMITHKT(32, "99"),

    /**
     * 海外当地旅行证件
     */
    OVERSEA_AND_LOCAL_TRAVEL_CARD(31, "99"),

    /**
     * 俄籍国内护照
     */
    RUSSIA_DOMESTIC_PASSPORT(30, "99");

    private final Integer idType;
    private final String insuranceCreateOrder;

    public String getInsuranceCreateOrder() {
        return insuranceCreateOrder;
    }

    public static String insuranceTypeCreateOrder(OrderCreateCertificateTypeEnum orderCreateCertificateTypeEnum) {
        if (orderCreateCertificateTypeEnum == null) {
            return OTHERDOCUMENT.insuranceCreateOrder;
        }
        return orderCreateCertificateTypeEnum.getInsuranceCreateOrder();
    }

    public static OrderCreateCertificateTypeEnum findByCertificateType(String type) {
        for (OrderCreateCertificateTypeEnum orderCreateCertificateTypeEnum : OrderCreateCertificateTypeEnum.values()) {
            if (orderCreateCertificateTypeEnum.toString().equals(type)) {
                return orderCreateCertificateTypeEnum;
            }
        }
        return OTHERDOCUMENT;
    }
    public static OrderCreateCertificateTypeEnum findByCertificateTypeById(Integer idType) {
        for (OrderCreateCertificateTypeEnum orderCreateCertificateTypeEnum : OrderCreateCertificateTypeEnum.values()) {
            if (orderCreateCertificateTypeEnum.getIdType().equals(idType)) {
                return orderCreateCertificateTypeEnum;
            }
        }
        return null;
    }


    OrderCreateCertificateTypeEnum(Integer idType, String insuranceCreateOrder) {
        this.idType = idType;
        this.insuranceCreateOrder = insuranceCreateOrder;
    }

    public Integer getIdType() {
        return idType;
    }
}
