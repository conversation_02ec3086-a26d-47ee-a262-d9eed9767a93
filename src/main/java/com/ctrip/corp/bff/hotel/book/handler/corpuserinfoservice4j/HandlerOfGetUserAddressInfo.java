package com.ctrip.corp.bff.hotel.book.handler.corpuserinfoservice4j;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.corpUserInfoService.CorpUserInfoService4jClient;
import corp.user.service.corpUserInfoService.GetUserAddressInfoRequestType;
import corp.user.service.corpUserInfoService.GetUserAddressInfoResponseType;
import org.springframework.stereotype.Component;

/**
 * @Author: chenchuang
 * @Date: 2024/8/22 16:26
 * @Description:发票地址历史列表
 */
@Component
public class HandlerOfGetUserAddressInfo extends AbstractHandlerOfSOA<GetUserAddressInfoRequestType,
        GetUserAddressInfoResponseType, CorpUserInfoService4jClient> {

    @Override
    protected String getMethodName() {
        return "getUserAddressInfo";
    }
}
