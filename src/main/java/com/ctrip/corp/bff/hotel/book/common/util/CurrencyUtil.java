package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.bff.framework.template.common.shark.currencytemplate.CurrencyDisplayUtil;
import com.ctrip.corp.bff.framework.template.common.shark.entity.CurrencyDisplayInfo;
import com.ctrip.corp.bff.framework.template.common.shark.entity.CurrencyProductLineEnum;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.foundation.common.enums.LanguageLocaleEnum;
import com.ctrip.corp.foundation.common.util.NumberDisplayUtil;
import com.ctrip.corp.foundation.translation.currency.enums.CurrencyEnum;
import com.ctrip.corp.foundation.translation.currency.util.HotelCurrencyDisplayUtil;
import com.ctrip.corp.foundation.translation.currency.util.currencystringsplit.CurrencyStringSplitInfo;
import com.ctrip.framework.foundation.Foundation;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: chenchuang
 * @Date: 2024/9/3 10:46

 */
public class CurrencyUtil {


    // 只转换币种,但是金额和币种均需要传
    public static String convertCurrency(BigDecimal amount, String currency) {
        if (amount == null || currency == null) {
            return null;
        }
        CurrencyDisplayInfo currencyDisplayInfo = new CurrencyDisplayInfo();
        currencyDisplayInfo.setNumber(amount);
        currencyDisplayInfo.setCurrency(currency);
        currencyDisplayInfo.setProductLine(CurrencyProductLineEnum.HOTEL);
        CurrencyStringSplitInfo currencyStringSplitInfo = CurrencyDisplayUtil.currencyStringSplitInfo(currencyDisplayInfo);

        return currencyStringSplitInfo.getCurrency();
    }
}
