package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.TripInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.soa._21234.CreateTripRequestType;
import com.ctrip.soa._21234.GetUserLiteInfoResponseType;
import org.springframework.stereotype.Component;

import java.util.Map;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/6/23 20:37
 */
@Component public class MapperOfCreateTripRequestType
    extends AbstractMapper<Tuple3<OrderCreateRequestType, GetUserLiteInfoResponseType, Map<String, StrategyInfo>>, CreateTripRequestType> {
    // 1:Online 2:Offline 3:App 4:Job 5:接口
    private final int APP = 3;
    private final int ONLINE = 1;
    private final int OFFLINE = 2;
    public static final String LANGUAGE_ZH_CN = "zh-CN";
    public static final String SERVER_FROM_ZH_CN = "m.ct/html5";
    public static final String SERVER_FROM_EN = "m.ct/html5/en";
    private static final String ITINERARY = "ITINERARY";

    @Override
    protected CreateTripRequestType convert(Tuple3<OrderCreateRequestType, GetUserLiteInfoResponseType, Map<String, StrategyInfo>> tuple) {
        OrderCreateRequestType orderCreateRequestType = tuple.getT1();
        Map<String, StrategyInfo> strategyInfoMap = tuple.getT3();
        CreateTripRequestType createTripRequestType = new CreateTripRequestType();
        createTripRequestType.setUid(orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId());
        createTripRequestType.setEid(orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId());
        createTripRequestType.setPolicyUid(StringUtil.isBlank(
            Optional.ofNullable(orderCreateRequestType.getHotelPolicyInput()).map(HotelPolicyInput::getPolicyInput)
                .map(PolicyInput::getPolicyUid).orElse(null)) ?
            orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId() :
            orderCreateRequestType.getHotelPolicyInput().getPolicyInput().getPolicyUid());
        createTripRequestType.setChannelType(buildChannelType(orderCreateRequestType.getIntegrationSoaRequestType()));
        createTripRequestType.setCorpTravelNo(
            Optional.ofNullable(orderCreateRequestType.getApprovalInput()).map(ApprovalInput::getMasterApprovalNo)
                .map(Long::valueOf).orElse(null));
        createTripRequestType.setLanguage(orderCreateRequestType.getIntegrationSoaRequestType().getLanguage());
        createTripRequestType.setServerFrom(
            LANGUAGE_ZH_CN.equalsIgnoreCase(orderCreateRequestType.getIntegrationSoaRequestType().getLanguage()) ?
                SERVER_FROM_ZH_CN : SERVER_FROM_EN);
        if (StrategyOfBookingInitUtil.tripNeedSubProductLine(strategyInfoMap)) {
            createTripRequestType.setSubProductLine(ITINERARY);
        }
        createTripRequestType.setTripName(Optional.ofNullable(orderCreateRequestType.getTripInput())
                .map(TripInput::getTripName).orElse(null));
        return createTripRequestType;
    }

    @Override protected ParamCheckResult check(Tuple3<OrderCreateRequestType, GetUserLiteInfoResponseType, Map<String, StrategyInfo>> tuple) {
        GetUserLiteInfoResponseType userLiteInfo = tuple.getT2();
        if (Objects.nonNull(userLiteInfo) && userLiteInfo.getUncommittedTrip() >= userLiteInfo.getUncommittedLimit()) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.UN_SUBMIT_TRIP_EXCEED);
        }
        return null;
    }

    protected int buildChannelType(IntegrationSoaRequestType integrationSoaRequestType) {
        if (integrationSoaRequestType.getSourceFrom() == null) {
            return APP;
        }
        switch (integrationSoaRequestType.getSourceFrom()) {
            case SourceFrom.Offline:
                return OFFLINE;
            case SourceFrom.Online:
                return ONLINE;
            default:
                return APP;
        }
    }
}
