package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount.AccountInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.pub.endorsement.contract.synctrip.SyncTripRequestType;
import com.ctrip.soa._21234.CreateTripResponseType;
import com.google.common.collect.Lists;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/12/12 16:50
 */
@Component public class MapperOfSyncTripRequestType extends
    AbstractMapper<Tuple4<CreateTripResponseType, OrderCreateRequestType, WrapperOfAccount.AccountInfo, OrderCreateToken>, SyncTripRequestType> {

    @Override protected SyncTripRequestType convert(
        Tuple4<CreateTripResponseType, OrderCreateRequestType, WrapperOfAccount.AccountInfo, OrderCreateToken> tuple) {
        CreateTripResponseType createTripResponseType = tuple.getT1();
        OrderCreateRequestType orderCreateRequestType = tuple.getT2();
        WrapperOfAccount.AccountInfo accountInfo = tuple.getT3();
        OrderCreateToken orderCreateToken = tuple.getT4();
        SyncTripRequestType syncTripRequestType = new SyncTripRequestType();
        syncTripRequestType.setEndorsementId(
            TemplateNumberUtil.parseLong(orderCreateRequestType.getApprovalInput().getMasterApprovalNo()));
        // 新建行程 or 客户选择的未关联过出差申请单的行程
        syncTripRequestType.setTripIds(Lists.newArrayList(
            OrderCreateProcessorOfUtil.getTripId(accountInfo, orderCreateRequestType, createTripResponseType,
                orderCreateToken)));
        syncTripRequestType.setAppId(String.valueOf(CommonConstant.APPID));
        syncTripRequestType.setSessionId(orderCreateRequestType.getIntegrationSoaRequestType().getRequestId());
        return syncTripRequestType;
    }

    @Override protected ParamCheckResult check(
        Tuple4<CreateTripResponseType, OrderCreateRequestType, WrapperOfAccount.AccountInfo, OrderCreateToken> tuple) {
        return null;
    }
}
