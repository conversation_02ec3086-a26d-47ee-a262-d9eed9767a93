package com.ctrip.corp.bff.hotel.book.common.enums;

import com.ctrip.arch.coreinfo.enums.KeyType;

import java.util.Objects;

/**
 * @Author: chenchuang
 * @Date: 2024/9/27 21:45
 * @Description: 证件类型
 */
public enum IdCardTypeEnum {

    ID("ID", 1),
    PASSPORT("PASSPORT", 2),
    STUDENT_ID_CARD("STUDENT_ID_CARD", 3),
    MILITARY_CARD("MILITARY_CARD", 4),
    DRIVING_LICENSE("DRIVING_LICENSE", 6),
    HOMEPERMIT("HOMEPERMIT", 7),
    MTP("MTP", 8),
    OTHERDOCUMENT("OTHERDOCUMENT", 99),
    HKMACPASS("HKMACPASS", 10),
    SEAMAN_CARD("SEAMAN_CARD", 11),
    FOREIGNER_PERMANENT_RESIDENCE_CARD("FOREIGNER_PERMANENT_RESIDENCE_CARD", 20),
    TAIWANPASS("TAIWANPASS", 22),
    TRAVELDOCUMENT("TRAVELDOCUMENT", 21),
    FOREIGNER_PERMANENT_RESIDENT_ID_CARD("FOREIGNER_PERMANENT_RESIDENT_ID_CARD", 28),
    RESIDENCEPERMITHKT("RESIDENCEPERMITHKT", 32);

    private String sharkName;
    private Integer idCode;

    public Integer getIdCode() {
        return idCode;
    }

    public String getSharkName() {
        return sharkName;
    }

    public void setIdCode(Integer idCode) {
        this.idCode = idCode;
    }

    public static IdCardTypeEnum getIdCardTypeEnum(String certificateType) {
        CertificateTypeEnum certificateTypeEnum = CertificateTypeEnum.of(certificateType);
        if (Objects.isNull(certificateTypeEnum)) {
            return null;
        }
        switch (certificateTypeEnum) {
            case IDENTITY_CARD:
                return IdCardTypeEnum.ID;
            case PASSPORT:
                return IdCardTypeEnum.PASSPORT;
            case TAIWANESE_CERTIFICATE:
                return IdCardTypeEnum.MTP;
            case TAIWAN_PASS:
                return IdCardTypeEnum.TAIWANPASS;
            case HK_AND_MACAU_PASS:
                return IdCardTypeEnum.HKMACPASS;
            case HOMETOWN_PERMIT:
                return IdCardTypeEnum.HOMEPERMIT;
            default:
                return null;
        }
    }

    IdCardTypeEnum(String sharkName, Integer idCode) {
        this.sharkName = sharkName;
        this.idCode = idCode;
    }


}
