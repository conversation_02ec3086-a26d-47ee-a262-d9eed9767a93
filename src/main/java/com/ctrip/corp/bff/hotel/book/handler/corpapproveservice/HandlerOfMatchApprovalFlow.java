package com.ctrip.corp.bff.hotel.book.handler.corpapproveservice;

import com.ctrip.corp.approve.ws.contract.CorpApproveServiceClient;
import com.ctrip.corp.approve.ws.contract.MatchApprovalFlowRequestType;
import com.ctrip.corp.approve.ws.contract.MatchApprovalFlowResponseType;
import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.soa._20184.QueryHotelAuthExtensionResponseType;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Description 匹配审批流
 * @Date 2024/8/12 21:10
 * @Version 1.0
 */
@Component
public class HandlerOfMatchApprovalFlow extends AbstractHandlerOfSOA<MatchApprovalFlowRequestType, MatchApprovalFlowResponseType, CorpApproveServiceClient> {

    @Override
    protected String getMethodName() {
        return "matchApprovalFlow";
    }

}
