package com.ctrip.corp.bff.hotel.book.handler.corpagghoteltmcservice;

import com.ctrip.corp.agg.hotel.tmc.CorpAggHotelTMCServiceClient;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyRequestType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType;
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextRequestType;
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextResponseType;
import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Description 差标政策上下文查询
 * @Date 2024/8/12 13:32
 * @Version 1.0
 */
@Component
public class HandlerOfGetTravelPolicyContext extends
        AbstractHandlerOfSOA<GetTravelPolicyContextRequestType
                , GetTravelPolicyContextResponseType, CorpAggHotelTMCServiceClient> {
    @Override
    protected String getMethodName() {
        return "getTravelPolicyContext";
    }

    @Override
    protected String getLogErrorCode(GetTravelPolicyContextResponseType response) {
        return Optional.ofNullable(response).map(GetTravelPolicyContextResponseType::getResponseCode)
                .orElse(0).toString();
    }
}
