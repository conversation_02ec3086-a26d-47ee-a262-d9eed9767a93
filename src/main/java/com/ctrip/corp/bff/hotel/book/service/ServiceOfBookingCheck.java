
package com.ctrip.corp.bff.hotel.book.service;

import com.ctrip.corp.bff.framework.hotel.common.util.ParamCheckUtil;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.processor.Processor;
import com.ctrip.corp.bff.framework.template.service.AbstractSyncService;
import com.ctrip.corp.bff.framework.template.service.generate.WebService;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.processor.ProcessorOfBookingCheck;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * <AUTHOR>
 * @description 预定校验
 * @date 2024/11/07
 */
@WebService(name = "BookingCheck")
public class ServiceOfBookingCheck extends AbstractSyncService<BookingCheckRequestType, BookingCheckResponseType> {

    @Autowired
    private ProcessorOfBookingCheck processorOfBookingCheck;

    @Override
    public void validateRequest(BookingCheckRequestType bookingCheckRequestType) throws BusinessException {
        // 校验-基本参数缺失 城市信息为空
        ParamCheckUtil.validCity(bookingCheckRequestType.getCityInput());
        // 校验-基本参数缺失 HotelBookInput预订信息节点未传入
        if (bookingCheckRequestType.getHotelBookInput() == null) {
            throw BusinessExceptionBuilder.createAlertException(OrderCreateErrorEnum.PARAM_VALID_HOTEL_BOOK_INPUT_NULL,
                OrderCreateErrorEnum.PARAM_VALID_HOTEL_BOOK_INPUT_NULL.getErrorCode().toString());
        }
        // 校验-入离店时间
        ParamCheckUtil.validDateRange(bookingCheckRequestType.getHotelBookInput().getHotelDateRangeInfo(),
            bookingCheckRequestType.getIntegrationSoaRequestType(), bookingCheckRequestType.getCityInput());
    }

    @Override
    protected Processor<BookingCheckRequestType, BookingCheckResponseType> getProcessor(BookingCheckRequestType bookingCheckRequestType) {
        return processorOfBookingCheck;
    }
}
