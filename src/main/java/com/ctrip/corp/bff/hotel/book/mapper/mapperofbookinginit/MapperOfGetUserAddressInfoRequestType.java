package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import corp.user.service.corpUserInfoService.GetUserAddressInfoRequestType;
import org.springframework.stereotype.Component;


/**
 * <AUTHOR>
 * @Description 用户地址信息
 * @Date 2024/8/22 10:00
 * @Version 1.0
 */
@Component
public class MapperOfGetUserAddressInfoRequestType extends AbstractMapper<Tuple1<BookingInitRequestType>, GetUserAddressInfoRequestType> {
    @Override
    protected GetUserAddressInfoRequestType convert(Tuple1<BookingInitRequestType> param) {
        IntegrationSoaRequestType integrationSoaRequestType = param.getT1().getIntegrationSoaRequestType();
        GetUserAddressInfoRequestType request = new GetUserAddressInfoRequestType();
        request.setUid(RequestHeaderUtil.getUserId(integrationSoaRequestType));
        final Integer PAGE_SIZE_20 = 20;
        request.setPageSize(PAGE_SIZE_20);
        request.setPageNum(1);
        request.setBizType("Hotel");
        return request;
    }

    @Override
    protected ParamCheckResult check(Tuple1<BookingInitRequestType> param) {
        return null;
    }
}
