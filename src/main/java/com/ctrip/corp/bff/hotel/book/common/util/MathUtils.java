package com.ctrip.corp.bff.hotel.book.common.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;
import java.util.Optional;

/**
 * @Author: chenchuang
 * @Date: 2024/9/3 15:33

 */
public class MathUtils {

    public MathUtils() {
    }

    public static boolean isLessOrEqualsZero(Number num) {
        return !isGreaterThanZero(num);
    }

    public static boolean isGreaterThanZero(Number num) {
        return isGreaterThan(num, 0);
    }

    public static boolean isGreaterThan(Number num1, Number num2) {
        if (num1 != null && num2 != null) {
            BigDecimal decimalNum1 = convertToBigDecimal(num1);
            BigDecimal decimalNum2 = convertToBigDecimal(num2);
            return decimalNum1.compareTo(decimalNum2) > 0;
        } else {
            return false;
        }
    }

    private static BigDecimal convertToBigDecimal(Number num) {
        if (num == null) {
            return null;
        } else if (num instanceof BigDecimal) {
            return (BigDecimal)num;
        } else if (num instanceof Integer) {
            return new BigDecimal((Integer)num);
        } else if (num instanceof Long) {
            return new BigDecimal((Long)num);
        } else if (num instanceof Short) {
            return new BigDecimal((Short)num);
        } else if (num instanceof Double) {
            return BigDecimal.valueOf((Double)num);
        } else {
            return num instanceof Float ? BigDecimal.valueOf((double)(Float)num) : null;
        }
    }

    public static BigDecimal divide(Number num1, Number num2) {
        Objects.requireNonNull(num1);
        Objects.requireNonNull(num2);
        BigDecimal decimalNum1 = convertToBigDecimal(num1);
        BigDecimal decimalNum2 = convertToBigDecimal(num2);
        return decimalNum1.divide(decimalNum2, 10, RoundingMode.HALF_EVEN);
    }

    public static BigDecimal add(Number num1, Number num2) {
        Objects.requireNonNull(num1);
        Objects.requireNonNull(num2);
        BigDecimal decimalNum1 = convertToBigDecimal(num1);
        BigDecimal decimalNum2 = convertToBigDecimal(num2);
        return decimalNum1.add(decimalNum2);
    }

}
