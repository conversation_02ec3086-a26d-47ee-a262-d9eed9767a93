package com.ctrip.corp.bff.hotel.book.handler.orderpaymentcenterbillservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.order.paymentcenter.bill.contract.OrderPaymentCenterBillServiceClient;
import com.ctrip.corp.order.paymentcenter.bill.contract.QueryPaymentBillConfigRequestType;
import com.ctrip.corp.order.paymentcenter.bill.contract.QueryPaymentBillConfigResponseType;
import com.ctrip.soa._21685.OrderPaymentCenterTransactionServiceClient;
import com.ctrip.soa._21685.PayConfigRequestType;
import com.ctrip.soa._21685.PayConfigResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 查询支付商戶信息
 * @Date 2024/8/20 17:24
 * @Version 1.0
 */
@Component
public class HandlerOfQueryPaymentBillConfig extends
        AbstractHandlerOfSOA<QueryPaymentBillConfigRequestType, QueryPaymentBillConfigResponseType
                , OrderPaymentCenterBillServiceClient> {

    @Override
    protected String getMethodName() {
        return "queryPaymentBillConfig";
    }
}
