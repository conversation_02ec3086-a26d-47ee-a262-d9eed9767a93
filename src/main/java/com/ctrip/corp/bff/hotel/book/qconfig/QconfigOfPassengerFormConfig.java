package com.ctrip.corp.bff.hotel.book.qconfig;

import com.ctrip.corp.bff.hotel.book.qconfig.entity.entityofpassengerform.PassengerFormConfigEntity;
import org.springframework.stereotype.Component;
import qunar.tc.qconfig.client.spring.QConfig;

/**
 * @Author: z.c. wang
 * @Description 酒店出行人表单配置
 * @Date: 2025/3/20 11:31
 * @Version 1.0
 */
@Component
public class QconfigOfPassengerFormConfig {

    @QConfig("passengerFormConfig.json")
    private PassengerFormConfigEntity passengerFormConfigEntity;

    public PassengerFormConfigEntity getPassengerFormConfigEntity() {
        return passengerFormConfigEntity;
    }

    public void setPassengerFormConfigEntity(PassengerFormConfigEntity passengerFormConfigEntity) {
        this.passengerFormConfigEntity = passengerFormConfigEntity;
    }
}
