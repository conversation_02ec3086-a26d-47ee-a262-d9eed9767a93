package com.ctrip.corp.bff.hotel.book.handler.corpuserinfoservice4j;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.corpUserInfoService.CorpUserInfoService4jClient;
import corp.user.service.corpUserInfoService.GetContactInvoiceDefaultInfoRequestType;
import corp.user.service.corpUserInfoService.GetContactInvoiceDefaultInfoResponseType;
import org.springframework.stereotype.Component;

/**
 * @Author: chenchuang
 * @Date: 2024/8/22 16:26
 * @Description:默认发票信息
 */
@Component
public class HandlerOfGetContactInvoiceDefaultInfo extends AbstractHandlerOfSOA<GetContactInvoiceDefaultInfoRequestType,
        GetContactInvoiceDefaultInfoResponseType, CorpUserInfoService4jClient> {

    @Override
    protected String getMethodName() {
        return "getContactInvoiceDefaultInfo";
    }
}
