package com.ctrip.corp.bff.hotel.book.handler.orderreimbursementservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.order.reimbursement.OrderReimbursementServiceClient;
import com.ctrip.order.reimbursement.ReimbursementQueryRequestType;
import com.ctrip.order.reimbursement.ReimbursementQueryResponseType;
import org.springframework.stereotype.Component;

/**
 * @Author: chenchuang
 * @Date: 2024/8/22 16:26
 * @Description:获取订单发票信息输出给前端
 */
@Component
public class HandlerOfReimbursementQuery extends AbstractHandlerOfSOA<ReimbursementQueryRequestType,
        ReimbursementQueryResponseType, OrderReimbursementServiceClient> {

    @Override
    protected String getMethodName() {
        return "reimbursementQuery";
    }
}
