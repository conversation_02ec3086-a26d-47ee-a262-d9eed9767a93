package com.ctrip.corp.bff.hotel.book.handler.preapprovalservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.PreApprovalServiceClient;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.SearchApprovalRequestType;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.SearchApprovalResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 查询审批单接口
 * @Date 2024/4/8 19:54
 * @Version 1.0
 */
@Component
public class HandlerOfSearchApproval extends AbstractHandlerOfSOA<SearchApprovalRequestType, SearchApprovalResponseType, PreApprovalServiceClient> {

    @Override
    protected String getMethodName() {
        return "searchApproval";
    }
}
