package com.ctrip.corp.bff.hotel.book.common.wrapper;

import com.corp.order.service.orderfoundationcentercostinfoservice.SaveOrderCostCenterResponseType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType;
import com.ctrip.corp.approve.ws.contract.MatchApprovalFlowResponseType;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount.AccountInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.qconfig.QConfigOfCodeMappingConfig;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryResponseType;
import com.ctrip.corp.bff.specific.contract.ApprovalFlowComputeResponseType;
import com.ctrip.corp.bff.specific.contract.ApprovalTextInfoResponseType;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType;
import com.ctrip.soa._21234.CreateTripResponseType;
import com.ctrip.soa._21234.SearchTripDetailResponseType;
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/10/11 15:23
 */
public class WrapperOfSaveCommonData {
    private CreateOrderResponseType createOrderResponseType;
    private QueryCheckAvailContextResponseType queryCheckAvailContextResponseType;
    private CheckTravelPolicyResponseType checkTravelPolicyResponseType;
    private CreateTripResponseType createTripResponseType;
    // 政策执行人
    private GetCorpUserInfoResponseType getCorpUserInfoResponseTypePolicy;
    private SSOInfoQueryResponseType ssoInfoQueryResponseType;
    private OrderCreateRequestType orderCreateRequestType;
    private MatchApprovalFlowResponseType matchApprovalFlowResponseType;
    private WrapperOfAccount.AccountInfo accountInfo;
    private ApprovalFlowComputeResponseType approvalFlowComputeResponseType;
    private ResourceToken resourceToken;
    private SearchTripDetailResponseType searchTripDetailResponseType;
    private OrderCreateToken orderCreateToken;
    private WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo;
    private ApprovalTextInfoResponseType approvalTextInfoResponseType;
    private QConfigOfCodeMappingConfig qConfigOfCodeMappingConfig;

    // 延用行程详情
    private SearchTripDetailResponseType searchTripDetailResponseTypeFolow;
    private SaveOrderCostCenterResponseType saveOrderCostCenterResponseType;

    private boolean saveCommonDataCostCenter;

    private QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig;

    public QconfigOfCertificateInitConfig getQconfigOfCertificateInitConfig() {
        return qconfigOfCertificateInitConfig;
    }
    private Map<String, StrategyInfo> strategyInfoMap;

    public Map<String, StrategyInfo> getStrategyInfoMap() {
        return strategyInfoMap;
    }
    public boolean isSaveCommonDataCostCenter() {
        return saveCommonDataCostCenter;
    }

    public SaveOrderCostCenterResponseType getSaveOrderCostCenterResponseType() {
        return saveOrderCostCenterResponseType;
    }

    public SearchTripDetailResponseType getSearchTripDetailResponseTypeFolow() {
        return searchTripDetailResponseTypeFolow;
    }

    public QConfigOfCodeMappingConfig getQConfigOfCodeMappingConfig() {
        return qConfigOfCodeMappingConfig;
    }

    public ApprovalTextInfoResponseType getApprovalTextInfoResponseType() {
        return approvalTextInfoResponseType;
    }

    public CreateOrderResponseType getCreateOrderResponseType() {
        return createOrderResponseType;
    }

    public QueryCheckAvailContextResponseType getQueryCheckAvailContextResponseType() {
        return queryCheckAvailContextResponseType;
    }

    public CheckTravelPolicyResponseType getCheckTravelPolicyResponseType() {
        return checkTravelPolicyResponseType;
    }

    public CreateTripResponseType getCreateTripResponseType() {
        return createTripResponseType;
    }

    public GetCorpUserInfoResponseType getGetCorpUserInfoResponseTypePolicy() {
        return getCorpUserInfoResponseTypePolicy;
    }

    public SSOInfoQueryResponseType getSsoInfoQueryResponseType() {
        return ssoInfoQueryResponseType;
    }

    public OrderCreateRequestType getOrderCreateRequestType() {
        return orderCreateRequestType;
    }

    public MatchApprovalFlowResponseType getMatchApprovalFlowResponseType() {
        return matchApprovalFlowResponseType;
    }

    public AccountInfo getAccountInfo() {
        return accountInfo;
    }

    public ApprovalFlowComputeResponseType getApprovalFlowComputeResponseType() {
        return approvalFlowComputeResponseType;
    }

    public ResourceToken getResourceToken() {
        return resourceToken;
    }

    public SearchTripDetailResponseType getSearchTripDetailResponseType() {
        return searchTripDetailResponseType;
    }

    public OrderCreateToken getOrderCreateToken() {
        return orderCreateToken;
    }

    public WrapperOfCheckAvail.CheckAvailContextInfo getCheckAvailContextInfo() {
        return checkAvailContextInfo;
    }
    public static WrapperOfSaveCommonData.Builder builder() {
        return new WrapperOfSaveCommonData.Builder();
    }
    public static class Builder {
        private CreateOrderResponseType createOrderResponseType;
        private QueryCheckAvailContextResponseType queryCheckAvailContextResponseType;
        private CheckTravelPolicyResponseType checkTravelPolicyResponseType;
        private CreateTripResponseType createTripResponseType;
        private GetCorpUserInfoResponseType getCorpUserInfoResponseTypePolicy;
        private SSOInfoQueryResponseType ssoInfoQueryResponseType;
        private OrderCreateRequestType orderCreateRequestType;
        private MatchApprovalFlowResponseType matchApprovalFlowResponseType;
        private WrapperOfAccount.AccountInfo accountInfo;
        private ApprovalFlowComputeResponseType approvalFlowComputeResponseType;
        private ResourceToken resourceToken;
        private SearchTripDetailResponseType searchTripDetailResponseType;
        private OrderCreateToken orderCreateToken;
        private WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo;
        private ApprovalTextInfoResponseType approvalTextInfoResponseType;
        private QConfigOfCodeMappingConfig qConfigOfCodeMappingConfig;
        private SaveOrderCostCenterResponseType saveOrderCostCenterResponseType;

        // 延用行程详情
        private SearchTripDetailResponseType searchTripDetailResponseTypeFolow;

        private boolean saveCommonDataCostCenter;

        private QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig;
        private Map<String, StrategyInfo> strategyInfoMap;

        public Builder setStrategyInfoMap(Map<String, StrategyInfo> strategyInfoMap) {
            this.strategyInfoMap = strategyInfoMap;
            return this;
        }

        public Builder setSaveCommonDataCostCenter(boolean saveCommonDataCostCenter) {
            this.saveCommonDataCostCenter = saveCommonDataCostCenter;
            return this;
        }


        public Builder setSaveOrderCostCenterResponseType(SaveOrderCostCenterResponseType saveOrderCostCenterResponseType) {
            this.saveOrderCostCenterResponseType = saveOrderCostCenterResponseType;
            return this;
        }

        public Builder setSearchTripDetailResponseTypeFolow(SearchTripDetailResponseType searchTripDetailResponseTypeFolow) {
            this.searchTripDetailResponseTypeFolow = searchTripDetailResponseTypeFolow;
            return this;
        }

        public Builder setQConfigOfCodeMappingConfig(QConfigOfCodeMappingConfig qConfigOfCodeMappingConfig) {
            this.qConfigOfCodeMappingConfig = qConfigOfCodeMappingConfig;
            return this;
        }

        public Builder setApprovalTextInfoResponseType(ApprovalTextInfoResponseType approvalTextInfoResponseType) {
            this.approvalTextInfoResponseType = approvalTextInfoResponseType;
            return this;
        }

        public Builder setCreateOrderResponseType(CreateOrderResponseType createOrderResponseType) {
            this.createOrderResponseType = createOrderResponseType;
            return this;
        }

        public Builder setQueryCheckAvailContextResponseType(
            QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
            this.queryCheckAvailContextResponseType = queryCheckAvailContextResponseType;
            return this;
        }

        public Builder setCheckTravelPolicyResponseType(CheckTravelPolicyResponseType checkTravelPolicyResponseType) {
            this.checkTravelPolicyResponseType = checkTravelPolicyResponseType;
            return this;
        }

        public Builder setCreateTripResponseType(CreateTripResponseType createTripResponseType) {
            this.createTripResponseType = createTripResponseType;
            return this;
        }

        public Builder setGetCorpUserInfoResponseTypePolicy(GetCorpUserInfoResponseType getCorpUserInfoResponseTypePolicy) {
            this.getCorpUserInfoResponseTypePolicy = getCorpUserInfoResponseTypePolicy;
            return this;
        }

        public Builder setSsoInfoQueryResponseType(SSOInfoQueryResponseType ssoInfoQueryResponseType) {
            this.ssoInfoQueryResponseType = ssoInfoQueryResponseType;
            return this;
        }

        public Builder setOrderCreateRequestType(OrderCreateRequestType orderCreateRequestType) {
            this.orderCreateRequestType = orderCreateRequestType;
            return this;
        }

        public Builder setMatchApprovalFlowResponseType(MatchApprovalFlowResponseType matchApprovalFlowResponseType) {
            this.matchApprovalFlowResponseType = matchApprovalFlowResponseType;
            return this;
        }

        public Builder setAccountInfo(WrapperOfAccount.AccountInfo accountInfo) {
            this.accountInfo = accountInfo;
            return this;
        }

        public Builder setApprovalFlowComputeResponseType(
            ApprovalFlowComputeResponseType approvalFlowComputeResponseType) {
            this.approvalFlowComputeResponseType = approvalFlowComputeResponseType;
            return this;
        }

        public Builder setResourceToken(ResourceToken resourceToken) {
            this.resourceToken = resourceToken;
            return this;
        }

        public Builder setSearchTripDetailResponseType(SearchTripDetailResponseType searchTripDetailResponseType) {
            this.searchTripDetailResponseType = searchTripDetailResponseType;
            return this;
        }

        public Builder setOrderCreateToken(OrderCreateToken orderCreateToken) {
            this.orderCreateToken = orderCreateToken;
            return this;
        }

        public Builder setCheckAvailInfo(WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo) {
            this.checkAvailContextInfo = checkAvailContextInfo;
            return this;
        }

        public Builder setQconfigOfCertificateInitConfig(QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig) {
            this.qconfigOfCertificateInitConfig = qconfigOfCertificateInitConfig;
            return this;
        }

        public WrapperOfSaveCommonData build() {
            WrapperOfSaveCommonData data = new WrapperOfSaveCommonData();
            data.createOrderResponseType = this.createOrderResponseType;
            data.queryCheckAvailContextResponseType = this.queryCheckAvailContextResponseType;
            data.checkTravelPolicyResponseType = this.checkTravelPolicyResponseType;
            data.createTripResponseType = this.createTripResponseType;
            data.getCorpUserInfoResponseTypePolicy = this.getCorpUserInfoResponseTypePolicy;
            data.ssoInfoQueryResponseType = this.ssoInfoQueryResponseType;
            data.orderCreateRequestType = this.orderCreateRequestType;
            data.matchApprovalFlowResponseType = this.matchApprovalFlowResponseType;
            data.accountInfo = this.accountInfo;
            data.approvalFlowComputeResponseType = this.approvalFlowComputeResponseType;
            data.resourceToken = this.resourceToken;
            data.searchTripDetailResponseType = this.searchTripDetailResponseType;
            data.searchTripDetailResponseTypeFolow = this.searchTripDetailResponseTypeFolow;
            data.orderCreateToken = this.orderCreateToken;
            data.checkAvailContextInfo = this.checkAvailContextInfo;
            data.approvalTextInfoResponseType = this.approvalTextInfoResponseType;
            data.qConfigOfCodeMappingConfig = this.qConfigOfCodeMappingConfig;
            data.saveOrderCostCenterResponseType = this.saveOrderCostCenterResponseType;
            data.saveCommonDataCostCenter = this.saveCommonDataCostCenter;
            data.qconfigOfCertificateInitConfig = this.qconfigOfCertificateInitConfig;

            data.strategyInfoMap = this.strategyInfoMap;
            return data;
        }
    }
}
