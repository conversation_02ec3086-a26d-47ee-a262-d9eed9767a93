package com.ctrip.corp.bff.hotel.book.handler.corpbffspecificservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.bff.specific.contract.*;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/6/26 22:31
 */
@Component
public class HandlerOfApprovalTextInfo extends AbstractHandlerOfSOA<ApprovalTextInfoRequestType, ApprovalTextInfoResponseType, CorpBffSpecificServiceClient> {

    @Override
    protected String getMethodName() {
        return "approvalTextInfo";
    }
}
