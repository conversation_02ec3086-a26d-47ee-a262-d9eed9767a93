package com.ctrip.corp.bff.hotel.book.mapper.mapperofhotelpassengerforminit;

import com.ctrip.corp.agg.hotel.roomavailable.entity.*;
import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum;
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil;
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.MapUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.*;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.passenger.PassengerCertificateTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.passenger.PassengerFormTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.AllowCountryCodeUtil;
import com.ctrip.corp.bff.hotel.book.common.util.PassengerInfoConfigUtil;
import com.ctrip.corp.bff.hotel.book.contract.HotelPassengerFormInitRequestType;
import com.ctrip.corp.bff.hotel.book.contract.HotelPassengerFormInitResponseType;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.entityofpassengerform.*;
import org.springframework.stereotype.Component;

import javax.swing.text.html.Option;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Author: z.c. wang
 * @Description 酒店出行人表单
 * @Date: 2025/3/20 10:24
 * @Version 1.0
 */
@Component
public class MapperOfHotelPassengerFormInitResponse extends
        AbstractMapper<Tuple3<HotelPassengerFormInitRequestType, QueryCheckAvailContextResponseType,
                PassengerFormConfigEntity>, HotelPassengerFormInitResponseType> {

    @Override
    protected HotelPassengerFormInitResponseType convert(Tuple3<HotelPassengerFormInitRequestType, QueryCheckAvailContextResponseType,
            PassengerFormConfigEntity> tuple) {
        HotelPassengerFormInitRequestType requestType = tuple.getT1();
        boolean employee = BooleanUtil.toBoolean(Optional.ofNullable(requestType.getPassengerFormRuleInput())
                .map(PassengerFormRuleInput::getEmployeeFlag).orElse(null));
        String nationalityCode = Optional.ofNullable(requestType.getPassengerFormRuleInput())
                .map(PassengerFormRuleInput::getNationalityCode).orElse(null);
        // 出行人表单规则qconfig配置
        PassengerFormConfigEntity passengerFormConfigEntity = tuple.getT3();
        LogUtil.loggingClogOnly(LogLevelEnum.Info, MapperOfHotelPassengerFormInitResponse.class, "passengerFormConfigEntity",
                JsonUtil.toJson(passengerFormConfigEntity), null);
        // 可订反查结果
        QueryCheckAvailContextResponseType checkAvailContextResponseType = tuple.getT2();

        HotelPassengerFormInitResponseType hotelPassengerFormInitResponseType = new HotelPassengerFormInitResponseType();
        PassengerFormRuleOutput passengerFormRuleOutput = new PassengerFormRuleOutput();
        PassengerRuleInfo passengerRuleInfo = new PassengerRuleInfo();
        // 有证件-基本信息规则
        passengerRuleInfo.setPassengerBaseRuleInfos(buildPassengerBaseRuleInfos(
                Optional.ofNullable(passengerFormConfigEntity).map(PassengerFormConfigEntity::getPassengerBaseRuleInfos).orElse(null),
                checkAvailContextResponseType, employee, true, nationalityCode));
        // 无证件-基本信息规则
        passengerRuleInfo.setNoCertificatePassengerRuleInfos(buildPassengerBaseRuleInfos(
                Optional.ofNullable(passengerFormConfigEntity).map(PassengerFormConfigEntity::getPassengerBaseRuleInfos).orElse(null),
                checkAvailContextResponseType, employee, false, nationalityCode));
        // 有证件-证件表单规则
        passengerRuleInfo.setCertificateRuleInfos(buildCertificatePassengerRuleInfos(
                Optional.ofNullable(passengerFormConfigEntity).map(PassengerFormConfigEntity::getCertificateDefaultRuleInfos).orElse(null),
                employee, nationalityCode, checkAvailContextResponseType));
        passengerFormRuleOutput.setPassengerRuleInfo(passengerRuleInfo);
        passengerFormRuleOutput.setNameRuleInfos(buildNameRuleInfos(
                Optional.ofNullable(passengerFormConfigEntity).map(PassengerFormConfigEntity::getNameFormRuleInfos).orElse(null),
                checkAvailContextResponseType, employee, nationalityCode));
        passengerFormRuleOutput.setCertificateRuleInfos(buildPassengerCertificateRuleInfos(
                checkAvailContextResponseType, nationalityCode, employee, passengerFormConfigEntity));
        hotelPassengerFormInitResponseType.setPassengerFormRuleOutput(passengerFormRuleOutput);
        return hotelPassengerFormInitResponseType;
    }

    /**
     * 支持的证件-酒店资源逻辑
     *
     * @param checkAvailContextResponseType
     * @param nationalityCode
     * @param employee
     * @param passengerFormConfigEntity
     * @return
     */
    private List<CertificateRuleInfo> buildPassengerCertificateRuleInfos(QueryCheckAvailContextResponseType checkAvailContextResponseType,
                                                                String nationalityCode,
                                                                Boolean employee,
                                                                PassengerFormConfigEntity passengerFormConfigEntity) {
        if (StringUtil.isBlank(nationalityCode)) {
            return null;
        }
        // 资源支持的证件
        List<PassengerCertificateTypeEnum> roomSupportCertificateType = Optional.ofNullable(checkAvailContextResponseType)
                .map(QueryCheckAvailContextResponseType::getBookingRules)
                .map(QueryBookingRulesType::getCertificateInfo).map(QueryCertificateType::getSupportCertificateType)
                .orElse(Collections.emptyList())
                .stream().map(PassengerCertificateTypeEnum::findByAggCertificateType).collect(Collectors.toList());
        // 国家支持的证件
        List<PassengerCertificateTypeEnum> countrySupportCertificates;
        CertificatesBasedOnCountryEntity certificatesBasedOnCountryEntity = Optional.ofNullable(passengerFormConfigEntity)
                .map(PassengerFormConfigEntity::getCertificateListBasedOnCountry).orElse(Collections.emptyList()).stream()
                .filter(x -> StringUtil.equalsIgnoreCase(x.getCountryCode(), nationalityCode)).findFirst().orElse(null);
        if (certificatesBasedOnCountryEntity != null && CollectionUtil.isNotEmpty(certificatesBasedOnCountryEntity.getCertificateList())) {
            countrySupportCertificates = certificatesBasedOnCountryEntity.getCertificateList().stream()
                    .map(x -> PassengerCertificateTypeEnum.findByCommonCertificateType(x.getCertificateType())).collect(Collectors.toList());
        } else {
            CertificatesBasedOnCountryEntity defaultCertificatesBasedOnCountryEntity = Optional.ofNullable(passengerFormConfigEntity)
                    .map(PassengerFormConfigEntity::getCertificateListBasedOnCountry).orElse(Collections.emptyList()).stream()
                    .filter(x -> StringUtil.equalsIgnoreCase(x.getCountryCode(), "default")).findFirst().orElse(null);
            countrySupportCertificates = Optional.ofNullable(defaultCertificatesBasedOnCountryEntity)
                    .map(CertificatesBasedOnCountryEntity::getCertificateList).orElse(Collections.emptyList()).stream()
                    .map(x -> PassengerCertificateTypeEnum.findByCommonCertificateType(x.getCertificateType())).collect(Collectors.toList());
        }
        List<PassengerCertificateTypeEnum> supportCertificateType = countrySupportCertificates;
        if (CollectionUtil.isNotEmpty(roomSupportCertificateType)) {
            supportCertificateType.retainAll(roomSupportCertificateType);
        }
        return convertCertificateRuleInfos(supportCertificateType, passengerFormConfigEntity, employee, nationalityCode,
                checkAvailContextResponseType);
    }

    private List<CertificateRuleInfo> convertCertificateRuleInfos(List<PassengerCertificateTypeEnum> supportCertificates,
                                                                  PassengerFormConfigEntity passengerFormConfigEntity,
                                                                  Boolean employee, String nationalityCode,
                                                                  QueryCheckAvailContextResponseType checkAvailContextResponseType) {
        List<CertificateRuleInfo> certificateRuleInfos = new ArrayList<>();
        for (PassengerCertificateTypeEnum passengerCertificateTypeEnum : supportCertificates) {
            CertificateRuleListEntity certificateRuleListEntity = Optional.ofNullable(passengerFormConfigEntity)
                    .map(PassengerFormConfigEntity::getCertificateFormRuleInfos).orElse(Collections.emptyList()).stream()
                    .filter(x -> StringUtil.equalsIgnoreCase(x.getCertificateType(), passengerCertificateTypeEnum.name()))
                    .findFirst().orElse(null);
            if (certificateRuleListEntity == null) {
                continue;
            }
            CertificateRuleInfo certificateRuleInfo = new CertificateRuleInfo();
            certificateRuleInfo.setCertificateType(certificateRuleListEntity.getCertificateType());
            certificateRuleInfo.setCertificateMaxNum(TemplateNumberUtil.parseInt(certificateRuleListEntity.getCertificateMaxNum()));
            certificateRuleInfo.setCertificateRuleInfos(convertFormRuleInfos(
                    buildCertificateRuleInfos(certificateRuleListEntity.getRuleInfoList(), employee), employee, nationalityCode,
                    checkAvailContextResponseType));
            certificateRuleInfos.add(certificateRuleInfo);
        }
        return CollectionUtil.isEmpty(certificateRuleInfos) ? null : certificateRuleInfos;
    }

    /**
     * 证件内容
     *
     * @param ruleInfoEntities
     * @param employee
     * @return
     */
    private List<RuleInfoEntity> buildCertificateRuleInfos(List<RuleInfoEntity> ruleInfoEntities, Boolean employee) {
        if (CollectionUtil.isEmpty(ruleInfoEntities)) {
            return null;
        }
        return ruleInfoEntities.stream().filter(ruleInfoEntity -> {
            if (ruleInfoEntity == null) {
                return false;
            }
            // 非员工过滤姓名
            if (BooleanUtil.isFalse(employee)) {
                if (StringUtil.equalsIgnoreCase(ruleInfoEntity.getFormType(), "FIRST_NAME_AND_MIDDLE_NAME")) {
                    return false;
                }
                if (StringUtil.equalsIgnoreCase(ruleInfoEntity.getFormType(), "LAST_NAME")) {
                    return false;
                }
            }
            return true;
        }).collect(Collectors.toList());
    }

    /**
     * 根据资源限制的姓名语种判断是否过滤证件
     *
     * @return
     */
    private boolean isDelCertificateByNameLanguage(Boolean roomNeedEnName,
                                                   PassengerCertificateTypeEnum passengerCertificateTypeEnum,
                                                   PassengerFormConfigEntity passengerFormConfigEntity) {
        NameRuleItemEntityEntity nameRuleItemEntityEntity = Optional.ofNullable(passengerFormConfigEntity)
                .map(PassengerFormConfigEntity::getNameFormRuleInfos)
                .orElse(Collections.emptyList())
                .stream().filter(x -> StringUtil.equalsIgnoreCase(x.getCertificateType(), passengerCertificateTypeEnum.name()))
                .findFirst().orElse(null);
        if (nameRuleItemEntityEntity == null || CollectionUtil.isEmpty(nameRuleItemEntityEntity.getNameTypes())) {
            return false;
        }
        if (!nameRuleItemEntityEntity.getNameTypes().contains("EN") && BooleanUtil.isTrue(roomNeedEnName)) {
            return true;
        }
        return false;
    }

    private List<NameRuleInfo> buildNameRuleInfos(List<NameRuleItemEntityEntity> nameRuleItemEntityEntities,
                                                  QueryCheckAvailContextResponseType checkAvailContextResponseType,
                                                  Boolean employee, String nationalityCode) {
        if (CollectionUtil.isEmpty(nameRuleItemEntityEntities)) {
            return null;
        }
        List<NameRuleInfo> nameRuleInfos = new ArrayList<>();
        for (NameRuleItemEntityEntity nameRuleItemEntityEntity : nameRuleItemEntityEntities) {
            if (nameRuleItemEntityEntity == null) {
                continue;
            }
            NameRuleInfo nameRuleInfo = new NameRuleInfo();
            nameRuleInfo.setCertificateType(nameRuleItemEntityEntity.getCertificateType());
            nameRuleInfo.setNameTypes(nameRuleItemEntityEntity.getNameTypes());
            nameRuleInfo.setLocalNameRuleInfos(convertFormRuleInfos(
                    nameRuleItemEntityEntity.getLocalNameRuleInfos(), employee, nationalityCode, checkAvailContextResponseType));
            nameRuleInfo.setEnNameRuleInfos(
                    convertFormRuleInfos(nameRuleItemEntityEntity.getEnNameRuleInfos(), employee, nationalityCode, checkAvailContextResponseType));
            nameRuleInfos.add(nameRuleInfo);
        }
        return nameRuleInfos;
    }

    // 员工证件表单规则
    private static final List<PassengerFormTypeEnum> EMP_CERTIFICATE_RULES = Arrays.asList(
            PassengerFormTypeEnum.NATIONALITY,
            PassengerFormTypeEnum.DOCUMENT_TYPE,
            PassengerFormTypeEnum.ID_NUMBER,
            PassengerFormTypeEnum.FIRST_NAME_AND_MIDDLE_NAME,
            PassengerFormTypeEnum.LAST_NAME,
            PassengerFormTypeEnum.ISSUED_PLACE,
            PassengerFormTypeEnum.EXPIRATION_DATE);

    // 非员工证件表单规则
    private static final List<PassengerFormTypeEnum> UN_EMP_CERTIFICATE_RULES = Arrays.asList(
            PassengerFormTypeEnum.NATIONALITY,
            PassengerFormTypeEnum.DOCUMENT_TYPE,
            PassengerFormTypeEnum.ID_NUMBER,
            PassengerFormTypeEnum.ISSUED_PLACE,
            PassengerFormTypeEnum.EXPIRATION_DATE);


    /**
     * 基础表单内容
     *
     * @param checkAvailContextResponseType
     * @param employee
     * @return
     */
    private List<PassengerFormTypeEnum> buildBaseRuleInfoItems(QueryCheckAvailContextResponseType checkAvailContextResponseType,
                                                               Boolean employee,
                                                               Boolean needCertificate) {
        List<PassengerFormTypeEnum> baseRuleInfoItems = new ArrayList<>();
        Integer hotelCountryId = Optional.ofNullable(checkAvailContextResponseType).map(QueryCheckAvailContextResponseType::getHotelInfo)
                .map(BookHotelInfoEntity::getGeographicalInfo).map(GeographicalSituationInfo::getCountryInfo).map(RegionInfo::getId).orElse(null);
        List<String> allowCountryCodeList = Optional.ofNullable(checkAvailContextResponseType).map(QueryCheckAvailContextResponseType::getBookingRules)
                .map(QueryBookingRulesType::getNationalityRestrictionInfo).map(NationalityRestrictionType::getAllowCountryCodeList).orElse(null);
        // 马来西亚资源 或 中宾内宾资源展示国籍
        if (BooleanUtil.isFalse(needCertificate)) {
            if (PassengerInfoConfigUtil.isMalaysiaCountry(hotelCountryId)
                    || AllowCountryCodeUtil.isOnlyForCnGuest(allowCountryCodeList)
                    || AllowCountryCodeUtil.isOnlyForCnGatGuest(allowCountryCodeList)) {
                baseRuleInfoItems.add(PassengerFormTypeEnum.NATIONALITY);
            }
        }
        // 员工+无证件基础模块展示姓名
        if (BooleanUtil.isTrue(employee) && BooleanUtil.isFalse(needCertificate)) {
            baseRuleInfoItems.add(PassengerFormTypeEnum.FIRST_NAME_AND_MIDDLE_NAME);
            baseRuleInfoItems.add(PassengerFormTypeEnum.LAST_NAME);
        }
        baseRuleInfoItems.add(PassengerFormTypeEnum.GENDER);
        baseRuleInfoItems.add(PassengerFormTypeEnum.WORKEMAIL);
        baseRuleInfoItems.add(PassengerFormTypeEnum.PHONE);
        baseRuleInfoItems.add(PassengerFormTypeEnum.BIRTH);
        return baseRuleInfoItems;
    }


    private List<FormRuleInfo> convertFormRuleInfos(List<RuleInfoEntity> certificateDefaultRuleInfos,
                                                    Boolean employee, String nationalityCode,
                                                    QueryCheckAvailContextResponseType checkAvailContextResponseType) {
        if (CollectionUtil.isEmpty(certificateDefaultRuleInfos)) {
            return null;
        }
        return certificateDefaultRuleInfos.stream().map(rule -> {
            FormRuleInfo formRuleInfo = new FormRuleInfo();
            formRuleInfo.setFormType(rule.getFormType());
            String regex = null;
            if (MapUtil.isNotEmpty(rule.getRegexByNationalityMap())) {
                regex = rule.getRegexByNationalityMap().get(nationalityCode);
                if (StringUtil.isBlank(regex)) {
                    regex = rule.getRegexByNationalityMap().get("default");
                }
            }
            if (StringUtil.isNotBlank(regex)) {
                formRuleInfo.setRegex(regex);
            } else {
                formRuleInfo.setRegex(rule.getRegex());
            }
            formRuleInfo.setDisabled(ruleDisabled(rule.getDisabled(), rule.getFormType(), employee));
            formRuleInfo.setRequiredFlag(ruleRequired(rule.getRequiredFlag(), rule.getFormType(), employee,
                    checkAvailContextResponseType));
            formRuleInfo.setInputLengthMax(TemplateNumberUtil.parseInt(rule.getInputLengthMax()));
            return formRuleInfo;
        }).collect(Collectors.toList());
    }

    /**
     * 必填逻辑
     *
     * @return
     */
    private String ruleRequired(String defaultRequired, String formType, Boolean employee, QueryCheckAvailContextResponseType checkAvailContextResponseType) {
        // bagashi性别必填
        Integer vendorId = Optional.ofNullable(checkAvailContextResponseType).map(QueryCheckAvailContextResponseType::getHotelInfo)
                .map(BookHotelInfoEntity::getVendorId).orElse(null);

        if (StringUtil.equalsIgnoreCase(formType, PassengerFormTypeEnum.GENDER.name())
                && TemplateNumberUtil.getValue(vendorId) == CommonConstant.VENDOR_ID_BAGASHI) {
            return "T";
        }

        return defaultRequired;
    }

    /**
     * 置灰逻辑
     *
     * @return
     */
    private String ruleDisabled(String defaultDisabled, String formType, Boolean employee) {
        // 员工置灰邮箱
        if (StringUtil.equalsIgnoreCase(formType, PassengerFormTypeEnum.WORKEMAIL.name()) && BooleanUtil.isTrue(employee)) {
            return "T";
        }

        return defaultDisabled;
    }

    private List<FormRuleInfo> buildPassengerBaseRuleInfos(List<RuleInfoEntity> certificateDefaultRuleInfos,
                                                           QueryCheckAvailContextResponseType checkAvailContextResponseType,
                                                           Boolean employee,
                                                           Boolean needCertificate,
                                                           String nationalityCode) {
        List<FormRuleInfo> passengerBaseRuleInfos = convertFormRuleInfos(certificateDefaultRuleInfos, employee, nationalityCode,
                checkAvailContextResponseType);

        List<PassengerFormTypeEnum> passengerFormTypeEnums = buildBaseRuleInfoItems(checkAvailContextResponseType, employee, needCertificate);
        return passengerBaseRuleInfos.stream().filter(
                certificateDefaultRule -> passengerFormTypeEnums.stream().anyMatch(
                        rule -> StringUtil.equalsIgnoreCase(certificateDefaultRule.getFormType(), rule.name()))).collect(Collectors.toList());

    }

    private List<FormRuleInfo> buildCertificatePassengerRuleInfos(List<RuleInfoEntity> certificateDefaultRuleInfos,
                                                                  Boolean employee, String nationalityCode,
                                                                  QueryCheckAvailContextResponseType checkAvailContextResponseType) {
        List<FormRuleInfo> certificateDefaultRules = convertFormRuleInfos(certificateDefaultRuleInfos, employee, nationalityCode,
                checkAvailContextResponseType);

        if (BooleanUtil.isTrue(employee)) {
            return certificateDefaultRules.stream().filter(
                    certificateDefaultRule -> EMP_CERTIFICATE_RULES.stream().anyMatch(
                            rule -> StringUtil.equalsIgnoreCase(certificateDefaultRule.getFormType(), rule.name()))).collect(Collectors.toList());
        } else {
            return certificateDefaultRules.stream().filter(
                    certificateDefaultRule -> UN_EMP_CERTIFICATE_RULES.stream().anyMatch(
                            rule -> StringUtil.equalsIgnoreCase(certificateDefaultRule.getFormType(), rule.name()))).collect(Collectors.toList());
        }

    }


    @Override
    protected ParamCheckResult check(Tuple3<HotelPassengerFormInitRequestType, QueryCheckAvailContextResponseType,
            PassengerFormConfigEntity> tuple) {
        return null;
    }
}
