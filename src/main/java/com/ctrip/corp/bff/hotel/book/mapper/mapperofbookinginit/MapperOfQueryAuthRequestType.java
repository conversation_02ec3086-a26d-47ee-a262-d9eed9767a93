package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import corp.user.service.authorityManage.QueryAuthRequestType;
import corp.user.service.authorityManage.QueryCenterAuthParamType;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2025/7/25 20:33
 */
@Component
public class MapperOfQueryAuthRequestType extends AbstractMapper<Tuple1<IntegrationSoaRequestType>,
    QueryAuthRequestType> {

    private static final String AUTH_CENTER = "Auth_Center";

    private static final String UID = "UID";

    private static final String TRAVEL_MANAGEMENT = "TRAVEL_MANAGEMENT";

    private static final String ROLE = "ROLE";

    private static final String ROLE_TYPE = "ADMIN";

    @Override protected QueryAuthRequestType convert(Tuple1<IntegrationSoaRequestType> integrationSoaRequestTypeTuple1) {
        QueryAuthRequestType queryAuthRequestType = new QueryAuthRequestType();
        queryAuthRequestType.setType(AUTH_CENTER);
        QueryCenterAuthParamType queryCenterAuthParamType = new QueryCenterAuthParamType();
        queryCenterAuthParamType.setUserType(UID);
        queryCenterAuthParamType.setUserValue(Optional.ofNullable(integrationSoaRequestTypeTuple1)
                .map(Tuple1::getT1)
                .map(IntegrationSoaRequestType::getUserInfo)
                .map(UserInfo::getUserId)
                .orElse(null));
        queryCenterAuthParamType.setSystem(TRAVEL_MANAGEMENT);
        queryCenterAuthParamType.setScope(ROLE);
        queryCenterAuthParamType.setRoleType(ROLE_TYPE);
        queryCenterAuthParamType.setPageIndex(1);
        queryCenterAuthParamType.setPageSize(10);
        queryAuthRequestType.setCenterAuthParam(queryCenterAuthParamType);
        return queryAuthRequestType;
    }

    @Override protected ParamCheckResult check(Tuple1<IntegrationSoaRequestType> integrationSoaRequestTypeTuple1) {
        return null;
    }
}
