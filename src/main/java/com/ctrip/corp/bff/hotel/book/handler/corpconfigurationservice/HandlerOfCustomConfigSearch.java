package com.ctrip.corp.bff.hotel.book.handler.corpconfigurationservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.corpsz.configuration.common.contract.CorpConfigurationServiceClient;
import com.ctrip.corp.corpsz.configuration.common.contract.CustomConfigSearchRequestType;
import com.ctrip.corp.corpsz.configuration.common.contract.CustomConfigSearchResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/05/29
 */
@Component public class HandlerOfCustomConfigSearch extends
    AbstractHandlerOfSOA<CustomConfigSearchRequestType, CustomConfigSearchResponseType, CorpConfigurationServiceClient> {

    @Override protected String getMethodName() {
        return "customConfigSearch";
    }
}
