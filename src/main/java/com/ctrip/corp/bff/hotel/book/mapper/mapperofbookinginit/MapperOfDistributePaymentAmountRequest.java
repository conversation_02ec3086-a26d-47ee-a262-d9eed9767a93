package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.agg.hotel.expense.contract.model.BaseChargeAmount;
import com.ctrip.corp.agg.hotel.expense.contract.model.ChargeAmountInfoType;
import com.ctrip.corp.agg.hotel.expense.contract.model.ServiceChargePriceType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.AddPriceRuleType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookingRulesType;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.HotelDateRangeUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfHotelTravelPolicy;
import com.ctrip.corp.bff.framework.hotel.entity.contract.*;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.BffCorpXProductForEnum;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.CorpXProductInfoToken;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple7;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.SoaErrorSharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.BookingInitErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelGuaranteeTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.*;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.foundation.common.util.Null;
import com.ctrip.corp.foundation.common.util.NumberUtil;
import com.ctrip.corp.hotelbook.commonws.entity.*;
import com.ctrip.model.CalculateServiceChargeV2ResponseType;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import static com.ctrip.corp.bff.hotel.book.common.util.BookingInitUtil.buildSelectGuaranteeType;
import static com.ctrip.corp.foundation.common.constant.StringConstants.T;

/**
 * <AUTHOR>
 * @Description agg费用详情request
 * @Date 2024/8/22 10:02
 * @Version 1.0
 */
@Component
public class MapperOfDistributePaymentAmountRequest extends MapperOfBookingInitCommonInfo<Tuple7<BookingInitRequestType,
        WrapperOfCheckAvail.CheckAvailInfo,
        WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo,
        GetSupportedPaymentMethodResponseType,
        WrapperOfAccount.AccountInfo,
        CalculateServiceChargeV2ResponseType,
        HotelPayTypeEnum>,
        DistributePaymentAmountRequestType> {
    @Override
    protected DistributePaymentAmountRequestType convert(Tuple7<BookingInitRequestType,
                WrapperOfCheckAvail.CheckAvailInfo,
                WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo,
                GetSupportedPaymentMethodResponseType,
                WrapperOfAccount.AccountInfo,
                CalculateServiceChargeV2ResponseType,
                HotelPayTypeEnum> param) {
        BookingInitRequestType bookingInitRequest = param.getT1();
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = param.getT2();
        WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo getHotelTravelPolicyResponse = param.getT3();
        GetSupportedPaymentMethodResponseType getSupportedPaymentMethodResponse = param.getT4();
        WrapperOfAccount.AccountInfo accountInfo = param.getT5();
        CalculateServiceChargeV2ResponseType calculateServiceChargeV2Response = param.getT6();
        HotelPayTypeEnum selectRoomPayTypeEnum = param.getT7();
        HotelPayTypeEnum servicePayTypeEnum = BookingInitUtil.getServicePayType(bookingInitRequest.getHotelPayTypeInput(),
            selectRoomPayTypeEnum,
            getSupportedPaymentMethodResponse, null,
            accountInfo, calculateServiceChargeV2Response);
        HotelPayTypeEnum roomPayTypeEnum =
                BookingInitUtil.getRoomPayType(selectRoomPayTypeEnum, getSupportedPaymentMethodResponse, null,
                        accountInfo);

        ChargeAmountInfoType infoType = BookingInitUtil.getChargeAmountInfoType(calculateServiceChargeV2Response
                , servicePayTypeEnum
                , roomPayTypeEnum);
        HotelGuaranteeTypeEnum selectGuaranteeType = BookingInitUtil.buildSelectGuaranteeType(bookingInitRequest.getHotelPayTypeInput());
        HotelGuaranteeTypeEnum defaultGuaranteeType = BookingInitUtil.buildDefaultGuaranteeType(
                Null.or(getSupportedPaymentMethodResponse, GetSupportedPaymentMethodResponseType::getGuaranteeMethodList), selectGuaranteeType);
        DistributePaymentAmountRequestType request = new DistributePaymentAmountRequestType();
        request.setRequestBaseInfo(getRequestBaseInfoType(bookingInitRequest));
        request.setScene(FILL_ORDER);
        request.setCorpPayType(CorpPayInfoUtil.isPublic(bookingInitRequest.getCorpPayInfo()) ? "C" : "P");
        request.setPolicyToken(Optional.ofNullable(getHotelTravelPolicyResponse)
                .map(WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo::getPolicyToken).orElse(null));
        request.setCheckAvailId(checkAvailInfo.getWsId());

        request.setPaymentMethod(CorpHotelBookCommonWSUtil.buildPaymentMethod(roomPayTypeEnum));
        request.setServiceChargePaymentMethod(CorpHotelBookCommonWSUtil.buildPaymentMethod(servicePayTypeEnum));
        request.setGuaranteeMethod(HotelGuaranteeTypeEnum.buildGuaranteeMethod(defaultGuaranteeType));
        // NOTE:蓝色空间加价逻辑，蓝色空间接入时处理
        request.setAddPriceAmount(buildAddPriceAmount(checkAvailInfo.getCustomCurrency(), bookingInitRequest.getAddPriceInput(),
                Optional.ofNullable(checkAvailInfo.getBookingRules()).map(BookingRulesType::getAddPriceRule).map(AddPriceRuleType::isCanAddPrice).orElse(false),
                bookingInitRequest.getHotelBookInput()));
        request.setCouponAmount(convertPriceType(getCouponAmount(checkAvailInfo)));
        BigDecimal originAmount = Optional.ofNullable(infoType).map(x -> x.getChargeAmountPack())
                .map(BaseChargeAmount::getChargeAmountOriginalCurrency).map(ServiceChargePriceType::getAmount).orElse(new BigDecimal(0));
        BigDecimal customAmount = Optional.ofNullable(infoType).map(x -> x.getChargeAmountPack())
                .map(BaseChargeAmount::getChargeAmountCustomCurrency).map(ServiceChargePriceType::getAmount).orElse(new BigDecimal(0));
        if (MathUtils.isGreaterThanZero(customAmount)) {
            request.setCustomServiceCharge(new PriceType(customAmount, accountInfo.getCurrency()));
        }
        if (MathUtils.isGreaterThanZero(originAmount)) {
            request.setOriginServiceCharge(new PriceType(originAmount, checkAvailInfo.getOriginCurrency()));
        }
        buildChargeAmount(request, infoType, accountInfo.getCurrency(), checkAvailInfo.getOriginCurrency());
        request.setCorpXProductList(buildCorpXProductInfoTypes(bookingInitRequest.getHotelInsuranceInput()));
        return request;
    }

    private PriceType buildAddPriceAmount(String customCurrency, AddPriceInput addPriceInput, Boolean canAddPrice, HotelBookInput hotelBookInput) {
        if (BooleanUtil.isNotTrue(canAddPrice)) {
            return null;
        }
        if (addPriceInput == null || addPriceInput.getAmountInfo() == null || StringUtil.isBlank(addPriceInput.getAmountInfo().getAmount())) {
            return null;
        }
        Integer amount = NumberUtil.parseInt(addPriceInput.getAmountInfo().getAmount());
        if (amount <= 0) {
            return null;
        }
        int roomNights = HotelDateRangeUtil.getRoomNights(hotelBookInput);
        if (roomNights <= 0) {
            return null;
        }
        PriceType priceType = new PriceType();
        priceType.setPrice(BigDecimal.valueOf(amount * roomNights));
        priceType.setCurrency(customCurrency);
        return priceType;
    }

    private PriceType convertPriceType(com.ctrip.corp.agg.hotel.salestrategy.entity.PriceType priceType) {
        if (priceType == null) {
            return null;
        }
        return new PriceType(priceType.getPrice(), priceType.getCurrency());
    }

    private void buildChargeAmount(DistributePaymentAmountRequestType request, ChargeAmountInfoType infoType,
                                   String customCurrency, String originCurrency) {
        if (infoType == null) {
            return;
        }
        BigDecimal originAmount = Optional.ofNullable(infoType.getChargeAmountPack())
                .map(BaseChargeAmount::getChargeAmountOriginalCurrency).map(ServiceChargePriceType::getAmount).orElse(new BigDecimal(0));
        BigDecimal customAmount = Optional.ofNullable(infoType.getChargeAmountPack())
                .map(BaseChargeAmount::getChargeAmountCustomCurrency).map(ServiceChargePriceType::getAmount).orElse(new BigDecimal(0));
        request.setCustomServiceCharge(new PriceType(customAmount, customCurrency));
        request.setOriginServiceCharge(new PriceType(originAmount, originCurrency));
    }


    @Override
    protected ParamCheckResult check(Tuple7<BookingInitRequestType,
            WrapperOfCheckAvail.CheckAvailInfo,
            WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo,
            GetSupportedPaymentMethodResponseType,
            WrapperOfAccount.AccountInfo,
            CalculateServiceChargeV2ResponseType,
            HotelPayTypeEnum> param) {
        BookingInitRequestType bookingInitRequest = param.getT1();
        CalculateServiceChargeV2ResponseType calculateServiceChargeV2ResponseType = param.getT6();
        if (BookingInitProcessorOfUtil.needCalculateServiceChargeV2(bookingInitRequest)) {
            checkCalculateServiceChargeV2ResponseType(calculateServiceChargeV2ResponseType);
        }
        return null;
    }

    private void checkCalculateServiceChargeV2ResponseType(
        CalculateServiceChargeV2ResponseType calculateServiceChargeV2ResponseType) {
        if (calculateServiceChargeV2ResponseType != null && TemplateNumberUtil.equals(
            calculateServiceChargeV2ResponseType.getResponseCode(), CommonConstant.SUCCESS_20000)) {
            return;
        }
        Integer logErrorCode = Optional.ofNullable(calculateServiceChargeV2ResponseType)
            .map(CalculateServiceChargeV2ResponseType::getResponseCode)
            .orElse(BookingInitErrorEnum.ACTION_NAME_CALCULATE_SERVICE_CHARGE.getErrorCode());
        String friendlyMessage = SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessage(
            SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_AGG_HOTEL_EXPENSE_SERVICE_CLIENT,
            SoaErrorSharkKeyConstant.ACTION_NAME_CALCULATE_SERVICE_CHARGE, String.valueOf(logErrorCode));
        if (StringUtil.isBlank(friendlyMessage)) {
            friendlyMessage = SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessageDefault(
                SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_AGG_HOTEL_EXPENSE_SERVICE_CLIENT,
                SoaErrorSharkKeyConstant.ACTION_NAME_CALCULATE_SERVICE_CHARGE);
        }
        throw BusinessExceptionBuilder.createAlertException(
            BookingInitErrorEnum.ACTION_NAME_CALCULATE_SERVICE_CHARGE.getErrorCode(),
            Optional.ofNullable(calculateServiceChargeV2ResponseType)
                .map(CalculateServiceChargeV2ResponseType::getResponseDesc).orElse(null), friendlyMessage,
            logErrorCode.toString());
    }

    public RequestBaseInfoType getRequestBaseInfoType(BookingInitRequestType request) {
        IntegrationSoaRequestType soaRequestType = request.getIntegrationSoaRequestType();
        String policyUid = getPolicyUid(request.getPolicyInput(), request.getIntegrationSoaRequestType().getUserInfo());
        UserInfo userInfo = soaRequestType.getUserInfo();

        RequestBaseInfoType baseInfo = new RequestBaseInfoType();
        baseInfo.setTraceId(soaRequestType.getRequestId());
        com.ctrip.corp.hotelbook.commonws.entity.UserInfoType userInfoType = new UserInfoType();
        userInfoType.setUid(userInfo.getUserId());
        userInfoType.setPolicyUid(policyUid);
        userInfoType.setCorpId(userInfo.getCorpId());

        baseInfo.setUserInfo(userInfoType);
        baseInfo.setBookingChannel(CorpHotelBookCommonWSUtil.getChannel(soaRequestType.getSourceFrom()));
        baseInfo.setRequestFrom(soaRequestType.getSourceFrom().name());
        baseInfo.setLocale(soaRequestType.getLanguage());

        return baseInfo;
    }


    private HotelGuaranteeTypeEnum getGuaranteeType(GetSupportedPaymentMethodResponseType response, HotelGuaranteeTypeEnum selectedGuarantee) {
        List<HotelGuaranteeTypeEnum> guaranteeTypeEnumList = Optional.ofNullable(response).map(GetSupportedPaymentMethodResponseType::getGuaranteeMethodList)
                .orElse(Lists.newArrayList()).stream().map(m -> {
                    HotelGuaranteeTypeEnum guaranteeTypeEnum = HotelGuaranteeTypeEnum.getGuaranteeMethod(m.getGuaranteeMethod());
                    Boolean disable = StringUtil.equalsIgnoreCase(DISABLED, m.getStatus());
                    if (guaranteeTypeEnum == HotelGuaranteeTypeEnum.NONE) {
                        return null;
                    }
                    return guaranteeTypeEnum;
                }).filter(Objects::nonNull).sorted().collect(Collectors.toList());
        HotelGuaranteeTypeEnum guaranteeTypeEnum = guaranteeTypeEnumList.stream().anyMatch(m -> m == selectedGuarantee)
                ? selectedGuarantee : CollectionUtil.findFirst(guaranteeTypeEnumList, Objects::nonNull);

        return guaranteeTypeEnum;
    }

    /**
     * X产品参数组装
     *
     * @param hotelInsuranceInput
     * @return
     */
    protected List<CorpXProductInfoType> buildCorpXProductInfoTypes(HotelInsuranceInput hotelInsuranceInput) {
        if (hotelInsuranceInput == null) {
            return new ArrayList<>();
        }
        List<CorpXProductInfoType> xProductList = new ArrayList<>();
        List<HotelInsuranceDetailInput> insuranceDetailInputs = Optional.ofNullable(hotelInsuranceInput.getHotelInsuranceDetailInputs()).orElse(new ArrayList<>());
        for (HotelInsuranceDetailInput insuranceDetailInput : insuranceDetailInputs) {
            if (insuranceDetailInput == null || StringUtils.isBlank(insuranceDetailInput.getInsuranceToken())) {
                continue;
            }
            CorpXProductInfoToken xProductInfoToken = TokenParseUtil.parseToken(insuranceDetailInput.getInsuranceToken(), CorpXProductInfoToken.class);
            if (xProductInfoToken == null || xProductInfoToken.getPriceMark() == null
                    || xProductInfoToken.getBffCorpXProductForEnum() == null) {
                continue;
            }

            CorpXProductInfoType xProduct = new CorpXProductInfoType();
            xProduct.setPriceMark(xProductInfoToken.getPriceMark());
            String ownerType = "";
            BffCorpXProductForEnum bffCorpXProductForEnum =
                    xProductInfoToken.getBffCorpXProductForEnum();
            if (BffCorpXProductForEnum.ORDER == bffCorpXProductForEnum) {
                ownerType = "ORDER";
            } else if (BffCorpXProductForEnum.PASSENGER == bffCorpXProductForEnum) {
                ownerType = "PERSON";
            }
            xProduct.setOwnerType(ownerType);
            List<CorpXGuestInfoType> guestList = new ArrayList<>();
            for (HotelBookPassengerInput hotelBookPassengerInput : Optional.ofNullable(insuranceDetailInput.getInsuranceHotelBookPassengerInputs())
                    .orElse(new ArrayList<>())) {
                HotelPassengerInput hotelPassengerInput = hotelBookPassengerInput.getHotelPassengerInput();
                if (hotelPassengerInput == null) {
                    continue;
                }
                CorpXGuestInfoType guest = new CorpXGuestInfoType();
                guest.setEmployee(T.equals(hotelPassengerInput.getEmployee()));
                guest.setRoomIndex(hotelPassengerInput.getRoomIndex());
                guest.setUid(hotelPassengerInput.getUid());
                guestList.add(guest);
            }
            xProduct.setGuestList(guestList);
            xProductList.add(xProduct);
        }
        return xProductList;
    }

}
