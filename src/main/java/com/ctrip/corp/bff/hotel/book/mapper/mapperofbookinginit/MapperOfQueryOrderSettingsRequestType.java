package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.QueryOrderSettingsRequestType;
import org.apache.commons.lang.StringUtils;
import org.springframework.stereotype.Component;

/**
 * @Author: chenchuang
 * @Date: 2024/9/20 16:04

 */
@Component
public class MapperOfQueryOrderSettingsRequestType extends AbstractMapper<Tuple1<Long>, QueryOrderSettingsRequestType> {
    @Override
    protected QueryOrderSettingsRequestType convert(Tuple1<Long> param) {
        Long orderId = param.getT1();
        if (orderId == null) {
            return null;
        }
        QueryOrderSettingsRequestType queryOrderSettingsRequestType = new QueryOrderSettingsRequestType();
        queryOrderSettingsRequestType.setOrderId(Long.valueOf(orderId));
        return queryOrderSettingsRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple1<Long> param) {
        return null;
    }
}
