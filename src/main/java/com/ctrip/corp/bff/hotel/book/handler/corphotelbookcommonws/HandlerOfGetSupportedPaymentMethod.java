package com.ctrip.corp.bff.hotel.book.handler.corphotelbookcommonws;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.hotelbook.commonws.CorpHotelBookCommonWSClient;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodRequestType;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodResponseType;
import org.springframework.stereotype.Component;

/**
 * @Author: chenchuang
 * @Date: 2024/8/22 16:26
 * @Description:支付方式
 */
@Component
public class HandlerOfGetSupportedPaymentMethod extends AbstractHandlerOfSOA<GetSupportedPaymentMethodRequestType,
        GetSupportedPaymentMethodResponseType, CorpHotelBookCommonWSClient> {

    @Override
    protected String getMethodName() {
        return "getSupportedPaymentMethod";
    }
}
