package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.contract.HotelInvoiceInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import corp.user.service.corpUserInfoService.InvoiceInfoType;
import corp.user.service.corpUserInfoService.SaveContactInvoiceDefaultInfoRequestType;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/18 8:52
 */
@Component public class MapperOfSaveContactInvoiceDefaultInfoRequestType
    extends AbstractMapper<Tuple1<OrderCreateRequestType>, SaveContactInvoiceDefaultInfoRequestType> {
    public static final String HOTEL_TYPE = "H";
    private static final String HOTEL_INVOICE_TYPE_ROOM = "ROOM";

    @Override protected SaveContactInvoiceDefaultInfoRequestType convert(Tuple1<OrderCreateRequestType> tuple) {
        OrderCreateRequestType orderCreateRequestType = tuple.getT1();
        SaveContactInvoiceDefaultInfoRequestType request = new SaveContactInvoiceDefaultInfoRequestType();
        request.setUid(orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().getUserId());
        request.setDefaultType(HOTEL_TYPE);
        request.setInvoice(buildInvoiceInfo(orderCreateRequestType.getHotelInvoiceInfos()));
        return request;
    }

    @Override protected ParamCheckResult check(Tuple1<OrderCreateRequestType> tuple) {
        return null;
    }

    private InvoiceInfoType buildInvoiceInfo(List<HotelInvoiceInfo> hotelInvoiceInfos) {
        // 取房费的发票信息 process中已做过相关判空校验
        if (!OrderCreateProcessorOfUtil.requireSaveContactInvoiceDefaultInfo(hotelInvoiceInfos)) {
            return null;
        }
        HotelInvoiceInfo hotelInvoiceInfo = hotelInvoiceInfos.stream().filter(Objects::nonNull)
            .filter(hotelInvoice -> HOTEL_INVOICE_TYPE_ROOM.equals(hotelInvoice.getHotelInvoiceType()))
            .collect(Collectors.toList()).stream().findFirst().orElse(null);
        InvoiceInfoType result = new InvoiceInfoType();
        result.setIsNeedInvoice(true);
        result.setInvoiceTitle(hotelInvoiceInfo.getInvoiceInfo().getInvoiceTitle());
        result.setDeliveryMode(StringUtil.EMPTY);
        result.setPayMode(StringUtil.EMPTY);
        result.setTaxpayerNumber(hotelInvoiceInfo.getInvoiceInfo().getTaxNumber());
        if (hotelInvoiceInfo.getInvoiceInfo().getInvoiceCompanyInfo() != null) {
            result.setCompanyAddress(hotelInvoiceInfo.getInvoiceInfo().getInvoiceCompanyInfo().getCompanyAddress());
            result.setCompanyTelephone(hotelInvoiceInfo.getInvoiceInfo().getInvoiceCompanyInfo().getCompanyTel());
            result.setBankName(hotelInvoiceInfo.getInvoiceInfo().getInvoiceCompanyInfo().getCompanyBank());
            result.setBankAccount(hotelInvoiceInfo.getInvoiceInfo().getInvoiceCompanyInfo().getCompanyBankAccount());
        }
        result.setInvoiceTitleType(buildInvoiceTitleType(hotelInvoiceInfo.getInvoiceInfo().getInvoiceTitleType()));
        if (hotelInvoiceInfo.getInvoiceInfo().getEmailInfo() != null) {
            result.setInvoiceEmail(hotelInvoiceInfo.getInvoiceInfo().getEmailInfo().getTransferEmail());
        }
        return result;
    }

    private String buildInvoiceTitleType(String invoiceTitle) {
        if ("I".equalsIgnoreCase(invoiceTitle)) {
            return "PS";
        }
        if ("C".equalsIgnoreCase(invoiceTitle)) {
            return "CP";
        }
        if ("P".equalsIgnoreCase(invoiceTitle)) {
            return "NC";
        }
        return null;
    }
}
