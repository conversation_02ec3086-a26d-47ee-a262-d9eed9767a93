package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.agg.direct.hotel.member.CustomerInfo;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.TemplateHeader;
import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.EmailInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PhoneInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.RegisterInputInfo;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfRegisterConfig;
import com.ctrip.corp.foundation.common.util.Null;
import com.ctrip.corp.foundation.common.util.NumberUtil;
import com.ctrip.model.RegisterRequestType;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.Optional;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024/9/5
 */
@Component
public class MapperOfRegisterRequestType extends AbstractMapper<Tuple4<IntegrationSoaRequestType, RegisterInputInfo,
        WrapperOfCheckAvail.CheckAvailContextInfo, QconfigOfRegisterConfig>, RegisterRequestType> {
    private static final Pattern PATTERN_IS_CHINESE =
            Pattern.compile("^((?![\\u3000-\\u303F])[\\u2E80-\\uFE4F]|\\·)*(?![\\u3000-\\u303F])[\\u2E80-\\uFE4F](\\·)*$",
                    Pattern.MULTILINE);

    @Override
    protected RegisterRequestType convert(Tuple4<IntegrationSoaRequestType, RegisterInputInfo, WrapperOfCheckAvail.CheckAvailContextInfo, QconfigOfRegisterConfig> para) {
        IntegrationSoaRequestType integrationSoaRequestType = para.getT1();
        RegisterInputInfo registerInputInfo = para.getT2();
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailContextInfo = para.getT3();
        QconfigOfRegisterConfig qconfig = para.getT4();
        String userId = Optional.ofNullable(integrationSoaRequestType).map(IntegrationSoaRequestType::getUserInfo).map(UserInfo::getUserId).orElse(null);
        if (registerInputInfo == null || checkAvailContextInfo == null || StringUtils.isBlank(userId)) {
            return null;
        }
        RegisterRequestType res = new RegisterRequestType();
        res.setRequestId(integrationSoaRequestType.getRequestId());
        res.setMgrGroupId(checkAvailContextInfo.getGroupId());
        res.setCustomerInfo(buildCustomerInfo(registerInputInfo, userId));
        res.setExecuteType(qconfig.getExecutiveTypeByGroupId(checkAvailContextInfo.getGroupId()));
        return res;
    }

    protected CustomerInfo buildCustomerInfo(RegisterInputInfo registerEntryVO, String uid) {
        if (registerEntryVO == null) {
            return null;
        }
        CustomerInfo res = new CustomerInfo();
        res.setUID(uid);
        String[] localName = getLocalName(registerEntryVO.getLocalName());
        res.setSurnameCN(localName != null && localName.length > 1 ? localName[0] : null);
        res.setGivenNameCN(localName != null && localName.length > 1 ? localName[1] : null);
        String[] nameEn = getNameEN(registerEntryVO.getNameEN());
        res.setSurname(nameEn != null && nameEn.length > 1 ? nameEn[0] : null);
        res.setGivenName(nameEn != null && nameEn.length > 1 ? nameEn[1] : null);
        res.setEmail(Optional.ofNullable(registerEntryVO.getEmailInfo()).map(EmailInfo::getTransferEmail).orElse(null));
        res.setMobilePhone(Optional.ofNullable(registerEntryVO.getPhoneInfo()).map(PhoneInfo::getTransferPhoneNo).orElse(null));
        return res;
    }

    protected String[] getLocalName(String localName) {
        if (StringUtils.isNotBlank(localName) && isChinese(localName.substring(0, 1)) && localName.length() > 1) {
            return new String[]{localName.substring(0, 1), localName.substring(1)};
        }
        if (StringUtils.isNotBlank(localName) && !isChinese(localName.substring(0, 1)) && localName.split("/").length > 1) {
            return localName.split("/");
        }
        return null;
    }

    protected String[] getNameEN(String nameEN) {
        if (StringUtils.isNotBlank(nameEN)
                && !isChinese(nameEN.substring(0, 1))
                && nameEN.split("/").length > 1) {
            return nameEN.split("/");
        }
        return null;
    }

    /**
     * 判断是否都是中文字(包含中文字)
     */
    protected boolean isChinese(String str) {
        return PATTERN_IS_CHINESE.matcher(str).find();
    }

    @Override
    protected ParamCheckResult check(Tuple4<IntegrationSoaRequestType, RegisterInputInfo, WrapperOfCheckAvail.CheckAvailContextInfo, QconfigOfRegisterConfig> para) {
        return null;
    }
}
