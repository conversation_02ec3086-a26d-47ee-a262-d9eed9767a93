package com.ctrip.corp.bff.hotel.book.common.mapper;

import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextRequestType;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/6/23 18:31
 */
@Component
public class MapperOfQueryCheckAvailContextRequestType extends
        AbstractMapper<Tuple2<IntegrationSoaRequestType, String>, QueryCheckAvailContextRequestType> {

    @Override
    protected QueryCheckAvailContextRequestType convert(Tuple2<IntegrationSoaRequestType, String> tuple) {
        String wsId = tuple.getT2();
        QueryCheckAvailContextRequestType queryCheckAvailContextRequestType = new QueryCheckAvailContextRequestType();
        queryCheckAvailContextRequestType.setTransactionId(wsId);
        queryCheckAvailContextRequestType.setRequestFrom("SOA_CREATE_ORDER");
        return queryCheckAvailContextRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple2<IntegrationSoaRequestType, String> tuple) {
        String wsid = tuple.getT2();
        if (StringUtil.isBlank(wsid)) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.VALID_QUERY_CHECK_AVAIL_CONTEXT_ERROR,
                "wsid error");
        }
        return null;
    }
}
