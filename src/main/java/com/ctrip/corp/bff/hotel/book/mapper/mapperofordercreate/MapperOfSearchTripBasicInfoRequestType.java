package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.soa._21234.SearchTripBasicInfoRequestType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/6/30 14:46
 */
@Component public class MapperOfSearchTripBasicInfoRequestType
    extends AbstractMapper<Tuple2<Long, IntegrationSoaRequestType>, SearchTripBasicInfoRequestType> {

    private static final int OFFLINE = 2;
    private static final int ONLINE = 1;
    private static final int APP = 3;

    @Override protected SearchTripBasicInfoRequestType convert(Tuple2<Long, IntegrationSoaRequestType> tuple) {
        Long tripId = tuple.getT1();
        IntegrationSoaRequestType integrationSoaRequestType = tuple.getT2();
        SearchTripBasicInfoRequestType searchTripBasicInfoRequestType = new SearchTripBasicInfoRequestType();
        searchTripBasicInfoRequestType.setTripId(tripId);
        searchTripBasicInfoRequestType.setLanguage(integrationSoaRequestType.getLanguage());
        searchTripBasicInfoRequestType.setChannelType(getChannelType(integrationSoaRequestType.getSourceFrom()));
        return searchTripBasicInfoRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple2<Long, IntegrationSoaRequestType> longOrderCreateRequestTypeTuple2) {
        return null;
    }

    /**
     * 1:Online 2:Offline 3:App
     *
     * @param sourceFrom
     * @return
     */
    private int getChannelType(SourceFrom sourceFrom) {
        if (sourceFrom == null) {
            return APP;
        }
        switch (sourceFrom) {
            case Offline:
                return OFFLINE;
            case Online:
                return ONLINE;
            default:
                return APP;
        }
    }
}
