package com.ctrip.corp.bff.hotel.book.common.enums;

import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum;
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil;
import com.ctrip.corp.bff.framework.template.common.utils.ExceptionUtil;
import com.ctrip.corp.bff.hotel.book.common.util.BenefitsAvailableUtil;
import com.ctrip.ibu.platform.shark.sdk.api.Shark;

/**
 * <AUTHOR>
 * @date 2021.6.1
 */
public enum CostCenterResourceEnum {
    /**
     * 成本中心输入一级
     */
    COST_CENTER_CC_1("cost1", 1, ""),
    /**
     * 成本中心输入二级
     */
    COST_CENTER_CC_2("cost2", 2,""),
    /**
     * 成本中心输入三级
     */
    COST_CENTER_CC_3("cost3", 3, ""),
    /**
     * 成本中心输入四级
     */
    COST_CENTER_CC_4("cost4", 4, ""),
    /**
     * 成本中心输入五级
     */
    COST_CENTER_CC_5("cost5", 5,""),
    /**
     * 成本中心输入六级
     */
    COST_CENTER_CC_6("cost6", 6, ""),
    /**
     * 成本中心输入自定义1
     */
    COST_CENTER_DEFINE1("d1", 0, "d1"),
    /**
     * 成本中心输入自定义2
     */
    COST_CENTER_DEFINE2("d2", 0,"d2"),
    /**
     * 成本中心出行目的
     */
    TRAVEL_PURPOSE("TravelPurpose", 0, "TP"),
    /**
     * 成本中心项目编号
     */
    PROJECT_NUMBER("ProjectNo", 0, "PN"),
    /**
     * 成本中心关联行程号
     */
    HOTEL_TRAVEL_NUMBER("journeyNo", 0, "HT");

    // 成本中心级别（1-6)
    int value = 0;

    // 后台配置Key
    String extName = "";

    // 字段名
    String configName = "";

    CostCenterResourceEnum(String configName, int value, String extName) {
        this.configName = configName;
        this.value = value;
        this.extName = extName;
    }

    public String buildDefaultTitle() {
        try {
            return Shark.getByLocale(this.getClass().getName() + "." + name(), "zh-CN");
        } catch (Exception e) {
            LogUtil.loggingClogOnly(LogLevelEnum.Error, CostCenterResourceEnum.class, "buildDefaultTitle",
                ExceptionUtil.getFullException(e), null);
            return null;
        }
    }

    public String buildDefaultTitleEn() {
        try {
            return Shark.getByLocale(this.getClass().getName() + "." + name(), "en-US");
        } catch (Exception e) {
            LogUtil.loggingClogOnly(LogLevelEnum.Error, CostCenterResourceEnum.class, "buildDefaultTitle",
                ExceptionUtil.getFullException(e), null);
            return null;
        }
    }

    public int getValue() {
        return this.value;
    }

    public String getExtName() {
        return this.extName;
    }
}
