package com.ctrip.corp.bff.hotel.book.qconfig.entity.entityofpassengerform;

import java.util.Map;

/**
 * @Author: z.c. wang
 * @Description 酒店出行人表单配置
 * @Date: 2025/3/14 14:37
 * @Version 1.0
 */
public class RuleInfoEntity {

    // 表单类型
    private String formType;
    // 是否必填
    private String requiredFlag;
    // 是否禁用
    private String disabled;
    // 输入长度最大
    private String inputLengthMax;
    // 正则表达式
    private String regex;
    // 正则表达式 - 中国
    private String regexChina;
    // 正则表达式 - 非中国
    private String regexNotChina;
    // 提示语shark
    private String errorMsgShark;

    // 元素支持的员工类型: "T"仅支持员工; "F"仅支持非员工; "T,F"支持员工和非员工
    private String supportEmployeeType;

    private Map<String, String> regexByNationalityMap;

    public Map<String, String> getRegexByNationalityMap() {
        return regexByNationalityMap;
    }

    public void setRegexByNationalityMap(Map<String, String> regexByNationalityMap) {
        this.regexByNationalityMap = regexByNationalityMap;
    }

    public String getFormType() {
        return formType;
    }

    public void setFormType(String formType) {
        this.formType = formType;
    }

    public String getRequiredFlag() {
        return requiredFlag;
    }

    public void setRequiredFlag(String requiredFlag) {
        this.requiredFlag = requiredFlag;
    }

    public String getDisabled() {
        return disabled;
    }

    public void setDisabled(String disabled) {
        this.disabled = disabled;
    }

    public String getInputLengthMax() {
        return inputLengthMax;
    }

    public void setInputLengthMax(String inputLengthMax) {
        this.inputLengthMax = inputLengthMax;
    }

    public String getRegex() {
        return regex;
    }

    public void setRegex(String regex) {
        this.regex = regex;
    }

    public String getErrorMsgShark() {
        return errorMsgShark;
    }

    public void setErrorMsgShark(String errorMsgShark) {
        this.errorMsgShark = errorMsgShark;
    }

    public String getSupportEmployeeType() {
        return supportEmployeeType;
    }

    public void setSupportEmployeeType(String supportEmployeeType) {
        this.supportEmployeeType = supportEmployeeType;
    }

    public String getRegexChina() {
        return regexChina;
    }

    public void setRegexChina(String regexChina) {
        this.regexChina = regexChina;
    }

    public String getRegexNotChina() {
        return regexNotChina;
    }

    public void setRegexNotChina(String regexNotChina) {
        this.regexNotChina = regexNotChina;
    }
}
