package com.ctrip.corp.bff.hotel.book.handler.corpaccountqueryservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.CorpAccountQueryService.CorpAccountQueryServiceClient;
import corp.user.service.CorpAccountQueryService.GetAuthDelayRequestType;
import corp.user.service.CorpAccountQueryService.GetAuthDelayResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/8/16 19:38
 */

@Component
public class HandlerOfGetAuthDelayConfig
    extends AbstractHandlerOfSOA<GetAuthDelayRequestType, GetAuthDelayResponseType, CorpAccountQueryServiceClient> {

    @Override
    protected String getMethodName() {
        return "getAuthDelayConfig";
    }
}
