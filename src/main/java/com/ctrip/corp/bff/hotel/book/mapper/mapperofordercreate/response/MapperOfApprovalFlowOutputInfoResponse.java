package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response;

import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.entity.IntegrationResponse;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.common.constant.SoaErrorSharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.contract.ApprovalFlowOutputInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType;
import com.ctrip.corp.bff.specific.contract.ApprovalFlowComputeResponseType;
import com.ctrip.corp.foundation.common.constant.StringConstants;
import com.ctrip.corp.foundation.common.util.Null;
import com.ctrip.corp.foundation.common.util.StringUtilsExt;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Description 审批流输出信息响应
 * @Date 2024/7/15 14:20
 * @Version 1.0
 */
@Component public class MapperOfApprovalFlowOutputInfoResponse extends
    AbstractMapper<Tuple2<ApprovalFlowComputeResponseType, OrderCreateToken>, Tuple2<Boolean, OrderCreateResponseType>> {

    /**
     * approvalFlowComputeResponseType ->
     * http://contract.mobile.flight.ctripcorp.com/#/operation-detail/21554/40/approvalFlowCompute?lang=zh-CN
     *
     * @param
     * @return
     */
    @Override protected Tuple2<Boolean, OrderCreateResponseType> convert(
        Tuple2<ApprovalFlowComputeResponseType, OrderCreateToken> tuple) {
        ApprovalFlowComputeResponseType approvalFlowComputeResponseType = tuple.getT1();
        OrderCreateToken orderCreateToken = tuple.getT2();
        OrderCreateResponseType orderCreateResponseType = new OrderCreateResponseType();
        // 审批通过直接走后续流程
        Boolean isApprovalAllDone = isApprovalAllDone(approvalFlowComputeResponseType);
        if (isApprovalAllDone) {
            return Tuple2.of(false, orderCreateResponseType);
        }
        orderCreateResponseType.setApprovalFlowOutputInfo(buildApprovalFlowOutPutInfo(approvalFlowComputeResponseType));
        orderCreateToken.addContinueTypes(ContinueTypeConst.SINGLE_APPROVAL_FLOW);
        orderCreateResponseType.setOrderCreateToken(
            TokenParseUtil.generateToken(orderCreateToken, OrderCreateToken.class));
        return Tuple2.of(true, orderCreateResponseType);
    }

    @Override protected ParamCheckResult check(Tuple2<ApprovalFlowComputeResponseType, OrderCreateToken> tuple) {

        ApprovalFlowComputeResponseType approvalFlowComputeResponseType = tuple.getT1();
        if (!checkApprovalFlowComputeResponseType(approvalFlowComputeResponseType)) {
            return OrderCreateProcessorOfUtil.buildParamCheckResult(Optional.ofNullable(approvalFlowComputeResponseType)
                    .map(ApprovalFlowComputeResponseType::getIntegrationResponse).orElse(null),
                OrderCreateErrorEnum.APPROVAL_FLOW_COMPUTE, SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_BFF_SPECIFIC,
                SoaErrorSharkKeyConstant.ACTION_NAME_APPROVAL_FLOW_COMPUTE);
        }

        return null;

    }

    private ApprovalFlowOutputInfo buildApprovalFlowOutPutInfo(
        ApprovalFlowComputeResponseType approvalFlowComputeResponseType) {
        ApprovalFlowOutputInfo approvalFlowOutputVO = new ApprovalFlowOutputInfo();
        approvalFlowOutputVO.setApprovalFlowToken(Optional.ofNullable(approvalFlowComputeResponseType)
            .map(ApprovalFlowComputeResponseType::getApprovalFlowToken).orElse(null));
        return approvalFlowOutputVO;
    }

    /**
     * 校验审批流接口返回正常
     *
     * @param approvalFlowComputeResponseType
     * @return
     */
    private boolean checkApprovalFlowComputeResponseType(
        ApprovalFlowComputeResponseType approvalFlowComputeResponseType) {
        if (approvalFlowComputeResponseType == null) {
            return false;
        }
        if (isApprovalAllDone(approvalFlowComputeResponseType)) {
            return true;
        }
        return StringUtilsExt.isNotBlank(approvalFlowComputeResponseType.getApprovalFlowToken());
    }

    private boolean isApprovalAllDone(ApprovalFlowComputeResponseType approvalFlowComputeResponseType) {
        return StringUtilsExt.equalsIgnoreCase(Optional.ofNullable(approvalFlowComputeResponseType)
            .map(ApprovalFlowComputeResponseType::getApprovalAllDone).orElse(null), StringConstants.T);
    }

}
