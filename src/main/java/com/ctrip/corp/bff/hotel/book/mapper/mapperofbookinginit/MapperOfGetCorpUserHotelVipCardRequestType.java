package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckAvailResponseType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelBrandItem;
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelItem;
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelRatePlan;
import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.hotel.book.common.enums.BookingInitErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import corp.user.service.corpUserInfoService.GetCorpUserHotelVipCardRequestType;
import corp.user.service.group4j.accounts.BizModeBindRelationData;
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @Description vip用户信息查詢
 * @Date 2024/8/22 10:03
 * @Version 1.0
 */
@Component
public class MapperOfGetCorpUserHotelVipCardRequestType extends
    AbstractMapper<Tuple5<IntegrationSoaRequestType, WrapperOfCheckAvail.BaseCheckAvailInfo, QueryBizModeBindRelationResponseType,
            List<StrategyInfo>, List<HotelBookPassengerInput>>, GetCorpUserHotelVipCardRequestType> {

    @Override
    protected GetCorpUserHotelVipCardRequestType convert(Tuple5<IntegrationSoaRequestType, WrapperOfCheckAvail.BaseCheckAvailInfo,
            QueryBizModeBindRelationResponseType, List<StrategyInfo>, List<HotelBookPassengerInput>> tuple) {
        IntegrationSoaRequestType integrationSoaRequestType = tuple.getT1();
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = tuple.getT2();
        QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType = tuple.getT3();
        List<StrategyInfo> strategyInfos = tuple.getT4();
        List<HotelBookPassengerInput> hotelPassengerInputs = tuple.getT5();
        GetCorpUserHotelVipCardRequestType result = new GetCorpUserHotelVipCardRequestType();
        Integer hotelGroupId = Optional.ofNullable(checkAvailInfo)
                .map(WrapperOfCheckAvail.BaseCheckAvailInfo::getGroupId)
                .orElse(null);
        HotelBookPassengerInput hotelPassengerInput = CollectionUtil.findFirst(hotelPassengerInputs,
                Objects::nonNull);
        if ((hotelPassengerInput == null || hotelPassengerInput.getHotelPassengerInput() == null || StringUtil.isBlank(hotelPassengerInput.getHotelPassengerInput().getUid()))
                && StrategyOfBookingInitUtil.needFirstPassengerMembershipCard(strategyInfos)) {
            return null;
        }
        if (StrategyOfBookingInitUtil.needFirstPassengerMembershipCard(strategyInfos)) {
            result.setHotelGroupID(hotelGroupId);
            result.setUid(Optional.ofNullable(hotelPassengerInput).map(HotelBookPassengerInput::getHotelPassengerInput).map(HotelPassengerInput::getUid).orElse(null));
            return result;
        }
        String userId = RequestHeaderUtil.getUserId(integrationSoaRequestType);
        if (queryBizModeBindRelationResponseType == null) {
            return null;
        }
        BizModeBindRelationData bizModeBindRelationData = CollectionUtil.findFirst(queryBizModeBindRelationResponseType.getBizModeBindRelationDataList(),
                t -> t != null && StringUtil.equalsIgnoreCase(userId, t.getDimensionId()));
        if (bizModeBindRelationData == null || StringUtil.isBlank(bizModeBindRelationData.getPrimaryDimensionId())) {
            return null;
        }
        String uid = bizModeBindRelationData.getPrimaryDimensionId();
        result.setUid(uid);
        result.setHotelGroupID(hotelGroupId);
        return result;
    }

    @Override
    protected ParamCheckResult check(Tuple5<IntegrationSoaRequestType, WrapperOfCheckAvail.BaseCheckAvailInfo, QueryBizModeBindRelationResponseType,
            List<StrategyInfo>, List<HotelBookPassengerInput>> tuple) {
        return null;
    }
}
