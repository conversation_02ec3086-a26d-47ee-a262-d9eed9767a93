package com.ctrip.corp.bff.hotel.book.handler.orderfoundationcenterauthorizationservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.soa._20184.OrderFoundationCenterAuthorizationServiceClient;
import com.ctrip.soa._20184.QueryHotelAuthExtensionRequestType;
import com.ctrip.soa._20184.QueryHotelAuthExtensionResponseType;
import com.ctrip.soa._21210.HotelOrderRepeatOrderRequestType;
import com.ctrip.soa._21210.HotelOrderRepeatOrderResponseType;
import com.ctrip.soa._22074.OrderGenericSearchSerivceClient;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Description 智能延用查询
 * @Date 2024/8/12 14:47
 * @Version 1.0
 */
@Component
public class HandlerOfQueryHotelAuthExtension extends
        AbstractHandlerOfSOA<QueryHotelAuthExtensionRequestType, QueryHotelAuthExtensionResponseType, OrderFoundationCenterAuthorizationServiceClient> {

    @Override
    protected String getMethodName() {
        return "queryHotelAuthExtension";
    }

    @Override
    protected String getLogErrorCode(QueryHotelAuthExtensionResponseType response) {
        return Optional.ofNullable(response).map(QueryHotelAuthExtensionResponseType::getResponseCode)
                .orElse(0).toString();
    }
}
