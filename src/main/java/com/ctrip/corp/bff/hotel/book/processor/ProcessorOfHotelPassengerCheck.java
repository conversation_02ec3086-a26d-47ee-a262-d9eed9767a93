package com.ctrip.corp.bff.hotel.book.processor;

import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextRequestType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ReservationResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.utils.ResourceTokenUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PassengerCheckInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ResourceTokenInfo;
import com.ctrip.corp.bff.framework.template.handler.WaitFuture;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.framework.template.processor.AbstractProcessor;
import com.ctrip.corp.bff.hotel.book.common.mapper.MapperOfQueryCheckAvailContextRequestType;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.contract.HotelPassengerCheckRequestType;
import com.ctrip.corp.bff.hotel.book.contract.HotelPassengerCheckResponseType;
import com.ctrip.corp.bff.hotel.book.handler.corphotelroomavailableservice.HandlerOfQueryCheckAvailContext;
import com.ctrip.corp.bff.hotel.book.mapper.mapperofhotelpassengercheck.MapperOfHotelPassengerCheckResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * @Author: z.c. wang
 * @Description 酒店出行人校验
 * @Date: 2025/3/14 14:38
 * @Version 1.0
 */
@Component
public class ProcessorOfHotelPassengerCheck extends AbstractProcessor<HotelPassengerCheckRequestType, HotelPassengerCheckResponseType> {

    @Autowired
    private MapperOfQueryCheckAvailContextRequestType mapperOfQueryCheckAvailContextRequestType;
    @Autowired
    private HandlerOfQueryCheckAvailContext handlerOfQueryCheckAvailContext;
    @Autowired
    private MapperOfHotelPassengerCheckResponse mapperOfHotelPassengerCheckResponse;

    @Override
    public HotelPassengerCheckResponseType execute(HotelPassengerCheckRequestType request) throws Exception {
        // resourceToken
        List<ResourceTokenInfo> resourceTokenInfoList = Optional.of(request)
                .map(HotelPassengerCheckRequestType::getPassengerCheckInput)
                .map(PassengerCheckInput::getResourceTokenInfos)
                .orElseGet(ArrayList::new);
        ResourceToken resourceToken = ResourceTokenUtil.tryParseToken(resourceTokenInfoList.stream().findFirst().map(ResourceTokenInfo::getResourceToken).orElse(null));
        // 可定反查
        WaitFuture<QueryCheckAvailContextRequestType, QueryCheckAvailContextResponseType> queryCheckAvailContextWaitFuture = null;
        String wsId = Optional.ofNullable(resourceToken).map(ResourceToken::getReservationResourceToken).map(
                ReservationResourceToken::getWsId).orElse(null);
        if (StringUtil.isNotBlank(wsId)) {
            queryCheckAvailContextWaitFuture = handlerOfQueryCheckAvailContext.handleAsync(
                    mapperOfQueryCheckAvailContextRequestType.map(Tuple2.of(request.getIntegrationSoaRequestType(), wsId)));
        }
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType = Optional.ofNullable(queryCheckAvailContextWaitFuture)
                .map(WaitFuture::get).orElse(null);

        return mapperOfHotelPassengerCheckResponse.map(Tuple2.of(request, queryCheckAvailContextResponseType));
    }

    @Override
    public Map<String, String> tracking(HotelPassengerCheckRequestType request, HotelPassengerCheckResponseType response) {
        return null;
    }
}
