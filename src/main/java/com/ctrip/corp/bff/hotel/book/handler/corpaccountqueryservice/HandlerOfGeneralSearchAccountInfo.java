package com.ctrip.corp.bff.hotel.book.handler.corpaccountqueryservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.CorpAccountQueryService.CorpAccountQueryServiceClient;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoRequestType;
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 通用查询账户信息
 * @Date 2024/4/9 19:28
 * @Version 1.0
 */
@Component
public class HandlerOfGeneralSearchAccountInfo extends
        AbstractHandlerOfSOA<GeneralSearchAccountInfoRequestType, GeneralSearchAccountInfoResponseType, CorpAccountQueryServiceClient> {

    @Override
    protected String getMethodName() {
        return "generalSearchAccountInfo";
    }
}
