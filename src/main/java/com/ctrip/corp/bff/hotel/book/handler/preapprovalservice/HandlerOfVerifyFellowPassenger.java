package com.ctrip.corp.bff.hotel.book.handler.preapprovalservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.PreApprovalServiceClient;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.verifyfellowpassenger.VerifyFellowPassengerRequestType;
import com.ctrip.corp.corpsz.preapprovalws.common.contract.verifyfellowpassenger.VerifyFellowPassengerResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/9/12 21:00
 */
@Component public class HandlerOfVerifyFellowPassenger extends
    AbstractHandlerOfSOA<VerifyFellowPassengerRequestType, VerifyFellowPassengerResponseType, PreApprovalServiceClient> {

    @Override protected String getMethodName() {
        return "verifyFellowPassenger";
    }
}
