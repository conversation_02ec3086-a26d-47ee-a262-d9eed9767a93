package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.approve.ws.contract.MatchApprovalFlowRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.specific.contract.ApprovalFlowCheckRequestType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/6/23 18:31
 */
@Component
public class MapperOfApprovalFlowCheckRequestType extends
        AbstractMapper<Tuple2<OrderCreateRequestType,
                    MatchApprovalFlowRequestType>, ApprovalFlowCheckRequestType> {
    @Override
    protected ApprovalFlowCheckRequestType convert(Tuple2<OrderCreateRequestType,
        MatchApprovalFlowRequestType> orderCreateRequestTypeTuple1) {
        OrderCreateRequestType orderCreateRequestType = orderCreateRequestTypeTuple1.getT1();
        MatchApprovalFlowRequestType matchApprovalFlowRequest = orderCreateRequestTypeTuple1.getT2();
        ApprovalFlowCheckRequestType approvalFlowCheckRequestType = new ApprovalFlowCheckRequestType();
        approvalFlowCheckRequestType.setApprovalFlowInput(orderCreateRequestType.getApprovalFlowInput());
        approvalFlowCheckRequestType.setIntegrationSoaRequestType(orderCreateRequestType.getIntegrationSoaRequestType());
        approvalFlowCheckRequestType.setSignature(OrderCreateProcessorOfUtil.buildSignature(matchApprovalFlowRequest));
        approvalFlowCheckRequestType.setVersion(OrderCreateProcessorOfUtil.VERSION);
        return approvalFlowCheckRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple2<OrderCreateRequestType,
    MatchApprovalFlowRequestType> orderCreateRequestTypeTuple1) {
        return null;
    }
}
