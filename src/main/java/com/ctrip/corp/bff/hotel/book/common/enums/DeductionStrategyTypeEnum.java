package com.ctrip.corp.bff.hotel.book.common.enums;

import java.util.Arrays;
import java.util.Objects;

/**
 * @Author: chenchuang
 * @Date: 2024/10/12 15:49
 * @Description: 优惠折扣方式
 */
public enum DeductionStrategyTypeEnum {

    /**
     * 固定立减
     */
    FIXED_REDUCTION(1),

    /**
     * 百分比减免
     */
    PERCENTAGE_REDUCTION(2),

    /**
     * 阶梯减免
     */
    LADDER_REDUCTION(3),

    /**
     * 其他
     */
    OTHER(null)
    ;

    /**
     * 可订优惠券返回的优惠折扣方式id
     */
    private final Integer id;

    public Integer getId() {
        return id;
    }

    DeductionStrategyTypeEnum(Integer id) {
        this.id = id;
    }

    public static DeductionStrategyTypeEnum getDeductionStrategyTypeById(Integer deductionStrategyTypeId) {
        return Arrays.stream(DeductionStrategyTypeEnum.values())
                .filter(x -> Objects.equals(x.getId(), deductionStrategyTypeId))
                .findFirst()
                .orElse(OTHER);
    }

}
