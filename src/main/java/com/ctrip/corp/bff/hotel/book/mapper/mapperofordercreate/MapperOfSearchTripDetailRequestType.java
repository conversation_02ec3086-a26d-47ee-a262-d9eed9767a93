package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.TripInput;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.soa._21234.SearchTripDetailRequestType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/7/8 16:50
 */
@Component
public class MapperOfSearchTripDetailRequestType
    extends AbstractMapper<Tuple3<IntegrationSoaRequestType, String, TripInput>, SearchTripDetailRequestType> {
    private static final int OFFLINE = 2;
    private static final int ONLINE = 1;
    private static final int APP = 3;
    @Override
    protected SearchTripDetailRequestType convert(Tuple3<IntegrationSoaRequestType, String, TripInput> tuple1) {
        SearchTripDetailRequestType searchTripDetailRequestType = new SearchTripDetailRequestType();
        // 指定查询的行程ID
        String tripId = tuple1.getT2();
        if (StringUtil.isNotBlank(tripId)) {
            searchTripDetailRequestType.setTripId(Long.parseLong(tripId));
        } else {
            searchTripDetailRequestType.setTripId(Long.parseLong(tuple1.getT3().getTripId()));
        }
        searchTripDetailRequestType.setLanguage(tuple1.getT1().getLanguage());
        searchTripDetailRequestType.setChannelType(
            getChannelType(tuple1.getT1().getSourceFrom()));
        return searchTripDetailRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple3<IntegrationSoaRequestType, String, TripInput> tuple1) {
        return null;
    }

    /**
     * 1:Online 2:Offline 3:App
     * @param sourceFrom
     * @return
     */
    private int getChannelType(SourceFrom sourceFrom) {
        if (sourceFrom == null) {
            return APP;
        }
        switch (sourceFrom) {
            case Offline:
                return OFFLINE;
            case Online:
                return ONLINE;
            default:
                return APP;
        }
    }
}
