package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.hotel.common.enums.check.ParamCheckEnum;
import com.ctrip.corp.bff.framework.hotel.common.util.CityInfoUtil;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelGeoInfoResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.utils.ResourceTokenUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.MiceInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ResourceTokenInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.mice.basic.auth.contract.TmsCreateOrderVerifyRequestType;
import com.ctrip.corp.foundation.common.util.NumberUtil;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @Date 2024/6/28 22:27
 */
@Component
public class MapperOfTmsCreateOrderVerifyRequestType extends AbstractMapper<Tuple2<OrderCreateRequestType, ResourceToken>, TmsCreateOrderVerifyRequestType> {
    /**
     * 国内酒店
     */
    private static final String CN_HOTEL = "CN_HOTEL";
    /**
     * 海外酒店
     */
    private static final String INTERNATIONAL_HOTEL = "INTERNATIONAL_HOTEL";

    @Override protected TmsCreateOrderVerifyRequestType convert(Tuple2<OrderCreateRequestType, ResourceToken> request) {
        OrderCreateRequestType orderCreateRequestType = request.getT1();
        TmsCreateOrderVerifyRequestType tmsCreateOrderVerifyRequestType = new TmsCreateOrderVerifyRequestType();
        tmsCreateOrderVerifyRequestType.setIntegrationSoaRequestType(orderCreateRequestType.getIntegrationSoaRequestType());
        MiceInput miceInput = Optional.ofNullable(orderCreateRequestType.getMiceInput()).orElse(new MiceInput());
        tmsCreateOrderVerifyRequestType.setActivityId(NumberUtil.parseLong(miceInput.getMiceActivityId()));
        tmsCreateOrderVerifyRequestType.setMiceToken(miceInput.getMiceToken());
        ResourceToken resourceToken = ResourceTokenUtil.tryParseToken(Optional
            .ofNullable(orderCreateRequestType.getResourceTokenInfo())
            .map(ResourceTokenInfo::getResourceToken)
            .orElse(null));
        boolean oversea = CityInfoUtil.oversea(Optional.ofNullable(resourceToken)
            .map(ResourceToken::getHotelResourceToken)
            .map(HotelResourceToken::getHotelGeoInfoResourceToken)
            .map(HotelGeoInfoResourceToken::getCityId)
            .orElse(null));
        tmsCreateOrderVerifyRequestType.setProductType(oversea ? INTERNATIONAL_HOTEL : CN_HOTEL);
        return tmsCreateOrderVerifyRequestType;
    }

    @Override protected ParamCheckResult check(Tuple2<OrderCreateRequestType, ResourceToken> request) {
        MiceInput miceInput = Optional.ofNullable(request).map(Tuple2::getT1).map(OrderCreateRequestType::getMiceInput).orElse(new MiceInput());
        if (StringUtil.isEmpty(miceInput.getMiceActivityId())) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.INVALID_MICE_ACTIVITY_ID, "");
        }
        if (StringUtil.isEmpty(miceInput.getMiceToken())) {
            return new ParamCheckResult(false, OrderCreateErrorEnum.INVALID_MICE_TOKEN, "");
        }
        Integer cityId = Optional.ofNullable(request)
            .map(Tuple2::getT2)
            .map(ResourceToken::getHotelResourceToken)
            .map(HotelResourceToken::getHotelGeoInfoResourceToken)
            .map(HotelGeoInfoResourceToken::getCityId)
            .orElse(null);
        if (cityId == null) {
            return new ParamCheckResult(false, ParamCheckEnum.INVALID_CITY, "");
        }
        return null;
    }
}
