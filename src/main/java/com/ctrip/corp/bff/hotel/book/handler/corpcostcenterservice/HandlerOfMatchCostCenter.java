package com.ctrip.corp.bff.hotel.book.handler.corpcostcenterservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.costcenterService.CorpCostCenterServiceClient;
import corp.user.service.costcenterService.matchCostCenter.MatchCostCenterRequestType;
import corp.user.service.costcenterService.matchCostCenter.MatchCostCenterResponseType;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/4/17 16:09
 */
@Component
public class HandlerOfMatchCostCenter extends
    AbstractHandlerOfSOA<MatchCostCenterRequestType, MatchCostCenterResponseType, CorpCostCenterServiceClient> {
    @Override
    protected String getMethodName() {
        return "matchCostCenter";
    }
}
