package com.ctrip.corp.bff.hotel.book.service;

import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.util.ParamCheckUtil;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException;
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ResourceTokenInfo;
import com.ctrip.corp.bff.framework.template.processor.Processor;
import com.ctrip.corp.bff.framework.template.service.AbstractSyncService;
import com.ctrip.corp.bff.framework.template.service.generate.WebService;
import com.ctrip.corp.bff.hotel.book.common.enums.BookingInitErrorEnum;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitResponseType;
import com.ctrip.corp.bff.hotel.book.processor.ProcessorOfBookingInit;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Optional;


/**
 * <AUTHOR>
 * @description 填写页
 * @date 2024/06/26
 */
@WebService(name = "bookingInit")
public class ServiceOfBookingInit extends AbstractSyncService<BookingInitRequestType, BookingInitResponseType> {
    @Autowired
    private ProcessorOfBookingInit processorOfBookingInit;


    @Override
    public void validateRequest(BookingInitRequestType requestType) throws BusinessException {
        // 校验-基本参数缺失 HotelBookInput预订信息节点未传入
        if (requestType.getHotelBookInput() == null) {
            throw BusinessExceptionBuilder.createAlertException(BookingInitErrorEnum.PARAM_VALID_HOTEL_BOOK_INPUT_NULL,
                    "checkHotelBookInput error");
        }

        // 校验-基本参数缺失 因公因私标识
        CorpPayInfoUtil.check(requestType.getCorpPayInfo());

        // 校验-缺失资源token 不可缺失
        if (StringUtil.isBlank(
                Optional.ofNullable(requestType.getResourceTokenInfo()).map(ResourceTokenInfo::getResourceToken)
                        .orElse(null))) {
            throw BusinessExceptionBuilder.createAlertException(BookingInitErrorEnum.PARAM_VALID_RESOURCE_TOKEN_NULL,
                    "checkResourceToken error");
        }
        
    }

    @Override
    protected Processor<BookingInitRequestType, BookingInitResponseType> getProcessor(BookingInitRequestType requestType) {
        return processorOfBookingInit;
    }
}
