package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitResponseType;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodRequestType;
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataRequestType;
import com.ctrip.order.reimbursement.ReimbursementQueryRequestType;
import org.springframework.stereotype.Component;

import java.util.Optional;

/**
 * @Author: chenchuang
 * @Date: 2024/8/22 16:26
 * @Description:
 */
@Component
public class MapperOfReimbursementQueryRequestType extends AbstractMapper<Tuple2<BookingInitRequestType, Long>, ReimbursementQueryRequestType> {

    @Override
    protected ReimbursementQueryRequestType convert(Tuple2<BookingInitRequestType, Long> param) {
        Long orderId = param.getT2();
        if (orderId == null) {
            return null;
        }
        ReimbursementQueryRequestType reimbursementQueryRequestType = new ReimbursementQueryRequestType();
        // 这里orderId拆包会有npe问题
        reimbursementQueryRequestType.setOrderId(orderId);
        reimbursementQueryRequestType.setLocal(Optional.ofNullable(param.getT1().getIntegrationSoaRequestType())
            .map(IntegrationSoaRequestType::getLanguage).orElse(null));
        return reimbursementQueryRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple2<BookingInitRequestType, Long> bookingInitRequestTypeBookingInitResponseTypeTuple2) {
        return null;
    }
}
