package com.ctrip.corp.bff.hotel.book.handler.triporderservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.soa._21234.*;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/7/8 16:29
 */
@Component
public class HandlerOfCreateTrip extends AbstractHandlerOfSOA<CreateTripRequestType
        , CreateTripResponseType, TripOrderServiceClient> {
    @Override
    protected String getMethodName() {
        return "createTrip";
    }
}
