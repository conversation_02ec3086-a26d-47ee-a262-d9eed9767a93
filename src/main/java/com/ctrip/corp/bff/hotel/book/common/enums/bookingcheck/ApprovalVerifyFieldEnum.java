package com.ctrip.corp.bff.hotel.book.common.enums.bookingcheck;

/**
 * <AUTHOR>
 * @date 2024-11-07
 **/
public enum ApprovalVerifyFieldEnum {
    /**
     * 入住开始时间
     */
    PRE_VERIFY_FIELD_CHECKIN_BEGIN_DATE(1),
    /**
     * 入住结束时间
     */
    PRE_VERIFY_FIELD_CHECKIN_END_DATE(2),
    /**
     * 离店开始时间
     */
    PRE_VERIFY_FIELD_CHECKOUT_BEGIN_DATE(4),
    /**
     * 离店结束时间
     */
    PRE_VERIFY_FIELD_CHECKOUT_END_DATE(8),
    /**
     * 入住城市
     */
    PRE_VERIFY_FIELD_CITY(32),
    ;

    private Integer code;

    ApprovalVerifyFieldEnum(Integer code) {
        this.code = code;
    }

    public Integer getCode() {
        return code;
    }
}
