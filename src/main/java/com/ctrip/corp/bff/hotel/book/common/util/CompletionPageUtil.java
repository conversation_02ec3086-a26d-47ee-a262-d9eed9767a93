package com.ctrip.corp.bff.hotel.book.common.util;

import java.util.List;
import java.util.Objects;
import java.util.Optional;

import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig;
import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.SourceFrom;
import com.ctrip.corp.bff.hotel.book.contract.BookUrlInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;

/**
 * <AUTHOR>
 * @description
 * @date 2025/2/26
 */
public class CompletionPageUtil {
    private CompletionPageUtil() {
    }

    /**
     * 完成页灰度开关
     */
    private static final String USE_NEW_COMPLETION_PAGE = "useNewCompletionPage";
    /**
     * 旧版完成页
     */
    private static final String SUCCESS = "SUCCESS";
    /**
     * 新版完成页
     */
    private static final String COMPLETION = "COMPLETION";

    /**
     * orderId参数, 传给完成页
     */
    public static final String ORDER_ID = "&orderId={0}";
    /**
     * orderId参数, 传给完成页
     */
    public static final String ORDER_NUMBER = "&orderNumber={0}";
    /**
     * offlineUid
     */
    public static final String OFFLINE_UID = "&offlineUid={0}";
    /**
     * from=backhome H5回到首页参数
     */
    public static final String FROM_BACK_HOME = "&from=backhome";

    /**
     * 支付平台的回调链接
     */
    public static final String S_BACK = "S_BACK";

    public static String buildCompletionPageUrl(
        OrderCreateRequestType orderCreateRequestType, Long orderId) {
        if (orderCreateRequestType == null) {
            return null;
        }

        if (QConfigOfCustomConfig.isSupport(USE_NEW_COMPLETION_PAGE,
            RequestHeaderUtil.getCorpId(orderCreateRequestType.getIntegrationSoaRequestType()))) {
            return getNewCompletionPageUrl(orderCreateRequestType, orderId);
        }
        return null;
    }

    public static String buildSBackUrl(OrderCreateRequestType orderCreateRequestType, Long orderId) {
        if (orderCreateRequestType == null) {
            return null;
        }
        String completionPageUrl = getUrlByType(orderCreateRequestType.getBookUrlInfos(), COMPLETION);
        String sBack = getUrlByType(orderCreateRequestType.getBookUrlInfos(), S_BACK);

        if (QConfigOfCustomConfig.isSupport(USE_NEW_COMPLETION_PAGE,
            RequestHeaderUtil.getCorpId(orderCreateRequestType.getIntegrationSoaRequestType()))) {
            return buildUrlWithParams(completionPageUrl,
                StringUtil.indexedFormat(ORDER_ID, String.valueOf(orderId)),
                StringUtil.indexedFormat(ORDER_NUMBER, String.valueOf(orderId))) + "&";
        }
        return sBack;
    }

    public static String getNewCompletionPageUrl(OrderCreateRequestType orderCreateRequestType, Long orderId) {
        String orderIdStr = TemplateNumberUtil.isZeroOrNull(orderId) ? StringUtil.EMPTY : String.valueOf(orderId);
        if (orderCreateRequestType == null) {
            return null;
        }
        String completionPageUrl = getUrlByType(orderCreateRequestType.getBookUrlInfos(), COMPLETION);
        if (StringUtil.isEmpty(completionPageUrl)) {
            return null;
        }

        if (SourceFrom.Offline == Optional.ofNullable(orderCreateRequestType.getIntegrationSoaRequestType())
            .map(IntegrationSoaRequestType::getSourceFrom).orElse(null)) {
            return buildUrlWithParams(completionPageUrl,
                    StringUtil.indexedFormat(ORDER_ID, orderIdStr),
                    StringUtil.indexedFormat(ORDER_NUMBER, orderIdStr),
                    StringUtil.indexedFormat(OFFLINE_UID, RequestHeaderUtil.getUserId(orderCreateRequestType.getIntegrationSoaRequestType())));
        }
        if (SourceFrom.H5 == Optional.ofNullable(orderCreateRequestType.getIntegrationSoaRequestType())
            .map(IntegrationSoaRequestType::getSourceFrom).orElse(null)) {
            return buildUrlWithParams(completionPageUrl,
                    StringUtil.indexedFormat(ORDER_ID, orderIdStr),
                    StringUtil.indexedFormat(ORDER_NUMBER, orderIdStr), FROM_BACK_HOME);
        }

        return buildUrlWithParams(completionPageUrl,
                StringUtil.indexedFormat(ORDER_ID, orderIdStr),
                StringUtil.indexedFormat(ORDER_NUMBER, orderIdStr));
    }

    private static String getUrlByType(List<BookUrlInfo> bookUrlInfos, String urlType) {
        if (CollectionUtil.isEmpty(bookUrlInfos) || StringUtil.isEmpty(urlType)) {
            return null;
        }
        return bookUrlInfos.stream().filter(Objects::nonNull)
            .filter(bookUrlInfo -> urlType.equalsIgnoreCase(bookUrlInfo.getUrlType()))
            .findFirst().map(BookUrlInfo::getUrlValue).orElse(null);
    }

    /**
     * 向URL追加参数
     *
     * @param url 原始URL
     * @param params 需要追加的参数，格式为"key=value"或"&key2=value2"或"?key2=value2"等
     * @return 追加参数后的URL
     */
    public static String buildUrlWithParams(String url, String... params) {
        if (url == null) {
            return url;
        }
        // 判断数组为空
        if (StringUtil.isAllBlank(params)) {
            return url;
        }
        // 拼接所有参数，去除每个参数前多余的&或?
        StringBuilder paramBuilder = new StringBuilder();
        for (String param : params) {
            if (StringUtil.isBlank(param)) {
                continue;
            }
            // 去除参数前的&或?，避免重复
            if (!paramBuilder.isEmpty()) {
                paramBuilder.append("&");
            }
            paramBuilder.append(param.replaceAll("^[&?]+", ""));
        }
        String paramStr = paramBuilder.toString();
        if (paramStr.isEmpty()) {
            return url;
        }
        // 如果URL中没有?，则直接追加参数
        if (!StringUtil.contains(url, '?')) {
            return url + "?" + paramStr;
        }
        // 如果URL中已经有?，则判断最后一个字符是&还是?，然后追加参数
        if (url.endsWith("?")) {
            return url + paramStr;
        } else if (url.endsWith("&")) {
            return url + paramStr;
        } else {
            return url + "&" + paramStr;
        }
    }
}
