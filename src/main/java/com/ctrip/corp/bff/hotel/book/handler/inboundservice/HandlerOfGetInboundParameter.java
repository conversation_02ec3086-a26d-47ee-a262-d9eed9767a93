package com.ctrip.corp.bff.hotel.book.handler.inboundservice;

import com.ctrip.basebiz.members.core.contract.GetInboundParameterRequestType;
import com.ctrip.basebiz.members.core.contract.GetInboundParameterResponseType;
import com.ctrip.basebiz.members.core.contract.InboundServiceClient;
import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import org.springframework.stereotype.Component;

/**
 * @Author: chenchuang
 * @Date: 2024/8/22 16:26
 * @Description:获取offline绑定信息
 */
@Component
public class HandlerOfGetInboundParameter extends AbstractHandlerOfSOA<GetInboundParameterRequestType,
    GetInboundParameterResponseType, InboundServiceClient> {

    @Override
    protected String getMethodName() {
        return "getInboundParameter";
    }
}
