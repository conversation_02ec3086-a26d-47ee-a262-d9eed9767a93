package com.ctrip.corp.bff.hotel.book.handler.corphotelbookcommonws;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.hotelbook.commonws.CorpHotelBookCommonWSClient;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodRequestType;
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodResponseType;
import com.ctrip.corp.hotelbook.commonws.entity.GetTaxDescriptionRequestType;
import com.ctrip.corp.hotelbook.commonws.entity.GetTaxDescriptionResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/4/11
 */
@Component
public class HandlerOfGetTaxDescription extends AbstractHandlerOfSOA<GetTaxDescriptionRequestType,
        GetTaxDescriptionResponseType, CorpHotelBookCommonWSClient> {
    @Override
    protected String getMethodName() {
        return "getTaxDescription";
    }
}
