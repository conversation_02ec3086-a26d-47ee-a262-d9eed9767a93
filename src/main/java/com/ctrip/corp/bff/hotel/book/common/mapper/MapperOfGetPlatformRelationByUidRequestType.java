package com.ctrip.corp.bff.hotel.book.common.mapper;

import com.ctrip.corp.bff.framework.hotel.common.util.RequestHeaderUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitResponseType;
import com.ctrip.order.reimbursement.ReimbursementQueryRequestType;
import corp.user.service.corpUserInfoService.GetPlatformRelationByUidRequestType;
import corp.user.service.group4j.accounts.BizModeBindRelationData;
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType;
import org.springframework.stereotype.Component;

import java.util.Objects;
import java.util.Optional;

/**
 * @Author: chenchuang
 * @Date: 2024/8/22 16:26
 * @Description:
 */
@Component
public class MapperOfGetPlatformRelationByUidRequestType extends AbstractMapper<Tuple2<IntegrationSoaRequestType,
        QueryBizModeBindRelationResponseType>, GetPlatformRelationByUidRequestType> {

    @Override
    protected GetPlatformRelationByUidRequestType convert(Tuple2<IntegrationSoaRequestType, QueryBizModeBindRelationResponseType> param) {
        if (param == null) {
            return null;
        }
        IntegrationSoaRequestType integrationSoaRequestType = param.getT1();
        QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType = param.getT2();
        String uid = Optional.ofNullable(integrationSoaRequestType).map(IntegrationSoaRequestType::getUserInfo).map(UserInfo::getUserId).orElse(null);
        if (queryBizModeBindRelationResponseType == null || StringUtil.isBlank(uid)) {
            return null;
        }
        BizModeBindRelationData bizModeBindRelationData = CollectionUtil.findFirst(queryBizModeBindRelationResponseType.getBizModeBindRelationDataList(),
                t -> t != null && StringUtil.equalsIgnoreCase(uid, t.getDimensionId()));
        if (bizModeBindRelationData == null || StringUtil.isBlank(bizModeBindRelationData.getPrimaryDimensionId())) {
            return null;
        }
        GetPlatformRelationByUidRequestType getPlatformRelationByUidRequestType = new GetPlatformRelationByUidRequestType();
        getPlatformRelationByUidRequestType.setUid(bizModeBindRelationData.getPrimaryDimensionId());
        return getPlatformRelationByUidRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple2<IntegrationSoaRequestType, QueryBizModeBindRelationResponseType> param) {
        return null;
    }
}
