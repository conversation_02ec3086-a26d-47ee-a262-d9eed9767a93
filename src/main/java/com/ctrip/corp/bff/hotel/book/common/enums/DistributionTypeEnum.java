package com.ctrip.corp.bff.hotel.book.common.enums;

/**
 * @Author: ch<PERSON><PERSON><PERSON>
 * @Date: 2024/9/13 10:32
 * @Description: 发票配送方式
 */
public enum DistributionTypeEnum {

    /**
     * EMS收费
     */
    EMS_CHARGE("emsCharge"),

    /**
     * EMS免费
     */
    EMS_FREE("emsFree"),

    /**
     * 打包配送
     */
    PACKAGE("package");


    private String key;

    DistributionTypeEnum(String key) {
        this.key = key;
    }

    public String getKey() {
        return key;
    }

    public void setKey(String key) {
        this.key = key;
    }
}
