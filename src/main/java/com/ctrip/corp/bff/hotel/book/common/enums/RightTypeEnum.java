package com.ctrip.corp.bff.hotel.book.common.enums;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;

/**
 * <AUTHOR>
 * @description
 * @date 2024/9/2
 */
public enum RightTypeEnum {
    /**
     * 免费兑早餐权益
     */
    MEAL(32, "Breakfast"),
    /**
     * 免费取消权益
     */
    FREE_CANCEL(33, "Cancel"),
    /**
     * 延迟退房权益
     */
    DELAYED_CHECK_OUT(34, "DelayCheckout"),
    /**
     * 免费升房权益
     */
    FREE_UPGRADE_ROOM(35, "HousePromotion");
    private final int type;
    private final String code;

    RightTypeEnum(int type, String code) {
        this.type = type;
        this.code = code;
    }

    public int getType() {
        return type;
    }

    public String getCode() {
        return code;
    }

    public static RightTypeEnum findByType(Integer type) {
        if (type == null) {
            return null;
        }
        for (RightTypeEnum rightTypeEnum : RightTypeEnum.values()) {
            if (rightTypeEnum.getType() == type) {
                return rightTypeEnum;
            }
        }
        return null;
    }

    public static RightTypeEnum findByCode(String code) {
        if (StringUtil.isEmpty(code)) {
            return null;
        }
        for (RightTypeEnum rightTypeEnum : RightTypeEnum.values()) {
            if (StringUtil.equalsIgnoreCase(code, rightTypeEnum.getCode())) {
                return rightTypeEnum;
            }
        }
        return null;
    }
}
