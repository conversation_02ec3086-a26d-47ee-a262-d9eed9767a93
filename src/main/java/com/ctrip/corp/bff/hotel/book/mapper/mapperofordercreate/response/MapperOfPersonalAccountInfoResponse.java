package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response;

import com.ctc.wstx.shaded.msv_core.datatype.xsd.AnyURIType;
import com.ctrip.corp.agg.hotel.salestrategy.entity.CalculateTravelRewardsRequestType;
import com.ctrip.corp.agg.hotel.salestrategy.entity.CalculateTravelRewardsResponseType;
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.entity.contract.*;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.common.constant.BooleanConstant;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.UserInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelBalanceTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType;
import com.ctrip.corp.foundation.common.enums.LanguageLocaleEnum;
import com.ctrip.corp.foundation.common.util.Null;
import corp.user.service.CorpAccountQueryService.*;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/21
 */
@Component
public class MapperOfPersonalAccountInfoResponse extends AbstractMapper<Tuple4<OrderCreateRequestType, CalculateTravelRewardsResponseType,
        QueryIndividualAccountResponseType, OrderCreateToken>, Tuple2<Boolean, OrderCreateResponseType>> {

    /**
     * 仅本人
     */
    private static final String ONLY_SELF_HOTEL_GUEST_ACCESS = "B";

    /**
     * 心程贝入口策略code
     */
    public static final String STRATEGY_KEY_BOOKING_WITH_PERSONAL_ACCOUNT = "BOOKING_WITH_PERSONAL_ACCOUNT";


    /**
     * 使用心程贝差标提醒话术code
     */
    private static final String BOOKING_WITH_PERSONAL_ACCOUNT_TIP = "BOOKING_WITH_PERSONAL_ACCOUNT_TIP";

    /**
     * 不允许使用+不支持奖励心程贝提示code
     */
    private static final String NOT_ALLOW_BOOKING_BONUS_WITH_PERSONAL_ACCOUNT = "NOT_ALLOW_BOOKING_BONUS_WITH_PERSONAL_ACCOUNT";

    /**
     * 不允许使用心程贝提示code
     */
    private static final String NOT_ALLOW_BOOKING_WITH_PERSONAL_ACCOUNT = "NOT_ALLOW_BOOKING_WITH_PERSONAL_ACCOUNT";

    /**
     * 不支持奖励心程贝
     */
    private static final String NOT_ALLOW_BONUS_WITH_PERSONAL_ACCOUNT = "NOT_ALLOW_BONUS_WITH_PERSONAL_ACCOUNT";

    /**
     * 因非仅本人不支持奖励心程贝
     */
    private static final String NOT_ONLY_SELF = "R0001";

    @Override
    protected Tuple2<Boolean, OrderCreateResponseType> convert(Tuple4<OrderCreateRequestType, CalculateTravelRewardsResponseType,
            QueryIndividualAccountResponseType, OrderCreateToken> para) {
        if (para == null) {
            return null;
        }
        OrderCreateRequestType orderCreateRequestType = para.getT1();
        CalculateTravelRewardsResponseType calculateTravelRewardsResponseType = para.getT2();
        QueryIndividualAccountResponseType queryIndividualAccountResponseType = para.getT3();
        OrderCreateToken orderCreateToken = para.getT4();
        // 非仅本人
        if (!ONLY_SELF_HOTEL_GUEST_ACCESS.equalsIgnoreCase(Optional.ofNullable(queryIndividualAccountResponseType)
                .map(QueryIndividualAccountResponseType::getIndividualAccountConfig)
                .map(IndividualAccountRule::getHotelControlConfigs).map(HotelControlConfigs::getHotelGuestAccess).orElse(null))) {
            return null;
        }
        if (orderCreateRequestType == null) {
            return null;
        }
        // 是否使用心程贝场景
        boolean isBookingWithPersonalAccount = isBookingWithPersonalAccount(orderCreateRequestType.getStrategyInfos());
        // 是否支持奖励心程贝
        boolean isSupportBonusPersonalAccount = BooleanUtil.isTrue(Optional.ofNullable(calculateTravelRewardsResponseType)
                .map(CalculateTravelRewardsResponseType::isCanCalculateTravelRewards).orElse(null));
        // 是否因非仅本人不支持奖励心程贝
        boolean notSupportBonusPersonalAccountBecauseOfNotOnlySelf = NOT_ONLY_SELF.equalsIgnoreCase(
                Optional.ofNullable(calculateTravelRewardsResponseType)
                        .map(CalculateTravelRewardsResponseType::getNoCalculateReasonCode).orElse(null));
        // 入住人是否仅本人
        boolean isClientOnlySelf = isClientOnlySelf(orderCreateRequestType.getHotelBookPassengerInputs(),
                orderCreateRequestType.getIntegrationSoaRequestType());
        // 是否已经提示过
        boolean isAlreadyAlert = isAlreadyAlert(orderCreateToken);
        // 心程贝仅本人提示
        if (isBookingWithPersonalAccount && isClientOnlySelf && !isAlreadyAlert) {
            orderCreateToken.addContinueTypes(ContinueTypeConst.PERSONAL_ACCOUNT_TIP);
            return Tuple2.of(true, buildPersonalAccountAlertTipResponse(BOOKING_WITH_PERSONAL_ACCOUNT_TIP, orderCreateToken,
                    queryIndividualAccountResponseType, orderCreateRequestType.getIntegrationSoaRequestType()));
        }
        // 使用+奖励心程贝不是仅本人提示
        if (isBookingWithPersonalAccount && isSupportBonusPersonalAccount && !isClientOnlySelf) {
            return Tuple2.of(true, buildPersonalAccountAlertTipResponse(NOT_ALLOW_BOOKING_BONUS_WITH_PERSONAL_ACCOUNT, orderCreateToken,
                    queryIndividualAccountResponseType, orderCreateRequestType.getIntegrationSoaRequestType()));
        }
        // 使用心程贝不是仅本人提示
        if (isBookingWithPersonalAccount && !isSupportBonusPersonalAccount && !isClientOnlySelf) {
            return Tuple2.of(true, buildPersonalAccountAlertTipResponse(NOT_ALLOW_BOOKING_WITH_PERSONAL_ACCOUNT, orderCreateToken,
                    queryIndividualAccountResponseType, orderCreateRequestType.getIntegrationSoaRequestType()));
        }
        // 奖励心程贝不是仅本人提示
        if (!isBookingWithPersonalAccount && !isSupportBonusPersonalAccount && notSupportBonusPersonalAccountBecauseOfNotOnlySelf && !isAlreadyAlert) {
            orderCreateToken.addContinueTypes(ContinueTypeConst.PERSONAL_ACCOUNT_TIP);
            return Tuple2.of(true, buildPersonalAccountAlertTipResponse(NOT_ALLOW_BONUS_WITH_PERSONAL_ACCOUNT, orderCreateToken,
                    queryIndividualAccountResponseType, orderCreateRequestType.getIntegrationSoaRequestType()));
        }
        return null;
    }

    /**
     * 是否已经提示过
     * @param orderCreateToken
     * @return
     */
    protected boolean isAlreadyAlert(OrderCreateToken orderCreateToken) {
        return orderCreateToken != null && CollectionUtils.isNotEmpty(orderCreateToken.getContinueTypes())
                && orderCreateToken.getContinueTypes().contains(ContinueTypeConst.PERSONAL_ACCOUNT_TIP);
    }

    /**
     * 是否使用心程贝场景
     * @param strategyInfos
     * @return
     */
    protected boolean isBookingWithPersonalAccount(List<StrategyInfo> strategyInfos) {
        if (CollectionUtil.isEmpty(strategyInfos)) {
            return false;
        }
        return strategyInfos.stream().filter(Objects::nonNull)
                .anyMatch(t -> STRATEGY_KEY_BOOKING_WITH_PERSONAL_ACCOUNT.equalsIgnoreCase(t.getStrategyKey())
                        && BooleanConstant.TRUE.equalsIgnoreCase(t.getStrategyValue()));
    }

    /**
     * 入住人是否仅本人
     * @param hotelBookPassengerInputs
     * @param integrationSoaRequestType
     * @return
     */
    protected boolean isClientOnlySelf(List<HotelBookPassengerInput> hotelBookPassengerInputs, IntegrationSoaRequestType integrationSoaRequestType) {
        if (CollectionUtil.isEmpty(hotelBookPassengerInputs) || integrationSoaRequestType == null || integrationSoaRequestType.getUserInfo() == null) {
            return false;
        }
        if (hotelBookPassengerInputs.stream()
                .filter(Objects::nonNull).toList().size() != 1) {
            return false;
        }
        String uid = integrationSoaRequestType.getUserInfo().getUserId();
        if (StringUtil.isBlank(uid)) {
            return false;
        }
        HotelBookPassengerInput hotelBookPassengerInput = CollectionUtil.findFirst(hotelBookPassengerInputs, Objects::nonNull);
        if (hotelBookPassengerInput == null || hotelBookPassengerInput.getHotelPassengerInput() == null
                || StringUtil.isBlank(hotelBookPassengerInput.getHotelPassengerInput().getUid())) {
            return false;
        }
        return StringUtil.equalsIgnoreCase(uid, hotelBookPassengerInput.getHotelPassengerInput().getUid());
    }


    protected OrderCreateResponseType buildPersonalAccountAlertTipResponse(String confirmCode,
                                                                           OrderCreateToken orderCreateToken,
                                                                           QueryIndividualAccountResponseType queryIndividualAccountResponseType,
                                                                           IntegrationSoaRequestType integrationSoaRequestType) {
        OrderCreateResponseType orderCreateResponseType = new OrderCreateResponseType();
        orderCreateResponseType.setOrderCreateToken(TokenParseUtil.generateToken(orderCreateToken, OrderCreateToken.class));
        orderCreateResponseType.setConfirmInfo(buildPersonalAccountConfirmInfo(confirmCode, queryIndividualAccountResponseType, integrationSoaRequestType));
        return orderCreateResponseType;
    }

    protected ConfirmInfo buildPersonalAccountConfirmInfo(String confirmCode,
                                                          QueryIndividualAccountResponseType queryIndividualAccountResponseType,
                                                          IntegrationSoaRequestType integrationSoaRequestType) {
        ConfirmDetailExtend confirmDetailExtend = buildConfirmDetailExtend(queryIndividualAccountResponseType, integrationSoaRequestType);
        ConfirmDetailInfo confirmDetailInfo = new ConfirmDetailInfo();
        confirmDetailInfo.setCode(confirmCode);
        confirmDetailInfo.setConfirmDetailExtends(confirmDetailExtend == null ? null : Arrays.asList(confirmDetailExtend));
        ConfirmInfo confirmInfo = new ConfirmInfo();
        confirmInfo.setConfirmDetailInfos(Arrays.asList(confirmDetailInfo));
        return confirmInfo;
    }

    private static final String PERSONAL_ACCOUNT_NAME = "PERSONAL_ACCOUNT_NAME";

    protected ConfirmDetailExtend buildConfirmDetailExtend(QueryIndividualAccountResponseType queryIndividualAccountResponseType,
                                                           IntegrationSoaRequestType integrationSoaRequestType) {
        if (queryIndividualAccountResponseType == null || queryIndividualAccountResponseType.getIndividualAccountConfig() == null
                || CollectionUtils.isEmpty(queryIndividualAccountResponseType.getIndividualAccountConfig().getNameList())) {
            return null;
        }
        IAccountLocale iAccountLocale = CollectionUtil.findFirst(queryIndividualAccountResponseType.getIndividualAccountConfig().getNameList(),
                t -> t != null && StringUtil.equalsIgnoreCase(buildAccountLocale(integrationSoaRequestType), t.getLocale()));
        if (iAccountLocale == null) {
            return null;
        }
        ConfirmDetailExtend confirmDetailExtend = new ConfirmDetailExtend();
        confirmDetailExtend.setKey(PERSONAL_ACCOUNT_NAME);
        confirmDetailExtend.setValue(iAccountLocale.getLocaleValue());
        return confirmDetailExtend;
    }

    protected String buildAccountLocale(IntegrationSoaRequestType integrationSoaRequestType) {
        if (integrationSoaRequestType == null || StringUtil.isBlank(integrationSoaRequestType.getLanguage())) {
            return null;
        }
        if (StringUtil.equalsIgnoreCase(integrationSoaRequestType.getLanguage(), LanguageLocaleEnum.ZH_CN.getLanguageLocaleString())) {
            return LanguageLocaleEnum.ZH_CN.getLanguageLocaleString();
        }
        return LanguageLocaleEnum.EN_US.getLanguageString();
    }

    @Override
    protected ParamCheckResult check(Tuple4<OrderCreateRequestType, CalculateTravelRewardsResponseType,
            QueryIndividualAccountResponseType, OrderCreateToken> para) {
        return null;
    }
}
