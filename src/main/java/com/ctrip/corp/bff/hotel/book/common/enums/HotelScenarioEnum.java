package com.ctrip.corp.bff.hotel.book.common.enums;

import io.protostuff.Tag;

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/29
 */
public enum HotelScenarioEnum {
    /**
     * 修改订单
     */
    MODIFY("MODIFY"),
    /**
     * 复制订单
     */
    COPY_ORDER("COPY_ORDER"),
    /**
     * 未知
     */
    UN_KNOW("UN_KNOW"),
    /**
     * 申请修改订单
     */
    APPLY_MODIFY("APPLY_MODIFY"),
    /**
     * 酒店延住
     */
    EXTEND("EXTEND"),
    ;

    private final String code;

    HotelScenarioEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}
