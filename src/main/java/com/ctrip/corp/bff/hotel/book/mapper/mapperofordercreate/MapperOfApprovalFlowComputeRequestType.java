package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.agg.hotel.roomavailable.entity.BookHotelInfoEntity;
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.approve.ws.contract.FlowTmpl;
import com.ctrip.corp.approve.ws.contract.MatchApprovalFlowRequestType;
import com.ctrip.corp.approve.ws.contract.MatchApprovalFlowResponseType;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple6;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple7;
import com.ctrip.corp.bff.hotel.book.common.constant.SoaErrorSharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail.BaseCheckAvailInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.corp.bff.specific.contract.ApprovalFlowComputeRequestType;
import com.ctrip.corp.bff.specific.contract.ApprovalFlowPassengerInfo;
import com.ctrip.corp.foundation.common.util.CollectionUtilsExt;
import com.ctrip.corp.foundation.common.util.StringUtilsExt;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/23 20:37
 */
@Component
public class MapperOfApprovalFlowComputeRequestType
        extends AbstractMapper<Tuple7<MatchApprovalFlowRequestType, MatchApprovalFlowResponseType,
            OrderCreateRequestType, QueryCheckAvailContextResponseType, WrapperOfCheckAvail.BaseCheckAvailInfo,
    QconfigOfCertificateInitConfig, Map<String, StrategyInfo>>, ApprovalFlowComputeRequestType> {


    public static final String APPROVALTYPE = "B";

    @Override
    protected ApprovalFlowComputeRequestType convert(Tuple7<MatchApprovalFlowRequestType, MatchApprovalFlowResponseType,
            OrderCreateRequestType, QueryCheckAvailContextResponseType, BaseCheckAvailInfo, QconfigOfCertificateInitConfig,
            Map<String, StrategyInfo>> tuple4) {

        MatchApprovalFlowRequestType matchApprovalFlowRequest = tuple4.getT1();

        // 匹配审批流信息
        MatchApprovalFlowResponseType matchApprovalFlowResponse = tuple4.getT2();

        // 创单req
        OrderCreateRequestType orderCreateRequest = tuple4.getT3();

        // 查询可订检查上下文
        QueryCheckAvailContextResponseType checkAvailContextResponse = tuple4.getT4();

        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = tuple4.getT5();
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig = tuple4.getT6();
        Map<String, StrategyInfo> strategyInfoMap = tuple4.getT7();

        if (matchApprovalFlowResponse == null) {
            return null;
        }


        ApprovalFlowComputeRequestType approvalFlowComputeRequestType = new ApprovalFlowComputeRequestType();
        approvalFlowComputeRequestType.setIntegrationSoaRequestType(orderCreateRequest.getIntegrationSoaRequestType());
        approvalFlowComputeRequestType.setApprovalType(APPROVALTYPE);
        approvalFlowComputeRequestType.setProductType(getProductType(checkAvailContextResponse));
        approvalFlowComputeRequestType.setApprovalFlowPassengerInfos(
            getApprovalFlowPassengerInfos(orderCreateRequest, checkAvailInfo, qconfigOfCertificateInitConfig,
                strategyInfoMap));
        approvalFlowComputeRequestType.setExternalId(
            Optional.ofNullable(matchApprovalFlowResponse.getFlowTmplInfo()).map(FlowTmpl::getExternalId)
                .orElse(null));
        approvalFlowComputeRequestType.setRooms(getRoomQuantity(orderCreateRequest));
        approvalFlowComputeRequestType.setSignature(OrderCreateProcessorOfUtil.buildSignature(matchApprovalFlowRequest));
        approvalFlowComputeRequestType.setVersion(OrderCreateProcessorOfUtil.VERSION);
        return approvalFlowComputeRequestType;
    }

    /**
     * 获取预定房间数量
     * @param orderCreateRequest
     * @return
     */
    private String getRoomQuantity(OrderCreateRequestType orderCreateRequest) {
        return Optional.ofNullable(orderCreateRequest)
            .map(OrderCreateRequestType::getHotelBookInput)
            .map(HotelBookInput::getRoomQuantity)
            .map(String::valueOf)
            .orElse(StringUtilsExt.EMPTY);
    }

    private List<ApprovalFlowPassengerInfo> getApprovalFlowPassengerInfos(OrderCreateRequestType orderCreateRequest,
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailContextResponse,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        Map<String, StrategyInfo> strategyInfoMap) {
        return Optional.ofNullable(orderCreateRequest).map(OrderCreateRequestType::getHotelBookPassengerInputs)
            .orElse(Collections.emptyList()).stream().map(t -> {
                ApprovalFlowPassengerInfo approvalFlowPassengerInfo = new ApprovalFlowPassengerInfo();
                approvalFlowPassengerInfo.setInfoId(t.getHotelPassengerInput().getInfoId());
                approvalFlowPassengerInfo.setUid(
                    StringUtil.isNotBlank(t.getHotelPassengerInput().getUid()) ? t.getHotelPassengerInput().getUid() :
                        t.getHotelPassengerInput().getInfoId());
                approvalFlowPassengerInfo.setEmployee(t.getHotelPassengerInput().getEmployee());
                approvalFlowPassengerInfo.setName(
                    OrderCreateProcessorOfUtil.getUseName(t, OrderCreateProcessorOfUtil.getCityId(orderCreateRequest),
                        checkAvailContextResponse, qconfigOfCertificateInitConfig, strategyInfoMap));
                return approvalFlowPassengerInfo;
            }).collect(Collectors.toList());
    }

    /**
     *  国内外产品信息
     * @param checkAvailContextResponse
     * @return
     */
    private String getProductType(QueryCheckAvailContextResponseType checkAvailContextResponse) {

        Boolean isArea =
            Optional.ofNullable(checkAvailContextResponse).map(QueryCheckAvailContextResponseType::getHotelInfo)
                .map(BookHotelInfoEntity::isOversea).orElse(false);
        return isArea ? "INTERNATIONAL_HOTEL" : "CN_HOTEL";
    }

    @Override
    protected ParamCheckResult check(Tuple7<MatchApprovalFlowRequestType, MatchApprovalFlowResponseType,
            OrderCreateRequestType, QueryCheckAvailContextResponseType, BaseCheckAvailInfo,
            QconfigOfCertificateInitConfig, Map<String, StrategyInfo>> tuple4) {

        MatchApprovalFlowResponseType matchApprovalFlowResponseType = tuple4.getT2();

        if (!checkMatchApprovalFlowResponseType(matchApprovalFlowResponseType)) {
            Integer logErrorCode =
                Optional.ofNullable(matchApprovalFlowResponseType).map(MatchApprovalFlowResponseType::getRetCode)
                    .orElse(OrderCreateErrorEnum.MATCH_APPROVAL_FLOW_ERROR.getErrorCode());
            String friendlyMessage = SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessage(
                SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_APPROVE_SERVICE,
                SoaErrorSharkKeyConstant.ACTION_NAME_MATCH_APPROVAL_FLOW, String.valueOf(logErrorCode));
            // 目前客户端无有特殊定制时 使用qConfigOfCodeMappingConfig映射
            Integer errorCode = OrderCreateErrorEnum.MATCH_APPROVAL_FLOW_ERROR.getErrorCode();
            return new ParamCheckResult(false, errorCode, String.valueOf(logErrorCode),
                Optional.ofNullable(matchApprovalFlowResponseType).map(MatchApprovalFlowResponseType::getDescription)
                    .orElse(null), friendlyMessage);
        }
        return null;
    }



    private boolean checkMatchApprovalFlowResponseType(MatchApprovalFlowResponseType matchApprovalFlowResponseType) {
        if (matchApprovalFlowResponseType == null) {
            return false;
        }
        if (!matchApprovalFlowResponseType.isResult()) {
            return false;
        }
        if (matchApprovalFlowResponseType.getFlowTmplInfo() == null || CollectionUtilsExt.isBlank(
            matchApprovalFlowResponseType.getFlowTmplInfo().getFlowNodeTmpls())) {
            return false;
        }
        if (StringUtilsExt.isBlank(matchApprovalFlowResponseType.getFlowTmplInfo().getExternalId())) {
            return false;
        }
        return true;
    }
}
