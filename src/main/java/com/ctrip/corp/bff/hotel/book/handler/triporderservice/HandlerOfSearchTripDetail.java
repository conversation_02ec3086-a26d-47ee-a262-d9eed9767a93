package com.ctrip.corp.bff.hotel.book.handler.triporderservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.soa._21234.SearchTripDetailRequestType;
import com.ctrip.soa._21234.SearchTripDetailResponseType;
import com.ctrip.soa._21234.TripOrderServiceClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/7/8 16:29
 */
@Component
public class HandlerOfSearchTripDetail extends AbstractHandlerOfSOA<SearchTripDetailRequestType, SearchTripDetailResponseType, TripOrderServiceClient> {
    @Override protected String getMethodName() {
        return "searchTripDetail";
    }
}
