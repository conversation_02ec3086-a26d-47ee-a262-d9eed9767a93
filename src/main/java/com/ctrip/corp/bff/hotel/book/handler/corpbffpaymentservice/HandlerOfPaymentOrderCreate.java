package com.ctrip.corp.bff.hotel.book.handler.corpbffpaymentservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.bff.payment.contract.CorpBffPaymentServiceClient;
import com.ctrip.corp.bff.payment.contract.PaymentOrderCreateRequestType;
import com.ctrip.corp.bff.payment.contract.PaymentOrderCreateResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/9/14 13:01
 */
@Component public class HandlerOfPaymentOrderCreate
    extends AbstractHandlerOfSOA<PaymentOrderCreateRequestType, PaymentOrderCreateResponseType, CorpBffPaymentServiceClient> {
    @Override protected String getMethodName() {
        return "paymentOrderCreate";
    }
}
