package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookingcheck;

import com.ctrip.corp.agg.hotel.checkbookinglimition.entity.CheckBookingLimitionResponseType;
import com.ctrip.corp.agg.hotel.checkbookinglimition.entity.ResultType;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.SoaErrorSharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.exception.BookingCheckErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.util.HotelRCUtil;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.CustomizedSharkConfig;
import com.ctrip.corp.bff.hotel.book.service.BookingCheckRequestType;
import com.ctrip.corp.bff.hotel.book.service.BookingCheckResponseType;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/13 21:05
 */
@Component public class MapperOfCheckBookingLimitionResponseType
    extends AbstractMapper<Tuple3<CheckBookingLimitionResponseType, List<CustomizedSharkConfig>,
    BookingCheckRequestType>, BookingCheckResponseType> {
    private static final String CODE_SHIELD_COUNTRY = "F0001";
    private static final String CODE_SHIELD_PROVINCE = "F0002";
    private static final String CODE_SHIELD_CITY = "F0003";
    private static final String CODE_HOTEL_WHITE_LIST = "F0004";
    private static final String CODE_HOTEL_BLACK_LIST = "F0005";
    private static final String CODE_HOTEL_PRODUCT_CONTROL = "F0006";
    private static final String CODE_HOTEL_KEY_WORD = "F0007";
    private static final String CODE_ALL_RESOURCE = "F0008";
    private static final String SHIELD_COUNTRY = "SHIELD_COUNTRY";
    private static final String SHIELD_PROVINCE = "SHIELD_PROVINCE";
    private static final String SHIELD_CITY = "SHIELD_CITY";
    private static final String HOTEL_WHITE_LIST = "HOTEL_WHITE_LIST";
    private static final String HOTEL_BLACK_LIST = "HOTEL_BLACK_LIST";
    private static final String HOTEL_PRODUCT_CONTROL = "HOTEL_PRODUCT_CONTROL";
    private static final String HOTEL_KEY_WORD = "HOTEL_KEY_WORD";
    private static final String ALL_RESOURCE = "ALL_RESOURCE";

    private static final String SHIELD = "SHIELD";
    private static final String HOTEL_LIST = "HOTEL_LIST";
    private static final String HOTEL_CONTROL_PRIVATE = "HOTEL_CONTROL_PRIVATE";
    private static final String PRIVATE = "P";
    private static final String HOTEL_CONTROL_PUBLIC = "HOTEL_CONTROL_PUBLIC";
    private static final String PUBLIC = "C";
    private static final String RESOURCE = "RESOURCE";

    @Override protected BookingCheckResponseType convert(
        Tuple3<CheckBookingLimitionResponseType, List<CustomizedSharkConfig>, BookingCheckRequestType> tuple) {
        return null;
    }

    @Override protected ParamCheckResult check(
        Tuple3<CheckBookingLimitionResponseType, List<CustomizedSharkConfig>, BookingCheckRequestType> tuple) {
        CheckBookingLimitionResponseType checkBookingLimitionResponseType = tuple.getT1();
        List<CustomizedSharkConfig> customizedSharkConfigs = tuple.getT2();
        BookingCheckRequestType bookingCheckRequestType = tuple.getT3();
        if (checkBookingLimitionResponseType == null || CommonConstant.SUCCESS_20000 != TemplateNumberUtil.getValue(
            checkBookingLimitionResponseType.getResponseCode())) {
            Integer logErrorCode = Optional.ofNullable(checkBookingLimitionResponseType)
                .map(CheckBookingLimitionResponseType::getResponseCode)
                .orElse(BookingCheckErrorEnum.CHECK_BOOKING_LIMITION_ERROR.getErrorCode());
            // 目前客户端无特殊定制 如果有使用qConfigOfCodeMappingConfig映射
            Integer errorCode = BookingCheckErrorEnum.CHECK_BOOKING_LIMITION_ERROR.getErrorCode();
            String friendlyMessage = SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessageHasDefault(
                SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_AGG_HOTEL_SALESTRATEGY_SERVICE,
                SoaErrorSharkKeyConstant.ACTION_NAME_CHECK_BOOKING_LIMITION, String.valueOf(logErrorCode));
            return new ParamCheckResult(false, errorCode, String.valueOf(logErrorCode),
                Optional.ofNullable(checkBookingLimitionResponseType)
                    .map(CheckBookingLimitionResponseType::getResponseDesc).orElse(null), friendlyMessage);
        }
        if (CommonConstant.SUCCESS_20000 == TemplateNumberUtil.getValue(
            checkBookingLimitionResponseType.getResponseCode()) && CollectionUtil.isNotEmpty(
            checkBookingLimitionResponseType.getResultList()) && checkBookingLimitionResponseType.getResultList()
            .stream().anyMatch(result -> result != null && RESOURCE.equalsIgnoreCase(result.getType()))) {
            Integer logErrorCode = Optional.ofNullable(checkBookingLimitionResponseType)
                .map(CheckBookingLimitionResponseType::getResponseCode)
                .orElse(BookingCheckErrorEnum.CHECK_BOOKING_LIMITION_ERROR.getErrorCode());
            // 目前客户端无特殊定制 如果有使用qConfigOfCodeMappingConfig映射
            Integer errorCode = BookingCheckErrorEnum.CHECK_BOOKING_LIMITION_ERROR.getErrorCode();
            return new ParamCheckResult(false, errorCode, String.valueOf(logErrorCode),
                Optional.ofNullable(checkBookingLimitionResponseType)
                    .map(CheckBookingLimitionResponseType::getResponseDesc).orElse(null),
                buildFriendlyMessage(checkBookingLimitionResponseType, customizedSharkConfigs,
                    bookingCheckRequestType));
        }
        return null;
    }

    private String buildFriendlyMessage(CheckBookingLimitionResponseType checkBookingLimitionResponseType,
        List<CustomizedSharkConfig> customizedSharkConfigs, BookingCheckRequestType bookingCheckRequestType) {
        if (checkBookingLimitionResponseType == null || checkBookingLimitionResponseType.getResultList() == null
            || CollectionUtil.isEmpty(checkBookingLimitionResponseType.getResultList())) {
            // 接口是按优先级校验的 所以只会命中一种报错
            return SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessageDefault(
                SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_AGG_HOTEL_SALESTRATEGY_SERVICE,
                SoaErrorSharkKeyConstant.ACTION_NAME_CHECK_BOOKING_LIMITION);
        }
        ResultType resultType = checkBookingLimitionResponseType.getResultList().stream()
            .filter(result -> result != null && RESOURCE.equalsIgnoreCase(result.getType()))
            .collect(Collectors.toList()).stream().findFirst().orElse(null);
        if (resultType == null || resultType.getTipInfo() == null) {
            return SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessageDefault(
                SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_AGG_HOTEL_SALESTRATEGY_SERVICE,
                SoaErrorSharkKeyConstant.ACTION_NAME_CHECK_BOOKING_LIMITION);
        }
        String limitScene = buildLimitScene(resultType.getTipInfo().getCode(), resultType.getTipInfo().getExtInfo());
        // 因公因私 无需占位场景
        if (Arrays.asList(HOTEL_CONTROL_PRIVATE, HOTEL_CONTROL_PUBLIC).contains(limitScene)) {
            return SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessage(
                SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_AGG_HOTEL_SALESTRATEGY_SERVICE,
                SoaErrorSharkKeyConstant.ACTION_NAME_CHECK_BOOKING_LIMITION, limitScene);
        }
        if (StringUtil.isBlank(limitScene)) {
            return SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessageDefault(
                SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_AGG_HOTEL_SALESTRATEGY_SERVICE,
                SoaErrorSharkKeyConstant.ACTION_NAME_CHECK_BOOKING_LIMITION);
        }
        String friendlyMessage =
            buildFriendlyMessageCustom(customizedSharkConfigs, bookingCheckRequestType, limitScene);
        if (StringUtil.isBlank(friendlyMessage)) {
            return SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessageDefault(
                SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_AGG_HOTEL_SALESTRATEGY_SERVICE,
                SoaErrorSharkKeyConstant.ACTION_NAME_CHECK_BOOKING_LIMITION);
        }
        if (friendlyMessage.contains("{0}")) {
            return StringUtil.indexedFormat(friendlyMessage,
                buildLimitSceneValue(resultType.getTipInfo().getCode(), resultType.getTipInfo().getExtInfo()));
        }
        return friendlyMessage;
    }

    /**
     * 定制化提示信息
     * key.corp.hotel.CorpAggHotelSaleStrategyService.checkBookingLimition.errorMsg.SHIELD.xx 此页面不支持预订国内酒店
     * @param customizedSharkConfigs
     * @param bookingCheckRequestType
     * @param limitScene
     * @return
     */
    protected String buildFriendlyMessageCustom(List<CustomizedSharkConfig> customizedSharkConfigs,
        BookingCheckRequestType bookingCheckRequestType, String limitScene) {
        String inputKeyPre = StringUtil.indexedFormat(SoaErrorSharkKeyConstant.SOA_ERROR,
            SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_AGG_HOTEL_SALESTRATEGY_SERVICE,
            SoaErrorSharkKeyConstant.ACTION_NAME_CHECK_BOOKING_LIMITION, limitScene);
        String friendlyMessage = HotelRCUtil.getValueByCorpId(inputKeyPre, inputKeyPre,
            bookingCheckRequestType.getIntegrationSoaRequestType().getUserInfo().getCorpId(), "bookingCheck",
            customizedSharkConfigs);
        if (SHIELD.equalsIgnoreCase(limitScene)) {
            return friendlyMessage;
        }
        return SoaErrorSharkKeyConstant.buildSoaErrorFriendlyMessage(
            SoaErrorSharkKeyConstant.SERVICE_NAME_CORP_AGG_HOTEL_SALESTRATEGY_SERVICE,
            SoaErrorSharkKeyConstant.ACTION_NAME_CHECK_BOOKING_LIMITION, limitScene);
    }

    private String buildLimitScene(String code, Map<String, String> extInfo) {
        if (StringUtil.isBlank(code) || CollectionUtil.isEmpty(extInfo)) {
            return null;
        }
        String limitSceneValue = buildLimitSceneValue(code, extInfo);
        if (StringUtil.isBlank(limitSceneValue)) {
            return null;
        }
        switch (code) {
            case CODE_SHIELD_COUNTRY:
            case CODE_SHIELD_PROVINCE:
            case CODE_SHIELD_CITY:
                return SHIELD;
            case CODE_HOTEL_WHITE_LIST:
            case CODE_HOTEL_BLACK_LIST:
                return HOTEL_LIST;
            case CODE_HOTEL_PRODUCT_CONTROL:
                if (PRIVATE.equalsIgnoreCase(limitSceneValue)) {
                    return HOTEL_CONTROL_PRIVATE;
                } else if (PUBLIC.equalsIgnoreCase(limitSceneValue)) {
                    return HOTEL_CONTROL_PUBLIC;
                } else {
                    return null;
                }
            case CODE_HOTEL_KEY_WORD:
                return HOTEL_KEY_WORD;
            case CODE_ALL_RESOURCE:
                return ALL_RESOURCE;
            default:
                return null;
        }
    }

    private String buildLimitSceneValue(String code, Map<String, String> extInfo) {
        if (StringUtil.isBlank(code) || CollectionUtil.isEmpty(extInfo)) {
            return null;
        }
        switch (code) {
            case CODE_SHIELD_COUNTRY:
                return extInfo.get(SHIELD_COUNTRY);
            case CODE_SHIELD_PROVINCE:
                return extInfo.get(SHIELD_PROVINCE);
            case CODE_SHIELD_CITY:
                return extInfo.get(SHIELD_CITY);
            case CODE_HOTEL_WHITE_LIST:
                return extInfo.get(HOTEL_WHITE_LIST);
            case CODE_HOTEL_BLACK_LIST:
                return extInfo.get(HOTEL_BLACK_LIST);
            case CODE_HOTEL_PRODUCT_CONTROL:
                return extInfo.get(HOTEL_PRODUCT_CONTROL);
            case CODE_HOTEL_KEY_WORD:
                return extInfo.get(HOTEL_KEY_WORD);
            case CODE_ALL_RESOURCE:
                return extInfo.get(ALL_RESOURCE);
            default:
                return null;
        }
    }
}
