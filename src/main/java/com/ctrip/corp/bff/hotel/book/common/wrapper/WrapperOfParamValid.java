package com.ctrip.corp.bff.hotel.book.common.wrapper;

import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount.AccountInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.AllocationResultToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.MemberBonusRuleEntry;
import com.ctrip.corp.bff.hotel.book.qconfig.entity.QconfigEntityOfGroupHotelMemberCardRule;
import com.ctrip.corp.corpsz.configuration.common.contract.CustomConfigSearchResponseType;
import com.ctrip.corp.corpsz.corpcustomrequirementservice.common.contract.ExternalDataCheckResponseType;
import com.ctrip.soa._21234.SearchTripDetailResponseType;
import corp.user.service.CorpAccountQueryService.GeneralBatchSearchAccountInfoResponseType;
import corp.user.service.CorpAccountQueryService.QueryIndividualAccountResponseType;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/6/23 19:26
 */
public class WrapperOfParamValid {
    private OrderCreateRequestType orderCreateRequestType;
    private ResourceToken resourceToken;
    private AllocationResultToken allocationResultToken;
    private OrderCreateToken orderCreateToken;
    private AccountInfo accountInfo;
    private QueryCheckAvailContextResponseType queryCheckAvailContextResponseType;
    private SearchTripDetailResponseType searchTripDetailResponseType;
    private QueryIndividualAccountResponseType queryIndividualAccountResponseType;
    private QconfigEntityOfGroupHotelMemberCardRule qconfigEntityOfGroupHotelMemberCardRule;
    private WrapperOfCheckAvail.BaseCheckAvailInfo baseCheckAvailInfo;
    private List<MemberBonusRuleEntry> memberBonusRuleEntries;
    private GeneralBatchSearchAccountInfoResponseType generalBatchSearchAccountInfoResponseType;
    private QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig;

    private ExternalDataCheckResponseType externalDataCheckResponseType;
    private CustomConfigSearchResponseType customConfigSearchResponseType;
    private Map<String, StrategyInfo> strategyInfoMap;


    public static class Build {
        private final WrapperOfParamValid wrapperOfParamValid = new WrapperOfParamValid();

        public Build withOrderCreateRequestType(OrderCreateRequestType orderCreateRequestType) {
            wrapperOfParamValid.orderCreateRequestType = orderCreateRequestType;
            return this;
        }

        public Build withResourceToken(ResourceToken resourceToken) {
            wrapperOfParamValid.resourceToken = resourceToken;
            return this;
        }

        public Build withAllocationResultToken(AllocationResultToken allocationResultToken) {
            wrapperOfParamValid.allocationResultToken = allocationResultToken;
            return this;
        }

        public Build withOrderCreateToken(OrderCreateToken orderCreateToken) {
            wrapperOfParamValid.orderCreateToken = orderCreateToken;
            return this;
        }

        public Build withAccountInfo(AccountInfo accountInfo) {
            wrapperOfParamValid.accountInfo = accountInfo;
            return this;
        }

        public Build withQueryCheckAvailContextResponseType(
            QueryCheckAvailContextResponseType queryCheckAvailContextResponseType) {
            wrapperOfParamValid.queryCheckAvailContextResponseType = queryCheckAvailContextResponseType;
            return this;
        }

        public Build withSearchTripDetailResponseType(SearchTripDetailResponseType searchTripDetailResponseType) {
            wrapperOfParamValid.searchTripDetailResponseType = searchTripDetailResponseType;
            return this;
        }

        public Build withQueryIndividualAccountResponseType(
            QueryIndividualAccountResponseType queryIndividualAccountResponseType) {
            wrapperOfParamValid.queryIndividualAccountResponseType = queryIndividualAccountResponseType;
            return this;
        }

        public Build withQconfigEntityOfGroupHotelMemberCardRule(
            QconfigEntityOfGroupHotelMemberCardRule qconfigEntityOfGroupHotelMemberCardRule) {
            wrapperOfParamValid.qconfigEntityOfGroupHotelMemberCardRule = qconfigEntityOfGroupHotelMemberCardRule;
            return this;
        }

        public Build withBaseCheckAvailInfo(WrapperOfCheckAvail.BaseCheckAvailInfo baseCheckAvailInfo) {
            wrapperOfParamValid.baseCheckAvailInfo = baseCheckAvailInfo;
            return this;
        }

        public Build withMemberBonusRuleEntries(List<MemberBonusRuleEntry> memberBonusRuleEntries) {
            wrapperOfParamValid.memberBonusRuleEntries = memberBonusRuleEntries;
            return this;
        }

        public Build withGeneralBatchSearchAccountInfoResponseType(
            GeneralBatchSearchAccountInfoResponseType generalBatchSearchAccountInfoResponseType) {
            wrapperOfParamValid.generalBatchSearchAccountInfoResponseType = generalBatchSearchAccountInfoResponseType;
            return this;
        }

        public Build withQconfigOfCertificateInitConfig(QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig) {
            wrapperOfParamValid.qconfigOfCertificateInitConfig = qconfigOfCertificateInitConfig;
            return this;
        }

        public Build withExternalDataCheckResponseType(ExternalDataCheckResponseType externalDataCheckResponseType) {
            wrapperOfParamValid.externalDataCheckResponseType = externalDataCheckResponseType;
            return this;
        }

        public Build withCustomConfigSearchResponseType(CustomConfigSearchResponseType customConfigSearchResponseType) {
            wrapperOfParamValid.customConfigSearchResponseType = customConfigSearchResponseType;
            return this;
        }

        public Build withStrategyInfoMap(Map<String, StrategyInfo> strategyInfoMap) {
            wrapperOfParamValid.strategyInfoMap = strategyInfoMap;
            return this;
        }

        public WrapperOfParamValid build() {
            return wrapperOfParamValid;
        }
    }

    public static Build builder() {
        return new WrapperOfParamValid.Build();
    }

    public OrderCreateRequestType getOrderCreateRequestType() {
        return orderCreateRequestType;
    }
    public ResourceToken getResourceToken() {
        return resourceToken;
    }
    public AllocationResultToken getAllocationResultToken() {
        return allocationResultToken;
    }
    public OrderCreateToken getOrderCreateToken() {
        return orderCreateToken;
    }
    public AccountInfo getAccountInfo() {
        return accountInfo;
    }
    public QueryCheckAvailContextResponseType getQueryCheckAvailContextResponseType() {
        return queryCheckAvailContextResponseType;
    }
    public SearchTripDetailResponseType getSearchTripDetailResponseType() {
        return searchTripDetailResponseType;
    }
    public QueryIndividualAccountResponseType getQueryIndividualAccountResponseType() {
        return queryIndividualAccountResponseType;
    }
    public QconfigEntityOfGroupHotelMemberCardRule getQconfigEntityOfGroupHotelMemberCardRule() {
        return qconfigEntityOfGroupHotelMemberCardRule;
    }
    public WrapperOfCheckAvail.BaseCheckAvailInfo getBaseCheckAvailInfo() {
        return baseCheckAvailInfo;
    }
    public List<MemberBonusRuleEntry> getMemberBonusRuleEntries() {
        return memberBonusRuleEntries;
    }
    public GeneralBatchSearchAccountInfoResponseType getGeneralBatchSearchAccountInfoResponseType() {
        return generalBatchSearchAccountInfoResponseType;
    }
    public QconfigOfCertificateInitConfig getQconfigOfCertificateInitConfig() {
        return qconfigOfCertificateInitConfig;
    }

    public ExternalDataCheckResponseType getExternalDataCheckResponseType() {
        return externalDataCheckResponseType;
    }

    public CustomConfigSearchResponseType getCustomConfigSearchResponseType() {
        return customConfigSearchResponseType;
    }

    public Map<String, StrategyInfo> getStrategyInfoMap() {
        return strategyInfoMap;
    }
}
