package com.ctrip.corp.bff.hotel.book.handler.corpauthoritymanageservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.authorityManage.CorpAuthorityManageServiceClient;
import corp.user.service.authorityManage.QueryAuthRequestType;
import corp.user.service.authorityManage.QueryAuthResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2025/7/25 20:19
 */
@Component
public class HandlerOfQueryAuth extends AbstractHandlerOfSOA<QueryAuthRequestType, QueryAuthResponseType, CorpAuthorityManageServiceClient> {
    @Override protected String getMethodName() {
        return "queryAuth";
    }
}
