package com.ctrip.corp.bff.hotel.book.common.enums;

/**
 * <AUTHOR>
 * @Date 2024-8-30 19:07:18
 */
public enum CancelPolicyAttributeTypeEnum {

    /**
     * 取消政策标题
     */
    TITLE("title"),
    /**
     * 取消政策带时间标题
     */
    TITLE_WITH_TIME("titleWithTime"),
    /**
     *
     */
    POS_TIME("POSTime"),
    /**
     * 酒店当地时间
     */
    HOTEL_LOCAL_TIME("hotelLocalTime"),
    /**
     * 取消政策属性
     */
    PROPERTY("property"),

    /**
     * 担保扣款话术
     */
    PAYMENT_DESC("paymentDesc"),

    /**
     * 供应商话术
     */
    SUPPLIER_DESC("supplierDesc"),

    /**
     * 取消政策描述
     */
    CANCEL_DESC("cancelDesc")
    ;

    private final String value;

    CancelPolicyAttributeTypeEnum(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }
}
