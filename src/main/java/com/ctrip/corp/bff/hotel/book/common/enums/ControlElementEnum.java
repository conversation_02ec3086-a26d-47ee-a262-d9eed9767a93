package com.ctrip.corp.bff.hotel.book.common.enums;

import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.hotel.book.common.constant.SharkKeyConstant;

import java.util.Arrays;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2023/01/03 10:55
 * 管控元素枚举
 * 校验字段 预订类型：ORDER_TYPE 出行人：TRAVELER 入住城市：TO_CITIES 入住日期：CHECK_IN 离店日期：CHECK_OUT 价格：PRICE
 * 币种：CURRENCY 星级：STAR 间夜均价：AVERAGE_PRICE 房间数量：ROOM_COUNT 每日房价：ROOM_NIGHT_PRICE 预算金额：BUDGET_AMOUNT
 * 平均单价：AVERAGE_UNIT_PRICE 可定金额：ALLOWED_BOOKING 可定间夜数：ROOM_DAYS 预算金额币种：BUDGET_CURRENCY 酒店id：HOTEL_ID
 * 入住区：LOCATION 有效性次数：VALIDITY_COUNT
 */
public enum ControlElementEnum {
    /**
     * 差标管控模式
     */
    TRAVEL_STAND_CONTROL_MODEL("", "standmodel"),
    /**
     * 剩余预算
     */
    BUDGET_LEFT("", "budgetleft"),
    /**
     * 预订类型 AWR001
     */
    ORDER_TYPE("Hotel.ProductType", "productType"),
    /**
     * 职级 AWR073
     */
    RANK("", "rank"),
    /**
     * 出行人 AWR002
     */
    TRAVELER("Hotel.PassportName", "psgname"),
    /**
     * 入住城市 AWR012
     */
    TO_CITIES("Hotel.DestCityID", "city"),
    /**
     * 入住日期 AWR013
     */
    CHECK_IN("Hotel.StartDate", "checkin"),
    /**
     * 离店日期 AWR014
     */
    CHECK_OUT("Hotel.EndDate", "checkout"),
    /**
     * 价格 AWR003
     */
    PRICE("Hotel.Price", "price"),
    /**
     * 币种 AWR004
     */
    CURRENCY("Hotel.Currency", "currency"),
    /**
     * 星级 AWR031
     */
    STAR("Hotel.StarRating", "star"),
    /**
     * 最大星级 AWR032
     */
    MAX_STAR("", "maxstar"),
    /**
     * 最小星级 AWR033
     */
    MIN_STAR("", "minstar"),
    /**
     * 间夜均价 AWR037
     */
    AVERAGE_PRICE("Hotel.AveragePrice", "averageprice"),
    /**
     * 房间数量 RoomCount
     */
    ROOM_COUNT("Hotel.RoomCount", "roomcount"),
    /**
     * 每日房价
     */
    ROOM_NIGHT_PRICE("Hotel.RoomNightPrice", "nightprice"),
    /**
     * 预算金额
     */
    BUDGET_AMOUNT("BudgetAmount", "budgetamount"),
    /**
     * 平均单价
     */
    AVERAGE_UNIT_PRICE("Hotel.AverageUnitPrice", "averageunitprice"),
    /**
     * 可订金额
     */
    ALLOWED_BOOKING("Hotel.AllowedBooking", "allowedbooking"),
    /**
     * 可订间夜数
     */
    ROOM_DAYS("Hotel.RoomDays", "roomdays"),
    /**
     * 差旅标准
     */
    TRAVEL_STANDARD("", "standard"),
    /**
     * 预算金额币种
     */
    BUDGET_CURRENCY("BudgetCurrency", "budgetcurrency"),
    /**
     * 酒店id管控
     */
    HOTELID("Hotel.HotelID", "hotelid"),
    /**
     * 入住区
     */
    LOCATION("Hotel.DestLocationID", "location"),
    /**
     * 有效性次数
     */
    VALIDITY_COUNT("Hotel.EffectivenessCount", "validitycount"),
    /**
     * 空
     */
    NONE("", "");

    /**
     * code
     */
    private final String fieldName;
    private final String sharkCode;

    ControlElementEnum(String fieldName, String sharkCode) {
        this.fieldName = fieldName;
        this.sharkCode = sharkCode;
    }

    public String getFieldName() {
        return fieldName;
    }

    public String getSharkCode() {
        return sharkCode;
    }

    public String getDescription() {
        return BFFSharkUtil.getSharkValue(
            StringUtil.indexedFormat(SharkKeyConstant.CONTROL_ELEMENT_NAME, getSharkCode()));
    }

    public static ControlElementEnum getControlElementEnum(String fieldName) {
        Optional<ControlElementEnum> result = Arrays.stream(ControlElementEnum.values())
            .filter(o -> StringUtil.equalsIgnoreCase(o.getFieldName(), fieldName)).findFirst();
        return result.orElse(NONE);
    }
}
