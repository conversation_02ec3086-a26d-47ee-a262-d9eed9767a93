package com.ctrip.corp.bff.hotel.book.common.enums;

import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import org.apache.commons.lang3.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-09-12
 */
public enum InvoiceTitleTypeEnum {

    /**
     * 默认值
     */
    NONE("", 999, "", "", ""),

    /**
     * 企业
     */
    ENTERPRISE("enterprise", 1, "CP", "ENTERPRISE", "TITLE_TYPE_E"),

    /**
     * 政府
     */
    GOVERNMENT("government", 2, "NC", "GOVERNMENT", "TITLE_TYPE_G"),

    /**
     * 个人
     */
    PERSONAL("personal", 0, "PS", "INDIVIDUAL", "TITLE_TYPE_P");

    private String description;

    private Integer value;

    /**
     * 订单详情接口吐出的协议酒店发票抬头类型code
     */
    private String cOrderCode;
    private String aggCode;

    private String showCode;

    /**
     * @param description 输出前端的契约
     * @param value
     * @param cOrderCode
     */
    InvoiceTitleTypeEnum(String description, Integer value, String cOrderCode, String aggCode, String showCode) {
        this.description = description;
        this.value = value;
        this.cOrderCode = cOrderCode;
        this.aggCode = aggCode;
        this.showCode = showCode;
    }

    public String getAggCode() {
        return aggCode;
    }

    public String getShowCode() {
        return showCode;
    }

    public static InvoiceTitleTypeEnum getValue(String value) {
        Optional<InvoiceTitleTypeEnum> result = Arrays.stream(InvoiceTitleTypeEnum.values())
                .filter(corpPayType -> StringUtil.equalsIgnoreCase(corpPayType.name(), value)).findFirst();
        return result.orElse(NONE);
    }

    public static InvoiceTitleTypeEnum getValueForAgg(String value) {
        Optional<InvoiceTitleTypeEnum> result = Arrays.stream(InvoiceTitleTypeEnum.values())
                .filter(corpPayType -> StringUtil.equalsIgnoreCase(corpPayType.getAggCode(), value)).findFirst();
        return result.orElse(NONE);
    }


    public static InvoiceTitleTypeEnum convertInvoiceTitleType(String titleType) {
        if (titleType == null) {
            return null;
        }

        switch (titleType) {
            case "1":
            case "CP":
            case "E":
            case "EN":
            case "B":
                return InvoiceTitleTypeEnum.ENTERPRISE;
            case "2":
            case "NC":
            case "G":
                return InvoiceTitleTypeEnum.GOVERNMENT;
            case "0":
            case "PS":
            case "P":
            default:
                return InvoiceTitleTypeEnum.PERSONAL;
        }
    }

    public static InvoiceTitleTypeEnum convertInvoiceTitleTypeEnum(String invoiceTitleType) {
        if (StringUtil.isBlank(invoiceTitleType)) {
            return null;
        }
        switch (invoiceTitleType) {
            case "C":
                return InvoiceTitleTypeEnum.ENTERPRISE;
            case "P":
                return InvoiceTitleTypeEnum.GOVERNMENT;
            case "I":
            default:
                return InvoiceTitleTypeEnum.PERSONAL;
        }
    }

    public static List<String> getAllShowTitleType(InvoiceEnum defaultInvoice) {
        // 数电赚只支持企业
        if (defaultInvoice == InvoiceEnum.D_VAT_INVOICE) {
            return Arrays.stream(InvoiceTitleTypeEnum.values()).filter(
                    invoiceTitleTypeEnum -> StringUtils.isNotBlank(invoiceTitleTypeEnum.getShowCode())
                        && InvoiceTitleTypeEnum.ENTERPRISE.equals(invoiceTitleTypeEnum))
                .map(InvoiceTitleTypeEnum::getShowCode).collect(Collectors.toList());
        }
        // 房费专票只支持企业/政府抬头 不支持个人
        if (defaultInvoice == InvoiceEnum.S_VAT) {
            return Arrays.stream(InvoiceTitleTypeEnum.values()).filter(
                    invoiceTitleTypeEnum -> StringUtils.isNotBlank(invoiceTitleTypeEnum.getShowCode())
                        && !InvoiceTitleTypeEnum.PERSONAL.equals(invoiceTitleTypeEnum))
                .map(InvoiceTitleTypeEnum::getShowCode).collect(Collectors.toList());
        }
        return Arrays.stream(InvoiceTitleTypeEnum.values()).filter(invoiceTitleTypeEnum ->
                StringUtils.isNotBlank(invoiceTitleTypeEnum.getShowCode())).map(InvoiceTitleTypeEnum::getShowCode).collect(Collectors.toList());
    }

    public static List<InvoiceTitleTypeEnum> getFromShowCode(String showCode) {
        List<InvoiceTitleTypeEnum> list = new ArrayList<>();
        for (InvoiceTitleTypeEnum titleTypeEnum : InvoiceTitleTypeEnum.values()) {
            if (Objects.equals(showCode, titleTypeEnum.getShowCode())) {
                list.add(titleTypeEnum);
            }
        }
        return list;
    }
}
