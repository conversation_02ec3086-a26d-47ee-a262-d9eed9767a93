package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response;

import com.ctrip.corp.agg.hotel.roomavailable.entity.BookHotelInfoEntity;
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelItem;
import com.ctrip.corp.agg.hotel.roomavailable.entity.MultipleLanguageText;
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomItem;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ResourceName;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ResourceNameContent;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple6;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.constant.SoaErrorSharkKeyConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum;
import com.ctrip.corp.bff.hotel.book.common.util.CompletionPageUtil;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.contract.BookingInfo;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.bff.hotel.book.contract.ConfirmOrderInfo;
import com.ctrip.corp.bff.hotel.book.contract.FinishInfoOutput;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType;
import com.ctrip.corp.bff.hotel.book.contract.TeamRoomConfirmInfo;
import com.ctrip.corp.bff.hotel.book.qconfig.QConfigOfCodeMappingConfig;
import com.ctrip.corp.foundation.common.enums.LanguageLocaleEnum;
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType;
import com.ctrip.corp.order.data.aggregation.query.contract.OrderOperationResponseType;
import com.ctrip.corp.order.data.aggregation.query.contract.OrderOperationType;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.TreeSet;

/**
 * <AUTHOR>
 * @Description 是否确认订单---策略传入
 * @Date 2024/7/15 14:20
 * @Version 1.0
 */
@Component public class MapperOfConfirmOrderInfoResponse extends
    AbstractMapper<Tuple6<OrderCreateToken, OrderCreateRequestType, QueryCheckAvailContextResponseType, CreateOrderResponseType,
        OrderOperationResponseType, QConfigOfCodeMappingConfig>, Tuple2<Boolean, OrderCreateResponseType>> {

    @Override protected Tuple2<Boolean, OrderCreateResponseType> convert(
        Tuple6<OrderCreateToken, OrderCreateRequestType, QueryCheckAvailContextResponseType, CreateOrderResponseType,
            OrderOperationResponseType, QConfigOfCodeMappingConfig> tuple) {
        OrderCreateToken orderCreateToken = tuple.getT1();
        OrderCreateRequestType requestType = tuple.getT2();
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType = tuple.getT3();
        OrderOperationResponseType orderOperationResponseType = tuple.getT5();
        Tuple2<Boolean, OrderCreateResponseType> responseTypeTuple2 = Tuple2.of(false, new OrderCreateResponseType());
        CreateOrderResponseType createOrderResponseType = tuple.getT4();
        OrderCreateResponseType responseType = new OrderCreateResponseType();
        ConfirmOrderInfo confirmOrderInfo = new ConfirmOrderInfo();
        confirmOrderInfo.setNeedConfirmOrder(BooleanUtil.parseStr(true));
        confirmOrderInfo.setSupportSelfPay(BooleanUtil.parseStr(
            Optional.ofNullable(orderOperationResponseType).map(OrderOperationResponseType::getOperationList)
                .orElse(new ArrayList<>()).stream()
                .filter(o -> OrderCreateProcessorOfUtil.BOOK_PAY.equals(o.getOperationCode())).findFirst()
                .map(OrderOperationType::getOperationAuthorized).orElse(Boolean.FALSE)));
        responseType.setConfirmOrderInfo(confirmOrderInfo);
        orderCreateToken.addContinueTypes(ContinueTypeConst.CONFIRM_ORDER);
        orderCreateToken.setCreateOrderResult(
            OrderCreateProcessorOfUtil.buildCreateOrderResult(createOrderResponseType, requestType, orderCreateToken));
        responseType.setOrderCreateToken(TokenParseUtil.generateToken(orderCreateToken, OrderCreateToken.class));

        BookingInfo bookingInfo = new BookingInfo();
        bookingInfo.setHotelNameInfo(buildHotelNameInfo(queryCheckAvailContextResponseType, requestType));
        bookingInfo.setResourceNameInfo(buildRoomName(queryCheckAvailContextResponseType, requestType));
        responseType.setBookingInfo(bookingInfo);

        FinishInfoOutput finishInfoOutput = new FinishInfoOutput();
        finishInfoOutput.setOrderId(
            String.valueOf(OrderCreateProcessorOfUtil.buildOrderId(orderCreateToken, createOrderResponseType)));
        finishInfoOutput.setCompletionPageUrl(CompletionPageUtil.buildCompletionPageUrl(
            requestType, OrderCreateProcessorOfUtil.buildOrderId(orderCreateToken, createOrderResponseType)));
        responseType.setFinishInfo(finishInfoOutput);
        return Tuple2.of(true, responseType);
    }

    @Override protected ParamCheckResult check(
        Tuple6<OrderCreateToken, OrderCreateRequestType, QueryCheckAvailContextResponseType, CreateOrderResponseType,
                            OrderOperationResponseType, QConfigOfCodeMappingConfig> tuple) {
        OrderCreateToken orderCreateToken = tuple.getT1();
        OrderCreateRequestType requestType = tuple.getT2();
        CreateOrderResponseType createOrderResponseType = tuple.getT4();
        QConfigOfCodeMappingConfig qConfigOfCodeMappingConfig = tuple.getT6();
        if (!orderCreateToken.isUseOrderCreate()) {
            return null;
        }
        if (!Optional.ofNullable(createOrderResponseType).map(CreateOrderResponseType::getResponseCode).orElse(0)
            .equals(CommonConstant.SUCCESS_20000) && !orderCreateToken.containsContinueType(
            ContinueTypeConst.PRICE_CHANGE)) {
            return OrderCreateProcessorOfUtil.buildParamCheckResultCreateOrderError(createOrderResponseType,
                qConfigOfCodeMappingConfig);
        }
        return null;
    }

    /**
     * 酒店名称
     *
     * @return
     */
    private ResourceName buildHotelNameInfo(QueryCheckAvailContextResponseType queryCheckAvailContextResponseType,
        OrderCreateRequestType orderCreateRequestType) {
        ResourceName hotelNameInfo = new ResourceName();
        hotelNameInfo.setEnResourceName(new ResourceNameContent(LanguageLocaleEnum.EN_US.getLanguageLocaleString(),
            Optional.ofNullable(queryCheckAvailContextResponseType.getHotelInfo())
                .map(BookHotelInfoEntity::getHotelName).map(MultipleLanguageText::getTextEn).orElse(null)));
        String language = Optional.ofNullable(orderCreateRequestType.getIntegrationSoaRequestType())
            .map(IntegrationSoaRequestType::getLanguage).orElse(null);
        hotelNameInfo.setLocaleResourceName(new ResourceNameContent(language,
            Optional.ofNullable(queryCheckAvailContextResponseType.getHotelInfo())
                .map(BookHotelInfoEntity::getHotelName).map(MultipleLanguageText::getTextGB).orElse(null)));
        return hotelNameInfo;
    }

    private ResourceName buildRoomName(QueryCheckAvailContextResponseType queryCheckAvailContextResponseType,
        OrderCreateRequestType orderCreateRequestType) {
        ResourceName resourceName = new ResourceName();
        resourceName.setLocaleResourceName(
            new ResourceNameContent(orderCreateRequestType.getIntegrationSoaRequestType().getLanguage(),
                Optional.ofNullable(queryCheckAvailContextResponseType.getRoomInfo().getRoomName())
                    .map(MultipleLanguageText::getTextGB).orElse(null)));
        return resourceName;
    }
}
