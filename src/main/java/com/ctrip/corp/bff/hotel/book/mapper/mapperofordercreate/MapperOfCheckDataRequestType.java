package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate;

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceInput;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CertificateInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.EmailInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.InvoiceInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PhoneInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.common.util.CheckDataUtil;
import com.ctrip.corp.bff.tools.contract.CheckDataRequestType;
import com.ctrip.corp.bff.tools.contract.DataInfo;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/6/23 20:37
 */
@Component public class MapperOfCheckDataRequestType
    extends AbstractMapper<Tuple1<OrderCreateRequestType>, CheckDataRequestType> {
    private static final String TYPE_EMAIL = "email";

    @Override protected CheckDataRequestType convert(Tuple1<OrderCreateRequestType> tuple) {
        OrderCreateRequestType orderCreateRequestType = tuple.getT1();
        CheckDataRequestType checkDataRequestType = new CheckDataRequestType();
        checkDataRequestType.setIntegrationSoaRequestType(orderCreateRequestType.getIntegrationSoaRequestType());
        checkDataRequestType.setDataInfoList(getDataInfoList(orderCreateRequestType));
        return checkDataRequestType;
    }

    @Override protected ParamCheckResult check(Tuple1<OrderCreateRequestType> tuple) {
        return null;
    }

    protected List<DataInfo> addInvoiceEmailData(OrderCreateRequestType orderCreateRequestType) {
        List<DataInfo> dataInfos = new ArrayList<>();
        if (CollectionUtil.isEmpty(orderCreateRequestType.getHotelInvoiceInfos())) {
            return dataInfos;
        }
        orderCreateRequestType.getHotelInvoiceInfos().forEach(i -> {
            if (i == null || StringUtil.isBlank(
                Optional.ofNullable(i.getInvoiceInfo()).map(InvoiceInfo::getEmailInfo).map(EmailInfo::getTransferEmail)
                    .orElse(null))) {
                return;
            }
            DataInfo dataInfo = new DataInfo();
            dataInfo.setData(i.getInvoiceInfo().getEmailInfo().getTransferEmail());
            dataInfo.setType(TYPE_EMAIL);
            dataInfo.setKey(CheckDataUtil.getInvoiceEmailKey(i.getHotelInvoiceType()));
            dataInfos.add(dataInfo);
        });
        return dataInfos;
    }

    protected List<DataInfo> addContactorData(OrderCreateRequestType orderCreateRequestType) {
        List<DataInfo> dataInfos = new ArrayList<>();
        if (orderCreateRequestType.getHotelContactorInfo() == null) {
            return dataInfos;
        }
        if (StringUtil.isNotBlank(Optional.ofNullable(orderCreateRequestType.getHotelContactorInfo().getEmailInfo())
            .map(EmailInfo::getTransferEmail).orElse(null))) {
            DataInfo dataInfo = new DataInfo();
            dataInfo.setData(orderCreateRequestType.getHotelContactorInfo().getEmailInfo().getTransferEmail());
            dataInfo.setType(TYPE_EMAIL);
            dataInfo.setKey(CheckDataUtil.KEY_CONTACT_PSG_EMAIL);
            dataInfos.add(dataInfo);
        }
        if (StringUtil.isNotBlank(Optional.ofNullable(orderCreateRequestType.getHotelContactorInfo().getPhoneInfo())
            .map(PhoneInfo::getTransferPhoneNo).orElse(null))) {
            DataInfo dataInfo = new DataInfo();
            dataInfo.setData(orderCreateRequestType.getHotelContactorInfo().getPhoneInfo().getTransferPhoneNo());
            dataInfo.setType(CheckDataUtil.getPhoneType(
                orderCreateRequestType.getHotelContactorInfo().getPhoneInfo().getCountryCode()));
            dataInfo.setKey(CheckDataUtil.KEY_CONTACT_PSG_PHONE);
            dataInfos.add(dataInfo);
        }
        return dataInfos;
    }

    protected List<DataInfo> addPassengerData(OrderCreateRequestType orderCreateRequestType) {
        List<DataInfo> dataInfos = new ArrayList<>();
        if (CollectionUtil.isEmpty(orderCreateRequestType.getHotelBookPassengerInputs())) {
            return dataInfos;
        }
        orderCreateRequestType.getHotelBookPassengerInputs().forEach(p -> {
            if (p == null) {
                return;
            }
            if (StringUtil.isNotBlank(
                Optional.ofNullable(p.getEmailInfo()).map(EmailInfo::getTransferEmail).orElse(null))) {
                DataInfo dataInfo = new DataInfo();
                dataInfo.setData(p.getEmailInfo().getTransferEmail());
                dataInfo.setType(TYPE_EMAIL);
                dataInfo.setKey(CheckDataUtil.getClientPsgEmailKey(
                    StringUtil.isNotBlank(p.getHotelPassengerInput().getUid()) ? p.getHotelPassengerInput().getUid() :
                        p.getHotelPassengerInput().getInfoId(),
                    orderCreateRequestType.getHotelBookPassengerInputs().indexOf(p)));
                dataInfos.add(dataInfo);
            }
            if (StringUtil.isNotBlank(
                Optional.ofNullable(p.getPhoneInfo()).map(PhoneInfo::getTransferPhoneNo).orElse(null))) {
                DataInfo dataInfo = new DataInfo();
                dataInfo.setData(p.getPhoneInfo().getTransferPhoneNo());
                dataInfo.setType(CheckDataUtil.getPhoneType(p.getPhoneInfo().getCountryCode()));
                dataInfo.setKey(CheckDataUtil.getClientPsgPhoneKey(
                    StringUtil.isNotBlank(p.getHotelPassengerInput().getUid()) ? p.getHotelPassengerInput().getUid() :
                        p.getHotelPassengerInput().getInfoId(),
                    orderCreateRequestType.getHotelBookPassengerInputs().indexOf(p)));
                dataInfos.add(dataInfo);
            }
            if (StringUtil.isNotBlank(
                Optional.ofNullable(p.getCertificateInfo()).map(CertificateInfo::getCertificateNo).orElse(null))) {
                String cardType = CheckDataUtil.getCardType(p.getCertificateInfo().getCertificateType());
                if (cardType != null) {
                    DataInfo dataInfo = new DataInfo();
                    dataInfo.setData(p.getCertificateInfo().getCertificateNo());
                    dataInfo.setType(cardType);
                    dataInfo.setKey(CheckDataUtil.getClientPsgCardKey(
                        StringUtil.isNotBlank(p.getHotelPassengerInput().getUid()) ?
                            p.getHotelPassengerInput().getUid() : p.getHotelPassengerInput().getInfoId(),
                        orderCreateRequestType.getHotelBookPassengerInputs().indexOf(p)));
                    dataInfos.add(dataInfo);
                }
            }
        });
        return dataInfos;
    }

    protected List<DataInfo> addInsurancePassengerData(OrderCreateRequestType orderCreateRequestType) {
        List<DataInfo> dataInfos = new ArrayList<>();
        if (CollectionUtil.isEmpty(Optional.ofNullable(orderCreateRequestType.getHotelInsuranceInput())
            .map(HotelInsuranceInput::getHotelInsuranceDetailInputs).orElse(null))) {
            return dataInfos;
        }
        orderCreateRequestType.getHotelInsuranceInput().getHotelInsuranceDetailInputs().forEach(i -> {
            if (i == null) {
                return;
            }
            if (CollectionUtil.isEmpty(i.getInsuranceHotelBookPassengerInputs())) {
                return;
            }
            i.getInsuranceHotelBookPassengerInputs().forEach(p -> {
                if (p == null) {
                    return;
                }
                if (StringUtil.isNotBlank(
                    Optional.ofNullable(p.getEmailInfo()).map(EmailInfo::getTransferEmail).orElse(null))) {
                    DataInfo dataInfo = new DataInfo();
                    dataInfo.setData(p.getEmailInfo().getTransferEmail());
                    dataInfo.setType(TYPE_EMAIL);
                    dataInfo.setKey(CheckDataUtil.getInsurancePsgEmailKey(
                        StringUtil.isNotBlank(p.getHotelPassengerInput().getUid()) ?
                            p.getHotelPassengerInput().getUid() : p.getHotelPassengerInput().getInfoId(),
                        i.getInsuranceHotelBookPassengerInputs().indexOf(p),
                        orderCreateRequestType.getHotelInsuranceInput().getHotelInsuranceDetailInputs().indexOf(i)));
                    dataInfos.add(dataInfo);
                }
                if (StringUtil.isNotBlank(
                    Optional.ofNullable(p.getPhoneInfo()).map(PhoneInfo::getTransferPhoneNo).orElse(null))) {
                    DataInfo dataInfo = new DataInfo();
                    dataInfo.setData(p.getPhoneInfo().getTransferPhoneNo());
                    dataInfo.setType(CheckDataUtil.getPhoneType(p.getPhoneInfo().getCountryCode()));
                    dataInfo.setKey(CheckDataUtil.getInsurancePsgPhoneKey(
                        StringUtil.isNotBlank(p.getHotelPassengerInput().getUid()) ?
                            p.getHotelPassengerInput().getUid() : p.getHotelPassengerInput().getInfoId(),
                        i.getInsuranceHotelBookPassengerInputs().indexOf(p),
                        orderCreateRequestType.getHotelInsuranceInput().getHotelInsuranceDetailInputs().indexOf(i)));
                    dataInfos.add(dataInfo);
                }
                if (StringUtil.isNotBlank(
                    Optional.ofNullable(p.getCertificateInfo()).map(CertificateInfo::getCertificateNo).orElse(null))) {
                    String cardType = CheckDataUtil.getCardType(p.getCertificateInfo().getCertificateType());
                    if (cardType != null) {
                        DataInfo dataInfo = new DataInfo();
                        dataInfo.setData(p.getCertificateInfo().getCertificateNo());
                        dataInfo.setType(cardType);
                        dataInfo.setKey(CheckDataUtil.getInsurancePsgCardKey(
                            StringUtil.isNotBlank(p.getHotelPassengerInput().getUid()) ?
                                p.getHotelPassengerInput().getUid() : p.getHotelPassengerInput().getInfoId(),
                            i.getInsuranceHotelBookPassengerInputs().indexOf(p),
                            orderCreateRequestType.getHotelInsuranceInput().getHotelInsuranceDetailInputs()
                                .indexOf(i)));
                        dataInfos.add(dataInfo);
                    }
                }
            });
        });
        return dataInfos;
    }

    private List<DataInfo> getDataInfoList(OrderCreateRequestType orderCreateRequestType) {
        List<DataInfo> dataInfos = new ArrayList<>();
        // 发票邮箱
        dataInfos.addAll(addInvoiceEmailData(orderCreateRequestType));
        // 联系人信息
        dataInfos.addAll(addContactorData(orderCreateRequestType));
        // 入住人信息
        dataInfos.addAll(addPassengerData(orderCreateRequestType));
        // 保险人信息
        dataInfos.addAll(addInsurancePassengerData(orderCreateRequestType));
        return dataInfos;
    }
}
