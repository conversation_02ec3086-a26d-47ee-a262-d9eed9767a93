package com.ctrip.corp.bff.hotel.book.handler.orderpaymentcentertransactionservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.soa._21685.OrderPaymentCenterTransactionServiceClient;
import com.ctrip.soa._21685.TransactionPayUrlRequestType;
import com.ctrip.soa._21685.TransactionPayUrlResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Description 双付丰享支付地址查询
 * @Date 2024/10/22 17:24
 * @Version 1.0
 */
@Component public class HandlerOfTransactionPayUrl extends
    AbstractHandlerOfSOA<TransactionPayUrlRequestType, TransactionPayUrlResponseType,
        OrderPaymentCenterTransactionServiceClient> {

    @Override protected String getMethodName() {
        return "transactionPayUrl";
    }
}
