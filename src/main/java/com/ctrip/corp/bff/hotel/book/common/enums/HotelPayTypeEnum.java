package com.ctrip.corp.bff.hotel.book.common.enums;

import com.ctrip.corp.bff.framework.template.common.defalut.LogDefaultUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;

import java.util.Arrays;
import java.util.Optional;

/**
 * @Author: chenchuang
 * @Date: 2024/8/29 14:22
 * @Description: 支付方式枚举
 * 房费支付方式 CORP_PAY：公司账户,SELF_PAY：个人支付,MIX_PAY：混付（随心订），UNION_PAY：银联支付，FLASH_STAY_PAY：闪住，CASH：现付无需担保, GUARANTEE_CORP_PAY：公帐担保, GUARANTEE_SELF_PAY：个人担保
 * 服务费支付方式 CORP_PAY：公司账户,SELF_PAY：个人支付
 */
public enum HotelPayTypeEnum {
    /**
     * 空值
     */
    NONE("NONE", "none"),

    /**
     * 公务卡支付
     */
    CORPORATE_CARD_PAY("CORPORATE_CARD_PAY", "CORPORATE_CARD_PAY"),

    /**
     * 公司账户支付
     */
    CORP_PAY("CORP_PAY", "corp"),

    /**
     * 个人支付
     */
    SELF_PAY("SELF_PAY", "ccard"),

    /**
     * 混付：(随心订)
     */
    MIX_PAY("MIX_PAY", "mixed"),


    /**
     * 公司账户预付
     */
    ADVANCE_PAY("ADVANCE_PAY", "advance"),

    /**
     * 现付无需担保
     */
    CASH("CASH", "cash"),

    /**
     * 银联支付
     */
    UNION_PAY("UNION_PAY", "UNION_PAY"),
    /**
     * 闪住
     */
    FLASH_STAY_PAY("FLASH_STAY_PAY", "FLASH_STAY_PAY"),
    /**
     * 现付公司担保
     */
    GUARANTEE_CORP_PAY("GUARANTEE_CORP_PAY", null),
    /**
     * 现付个人担保
     */
    GUARANTEE_SELF_PAY("GUARANTEE_SELF_PAY", null),
    /**
     * 虚拟支付
     */
    PRBAL("PRBAL", "PRBAL"),

    /**
     * 公司信用卡担保
     */
    CORP_CREDIT_CARD_GUARANTEE("CORP_CREDIT_CARD_GUARANTEE", "CORP_CREDIT_CARD_GUARANTEE"),
    ;

    /**
     * code
     */
    private String code;

    private String orderPayTypeCode;

    HotelPayTypeEnum(String code, String orderPayTypeCode) {
        this.code = code;
        this.orderPayTypeCode = orderPayTypeCode;
    }

    public String getCode() {
        return code;
    }

    public String getOrderPayTypeCode() {
        return orderPayTypeCode;
    }

    public static HotelPayTypeEnum getValue(String value) {
        Optional<HotelPayTypeEnum> result =
                Arrays.stream(HotelPayTypeEnum.values()).filter(corpPayType -> corpPayType.getCode().equalsIgnoreCase(value)).findFirst();
        return result.orElse(NONE);
    }

    public static HotelPayTypeEnum getValueByOrder(String value) {
        if (StringUtil.isEmpty(value)) {
            return HotelPayTypeEnum.NONE;
        }
        Optional<HotelPayTypeEnum> result =
            Arrays.stream(HotelPayTypeEnum.values()).filter(corpPayType -> StringUtil.equalsIgnoreCase(corpPayType.getOrderPayTypeCode(), value)).findFirst();
        return result.orElse(NONE);
    }

    public static boolean isCorpPay(String payType) {
        return CORP_PAY.getCode().equals(payType) || FLASH_STAY_PAY.getCode().equals(payType);
    }

    public static boolean isCorpCardPay(String payType) {
        return CORPORATE_CARD_PAY.getCode().equals(payType);
    }


    public boolean isCorpPay() {
        return CORP_PAY.getCode().equals(this.getCode()) || FLASH_STAY_PAY.getCode().equals(this.getCode());
    }

    public  boolean isSelfPay() {
        return SELF_PAY.getCode().equals(this.getCode()) || UNION_PAY.getCode().equals(this.getCode());
    }

    public static boolean isSelfPay(String payType) {
        return SELF_PAY.getCode().equals(payType) || UNION_PAY.getCode().equals(payType);
    }

    public static boolean isMixPay(String payType) {
        return MIX_PAY.getCode().equals(payType);
    }

    public  boolean isMixPay() {
        return MIX_PAY.getCode().equals(this.getCode());
    }

    public  boolean isFlashPay() {
        return FLASH_STAY_PAY.getCode().equals(this.getCode());
    }

    public static String getCheckTravelPolicyPayType(HotelPayTypeEnum hotelPayTypeEnum) {

        if (hotelPayTypeEnum == null) {
            return "UNKNOWN";
        }
        switch (hotelPayTypeEnum) {
            case CORP_PAY:
            case ADVANCE_PAY:
            case FLASH_STAY_PAY:
                return "CORP_PAY";
            case CORPORATE_CARD_PAY:
                return "CORPORATE_CARD_PAY";
            case SELF_PAY:
            case CASH:
            case UNION_PAY:
                return "PERSONAL_PAY";
            case MIX_PAY:
                return "MIX_PAY";
            default:
                LogDefaultUtil.bizDefaultVal("UNKNOWN", "NewCheckTravelPolicyBiz.getCheckTravelPolicyPayType",
                        "bffPayTypeEnum is " + hotelPayTypeEnum.name());
                return "UNKNOWN";
        }
    }
}
