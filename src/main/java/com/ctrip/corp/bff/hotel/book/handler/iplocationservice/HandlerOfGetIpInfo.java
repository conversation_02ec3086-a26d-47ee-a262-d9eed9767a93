package com.ctrip.corp.bff.hotel.book.handler.iplocationservice;

import com.ctrip.basebiz.iplocation.proto.GetIpInfoRequestTypeV2;
import com.ctrip.basebiz.iplocation.proto.GetIpInfoResponseTypeV2;
import com.ctrip.basebiz.iplocation.proto.IpLocationServiceClient;
import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import org.springframework.stereotype.Component;

/**
 * @Author: junlongma
 * @Description 获取ip信息
 * @Date: 2024-11-21 13:27:20
 */
@Component
public class HandlerOfGetIpInfo extends
    AbstractHandlerOfSOA<GetIpInfoRequestTypeV2, GetIpInfoResponseTypeV2, IpLocationServiceClient> {

    @Override
    protected String getMethodName() {
        return "getIpInfo";
    }
}
