package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response;

import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail.BaseCheckAvailInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType;
import com.ctrip.corp.bff.hotel.book.contract.TeamRoomConfirmInfo;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;

/**
 * <AUTHOR>
 * @Description 团队房确认=入住人有重名+勾选团队房
 * @Date 2024/7/15 14:20
 * @Version 1.0
 */
@Component public class MapperOfTeamRoomConfirmInfoResponse extends
    AbstractMapper<Tuple5<OrderCreateToken, OrderCreateRequestType, BaseCheckAvailInfo,
            QconfigOfCertificateInitConfig,
            Map<String, StrategyInfo>>, Tuple2<Boolean, OrderCreateResponseType>> {

    @Override protected Tuple2<Boolean, OrderCreateResponseType> convert(
        Tuple5<OrderCreateToken, OrderCreateRequestType, WrapperOfCheckAvail.BaseCheckAvailInfo,
            QconfigOfCertificateInitConfig,
            Map<String, StrategyInfo>> tuple) {
        OrderCreateToken orderCreateToken = tuple.getT1();
        OrderCreateRequestType requestType = tuple.getT2();
        WrapperOfCheckAvail.BaseCheckAvailInfo queryCheckAvailContextResponseType = tuple.getT3();
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig = tuple.getT4();
        Map<String, StrategyInfo> strategyInfoMap = tuple.getT5();
        Tuple2<Boolean, OrderCreateResponseType> responseTypeTuple2 = Tuple2.of(false, new OrderCreateResponseType());
        if (requestType.getTeamRoomInfo() == null) {
            return responseTypeTuple2;
        }
        if (orderCreateToken.containsContinueType(ContinueTypeConst.TEAM_ROOM)) {
            return responseTypeTuple2;
        }
        if (!BooleanUtil.parseStr(true).equalsIgnoreCase(requestType.getTeamRoomInfo().getTeamRoom())) {
            return responseTypeTuple2;
        }
        if (!checkClientPsgSame(requestType, queryCheckAvailContextResponseType, qconfigOfCertificateInitConfig, strategyInfoMap)) {
            return responseTypeTuple2;
        }
        OrderCreateResponseType responseType = new OrderCreateResponseType();
        TeamRoomConfirmInfo teamRoomConfirmInfo = new TeamRoomConfirmInfo();
        teamRoomConfirmInfo.setNeedTeamRoomConfirm(BooleanUtil.parseStr(true));
        responseType.setTeamRoomConfirmInfo(teamRoomConfirmInfo);
        orderCreateToken.addContinueTypes(ContinueTypeConst.TEAM_ROOM);
        responseType.setOrderCreateToken(TokenParseUtil.generateToken(orderCreateToken, OrderCreateToken.class));
        return Tuple2.of(true, responseType);
    }

    @Override protected ParamCheckResult check(
        Tuple5<OrderCreateToken, OrderCreateRequestType, WrapperOfCheckAvail.BaseCheckAvailInfo,
            QconfigOfCertificateInitConfig,
            Map<String, StrategyInfo>> tuple) {
        return null;
    }

    protected boolean checkClientPsgSame(OrderCreateRequestType orderCreateRequestType,
        WrapperOfCheckAvail.BaseCheckAvailInfo queryCheckAvailContextResponseType,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        Map<String, StrategyInfo> strategyInfoMap) {
        List<String> names = new ArrayList<>();
        Set<String> nameSet = new TreeSet<>();
        orderCreateRequestType.getHotelBookPassengerInputs().forEach(p -> {
            if (p == null) {
                return;
            }
            String useNameCnOrEn =
                OrderCreateProcessorOfUtil.getUseName(p, orderCreateRequestType.getCityInput().getCityId(),
                    queryCheckAvailContextResponseType, qconfigOfCertificateInitConfig, strategyInfoMap);
            names.add(useNameCnOrEn);
            nameSet.add(useNameCnOrEn);
        });
        if (names.size() != nameSet.size()) {
            return true;
        }
        return false;
    }
}
