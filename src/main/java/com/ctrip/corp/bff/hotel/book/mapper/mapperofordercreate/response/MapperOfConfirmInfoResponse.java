package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response;

import com.ctrip.corp.bff.framework.hotel.entity.contract.ConfirmDetailExtend;
import com.ctrip.corp.bff.framework.hotel.entity.contract.ConfirmDetailInfo;
import com.ctrip.corp.bff.framework.hotel.entity.contract.ConfirmInfo;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken;
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CertificateInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple6;
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateCertificateTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil;
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail;
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail.BaseCheckAvailInfo;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType;
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType;
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig;
import ctrip.BBZ.members.bbzmbrCommonPassenger.CommonPassenger;
import ctrip.BBZ.members.bbzmbrCommonPassenger.CommonPassengerCard;
import ctrip.BBZ.members.bbzmbrCommonPassenger.GetCommonPassengerResponseType;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description 变价响应
 * @Date 2024/7/15 14:20
 * @Version 1.0
 */
@Component public class MapperOfConfirmInfoResponse extends
    AbstractMapper<Tuple6<List<GetCommonPassengerResponseType>, OrderCreateToken, OrderCreateRequestType,
            BaseCheckAvailInfo, QconfigOfCertificateInitConfig,
        Map<String, StrategyInfo>>, Tuple2<Boolean, OrderCreateResponseType>> {

    private static final String INFOID_REMIND = "INFOID_REMIND";
    private static final String INFOID_REAL_NAME = "{0}_REAL_NAME";
    private static final String INFOID_ORDER_NAME = "{0}_ORDER_NAME";

    private static final String INFOID_REAL_CERTIFICATE = "{0}_REAL_CERTIFICATE.{1}";
    private static final String INFOID_ORDER_CERTIFICATE = "{0}_ORDER_CERTIFICATE.{1}";

    @Override protected Tuple2<Boolean, OrderCreateResponseType> convert(
        Tuple6<List<GetCommonPassengerResponseType>, OrderCreateToken, OrderCreateRequestType, BaseCheckAvailInfo,
                    QconfigOfCertificateInitConfig,
                    Map<String, StrategyInfo>> tuple) {
        List<CommonPassenger> commonPassengerList = buildCommonPassengerList(tuple.getT1());
        OrderCreateToken orderCreateToken = tuple.getT2();
        OrderCreateRequestType orderCreateRequestType = tuple.getT3();
        WrapperOfCheckAvail.BaseCheckAvailInfo baseCheckAvailInfo = tuple.getT4();
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig = tuple.getT5();
        OrderCreateResponseType orderCreateResponseType = new OrderCreateResponseType();
        ConfirmInfo confirmInfo = buildConfirmInfo(baseCheckAvailInfo, commonPassengerList, orderCreateRequestType,
            qconfigOfCertificateInitConfig, tuple.getT6());
        if (confirmInfo == null) {
            return Tuple2.of(false, orderCreateResponseType);
        }
        orderCreateResponseType.setConfirmInfo(confirmInfo);
        orderCreateResponseType.setOrderCreateToken(buildOrderCreateToken(orderCreateToken));
        return Tuple2.of(true, orderCreateResponseType);
    }

    @Override protected ParamCheckResult check(
        Tuple6<List<GetCommonPassengerResponseType>, OrderCreateToken, OrderCreateRequestType, WrapperOfCheckAvail.BaseCheckAvailInfo,
            QconfigOfCertificateInitConfig,
            Map<String, StrategyInfo>> tuple) {
        return null;
    }

    private List<CommonPassenger> buildCommonPassengerList(
        List<GetCommonPassengerResponseType> getCommonPassengerResponseTypes) {
        if (CollectionUtil.isEmpty(getCommonPassengerResponseTypes)) {
            return new ArrayList<>();
        }
        return getCommonPassengerResponseTypes.stream().map(GetCommonPassengerResponseType::getCommonPassengers)
            .filter(CollectionUtil::isNotEmpty).flatMap(List::stream).collect(Collectors.toList());
    }

    private String buildOrderCreateToken(OrderCreateToken orderCreateToken) {
        orderCreateToken.addContinueTypes(ContinueTypeConst.INFOID_REMIND);
        return TokenParseUtil.generateToken(orderCreateToken, OrderCreateToken.class);
    }

    private ConfirmInfo buildConfirmInfo(WrapperOfCheckAvail.BaseCheckAvailInfo baseCheckAvailInfo,
        List<CommonPassenger> commonPassengerList, OrderCreateRequestType orderCreateRequestType,
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig,
        Map<String, StrategyInfo> strategyInfoMap) {
        if (CollectionUtil.isEmpty(commonPassengerList) || CollectionUtil.isEmpty(
            orderCreateRequestType.getHotelBookPassengerInputs())) {
            return null;
        }

        ConfirmInfo confirmInfo = new ConfirmInfo();
        List<ConfirmDetailInfo> confirmDetailInfos = new ArrayList<>();
        ConfirmDetailInfo confirmDetailInfo = new ConfirmDetailInfo();
        confirmDetailInfo.setCode(INFOID_REMIND);
        List<ConfirmDetailExtend> confirmDetailExtends = new ArrayList<>();
        confirmDetailInfo.setConfirmDetailExtends(confirmDetailExtends);
        confirmDetailInfos.add(confirmDetailInfo);
        confirmInfo.setConfirmDetailInfos(confirmDetailInfos);
        orderCreateRequestType.getHotelBookPassengerInputs().stream().forEach(hotelBookPassengerInput -> {
            if (hotelBookPassengerInput == null) {
                return;
            }
            if (hotelBookPassengerInput.getHotelPassengerInput() == null) {
                return;
            }
            if (BooleanUtil.parseStr(true)
                .equalsIgnoreCase(hotelBookPassengerInput.getHotelPassengerInput().getEmployee())) {
                return;
            }
            String infoIdInput = hotelBookPassengerInput.getHotelPassengerInput().getInfoId();
            if (StringUtil.isBlank(infoIdInput)) {
                return;
            }
            CommonPassenger commonPassenger =
                commonPassengerList.stream().filter(item -> item != null && infoIdInput.equals(item.getPassengerID()))
                    .findFirst().orElse(null);
            if (commonPassenger == null) {
                return;
            }
            String commonPassengerCNName = commonPassenger.getCNName();
            String commonPassengerENName =
                buildNameEN(commonPassenger.getENFirstName(), commonPassenger.getENLastName(),
                    commonPassenger.getENMiddleName());
            if (StringUtil.isBlank(commonPassengerCNName) && StringUtil.isBlank(commonPassengerENName)) {
                return;
            }
            String hotelPassengerUseNameInput = OrderCreateProcessorOfUtil.getUseName(hotelBookPassengerInput,
                orderCreateRequestType.getCityInput().getCityId(), baseCheckAvailInfo, qconfigOfCertificateInitConfig,
                strategyInfoMap);
            boolean useEname = OrderCreateProcessorOfUtil.usePsgEname(hotelBookPassengerInput,
                orderCreateRequestType.getCityInput().getCityId(), baseCheckAvailInfo, qconfigOfCertificateInitConfig,
                strategyInfoMap);
            if (useEname) {
                if (!StringUtil.equalsIgnoreCase(commonPassengerENName, hotelPassengerUseNameInput)) {
                    confirmDetailExtends.add(buildConfirmDetailExtend(INFOID_REAL_NAME, commonPassengerENName,
                        hotelBookPassengerInput.getHotelPassengerInput().getInfoId(), null));
                    confirmDetailExtends.add(buildConfirmDetailExtend(INFOID_ORDER_NAME, hotelPassengerUseNameInput,
                        hotelBookPassengerInput.getHotelPassengerInput().getInfoId(), null));
                }
            } else {
                if (!StringUtil.equalsIgnoreCase(commonPassengerCNName, hotelPassengerUseNameInput)) {
                    confirmDetailExtends.add(buildConfirmDetailExtend(INFOID_REAL_NAME, commonPassengerCNName,
                        hotelBookPassengerInput.getHotelPassengerInput().getInfoId(), null));
                    confirmDetailExtends.add(buildConfirmDetailExtend(INFOID_ORDER_NAME, hotelPassengerUseNameInput,
                        hotelBookPassengerInput.getHotelPassengerInput().getInfoId(), null));
                }
            }
            if (CollectionUtil.isEmpty(confirmDetailExtends)) {
                return;
            }
            // 存在不一致姓名的非员工时 有证件则需要展示证件信息 无证件无需展示
            if (hotelBookPassengerInput.getCertificateInfo() == null) {
                return;
            }
            if (StringUtil.isBlank(hotelBookPassengerInput.getCertificateInfo().getTransferCertificateNo())) {
                return;
            }
            String certificateTypeInput = Optional.ofNullable(hotelBookPassengerInput.getCertificateInfo())
                .map(CertificateInfo::getCertificateType).orElse(null);
            if (StringUtil.isBlank(certificateTypeInput)) {
                return;
            }
            if (CollectionUtil.isEmpty(commonPassenger.getCommonPassengerCardList())) {
                return;
            }
            CommonPassengerCard commonPassengerCard = commonPassenger.getCommonPassengerCardList().stream()
                .filter(item -> item != null && buildCertificateTypeEquals(certificateTypeInput, item.getCardType()))
                .findFirst().orElse(null);
            if (commonPassengerCard == null) {
                return;
            }
            confirmDetailExtends.add(buildConfirmDetailExtend(INFOID_REAL_CERTIFICATE, commonPassengerCard.getCardNo(),
                hotelBookPassengerInput.getHotelPassengerInput().getInfoId(), certificateTypeInput));
            confirmDetailExtends.add(buildConfirmDetailExtend(INFOID_ORDER_CERTIFICATE,
                hotelBookPassengerInput.getCertificateInfo().getTransferCertificateNo(),
                hotelBookPassengerInput.getHotelPassengerInput().getInfoId(), certificateTypeInput));
        });
        return CollectionUtil.isEmpty(confirmDetailInfo.getConfirmDetailExtends()) ? null : confirmInfo;
    }

    /**
     * @param certificateTypeInput 证件类型 * IDENTITY_CARD 身份证 * PASSPORT 护照 * STUDENT_ID_CARD 学生证 * MILITARY_CARD
     *                             军人证 * DRIVING_LICENSE 驾驶证 * HOMEPERMIT 回乡证 * MTP 台胞证 * OTHERDOCUMENT
     *                             其它 * HKMACPASS 港澳通行证 * SEAMAN_CARD 国际海员证 * FOREIGNER_PERMANENT_RESIDENCE_CARD
     *                             外国人永久居留证 * TAIWANPASS 台湾通行证 * TRAVELDOCUMENT
     *                             旅行证 * FOREIGNER_PERMANENT_RESIDENT_ID_CARD
     *                             外国人永久居留身份证 * RESIDENCEPERMITHKT 港澳台居民居住证 * OVERSEA_AND_LOCAL_TRAVEL_CARD
     *                             海外当地旅行证件 * RUSSIA_DOMESTIC_PASSPORT 俄籍国内护照
     * @param cardType             证件类型:0 出生日期;1 身份证;2 护照;3 学生证;4 军人证;6 驾驶证;7 回乡证;8 台胞证;10 港澳通行证;
     *                             11 国际海员证;20 外国人永久居留证;21 旅行证;22 台湾通行证;23 士兵证;24 临时身份证;25 户口簿;
     *                             26 警官证;27 出生证明;99 其它
     * @return
     */
    protected boolean buildCertificateTypeEquals(String certificateTypeInput, String cardType) {
        if (StringUtil.isBlank(certificateTypeInput) || StringUtil.isBlank(cardType)) {
            return false;
        }
        if (TemplateNumberUtil.isZeroOrNull(TemplateNumberUtil.parseInt(cardType))) {
            return false;
        }
        OrderCreateCertificateTypeEnum commonPassengerCardType =
            OrderCreateCertificateTypeEnum.findByCertificateTypeById(TemplateNumberUtil.parseInt(cardType));
        if (commonPassengerCardType == null) {
            return false;
        }
        OrderCreateCertificateTypeEnum certificateType =
            OrderCreateCertificateTypeEnum.findByCertificateType(certificateTypeInput);
        if (certificateType == null) {
            return false;
        }
        return commonPassengerCardType.equals(certificateType);
    }

    protected ConfirmDetailExtend buildConfirmDetailExtend(String key, String value, String infoId,
        String certificateTypeInput) {
        ConfirmDetailExtend confirmDetailExtend = new ConfirmDetailExtend();
        if (StringUtil.isNotBlank(infoId) && StringUtil.isNotBlank(certificateTypeInput)) {
            confirmDetailExtend.setKey(StringUtil.indexedFormat(key, infoId, certificateTypeInput));
        } else if (StringUtil.isNotBlank(infoId)) {
            confirmDetailExtend.setKey(StringUtil.indexedFormat(key, infoId));
        } else {
            confirmDetailExtend.setKey(key);
        }
        confirmDetailExtend.setValue(value);
        return confirmDetailExtend;
    }

    // 本来应该同老项目domain包的 CommonConvert.getNameEN逻辑 但是老逻辑看起来是有毛病的
    protected String buildNameEN(String nameENFirstName, String nameENLastName, String nameENMiddleName) {
        return OrderCreateProcessorOfUtil.getEname(nameENFirstName, nameENLastName, nameENMiddleName);
    }
}
