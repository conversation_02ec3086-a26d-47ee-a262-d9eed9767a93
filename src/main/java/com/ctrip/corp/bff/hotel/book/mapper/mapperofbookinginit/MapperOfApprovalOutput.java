package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount;
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfSearchApproval;
import com.ctrip.corp.bff.framework.hotel.entity.contract.ApprovalOutput;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.OrderResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken;
import com.ctrip.corp.bff.framework.hotel.resourcetoken.utils.ResourceTokenUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.MiceInput;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ResourceTokenInfo;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple7;
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelBalanceTypeEnum;
import com.ctrip.corp.bff.hotel.book.common.enums.TripApplicationEnum;
import com.ctrip.corp.bff.hotel.book.common.util.TokenInfoGetUtil;
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType;
import com.ctrip.corp.bff.specific.contract.ApprovalInfo;
import com.ctrip.corp.bff.specific.contract.ApprovalTextInfoResponseType;
import com.ctrip.corp.bff.specific.contract.BatchApprovalDefaultResponseType;
import com.ctrip.corp.bff.specific.contract.TextInfoItem;
import com.ctrip.corp.order.data.aggregation.query.contract.ApprovalType;
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataResponseType;
import com.ctrip.corp.order.data.aggregation.query.contract.TravelInfoType;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

/**
 * <AUTHOR>
 * @desc 获取默认审批单，按照优先级如下
 * 1.如果是紧急预定，直接返回紧急预定
 * 2.如果是提前审批前置模式，直接返回前端传入的审批单
 * 3.如果是修改场景，根据订单号查询到原单信息，如果是有效审批单，则返回
 * 4.查询公共默认审批单接口
 * 5.都不满足，不默认
 * @date 2024/6/23 18:31
 */
@Component
public class MapperOfApprovalOutput extends AbstractMapper<Tuple7<BookingInitRequestType, BatchApprovalDefaultResponseType, WrapperOfSearchApproval.ApprovalInfo,
        QueryHotelOrderDataResponseType, ResourceToken, WrapperOfAccount.AccountInfo, ApprovalTextInfoResponseType>, ApprovalOutput> {

    private static final int EMERGENCY = 3;

    private static final String TRUE = "T";
    
    private static final String FALSE = "F";
    /**
     * 现付
     */
    private static final String CASH = "cash";
    /**
     * 信用卡
     */
    private static final String CCRAD = "ccard";
    /**
     * 紧急预定标题（例如：请选择本人出差单或为他人预定）
     */
    private static final String EMERGENCY_TITLE = "EMERGENCY_TITLE";

    @Override
    protected ApprovalOutput convert(Tuple7<BookingInitRequestType, BatchApprovalDefaultResponseType, WrapperOfSearchApproval.ApprovalInfo,
            QueryHotelOrderDataResponseType, ResourceToken, WrapperOfAccount.AccountInfo, ApprovalTextInfoResponseType> param) {
        BookingInitRequestType bookingInitRequestType = param.getT1();
        ApprovalInput approvalInput = bookingInitRequestType.getApprovalInput();
        BatchApprovalDefaultResponseType approvalDefaultResponseType = param.getT2();
        WrapperOfSearchApproval.ApprovalInfo approvalInfo = param.getT3();
        QueryHotelOrderDataResponseType queryHotelOrderDataResponseType = param.getT4();
        ResourceToken resourceToken = param.getT5();
        WrapperOfAccount.AccountInfo accountInfo = param.getT6();
        ApprovalTextInfoResponseType approvalTextInfoResponseType = param.getT7();
        Boolean overSea = TokenInfoGetUtil.isOverSeaFromResourceToken(resourceToken);

        boolean emergency = Optional.ofNullable(approvalInput).map(ApprovalInput::getEmergency).orElse(FALSE).equalsIgnoreCase(TRUE);
        String subApprovalNo = Optional.ofNullable(approvalInput).map(ApprovalInput::getSubApprovalNo).orElse("");
        String masterApprovalNo = Optional.ofNullable(approvalInput).map(ApprovalInput::getMasterApprovalNo).orElse("");
        ApprovalOutput approvalOutput = buildCommonApprovalOutput(accountInfo, resourceToken, bookingInitRequestType, approvalTextInfoResponseType);
        if (emergency) {
            // 紧急预定场景直接返回标记即可
            approvalOutput.setDefaultEmergencyBook(TRUE);
            return approvalOutput;
        }
        if (!accountInfo.isPreApprovalRequired(overSea, bookingInitRequestType.getCorpPayInfo())
            && !accountInfo.isTravelApplyRequired(overSea, bookingInitRequestType.getCorpPayInfo())) {
            approvalOutput.setDefaultEmergencyBook(FALSE);
            return approvalOutput;
        }
        if (!StringUtils.isEmpty(subApprovalNo)) {
            return preApproval(approvalOutput, subApprovalNo, masterApprovalNo, approvalDefaultResponseType);
        }
        if (getOrderId(bookingInitRequestType) != null && approvalInfo != null && Boolean.TRUE.equals(approvalInfo.getValidFlag())) {
            return orderApproval(approvalOutput, queryHotelOrderDataResponseType);
        }
        // 根据接口返回的审批单给到默认值
        return getApprovalOutputByDefault(approvalOutput, approvalDefaultResponseType);
    }

    @Override
    protected ParamCheckResult check(Tuple7<BookingInitRequestType, BatchApprovalDefaultResponseType, WrapperOfSearchApproval.ApprovalInfo,
            QueryHotelOrderDataResponseType, ResourceToken, WrapperOfAccount.AccountInfo, ApprovalTextInfoResponseType> param) {
        return null;
    }

    public static ApprovalOutput buildCommonApprovalOutput(WrapperOfAccount.AccountInfo accountInfo, ResourceToken resourceToken,
                                                           BookingInitRequestType bookingInitRequestType,
                                                           ApprovalTextInfoResponseType approvalTextInfoResponseType) {
        Boolean oversea = TokenInfoGetUtil.isOverSeaFromResourceToken(resourceToken);
        ApprovalOutput approvalOutput = new ApprovalOutput();
        Boolean isPersonPayNeedAdvanceAuth = accountInfo.isPersonPayNeedAdvanceAuth(oversea);
        Boolean isHltRelatedJourneyNo = "T".equals(accountInfo.getIsHtlReleateJourneyNoInput());
        approvalOutput.setShowApprovalModel(StringUtils.isEmpty(Optional.ofNullable(bookingInitRequestType.getMiceInput()).map(MiceInput::getMiceToken).orElse(null))
                && !TripApplicationEnum.NONE.equals(getTripApplicationVersion(accountInfo, oversea, bookingInitRequestType.getCorpPayInfo())));
        if (BooleanUtils.isNotTrue(isPersonPayNeedAdvanceAuth)) {
            String balanceType = Optional.ofNullable(resourceToken).map(ResourceToken::getRoomResourceToken).map(RoomResourceToken::getBalanceType).orElse(null);
            Boolean isFG = HotelBalanceTypeEnum.FG.getDesc().equalsIgnoreCase(balanceType);
            Boolean isAmadeus = Optional.ofNullable(resourceToken).map(ResourceToken::getRoomResourceToken).map(RoomResourceToken::getAmadeus).orElse(null);
            String payType = BooleanUtils.isTrue(isAmadeus) || BooleanUtils.isTrue(isFG) ? CASH : CCRAD;
            approvalOutput.setSupport(Collections.singletonList(payType));
        }
        approvalOutput.setChecked(BooleanUtils.isTrue(isPersonPayNeedAdvanceAuth) || BooleanUtils.isTrue(isHltRelatedJourneyNo));
        if (approvalTextInfoResponseType != null) {
            approvalOutput.setEmergencyName(approvalTextInfoResponseType.getEmergencyName());
            approvalOutput.setApprovalTitle(Optional.ofNullable(approvalTextInfoResponseType.getTextInfoItems()).orElse(new ArrayList<>()).stream()
                .filter(item -> EMERGENCY_TITLE.equals(item.getItemType())).findFirst().map(TextInfoItem::getItemValue).orElse(null));
        }
        return approvalOutput;
    }

    /**
     * APPLICATION:新出差申请 OLD_APPLICATION:老出差申请 NONE:非出差申请 APPROVAL:提前审批模式
     *
     * @param accountInfo
     * @parma oversea
     * @return
     */
    public static TripApplicationEnum getTripApplicationVersion(WrapperOfAccount.AccountInfo accountInfo, Boolean oversea, CorpPayInfo corpPayInfo) {
        if (CorpPayInfoUtil.isPrivate(corpPayInfo) || accountInfo == null) {
            return TripApplicationEnum.NONE;
        }
        // 如果支持提前审批，直接返回
        if (BooleanUtils.isTrue(accountInfo.isPreApprovalRequired(oversea, corpPayInfo))) {
            return TripApplicationEnum.APPLICATION;
        } else if (BooleanUtils.isTrue(accountInfo.isTravelApplyRequired(oversea, corpPayInfo))) {
            // 判断出差申请
            return TripApplicationEnum.APPLICATION;
        }
        return TripApplicationEnum.NONE;
    }

    /**
     * 提前审批前置
     * @param subApprovalNo
     * @param masterApprovalNo
     * @param approvalDefaultResponseType
     * @return
     */
    protected ApprovalOutput preApproval(ApprovalOutput approvalOutput, String subApprovalNo, String masterApprovalNo,
                                         BatchApprovalDefaultResponseType approvalDefaultResponseType) {
        approvalOutput.setDefaultApprovalSubNo(subApprovalNo);
        approvalOutput.setDefaultApprovalMainNo(masterApprovalNo);
        approvalOutput.setDefaultEmergencyBook(FALSE);
        // 提前审批前置场景，直接使用前端传入的审批单号
        if (StringUtils.isEmpty(masterApprovalNo)) {
            // 目前前端审批前置场景不会带入主单号，需要反查
            return getApprovalOutputByDefault(approvalOutput, approvalDefaultResponseType);
        }
        return approvalOutput;
    }

    /**
     * 订后场景，根据订单号查询
     * @param queryHotelOrderDataResponseType
     * @return
     */
    protected ApprovalOutput orderApproval(ApprovalOutput approvalOutput, QueryHotelOrderDataResponseType queryHotelOrderDataResponseType) {
        // 有订单号的场景，修改场景，需要带入原单的审批单，先BFF做兼容，等公共下沉逻辑
        String orderEmergency = isOrderEmergency(queryHotelOrderDataResponseType);
        approvalOutput.setDefaultEmergencyBook(orderEmergency);
        if (FALSE.equals(orderEmergency)) {
            approvalOutput.setDefaultApprovalMainNo(getMainApprovalNo(queryHotelOrderDataResponseType));
            approvalOutput.setDefaultApprovalSubNo(getSubApprovalNo(queryHotelOrderDataResponseType));
        }
        return approvalOutput;
    }

    /**
     * 获取订单号
     * @param bookingInitRequestType
     * @return
     */
    protected Long getOrderId(BookingInitRequestType bookingInitRequestType) {
        ResourceToken resourceToken = ResourceTokenUtil.tryParseToken(
                Optional.ofNullable(bookingInitRequestType.getResourceTokenInfo()).map(ResourceTokenInfo::getResourceToken).orElse(null));
        Long orderId = Optional.ofNullable(resourceToken).map(ResourceToken::getOrderResourceToken)
                .map(OrderResourceToken::getOrderId).orElse(null);
        if (orderId != null) {
            return orderId;
        }
        if (!StringUtils.isEmpty(bookingInitRequestType.getOrderId())) {
            return Long.valueOf(bookingInitRequestType.getOrderId());
        }
        return null;
    }

    /**
     * 根据公共默认审批单接口生产出参
     * @param approvalDefaultResponseType
     * @return
     */
    protected ApprovalOutput getApprovalOutputByDefault(ApprovalOutput approvalOutput, BatchApprovalDefaultResponseType approvalDefaultResponseType) {
        List<ApprovalInfo> approvalInfos = Optional.ofNullable(approvalDefaultResponseType).map(BatchApprovalDefaultResponseType::getApprovalInfos).orElse(new ArrayList<>());
        ApprovalInfo defaultApprovalInfo = approvalInfos.stream()
                .filter(approvalInfo -> CommonConstant.OPEN.equalsIgnoreCase(approvalInfo.getDefaultApproval())).findFirst().orElse(null);
        // 根据接口返回的审批单给到默认值
        if (defaultApprovalInfo != null && defaultApprovalInfo.getApprovalBaseInfo() != null) {
            approvalOutput.setDefaultApprovalMainNo(defaultApprovalInfo.getApprovalBaseInfo().getMasterApprovalNo());
            approvalOutput.setDefaultApprovalSubNo(defaultApprovalInfo.getApprovalBaseInfo().getSubApprovalNo());
            approvalOutput.setDefaultEmergencyBook(FALSE);
        }
        return approvalOutput;
    }

    protected String isOrderEmergency(QueryHotelOrderDataResponseType queryOrderDataResponseType) {
        if (queryOrderDataResponseType == null) {
            return FALSE;
        }
        return Optional.ofNullable(queryOrderDataResponseType).map(QueryHotelOrderDataResponseType::getTravelInfo).map(TravelInfoType::getApproval)
                .map(ApprovalType::getPrepareApprovalStatus).orElse(0) == EMERGENCY ? TRUE : FALSE;
    }

    private String getSubApprovalNo(QueryHotelOrderDataResponseType queryHotelOrderDataResponseType) {
        return Optional.ofNullable(queryHotelOrderDataResponseType).map(QueryHotelOrderDataResponseType::getTravelInfo)
                .map(TravelInfoType::getApproval)
                .map(ApprovalType::getSubPreApprovalNo).orElse(null);
    }

    private String getMainApprovalNo(QueryHotelOrderDataResponseType queryHotelOrderDataResponseType) {
        return Optional.ofNullable(queryHotelOrderDataResponseType).map(QueryHotelOrderDataResponseType::getTravelInfo)
                .map(TravelInfoType::getApproval)
                .map(ApprovalType::getPrepareApprovalNo).orElse(null);
    }
}
