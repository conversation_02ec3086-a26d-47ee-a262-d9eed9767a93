package com.ctrip.corp.bff.hotel.book.handler.corpconfigurationservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.corpsz.configuration.common.contract.CorpConfigurationServiceClient;
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigRequestType;
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/6/26 22:31
 */
@Component
public class HandlerOfGetSubAccountConfig extends AbstractHandlerOfSOA<GetSubAccountConfigRequestType, GetSubAccountConfigResponseType, CorpConfigurationServiceClient> {

    @Override
    protected String getMethodName() {
        return "getSubAccountConfig";
    }
}
