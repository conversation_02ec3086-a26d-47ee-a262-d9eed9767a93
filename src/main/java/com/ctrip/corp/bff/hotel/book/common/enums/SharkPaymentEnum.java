package com.ctrip.corp.bff.hotel.book.common.enums;

/**
 * @Author: chenchu<PERSON>
 * @Date: 2024/10/21 15:23
 * @Description: 支付相关shark类型
 */
public enum SharkPaymentEnum {

    /**
     * 预订可管控，报销更合规
     */
    ADMIN_CORP_PAY_GUID("AdminCorpPayGuid"),

    /**
     * 免垫付，免报销
     */
    USER_CORP_PAY_GUID("UserCorpPayGuid"),

    /**
     * 无支付方式可选，请联系贵司差旅负责人更改后台配置
     */
    NO_SUPPORT_SERVICE_PAY("NoSupportServicePay"),

    /**
     * 当前房型无可用支付方式，请选择其他房型。
     */
    NO_SUPPORT_PAY("NoSupportPay"),
    ;

    /**
     * code
     */
    private String code;

    SharkPaymentEnum(String code) {
        this.code = code;
    }

    public String getCode() {
        return code;
    }
}
