package com.ctrip.corp.bff.hotel.book.handler.handlerofgroup4jservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.corpUserGroupService.Group4jServiceClient;
import corp.user.service.group4j.accounts.QueryBizModeBindRelationRequestType;
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType;
import org.springframework.stereotype.Component;

import javax.swing.text.html.Option;
import java.util.Objects;
import java.util.Optional;

/**
 * <AUTHOR>
 * @date 2024/12/27
 */
@Component
public class HandlerOfQueryBizModeBindRelation extends AbstractHandlerOfSOA<QueryBizModeBindRelationRequestType,
        QueryBizModeBindRelationResponseType, Group4jServiceClient> {
    @Override
    protected String getMethodName() {
        return "queryBizModeBindRelation";
    }


    @Override
    protected String getLogErrorCode(QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType) {
        return Optional.ofNullable(queryBizModeBindRelationResponseType)
                .map(QueryBizModeBindRelationResponseType::getResponseCode)
                .map(t -> t != null ? t.toString() : null).orElse(null);
    }

}
