package com.ctrip.corp.bff.hotel.book.handler.corpuserinfoservice4j;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.corpUserInfoService.CorpUserInfoService4jClient;
import corp.user.service.corpUserInfoService.GetCorpUserHotelVipCardRequestType;
import corp.user.service.corpUserInfoService.GetCorpUserHotelVipCardResponseType;
import org.springframework.stereotype.Component;

/**
 * @Author: chenchuang
 * @Date: 2024/8/22 16:26
 * @Description:查询酒店会员卡信息
 */
@Component
public class HandlerOfGetCorpUserHotelVipCard extends AbstractHandlerOfSOA<GetCorpUserHotelVipCardRequestType,
        GetCorpUserHotelVipCardResponseType, CorpUserInfoService4jClient> {

    @Override
    protected String getMethodName() {
        return "getCorpUserHotelVipCard";
    }
}
