package com.ctrip.corp.bff.hotel.book.handler.invoiceservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.soa.platform.sps.InvoiceService.v1.GetInvoiceTitlesRequest;
import com.ctrip.soa.platform.sps.InvoiceService.v1.GetInvoiceTitlesResponse;
import com.ctrip.soa.platform.sps.InvoiceService.v1.InvoiceServiceClient;
import org.springframework.stereotype.Component;

/**
 * @Author: chenchuang
 * @Date: 2024/8/22 16:26
 * @Description:获取发票抬头历史列表
 */
@Component
public class HandlerOfGetInvoiceTitleList extends AbstractHandlerOfSOA<GetInvoiceTitlesRequest, GetInvoiceTitlesResponse, InvoiceServiceClient> {

    @Override
    protected String getMethodName() {
        return "getInvoiceTitleList";
    }
}
