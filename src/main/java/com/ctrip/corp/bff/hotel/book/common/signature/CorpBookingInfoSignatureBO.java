package com.ctrip.corp.bff.hotel.book.common.signature;

import io.protostuff.Tag;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/10/23 18:32
 */
public class CorpBookingInfoSignatureBO {
    @Tag(1)
    public String bookingChannel;

    @Tag(2)
    public String bookType;

    @Tag(3)
    public String payType;

    @Tag(4)
    public Boolean reachTravel;

    @Tag(5)
    public Boolean standard;

    @Tag(6)
    public String currencyType;

    @Tag(7)
    public Boolean highRisk;

    @Tag(8)
    public String hotelType;

    @Tag(9)
    public String nightAmount;

    @Tag(10)
    public String orderAmount;

    @Tag(11)
    public String starLevel;

    @Tag(12)
    public String productType;

    @Tag(13)
    public Integer travelReason;

    @Tag(14)
    public Boolean contingent;

    @Tag(15)
    public String overStandardStage;

    @Tag(16)
    public Boolean control;

    @Tag(17)
    public List<RcItemSignatureBO> rcItemSignatureBOS;

    @Tag(18)
    public OrderAmountItemSignatureBO nightPrice;

    @Tag(19)
    public OrderAmountItemSignatureBO personPay;

    @Tag(20)
    public OrderAmountItemSignatureBO accountPay;

    @Tag(21)
    public OrderAmountItemSignatureBO totalPay;

    public String getBookingChannel() {
        return bookingChannel;
    }

    public void setBookingChannel(String bookingChannel) {
        this.bookingChannel = bookingChannel;
    }

    public String getBookType() {
        return bookType;
    }

    public void setBookType(String bookType) {
        this.bookType = bookType;
    }

    public String getPayType() {
        return payType;
    }

    public void setPayType(String payType) {
        this.payType = payType;
    }

    public Boolean getReachTravel() {
        return reachTravel;
    }

    public void setReachTravel(Boolean reachTravel) {
        this.reachTravel = reachTravel;
    }

    public Boolean getStandard() {
        return standard;
    }

    public void setStandard(Boolean standard) {
        this.standard = standard;
    }

    public String getCurrencyType() {
        return currencyType;
    }

    public void setCurrencyType(String currencyType) {
        this.currencyType = currencyType;
    }

    public Boolean getHighRisk() {
        return highRisk;
    }

    public void setHighRisk(Boolean highRisk) {
        this.highRisk = highRisk;
    }

    public String getHotelType() {
        return hotelType;
    }

    public void setHotelType(String hotelType) {
        this.hotelType = hotelType;
    }


    public String getNightAmount() {
        return nightAmount;
    }

    public void setNightAmount(String nightAmount) {
        this.nightAmount = nightAmount;
    }

    public String getOrderAmount() {
        return orderAmount;
    }

    public void setOrderAmount(String orderAmount) {
        this.orderAmount = orderAmount;
    }


    public String getStarLevel() {
        return starLevel;
    }

    public void setStarLevel(String starLevel) {
        this.starLevel = starLevel;
    }

    public String getProductType() {
        return productType;
    }

    public void setProductType(String productType) {
        this.productType = productType;
    }

    public Integer getTravelReason() {
        return travelReason;
    }

    public void setTravelReason(Integer travelReason) {
        this.travelReason = travelReason;
    }

    public Boolean getContingent() {
        return contingent;
    }

    public void setContingent(Boolean contingent) {
        this.contingent = contingent;
    }

    public String getOverStandardStage() {
        return overStandardStage;
    }

    public void setOverStandardStage(String overStandardStage) {
        this.overStandardStage = overStandardStage;
    }

    public Boolean getControl() {
        return control;
    }

    public void setControl(Boolean control) {
        this.control = control;
    }

    public List<RcItemSignatureBO> getRcItemSignatureBOS() {
        return rcItemSignatureBOS;
    }

    public void setRcItemSignatureBOS(List<RcItemSignatureBO> rcItemSignatureBOS) {
        this.rcItemSignatureBOS = rcItemSignatureBOS;
    }

    public OrderAmountItemSignatureBO getNightPrice() {
        return nightPrice;
    }

    public void setNightPrice(OrderAmountItemSignatureBO nightPrice) {
        this.nightPrice = nightPrice;
    }

    public OrderAmountItemSignatureBO getPersonPay() {
        return personPay;
    }

    public void setPersonPay(OrderAmountItemSignatureBO personPay) {
        this.personPay = personPay;
    }

    public OrderAmountItemSignatureBO getAccountPay() {
        return accountPay;
    }

    public void setAccountPay(OrderAmountItemSignatureBO accountPay) {
        this.accountPay = accountPay;
    }

    public OrderAmountItemSignatureBO getTotalPay() {
        return totalPay;
    }

    public void setTotalPay(OrderAmountItemSignatureBO totalPay) {
        this.totalPay = totalPay;
    }
}
