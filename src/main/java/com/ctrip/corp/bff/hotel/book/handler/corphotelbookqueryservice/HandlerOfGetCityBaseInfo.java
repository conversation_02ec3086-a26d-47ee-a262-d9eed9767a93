package com.ctrip.corp.bff.hotel.book.handler.corphotelbookqueryservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoRequestType;
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType;
import com.ctrip.corp.hotel.book.query.service.CorpHotelBookQueryServiceClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/8/16 19:38
 */

@Component
public class HandlerOfGetCityBaseInfo extends
    AbstractHandlerOfSOA<GetCityBaseInfoRequestType, GetCityBaseInfoResponseType, CorpHotelBookQueryServiceClient> {

    @Override
    protected String getMethodName() {
        return "getCityBaseInfo";
    }
}
