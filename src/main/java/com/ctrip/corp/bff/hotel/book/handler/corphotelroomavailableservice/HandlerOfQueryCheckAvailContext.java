package com.ctrip.corp.bff.hotel.book.handler.corphotelroomavailableservice;

import com.ctrip.corp.agg.hotel.roomavailable.CorpHotelRoomAvailableServiceClient;
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextRequestType;
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType;
import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/7/8 21:25
 */

@Component public class HandlerOfQueryCheckAvailContext extends
    AbstractHandlerOfSOA<QueryCheckAvailContextRequestType, QueryCheckAvailContextResponseType, CorpHotelRoomAvailableServiceClient> {
    @Override protected String getMethodName() {
        return "queryCheckAvailContext";
    }
}
