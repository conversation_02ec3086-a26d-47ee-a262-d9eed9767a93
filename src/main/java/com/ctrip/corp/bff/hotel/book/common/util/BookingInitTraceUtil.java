package com.ctrip.corp.bff.hotel.book.common.util;

import com.ctrip.corp.bff.framework.hotel.common.enums.TrackingBookModeEnum;
import com.ctrip.corp.bff.framework.hotel.common.enums.TrackingEnum;
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPositionInfo;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInfo;
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo;
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum;
import com.ctrip.corp.bff.hotel.book.contract.*;

import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/7/30
 */
public class BookingInitTraceUtil {
    /** 一个房型如果有多个信息，用 | 分割 */
    private static final String ROOM_PROPERTY_SEPARATOR = "|";
    public static Map<String, String> tracking(BookingInitRequestType requestType, BookingInitResponseType responseType) {
        Map<String, String> trackingMap = TrackingUtil.buildBaseTrackingMap(requestType.getHotelBookInput());
        trackingMap.put(TrackingEnum.CITY_ID.getCode(),
                Optional.ofNullable(responseType).map(BookingInitResponseType::getBookingHotelInfo)
                        .map(BookingHotelInfo::getHotelPositionInfo).map(HotelPositionInfo::getCityInfo)
                        .map(CityInfo::getCityId).orElse(0).toString());
        trackingMap.put(TrackingEnum.CITY_REGION.getCode(),
                Optional.ofNullable(responseType).map(BookingInitResponseType::getBookingHotelInfo)
                        .map(BookingHotelInfo::getHotelPositionInfo).map(HotelPositionInfo::getCityInfo)
                        .map(CityInfo::getCityArea).orElse(StringUtil.EMPTY));
        trackingMap.put("hotelId", Optional.ofNullable(responseType).map(BookingInitResponseType::getBookingHotelInfo)
                .map(BookingHotelInfo::getHotelId).orElse(StringUtil.EMPTY));
        /*todo：trackingMap.put("wsId", ""); trackingMap.put("locationId", "");
        trackingMap.put(TrackingEnum.GDS_TYPE, "");  trackingMap.put(TrackingEnum.ROOM_TYPE, StringUtil.EMPTY);*/

        if (requestType.getHotelPayTypeInput() != null) {
            HotelPayTypeEnum roomPayType = HotelPayTypeUtil.getRoomPayType(requestType.getHotelPayTypeInput());
            trackingMap.put("roomPayType",
                    roomPayType != null ? roomPayType.getCode() : HotelPayTypeEnum.NONE.getCode());
        }
        trackingMap.put(TrackingEnum.CORP_PAY_TYPE.getCode(),
                Optional.ofNullable(requestType).map(BookingInitRequestType::getCorpPayInfo)
                        .map(CorpPayInfo::getCorpPayType).orElse(StringUtil.EMPTY));

        String bookMode = TrackingBookModeEnum.NORMAL.name();
        if (responseType != null && responseType.getApprovalOutput() != null && (
                StringUtil.isNotEmpty(responseType.getApprovalOutput().getDefaultApprovalSubNo()) || "T".equalsIgnoreCase(
                        responseType.getApprovalOutput().getDefaultEmergencyBook()))) {
            bookMode = TrackingBookModeEnum.APPROVAL.name();
        }
        trackingMap.put(TrackingEnum.BOOK_MODE.getCode(), bookMode);

        List<RoomProperty> roomProperties =
                Optional.ofNullable(responseType).map(BookingInitResponseType::getBookingRoomInfo)
                        .map(BookingRoomInfo::getRoomProperties).orElse(null);
        Map<String, String> trackingRoomProperties = trackingForRoomProperties(roomProperties);
        if (CollectionUtil.isNotEmpty(trackingRoomProperties)) {
            for (Map.Entry<String, String> trackingRoomProperty : trackingRoomProperties.entrySet()) {
                trackingMap.put(trackingRoomProperty.getKey(), trackingRoomProperty.getValue());
            }
        }

        return trackingMap;
    }


    private static Map<String, String> trackingForRoomProperties(List<RoomProperty> roomProperties) {
        if (CollectionUtil.isEmpty(roomProperties)) {
            return null;
        }

        Map<String, List<RoomProperty>> certificateRoomPropertiesMap = mapCertificateRoomProperties(roomProperties);
        if (null == certificateRoomPropertiesMap || certificateRoomPropertiesMap.isEmpty()) {
            return null;
        }

        return certificateRoomPropertiesMap.entrySet().stream().filter(Objects::nonNull)
                .collect(Collectors.toMap(Map.Entry::getKey,
                        certificateRoomProperties -> roomPropertyToString(certificateRoomProperties.getValue()),
                        (key1, key2) -> key1));
    }

    private static Map<String, List<RoomProperty>> mapCertificateRoomProperties(List<RoomProperty> roomProperties) {
        if (CollectionUtil.isEmpty(roomProperties)) {
            return null;
        }

        return roomProperties.stream().filter(Objects::nonNull)
                .filter(roomProperty -> StringUtil.isNotBlank(roomProperty.getPropertyType()))
                .collect(Collectors.groupingBy(RoomProperty::getPropertyType));
    }

    private static String roomPropertyToString(List<RoomProperty> roomProperties) {
        if (CollectionUtil.isEmpty(roomProperties)) {
            return "";
        }
        return roomProperties.stream().filter(Objects::nonNull).map(RoomProperty::getPropertyValue)
                .collect(Collectors.joining(ROOM_PROPERTY_SEPARATOR));
    }
}
