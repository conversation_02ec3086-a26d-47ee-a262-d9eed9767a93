package com.ctrip.corp.bff.hotel.book.handler.corp4jservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import corp.user.service.corp4jservice.Corp4jServiceClient;
import corp.user.service.corp4jservice.GetCorpInfoRequestType;
import corp.user.service.corp4jservice.GetCorpInfoResponseType;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @Date 2024/4/9 18:45
 * @Version 1.0
 */
@Component
public class HandlerOfGetCorpInfo extends
        AbstractHandlerOfSOA<GetCorpInfoRequestType, GetCorpInfoResponseType, Corp4jServiceClient> {
    @Override
    protected String getMethodName() {
        return "getCorpInfo";
    }
}
