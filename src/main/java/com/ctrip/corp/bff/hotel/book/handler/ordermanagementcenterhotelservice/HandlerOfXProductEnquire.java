package com.ctrip.corp.bff.hotel.book.handler.ordermanagementcenterhotelservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.corp.order.managementcenter.hotel.service.contract.OrderManagementCenterHotelServiceClient;
import com.ctrip.corp.order.managementcenter.hotel.service.contract.XProductEnquireRequestType;
import com.ctrip.corp.order.managementcenter.hotel.service.contract.XProductEnquireResponseType;
import org.springframework.stereotype.Component;

/**
 * @Author: chenchuang
 * @Date: 2024/8/22 16:26
 * @Description:获取保单信息
 */
@Component
public class HandlerOfXProductEnquire extends AbstractHandlerOfSOA<XProductEnquireRequestType,
        XProductEnquireResponseType, OrderManagementCenterHotelServiceClient> {

    @Override
    protected String getMethodName() {
        return "xProductEnquire";
    }
}
