package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit;

import com.ctrip.basebiz.geolocation.service.GetAllNationalityRequestType;
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil;
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType;
import com.ctrip.corp.bff.framework.template.mapper.AbstractMapper;
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult;
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2025/4/10
 */
@Component
public class MapperOfGetAllNationalityRequestType extends AbstractMapper<Tuple1<IntegrationSoaRequestType>, GetAllNationalityRequestType> {
    @Override
    protected GetAllNationalityRequestType convert(Tuple1<IntegrationSoaRequestType> para) {
        if (para == null) {
            return null;
        }
        IntegrationSoaRequestType integrationSoaRequestType = para.getT1();
        if (integrationSoaRequestType == null || StringUtil.isBlank(integrationSoaRequestType.getLanguage())) {
            return null;
        }
        GetAllNationalityRequestType getAllNationalityRequestType = new GetAllNationalityRequestType();
        getAllNationalityRequestType.setLocale(integrationSoaRequestType.getLanguage());

        return getAllNationalityRequestType;
    }

    @Override
    protected ParamCheckResult check(Tuple1<IntegrationSoaRequestType> integrationSoaRequestTypeTuple1) {
        return null;
    }
}
