package com.ctrip.corp.bff.hotel.book.handler.triporderservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.soa._21234.SearchTripBasicInfoRequestType;
import com.ctrip.soa._21234.SearchTripBasicInfoResponseType;
import com.ctrip.soa._21234.TripOrderServiceClient;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/7/8 16:29
 */
@Component public class HandlerOfSearchTripBasicInfo extends
    AbstractHandlerOfSOA<SearchTripBasicInfoRequestType, SearchTripBasicInfoResponseType, TripOrderServiceClient> {
    @Override protected String getMethodName() {
        return "searchTripBasicInfo";
    }
}
