package com.ctrip.corp.bff.hotel.book.handler.htlconfigurationmiddleservice;

import com.ctrip.corp.bff.framework.template.handler.AbstractHandlerOfSOA;
import com.ctrip.hotel.wireless.htlgetgroupvippackagefunction.soa.HtlConfigurationMiddleServiceClient;
import com.ctrip.hotel.wireless.htlgetgroupvippackagefunction.soa.contract.GetGroupVipPackageRequestType;
import com.ctrip.hotel.wireless.htlgetgroupvippackagefunction.soa.contract.GetGroupVipPackageResponseType;
import org.springframework.stereotype.Component;

/**
 * @Author: chenchuang
 * @Date: 2024/8/22 16:26
 * @Description:获取集团信息
 */
@Component
public class HandlerOfGetGroupVipPackage extends AbstractHandlerOfSOA<GetGroupVipPackageRequestType,
        GetGroupVipPackageResponseType, HtlConfigurationMiddleServiceClient> {

    @Override
    protected String getMethodName() {
        return "getGroupVipPackage";
    }
}
