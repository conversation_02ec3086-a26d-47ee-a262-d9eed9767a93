package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.contract.integration.TripInput
import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;

class MapperOfSearchTripDetailRequestTypeTest extends Specification {

    def tester = Spy(new MapperOfSearchTripDetailRequestType())

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        given:
        expect:
        tester.convert(Tuple3.of(new IntegrationSoaRequestType(), "2", new TripInput())).tripId ==2
    }

    def "getChannelType" () {
        expect:
        new MapperOfSearchTripDetailRequestType().getChannelType(source) == res
        where:
        source | res
        SourceFrom.Offline || 2
        SourceFrom.Online || 1
        SourceFrom.H5 || 3
    }
}
