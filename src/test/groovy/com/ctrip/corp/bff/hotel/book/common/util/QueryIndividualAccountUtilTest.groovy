package com.ctrip.corp.bff.hotel.book.common.util

import com.ctrip.corp.bff.framework.hotel.common.util.PersonAccountUtil
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import corp.user.service.CorpAccountQueryService.HotelControlConfigs
import corp.user.service.CorpAccountQueryService.IndividualAccountRule
import corp.user.service.CorpAccountQueryService.QueryIndividualAccountResponseType
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2024/7/16 20:21
 *
 */
class QueryIndividualAccountUtilTest extends Specification {

    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }

    @Unroll
    def "test supportPersonalAccount"() {
        given:
        def queryIndividualAccountResponseType = new QueryIndividualAccountResponseType(
                individualAccountConfig: new IndividualAccountRule(consumeChannel: consumeChannel, hotelControlConfigs: new HotelControlConfigs(
                        hotelBusiness: hotelBusiness, hotelPrivacy: hotelPrivacy, hotelControl: hotelControl
                )),
                isConfigExist: isConfigExist,
        )
        def corpPayType = new CorpPayInfo(corpPayType: corpPayTypeStr)
        expect:
        result == PersonAccountUtil.supportPersonalAccount(queryIndividualAccountResponseType, corpPayType, new IntegrationSoaRequestType(sourceFrom: sourceFrom))
        where:
        consumeChannel | isConfigExist | corpPayTypeStr | hotelBusiness | hotelPrivacy | hotelControl | sourceFrom         || result
        "1"            | true          | "private"      | "0"           | "1"          | "1"          | SourceFrom.Online  || true
        "1"            | true          | "public"       | "1"           | "0"          | "1"          | SourceFrom.Online  || true
        "1,2"          | true          | "public"       | "1"           | "0"          | "1"          | SourceFrom.Native  || true
        "1"            | true          | "public"       | "1"           | "0"          | "0"          | SourceFrom.Online  || false
        "1"            | true          | "public"       | "1"           | "0"          | "1"          | SourceFrom.Offline || false
        "1"            | false         | "public"       | "1"           | "0"          | "1"          | SourceFrom.Online  || false
        "2"            | true          | "public"       | "1"           | "0"          | "1"          | SourceFrom.Offline || true
        "2"            | false         | "public"       | "1"           | "0"          | "1"          | SourceFrom.Offline || false
    }
}
