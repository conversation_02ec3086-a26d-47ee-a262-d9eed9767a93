package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2025/3/25 20:02
 *
 */
class MapperOfRetrieveTicketsByOrderTypeRequestTypeTest extends Specification {


    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }


    def mapper = new MapperOfRetrieveTicketsByOrderTypeRequestType()

    @Unroll
    def "testConvert with #description"() {
        given: "A Tuple1 instance with IntegrationSoaRequestType"
        def integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: new UserInfo(userId: userId))

        when: "Calling convert method"
        def result = mapper.convert(Tuple1.of(integrationSoaRequestType) as Tuple1<IntegrationSoaRequestType>)

        then: "The result should match the expected outcome"
        result.getCustomerID() == expectedCustomerId
        result.getOrderType() == expectedOrderType

        where:
        description    | userId  | expectedCustomerId | expectedOrderType
        "valid userId" | "user1" | "user1"            | 1
        "null userId"  | null    | null               | 1
    }
}
