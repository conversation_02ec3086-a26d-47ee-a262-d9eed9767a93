package com.ctrip.corp.bff.hotel.book.handler.orderindexextservice

import mockit.internal.state.SavePoint
import spock.lang.Specification


class HandlerOfGetOrderFoundationDataTest extends Specification{
    def savePoint = new SavePoint()

    def setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "testHandle"() {
        given:
        def handler = new HandlerOfGetOrderFoundationData()

        when:
        def response = handler.getMethodName()

        then:
        response == "getOrderFoundationData"
    }
}
