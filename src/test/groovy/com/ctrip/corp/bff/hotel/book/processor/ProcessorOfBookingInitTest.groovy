package com.ctrip.corp.bff.hotel.book.processor

import com.ctrip.corp.bff.framework.hotel.common.enums.TrackingEnum
import com.ctrip.corp.bff.framework.hotel.entity.contract.ApprovalOutput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPositionInfo
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.AggCostAllocationToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.BookInitToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.CouponToken
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil
import com.ctrip.corp.bff.hotel.book.contract.BookingHotelInfo
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import com.ctrip.corp.bff.hotel.book.contract.BookingInitResponseType
import com.ctrip.corp.bff.hotel.book.contract.RoomProperty
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodResponseType
import mockit.MockUp
import mockit.internal.state.SavePoint
import org.mockito.Mock
import spock.lang.Specification

/**
 * <AUTHOR>
 * @Date 2024/12/3 21:00
 */
class ProcessorOfBookingInitTest extends Specification {

    def savePoint = new SavePoint()

    def setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }
    def processorOfBookingInit = new ProcessorOfBookingInit()

    def testBuildApprovalInput() {
        expect:
        processorOfBookingInit.buildApprovalInput(requestType, approvalOutput) == result
        where:
        requestType                                                                                                     | approvalOutput                                                                                   || result
        null                                                                                                            | null                                                                                             || null
        new BookingInitRequestType(strategyInfos: [new StrategyInfo(strategyKey: "FIRST_REQUEST", strategyValue: "T")]) | new com.ctrip.corp.bff.framework.hotel.entity.contract.ApprovalOutput(defaultApprovalSubNo: "1") || new ApprovalInput(subApprovalNo: "1")
        new BookingInitRequestType(strategyInfos: [new StrategyInfo(strategyKey: "FIRST_REQUEST", strategyValue: "F")]) | new com.ctrip.corp.bff.framework.hotel.entity.contract.ApprovalOutput(defaultApprovalSubNo: "1") || null
        new BookingInitRequestType(strategyInfos: [new StrategyInfo(strategyKey: "FIRST_REQUEST", strategyValue: "T")]) | null                                                                                             || null
    }


    def "test"() {
        given:
        when:
        // 期望
        def a = TokenParseUtil.parseToken("H4sIAAAAAAAA_-PiMjK0NLY0MjAxN5OS4Wi4cOLAzScMAgwSIkp8xiZmegZwoIVX1givrBWjGwCzt7NzaQAAAA", BookInitToken .class)
        // 实际
        def b = TokenParseUtil.parseToken("H4sIAAAAAAAA_-PiMjK0NLY0MjAxN5OS4Wi4cOLAzScMAgwSIkp8xiZmegZwoIVX1givrBWjGwCzt7NzaQAAAA", BookInitToken.class)
        then:
        System.out.println(JsonUtil.toJson(a))
        System.out.println(JsonUtil.toJson(b))
        JsonUtil.toJson(a) == JsonUtil.toJson(b)
    }


}
