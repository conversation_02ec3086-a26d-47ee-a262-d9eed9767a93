package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelGeoInfoResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.template.entity.contract.integration.MiceInput
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;

class MapperOfTmsCreateOrderVerifyRequestTypeTest extends Specification {

    def tester = Spy(new MapperOfTmsCreateOrderVerifyRequestType())

    def savePoint = new mockit.internal.state.SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        expect:
        tester.convert(Tuple2.of(new OrderCreateRequestType(), null)).productType == "INTERNATIONAL_HOTEL"
    }


    def "check" () {
        expect:
        !tester.check(Tuple2.of(new OrderCreateRequestType(), null)).result
        !tester.check(Tuple2.of(new OrderCreateRequestType(miceInput: new MiceInput()), null)).result
        !tester.check(Tuple2.of(new OrderCreateRequestType(miceInput: new MiceInput(miceActivityId: "a")), null)).result
        !tester.check(Tuple2.of(new OrderCreateRequestType(miceInput: new MiceInput(miceActivityId: "a", miceToken: "a")), null)).result
        tester.check(Tuple2.of(new OrderCreateRequestType(miceInput: new MiceInput(miceActivityId: "a", miceToken: "a")), new ResourceToken(hotelResourceToken: new HotelResourceToken(hotelGeoInfoResourceToken: new HotelGeoInfoResourceToken(cityId: 2))))) == null
    }
}

