package com.ctrip.corp.bff.hotel.book.common.util

import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckAgreementRcInfoType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckBookAheadRcInfoType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckOverStandardRcInfoType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.RoomModeInfoType
import com.ctrip.corp.agg.hotel.tmc.entity.FinalFloatingAmountSettingType
import com.ctrip.corp.agg.hotel.tmc.entity.FinalPolicyType
import com.ctrip.corp.agg.hotel.tmc.entity.GetHotelTravelPolicyResponseType
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfHotelTravelPolicy
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelRcInfo
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.hotel.book.common.enums.RcTypeEnum
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import com.ctrip.corp.bff.hotel.book.qconfig.entity.CustomizedSharkConfig
import com.ctrip.corp.bff.hotel.book.sharkmock.SharkMockUtil
import com.fasterxml.jackson.annotation.JsonProperty
import corp.user.service.corp4jservice.GetReasoncodesResponseType
import corp.user.service.corp4jservice.ReasoncodeInfo
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2025/1/13 21:12
 *
 */
class HotelRCUtilTest extends Specification {

    def savePoint = new SavePoint()

    void setup() {
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
    }

    void cleanup() {
        savePoint.rollback()
    }


    @Unroll
    def "testBuildRCContentOverStand with different scenarios"() {
        given:
        GetReasoncodesResponseType getReasoncodesResponseType = new GetReasoncodesResponseType(
                reasonCodes: Arrays.asList(new ReasoncodeInfo(reasonCode: "AC", reasonInfo: "中英文员工级别G级及以下,预订600到800协议酒店符合标准。", reasonInfoEn: "中英文员工级别G级及以下,预订600到800协议酒店符合标准。", rcType: 1)))
        IntegrationSoaRequestType integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "shanglv_001", userId: "_SL2240552768"))
        CheckOverStandardRcInfoType checkOverStandardRcInfoType = new CheckOverStandardRcInfoType(required: true, starInControl: true, priceInControl: false)
        List<CustomizedSharkConfig> customizedSharkConfigs = new ArrayList<>()
        GetHotelTravelPolicyResponseType getHotelTravelPolicyResponseType = new GetHotelTravelPolicyResponseType(
                responseCode: 20000,
                finalPolicy: new FinalPolicyType(finalFloatingAmountSetting: new FinalFloatingAmountSettingType(floatAmountType: "TA", floatingAmount: new BigDecimal(10))));
        WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo hotelTravelPolicyInfo = WrapperOfHotelTravelPolicy.builder().hotelTravelPolicyResponseType(getHotelTravelPolicyResponseType).build();

        when:
        HotelRcInfo result = HotelRCUtil.buildRCContentOverStand(getReasoncodesResponseType, integrationSoaRequestType, checkOverStandardRcInfoType, customizedSharkConfigs, hotelTravelPolicyInfo)
        then:
        result.rcType == "LOW_PRICE"
        result.rcDesc == "请选择未预订差标内房型的原因"
        result.rcTitle == "根据贵公司差旅政策，因您未预订符合价格房型，故请您选择原因（若继续预订，差补额度将加入差标，后续报销则相应扣减）"
        result.rcInfo.getRcContents().get(0).code == "AC"
        result.rcInfo.getRcContents().get(0).type == "LOW_PRICE"
        result.rcInfo.getRcContents().get(0).value == "中英文员工级别G级及以下,预订600到800协议酒店符合标准。"
        result.rcInfo.getRcContents().get(0).rcToken == "H4sIAAAAAAAA_wFhAJ7_CglMT1dfUFJJQ0USAkFDGk3kuK3oi7HmloflkZjlt6XnuqfliKtH57qn5Y-K5Lul5LiLLOmihOiuojYwMOWIsDgwMOWNj-iurumFkuW6l-espuWQiOagh-WHhuOAgiIBRoLzuaJhAAAA"
    }

    @Unroll
    def "buildRcTitleOverStand with different scenarios"() {
        given:
        IntegrationSoaRequestType integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "shanglv_001", userId: "_SL2240552768"))
        CheckOverStandardRcInfoType checkOverStandardRcInfoType = new CheckOverStandardRcInfoType(
                required: true,
                starInControl: true,
                priceInControl: false,
                roomModeInfoList: Arrays.asList(new RoomModeInfoType(roomIndex: 1, priceInControl: false, priceOverType: "UNDER_PRICE"), new RoomModeInfoType(roomIndex: 2, priceInControl: true, priceOverType: "UNDER_PRICE")))
        List<CustomizedSharkConfig> customizedSharkConfigs = new ArrayList<>()
        GetHotelTravelPolicyResponseType getHotelTravelPolicyResponseType = new GetHotelTravelPolicyResponseType(
                responseCode: 20000,
                finalPolicy: new FinalPolicyType(finalFloatingAmountSetting: new FinalFloatingAmountSettingType(floatAmountType: "TA", floatingAmount: new BigDecimal(10))));
        WrapperOfHotelTravelPolicy.HotelTravelPolicyInfo hotelTravelPolicyInfo = WrapperOfHotelTravelPolicy.builder().hotelTravelPolicyResponseType(getHotelTravelPolicyResponseType).build();

        when:
        String result = HotelRCUtil.buildRcTitleOverStand(checkOverStandardRcInfoType, integrationSoaRequestType, customizedSharkConfigs, hotelTravelPolicyInfo)
        then:
        result == "根据贵公司差旅政策，房间1未预订符合价格房型的原因是：（若继续预订，差补额度将加入差标，后续报销则相应扣减）"
    }

    @Unroll
    def "buildRCContentAgreement with different scenarios"() {
        given:
        GetReasoncodesResponseType getReasoncodesResponseType = new GetReasoncodesResponseType(
                reasonCodes: Arrays.asList(new ReasoncodeInfo(reasonCode: "AO", reasonInfo: "中文协议房型满房", reasonInfoEn: "中文协议房型满房", rcType: 2)))
        IntegrationSoaRequestType integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "shanglv_001", userId: "_SL2240552768"))
        List<CustomizedSharkConfig> customizedSharkConfigs = new ArrayList<>()
        CheckAgreementRcInfoType checkAgreementRcInfoType = new CheckAgreementRcInfoType(required: true)

        when:
        HotelRcInfo result = HotelRCUtil.buildRCContentAgreement(checkAgreementRcInfoType, getReasoncodesResponseType, integrationSoaRequestType, customizedSharkConfigs)
        then:
        result.rcType == "AGREEMENT"
        result.rcDesc == "请选择未预订协议房型的原因"
        result.rcTitle == "根据贵公司差旅政策，因您未预订协议房型，故请您选择原因"
        result.rcInfo.getRcContents().get(0).code == "AO"
        result.rcInfo.getRcContents().get(0).type == "AGREEMENT"
        result.rcInfo.getRcContents().get(0).value == "中文协议房型满房"
        result.rcInfo.getRcContents().get(0).rcToken == "H4sIAAAAAAAA_-PidHQPcnX1dfULEWJy9JeSeLJj7bNp7U97-1-sW_esY__Ted3Pdi8EMpQY3QCfmU6eLAAAAA"
    }


    @Unroll
    def "buildRCContentAgreement with customtitie"() {
        given:
        CheckTravelPolicyResponseType checkTravelPolicyResponseType = JsonUtil.fromJson("{\"checkRcResult\":{\"checkResultList\":[{\"serviceChargeType\":\"NONE\",\"checkResult\":\"RC_REQUIRED\"}],\"invalidRcTypeList\":[],\"overStandardRc\":[{\"serviceChargeType\":\"NONE\",\"starInControl\":true,\"priceInControl\":true,\"required\":false,\"selected\":false,\"dailyPriceResult\":[],\"overStandardTotalAmount\":0.00,\"floatPriceOverDetail\":{\"overType\":\"BELOW_BASE_STANDARD_UPPER_LIMIT\"}}],\"bookAheadRc\":{\"required\":false,\"selected\":false,\"bookAheadDay\":0},\"agreementRc\":{\"required\":true,\"selected\":false}},\"controlledAmountDetailInfo\":{\"accountPaymentAmount\":{\"price\":734,\"currency\":\"CNY\"},\"individualPaymentAmount\":{\"price\":0,\"currency\":\"CNY\"},\"totalPaymentAmount\":{\"price\":734,\"currency\":\"CNY\"},\"avgPaymentAmount\":{\"price\":367.00,\"currency\":\"CNY\"}},\"approvalPaymentType\":\"CORP_PAY\",\"approvalPriceOptimized\":\"F\",\"interruptCodeList\":[4],\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2025-03-11 20:07:19.516+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", CheckTravelPolicyResponseType.class)
        GetReasoncodesResponseType getReasoncodesResponseType = JsonUtil.fromJson("{\"responseStatus\":{\"timestamp\":\"2025-03-11 20:07:18.531+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]},\"reasonCodes\":[{\"reasonCode\":\"VV\",\"reasonInfo\":\"因公/其他\",\"reasonInfoEn\":\"Others\",\"state\":true,\"rcType\":51,\"rcTip\":\"\",\"rcTipEn\":\"\"},{\"reasonCode\":\"FBN\",\"reasonInfo\":\"协议酒店满房\",\"reasonInfoEn\":\"Corporate hotels / hotels compliant with policy are fully booked\",\"state\":true,\"rcType\":2,\"rcTip\":\"\",\"rcTipEn\":\"\"},{\"reasonCode\":\"PLN\",\"reasonInfo\":\"协议酒店远离出差办事地点\",\"reasonInfoEn\":\"Corporate hotels / Hotels compliant with travel policy are far away\",\"state\":true,\"rcType\":2,\"rcTip\":\"\",\"rcTipEn\":\"\"},{\"reasonCode\":\"ACN\",\"reasonInfo\":\"陪同领导或客户\",\"reasonInfoEn\":\"Accompany leaders / clients\",\"state\":true,\"rcType\":256,\"productLine\":1,\"defaultRC\":true},{\"reasonCode\":\"BGN\",\"reasonInfo\":\"行程变更，前续行程已提交订单修改\",\"reasonInfoEn\":\"Itinerary changes, the previous itinerary has been submitted to modify the order\",\"state\":true,\"rcType\":128,\"productLine\":1,\"defaultRC\":true},{\"reasonCode\":\"ECN\",\"reasonInfo\":\"为客户预订/招待客户\",\"reasonInfoEn\":\"Reservation for clients / Corporate hospitality\",\"state\":true,\"rcType\":8,\"productLine\":1,\"defaultRC\":true},{\"reasonCode\":\"ERN\",\"reasonInfo\":\"48小时内临时预订/紧急预订\",\"reasonInfoEn\":\"Urgent reservation within 48 hours\",\"state\":true,\"rcType\":8,\"productLine\":1,\"defaultRC\":true},{\"reasonCode\":\"JHN\",\"reasonInfo\":\"计划外的紧急出差任务\",\"reasonInfoEn\":\"unplanned emergency business trip\",\"state\":true,\"rcType\":8,\"productLine\":1,\"defaultRC\":true},{\"reasonCode\":\"XCN\",\"reasonInfo\":\"行程改变\",\"reasonInfoEn\":\"change schedule\",\"state\":true,\"rcType\":8,\"productLine\":1,\"defaultRC\":true}]}", GetReasoncodesResponseType.class)
        List<CustomizedSharkConfig> customizedSharkConfigs = Arrays.asList(JsonUtil.fromJson("{\n" +
                "        \"sceneDes\": \"APP房型列表页定制RC，低价RC、提前预定RC、协议RC\",\n" +
                "        \"scene\": \"ROOM_LIST_RC\",\n" +
                "        \"corpIds\": [\n" +
                "            \"hoteltest_zhc\",\n" +
                "            \"1p15123007198\",\n" +
                "            \"testcorp2\",\n" +
                "            \"VIATRIS\",\n" +
                "            \"viatrishk\",\n" +
                "            \"djcx\"\n" +
                "        ],\n" +
                "        \"sharkKeyPres\": [\n" +
                "            \"key.corp.app.hotel.rc.OVER_STANDARD_ROOM\",\n" +
                "            \"key.corp.app.hotel.rc.OVER_STANDARD\",\n" +
                "            \"key.corp.app.hotel.rc.CPrice\",\n" +
                "            \"key.corp.app.hotel.rc.Reservation\",\n" +
                "            \"key.corp.app.hotel.rc.BigTitle\"\n" +
                "        ]\n" +
                "    }", CustomizedSharkConfig.class))
        BookingInitRequestType bookingInitRequestType = JsonUtil.fromJson("{\"integrationSoaRequestType\":{\"userInfo\":{\"userId\":\"2701887325\",\"corpId\":\"djcx\",\"groupId\":\"Gr_00001719\",\"pos\":\"CHINA\"},\"token\":\"C27A959F79068BB05AAC82F1ABFA1C570AC4BB961187146C4C027135DBA55182\",\"language\":\"zh-CN\",\"requestId\":\"6025437bed204195b2607073181bbac6\",\"sourceFrom\":\"H5\",\"transactionID\":\"TID.SEARCHHOTEL.6.A63FC6A977CD4AA7BD76BA392ECBBEDB\",\"gatewayHost\":\"ct.ctrip.com\",\"timezoneOffsetMinutesNew\":\"480\",\"logIndices\":[{\"key\":\"groupId\",\"value\":\"Gr_00001719\"},{\"key\":\"timezoneOffsetMinutesNew\",\"value\":\"480\"},{\"key\":\"gatewayHost\",\"value\":\"ct.ctrip.com\"},{\"key\":\"pvid\",\"value\":\"34\"},{\"key\":\"subChannel\",\"value\":\"\"},{\"key\":\"CID\",\"value\":\"09031136118247099581\"},{\"key\":\"EID\"},{\"key\":\"CIP\",\"value\":\"2409:895a:b523:8fba:182b:923d:9e1a:88c5\"},{\"key\":\"miceToken\",\"value\":\"\"},{\"key\":\"groupName\",\"value\":\"深圳市大疆创新科技有限公司\"},{\"key\":\"gatewayIdc\",\"value\":\"SHARB\"},{\"key\":\"traceID\",\"value\":\"\"},{\"key\":\"A\",\"value\":\"hotelcheckavail\"},{\"key\":\"CorpID\",\"value\":\"djcx\"},{\"key\":\"C\",\"value\":\"corpbookinghotelbookservice\"},{\"key\":\"M\",\"value\":\"2701887325\"},{\"key\":\"sid\",\"value\":\"6\"},{\"key\":\"S\",\"value\":\"17969d3aae404dd4aa82d89cece94238978a9b657ef0\"},{\"key\":\"T\",\"value\":\"C27A959F79068BB05AAC82F1ABFA1C570AC4BB961187146C4C027135DBA55182\"},{\"key\":\"pos\",\"value\":\"CHINA\"},{\"key\":\"GE\",\"value\":\"1741694838188\"},{\"key\":\"sourceFrom\",\"value\":\"H5\"},{\"key\":\"corpName\",\"value\":\"深圳市大疆创新科技有限公司\"},{\"key\":\"RID\",\"value\":\"6025437bed204195b2607073181bbac6\"},{\"key\":\"TID\",\"value\":\"TID.SEARCHHOTEL.6.A63FC6A977CD4AA7BD76BA392ECBBEDB\"},{\"key\":\"VID\",\"value\":\"1708420904146.1154K6ykuvBj\"}],\"ticket\":\"JzHfGHOZA+xoPu9L4i/rilsWssXUlALIe5kpj1FpJc3FHeCMxQv5GU42J+d1VdkGgZA1wuB7u7t6fLXyOeHeaU44pLwxT2s60LjJLA0CJs4\\u003d\",\"transferInfo\":[{\"key\":\"iframe\",\"value\":\"F\"},{\"key\":\"channel\",\"value\":\"sso\"}]},\"hotelBookInput\":{\"hotelDateRangeInfo\":{\"checkIn\":\"2025-03-11\",\"checkOut\":\"2025-03-13\"},\"roomQuantity\":1,\"adultQuantity\":1},\"policyInput\":{},\"resourceTokenInfo\":{\"resourceToken\":\"{\\\"hotelResourceToken\\\":{\\\"masterHotelId\\\":535344,\\\"hotelId\\\":81140635,\\\"searchHotelTraceId\\\":\\\"a2f259bf87af4aaa8c176df717f9a2b5\\\",\\\"hid\\\":\\\"ASIDMzI4QgFCSgYIABAAGABSBBAAGABaA0NOWWINCgNDTlkSA0NOWRoBMWIPCgNDTlkSA0NOWRoDMS4waAFyBjQwMC4wMHoEMC4wMIIBIGEyZjI1OWJmODdhZjRhYWE4YzE3NmRmNzE3ZjlhMmI1igEKMTI1Njg1OTU0OJoBJGFmNWU0NmRjLWI3MzktNDc4Ny1hMTY2LWNjNzE1MWMyZDAzZaIBAggAqgEkMGE1NTQzNDgtNzNkYS00YzYyLWFjZTctNmUzMWZkZWE2ZmMw\\\",\\\"hotelType\\\":\\\"M\\\",\\\"bonusPoint\\\":\\\"F\\\",\\\"hotelGeoInfoResourceToken\\\":{\\\"cityId\\\":693,\\\"locationId\\\":658}},\\\"roomResourceToken\\\":{\\\"roomId\\\":1256859543,\\\"baseRoomId\\\":1335587,\\\"ratePlanTraceLogId\\\":\\\"d013d81d67184b4b834f348e35b3dc3e\\\",\\\"bedName\\\":\\\"1张1.8米大床\\\",\\\"windowName\\\":\\\"有窗\\\",\\\"breakfastInfo\\\":\\\"无餐食\\\",\\\"confirmType\\\":\\\"IMMEDIATE\\\",\\\"roomType\\\":\\\"M\\\",\\\"balanceType\\\":\\\"PP\\\",\\\"tmcPrice\\\":false,\\\"adult\\\":2,\\\"pId\\\":\\\"AQoDMzY3EgYzNjcuMDAqA0NOWTINCgNDTlkSA0NOWRoBMTINCgNDTlkSA0NOWRoBMToCCABCBwjpDxADGAtKBwjpDxADGA1QAVgAaBF4AoABm7fYJg\\\\u003d\\\\u003d\\\",\\\"amadeus\\\":false,\\\"supportAccommodation\\\":\\\"F\\\",\\\"roomOriginCurrency\\\":\\\"CNY\\\",\\\"roomScenarioEnum\\\":\\\"ROOM_LIST\\\",\\\"ratePlanRequestToken\\\":\\\"ASi1L/0AUA0WAAZwqTsQDzTAAioAqRJ4TwQKABqpCpDoJYxy2ho1GRsytwjDAJMFY+MEsDToViNuBWy0Agy8UgekRwQvjKD3AZ0AlwCXABHPFjmlSB5hlfekLMoDOjHVBc6VgYxVxPPIzFC9taEnN68hbNu2o/x2Qqesy4uZnmSsGUEwUFaacpvVJL5JU3NR9sYLQ5znI7N2gpPP9fiYnMmMfIQuFUoVaoSYuEVDRA/BO4iNqDX6B633Dr1v6HefL7iqHBflwCzKkjgWi2+UIAALouZwRwIo3Cq8BrAw67qQSCrqwpIkRFAxwkB1OvqUOq4fY1l7ls69MHDVaSDV1p5tcjDLxLzYzHahbESUh+wrqwSDdPRIYxNtbs2NPDvzJt5UCPVO2JPerCCIHjWxCnNdJ6b1heEvMZddQM7L5oZa5o1G6vl1HezF1QUhbQtSs2cV1U8q2UyMWRWlMDyHGGaBpYwh6+xxj0wfXupwjsQbB5ZdEedhEDlxPWusxo8RaJcB9ddYOy3ZOLUVTFmXHPQM+8PKb83ON9em5/xP+f86weQr9KIcVeW4LMpCT5M70cwnmLIuMegZFbhWxLjTEQMrejy7jVpMpyLJ34QoHZ2JH+BGw4oynpfGjBz9tpg20+RvU9BM/vS7xPMWO6Es42HIlQqy+dju6NVyYyVc5wWxOQRm1Esn8r1PpV8MNHJJrvK6D8Mroj3eJOv76/WTDugreF/B+wneS/A+gncQvHvgnQPvG/jNZ+7yDFzDLdzjEk7xCHwBN8AtrJ5CAAmLRkdx9kn+v6xB32Pr/2k/u/6m/H/JYqRZAwlCN+E5s+QwI/plDCbfpQc9Q/u6sYJaSq1EM5xYYPJdQnCWo4l+GYFp69KBnhGOY4luIheGo8aV6JcdmHyXFPQMdv3afHM2x88BCABWKEDUQMgGYCfM7246k/ynmF52AIdCBg\\\\u003d\\\\u003d\\\",\\\"ratePlanRoomToken\\\":\\\"ASi1L/0AUFkAAAoBTRAAGJfPqNcE\\\",\\\"roomUniqueKey\\\":\\\"V1-ASi1L/0AUJkAAAiXz6jXBBIBTRoETk9ORSICUFA\\\\u003d\\\",\\\"adultNumber\\\":2,\\\"windowType\\\":2,\\\"customRoomAmount\\\":734,\\\"customRoomAmountCurrency\\\":\\\"CNY\\\"},\\\"reservationResourceToken\\\":{\\\"policyToken\\\":\\\"v1KLUv/QBQpQkAlhI9JTCp6AHEvMHYt03XSQxk91SxKqdKZ4zXkQTaSURrRf+goKGgggMyADQAMgDrvSB/l61O4W3bF1VTul020TiL5zmwRbNtH6RtJz/kckdlh9QujAEwhmjbDk6pMrUMASzx/BrQVmRJ255MpjjawcpsSD8ancNdlp0Jgg4JhdNAjGGITczzHBCMhDUT47BEkUAs3raNGGEjckXOquJdYqTe9i04cDkYpfTQDSgpC95qOqG458dLAm/KwYKUDWe3S9P5bStHrg0JCG1jV3hGPHrRO/rkrKozar9HlqwiZIGH1EQhzu5iqcIHo6fUcWJvp5JifE8eDRcAOhtnY41LmsHWBvfNxTN3IaoQpuagoQqpC5giqEOVqilWweXYJ/kWqGKvYo8waqkCODeAlNeZOaxsNgE\\\\u003d\\\"}}\"},\"corpPayInfo\":{\"corpPayType\":\"public\"},\"approvalInput\":{\"masterApprovalNo\":\"\",\"subApprovalNo\":\"**********-0-1-H\",\"emergency\":\"F\"},\"strategyInfos\":[{\"strategyKey\":\"BOOKING_SCENARIO\",\"strategyValue\":\"HOTEL_LIST\"},{\"strategyKey\":\"BOOKING_WITH_PERSONAL_ACCOUNT\",\"strategyValue\":\"F\"},{\"strategyKey\":\"HOTEL_CHECK_AVAIL\",\"strategyValue\":\"T\"},{\"strategyKey\":\"BOOKING_PLATFORM\",\"strategyValue\":\"android\"}],\"travelPolicyInfo\":{\"supportMealStandard\":\"F\"}}", BookingInitRequestType.class)

        when:
        HotelRcInfo result = HotelRCUtil.buildRCContentAgreement(checkTravelPolicyResponseType.getCheckRcResult().getAgreementRc(), getReasoncodesResponseType, bookingInitRequestType.getIntegrationSoaRequestType(), customizedSharkConfigs)
        then:
        result.rcTitle == "当前所选酒店非公司协议酒店/协议房型，请问没有选择预定协议酒店/协议房型的原因是?"
    }

    @Unroll
    def "buildRCContentBookAhead with different scenarios"() {
        given:
        GetReasoncodesResponseType getReasoncodesResponseType = new GetReasoncodesResponseType(
                reasonCodes: Arrays.asList(new ReasoncodeInfo(reasonCode: "UY", reasonInfo: "提前一天", reasonInfoEn: "提前一天", rcType: 8)))
        IntegrationSoaRequestType integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "shanglv_001", userId: "_SL2240552768"))
        List<CustomizedSharkConfig> customizedSharkConfigs = new ArrayList<>()
        CheckBookAheadRcInfoType checkBookAheadRcInfoType = new CheckBookAheadRcInfoType(required: true, bookAheadDay: 5)

        when:
        HotelRcInfo result = HotelRCUtil.buildRCContentBookAhead(checkBookAheadRcInfoType, getReasoncodesResponseType, integrationSoaRequestType, customizedSharkConfigs)
        then:
        result.rcType == "BOOK_AHEAD"
        result.rcDesc == "未提前预订酒店的原因"
        result.rcTitle == "根据贵公司差旅政策，因您未提前5天预订，故请您选择原因"
        result.rcInfo.getRcContents().get(0).type == "BOOK_AHEAD"
        result.rcInfo.getRcContents().get(0).value == "提前一天"
        result.rcInfo.getRcContents().get(0).rcToken == "H4sIAAAAAAAA_-PicvL394539HB1dBFiCo2U4nnWP-FpZ--THQ1Pl6xUYnQDAKXFKZ4hAAAA"
    }

    def "getPriceReasonsWithoutException" () {
        expect:
        HotelRCUtil.getPriceReasonsWithoutException(null, null) == null
        HotelRCUtil.getPriceReasonsWithoutException(new GetReasoncodesResponseType(), null) == null
        HotelRCUtil.getPriceReasonsWithoutException(new GetReasoncodesResponseType(reasonCodes: []), null) == null
        HotelRCUtil.getPriceReasonsWithoutException(new GetReasoncodesResponseType(reasonCodes: [null]), null) == null
        HotelRCUtil.getPriceReasonsWithoutException(new GetReasoncodesResponseType(reasonCodes: [new ReasoncodeInfo()]), null) == null
        HotelRCUtil.getPriceReasonsWithoutException(new GetReasoncodesResponseType(reasonCodes: [new ReasoncodeInfo(rcType: 15)]), RcTypeEnum.MODIFY) == null
        HotelRCUtil.getPriceReasonsWithoutException(new GetReasoncodesResponseType(reasonCodes: [new ReasoncodeInfo(rcType: 16)]), RcTypeEnum.MODIFY).size() == 1
    }
}
