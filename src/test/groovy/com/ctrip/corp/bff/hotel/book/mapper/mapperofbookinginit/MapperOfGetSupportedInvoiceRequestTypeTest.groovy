package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;

class MapperOfGetSupportedInvoiceRequestTypeTest extends Specification {

    def tester = Spy( new MapperOfGetSupportedInvoiceRequestType() )

    def savePoint = new SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        expect:
        tester.convert(Tuple5.of(Mock(WrapperOfCheckAvail.CheckAvailInfo), null, new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public"),integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo())), null, null)).scene == "FILL_ORDER"
    }
}
