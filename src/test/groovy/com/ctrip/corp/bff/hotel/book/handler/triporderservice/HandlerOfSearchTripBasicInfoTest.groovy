package com.ctrip.corp.bff.hotel.book.handler.triporderservice

import mockit.internal.state.SavePoint
import spock.lang.Specification

class HandlerOfSearchTripBasicInfoTest extends Specification{

    def savePoint = new SavePoint()

    def setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "testHandle"() {
        given:
        def handler = new HandlerOfSearchTripBasicInfo()

        when:
        def response = handler.getMethodName()

        then:
        response == "searchTripBasicInfo"
    }
}
