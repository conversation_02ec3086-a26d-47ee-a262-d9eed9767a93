package com.ctrip.corp.bff.hotel.book.common.util

import com.ctrip.corp.agg.hotel.roomavailable.entity.BillingGuestInfoType
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookingRulesType
import com.ctrip.corp.agg.hotel.roomavailable.entity.SpecialOfferRoomInfoEntity
import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2024/12/25
 */
class PassengerInfoConfigUtilTest extends Specification {
    def savePoint = new SavePoint()

    void setup() {
        new MockUp<LogUtil>() {
            @Mock
            public static void loggingClogOnly(LogLevelEnum level,
                                               Class<?> type,
                                               String title,
                                               String message,
                                               Map<String, String> indexTags) {
                return;
            }

            @Mock
            public static void loggingClogOnly(LogLevelEnum level,
                                               Class<?> type,
                                               String title,
                                               String message,
                                               Throwable throwable,
                                               Map<String, String> indexTags) {
                return;
            }
        }
        new MockUp<JsonUtil>() {
            @Mock
            public static String toJson(Object obj) {
                return "mock";
            }
        }
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "test getSpecialOfferTips when specialOfferRoomInfoEntity is null"() {
        given:
        SpecialOfferRoomInfoEntity specialOfferRoomInfoEntity = null

        when:
        List<String> result = PassengerInfoConfigUtil.getSpecialOfferTips(specialOfferRoomInfoEntity)

        then:
        result == Collections.emptyList()
    }

    def "test getSpecialOfferTips when specialOfferRoomInfoEntity has blank hints"() {
        given:
        SpecialOfferRoomInfoEntity specialOfferRoomInfoEntity = new SpecialOfferRoomInfoEntity()
        specialOfferRoomInfoEntity.setBookingRuleHint("")
        specialOfferRoomInfoEntity.setCertificateInputHint("")

        when:
        List<String> result = PassengerInfoConfigUtil.getSpecialOfferTips(specialOfferRoomInfoEntity)

        then:
        result == Collections.emptyList()
    }

    def "test getSpecialOfferTips when specialOfferRoomInfoEntity has non-blank hints"() {
        given:
        SpecialOfferRoomInfoEntity specialOfferRoomInfoEntity = new SpecialOfferRoomInfoEntity()
        specialOfferRoomInfoEntity.setBookingRuleHint("Booking rule hint")
        specialOfferRoomInfoEntity.setCertificateInputHint("Certificate input hint")

        when:
        List<String> result = PassengerInfoConfigUtil.getSpecialOfferTips(specialOfferRoomInfoEntity)

        then:
        result == ["Booking rule hint", "Certificate input hint"]
    }

    def "buildAmountInfo" () {
        expect:
        PassengerInfoConfigUtil.buildAmountInfo(null, null) == null
        PassengerInfoConfigUtil.buildAmountInfo("CNY", null) == null
        PassengerInfoConfigUtil.buildAmountInfo("CNY", 2).currency == "CNY"
        PassengerInfoConfigUtil.buildAmountInfo("CNY", 2).amount == "2"
    }

    def "getGuestsNameLanguageList" () {
        given:
        new MockUp<StrategyOfBookingInitUtil>() {
            @Mock
            public static boolean isGuestNameLanguageAggResult(List<StrategyInfo> strategyInfos) {
                return true
            }
            }
        def input = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        input.getBookingRules() >> new BookingRulesType(billingGuestInfo: new BillingGuestInfoType(guestsNameLanguages: ["a"]))
        expect:
        PassengerInfoConfigUtil.getGuestsNameLanguageList(input, new BookingInitRequestType()) == ["a"]
    }



    def "getGuestsNameLanguageList0" () {
        given:
        new MockUp<StrategyOfBookingInitUtil>() {
            @Mock
            public static boolean isGuestNameLanguageAggResult(List<StrategyInfo> strategyInfos) {
                return false
            }
        }
        new MockUp<PassengerInfoConfigUtil>() {
            @Mock
            public static boolean needOnlyUseEnName(WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo) {
                return true
            }
            }
        def input = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        input.getBookingRules() >> new BookingRulesType(billingGuestInfo: new BillingGuestInfoType(guestsNameLanguages: ["a"]))
        expect:
        PassengerInfoConfigUtil.getGuestsNameLanguageList(input, new BookingInitRequestType()) == ["en"]
    }

    def "getGuestsNameLanguageList00" () {
        given:
        new MockUp<StrategyOfBookingInitUtil>() {
            @Mock
            public static boolean isGuestNameLanguageAggResult(List<StrategyInfo> strategyInfos) {
                return false
            }
        }
        new MockUp<PassengerInfoConfigUtil>() {
            @Mock
            public static boolean needOnlyUseEnName(WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo) {
                return false
            }
        }
        def input = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        expect:
        PassengerInfoConfigUtil.getGuestsNameLanguageList(input, new BookingInitRequestType()) == ["en", "zh"]
    }

    def "getGuestsNameLanguageList000" () {
        given:
        new MockUp<StrategyOfBookingInitUtil>() {
            @Mock
            public static boolean isGuestNameLanguageAggResult(List<StrategyInfo> strategyInfos) {
                return false
            }
        }
        new MockUp<PassengerInfoConfigUtil>() {
            @Mock
            public static boolean needOnlyUseEnName(WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo) {
                return false
            }
        }
        def input = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        input.getBookingRules() >> new BookingRulesType(billingGuestInfo: new BillingGuestInfoType(guestsNameLanguages: ["zh"]))
        expect:
        PassengerInfoConfigUtil.getGuestsNameLanguageList(input, new BookingInitRequestType()) == ["zh", "en"]
    }


}
