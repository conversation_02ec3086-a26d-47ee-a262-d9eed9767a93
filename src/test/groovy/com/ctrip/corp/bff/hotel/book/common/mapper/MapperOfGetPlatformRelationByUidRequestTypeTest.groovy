package com.ctrip.corp.bff.hotel.book.common.mapper

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import corp.user.service.group4j.accounts.BizModeBindRelationData
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType
import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;

class MapperOfGetPlatformRelationByUidRequestTypeTest extends Specification {

    def tester = Spy(MapperOfGetPlatformRelationByUidRequestType)

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        expect:
        tester.convert(null) == null
        tester.convert(Tuple2.of(null,null)) == null
        tester.convert(Tuple2.of(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "uid")),null)) == null
        tester.convert(Tuple2.of(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "uid")), new QueryBizModeBindRelationResponseType())) == null
        tester.convert(Tuple2.of(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "uid")), new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: []))) == null
        tester.convert(Tuple2.of(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "uid")), new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [null]))) == null
        tester.convert(Tuple2.of(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "uid")), new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData()]))) == null
        tester.convert(Tuple2.of(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "uid")), new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(dimensionId: "t", primaryDimensionId: "test")]))) == null
        tester.convert(Tuple2.of(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "uid")), new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(dimensionId: "uid")]))) == null
        tester.convert(Tuple2.of(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "uid")), new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(dimensionId: "uid", primaryDimensionId: "test")]))).uid == "test"
    }
}
