package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response

import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.sharkmock.SharkMockUtil
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2025/4/18 20:54
 *
 */
class MapperOfConfirmDetailResponseTest extends Specification {
    MapperOfConfirmDetailResponse mapper = new MapperOfConfirmDetailResponse()

    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }

    @Unroll
    def "testConvert with #description"() {
        given: "An OrderCreateRequestType and CreateOrderResponseType"
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                if ("open".equalsIgnoreCase(corpId)) {
                    return true;
                }
                return false
            }
        }
        OrderCreateRequestType requestType = new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: corp)))
        CreateOrderResponseType createOrderResponseType = new CreateOrderResponseType(responseCode: responseCode)

        when: "Calling convert"
        def result = mapper.convert(Tuple2.of(requestType, createOrderResponseType) as Tuple2<OrderCreateRequestType, CreateOrderResponseType>)

        then: "The result should match the expected value"
        result.getT1() == expectedResult
        result.getT2().getConfirmInfo()?.getConfirmDetailInfos()?.get(0)?.getCode() == expectedCode

        where:
        description                 | corp    | responseCode | requireRepeat | expectedResult | expectedCode
        "Repeat order scenario"     | "open"  | 99997        | true          | true           | "REPEAT_ORDER"
        "Repeat order scenario"     | "close" | 99997        | true          | false          | null
        "Non-repeat order scenario" | "open"  | 20000        | false         | false          | null
    }

}
