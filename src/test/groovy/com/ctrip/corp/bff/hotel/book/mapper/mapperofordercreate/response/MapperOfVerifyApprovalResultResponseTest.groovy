package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response

import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.ApprovalBillChecklistType
import com.ctrip.corp.agg.hotel.tmc.entity.FieldInfoType
import com.ctrip.corp.agg.hotel.tmc.entity.FieldValueDetailType
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfCityBaseInfo
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.common.shark.DateDisplayUtil
import com.ctrip.corp.bff.framework.template.common.shark.currencytemplate.CurrencyDisplayUtil
import com.ctrip.corp.bff.framework.template.common.shark.entity.CurrencyDisplayInfo
import com.ctrip.corp.bff.framework.template.common.utils.CalendarUtil
import com.ctrip.corp.bff.framework.template.common.utils.date.DateUtil
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import com.ctrip.corp.bff.hotel.book.contract.ElementDetail
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.contract.VerifyResult
import com.ctrip.corp.bff.hotel.book.sharkmock.SharkMockUtil
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.CountryInfo
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.HotelApprovalInfo
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.HotelDetail
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.HotelExtInfo
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.HotelProductInfo
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.LocationInfo
import com.ctrip.corp.hotel.book.query.entity.CityBaseInfoEntity
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2024/12/10 16:54
 *
 */
class MapperOfVerifyApprovalResultResponseTest extends Specification {


    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }
    @Unroll
    def "test BillTravelStandard with #description"() {
        given:
        MapperOfVerifyApprovalResultResponse mapper = new MapperOfVerifyApprovalResultResponse()
        HotelApprovalInfo hotelApprovalInfo = new HotelApprovalInfo(hotelProductInfo: new HotelProductInfo(averagePrice: averagePrice))

        when:
        String result = mapper.billTravelStandard(hotelApprovalInfo)

        then:
        result == expectedResult

        where:
        description               | averagePrice                 | expectedResult
        "average price is 100.00" | new BigDecimal("100.00")     | "100"
        "average price is 100.00" | new BigDecimal("100.890890") | "100.89"
        "average price is null"   | null                         | ""
    }


    def "testBuildHotelNameElementDetails"() {
        given:
        MapperOfVerifyApprovalResultResponse mapper = new MapperOfVerifyApprovalResultResponse()
        HotelApprovalInfo hotelApprovalInfo = new HotelApprovalInfo(hotelProductInfo: new HotelProductInfo())
        HotelExtInfo hotelExtInfo = new HotelExtInfo()
        hotelExtInfo.setCityName("Shanghai")
        hotelExtInfo.setHotelDetailList([new HotelDetail(hotelName: "Hotel A"), new HotelDetail(hotelName: "Hotel B")])
        HotelExtInfo hotelExtInfoBJ = new HotelExtInfo()
        hotelExtInfoBJ.setCityName("beijing")
        hotelExtInfoBJ.setHotelDetailList([new HotelDetail(hotelName: "Hotel C")])
        hotelApprovalInfo.getHotelProductInfo().setHotelExtInfoList([hotelExtInfo, hotelExtInfoBJ])

        when:
        List<ElementDetail> result = mapper.buildHotelNameElementDetails(hotelApprovalInfo)

        then:
        result.size() == 2
        result[0].elementKey == "Shanghai"
        result[0].elementValue == "Hotel A、Hotel B"
        result[1].elementKey == "beijing"
        result[1].elementValue == "Hotel C"
    }

    def "billOriginCity"() {
        given:
        HotelApprovalInfo hotelApprovalInfo = JsonUtil.fromJson("{\n" +
                "                \"approvalBaseInfo\": {\n" +
                "                    \"approvalNumber\": \"ZYSQ-20241211-1283\",\n" +
                "                    \"approvalNumberMain\": \"ZYSQ-20241211-1283\",\n" +
                "                    \"corpID\": \"zycs\",\n" +
                "                    \"commitCorpEmpID\": \"2119666008\",\n" +
                "                    \"approvalExpiredTime\": \"2024-12-23\",\n" +
                "                    \"available\": true,\n" +
                "                    \"createTime\": \"2024-12-11 15:19:53\",\n" +
                "                    \"approvalStatus\": 1,\n" +
                "                    \"commitStatus\": 1,\n" +
                "                    \"currency\": \"CNY\"\n" +
                "                },\n" +
                "                \"approvalExtendInfo\": {\n" +
                "                    \"costCenter2\": \"子公司干部访谈，客服工作沟通。\"\n" +
                "                },\n" +
                "                \"approvalRankInfo\": {\n" +
                "                    \"rankID\": \"\",\n" +
                "                    \"rankName\": \"\"\n" +
                "                },\n" +
                "                \"hotelProductInfo\": {\n" +
                "                    \"productType\": \"DOMESTIC_HOTEL\",\n" +
                "                    \"averagePrice\": 0,\n" +
                "                    \"checkInBeginDate\": \"2024-12-16\",\n" +
                "                    \"checkInCityCode\": \"\",\n" +
                "                    \"checkInCityInfoList\": [\n" +
                "                        {\n" +
                "                            \"cityName\": \"null\",\n" +
                "                            \"cityNameEn\": \"InvalidCity\"\n" +
                "                        }\n" +
                "                    ],\n" +
                "                    \"checkInEndDate\": \"2024-12-23\",\n" +
                "                    \"checkOutBeginDate\": \"2024-12-16\",\n" +
                "                    \"checkOutEndDate\": \"2024-12-23\",\n" +
                "                    \"controlBudget\": false,\n" +
                "                    \"currency\": \"   \",\n" +
                "                    \"effectivenessAmount\": 0,\n" +
                "                    \"originalEffectivenessAmount\": 0,\n" +
                "                    \"maxStarRating\": 0,\n" +
                "                    \"maxPrice\": 0,\n" +
                "                    \"minPrice\": 0,\n" +
                "                    \"minStarRating\": 0,\n" +
                "                    \"preVerifyFields\": 0,\n" +
                "                    \"roomCount\": 0,\n" +
                "                    \"roomNightPrice\": 0,\n" +
                "                    \"skipFields\": 32,\n" +
                "                    \"destCityName\": \"null\",\n" +
                "                    \"regionName\": \"\",\n" +
                "                    \"travelStandControlModel\": \"\",\n" +
                "                    \"verifyFields\": 0,\n" +
                "                    \"passengerAmountCheck\": 0\n" +
                "                }\n" +
                "            }",HotelApprovalInfo.class)
        GetCityBaseInfoResponseType getCityBaseInfoResponseType = new GetCityBaseInfoResponseType()
        getCityBaseInfoResponseType.cityBaseInfo = Arrays.asList(new CityBaseInfoEntity(cityId: 559, cityName: "Test City"))
        WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo = WrapperOfCityBaseInfo.builder()
                .getCityBaseInfoResponseType(getCityBaseInfoResponseType)
                .cityInput(new CityInput(cityId: 559)).build()
        when:
        def result = new MapperOfVerifyApprovalResultResponse().billOriginCity(hotelApprovalInfo, cityBaseInfo, false)
        then:
        result == null

        when:
        hotelApprovalInfo = JsonUtil.fromJson("{\n" +
                "                \"approvalBaseInfo\": {\n" +
                "                    \"approvalNumber\": \"34814928756\",\n" +
                "                    \"approvalNumberMain\": \"34814928744\",\n" +
                "                    \"corpID\": \"715700\",\n" +
                "                    \"commitCorpEmpID\": \"2174549113\",\n" +
                "                    \"approvalExpiredTime\": \"2025-11-05\",\n" +
                "                    \"endorsementNo\": \"34814928744\",\n" +
                "                    \"endorsementName\": \"北汽重卡驾驶室交流\",\n" +
                "                    \"available\": true,\n" +
                "                    \"createTime\": \"2024-11-05 11:04:38\",\n" +
                "                    \"approvalStatus\": 1,\n" +
                "                    \"externalId\": \"4400c63e-bfa2-4921-ac65-a57619d19857\",\n" +
                "                    \"travelDateStart\": \"2024-11-06\",\n" +
                "                    \"travelDateEnd\": \"2024-11-09\",\n" +
                "                    \"totalPrice\": 2600,\n" +
                "                    \"commitStatus\": 1,\n" +
                "                    \"currency\": \"CNY\",\n" +
                "                    \"travelDays\": 4\n" +
                "                },\n" +
                "                \"approvalExtendInfo\": {},\n" +
                "                \"approvalRankInfo\": {\n" +
                "                    \"rankID\": \"\",\n" +
                "                    \"rankName\": \"\"\n" +
                "                },\n" +
                "                \"hotelProductInfo\": {\n" +
                "                    \"productType\": \"DOMESTIC_HOTEL\",\n" +
                "                    \"checkInBeginDate\": \"2024-11-06\",\n" +
                "                    \"checkInCityCode\": \"213,1358\",\n" +
                "                    \"checkInCityInfoList\": [\n" +
                "                        {\n" +
                "                            \"cityName\": \"常州\",\n" +
                "                            \"cityNameEn\": \"Changzhou\",\n" +
                "                            \"cityId\": \"213\",\n" +
                "                            \"compatibleCityList\": [\n" +
                "                                {\n" +
                "                                    \"cityName\": \"溧阳\",\n" +
                "                                    \"cityNameEn\": \"Liyang\",\n" +
                "                                    \"cityId\": \"1358\",\n" +
                "                                    \"cityType\": \"1\"\n" +
                "                                }\n" +
                "                            ]\n" +
                "                        }\n" +
                "                    ],\n" +
                "                    \"checkInEndDate\": \"2024-11-09\",\n" +
                "                    \"checkOutBeginDate\": \"2024-11-06\",\n" +
                "                    \"checkOutEndDate\": \"2024-11-09\",\n" +
                "                    \"controlBudget\": false,\n" +
                "                    \"currency\": \"CNY\",\n" +
                "                    \"effectivenessAmount\": 0,\n" +
                "                    \"originalEffectivenessAmount\": 0,\n" +
                "                    \"maxStarRating\": 0,\n" +
                "                    \"maxPrice\": 2600,\n" +
                "                    \"minPrice\": 0,\n" +
                "                    \"minStarRating\": 0,\n" +
                "                    \"passengerInfoList\": [\n" +
                "                        {\n" +
                "                            \"passengerId\": \"578513902\",\n" +
                "                            \"passengerName\": \"袁永香\",\n" +
                "                            \"passengerNameEn\": \"\",\n" +
                "                            \"uID\": \"2174549113\",\n" +
                "                            \"eID\": \"040\",\n" +
                "                            \"credentialsNumber\": \"\",\n" +
                "                            \"rankID\": \"\",\n" +
                "                            \"rankName\": \"\",\n" +
                "                            \"approvalExtendInfo\": {\n" +
                "                                \"costCenter1\": \"\",\n" +
                "                                \"costCenter2\": \"\",\n" +
                "                                \"costCenter3\": \"\",\n" +
                "                                \"costCenter4\": \"\",\n" +
                "                                \"costCenter5\": \"\",\n" +
                "                                \"costCenter6\": \"\",\n" +
                "                                \"journeyReason\": \"\",\n" +
                "                                \"project\": \"\"\n" +
                "                            },\n" +
                "                            \"policyEID\": \"\",\n" +
                "                            \"policyRankID\": \"\",\n" +
                "                            \"policyRankName\": \"\",\n" +
                "                            \"verifyFields\": 0,\n" +
                "                            \"mobilePhone\": \"\"\n" +
                "                        }\n" +
                "                    ],\n" +
                "                    \"preVerifyFields\": 0,\n" +
                "                    \"skipFields\": 0,\n" +
                "                    \"destCityName\": \"常州,溧阳\",\n" +
                "                    \"regionName\": \"常州\",\n" +
                "                    \"verifyFields\": 0\n" +
                "                }\n" +
                "            }",HotelApprovalInfo.class)
        result = new MapperOfVerifyApprovalResultResponse().billOriginCity(hotelApprovalInfo, cityBaseInfo, false)
        then:
        result == "常州"
    }


    @Unroll
    def "testBillCheckIn with different scenarios"() {
        given:
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
        new MockUp<DateDisplayUtil>() {
            @Mock
            public static String ymdShortString(Date date, String locale) {
                return CalendarUtil.format(date,"yyyy-MM-dd");
            }
        }
        MapperOfVerifyApprovalResultResponse mapper = new MapperOfVerifyApprovalResultResponse()
        HotelApprovalInfo hotelApprovalInfo = new HotelApprovalInfo(
                hotelProductInfo: new HotelProductInfo(
                        checkInBeginDate: "2024-12-25", checkInEndDate: "2024-12-27",
                        checkOutBeginDate: "2024-12-26", checkOutEndDate: "2024-12-28"))
        List<ApprovalBillChecklistType> approvalBillChecklistTypes = new ArrayList<>()
        IntegrationSoaRequestType integrationSoaRequestType = new IntegrationSoaRequestType()
        when:
        def result = mapper.billCheckIn(hotelApprovalInfo, approvalBillChecklistTypes, integrationSoaRequestType)

        then:
        result == "2024-12-25-2024-12-27"
    }


    @Unroll
    def "testBuildVerifyResultsOfApprovalBillChecklistType with #description"() {
        given:
        new MockUp<CurrencyDisplayUtil>(){
            @Mock
            public static String currencyString(CurrencyDisplayInfo currencyDisplayInfo) {
                return currencyDisplayInfo.getCurrency() + currencyDisplayInfo.getNumber();
            }
        }
        MapperOfVerifyApprovalResultResponse mapper = new MapperOfVerifyApprovalResultResponse()
        List<ApprovalBillChecklistType> approvalBillChecklistTypes = [
                new ApprovalBillChecklistType(fieldName: "BudgetAmount", fieldValue: "1000", inputValue: "500", inControl: true,
                        fieldValueDetail: new FieldValueDetailType(fieldValueList: [new FieldInfoType(value: "1000")]),
                        inputValueDetail: new FieldValueDetailType(fieldValueList: [new FieldInfoType(value: "500")])),
                new ApprovalBillChecklistType(fieldName: "BudgetCurrency", fieldValue: "CNY", inputValue: "HKD", inControl: true,
                        fieldValueDetail: new FieldValueDetailType(fieldValueList: [new FieldInfoType(value: "CNY")]),
                        inputValueDetail: new FieldValueDetailType(fieldValueList: [new FieldInfoType(value: "HKD")])),
                new ApprovalBillChecklistType(fieldName: "Hotel.PassportName", fieldValue: "zhangsan", inputValue: "lisi", inControl: false,
                        fieldValueDetail: new FieldValueDetailType(fieldValueList: [new FieldInfoType(value: "zhangsan")]),
                        inputValueDetail: new FieldValueDetailType(fieldValueList: [new FieldInfoType(value: "lisi")]))
        ]
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        when:
        def result = mapper.buildVerifyResultsOfApprovalBillChecklistType(approvalBillChecklistTypes, orderCreateRequestType)
        then:
        result != null
        result.get(0).approvalValue == "CNY1000"
        result.get(0).inputValue == "HKD500"
        result.get(1).approvalValue == "zhangsan"
        result.get(1).inputValue == "lisi"
    }
}
