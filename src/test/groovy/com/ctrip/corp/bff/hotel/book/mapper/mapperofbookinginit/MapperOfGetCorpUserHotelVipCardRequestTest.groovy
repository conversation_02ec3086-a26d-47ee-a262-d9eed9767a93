package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckAvailResponseType
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelBrandItem
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelItem
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelRatePlan
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomItem
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import corp.user.service.group4j.accounts.BizModeBindRelationData
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/29
 */
class MapperOfGetCorpUserHotelVipCardRequestTest extends Specification {
    def myTestClass = new MapperOfGetCorpUserHotelVipCardRequestType()
    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        given:
        def bookingInitRequestType =  new IntegrationSoaRequestType(
                        "userInfo" : new UserInfo(
                                "userId" : "userId"
                        )

        )
        def checkAvailResponseType = new CheckAvailResponseType(
                "hotelRatePlan" : new HotelRatePlan(
                        "roomInfo": new RoomItem(balanceType: "PP"),
                        "hotelInfo" : new HotelItem(
                                "hotelBrandInfo" : new HotelBrandItem(
                                        "groupId" : 1234
                                )
                        )
                )
        )
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo()
        expect:
        myTestClass.convert(Tuple5.of(bookingInitRequestType, checkAvailInfo, null, null, null)) == null
        myTestClass.convert(Tuple5.of(bookingInitRequestType, checkAvailInfo, new QueryBizModeBindRelationResponseType(), null, null)) == null
        myTestClass.convert(Tuple5.of(bookingInitRequestType, checkAvailInfo, new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: []), null, null)) == null
        myTestClass.convert(Tuple5.of(bookingInitRequestType, checkAvailInfo, new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [null]), null, null)) == null
        myTestClass.convert(Tuple5.of(bookingInitRequestType, checkAvailInfo, new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData()]), null, null)) == null
        myTestClass.convert(Tuple5.of(bookingInitRequestType, checkAvailInfo, new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(dimensionId: "u", primaryDimensionId: "uid")]), null, null)) == null
        myTestClass.convert(Tuple5.of(bookingInitRequestType, checkAvailInfo, new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(dimensionId: "userId", primaryDimensionId: "uid")]), null, null)).uid == "uid"
    }
}
