package com.ctrip.corp.bff.hotel.book.processor

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInfo
import com.ctrip.corp.bff.hotel.book.contract.FinishInfoOutput
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2024/12/18 13:47
 *
 */
class ProcessorOfOrderCreateTest extends Specification {


    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }
    def "testTracking"() {
        given:
        ProcessorOfOrderCreate processor = new ProcessorOfOrderCreate()
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        HotelBookInput hotelBookInput = new HotelBookInput(
                hotelDateRangeInfo: new HotelDateRangeInfo(checkIn: "2024-12-18", checkOut: "2024-12-20"))
        orderCreateRequestType.setHotelBookInput(hotelBookInput)
        OrderCreateResponseType orderCreateResponseType = new OrderCreateResponseType(
                cityInfo: new CityInfo(cityId: 1, cityArea: "HK"),
                finishInfo: new FinishInfoOutput(orderId: "6789678"))

        when:
        Map<String, String> trackingMap = processor.tracking(orderCreateRequestType, orderCreateResponseType)

        then:
        trackingMap != null
        trackingMap.get("cityRegion") == "HK"
        trackingMap.get("checkIn") == "2024-12-18"
        trackingMap.get("orderId") == "6789678"
        trackingMap.get("bookMode") == "NORMAL"
        trackingMap.get("cityId") == "1"
        trackingMap.get("checkOut") == "2024-12-20"
    }
}
