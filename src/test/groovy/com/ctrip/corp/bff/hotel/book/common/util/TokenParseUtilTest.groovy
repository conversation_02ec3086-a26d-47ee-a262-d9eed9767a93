package com.ctrip.corp.bff.hotel.book.common.util

import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.common.RcToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2024/11/7 22:23
 *
 */
class TokenParseUtilTest extends Specification {


    def savePoint = new SavePoint()

    void setup() {
        new MockUp<LogUtil>() {
            @Mock
            public static void loggingClogOnly(LogLevelEnum level,
                                               Class<?> type,
                                               String title,
                                               String message,
                                               Map<String, String> indexTags) {
                return;
            }
            @Mock
            public static void loggingClogOnly(LogLevelEnum level,
                                               Class<?> type,
                                               String title,
                                               String message,
                                               Throwable throwable,
                                               Map<String, String> indexTags) {
                return;
            }
        }
        new MockUp<JsonUtil>() {
            @Mock
            public static String toJson(Object obj) {
                return "mock";
            }
        }
    }

    void cleanup() {
        savePoint.rollback()
    }
    def "test OrderCreateToken"() {
        given:
        when:
        def result = TokenParseUtil.parseToken("H4sIAAAAAAAAAOMSCfb0c/dxjXcMCAjyD3P0iXfz8Q9X0uBSMUwzT0qxTDXWNbQ0NtQ1MTYw0E1MNU/WTUq2MEpLNrY0tTQwEWDQYAAA20jsikIAAAA\\u003d", OrderCreateToken.class)
        def result2 = TokenParseUtil.parseToken("H4sIAAAAAAAA_-Pi9fRz8_d0iQ9y9fX0c9FgBAD_0xXNEQAAAA", OrderCreateToken.class)
        then:
        result != null
        result2 != null
    }

    def "test RcToken"() {
        given:
        when:
        def result = TokenParseUtil.parseToken("H4sIAAAAAAAAABNicnGREnnaP-3Zto6Xi1perFsU5Pxkx9pn09qVGN0AXJrx9B0AAAA", RcToken.class)
        then:
        result != null
    }
}
