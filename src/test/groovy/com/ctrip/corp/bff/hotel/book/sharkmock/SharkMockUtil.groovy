package com.ctrip.corp.bff.hotel.book.sharkmock

/**
 * <AUTHOR>
 * @date 2024/7/12 8:14
 *
 */
class SharkMockUtil {
    public static Map<String, String> mapSharks() {
        Map<String, String> maps = new HashMap<>();
        maps.put("ctrip.com.hotel.booking.biz.text.contact.transferdata","联系人：{0}信息不正确，请返回修改")
        maps.put("ctrip.com.hotel.booking.biz.text.client.transferdata","入住人：{0}信息不正确，请返回修改")
        maps.put("ctrip.com.hotel.booking.biz.text.transferdata.email","邮箱")
        maps.put("ctrip.com.hotel.booking.biz.text.transferdata.phone","手机")
        maps.put("ctrip.com.hotel.booking.biz.text.transferdata.card","证件")
        maps.put("ctrip.com.hotel.booking.biz.text.fail.follow.reason.001","订单数据为空")
        maps.put("ctrip.com.hotel.booking.biz.text.fail.follow.reason.002"," 配置数据为空")
        maps.put("com.ctrip.ct.groupvipcardbind.cardnumberletterrangecheckfail", "会员卡号由【{0}-{1}】位数字和字母共同组成，请输入正确卡号")
        maps.put("com.ctrip.ct.groupvipcardbind.cardnumberletterfixedcheckfail", "会员卡号由【{0}】位数字和字母共同组成，请输入正确卡号")
        maps.put("com.ctrip.ct.groupvipcardbind.cardnumberrangecheckfail", "会员卡号由【{0}-{1}】位数字组成，请输入正确卡号")
        maps.put("com.ctrip.ct.groupvipcardbind.cardnumberfixedcheckfail", "会员卡号由【{0}】位数字组成，请输入正确卡号")
        maps.put("key.corp.hotel.CorpAggHotelSaleStrategyService.checkBookingLimition.errorMsg.SHIELD.customId", "此页面不支持预订国内酒店")
        maps.put("key.corp.hotel.CorpAggHotelSaleStrategyService.checkBookingLimition.errorMsg.SHIELD", "因贵司差旅政策，不可预订{0}地区的酒店。")
        maps.put("ctrip.com.hotel.booking.biz.text.control.value.noany", "{0}-{1}")
        maps.put("com.ctrip.corp.bff.hotel.book.common.enums.CostCenterResourceEnum.COST_CENTER_CC_1", "成本中心1")
        maps.put("com.ctrip.corp.bff.hotel.book.common.enums.CostCenterResourceEnum.COST_CENTER_CC_2", "成本中心2")
        maps.put("com.ctrip.corp.bff.hotel.book.common.enums.CostCenterResourceEnum.COST_CENTER_CC_3", "成本中心3")
        maps.put("com.ctrip.corp.bff.hotel.book.common.enums.CostCenterResourceEnum.COST_CENTER_CC_4", "成本中心4")
        maps.put("com.ctrip.corp.bff.hotel.book.common.enums.CostCenterResourceEnum.COST_CENTER_CC_5", "成本中心5")
        maps.put("com.ctrip.corp.bff.hotel.book.common.enums.CostCenterResourceEnum.COST_CENTER_CC_6", "成本中心6")
        maps.put("com.ctrip.corp.bff.hotel.book.common.enums.CostCenterResourceEnum.HOTEL_TRAVEL_NUMBER", "酒店关联行程号")

        maps.put("com.ctrip.corp.bff.hotel.book.common.enums.CostCenterResourceEnum.COST_CENTER_CC_1.en-US", "Cost center No.1")
        maps.put("com.ctrip.corp.bff.hotel.book.common.enums.CostCenterResourceEnum.COST_CENTER_CC_2.en-US", "Cost center No.2")
        maps.put("com.ctrip.corp.bff.hotel.book.common.enums.CostCenterResourceEnum.COST_CENTER_CC_3.en-US", "Cost center No.3")
        maps.put("com.ctrip.corp.bff.hotel.book.common.enums.CostCenterResourceEnum.COST_CENTER_CC_4.en-US", "Cost center No.4")
        maps.put("com.ctrip.corp.bff.hotel.book.common.enums.CostCenterResourceEnum.COST_CENTER_CC_5.en-US", "Cost center No.5")
        maps.put("com.ctrip.corp.bff.hotel.book.common.enums.CostCenterResourceEnum.COST_CENTER_CC_6.en-US", "Cost center No.6")
        maps.put("com.ctrip.corp.bff.hotel.book.common.enums.CostCenterResourceEnum.HOTEL_TRAVEL_NUMBER.en-US", "Associated itinerary No. of hotel")
        maps.put("key.corp.bff.hotel.OrderCreateErrorEnum.ONLY_EMPLOYEE_BOOKING", "该房型仅限员工入住，当前入住人包含非员工，请更换其它房型")
        maps.put("ctrip.com.hotel.booking.biz.alter.flash.pay.intercept.FlashOrderUnFinish", "您有{0}笔闪住订单在途，暂不可使用闪住功能，请更换其它支付方式")
        maps.put("key.corp.hotel.ordercreate.rcinfo.rcdesc.LOW_PRICE","请选择未预订差标内房型的原因")
        maps.put("key.corp.hotel.ordercreate.rcinfo.rcdesc.BOOK_AHEAD","未提前预订酒店的原因")
        maps.put("key.corp.hotel.ordercreate.rcinfo.rcdesc.AGREEMENT","请选择未预订协议房型的原因")
        maps.put("key.corp.hotel.booking.biz.text.lowprice.price","根据贵公司差旅政策，因您未预订符合价格房型，故请您选择原因")
        maps.put("key.corp.app.hotel.rc.Reservation","根据贵公司差旅政策，因您未提前{0}天预订，故请您选择原因")
        maps.put("key.corp.app.hotel.rc.CPrice","根据贵公司差旅政策，因您未预订协议房型，故请您选择原因")
        maps.put("ctrip.com.hotel.booking.biz.text.policy.float.over.price.TA","（若继续预订，差补额度将加入差标，后续报销则相应扣减）")
        maps.put("key.corp.hotel.ordercreate.rcinfo.rcroom","房间{0}")
        maps.put("key.corp.hotel.booking.biz.text.lowprice.room.price","根据贵公司差旅政策，{0}未预订符合价格房型的原因是：")
        maps.put("OrderCreateErrorEnum.INVALID_TRAVEL_TOGETHER_PASSENGER","出行人{0}关联审批单状态变更，请返回重新选择")
        maps.put("key.corp.hotel.paymentorder.mixpay.roomfee.title","房费")


        maps.put("corp.bff.hotel.bookinginit.invoiceTip.feeDesc.default","下单后在订单详情页发票信息中查看开票信息")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.feeDesc.ROOM_FEE.CorpSettlement","发票由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.feeDesc.ROOM_FEE.CorpOrder","下单后在订单详情页申请开具发票，由{0}，该房型可开具增值税普票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱地址，发票明细为{1}")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.feeDesc.ROOM_FEE.CorpOrder.Vat","下单后在订单详情页申请开具发票，由{0}，该房型可开具增值税普票、专票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱地址，发票明细为{1}")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.feeDesc.ROOM_FEE.MIX","个人支付部分下单后在订单详情页申请开具发票，由{0}，该房型可开具增值税普票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱，发票明细为{1}。公账支付部分发票由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.feeDesc.ROOM_FEE.MIX.Vat","个人支付部分下单后在订单详情页申请开具发票，由{0}，该房型可开具增值税普票、专票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱，发票明细为{1}。公账支付部分发票由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.feeDesc.ROOM_FEE.HOTEL.MIX","个人支付部分发票由酒店开具，请到前台索取。公账支付发票由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.feeDesc.ROOM_FEE.HOTEL","发票由酒店开具，请到前台索取")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.feeDesc.SERVICE_FEE.CorpSettlement","携程预订商旅管理服务费发票会统一寄送到公司")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.feeDesc.SERVICE_FEE.CorpOrder","下单后在订单详情页申请开具发票，由{0}，可开具增值税普票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱地址，发票明细为{1}")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.feeDesc.SERVICE_FEE.CorpOrder.Vat","下单后在订单详情页申请开具发票，由{0}，可开具增值税普票、专票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱地址，发票明细为{1}")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.feeDesc.INSURANCE.CorpSettlement","开具普票并统一寄送到公司")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.feeDesc.INSURANCE.CorpOrder","下单后在订单详情页申请开具普票")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.feeDesc.PERSON_ACCOUNT_PAY","若使用{0}支付，{0}支付部分统一寄送到公司")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.company.HONGRUI","上海携程宏睿国旅旅行社有限公司开具")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.company.NANTONG","上海携程宏睿国旅旅行社有限公司南通分公司开具")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.company.HUASHENG","西安华圣商旅服务有限公司开具")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.invoiceDetail.BOOKINGACCOMMODATIONFEE","经济代理*代订住宿费")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.invoiceDetail.VATAGENCYSERVICEFEE","经济代理*代理服务费")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.detail.ROOM_FEE.PAY_TYPE.SELF_PAY","个付")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.detail.brief.DInvoice","普票")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.detail.brief.DVatInvoice","可专票")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.detail.ROOM_FEE.CORP_PAY","公账支付部分发票由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司")

        maps.put("corp.bff.hotel.bookinginit.invoiceTip.feeDesc.ROOM_FEE.INVOICE.CorpSettlement","消费凭证由携程开具，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，统一寄送到公司")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.feeDesc.ROOM_FEE.MIX.INVOICE","个人支付部分下单后在订单详情页申请开具发票，由%1\$s，该房型可开具增值税普票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱，发票明细为%2\$s。公账支付部分的消费凭证会统一寄送到公司")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.feeDesc.ROOM_FEE.MIX.INVOICE.Vat","个人支付部分下单后在订单详情页申请开具发票，由%1\$s，该房型可开具增值税普票、专票，开票金额不包含优惠券金额、银行立减金额以及礼品卡支付的部分，通过邮件发送至您的邮箱，发票明细为%2\$s。公账支付部分的消费凭证会统一寄送到公司")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.feeDesc.SERVICE_FEE.INVOICE.CorpSettlement","携程预订商旅管理服务费消费凭证会统一寄送到公司")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.feeDesc.INSURANCE.INVOICE.CorpSettlement","消费凭证由供应商开具并统一寄送到公司")
        maps.put("corp.bff.hotel.bookinginit.invoiceTip.feeDesc.ROOM_FEE.INVOICE.HOTEL","消费凭证由酒店开具，请到前台索取")
        maps.put("key.corp.hotel.bookingInit.invoice.INVOICE.title","消费凭证")

        maps.put("key.corp.app.hotel.rc.CPrice.djcx","当前所选酒店非公司协议酒店/协议房型，请问没有选择预定协议酒店/协议房型的原因是?")
        maps.put("key.corp.app.hotel.rc.Reservation.djcx","当前所选酒店非公司协议酒店/协议房型，请问没有选择预定协议酒店/协议房型的原因是?")
        maps.put("key.corp.hotel.OrderFoundationCenterDataSyncService.saveCommonData.errorMsg","预订信息获取失败，请稍后重试或联系商旅客服")
        maps.put("key.corp.hotel.OrderFoundationCenterDataSyncService.saveCommonData.errorMsg.78787","78787error")
        maps.put("key.corp.bff.hotel.OrderCreateErrorEnum.EXTERNAL_EMPLOYEE_ID_ERROR", "%1\$s的企微号不存在，请确认并修改企微号");
        maps.put("key.corp.hotel.TripOrderService.createTrip.errorMsg.78798","78798error")
        return maps;
    }
}