package com.ctrip.corp.bff.hotel.book.common.wrapper

import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckAvailResponseType
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelRatePlan
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomItem
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelItem
import com.ctrip.corp.bff.framework.hotel.common.util.WaitFutureUtil
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken
import com.ctrip.corp.bff.framework.template.entity.ActionInfo
import com.ctrip.corp.bff.framework.template.entity.MapString
import com.ctrip.corp.bff.hotel.book.qconfig.QConfigOfCodeMappingConfig
import com.ctrip.corp.bff.hotel.book.qconfig.entity.CodeMappingConfig
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2024/11/5 16:13
 *
 */
class WrapperOfCheckAvailTest extends Specification {
    @Unroll
    def "buildActionInfo"() {
        given:
        def qConfigOfCodeMappingConfig = new QConfigOfCodeMappingConfig()
        qConfigOfCodeMappingConfig.setCodeMappingEntities([new CodeMappingConfig(
                actionName: "corphotelroomavailableserviceclient.checkavail",
                mappingActionRelations: ["50119": "recommendRoom_reloadRoomList"],
        )])
        expect:
        acitonInfo == WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(new CheckAvailResponseType(responseCode: responseCode, hotelRatePlan: new HotelRatePlan(roomInfo: new RoomItem())))
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken(balanceType: "PP")))
                .buildActionInfo(responseCode, qConfigOfCodeMappingConfig);
        where:
        responseCode || acitonInfo
        20000        || null
        50119        || new ActionInfo(actionType: "recommendRoom_reloadRoomList", dataExtInfo: [new MapString(key: "checkAvailResponseCode", value: "50119")])
        99999        || null
    }

    @Unroll
    def "getStar should return correct star value"() {
        given:
        def hotelItem = new HotelItem(star: expectedStar)
        def roomItem = new RoomItem(balanceType: "PP")
        def hotelRatePlan = new HotelRatePlan(hotelInfo: hotelItem, roomInfo: roomItem)
        def checkAvailResponseType = new CheckAvailResponseType(hotelRatePlan: hotelRatePlan)
        def resourceToken = new ResourceToken(roomResourceToken: new RoomResourceToken(balanceType: "PP"))
        
        when:
        def wrapperOfCheckAvail = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(resourceToken)
                .build()
        def checkAvailInfo = wrapperOfCheckAvail.getCheckAvailInfo()
        def actualStar = checkAvailInfo.getStar()
        
        then:
        actualStar == expectedStar
        
        where:
        expectedStar << [0, 1, 2, 3, 4, 5, 10, 100]
    }

    def "getStar should return 0 when hotelInfo is null"() {
        given:
        def roomItem = new RoomItem(balanceType: "PP")
        def hotelRatePlan = new HotelRatePlan(hotelInfo: null, roomInfo: roomItem)
        def checkAvailResponseType = new CheckAvailResponseType(hotelRatePlan: hotelRatePlan)
        def resourceToken = new ResourceToken(roomResourceToken: new RoomResourceToken(balanceType: "PP"))
        
        when:
        def wrapperOfCheckAvail = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(resourceToken)
                .build()
        def checkAvailInfo = wrapperOfCheckAvail.getCheckAvailInfo()
        def actualStar = checkAvailInfo.getStar()
        
        then:
        actualStar == 0
    }

    def "getStar should return 0 when hotelInfo.star is null"() {
        given:
        def hotelItem = new HotelItem(star: null)
        def roomItem = new RoomItem(balanceType: "PP")
        def hotelRatePlan = new HotelRatePlan(hotelInfo: hotelItem, roomInfo: roomItem)
        def checkAvailResponseType = new CheckAvailResponseType(hotelRatePlan: hotelRatePlan)
        def resourceToken = new ResourceToken(roomResourceToken: new RoomResourceToken(balanceType: "PP"))
        
        when:
        def wrapperOfCheckAvail = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(resourceToken)
                .build()
        def checkAvailInfo = wrapperOfCheckAvail.getCheckAvailInfo()
        def actualStar = checkAvailInfo.getStar()
        
        then:
        actualStar == 0
    }
}
