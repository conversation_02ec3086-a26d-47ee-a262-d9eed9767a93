package com.ctrip.corp.bff.hotel.book.processor

import com.ctrip.corp.bff.framework.hotel.common.mapper.*
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfSearchApproval
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant
import com.ctrip.corp.bff.hotel.book.common.mapper.MapperOfSSOInfoQueryRequestType
import com.ctrip.corp.bff.hotel.book.contract.ApprovalDetailSearchRequestType
import com.ctrip.corp.bff.hotel.book.handler.corpaccountqueryservice.HandlerOfGeneralSearchAccountInfo
import com.ctrip.corp.bff.hotel.book.handler.corpbffprofileservice.HandlerOfSSOInfoQuery
import com.ctrip.corp.bff.hotel.book.handler.corpbffspecificservice.HandlerOfApprovalTextInfo
import com.ctrip.corp.bff.hotel.book.handler.corpbffspecificservice.HandlerOfBatchApprovalDefault
import com.ctrip.corp.bff.hotel.book.handler.corpconfigurationservice.HandlerOfGetSubAccountConfig
import com.ctrip.corp.bff.hotel.book.handler.corphotelbookqueryservice.HandlerOfGetCityBaseInfo
import com.ctrip.corp.bff.hotel.book.handler.corpuserinfoservice4j.HandlerOfGetCorpUserInfo
import com.ctrip.corp.bff.hotel.book.handler.preapprovalservice.HandlerOfSearchApproval
import com.ctrip.corp.bff.hotel.book.mapper.mapperofapprovaldetailsearch.MapperOfApprovalDetailSearchResponseType
import com.ctrip.corp.bff.hotel.book.mapper.mapperofapprovaldetailsearch.MapperOfBatchApprovalDefaultRequestByDetail
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfApprovalTextInfoRequestType
import com.ctrip.corp.bff.specific.contract.ApprovalBaseInfo
import com.ctrip.corp.bff.specific.contract.ApprovalInfo
import com.ctrip.corp.bff.specific.contract.BatchApprovalDefaultResponseType
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.CityInfo
import com.ctrip.corp.foundation.timezone.exception.TimeZoneNotFoundException
import com.ctrip.corp.foundation.timezone.service.CorpGenericTimeZoneService
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2024-12-11
 * */
class ProcessorOfApprovalDetailSearchTest extends Specification {

    def accountInfo = Mock(WrapperOfAccount.AccountInfo.class)
    def processor = new ProcessorOfApprovalDetailSearch()
    def approvalInfo = Mock(WrapperOfSearchApproval.ApprovalInfo.class)

    def savePoint = new SavePoint()

    def setup() {
        def handlerOfGeneralSearchAccountInfo = Mock(HandlerOfGeneralSearchAccountInfo)
        processor.handlerOfGeneralSearchAccountInfo = handlerOfGeneralSearchAccountInfo
        processor.handlerOfGetCorpUserInfo = Mock(HandlerOfGetCorpUserInfo)
        processor.handlerOfGetSubAccountConfig = Mock(HandlerOfGetSubAccountConfig)
        processor.handlerOfSSOInfoQuery = Mock(HandlerOfSSOInfoQuery)
        processor.handlerOfApprovalTextInfo = Mock(HandlerOfApprovalTextInfo)
        processor.handlerOfBatchApprovalDefault = Mock(HandlerOfBatchApprovalDefault)
        processor.handlerOfSearchApproval = Mock(HandlerOfSearchApproval)
        processor.mapperOfGeneralSearchAccountInfoRequest = Mock(MapperOfGeneralSearchAccountInfoRequest)
        processor.mapperOfGetCorpUserInfoRequest = Mock(MapperOfGetCorpUserInfoRequest)
        processor.mapperOfGetSubAccountConfigRequest = Mock(MapperOfGetSubAccountConfigRequest)
        processor.mapperOfSSOInfoQueryRequestType = Mock(MapperOfSSOInfoQueryRequestType)
        processor.mapperOfApprovalTextInfoRequestType = Mock(MapperOfApprovalTextInfoRequestType)
        processor.mapperOfBatchApprovalDefaultRequest = Mock(MapperOfBatchApprovalDefaultRequestByDetail)
        processor.mapperOfSearchApprovalRequest = Mock(MapperOfSearchApprovalRequest)
        processor.mapperOfApprovalDetailSearchResponseType = Mock(MapperOfApprovalDetailSearchResponseType)

        approvalInfo.getCheckInCityCode() >> ["1", "2", "3"]
    }

    void cleanup() {
        savePoint.rollback()
    }


    def "buildSearchApprovalRequest"() {
        given:
        def approvalDetailSearchRequest = new ApprovalDetailSearchRequestType()
        approvalDetailSearchRequest.setCorpPayInfo(new CorpPayInfo(corpPayType: "public"))
        def approvalInfo = new ApprovalInfo(defaultApproval: CommonConstant.OPEN, approvalBaseInfo:
                new ApprovalBaseInfo(subApprovalNo: "subNo", masterApprovalNo: "mainNo"))
        def batchApprovalDefaultResponseType = new BatchApprovalDefaultResponseType(approvalInfos: [approvalInfo])

        when:
        def result = processor.buildSearchApprovalRequest(approvalDetailSearchRequest, accountInfo, batchApprovalDefaultResponseType)

        then:
        result != null
    }

    def "buildCityIdToOffsetMap returns empty map when corpGenericTimeZoneService is null"() {
        given:
        def approvalInfo = Mock(WrapperOfSearchApproval.ApprovalInfo)

        when:
        def result = processor.buildCityIdToOffsetMap(null, approvalInfo)

        then:
        result.isEmpty()
    }

    def "buildCityIdToOffsetMap returns empty map when approvalInfo is null"() {
        given:
        def corpGenericTimeZoneService = Mock(CorpGenericTimeZoneService)

        when:
        def result = processor.buildCityIdToOffsetMap(corpGenericTimeZoneService, null)

        then:
        result.isEmpty()
    }

    def "buildCityIdToOffsetMap returns empty map when checkInCityInfos is empty"() {
        given:
        def corpGenericTimeZoneService = Mock(CorpGenericTimeZoneService)
        def approvalInfo = Mock(WrapperOfSearchApproval.ApprovalInfo) {
            getCheckInCityInfos() >> []
        }

        when:
        def result = processor.buildCityIdToOffsetMap(corpGenericTimeZoneService, approvalInfo)

        then:
        result.isEmpty()
    }

    def "buildCityIdToOffsetMap returns correct map for valid cityIds"() {
        given:
        def corpGenericTimeZoneService = Mock(CorpGenericTimeZoneService) {
            getZoneId(1) >> "Asia/Shanghai"
            getZoneId(2) >> "Asia/Tokyo"
        }
        def approvalInfo = Mock(WrapperOfSearchApproval.ApprovalInfo) {
            getCheckInCityInfos() >> [
                    new CityInfo(cityId: "1"),
                    new CityInfo(cityId: "2")
            ]
        }

        when:
        def result = processor.buildCityIdToOffsetMap(corpGenericTimeZoneService, approvalInfo)

        then:
        result.size() == 2
        result["1"] == "480"
        result["2"] == "540"
    }

    def "buildCityIdToOffsetMap handles TimeZoneNotFoundException"() {
        given:
        def corpGenericTimeZoneService = Mock(CorpGenericTimeZoneService) {
            getZoneId(1) >> { throw new TimeZoneNotFoundException("Time zone not found") }
        }
        def approvalInfo = Mock(WrapperOfSearchApproval.ApprovalInfo) {
            getCheckInCityInfos() >> [new CityInfo(cityId: "1")]
        }

        when:
        def result = processor.buildCityIdToOffsetMap(corpGenericTimeZoneService, approvalInfo)

        then:
        result.size() == 1
        result["1"] == ""
    }
}
