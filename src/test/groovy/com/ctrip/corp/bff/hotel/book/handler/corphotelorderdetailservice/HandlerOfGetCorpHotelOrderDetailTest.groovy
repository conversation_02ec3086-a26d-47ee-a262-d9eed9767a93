package com.ctrip.corp.bff.hotel.book.handler.corphotelorderdetailservice


import mockit.internal.state.SavePoint
import spock.lang.Specification

class HandlerOfGetCorpHotelOrderDetailTest extends Specification{
    def savePoint = new SavePoint()

    def setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "testHandle"() {
        given:
        def handler = new HandlerOfGetCorpHotelOrderDetail()

        when:
        def response = handler.getMethodName()

        then:
        response == "getCorpHotelOrderDetail"
    }
}
