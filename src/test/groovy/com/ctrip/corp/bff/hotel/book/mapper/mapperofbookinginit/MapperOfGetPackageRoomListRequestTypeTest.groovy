package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.agg.hotel.roomavailable.entity.PackageRoomInfoType
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomItem
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;

class MapperOfGetPackageRoomListRequestTypeTest extends Specification {

    def tester = Spy(MapperOfGetPackageRoomListRequestType)

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        given:
        def input = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        input.getRoomItem() >> new RoomItem(packageRoomInfo: new PackageRoomInfoType(packageRoomToken: "a"))
        expect:
        tester.convert(Tuple3.of(new BookingInitRequestType(integrationSoaRequestType: new IntegrationSoaRequestType()), new ResourceToken(roomResourceToken: new RoomResourceToken(adult: 2)), input)).packageTokenList == ["a"]
    }

    def "buildRequestBaseInfoType" () {
        expect:
        tester.buildRequestBaseInfoType(new BookingInitRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")))).userInfo.uid == "a"
    }
}
