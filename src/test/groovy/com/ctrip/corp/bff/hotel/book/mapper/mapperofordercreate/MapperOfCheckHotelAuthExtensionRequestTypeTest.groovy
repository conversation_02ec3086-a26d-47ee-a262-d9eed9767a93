package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextRequestType
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextResponseType
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple8
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.FollowApprovalInfoInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.FollowApprovalResult
import com.ctrip.soa.corp.order.orderindexextservice.v1.GetOrderFoundationDataResponseType
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil
import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckHotelAuthExtension
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
import mockit.Mock
import mockit.MockUp



class MapperOfCheckHotelAuthExtensionRequestTypeTest extends Specification {

    def tester = Spy(new MapperOfCheckHotelAuthExtensionRequestType())
    def savePoint = new SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

        def "buildTripId 返回 null 当 isNotZeroAndNull 为 false"() {
            given:
            def orderCreateRequestType = Mock(OrderCreateRequestType)
            def getOrderFoundationDataResponseType = Mock(GetOrderFoundationDataResponseType)
            def strategyInfoMap = [:] as Map<String, StrategyInfo>
            def artificialReuseNoTripId = 0L

            and: "mock 静态方法"
            GroovyMock(OrderCreateProcessorOfUtil, global: true)
            GroovyMock(TemplateNumberUtil, global: true)
            OrderCreateProcessorOfUtil.buildArtificialReuseNoTripId(orderCreateRequestType, getOrderFoundationDataResponseType) >> artificialReuseNoTripId
            TemplateNumberUtil.isNotZeroAndNull(artificialReuseNoTripId) >> false

            when:
            def result = tester.buildTripId(orderCreateRequestType, strategyInfoMap, getOrderFoundationDataResponseType)

            then:
            result == null
        }


        def "FollowApprovalResult.getFollowOrderNo 不为空时返回其Long值"() {
            given:
            def orderCreateRequestType = Mock(OrderCreateRequestType)
            def strategyInfoMap = [:] as Map<String, StrategyInfo>
            def getOrderFoundationDataResponseType = Mock(GetOrderFoundationDataResponseType)
            def orderCreateToken = Mock(OrderCreateToken)
            def approvalReuseReBookOrderId = 0L
            def artificialReuseNoOrderId = 0L
            def followOrderNo = "333"
            def followApprovalResult = Mock(FollowApprovalResult)

            GroovyMock(OrderCreateProcessorOfUtil, global: true)
            GroovyMock(TemplateNumberUtil, global: true)
            GroovyMock(StringUtil, global: true)
            OrderCreateProcessorOfUtil.buildApprovalReuseReBookOrderId(orderCreateRequestType, strategyInfoMap) >> approvalReuseReBookOrderId
            TemplateNumberUtil.isNotZeroAndNull(approvalReuseReBookOrderId) >> false
            OrderCreateProcessorOfUtil.buildArtificialReuseNoOrderId(orderCreateRequestType, getOrderFoundationDataResponseType) >> artificialReuseNoOrderId
            TemplateNumberUtil.isNotZeroAndNull(artificialReuseNoOrderId) >> false

            orderCreateToken.getFollowApprovalResult() >> followApprovalResult
            followApprovalResult.getFollowOrderNo() >> followOrderNo
            StringUtil.isNotBlank(followOrderNo) >> true
            orderCreateRequestType.getFollowApprovalInfoInput() >> Mock(FollowApprovalInfoInput)

            when:
            def result = tester.buildAuthorizedOrderId(orderCreateRequestType, strategyInfoMap, getOrderFoundationDataResponseType, orderCreateToken)

            then:
            result == 333L
        }

        def "其它情况返回 FollowApprovalInfoInput.getFollowOrderId 的Long值"() {
            given:
            def orderCreateRequestType = Mock(OrderCreateRequestType)
            def strategyInfoMap = [:] as Map<String, StrategyInfo>
            def getOrderFoundationDataResponseType = Mock(GetOrderFoundationDataResponseType)
            def orderCreateToken = Mock(OrderCreateToken)
            def approvalReuseReBookOrderId = 0L
            def artificialReuseNoOrderId = 0L
            def followApprovalResult = null
            def followOrderId = "444"
            def followApprovalInfoInput = Mock(FollowApprovalInfoInput)

            GroovyMock(OrderCreateProcessorOfUtil, global: true)
            GroovyMock(TemplateNumberUtil, global: true)
            GroovyMock(StringUtil, global: true)
            OrderCreateProcessorOfUtil.buildApprovalReuseReBookOrderId(orderCreateRequestType, strategyInfoMap) >> approvalReuseReBookOrderId
            TemplateNumberUtil.isNotZeroAndNull(approvalReuseReBookOrderId) >> false
            OrderCreateProcessorOfUtil.buildArtificialReuseNoOrderId(orderCreateRequestType, getOrderFoundationDataResponseType) >> artificialReuseNoOrderId
            TemplateNumberUtil.isNotZeroAndNull(artificialReuseNoOrderId) >> false

            orderCreateToken.getFollowApprovalResult() >> followApprovalResult
            StringUtil.isNotBlank(null) >> false

            orderCreateRequestType.getFollowApprovalInfoInput() >> followApprovalInfoInput
            followApprovalInfoInput.getFollowOrderId() >> followOrderId

            when:
            def result = tester.buildAuthorizedOrderId(orderCreateRequestType, strategyInfoMap, getOrderFoundationDataResponseType, orderCreateToken)

            then:
            result == 444L
        }

    def "convert" () {
        given:
        def accountInfo = Mock(WrapperOfAccount.AccountInfo)
        def getTravelPolicyContextResponseType = new GetTravelPolicyContextResponseType()
        def checkTravelPolicyResponseType = new CheckTravelPolicyResponseType()
        def queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType()
        def userId = "a"
        def orderCreateRequestType = new OrderCreateRequestType(
                cityInput: new CityInput(cityId: 2),
                hotelBookInput: new HotelBookInput(roomQuantity: 1),
                followApprovalInfoInput: new FollowApprovalInfoInput(followOrderId: 2),
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: userId))
        )
        def resourceToken = new ResourceToken()
        def baseCheckAvailInfo = Mock(WrapperOfCheckAvail.BaseCheckAvailInfo)
        def qconfigOfCertificateInitConfig = new QconfigOfCertificateInitConfig()
        def strategyInfoMap = [:] as Map<String, StrategyInfo>
        def getOrderFoundationDataResponseType = new GetOrderFoundationDataResponseType()
        def orderCreateToken = new OrderCreateToken()

        def wrapper = WrapperOfCheckHotelAuthExtension.builder()
                .withAccountInfo(accountInfo)
                .withGetTravelPolicyContextResponseType(getTravelPolicyContextResponseType)
                .withCheckTravelPolicyResponseType(checkTravelPolicyResponseType)
                .withQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                .withOrderCreateRequestType(orderCreateRequestType)
                .withResourceToken(resourceToken)
                .withCheckAvailInfo(baseCheckAvailInfo)
                .withQconfigOfCertificateInitConfig(qconfigOfCertificateInitConfig)
                .withStrategyInfoMap(strategyInfoMap)
                .withGetOrderFoundationDataResponseType(getOrderFoundationDataResponseType)
                .withOrderCreateToken(orderCreateToken)
                .build()

        when:
        def result = tester.convert(Tuple1.of(wrapper))

        then:
        result.uid == userId
    }

    def "buildTripId 返回 artificialReuseNoTripId 当 isNotZeroAndNull 为 true"() {
        given:
        def orderCreateRequestType = Mock(OrderCreateRequestType)
        def getOrderFoundationDataResponseType = Mock(GetOrderFoundationDataResponseType)
        def strategyInfoMap = [:] as Map<String, StrategyInfo>
        def artificialReuseNoTripId = 123L

        // 用 JMockit mock 静态方法
        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            Long buildArtificialReuseNoTripId(OrderCreateRequestType req, GetOrderFoundationDataResponseType resp) {
                return artificialReuseNoTripId
            }
        }
        new MockUp<TemplateNumberUtil>() {
            @Mock
            boolean isNotZeroAndNull(Long value) {
                return true
            }
        }

        when:
        def result = tester.buildTripId(orderCreateRequestType, strategyInfoMap, getOrderFoundationDataResponseType)

        then:
        result == artificialReuseNoTripId
    }

    def "buildAuthorizedOrderId 返回 approvalReuseReBookOrderId 当 isNotZeroAndNull 为 true"() {
        given:
        def orderCreateRequestType = Mock(OrderCreateRequestType)
        def getOrderFoundationDataResponseType = Mock(GetOrderFoundationDataResponseType)
        def strategyInfoMap = [:] as Map<String, StrategyInfo>
        def orderCreateToken = Mock(OrderCreateToken)
        def approvalReuseReBookOrderId = 111L

        // JMockit mock静态方法
        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            Long buildApprovalReuseReBookOrderId(OrderCreateRequestType req, Map<String, StrategyInfo> map) {
                return approvalReuseReBookOrderId
            }
            @Mock
            Long buildArtificialReuseNoOrderId(OrderCreateRequestType req, GetOrderFoundationDataResponseType resp) {
                return 0L
            }
        }
        new MockUp<TemplateNumberUtil>() {
            @Mock
            boolean isNotZeroAndNull(Long value) {
                return value == approvalReuseReBookOrderId
            }
        }
        new MockUp<StringUtil>() {
            @Mock
            boolean isNotBlank(String str) {
                return false
            }
        }

        when:
        def result = tester.buildAuthorizedOrderId(orderCreateRequestType, strategyInfoMap, getOrderFoundationDataResponseType, orderCreateToken)

        then:
        result == approvalReuseReBookOrderId
    }

    def "buildAuthorizedOrderId 返回 artificialReuseNoOrderId 当 approvalReuseReBookOrderId 为0 artificialReuseNoOrderId不为0"() {
        given:
        def orderCreateRequestType = Mock(OrderCreateRequestType)
        def getOrderFoundationDataResponseType = Mock(GetOrderFoundationDataResponseType)
        def strategyInfoMap = [:] as Map<String, StrategyInfo>
        def orderCreateToken = Mock(OrderCreateToken)
        def approvalReuseReBookOrderId = 0L
        def artificialReuseNoOrderId = 222L

        // JMockit mock静态方法
        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            Long buildApprovalReuseReBookOrderId(OrderCreateRequestType req, Map<String, StrategyInfo> map) {
                return approvalReuseReBookOrderId
            }
            @Mock
            Long buildArtificialReuseNoOrderId(OrderCreateRequestType req, GetOrderFoundationDataResponseType resp) {
                return artificialReuseNoOrderId
            }
        }
        new MockUp<TemplateNumberUtil>() {
            @Mock
            boolean isNotZeroAndNull(Long value) {
                return value == artificialReuseNoOrderId
            }
        }
        new MockUp<StringUtil>() {
            @Mock
            boolean isNotBlank(String str) {
                return false
            }
        }

        when:
        def result = tester.buildAuthorizedOrderId(orderCreateRequestType, strategyInfoMap, getOrderFoundationDataResponseType, orderCreateToken)

        then:
        result == artificialReuseNoOrderId
    }
}
