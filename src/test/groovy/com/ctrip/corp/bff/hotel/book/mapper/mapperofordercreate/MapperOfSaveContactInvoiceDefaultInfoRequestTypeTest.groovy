package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;

class MapperOfSaveContactInvoiceDefaultInfoRequestTypeTest extends Specification {


    def tester = Spy(new MapperOfSaveContactInvoiceDefaultInfoRequestType())

    def savePoint = new mockit.internal.state.SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert"() {
        expect:
        tester.convert(Tuple1.of(new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a"))))).uid == "a"
    }


    def "buildInvoiceTitleType"() {
        expect:
        new MapperOfSaveContactInvoiceDefaultInfoRequestType().buildInvoiceTitleType(title) == res
        where:
        title | res
        "I"   | "PS"
        "C"   | "CP"
        "P"   | "NC"
        "A"   | null
    }
}
