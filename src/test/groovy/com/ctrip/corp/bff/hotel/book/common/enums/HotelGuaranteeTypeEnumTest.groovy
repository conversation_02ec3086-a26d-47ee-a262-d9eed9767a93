package com.ctrip.corp.bff.hotel.book.common.enums

import spock.lang.Specification

class HotelGuaranteeTypeEnumTest extends Specification {
    void setup() {
    }

    void cleanup() {
    }


    def "buildGuaranteeMethod" () {
        expect:
        HotelGuaranteeTypeEnum.buildGuaranteeMethod(type) == res
        where:
        type || res
        HotelGuaranteeTypeEnum.CORP_CREDIT_CARD_GUARANTEE || "CORP_CREDIT_CARD_GUARANTEE"
    }
}
