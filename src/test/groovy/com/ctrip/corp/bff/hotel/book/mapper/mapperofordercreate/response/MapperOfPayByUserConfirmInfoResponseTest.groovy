package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response

import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum
import com.ctrip.corp.bff.hotel.book.common.enums.PaymentGuaranteePolyEnum
import com.ctrip.corp.bff.hotel.book.common.util.HotelPayTypeUtil
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.contract.PayByUserInfo
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2025/6/4 22:15
 *
 */
class MapperOfPayByUserConfirmInfoResponseTest extends Specification {


    def tester = Spy(MapperOfPayByUserConfirmInfoResponse)

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    @Unroll
    def "testBuildPayByUserConfirmInfo with #description"() {
        given: "Mocked dependencies"
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            getEmail() >> "<EMAIL>"
            settlementCurrencyForAgg() >> "CNY"
        }
        def baseCheckAvailInfo = Mock(WrapperOfCheckAvail.BaseCheckAvailInfo) {
            getRoomAmount() >> new BigDecimal("100.00")
            getGuaranteeAmount() >> new BigDecimal("50.00")
            getOriginCurrency() >> "SGD"
            getPaymentGuaranteePolyEnum() >> PaymentGuaranteePolyEnum.PAY_TO_CTRIP
        }
        def orderCreateRequestType = new OrderCreateRequestType(hotelPayTypeInput: [new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY")])
        def mapper = new MapperOfPayByUserConfirmInfoResponse()

        when: "Calling buildPayByUserConfirmInfo"
        def result = mapper.buildPayByUserConfirmInfo(accountInfo, baseCheckAvailInfo, orderCreateRequestType)

        then: "The result should match the expected outcome"
        result != null
        result.emailInfo.email == "<EMAIL>"
        result.payByUserShowAmount == null


        when: "Calling buildPayByUserConfirmInfo"
        orderCreateRequestType = new OrderCreateRequestType(hotelPayTypeInput: [new HotelPayTypeInput(payCode: "ROOM", payType: "GUARANTEE_SELF_PAY")])
        result = mapper.buildPayByUserConfirmInfo(accountInfo, baseCheckAvailInfo, orderCreateRequestType)

        then: "The result should match the expected outcome"
        result != null
        result.emailInfo.email == "<EMAIL>"
        result.payByUserShowAmount != null
        result.payByUserShowAmount.amount == "50.00"
        result.payByUserShowAmount.currency == "SGD"


        when: "Calling buildPayByUserConfirmInfo"
        orderCreateRequestType = new OrderCreateRequestType(hotelPayTypeInput: [new HotelPayTypeInput(payCode: "ROOM", payType: "GUARANTEE_SELF_PAY")])
        result = mapper.buildPayByUserConfirmInfo(accountInfo, baseCheckAvailInfo, orderCreateRequestType)

        then: "The result should match the expected outcome"
        result != null
        result.emailInfo.email == "<EMAIL>"
        result.payByUserShowAmount != null
        result.payByUserShowAmount.amount == "50.00"
        result.payByUserShowAmount.currency == "SGD"
    }


    def "convert"() {
        given:
        OrderCreateToken orderCreateToken = new OrderCreateToken()
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            getEmail() >> "<EMAIL>"
            settlementCurrencyForAgg() >> "CNY"
        }
        def baseCheckAvailInfo = Mock(WrapperOfCheckAvail.BaseCheckAvailInfo) {
            getRoomAmount() >> new BigDecimal("100.00")
            getGuaranteeAmount() >> new BigDecimal("50.00")
            getOriginCurrency() >> "SGD"
            getPaymentGuaranteePolyEnum() >> PaymentGuaranteePolyEnum.PAY_TO_CTRIP
        }
        def orderCreateRequestType = new OrderCreateRequestType(
                payByUserInfo: new PayByUserInfo(payByUser: "T"),
                hotelPayTypeInput: [new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY")])
        def tuple = Tuple4.of(
                orderCreateToken,
                orderCreateRequestType,
                baseCheckAvailInfo,
                accountInfo
        )
        when:
        def result = tester.convert(tuple)
        then:
        result != null
        orderCreateToken.continueTypes.get(0) == "PAY_BY_USER_CONFIRM"
        result.getT2().payByUserConfirmInfo != null
        result.getT1()
        result.getT2().getOrderCreateToken() != null



        when:
        orderCreateRequestType = new OrderCreateRequestType(
                payByUserInfo: new PayByUserInfo(payByUser: "F"),
                hotelPayTypeInput: [new HotelPayTypeInput(payCode: "ROOM", payType: "CORP_PAY")])
        tuple = Tuple4.of(
                orderCreateToken,
                orderCreateRequestType,
                baseCheckAvailInfo,
                accountInfo
        )
        result = tester.convert(tuple)
        then:
        result != null
        !result.getT1()
        result.getT2().getPayByUserConfirmInfo() == null
    }

}
