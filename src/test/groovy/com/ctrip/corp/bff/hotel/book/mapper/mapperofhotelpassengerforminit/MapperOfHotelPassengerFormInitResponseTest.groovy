package com.ctrip.corp.bff.hotel.book.mapper.mapperofhotelpassengerforminit

import com.ctrip.corp.agg.hotel.roomavailable.entity.BookHotelInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.GeographicalSituationInfo
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryBillingGuestInfoType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryBookingRulesType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType
import com.ctrip.corp.agg.hotel.roomavailable.entity.RegionInfo
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil
import com.ctrip.corp.bff.framework.template.entity.contract.integration.FormRuleInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.NameRuleInfo
import com.ctrip.corp.bff.hotel.book.common.enums.passenger.PassengerCertificateTypeEnum
import com.ctrip.corp.bff.hotel.book.common.enums.passenger.PassengerFormTypeEnum
import com.ctrip.corp.bff.hotel.book.qconfig.entity.entityofpassengerform.NameRuleItemEntityEntity
import com.ctrip.corp.bff.hotel.book.qconfig.entity.entityofpassengerform.PassengerFormConfigEntity
import com.ctrip.corp.bff.hotel.book.qconfig.entity.entityofpassengerform.RuleInfoEntity
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * @Author: z.c. wang
 * @Date: 2025/4/18 17:49
 * @Version 1.0
 */
class MapperOfHotelPassengerFormInitResponseTest extends Specification {

    def savePoint = new SavePoint()

    void setup() {
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return key
            }

            @Mock
            public static String getSharkValueFormat(String key, Object... arguments) {
                return key
            }

            @Mock
            public static String getTemplate(String key, BigDecimal num) {
                return key
            }
        }
    }

    void cleanup() {
        savePoint.rollback()
    }

    def mapper = new MapperOfHotelPassengerFormInitResponse()

    def "test buildNameRuleInfos method with empty input list"() {
        given: "An empty list of NameRuleItemEntityEntity and a QueryCheckAvailContextResponseType object"
        List<NameRuleItemEntityEntity> nameRuleItemEntityEntities = []
        QueryCheckAvailContextResponseType checkAvailContextResponseType = new QueryCheckAvailContextResponseType()

        when: "The buildNameRuleInfos method is called"
        List<NameRuleInfo> result = mapper.buildNameRuleInfos(nameRuleItemEntityEntities, null, false, null)

        then: "The result should be null"
        result == null
    }

    @Unroll
    def "test buildNameRuleInfos method with non-empty input list"() {
        given: "A list of NameRuleItemEntityEntity and a QueryCheckAvailContextResponseType object"
        List<NameRuleItemEntityEntity> nameRuleItemEntityEntities = nameRuleEntities

        when: "The buildNameRuleInfos method is called"
        List<NameRuleInfo> result = mapper.buildNameRuleInfos(nameRuleItemEntityEntities, null, false, null)

        then: "The result should match expected NameRuleInfo list"
        result == expectedNameRuleInfos

        where:
        nameRuleEntities                                                                                                                           | contextResponse                                                                                                                                               | expectedNameRuleInfos
        [new NameRuleItemEntityEntity(certificateType: "ID", nameTypes: ["EN"], enNameRuleInfos: [new RuleInfoEntity(formType: "EN_FIRST_NAME")])] | new QueryCheckAvailContextResponseType(bookingRules: new QueryBookingRulesType(billingGuestInfo: new QueryBillingGuestInfoType(guestsNameLanguages: ["en"]))) | [new NameRuleInfo(certificateType: "ID", nameTypes: ["EN"], enNameRuleInfos: [new FormRuleInfo(formType: "EN_FIRST_NAME")])]
    }


    @Unroll
    def "test buildBaseRuleInfoItems method"() {
        given: "A QueryCheckAvailContextResponseType object, employee status, and needCertificate flag"
        QueryCheckAvailContextResponseType checkAvailContextResponseType = contextResponse
        Boolean employee = isEmployee
        Boolean needCertificate = needCert

        when: "The buildBaseRuleInfoItems method is called"
        List<PassengerFormTypeEnum> result = mapper.buildBaseRuleInfoItems(checkAvailContextResponseType, employee, needCertificate)

        then: "The result should match expected base rule info items"
        result == expectedBaseRuleInfoItems

        where:
        contextResponse                                                                                                                                                   | isEmployee | needCert | expectedBaseRuleInfoItems
        new QueryCheckAvailContextResponseType(hotelInfo: new BookHotelInfoEntity(geographicalInfo: new GeographicalSituationInfo(countryInfo: new RegionInfo(id: 2))))   | true       | false    | [PassengerFormTypeEnum.NATIONALITY, PassengerFormTypeEnum.FIRST_NAME_AND_MIDDLE_NAME, PassengerFormTypeEnum.LAST_NAME, PassengerFormTypeEnum.GENDER, PassengerFormTypeEnum.WORKEMAIL, PassengerFormTypeEnum.PHONE, PassengerFormTypeEnum.BIRTH]
        new QueryCheckAvailContextResponseType(hotelInfo: new BookHotelInfoEntity(geographicalInfo: new GeographicalSituationInfo(countryInfo: new RegionInfo(id: 456)))) | false      | true     | [PassengerFormTypeEnum.GENDER, PassengerFormTypeEnum.WORKEMAIL, PassengerFormTypeEnum.PHONE, PassengerFormTypeEnum.BIRTH]
    }

    @Unroll
    def "test buildCertificatePassengerRuleInfos method"() {
        given: "A list of RuleInfoEntity and employee status"
        List<RuleInfoEntity> certificateDefaultRuleInfos = ruleInfos
        Boolean employee = isEmployee

        when: "The buildCertificatePassengerRuleInfos method is called"
        List<FormRuleInfo> result = mapper.buildCertificatePassengerRuleInfos(certificateDefaultRuleInfos, employee, null, null)

        then: "The result should match expected FormRuleInfo list"
        result == expectedFormRuleInfos

        where:
        ruleInfos                                                                                    | isEmployee | expectedFormRuleInfos
        [new RuleInfoEntity(formType: "NATIONALITY"), new RuleInfoEntity(formType: "DOCUMENT_TYPE")] | true       | [new FormRuleInfo(formType: "NATIONALITY"), new FormRuleInfo(formType: "DOCUMENT_TYPE")]
        [new RuleInfoEntity(formType: "NATIONALITY"), new RuleInfoEntity(formType: "DOCUMENT_TYPE")] | false      | [new FormRuleInfo(formType: "NATIONALITY"), new FormRuleInfo(formType: "DOCUMENT_TYPE")]
    }

    @Unroll
    def "Test ruleRequired method"() {

        expect:
        mapper.ruleRequired(defaultRequired, formType, employee, null) == expectedResult

        where:
        defaultRequired | formType | employee || expectedResult
        "T"             | "GENDER" | true     || "T"
        "T"             | "PHONE"  | true     || "T"
        "T"             | "BIRTH"  | true     || "T"
        "T"             | "OTHER"  | false    || "T"
        "F"             | "GENDER" | false    || "F"
        "F"             | "PHONE"  | false    || "F"
        "F"             | "BIRTH"  | false    || "F"
        "F"             | "OTHER"  | false    || "F"
    }

    @Unroll
    def "Test isDelCertificateByNameLanguage method"() {

        expect:
        mapper.isDelCertificateByNameLanguage(roomNeedEnName, passengerCertificateTypeEnum, passengerFormConfigEntity) == expectedResult

        where:
        roomNeedEnName | passengerCertificateTypeEnum               | passengerFormConfigEntity                       || expectedResult
        true           | PassengerCertificateTypeEnum.PASSPORT      | createFormConfigEntity("PASSPORT", ["EN"])      || false
        true           | PassengerCertificateTypeEnum.PASSPORT      | createFormConfigEntity("PASSPORT", ["CN"])      || true
        false          | PassengerCertificateTypeEnum.PASSPORT      | createFormConfigEntity("PASSPORT", ["EN"])      || false
        false          | PassengerCertificateTypeEnum.PASSPORT      | createFormConfigEntity("PASSPORT", ["CN"])      || false
        true           | PassengerCertificateTypeEnum.IDENTITY_CARD | createFormConfigEntity("IDENTITY_CARD", ["CN"]) || true
        true           | PassengerCertificateTypeEnum.IDENTITY_CARD | null                                            || false
    }

    private PassengerFormConfigEntity createFormConfigEntity(String certificateType, List<String> nameTypes) {
        def nameRuleItem = new NameRuleItemEntityEntity(certificateType: certificateType, nameTypes: nameTypes)
        return new PassengerFormConfigEntity(nameFormRuleInfos: [nameRuleItem])
    }

    @Unroll
    def "buildCertificateRuleInfos"() {
        given:
        def result
        when:
        result = mapper.buildCertificateRuleInfos(null, false)
        then:
        result == null

        when:
        result = mapper.buildCertificateRuleInfos([null], false)
        then:
        result == []

        when:
        result = mapper.buildCertificateRuleInfos([new RuleInfoEntity(formType: "FIRST_NAME_AND_MIDDLE_NAME"),
                                                   new RuleInfoEntity(formType: "LAST_NAME"),
                                                   new RuleInfoEntity(formType: "ID")], false)
        then:
        result.size() == 1

    }

    @Unroll
    def "buildPassengerCertificateRuleInfos"() {
        given:
        def result
        when:
        result = mapper.buildPassengerCertificateRuleInfos(null, "CN", false, null)
        then:
        result == null
    }

    @Unroll
    def "convertFormRuleInfos"() {
        given:
        def result
        when:
        result = mapper.convertFormRuleInfos([
                new RuleInfoEntity(regexByNationalityMap: ["default": "asd"])], false, "CH", null)
        then:
        result.get(0).getRegex() == "asd"
    }
}
