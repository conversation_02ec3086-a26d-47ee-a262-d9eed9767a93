package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckOverStandardRcInfoType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckRcResultType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.FloatPriceOverDetailInfoType
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextResponseType
import com.ctrip.corp.agg.hotel.tmc.entity.AccountSettingType
import com.ctrip.corp.approve.ws.contract.RcItem
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfAccountInfoConfig
import com.ctrip.corp.bff.framework.hotel.common.util.WaitFutureUtil
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ReservationResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.common.RcToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CostCenterInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.RCInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfMatchApprovalFlow
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryResponseType
import com.ctrip.corp.hotel.book.query.entity.CityBaseInfoEntity
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

import com.ctrip.corp.bff.framework.specific.common.utils.CostCenterKeyUtils
import com.ctrip.corp.bff.framework.specific.common.entity.costcenter.CostCenterKeyDto
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil

/**
 * <AUTHOR>
 * @date 2024/10/29 22:31
 *
 */
class MapperOfMatchApprovalFlowRequestTypeTest extends Specification {
    def myTestClass = Spy(new MapperOfMatchApprovalFlowRequestType())


    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }

    def "testBuildApproveObj_NullOrderCreateRequest"() {
        given: "Null OrderCreateRequestType"
        def orderCreateRequestType = null
        def checkAvailInfo = Mock(WrapperOfCheckAvail.BaseCheckAvailInfo)

        when: "buildApproveObj is invoked"
        def result = myTestClass.buildApproveObj(orderCreateRequestType, checkAvailInfo, null, null)

        then: "Should handle null gracefully"
        1 * myTestClass.buildApproveObj(null, _, _, _) >> null
        result == null
    }


    def "testBuildApproveObj_EmptyCheckAvailInfo"() {
        given: "Empty BaseCheckAvailInfo"
        def orderCreateRequestType = Mock(OrderCreateRequestType) {
            getIntegrationSoaRequestType() >> new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "shanglv_001", userId: "_SL2240552768"))
        }
        def checkAvailInfo = null

        when: "buildApproveObj is invoked"
        def result = myTestClass.buildApproveObj(orderCreateRequestType, checkAvailInfo, null, null)

        then: "Should handle null gracefully"
        1 * myTestClass.buildApproveObj(_, null, null, null) >> null
        result == null
    }

    def "buildApproveObj"() {
        given:
        QueryCheckAvailContextResponseType checkAvailContextResponse = JsonUtil.fromJson("{\"baseInfo\":{\"userInfo\":{\"uid\":\"_SL2240552768\",\"policyUid\":\"\",\"corpId\":\"shanglv_001\",\"eid\":\"\",\"usersCityId\":2,\"userCountryCode\":\"CN\"},\"customCurrency\":\"CNY\",\"website\":\"Online\",\"feeType\":\"C\",\"quantity\":1,\"startTime\":\"2024-11-06T00:00:00+08\",\"endTime\":\"2024-11-07T00:00:00+08\",\"language\":\"CN\",\"locale\":\"zh-CN\",\"startTimeUTC\":\"2024-11-05T16:00:00Z\",\"endTimeUTC\":\"2024-11-06T16:00:00Z\",\"pOS\":\"zh-CN\",\"pOSType\":{\"pOS\":\"zh-CN\",\"countryId\":1},\"platform\":\"online\",\"bookingWithPersonalAccount\":false},\"hotelInfo\":{\"subHotelId\":6256782,\"masterHotelId\":6256784,\"star\":3,\"rStar\":0,\"starLicence\":false,\"customerEval\":3.5,\"hotelGroupId\":5,\"hotelBrandId\":49,\"balancePeriod\":\"O\",\"oversea\":false,\"tripBusinessHotel\":false,\"cuHotel\":false,\"hasServiceFee\":false,\"localTimeOffsetHours\":0,\"telephone\":\"\",\"geographicalInfo\":{\"locationInfo\":{\"id\":120},\"cityInfo\":{\"id\":2,\"name\":{\"textGB\":\"上海\",\"textEn\":\"Shanghai\"}},\"provinceInfo\":{\"id\":2},\"countryInfo\":{\"id\":1},\"coordinateInfo\":{}},\"address\":{\"textGB\":\"丹巴路28弄26号\",\"textEn\":\"\"},\"hotelName\":{\"textGB\":\"全季酒店(上海长风公园店)\",\"textEn\":\"Ji Hotel Changfeng Park\"},\"currentCountry\":true,\"timeZoneId\":\"Asia/Shanghai\"},\"roomInfo\":{\"subRoomId\":********,\"roomName\":{\"textGB\":\"豪华大床房\",\"textEn\":\"Standard Room\"},\"basicRoomName\":{\"textGB\":\"豪华大床房\",\"textEn\":\"Standard Room\"},\"masterBasicRoomId\":28276398,\"masterBasicRoomName\":{\"textGB\":\"豪华大床房\"},\"roomType\":\"C\",\"tmcPriceType\":\"NONE\",\"balanceType\":\"PP\",\"gdsType\":\"HZ\",\"mealType\":4,\"priceSuitPropertyValueId\":1262885,\"ratePlanKey\":{\"checkAvlID\":0,\"ratePlanID\":\"0\"},\"addPrice\":false,\"invoiceType\":\"Special\",\"invoiceLimitType\":\"NotSupported\",\"applyAreaPropertyId\":2,\"maxGuestCountPerRoom\":2,\"maskRoom\":false,\"hourlyRoom\":false,\"corpPrice\":true,\"manualGds\":false,\"joined30MinutesFreeCancel\":false,\"premiumRoom\":false,\"welfareRoom\":false,\"groupMemberShip\":false,\"earnHotelPoints\":true,\"bonusPointInfo\":{\"bonusPointRoom\":true,\"bonusPointCode\":\"HZZSF\",\"pointsMode\":\"SJHMS\"},\"earlyArrivalTime\":\"2024-11-06T14:00:00+08\",\"lastCancelTime\":\"2024-11-06T00:00:00+08\",\"cnyAmount\":900.00,\"originAmountInfo\":{\"amount\":900.00,\"currency\":\"CNY\",\"exchange\":1},\"customAmountInfo\":{\"amount\":900.00,\"currency\":\"CNY\",\"exchange\":1},\"originCostInfo\":{\"amount\":900,\"currency\":\"CNY\",\"exchange\":1},\"originCostBeforeTaxInfo\":{\"amount\":771.87,\"currency\":\"CNY\",\"exchange\":1},\"roomDiscountInfo\":{\"promotionSwitchOn\":false,\"roomDailyDiscountInfo\":[],\"originPromotionAmount\":{\"price\":0,\"currency\":\"CNY\"},\"customPromotionAmount\":{\"price\":0,\"currency\":\"CNY\"}},\"roomDailyInfo\":[{\"effectDate\":\"2024-11-06T00:00:00+08\",\"cnyAmount\":900.00,\"amount\":900.00,\"customAmount\":900.00,\"afterPromotionCnyAmount\":900.00,\"afterPromotionCustomAmount\":900.00,\"cost\":900,\"costBeforeTax\":771.87,\"customizedCnyAmount\":0,\"mealNumber\":0,\"holdDeadline\":0,\"afterPromotionCustomAmountExcludeTax\":771.87,\"afterPromotionCnyAmountExcludeTax\":771.87,\"customAmountExcludeTax\":771.87,\"cnyAmountExcludeTax\":771.87}],\"paymentRulesInfo\":{\"paymentGuaranteePoly\":\"TRIP\",\"canTravelMoneyPay\":false,\"forceVccPay\":false,\"canCreditCardPay\":false},\"longRentalInfo\":{\"longRental\":false,\"needInquire\":false},\"holdRoomInfo\":{\"holdRoom\":false},\"extraPayTaxMultiCurrencyAmountInfo\":{\"customExtraPayTaxTotalAmount\":0,\"cnyExtraPayTaxTotalAmount\":0},\"pricedRoom\":false,\"generalizedPremiumRoom\":false,\"customAmountExcludeTaxInfo\":{\"amount\":900.00,\"currency\":\"CNY\",\"exchange\":1},\"supportAnticipation\":false,\"roomRightInfoList\":[],\"invoiceTypeExcludeAddPrice\":\"Special\",\"roomMealInfo\":{\"mealType\":4,\"mealDescList\":[\"无餐食\"],\"dailyMealInfoList\":[{\"effectDateUTC\":\"2024-11-05T16:00:00Z\",\"mealDescList\":[\"无餐食\"]},{\"effectDateUTC\":\"2024-11-06T16:00:00Z\",\"mealDescList\":[\"无餐食\"]}]},\"saleRoomTags\":[{\"tagCode\":\"XYJ\",\"tagDesc\":\"\",\"configInfoList\":[{\"position\":301,\"priority\":7,\"style\":\"CONTRACT\"}]}],\"taxDetails\":[],\"pid\":\"AQoGMzU5LjMzEgYzNTkuMzMigQEKf3YxX0NJN3gvUUlReFliRkx4b0hNVEkyTWpnNE5TSUNTRm9xQWtOSE1nWXpOVGt1TXpNNkJqTTFPUzR6TTBJTkNnTkRUbGtTQTBOT1dSb0JNVW9nWmpSaE1qTmtPRE5tTmpJd05ESmtOamt6TmpBMVltSmpNR1EwWXpGa05UYz0qA0NOWTINCgNDTlkSA0NOWRoBMTINCgNDTlkSA0NOWRoBMToCCABCBwjoDxALGAZKBwjoDxALGAdQAVgAYgJIWmgEeAKAAY7x/QI\\u003d\",\"onlyGroupMemberCanBook\":false},\"bookingRules\":{\"cancelPenalties\":{\"paymentGuaranteePoly\":\"TRIP\",\"cancelDeductDetail\":{\"originDeductionPrice\":{\"price\":900.00,\"currency\":\"CNY\"},\"customDeductionPrice\":{\"price\":900.00,\"currency\":\"CNY\"}},\"guaranteePolicyInfo\":{}},\"authorizationRestriction\":{\"latestAuthorizeTime\":\"2024-11-05T23:30:00+08\"},\"confirmRules\":{\"justifyConfirm\":true,\"forbidNotImConfirm\":true},\"welfareRoomRules\":{},\"limitDepartureDateTime\":{\"departureEnd\":\"2024-11-07T12:00:00+08\",\"departureEndUTC\":\"2024-11-07T04:00:00Z\"},\"certificateInfo\":{\"needCertificate\":false},\"minLOS\":1,\"cancelPolicyInfo\":{\"cancelType\":\"LIMIT\",\"freeCancelPolicySceneType\":\"NOT_SUPPORTED\",\"lastCancelTimeUTC\":\"2024-11-05T16:00:00Z\",\"guaranteePolicyInfo\":{\"guaranteeType\":\"FIRST_DAY_PREPAY\",\"paymentGuaranteePoly\":\"TRIP\",\"guaranteeReason\":\"NONE\",\"guaranteePriceInfo\":{\"originGuaranteePrice\":{\"price\":900.00,\"currency\":\"CNY\"},\"customGuaranteePrice\":{\"price\":900.00,\"currency\":\"CNY\"}}}},\"limitArrivalDateTime\":{\"arrivalFromUTC\":\"2024-11-06T06:00:00Z\",\"arrivalEndUTC\":\"2024-11-06T22:00:00Z\",\"defaultArrivalTimeEnd\":false},\"rPLimitInfo\":{\"minLos\":1,\"maxLos\":0,\"minAdvancedBookingDays\":0,\"maxAdvancedBookingDays\":0},\"nationalityRestrictionInfo\":{\"allowCountryCodeList\":[\"CN\"]},\"billingGuestInfo\":{\"guestsNameLanguages\":[\"en\"]},\"restrictRuleList\":[]},\"bookExtInfo\":{\"featureControlFlagMap\":{\"EnableMemberPoints\":false,\"EnableXMinutesFreeCancel\":true,\"BrandNewCustomer\":true,\"EnableShadowDiscount\":false,\"EnableYXH\":false,\"EnablePrepayDiscount\":false,\"EnableNewGroupMember\":false,\"ForbidMultiNationality\":false,\"NewUserCountryCode\":true,\"EnableCNYTWODecimal\":true,\"ShieldMaskRoom\":true,\"EnableAddPrice\":false,\"IgnoreSpecialCancelPolicyNotMatchingFilter\":true,\"StrictCancellationPolicy\":false,\"IgnoreRemitTax\":false,\"ShieldMultiplePrice\":false,\"Enable30MinuteFreeCancel\":false,\"EnablePromotionSubTag\":true,\"EnableHotelNewCustomer\":false,\"DisableIPBlock\":false,\"EnableDirectRate\":false,\"EnableOutputPackageRoom\":false,\"ShieldMaskNakedSale\":false,\"EnableMobileRate\":false,\"EnableMatchPolicyWithExtraPayTax\":false}},\"userRightsInfo\":{\"userChosenCouponInfo\":[],\"hotelNewCustomer\":true,\"userSelectedCountryCode\":\"CN,CN\"},\"reservationToken\":\"AQgBEgoyMDI0LTExLTA2GhYKAUMSAlBQGgRUUklQIgJIWkjFhsUvIgIIACoHCgV6aC1DTg\\u003d\\u003d\",\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2024-10-29 15:37:23.822+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", QueryCheckAvailContextResponseType.class)
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(checkAvailContextResponse)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo()
        OrderCreateRequestType orderCreateRequestType = JsonUtil.fromJson("{\"integrationSoaRequestType\":{\"userInfo\":{\"userId\":\"_SL2240552768\",\"corpId\":\"shanglv_001\",\"groupId\":\"Gr_00006743\",\"pos\":\"CHINA\"},\"language\":\"zh-CN\",\"requestId\":\"09e6ab28dbd5401ab60c768f1f19fbbc\",\"sourceFrom\":\"Online\",\"transactionID\":\"TID.HOTELBOOK.32.B71A8E70A65942E68B393AD698C68009\",\"gatewayHost\":\"ct.ctrip.fat12024.qa.nt.ctripcorp.com\",\"timezoneOffsetMinutesNew\":\"480\",\"logIndices\":[{\"key\":\"groupId\",\"value\":\"Gr_00006743\"},{\"key\":\"timezoneOffsetMinutesNew\",\"value\":\"480\"},{\"key\":\"gatewayHost\",\"value\":\"ct.ctrip.fat12024.qa.nt.ctripcorp.com\"},{\"key\":\"pvid\",\"value\":\"82\"},{\"key\":\"subChannel\",\"value\":\"\"},{\"key\":\"CID\",\"value\":\"09031033110632628895\"},{\"key\":\"EID\"},{\"key\":\"CIP\",\"value\":\"**************\"},{\"key\":\"miceToken\",\"value\":\"\"},{\"key\":\"groupName\",\"value\":\"开卡测试\"},{\"key\":\"gatewayIdc\",\"value\":\"NTGXH\"},{\"key\":\"traceID\",\"value\":\"\"},{\"key\":\"A\",\"value\":\"ordercheck\"},{\"key\":\"CorpID\",\"value\":\"shanglv_001\"},{\"key\":\"C\",\"value\":\"corpbookinghotelbookservice\"},{\"key\":\"M\",\"value\":\"_SL2240552768\"},{\"key\":\"sid\",\"value\":\"34\"},{\"key\":\"S\",\"value\":\"cf8acce0952848b1a1ee990f9082eb02\"},{\"key\":\"T\"},{\"key\":\"pos\",\"value\":\"CHINA\"},{\"key\":\"GE\",\"value\":\"1730187442758\"},{\"key\":\"sourceFrom\",\"value\":\"Online\"},{\"key\":\"corpName\",\"value\":\"胡明明勿改日本站\"},{\"key\":\"RID\",\"value\":\"09e6ab28dbd5401ab60c768f1f19fbbc\"},{\"key\":\"TID\",\"value\":\"TID.HOTELBOOK.32.B71A8E70A65942E68B393AD698C68009\"},{\"key\":\"VID\",\"value\":\"1729220411588.a497XLFJONy2\"}],\"ticket\":\"4UUk4v4Gb0/Wv888R9NXpTO2MUiKDWykvSVku7pnBzbf1qSn5wKh2BVU1wrDamQbwKy/+W9f0PVnjERRdhTSPIvpPVjgTFSRO+rke+CBovk\\u003d\",\"transferInfo\":[{\"key\":\"iframe\",\"value\":\"F\"},{\"key\":\"channel\",\"value\":\"Online\"}]},\"hotelBookPassengerInputs\":[{\"hotelPassengerInput\":{\"uid\":\"_SL2240551829\",\"employee\":\"T\",\"external\":\"F\",\"employeeId\":\"444\"},\"passengerBasicInfo\":{\"gender\":\"U\"},\"name\":\"过滤七\",\"isRegistrant\":\"F\",\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"17788855543\",\"transferPhoneNo\":\"17788855543\"},\"nationalityInfo\":{\"nationalityCode\":\"CN\"}},{\"hotelPassengerInput\":{\"uid\":\"_SL2240552768\",\"employee\":\"T\",\"external\":\"F\",\"employeeId\":\"TR011673\"},\"passengerBasicInfo\":{\"gender\":\"U\",\"birth\":\"1975-11-12\"},\"name\":\"过滤八\",\"isRegistrant\":\"F\",\"phoneInfo\":{\"countryCode\":\"63\",\"phoneNo\":\"17744555555\",\"transferPhoneNo\":\"17744555555\"},\"nationalityInfo\":{\"nationalityCode\":\"CN\"}}],\"hotelInvoiceInfos\":[null],\"cityInput\":{\"cityId\":2},\"hotelContactorInfo\":{\"name\":\"商旅客户\",\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"17744555555\",\"transferPhoneNo\":\"17744555555\"},\"emailInfo\":{\"email\":\"<EMAIL>\",\"transferEmail\":\"<EMAIL>\"}},\"miceInput\":{},\"hotelBookInput\":{\"hotelDateRangeInfo\":{\"checkIn\":\"2024-11-06\",\"checkOut\":\"2024-11-07\"},\"roomQuantity\":1},\"bookModeInfo\":{\"bookingType\":\"NONE\"},\"costAllocationInfo\":{\"costAllocationToken\":\"H4sIAAAAAAAAAOMS5DhzkE2AQYJFid3QwMxcz8BASJGLNz7Yx8jIxMDU1MjczEJIgOPZAmawGjZTY2M9U1QlhhZGlphKpAibIkXYFCXCpigRNgUAlF6ejOUAAAA\\u003d\",\"shareAmounts\":[{\"shareAmount\":{\"amount\":\"533.5\"},\"shareAmountKey\":\"_SL2240551829\"},{\"shareAmount\":{\"amount\":\"533.5\"},\"shareAmountKey\":\"_SL2240552768\"}]},\"resourceTokenInfo\":{\"resourceToken\":\"{\\\"hotelResourceToken\\\":{\\\"masterHotelId\\\":6256784,\\\"hotelId\\\":6256782,\\\"searchHotelTraceId\\\":\\\"f4a23d83f62042d693605bbc0d4c1d57\\\",\\\"hid\\\":\\\"ASIGMjAwLjAwQgFBSgYIABAAGABSbQpndjFfQ0pEeC9RSVFqdkg5QWhvQ1EwY2lBekl3TUNvRE1qQXdNZzBLQTBOT1dSSURRMDVaR2dFeE9pQm1OR0V5TTJRNE0yWTJNakEwTW1RMk9UTTJNRFZpWW1Nd1pEUmpNV1ExTnc9PRAAGAFaA0NOWWINCgNDTlkSA0NOWRoBMWINCgNDTlkSA0NOWRoBMWgDcgYxMDAuMDB6BDEuMDCCASBmNGEyM2Q4M2Y2MjA0MmQ2OTM2MDViYmMwZDRjMWQ1N4oBCDk5Njk3NDczmgEkNmQ1YjQ1NjMtZjI1Mi00NDQ0LWFiNDUtYTgzZTY0Njc5OTNkogECCAA\\\\u003d\\\",\\\"hotelType\\\":\\\"C\\\",\\\"bonusPoint\\\":\\\"F\\\",\\\"hotelGeoInfoResourceToken\\\":{\\\"cityId\\\":2,\\\"locationId\\\":120}},\\\"roomResourceToken\\\":{\\\"roomId\\\":********,\\\"baseRoomId\\\":28276398,\\\"ratePlanTraceLogId\\\":\\\"7a128b28-e4cc-436c-92a3-8496487ce6d5\\\",\\\"allRoomType\\\":\\\"EXIST_C\\\",\\\"bedName\\\":\\\"床型由酒店随机安排\\\",\\\"windowName\\\":\\\"\\\",\\\"breakfastInfo\\\":\\\"无餐食\\\",\\\"confirmType\\\":\\\"IMMEDIATE\\\",\\\"roomType\\\":\\\"C\\\",\\\"balanceType\\\":\\\"PP\\\",\\\"tmcPrice\\\":false,\\\"adult\\\":2,\\\"pId\\\":\\\"AQoGMzU5LjMzEgYzNTkuMzMigQEKf3YxX0NJN3gvUUlReFliRkx4b0hNVEkyTWpnNE5TSUNTRm9xQWtOSE1nWXpOVGt1TXpNNkJqTTFPUzR6TTBJTkNnTkRUbGtTQTBOT1dSb0JNVW9nWmpSaE1qTmtPRE5tTmpJd05ESmtOamt6TmpBMVltSmpNR1EwWXpGa05UYz0qA0NOWTINCgNDTlkSA0NOWRoBMTINCgNDTlkSA0NOWRoBMToCCABCBwjoDxALGAZKBwjoDxALGAdQAVgAYgJIWmgEeAKAAY7x/QI\\\\u003d\\\",\\\"amadeus\\\":false,\\\"supportAccommodation\\\":\\\"F\\\",\\\"unKnowRoomDesc\\\":\\\"\\\",\\\"roomOriginCurrency\\\":\\\"CNY\\\",\\\"roomScenarioEnum\\\":\\\"ROOM_LIST\\\",\\\"ratePlanRequestToken\\\":\\\"ASi1L/0AUK0VACYvqEIgS1UAABAAsAUEG0CwDSCgQABCwSZCzNjqKk6iJklatKQpvRbFVRRFUZUkUVLRWKCm5RqrV2ZZqdbKgj8XjgN6+B+ZAJcAlgCfEGxyyGsypi0nw/PYUDRlWSe1bvUemrK+dY4mXi6sjWa4QUtdI+O1XOLFFVdszxlyhVC1qFSHvfYyVAcB5U4PkRaJkRHT5tNYJonUYw+Q4PAa9mdIw0ziMWT/Fv4p/Onl661UFpZlMeEVd7Bb2H2hKKD0AEgIM4tPHQCCVVWqCfaM1k+JALdULk6hVBPOP1M18ADACBEXEAQejjRrPJw5BxqZpGeGXDINvMG1Cycj5pIOtXzgC2M2IIuDT3SlbJuS6XE8Us8UTuYVtO6uba7adnNhklG0vBJPdZpv7PFIDrWxfelXuuXpHZUq98ywKUir4w2UhZO5dQ1zYAq5aaz0ibkmHMW1ZwqTJ46ua9xmRhGo7mAGhLkgQchIh50rtJWcnXZycXaYDiUNTa2UiWAB8SmK41nBo7+j4lMhDE6L1LEUM/WyKhXnEw/sHRSfitB1dnutsu7WE0O/LvS8XY45cNTwGfWpcKcMXpM2zdeRV1j7kLpUgyRpNk0sYjUbZ77pJhNpL2FL1+4kHdGl2Fss+9DSuYFekCIX0Rq9FkvTmIQ6Wmwz5k2F1jB7iV1NKbi5IB4+Vpxd2Q5EYS4kh4HB5RHUa68U+0Ps97C/w/4N+zPsv7C/wn4K+7Tl/dkT9sUed2ZbdrUd7AUbwR7AH8T+Dwoq+maWG0dUO6zi0d/R4FNZx4mfOpZZqaJbeODR35HgU6AolupZiqbaYQUe/R0TnwJFsVS1cANBMdtKtcMmHv0dFJ9yMeJMPm002+U5zlx6Ld3QSlM8sHcw+BQ9ckWg2ZVPm2c95QYAVihAlB0ELB4nzPQF3hEreg\\\\u003d\\\\u003d\\\",\\\"ratePlanRoomToken\\\":\\\"ASi1L/0AUHEAAAoBQxAAGMWGxS8iAlBQ\\\",\\\"roomUniqueKey\\\":\\\"V1-ASi1L/0AULEAAAjFhsUvEgFDGgROT05FIgJQUDilik0\\\\u003d\\\",\\\"adultNumber\\\":2,\\\"windowType\\\":2},\\\"reservationResourceToken\\\":{\\\"wsId\\\":\\\"9689295b-1d5c-4b73-aed9-2e87460a8ee1\\\",\\\"policyToken\\\":\\\"v1KLUv/QBQ1QkAZpM+JDCLugGDw2vtv9Jq6+ekJNVeVfHfU9ougLhGEWXTys8KGgoqODUAMwA0AMkiWotcUXqerHX7nGrK3mQVAgNLItGYZXFAkXX7YHU7mULZNzB+SG3SGkBrCLfecD4YU+sQFCNwKzKDW5cmTw1veE2H/NXYDzfZ9yIO6CbiXlphFEujdD9GUTSvKe7eeMP4un1vkHQDMUJG5EvOquJNYqzvllrQkTWM8qHwESgpD6jlVdrE66Rup6b8oNLgfUpokJLx3BhDMn54Bg8Luwpuo188Ie88h3Z9clbYLzCP9B1LVhFyAYVUVKHnzvjB4GPh8/EY8ftXSS2oS68yAhcAORtnY4eztcN9c/HUYjSGEKbmoKEKqQuYIqhDlapZLKEqz2fxG6hyr2KPMGopAjgNILWBTaIzR1jEbAI\\\\u003d\\\"},\\\"bookInitResourceToken\\\":{\\\"serviceChargeResourceToken\\\":{\\\"serviceChargeAmount\\\":167.00,\\\"serviceFeeByRoomNight\\\":167.00,\\\"serviceFeeItemResourceTokens\\\":[{\\\"chargeItemCode\\\":\\\"BookingOrderSC\\\",\\\"originalAmount\\\":167.00,\\\"settlementAmount\\\":167.00}]},\\\"aggBookPriceResourceToken\\\":{\\\"currency\\\":\\\"CNY\\\",\\\"accountPaymentAmount\\\":1067.00,\\\"individualPaymentAmount\\\":0,\\\"mixPayAccountExtraRatio\\\":0,\\\"mixPayAccountExtraAmount\\\":0},\\\"roomPriceResourceToken\\\":{}}}\"},\"corpPayInfo\":{\"corpPayType\":\"public\"},\"userActionInfo\":{\"userActionToken\":\"def\"},\"rcInfos\":[{\"rcToken\":\"H4sIAAAAAAAAAAFLALT_CglMT1dfUFJJQ0USAkFCGjfkuK3oi7HmlocJ55uu55qE5Zyw6KGM5pS_5Yy66IyD5Zu05YaF5rKh5pyJ5Y2P6K6u6YWS5bqXIgFG7tJgcEsAAAA\"}],\"followApprovalInfoInput\":{\"aiFollow\":\"F\",\"artificialFollow\":\"F\",\"followTripId\":\"\",\"followSelected\":\"F\"},\"bookUrlInfos\":[{\"urlType\":\"SUCCESS\"},{\"urlType\":\"S_BACK\"},{\"urlType\":\"E_BACK\"},{\"urlType\":\"FROM_URL\"}],\"paymentInfoInput\":{\"canWechatPayByOthers\":\"F\"},\"clientInfo\":{},\"offlineInfo\":{\"forciblyAssure\":\"F\",\"especialAuth\":\"F\",\"autoConfirm\":\"F\",\"exigentOrder\":\"F\",\"operationRule\":\"\"},\"roomInfoInput\":{\"roomRemark\":\"\",\"roomCustomRemark\":\"\"},\"sendInfo\":{\"sendSMS\":\"F\",\"sendEmail\":\"F\"},\"ssoInput\":{\"ssoKey\":\"6f974250-959e-11ef-9f6d-815f0d819655\"},\"customBedInput\":{},\"flashStayInput\":{\"flashPlatFrom\":\"Online\"},\"hotelPayTypeInput\":[{\"payCode\":\"ROOM\",\"payType\":\"CORP_PAY\"}],\"strategyInfos\":[{\"strategyKey\":\"HOTEL_LIST\",\"strategyValue\":\"T\"}],\"costCenterInfo\":{\"costCenterJsonString\":\"{\\\"items\\\":{\\\"fdefault\\\":{\\\"cost1\\\":\\\"内容1\\\",\\\"cost2\\\":\\\"Z18516GLN964-合理化用药/早癌项目\\\",\\\"cost3\\\":\\\"内容1\\\"},\\\"_SL2240551829\\\":{\\\"name\\\":\\\"过滤七\\\",\\\"cost4\\\":\\\"内容31\\\",\\\"cost5\\\":\\\"\\\",\\\"cost6\\\":\\\"\\\"},\\\"_SL2240552768\\\":{\\\"name\\\":\\\"过滤八\\\",\\\"cost4\\\":\\\"\\\",\\\"cost5\\\":\\\"内容6\\\",\\\"cost6\\\":\\\"内容5\\\"}}}\"},\"useNewOrderCreate\":\"T\"}", OrderCreateRequestType.class)
        when:
        def result = myTestClass.buildApproveObj(orderCreateRequestType, checkAvailInfo, null, null)
        then:
        result != null
    }

    @Unroll
    def "testGetRcItems with different scenarios"() {
        given:
        MapperOfMatchApprovalFlowRequestType mapper = new MapperOfMatchApprovalFlowRequestType()
        List<RCInput> validRcInputs = rcInputs

        when: "calling getRcItems with specific parameters"
        def result = mapper.getRcItems(validRcInputs)

        then: "the result should be as expected"
        result == expectedResult

        where:
        rcInputs                                                                                                             || expectedResult
        [new RCInput(rcToken: TokenParseUtil.generateToken(new RcToken(type: "LOW_PRICE", code: "AA"), RcToken.class))]      || [new RcItem(rcType: 1, rc: "AA")]
        [new RCInput(rcToken: TokenParseUtil.generateToken(new RcToken(type: "LOW_PRICE", code: "AA"), RcToken.class)),
         new RCInput(rcToken: TokenParseUtil.generateToken(new RcToken(type: "AGREEMENT", code: "BB"), RcToken.class)),
         new RCInput(rcToken: TokenParseUtil.generateToken(new RcToken(type: "BOOK_AHEAD", code: "CC"), RcToken.class)),
         new RCInput(rcToken: TokenParseUtil.generateToken(new RcToken(type: "CONFLICT_BOOK", code: "DD"), RcToken.class)),
         new RCInput(rcToken: TokenParseUtil.generateToken(new RcToken(type: "OFFLINE_MODIFY", code: "AA"), RcToken.class))] || [new RcItem(rcType: 1, rc: "AA"),
                                                                                                                                 new RcItem(rcType: 2, rc: "BB"),
                                                                                                                                 new RcItem(rcType: 8, rc: "CC"),
                                                                                                                                 new RcItem(rcType: 128, rc: "DD")]
        [new RCInput(rcToken: TokenParseUtil.generateToken(new RcToken(type: "MODIFY", code: "AA"), RcToken.class))]         || []
        [new RCInput(rcToken: "token3")]                                                                                     || []
        []                                                                                                                   || []
        null                                                                                                                 || []
    }


    @Unroll
    def "testBuildIncludeServiceFee with #description"() {
        given:
        MapperOfMatchApprovalFlowRequestType mapper = new MapperOfMatchApprovalFlowRequestType()
        GetTravelPolicyContextResponseType getTravelPolicyContextResponseType = new GetTravelPolicyContextResponseType(accountSetting: new AccountSettingType(includeServiceCharge: includeServiceFee))

        when:
        boolean result = mapper.buildIncludeServiceFee(roomPayTypeEnum, servicePayTypeEnum, getTravelPolicyContextResponseType)

        then:
        result == expectedResult

        where:
        description                         | roomPayTypeEnum                     | servicePayTypeEnum        | includeServiceFee | expectedResult
        "roomPayTypeEnum is CASH"           | HotelPayTypeEnum.CASH               | HotelPayTypeEnum.SELF_PAY | false             | true
        "roomPayTypeEnum is CASH"           | HotelPayTypeEnum.GUARANTEE_SELF_PAY | HotelPayTypeEnum.SELF_PAY | false             | true
        "roomPayTypeEnum is CASH"           | HotelPayTypeEnum.GUARANTEE_CORP_PAY | HotelPayTypeEnum.SELF_PAY | false             | true

        "roomPayTypeEnum is CASH"           | HotelPayTypeEnum.CASH               | HotelPayTypeEnum.CORP_PAY | false             | false
        "roomPayTypeEnum is CASH"           | HotelPayTypeEnum.GUARANTEE_SELF_PAY | HotelPayTypeEnum.CORP_PAY | true              | true
        "roomPayTypeEnum is CASH"           | HotelPayTypeEnum.GUARANTEE_CORP_PAY | HotelPayTypeEnum.CORP_PAY | false             | false

        "roomPayTypeEnum is CASH"           | HotelPayTypeEnum.CASH               | HotelPayTypeEnum.NONE     | true              | true
        "roomPayTypeEnum is CASH"           | HotelPayTypeEnum.GUARANTEE_SELF_PAY | null                      | true              | true
        "roomPayTypeEnum is CASH"           | HotelPayTypeEnum.GUARANTEE_CORP_PAY | null                      | true              | true

        "roomPayTypeEnum is CORP_PAY"       | HotelPayTypeEnum.CORP_PAY           | HotelPayTypeEnum.CORP_PAY | false             | true
        "roomPayTypeEnum is CORP_PAY"       | HotelPayTypeEnum.CORP_PAY           | HotelPayTypeEnum.NONE     | true              | true

        "roomPayTypeEnum is MIX_PAY"        | HotelPayTypeEnum.MIX_PAY            | HotelPayTypeEnum.CORP_PAY | false             | true
        "roomPayTypeEnum is MIX_PAY"        | HotelPayTypeEnum.MIX_PAY            | HotelPayTypeEnum.NONE     | true              | true

        "roomPayTypeEnum is FLASH_STAY_PAY" | HotelPayTypeEnum.FLASH_STAY_PAY     | HotelPayTypeEnum.CORP_PAY | false             | true
        "roomPayTypeEnum is FLASH_STAY_PAY" | HotelPayTypeEnum.FLASH_STAY_PAY     | HotelPayTypeEnum.NONE     | true              | true


        "roomPayTypeEnum is SELF_PAY"       | HotelPayTypeEnum.SELF_PAY           | HotelPayTypeEnum.SELF_PAY | false             | true
        "roomPayTypeEnum is FLASH_STAY_PAY" | HotelPayTypeEnum.SELF_PAY           | HotelPayTypeEnum.NONE     | true              | true
    }


    def "testGetOverStandardStage"() {
        given: "Mock dependencies and inputs"
        def mapper = new MapperOfMatchApprovalFlowRequestType()
        def checkTravelPolicyResponseType = new CheckTravelPolicyResponseType()
        def orderCreateRequestType = new OrderCreateRequestType()
        def resourceToken = new ResourceToken()
        def checkRcResultType = new CheckRcResultType()
        def checkOverStandardRcInfoType = new CheckOverStandardRcInfoType()
        def floatPriceOverDetailInfoType = new FloatPriceOverDetailInfoType()
        checkOverStandardRcInfoType.setServiceChargeType("NONE")
        floatPriceOverDetailInfoType.setOverType("OVER_BASE_STANDARD_UPPER_LIMIT")
        checkOverStandardRcInfoType.setFloatPriceOverDetail(floatPriceOverDetailInfoType)
        checkRcResultType.setOverStandardRc([checkOverStandardRcInfoType])
        checkTravelPolicyResponseType.setCheckRcResult(checkRcResultType)

        when: "Calling getOverStandardStage method"
        def result = mapper.getOverStandardStage(checkTravelPolicyResponseType, orderCreateRequestType, resourceToken, null)

        then: "Assert the expected outcomes"
        result == "S" // Replace with the actual expected result
    }

    @Unroll
    def "testBuildRoomPayCorpPay with #description"() {
        given: "A MapperOfMatchApprovalFlowRequestType instance and input parameters"
        def mapper = new MapperOfMatchApprovalFlowRequestType()
        def orderCreateRequestType = Mock(OrderCreateRequestType)
        def resourceToken = Mock(ResourceToken)

        when: "Calling buildRoomPayCorpPay"
        def result = mapper.buildRoomPayCorpPay(roomPayType, orderCreateRequestType, resourceToken)

        then: "The result should match the expected value"
        result == expectedResult

        where:
        description                 | roomPayType               | expectedResult
        "PRBAL scenario"            | HotelPayTypeEnum.PRBAL    | true
        "CORP_PAY scenario"         | HotelPayTypeEnum.CORP_PAY | true
        "MIX_PAY scenario"          | HotelPayTypeEnum.MIX_PAY  | true
        "SELF_PAY scenario"         | HotelPayTypeEnum.SELF_PAY | false
        "Null roomPayType scenario" | null                      | false
    }

    def "matchFlowByCorp"() {
        given:
        def mapper = new MapperOfMatchApprovalFlowRequestType()
        expect:
        result == mapper.matchFlowByCorp(roomHotelPayTypeEnum)
        where:
        roomHotelPayTypeEnum                || result
        HotelPayTypeEnum.CORP_PAY           || true
        HotelPayTypeEnum.MIX_PAY            || true
        HotelPayTypeEnum.FLASH_STAY_PAY     || true
        HotelPayTypeEnum.CORPORATE_CARD_PAY || true
        HotelPayTypeEnum.PRBAL              || true
        HotelPayTypeEnum.SELF_PAY           || false
        HotelPayTypeEnum.CASH               || false
    }

    def "check"() {
        given:
        WrapperOfMatchApprovalFlow wrapperOfMatchApprovalFlow = WrapperOfMatchApprovalFlow.builder()
                .withGetTravelPolicyContextResponseType(new GetTravelPolicyContextResponseType())
                .withCheckTravelPolicyResponseType(new CheckTravelPolicyResponseType())
                .withQueryCheckAvailContextResponseType(new QueryCheckAvailContextResponseType())
                .withOrderCreateRequestType(new OrderCreateRequestType())
                .withResourceToken(new ResourceToken())
                .withBaseCheckAvailInfo(Mock(WrapperOfCheckAvail.BaseCheckAvailInfo))
                .withAccountInfo(Mock(WrapperOfAccount.AccountInfo))
                .withSSOInfoQueryResponseType(new SSOInfoQueryResponseType())
                .withOrderCreateToken(new OrderCreateToken())
                .withQconfigOfCertificateInitConfig(new QconfigOfCertificateInitConfig())
                .withStrategyInfoMap(new HashMap<String, StrategyInfo>())
                .withGetCityBaseInfoResponseType(new GetCityBaseInfoResponseType())
                .withQConfigOfAccountInfoConfig(new QConfigOfAccountInfoConfig())
                .build();
        when:
        def result = new MapperOfMatchApprovalFlowRequestType().check(Tuple1.of(wrapperOfMatchApprovalFlow))
        then:
        result == null
        wrapperOfMatchApprovalFlow.getGetTravelPolicyContextResponseType() != null
        wrapperOfMatchApprovalFlow.getCheckTravelPolicyResponseType() != null
        wrapperOfMatchApprovalFlow.getQueryCheckAvailContextResponseType() != null
        wrapperOfMatchApprovalFlow.getOrderCreateRequestType() != null
        wrapperOfMatchApprovalFlow.getResourceToken() != null
        wrapperOfMatchApprovalFlow.getBaseCheckAvailInfo() != null
        wrapperOfMatchApprovalFlow.getAccountInfo() != null
        wrapperOfMatchApprovalFlow.getSsoInfoQueryResponseType() != null
        wrapperOfMatchApprovalFlow.getOrderCreateToken() != null
        wrapperOfMatchApprovalFlow.getQconfigOfCertificateInitConfig() != null
        wrapperOfMatchApprovalFlow.getStrategyInfoMap() != null
        wrapperOfMatchApprovalFlow.getGetCityBaseInfoResponseType() != null
        wrapperOfMatchApprovalFlow.getQConfigOfAccountInfoConfig() != null
    }

    def "buildCityBaseInfoEntity"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                cityInput: new CityInput(cityId: 2)
        )
        def mapper = new MapperOfMatchApprovalFlowRequestType()
        def getCityBaseInfoResponseType = new GetCityBaseInfoResponseType(cityBaseInfo: [new CityBaseInfoEntity(cityId: 2, cityName: "Test City")])
        when:
        def result = mapper.buildCityBaseInfoEntity(getCityBaseInfoResponseType, orderCreateRequestType)

        then:
        result != null
        result.cityId == 2
        result.cityName == "Test City"


        when:
        orderCreateRequestType = new OrderCreateRequestType(
                cityInput: new CityInput(cityId: 3)
        )
        result = mapper.buildCityBaseInfoEntity(getCityBaseInfoResponseType, orderCreateRequestType)

        then:
        result == null


        when:
        getCityBaseInfoResponseType = new GetCityBaseInfoResponseType(cityBaseInfo: [])
        result = mapper.buildCityBaseInfoEntity(getCityBaseInfoResponseType, orderCreateRequestType)

        then:
        result == null
    }


    def "buildProductType"() {
        given:
        new MockUp<StrategyOfBookingInitUtil>() {
            @Mock
            public static boolean cityWithinPointOfSale(Map<String, StrategyInfo> strategyInfoMap) {
                return true
            }
        }
        QueryCheckAvailContextResponseType checkAvailContextResponseType = new QueryCheckAvailContextResponseType()
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        Map<String, StrategyInfo> strategyInfoMap = new HashMap<>()
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            cityWithinPointOfSale(_, _) >> true >> false
        }
        GetCityBaseInfoResponseType getCityBaseInfoResponseType = new GetCityBaseInfoResponseType(cityBaseInfo: [new CityBaseInfoEntity(cityId: 2, cityName: "Test City")])
        QConfigOfAccountInfoConfig qConfigOfAccountInfoConfig = new QConfigOfAccountInfoConfig()
        when:
        String result = myTestClass.buildProductType(
                checkAvailContextResponseType,
                orderCreateRequestType,
                strategyInfoMap,
                accountInfo,
                getCityBaseInfoResponseType,
                qConfigOfAccountInfoConfig
        )
        then:
        result == "H"

        when:
        result = myTestClass.buildProductType(
                checkAvailContextResponseType,
                orderCreateRequestType,
                strategyInfoMap,
                accountInfo,
                getCityBaseInfoResponseType,
                qConfigOfAccountInfoConfig
        )
        then:
        result == "O"
    }
    def "testBuildTravelPurposeItemsNew_InvalidInputs"() {
        given: "A list of invalid or null CostCenterInput objects"
        MapperOfMatchApprovalFlowRequestType mapper = new MapperOfMatchApprovalFlowRequestType()
        List<CostCenterInput> costCenterInputs = [
                new CostCenterInput(itemKey: "INVALID|123", value: "Invalid Type"),
                new CostCenterInput(itemKey: "TRIP_PURPOSE|", value: null),
                null
        ]

        when: "buildTravelPurposeItemsNew is called"
        def result = mapper.buildTravelPurposeItemsNew(costCenterInputs)

        then: "The result is an empty list"
        result == []
    }


    def "testBuildProjectNoItemsNew_InvalidInputs"() {
        given: "A list of invalid or null CostCenterInput objects"
        MapperOfMatchApprovalFlowRequestType mapper = new MapperOfMatchApprovalFlowRequestType()
        List<CostCenterInput> costCenterInputs = [
                new CostCenterInput(itemKey: "INVALID|123", value: "Invalid"),
                new CostCenterInput(itemKey: "TRIP_PURPOSE|1", value: "Not a project"),
                null
        ]

        when: "buildProjectNoItemsNew is called"
        def result = mapper.buildProjectNoItemsNew(costCenterInputs)

        then: "The result is an empty list"
        result == []
    }

    def "testBuildProjectNoItemsNew_EmptyInputs"() {
        given: "An empty list of CostCenterInput objects"
        MapperOfMatchApprovalFlowRequestType mapper = new MapperOfMatchApprovalFlowRequestType()
        List<CostCenterInput> costCenterInputs = []

        when: "buildProjectNoItemsNew is called"
        def result = mapper.buildProjectNoItemsNew(costCenterInputs)

        then: "The result is an empty list"
        result == []
    }

    def "testBuildCostCenterItemsNew_InvalidInputs"() {
        given: "A list of invalid or null CostCenterInput objects"
        MapperOfMatchApprovalFlowRequestType mapper = new MapperOfMatchApprovalFlowRequestType()
        List<CostCenterInput> costCenterInputs = [
                new CostCenterInput(itemKey: "INVALID|123", value: "Invalid"),
                new CostCenterInput(itemKey: "TRIP_PURPOSE|1", value: "Not a cost center"),
                null
        ]

        when: "buildCostCenterItemsNew is called"
        def result = mapper.buildCostCenterItemsNew(costCenterInputs)

        then: "The result is an empty list"
        result == []
    }

    def "testBuildCostCenterItemsNew_EmptyInputs"() {
        given: "An empty list of CostCenterInput objects"
        MapperOfMatchApprovalFlowRequestType mapper = new MapperOfMatchApprovalFlowRequestType()
        List<CostCenterInput> costCenterInputs = []

        when: "buildCostCenterItemsNew is called"
        def result = mapper.buildCostCenterItemsNew(costCenterInputs)

        then: "The result is an empty list"
        result == []
    }


    def "testBuildTravelPurposeItemsNew_TypeNotTripPurpose"() {
        given: "itemKey 不是 TRIP_PURPOSE"
        MapperOfMatchApprovalFlowRequestType mapper = new MapperOfMatchApprovalFlowRequestType()
        List<CostCenterInput> costCenterInputs = [
                new CostCenterInput(itemKey: "PROJECT_NO|123", value: "项目编号")
        ]

        when:
        def result = mapper.buildTravelPurposeItemsNew(costCenterInputs)

        then:
        result == []
    }


    def "testBuildTravelPurposeItemsNew_TripPurposeWithEmptyValue"() {
        given: "itemKey 是 TRIP_PURPOSE, value 为空字符串"
        MapperOfMatchApprovalFlowRequestType mapper = new MapperOfMatchApprovalFlowRequestType()
        List<CostCenterInput> costCenterInputs = [
                new CostCenterInput(itemKey: "TRIP_PURPOSE|123", value: "")
        ]

        when:
        def result = mapper.buildTravelPurposeItemsNew(costCenterInputs)

        then:
        result == []
    }

    def "testBuildProjectNoItemsNew_CostCenterKeyDtoIsNull"() {
        given:
        MapperOfMatchApprovalFlowRequestType mapper = Spy(MapperOfMatchApprovalFlowRequestType)
        def input = new CostCenterInput(itemKey: "INVALID|123", value: "Invalid")
        GroovyMock(CostCenterKeyUtils, global: true)
        CostCenterKeyUtils.analysisCostCenterKey(_) >> null

        when:
        def result = mapper.buildProjectNoItemsNew([input])

        then:
        result == []
    }

    def "testBuildProjectNoItemsNew_CostCenterTypeIsBlank"() {
        given:
        MapperOfMatchApprovalFlowRequestType mapper = Spy(MapperOfMatchApprovalFlowRequestType)
        def input = new CostCenterInput(itemKey: "BLANK|123", value: "Invalid")
        def keyDto = Mock(CostCenterKeyDto) {
            getCostCenterType() >> ""
        }
        GroovyMock(CostCenterKeyUtils, global: true)
        CostCenterKeyUtils.analysisCostCenterKey(_) >> keyDto

        when:
        def result = mapper.buildProjectNoItemsNew([input])

        then:
        result == []
    }

    def "testBuildProjectNoItemsNew_CostCenterTypeNotProjectNo"() {
        given:
        MapperOfMatchApprovalFlowRequestType mapper = Spy(MapperOfMatchApprovalFlowRequestType)
        def input = new CostCenterInput(itemKey: "OTHER|123", value: "Invalid")
        def keyDto = Mock(CostCenterKeyDto) {
            getCostCenterType() >> "OTHER"
        }
        GroovyMock(CostCenterKeyUtils, global: true)
        CostCenterKeyUtils.analysisCostCenterKey(_) >> keyDto

        when:
        def result = mapper.buildProjectNoItemsNew([input])

        then:
        result == []
    }

    def "buildCostCenterItemNew cost1 非空白时返回 CostCenter"() {
        given:
        def mapper = new MapperOfMatchApprovalFlowRequestType()
        def method = MapperOfMatchApprovalFlowRequestType.getDeclaredMethod("buildCostCenterItemNew", String, Integer)
        method.setAccessible(true)

        and: "mock StringUtil.isNotBlank"
        GroovyMock(StringUtil, global: true)
        StringUtil.isNotBlank("abc") >> true

        when:
        def result = method.invoke(mapper, "abc", 2)

        then:
        result != null
        result.costCenterId == "abc"
        result.costCenterName == "abc"
        result.costLevel == 2
    }

    def "buildCostCenterItemNew cost1 为空或空白时返回 null"() {
        given:
        def mapper = new MapperOfMatchApprovalFlowRequestType()
        def method = MapperOfMatchApprovalFlowRequestType.getDeclaredMethod("buildCostCenterItemNew", String, Integer)
        method.setAccessible(true)

        and: "mock StringUtil.isNotBlank"
        GroovyMock(StringUtil, global: true)
        StringUtil.isNotBlank("") >> false
        StringUtil.isNotBlank(null) >> false

        expect:
        method.invoke(mapper, "", 1) == null
        method.invoke(mapper, null, 1) == null
    }

    def "testGetOverStandardStage with blockMatchOveStandardStageApprovalFlow false and OverType as variable"() {
        given: "Mock dependencies and inputs"
        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            public static CheckOverStandardRcInfoType getCheckOverStandardRcInfoType(CheckTravelPolicyResponseType checkTravelPolicyResponseType, List<HotelPayTypeInput> hotelPayTypeInputs, ResourceToken resourceToken) {
                return new CheckOverStandardRcInfoType(floatPriceOverDetail: new FloatPriceOverDetailInfoType(overType: "OVER_BASE_STANDARD_UPPER_LIMIT"))
            }
        }
        /*GroovyMock(OrderCreateProcessorOfUtil, global: true)
        OrderCreateProcessorOfUtil.getCheckOverStandardRcInfoType(_ as CheckTravelPolicyResponseType, _ as List<HotelPayTypeInput>, _ as ResourceToken) >> new CheckOverStandardRcInfoType(floatPriceOverDetail: new FloatPriceOverDetailInfoType(
                overType: overType
        ))*/
        def mapper = new MapperOfMatchApprovalFlowRequestType()
        def checkTravelPolicyResponseType = new CheckTravelPolicyResponseType()
        def orderCreateRequestType = new OrderCreateRequestType()
        def resourceToken = new ResourceToken()
        def strategyInfoMap = new HashMap<String, StrategyInfo>()
        and: "Mock behavior for blockMatchOveStandardStageApprovalFlow and OverType"
        GroovyMock(StrategyOfBookingInitUtil, global: true)
        StrategyOfBookingInitUtil.blockMatchOveStandardStageApprovalFlow(strategyInfoMap) >> false

        when: "Calling getOverStandardStage"
        def result = mapper.getOverStandardStage(checkTravelPolicyResponseType, orderCreateRequestType, resourceToken, strategyInfoMap)

        then: "The result should match the OverType"
        result == "S"
    }

    def "testGetOverStandardStage with blockMatchOveStandardStageApprovalFlow true"() {
        given: "Mock dependencies and inputs"
        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            public static CheckOverStandardRcInfoType getCheckOverStandardRcInfoType(CheckTravelPolicyResponseType checkTravelPolicyResponseType, List<HotelPayTypeInput> hotelPayTypeInputs, ResourceToken resourceToken) {
                return new CheckOverStandardRcInfoType(floatPriceOverDetail: new FloatPriceOverDetailInfoType(overType: "OVER_BASE_STANDARD_UPPER_LIMIT"))
            }
        }
        new MockUp<StrategyOfBookingInitUtil>() {
            @Mock
            public static boolean blockMatchOveStandardStageApprovalFlow(Map<String, StrategyInfo> strategyInfoMap) {
                return true
            }
        }
        def mapper = new MapperOfMatchApprovalFlowRequestType()
        def checkTravelPolicyResponseType = new CheckTravelPolicyResponseType()
        def orderCreateRequestType = new OrderCreateRequestType()
        def resourceToken = new ResourceToken()
        def strategyInfoMap = new HashMap<String, StrategyInfo>()
        when: "Calling getOverStandardStage"
        StrategyOfBookingInitUtil.blockMatchOveStandardStageApprovalFlow(strategyInfoMap) >> true
        def result = mapper.getOverStandardStage(checkTravelPolicyResponseType, orderCreateRequestType, resourceToken, strategyInfoMap)

        then: "The result should match the OverType"
        result == null
    }
}
