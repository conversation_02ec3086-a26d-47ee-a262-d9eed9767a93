package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import spock.lang.Specification

class MapperOfBatchSearchClientsInfoRequestTypeTest extends Specification {

    def tester = Spy(MapperOfBatchSearchClientsInfoRequestType)

    def savePoint = new mockit.internal.state.SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        expect:
        tester.convert(Tuple1.of(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")))).uID == "a"
    }
}
