package com.ctrip.corp.bff.hotel.book.common.util

import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil
import com.ctrip.corp.bff.framework.template.common.utils.CalendarUtil
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2024/7/11 9:43
 *
 */
class AgeUtilTest extends Specification {
    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }

    @Unroll
    def "Test ageInRange"() {
        given:
        expect:
        AgeUtil.ageInRange(age) == expected
        where:
        age  || expected
        null || false
        17   || false
        18   || true
        85   || true
        86   || false
    }

    @Unroll
    def "Test getAgeByBirth"() {
        expect:
        new MockUp<AgeUtil>() {
            @Mock
            private static int getAge(String birthYear, String birthMonth, String birthDay) {
                return 35;
            }
        }
        AgeUtil.getAgeByBirth(birth) == expected

        where:
        birth        || expected
        "1990-01-01" || 35
    }

    @Unroll
    def "Test getAge"() {
        expect:
        new MockUp<Calendar>() {
            @Mock
            public static Calendar getInstance(Locale aLocale) {
                return CalendarUtil.getCalendar(2025, 1, 1)
            }
        }
        AgeUtil.getAge(birthYear, birthMonth, birthDay) == expected

        where:
        birthYear | birthMonth | birthDay || expected
        "1990"    | "01"       | "01"     || 35
        "2000"    | "12"       | "31"     || 24
        "1985"    | "06"       | "15"     || 39
        ""        | "01"       | "01"     || 0
        "1990"    | ""         | "01"     || 0
        "1990"    | "01"       | ""       || 0
    }

    @Unroll
    def "Test getAgeByCardNo"() {
        expect:
        new MockUp<LogUtil>() {
            @Mock
            public static void loggingClogOnly(LogLevelEnum level,
                                               Class<?> type,
                                               String title,
                                               String message,
                                               Map<String, String> indexTags) {
                return;
            }
        }
        AgeUtil.getAgeByCardNo(idCardNumber) == expected

        where:
        idCardNumber         || expected
        "11010119900101001X" || 35
        "110101910101001"    || 34
        ""                   || 0
        "123456"             || 0
    }
}

