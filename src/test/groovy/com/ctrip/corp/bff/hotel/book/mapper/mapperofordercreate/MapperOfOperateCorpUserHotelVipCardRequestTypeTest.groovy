package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.MembershipInfo
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import corp.user.service.group4j.accounts.BizModeBindRelationData
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType
import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;

class MapperOfOperateCorpUserHotelVipCardRequestTypeTest extends Specification {


    def tester = Spy(MapperOfOperateCorpUserHotelVipCardRequestType)

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        given:
        def check = Mock(WrapperOfCheckAvail.BaseCheckAvailInfo)
        expect:
        tester.convert(null) == null
        tester.convert(Tuple3.of(null,null,null)) == null
        tester.convert(Tuple3.of(new OrderCreateRequestType(),null,null)) == null
        tester.convert(Tuple3.of(new OrderCreateRequestType(membershipInfo: new MembershipInfo()),null,null)) == null
        tester.convert(Tuple3.of(new OrderCreateRequestType(membershipInfo: new MembershipInfo(membershipNo: "a")),null,null)) == null
        tester.convert(Tuple3.of(new OrderCreateRequestType(membershipInfo: new MembershipInfo(membershipNo: "a", membershipUid: "b")),null,null)) == null
        tester.convert(Tuple3.of(new OrderCreateRequestType(membershipInfo: new MembershipInfo(membershipNo: "a", membershipUid: "b")),null,check)) == null
        tester.convert(Tuple3.of(new OrderCreateRequestType(membershipInfo: new MembershipInfo(membershipNo: "a", membershipUid: "b"), integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "c"))),null,check)) == null
        tester.convert(Tuple3.of(new OrderCreateRequestType(membershipInfo: new MembershipInfo(membershipNo: "a", membershipUid: "b"), integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "c")), hotelBookPassengerInputs: []),null,check)) == null
        tester.convert(Tuple3.of(new OrderCreateRequestType(membershipInfo: new MembershipInfo(membershipNo: "a", membershipUid: "b"), integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "c")), hotelBookPassengerInputs: [null]),null,check)) == null
        tester.convert(Tuple3.of(new OrderCreateRequestType(membershipInfo: new MembershipInfo(membershipNo: "a", membershipUid: "b"), integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "c")), hotelBookPassengerInputs: [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput())]),null,check)) == null
        tester.convert(Tuple3.of(new OrderCreateRequestType(membershipInfo: new MembershipInfo(membershipNo: "a", membershipUid: "b"), integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "c")), hotelBookPassengerInputs: [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(employee: "F", uid: "b"))]),null,check)) == null
        tester.convert(Tuple3.of(new OrderCreateRequestType(membershipInfo: new MembershipInfo(membershipNo: "a", membershipUid: "b"), integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "c")), hotelBookPassengerInputs: [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(employee: "T", uid: "b"))]),null,check)) == null
        tester.convert(Tuple3.of(new OrderCreateRequestType(membershipInfo: new MembershipInfo(membershipNo: "a", membershipUid: "b"), integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "c")), hotelBookPassengerInputs: [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(employee: "T", uid: "b"))]),new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: []),check)) == null
        tester.convert(Tuple3.of(new OrderCreateRequestType(membershipInfo: new MembershipInfo(membershipNo: "a", membershipUid: "b"), integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "c")), hotelBookPassengerInputs: [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(employee: "T", uid: "b"))]),new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(primaryDimensionId: "b", dimensionId: "b")]),check)).operator == "c"

    }
}
