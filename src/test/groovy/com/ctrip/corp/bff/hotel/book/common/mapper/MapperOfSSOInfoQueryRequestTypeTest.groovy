package com.ctrip.corp.bff.hotel.book.common.mapper

import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;

class MapperOfSSOInfoQueryRequestTypeTest extends Specification {


    def tester = Spy(new MapperOfSSOInfoQueryRequestType())

    def savePoint = new mockit.internal.state.SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        expect:
        tester.convert(Tuple2.of(null, null)).ssoInput == null
    }
}
