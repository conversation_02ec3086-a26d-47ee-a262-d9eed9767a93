package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.bff.framework.hotel.common.util.CityInfoUtil
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelGeoInfoResourceToken
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2025/4/9 2:09
 *
 */
class MapperOfBuildTravelPolicyRequestTypeTest extends Specification {


    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }

    @Unroll
    def "testBuildApprovalInput not null with #description"() {
        given: "A MapperOfBuildTravelPolicyRequestType instance and input parameters"
        def mapper = new MapperOfBuildTravelPolicyRequestType()
        def approvalInput = new ApprovalInput(subApprovalNo: "subApprovalNo")
        def bookingInitRequestType = Mock(BookingInitRequestType)
        bookingInitRequestType.strategyInfos >> [new StrategyInfo(strategyValue: hotelCheckAvail, strategyKey: "HOTEL_CHECK_AVAIL")]
        def hotelGeoInfoResourceToken = Mock(HotelGeoInfoResourceToken)
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
        }
        accountInfo.isOaApprovalHead() >> true
        accountInfo.isPreApprovalRequired(_, _) >> true

        when: "Calling buildApprovalInput"
        def result = mapper.buildApprovalInput(approvalInput, accountInfo, bookingInitRequestType, hotelGeoInfoResourceToken)

        then:
        result.subApprovalNo == expectedSubApprovalNo

        where:
        description                          | hotelCheckAvail | isPreApprovalRequired | isOaApprovalHead | expectedSubApprovalNo
        "hotelCheckAvail is false"           | "F"             | false                 | false            | "subApprovalNo"
        "Pre-approval required with OA head" | "T"             | true                  | true             | "subApprovalNo"
    }

    @Unroll
    def "testBuildApprovalInput null with #description"() {
        given: "A MapperOfBuildTravelPolicyRequestType instance and input parameters"
        def mapper = new MapperOfBuildTravelPolicyRequestType()
        def approvalInput = new ApprovalInput(subApprovalNo: "subApprovalNo")
        def accountInfo = Mock(WrapperOfAccount.AccountInfo)
        def bookingInitRequestType = Mock(BookingInitRequestType)
        bookingInitRequestType.strategyInfos >> [new StrategyInfo(strategyValue: hotelCheckAvail, strategyKey: "HOTEL_CHECK_AVAIL")]
        def hotelGeoInfoResourceToken = Mock(HotelGeoInfoResourceToken)
        accountInfo.isPreApprovalRequired(_ as Boolean, _ as CorpPayInfo) >> isPreApprovalRequired
        accountInfo.isOaApprovalHead() >> isOaApprovalHead

        expect: "Calling buildApprovalInput"
        result == mapper.buildApprovalInput(approvalInput, accountInfo, bookingInitRequestType, hotelGeoInfoResourceToken)

        where:
        description                          | hotelCheckAvail | isPreApprovalRequired | isOaApprovalHead | result
        "No travel or pre-approval required" | "T"             | false                 | false            | null
        "Default case, no approval input"    | "T"             | true                  | false            | null
    }

    @Unroll
    def "testBuildLocationId with #description"() {
        given: "A MapperOfBuildTravelPolicyRequestType instance and mocked HotelGeoInfoResourceToken"
        def mapper = new MapperOfBuildTravelPolicyRequestType()
        def hotelGeoInfoResourceToken = Mock(HotelGeoInfoResourceToken) {
            getLocationId() >> locationId
        }

        when: "Calling buildLocationId"
        def result = mapper.buildLocationId(hotelGeoInfoResourceToken)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                          | locationId | expectedResult
        "HotelGeoInfoResourceToken is null"  | null       | null
        "Valid locationId"                   | 123        | 123
        "Invalid locationId (zero)"          | 0          | null
    }

    @Unroll
    def "testBuildLocationId null"() {
        given:
        def mapper = new MapperOfBuildTravelPolicyRequestType()
        when:
        def result = mapper.buildLocationId(null)

        then:
        result == null
    }
}
