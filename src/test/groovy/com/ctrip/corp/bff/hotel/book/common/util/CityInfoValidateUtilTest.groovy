package com.ctrip.corp.bff.hotel.book.common.util

import spock.lang.Specification

/**
 * <AUTHOR>
 * @description
 * @date 2025/6/11
 */
class CityInfoValidateUtilTest extends Specification{
    def "validCity returns true for valid cityId and cityName"() {
        expect:
        CityInfoValidateUtil.validCity("123", "ValidCity") == true
    }

    def "validCity returns false for null cityId"() {
        expect:
        CityInfoValidateUtil.validCity(null, "ValidCity") == false
    }

    def "validCity returns false for non-numeric cityId"() {
        expect:
        CityInfoValidateUtil.validCity("abc", "ValidCity") == false
    }

    def "validCity returns false for negative cityId"() {
        expect:
        CityInfoValidateUtil.validCity("-123", "ValidCity") == false
    }

    def "validCity returns false for zero cityId"() {
        expect:
        CityInfoValidateUtil.validCity("0", "ValidCity") == false
    }

    def "validCity returns false for invalid cityName"() {
        expect:
        CityInfoValidateUtil.validCity("123", "InvalidCity") == false
    }

    def "validCity returns false for bad case"() {
        expect:
        CityInfoValidateUtil.validCity("1139769675035361442", "ashudasui") == false
    }
}
