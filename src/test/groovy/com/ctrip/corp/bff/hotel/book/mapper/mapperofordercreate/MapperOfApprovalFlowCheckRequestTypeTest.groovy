package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.approve.ws.contract.MatchApprovalFlowRequestType
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;

class MapperOfApprovalFlowCheckRequestTypeTest extends Specification {

    def tester = Spy(new MapperOfApprovalFlowCheckRequestType())

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        given:
        expect:
        tester.convert(Tuple2.of(new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(language: "zh-CN")), new MatchApprovalFlowRequestType())).integrationSoaRequestType.language == "zh-CN"
    }
}
