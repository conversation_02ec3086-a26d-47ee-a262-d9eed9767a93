package com.ctrip.corp.bff.hotel.book.common.util


import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.contract.RegisterInputInfo
import corp.user.service.corpUserInfoService.CorpUserHotelVipCard
import corp.user.service.corpUserInfoService.GetCorpUserHotelVipCardResponseType
import corp.user.service.group4j.accounts.BizModeBindRelationData
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @description
 * @date 2024/8/19
 */
class OrderCreateOfUtilTest extends Specification {
    def myTestClass = new OrderCreateProcessorOfUtil()

    def savePoint = new SavePoint()

    void setup() {
        new MockUp<WrapperOfAccount.AccountInfo>() {
            @Mock
            public boolean bookPolicyPsgMustSameTripApprove(Boolean isOverSea, CorpPayInfo corpPayInfo) {
                if (isOverSea) {
                    return false;
                }
                return true;
            }

            @Mock
            public boolean isCCardCanNotBookingForNoEmp() {
                return false;
            }
        }
    }

    void cleanup() {
        savePoint.rollback()
    }

    @Unroll
    def "test requireVerifyFellowControl"() {
        given:
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo);
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                "cityInput": new CityInput(
                        "cityId": cityId,
                ),
                "integrationSoaRequestType": new IntegrationSoaRequestType(
                        "userInfo": new UserInfo(
                                "corpId": corpId
                        )
                ),
                "corpPayInfo": new CorpPayInfo(
                        "corpPayType": corpPayType
                )
        )
        accountInfo.preApprovalSameTrip(_ as Boolean, _ as CorpPayInfo) >> preApprovalSameTrip

        expect:
        result == myTestClass.requireVerifyFellowControl(orderCreateRequestType, accountInfo, null)
        where:
        cityId | corpId | preApprovalSameTrip | corpPayType || result
        1      | "true" | true                | "public"    || true
        33     | "true" | true                | "public"    || true
        1      | "123"  | false               | "public"    || false
        33     | "123"  | false               | "public"    || false
        1      | "true" | true                | "public"    || true
        33     | "true" | true                | "private"   || false
    }

    def "requireRegister" () {
        given:
        def onlyGroupMemberCanBook = Mock(WrapperOfCheckAvail.CheckAvailContextInfo)
        onlyGroupMemberCanBook.isOnlyGroupMemberCanBook() >> true
        onlyGroupMemberCanBook.getGroupId() >> 2
        def NotonlyGroupMemberCanBook  = Mock(WrapperOfCheckAvail.CheckAvailContextInfo)
        NotonlyGroupMemberCanBook.isOnlyGroupMemberCanBook() >> false

        expect:
        OrderCreateProcessorOfUtil.requireRegister(null,null,null,null,null,null)
        !OrderCreateProcessorOfUtil.requireRegister(null,null,null,null,NotonlyGroupMemberCanBook,null)
        !OrderCreateProcessorOfUtil.requireRegister(null,null,null,null,onlyGroupMemberCanBook,null)
        !OrderCreateProcessorOfUtil.requireRegister(new OrderCreateToken(membershipRegisterId: "s"),new RegisterInputInfo(),null,null,onlyGroupMemberCanBook,null)
        OrderCreateProcessorOfUtil.requireRegister(new OrderCreateToken(),new RegisterInputInfo(),null,null,onlyGroupMemberCanBook,null)
        OrderCreateProcessorOfUtil.requireRegister(new OrderCreateToken(),new RegisterInputInfo(),null,null,onlyGroupMemberCanBook,new QueryBizModeBindRelationResponseType())
        OrderCreateProcessorOfUtil.requireRegister(new OrderCreateToken(),new RegisterInputInfo(),null,null,onlyGroupMemberCanBook,new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData()]))
        OrderCreateProcessorOfUtil.requireRegister(new OrderCreateToken(),new RegisterInputInfo(),null,new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "1")),onlyGroupMemberCanBook,new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(primaryDimensionId: "2", dimensionId: "3")]))
        OrderCreateProcessorOfUtil.requireRegister(new OrderCreateToken(),new RegisterInputInfo(),null,new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "1")),onlyGroupMemberCanBook,new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(primaryDimensionId: "2", dimensionId: "1")]))
        OrderCreateProcessorOfUtil.requireRegister(new OrderCreateToken(),new RegisterInputInfo(),new GetCorpUserHotelVipCardResponseType(corpUserHotelVipCardList: []),new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "1")),onlyGroupMemberCanBook,new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(primaryDimensionId: "2", dimensionId: "1")]))
        OrderCreateProcessorOfUtil.requireRegister(new OrderCreateToken(),new RegisterInputInfo(),new GetCorpUserHotelVipCardResponseType(corpUserHotelVipCardList: [new CorpUserHotelVipCard()]),new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "1")),onlyGroupMemberCanBook,new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(primaryDimensionId: "2", dimensionId: "1")]))
        OrderCreateProcessorOfUtil.requireRegister(new OrderCreateToken(),new RegisterInputInfo(),new GetCorpUserHotelVipCardResponseType(corpUserHotelVipCardList: [new CorpUserHotelVipCard(uid: "2", hotelGroupID: 1, htlVipCardID: "2")]),new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "1")),onlyGroupMemberCanBook,new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(primaryDimensionId: "2", dimensionId: "1")]))
        !OrderCreateProcessorOfUtil.requireRegister(new OrderCreateToken(),new RegisterInputInfo(),new GetCorpUserHotelVipCardResponseType(corpUserHotelVipCardList: [new CorpUserHotelVipCard(uid: "2", hotelGroupID: 2, htlVipCardID: "2")]),new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "1")),onlyGroupMemberCanBook,new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(primaryDimensionId: "2", dimensionId: "1")]))
    }
}
