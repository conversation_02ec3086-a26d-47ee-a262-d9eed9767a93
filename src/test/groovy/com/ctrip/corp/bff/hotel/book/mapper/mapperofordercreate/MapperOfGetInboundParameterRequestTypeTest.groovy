package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.basebiz.members.core.contract.GetInboundParameterRequestType
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.hotel.book.contract.OfflineInfo
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2024/12/23 9:26
 *
 */
class MapperOfGetInboundParameterRequestTypeTest extends Specification {

    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }
    MapperOfGetInboundParameterRequestType mapper = new MapperOfGetInboundParameterRequestType()

    @Unroll
    def "testConvert with different scenarios"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        orderCreateRequestType.setOfflineInfo(new OfflineInfo(cookieId: cookieId))
        Tuple1<OrderCreateRequestType> tuple1 = Tuple1.of(orderCreateRequestType)

        when:
        GetInboundParameterRequestType result = mapper.convert(tuple1)

        then:
        result.getInboundKey() == expectedInboundKey
        result.getParameterList().size() == 1
        result.getParameterList().get(0).getKey() == "BizType"
        result.getParameterList().get(0).getValue() == "CRP"

        where:
        cookieId       || expectedInboundKey
        "cookie123"    || "cookie123"
        "cookie456"    || "cookie456"
        null           || null
    }
}
