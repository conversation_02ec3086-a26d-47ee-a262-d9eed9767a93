package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response

import com.ctrip.corp.bff.framework.hotel.common.util.WaitFutureUtil
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfCityBaseInfo
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import com.ctrip.corp.bff.hotel.book.contract.FollowApprovalInfoInput
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType
import com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response.MapperOfFollowApprovalResponse
import com.ctrip.corp.bff.hotel.book.sharkmock.SharkMockUtil
import com.ctrip.corp.hotel.book.query.entity.CityBaseInfoEntity
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType
import com.ctrip.soa._20184.CheckHotelAuthExtensionResponseType
import com.ctrip.soa._20184.OriginalOrderInfoType
import com.ctrip.soa._20184.QueryHotelAuthExtensionResponseType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2024/8/20 9:11
 *
 */
class MapperOfFollowApprovalResponseTest extends Specification {
    def myTestClass = new MapperOfFollowApprovalResponse()

    def savePoint = new SavePoint()

    void setup() {
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "test buildOrderCreateResponseTypeQuery method"() {
        given:
        MapperOfFollowApprovalResponse mapper = new MapperOfFollowApprovalResponse()

        QueryHotelAuthExtensionResponseType queryHotelAuthExtensionResponseType = new QueryHotelAuthExtensionResponseType(
                continueAuth: true,
                originalOrderInfo: new OriginalOrderInfoType(originalOrderId: 123456))
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(followApprovalInfoInput: new FollowApprovalInfoInput(followOrderId : "123456"))
        GetCityBaseInfoResponseType getCityBaseInfoResponseType = new GetCityBaseInfoResponseType()
        myTestClass.buildCityInfo(_,_) >> new CityInfo()
        WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo = WrapperOfCityBaseInfo.builder()
                .getCityBaseInfoResponseType(getCityBaseInfoResponseType)
                .cityInput(orderCreateRequestType.getCityInput()).build()
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo)
        when:
        Tuple2<Boolean, OrderCreateResponseType> result = myTestClass.buildOrderCreateResponseTypeByQuery(
                queryHotelAuthExtensionResponseType, cityBaseInfo , new OrderCreateToken(), new OrderCreateRequestType(), accountInfo)

        then:
        result != null
        result.getT1()
        result.getT2() != null
        result.getT2().followApprovalInfo != null
        result.getT2().orderCreateToken != null
        result.getT2().cityInfo == null
    }

    def "buildCityInfo should return CityInfo with localeCityName"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        orderCreateRequestType.cityInput = new CityInput()
        orderCreateRequestType.cityInput.cityId = 1

        GetCityBaseInfoResponseType getCityBaseInfoResponseType = new GetCityBaseInfoResponseType()
        CityBaseInfoEntity cityBaseInfoEntity = new CityBaseInfoEntity()
        cityBaseInfoEntity.cityId = 1
        cityBaseInfoEntity.cityName = "Test City"
        getCityBaseInfoResponseType.cityBaseInfo = new ArrayList<>()
        getCityBaseInfoResponseType.cityBaseInfo.add(cityBaseInfoEntity)

        when:
        CityInfo cityInfo = myTestClass.buildCityInfo(orderCreateRequestType, getCityBaseInfoResponseType)

        then:
        cityInfo != null
        cityInfo.localeCityName == "Test City"
    }

    def "buildOrderCreateResponseTypeCheck should return Tuple2 with true and OrderCreateResponseType"() {
        given:
        CheckHotelAuthExtensionResponseType checkHotelAuthExtensionResponseType = new CheckHotelAuthExtensionResponseType()
        checkHotelAuthExtensionResponseType.continueAuth = true
        checkHotelAuthExtensionResponseType.tripId = 1245L

        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        orderCreateRequestType.cityInput = new CityInput()
        orderCreateRequestType.cityInput.cityId = 1
        GetCityBaseInfoResponseType getCityBaseInfoResponseType = new GetCityBaseInfoResponseType()
        getCityBaseInfoResponseType.cityBaseInfo = Arrays.asList(new CityBaseInfoEntity(cityId: 1, cityName: "Test City"))

        WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo = WrapperOfCityBaseInfo.builder()
                .getCityBaseInfoResponseType(getCityBaseInfoResponseType)
                .cityInput(orderCreateRequestType.getCityInput()).build()
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo)
        when:
        Tuple2<Boolean, OrderCreateResponseType> result = myTestClass.buildOrderCreateResponseTypeByCheck(
                checkHotelAuthExtensionResponseType, orderCreateRequestType, cityBaseInfo, new OrderCreateToken(), accountInfo)

        then:
        result != null
        result.getT1()
        result.getT2() != null
        result.getT2().followApprovalInfo.tripId == "1245"
        result.getT2().cityInfo != null
        result.getT2().cityInfo.localeCityName == "Test City"
    }

    def "buildOrderCreateTokenCanFollow should return OrderCreateToken with correct properties"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        String tripId = "123"
        String followOrderNo = "456"

        when:
        OrderCreateToken result = myTestClass.buildOrderCreateToken(true,
                tripId, followOrderNo, new OrderCreateToken())

        then:
        result != null
        result.followApprovalResult != null
        result.followApprovalResult.canFollowApproval == "T"
        result.followApprovalResult.followOrderNo == followOrderNo
        result.followApprovalResult.tripId == tripId
    }

    def "buildOrderCreateTokenFailFollow should return OrderCreateToken with correct properties"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        orderCreateRequestType.setOrderCreateToken("someToken")

        when:
        OrderCreateToken result = myTestClass.buildOrderCreateToken(false, null, null, new OrderCreateToken())

        then:

        then:
        result != null
        result.followApprovalResult != null
        result.followApprovalResult.canFollowApproval == "F"
    }

    def "buildFollowApprovalInfoFailFollow should return FollowApprovalInfo with correct properties"() {
        given:
        CheckHotelAuthExtensionResponseType checkHotelAuthExtensionResponseType = new CheckHotelAuthExtensionResponseType()
        checkHotelAuthExtensionResponseType.reasonList = ["001", "002"]

        when:
        def result = myTestClass.buildNoFollowApprovalInfo(null, null, checkHotelAuthExtensionResponseType.reasonList)

        then:
        result != null
        result.followApprovalResult == "F"
        result.reasonInfos != null
        result.reasonInfos.size() == 2
        result.reasonInfos[0].reasonType == "001"
        result.reasonInfos[0].reasonDesc == "订单数据为空"
        result.reasonInfos[1].reasonType == "002"
        result.reasonInfos[1].reasonDesc == " 配置数据为空"
    }
}
