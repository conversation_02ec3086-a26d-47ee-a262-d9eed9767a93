package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response

import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.hotelbooking.hotelws.entity.AmountDifferenceType
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType
import com.ctrip.corp.hotelbooking.hotelws.entity.OrderPaymentType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;

class MapperOfPriceChangeInfoResponseTest extends Specification {

    def tester = Spy(new MapperOfPriceChangeInfoResponse())

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        given:
        new MockUp<TokenParseUtil>() {
            @Mock
            public static <T> String generateToken(T pidEntity, Class<T> tClass) {
                return ""
            }
        }
        expect:
        tester.convert(Tuple3.of(new CreateOrderResponseType(orderPaymentInfo: new OrderPaymentType(amountDifferenceInfo: new AmountDifferenceType(amount: 2))), new OrderCreateToken(), new OrderCreateRequestType())).getT2().orderCreateToken == ""
    }
}
