package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.template.entity.contract.integration.TripInput
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import spock.lang.Specification

class MapperOfGetTripBookingInfosRequestTest extends Specification {

    def tester = Spy(MapperOfGetTripBookingInfosRequest)

    def savePoint = new mockit.internal.state.SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        expect:
        tester.convert(Tuple2.of(null, null)) == null
        tester.convert(Tuple2.of(new TripInput(tripId: -1), null)) == null
        tester.convert(Tuple2.of(new TripInput(tripId: 1), new ResourceToken())) .tripIdList == [1l]

    }
}
