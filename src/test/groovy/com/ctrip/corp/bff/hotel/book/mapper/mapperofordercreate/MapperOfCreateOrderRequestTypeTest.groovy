package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.arch.coreinfo.CoreInfoClient
import com.ctrip.arch.coreinfo.entity.InfoKey
import com.ctrip.arch.coreinfo.enums.KeyType
import com.ctrip.basebiz.members.core.contract.CallEntity
import com.ctrip.basebiz.members.core.contract.GetInboundParameterResponseType
import com.ctrip.basebiz.members.core.contract.MembersEntity
import com.ctrip.corp.agg.commonws.entity.GetPackageRoomListResponseType
import com.ctrip.corp.agg.commonws.entity.PackageExtendInfoType
import com.ctrip.corp.agg.commonws.entity.XProductSkuInfoType
import com.ctrip.corp.agg.commonws.entity.XProductSpuInfoType
import com.ctrip.corp.agg.commonws.entity.XProductStaticInfoType
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookHotelInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookRoomInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryBonusPointInfoType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryBookingRulesType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCancelPolicyType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryGroupMemberShipType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryLimitArrivalDateTimeType
import com.ctrip.corp.agg.hotel.roomavailable.entity.XProductEntityType
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig
import com.ctrip.corp.bff.framework.hotel.common.util.CoreInfoUtil
import com.ctrip.corp.bff.framework.hotel.common.util.WaitFutureUtil
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelModifyBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.InitConfigInfo
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.AllocationResultToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HourRoomToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.OrderResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ReservationResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.common.RcToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.common.utils.CalendarUtil
import com.ctrip.corp.bff.framework.template.common.utils.date.DateUtil
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.MapString
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CertificateInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.EmailInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PhoneInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.RCInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.hotel.book.common.builder.BookingInitAssembleRequest
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant
import com.ctrip.corp.bff.hotel.book.common.constant.InitConfigInfoConstant
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.AvailableRight
import com.ctrip.corp.bff.hotel.book.contract.ClientInfo
import com.ctrip.corp.bff.hotel.book.contract.HotelContactorInfo
import com.ctrip.corp.bff.hotel.book.contract.MembershipInfo
import com.ctrip.corp.bff.hotel.book.contract.OfflineInfo
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.contract.RoomInfoInput
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfQueryCtripMrgMemberUserInfoRequest
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.response.MapperOfBookingInitResponse
import com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response.MapperOfRepeatOrderInfoResponse
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfEmailInfoConfig
import com.ctrip.corp.bff.hotel.book.qconfig.entity.CorpEmail
import com.ctrip.corp.bff.hotel.book.qconfig.entity.EmailInfoConfig
import com.ctrip.corp.bff.hotel.book.sharkmock.SharkMockUtil
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType
import com.ctrip.corp.foundation.common.plus.time.HotelTimeZoneUtil
import com.ctrip.corp.hotelbooking.hotelws.entity.CertificateTypeEnum
import com.ctrip.corp.hotelbooking.hotelws.entity.ClientEntity
import com.ctrip.corp.hotelbooking.hotelws.entity.ConfirmTypes
import com.ctrip.corp.hotelbooking.hotelws.entity.ContactorEntity
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOfflineExtEntity
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOriOrderEntity
import com.ctrip.corp.hotelbooking.hotelws.entity.OrderModifyInfoType
import com.ctrip.corp.hotelbooking.hotelws.entity.PointInfo
import com.ctrip.corp.hotelbooking.hotelws.entity.TokensEntity
import com.ctrip.corp.order.data.aggregation.query.contract.OrderGenericInfoType
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataResponseType
import com.ctrip.soa._21234.CreateTripResponseType
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType
import corp.user.service.group4j.accounts.BizModeBindRelationData
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

import java.time.LocalDateTime

/**
 * <AUTHOR>
 * @date 2024/12/4 16:18
 *
 */
class MapperOfCreateOrderRequestTypeTest extends Specification {
    def tester = Spy(MapperOfCreateOrderRequestType)

    def savePoint = new SavePoint()
    void cleanup() {
        savePoint.rollback()
    }

    void setup() {
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return key
            }
        }
    }

    @Unroll
    def "testEncryptData with different scenarios"() {
        given:
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                return true
            }
        }
        new MockUp<CoreInfoUtil>() {
            @Mock
            public static String encrypt(InfoKey infoKey) {
                return "encrypted_${infoKey.getKey()}"
            }
        }
        // CoreInfoUtil.metaClass.static.encrypt = { InfoKey infoKey -> return "encrypted_${infoKey.getValue()}" }

        when: "calling encryptData with specific parameters"
        def result = tester.encryptData(keyType, needEncryptData, corpIdreq)

        then: "the result should be as expected"
        result == expectedResult

        where:
        keyType      | needEncryptData | corpIdreq || expectedResult
        //KeyType.Mail  | "<EMAIL>" | "corp123" || "<EMAIL>"
        //KeyType.Phone | "1234567890"       | "corp123" || "encrypted_1234567890"
        KeyType.Mail | ""              | "corp123" || ""
        KeyType.Mail | null            | "corp123" || null
        //KeyType.Mail  | "<EMAIL>" | ""        || "<EMAIL>"
    }

    @Unroll
    def "testGetContactorInfo with different scenarios"() {
        given:
        MapperOfCreateOrderRequestType mapper = new MapperOfCreateOrderRequestType()
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo()),
                cityInput: new CityInput(cityId: 789))
        orderCreateRequestType.setHotelContactorInfo(hotelContactorInfo)

        when: "calling getContactorInfo with specific parameters"
        ContactorEntity result = tester.getContactorInfo(orderCreateRequestType, null)

        then: "the result should be as expected"
        result?.getName() == expectedName
        result?.getMobilePhoneCountryCode() == expectedCountryCode
        result?.getConfirmType() == expectedConfirmType

        where:
        hotelContactorInfo                                                                                                                                        | expectedName                                                      | expectedCountryCode | expectedConfirmType
        new HotelContactorInfo(name: "John Doe", phoneInfo: new PhoneInfo(countryCode: "86"), emailInfo: new EmailInfo(transferEmail: "<EMAIL>"))    | "John Doe"                                                        | "86"                | ConfirmTypes.Email
        new HotelContactorInfo(name: "Jane Smith", phoneInfo: new PhoneInfo(countryCode: "1"), emailInfo: new EmailInfo(transferEmail: "<EMAIL>")) | "Jane Smith"                                                      | "1"                 | ConfirmTypes.Email
        new HotelContactorInfo(name: "Alice", phoneInfo: new PhoneInfo(countryCode: "44"), emailInfo: new EmailInfo(transferEmail: "<EMAIL>"))          | "Alice"                                                           | "44"                | ConfirmTypes.Email
        new HotelContactorInfo(name: null, phoneInfo: new PhoneInfo(countryCode: "91"), emailInfo: new EmailInfo(transferEmail: "<EMAIL>"))           | "key.corp.hotel.ordercreate.createorder.contactorpsg.defaultname" | "91"                | ConfirmTypes.Email
        null                                                                                                                                                      | null                                                              | null                | null
    }


    @Unroll
    def "testGetQConfigEmail with #description"() {
        given: "Mock dependencies and inputs"
        def qconfigOfEmailInfoConfig = new QconfigOfEmailInfoConfig(
                emailInfoConfig: new EmailInfoConfig(defaultEmail: "defaultEmail",
                        corpEmails: Arrays.asList(new CorpEmail(corpID: "corpId", email: "corpId"), new CorpEmail(corpID: "corpId2", email: null))))
        def integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: corpId))

        when: "Calling getQConfigEmail method"
        def result = tester.buildQConfigEmail(qconfigOfEmailInfoConfig, integrationSoaRequestType)

        then: "The result should match the expected outcome"
        result == expectedResult

        where: "Different scenarios for testing"
        description         | corpId    || expectedResult
        "corpId is ctrip"   | "ctrip"   || null
        "corpId is corpId"  | "corpId"  || "corpId"
        "corpId is corpId2" | "corpId2" || "defaultEmail"
    }


    @Unroll
    def "testGetClientListInfo with different scenarios"() {
        given:
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                return false
            }
        }
        MapperOfCreateOrderRequestType mapper = new MapperOfCreateOrderRequestType()
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                hotelBookPassengerInputs: hotelBookPassengerInputs,
                cityInput: new CityInput(cityId: 22249),
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "corpId")))
        AllocationResultToken costAllocationToken = new AllocationResultToken(cnyAllocationAmount: new HashMap<String, BigDecimal>())
        WrapperOfCheckAvail.BaseCheckAvailInfo queryCheckAvailContextResponseType =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(balanceType: "PP")))
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsId")))
                        .build().getCheckAvailContextInfo();

        when: "calling getClientListInfo with specific parameters"
        List<ClientEntity> result = mapper.getClientListInfo(orderCreateRequestType, costAllocationToken, queryCheckAvailContextResponseType, null, null)

        then: "the result should be as expected"
        result.size() == expectedSize
        result*.getName() == expectedNames
        result*.getMobilePhone() == expectedMobilePhones
        result*.getEmail() == expectedEmails

        where:
        hotelBookPassengerInputs                                                                                                                                                                                                                                                                                                                       | expectedSize | expectedNames              | expectedMobilePhones  | expectedEmails
        [new HotelBookPassengerInput(name: "John Doe", hotelPassengerInput: new HotelPassengerInput(), phoneInfo: new PhoneInfo(countryCode: "86", transferPhoneNo: "13718965555"), emailInfo: new EmailInfo(transferEmail: "<EMAIL>")), new HotelBookPassengerInput(name: "Jane Smith", hotelPassengerInput: new HotelPassengerInput())] | 2            | ["John Doe", "Jane Smith"] | ["13718965555", null] | ["<EMAIL>", null]
        [new HotelBookPassengerInput(name: "Alice", hotelPassengerInput: new HotelPassengerInput()), new HotelBookPassengerInput(name: "Bob", hotelPassengerInput: new HotelPassengerInput())]                                                                                                                                                         | 2            | ["Alice", "Bob"]           | [null, null]          | [null, null]
        [new HotelBookPassengerInput(name: "Charlie", hotelPassengerInput: new HotelPassengerInput())]                                                                                                                                                                                                                                                 | 1            | ["Charlie"]                | [null]                | [null]
        []                                                                                                                                                                                                                                                                                                                                             | 0            | []                         | []                    | []
        null                                                                                                                                                                                                                                                                                                                                           | 0            | []                         | []                    | []
    }

    @Unroll
    def "testBuildPointMode with different scenarios"() {
        given:
        MapperOfCreateOrderRequestType mapper = new MapperOfCreateOrderRequestType()
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(new QueryCheckAvailContextResponseType(roomInfo:
                                new BookRoomInfoEntity(balanceType: "PP", onlyGroupMemberCanBook: onlyGroupMemberCanBook,
                                        bonusPointInfo: new QueryBonusPointInfoType(pointsMode: pointsMode, bonusPointRoom: bonusPointRoom))))
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsId")))
                        .build().getCheckAvailContextInfo()

        when: "calling buildPointMode with specific parameters"
        def result = mapper.buildPointMode(checkAvailInfo)

        then: "the result should be as expected"
        result == expectedResult

        where:
        onlyGroupMemberCanBook | bonusPointRoom | pointsMode || expectedResult
        false                  | true           | "HYKMS"    || "MEMBER_CARD"
        true                   | false          | "SJHMS"    || "PHONE"
        true                   | true           | "XXMS"     || "OFFLINE"
        true                   | true           | ""         || null
        true                   | true           | "ddd"      || null
        false                  | false          | "XXMS"     || null
    }

    @Unroll
    def "testBuildNeedRegisterRoom with different scenarios"() {
        given:
        MapperOfCreateOrderRequestType mapper = new MapperOfCreateOrderRequestType()
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(new QueryCheckAvailContextResponseType(
                                hotelInfo: new BookHotelInfoEntity(hotelGroupId: hotelGroupId),
                                roomInfo: new BookRoomInfoEntity(balanceType: "PP", groupMemberShip: groupMemberShip),
                                bookingRules: new QueryBookingRulesType(
                                        groupMemberShipInfo: new QueryGroupMemberShipType(needRegister: needRegister, groupRegisterRule: groupRegisterRule))))
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsId")))
                        .build().getCheckAvailContextInfo()

        when: "calling buildNeedRegisterRoom with specific parameters"
        def result = mapper.buildNeedRegisterRoom(checkAvailInfo)

        then: "the result should be as expected"
        result == expectedResult

        where:
        groupMemberShip | needRegister | groupRegisterRule | hotelGroupId || expectedResult
        true            | true         | "TRIP_REGISTER"   | 1            || true
        true            | true         | "TRIP_REGISTER"   | 0            || false
        true            | true         | "NO_REGISTER"     | 1            || false
        true            | false        | "TRIP_REGISTER"   | 1            || false
        false           | true         | "TRIP_REGISTER"   | 1            || false
    }

    def "getCustomerProperty" () {
        given:
        tester.buildRegisteredMemberUid(_, _,_) >> null
        expect:
        def res = tester.getCustomerProperty(new MembershipInfo(groupMemberShipCode: "code", groupMemberShipLevel: "level"), null, null, null, null)
        with(res) {
            groupMemberShipCode == "code"
            groupMemberShipLevel == "level"
        }
    }

    def "testBuildChannel"() {
        given:
        MapperOfCreateOrderRequestType mapper = new MapperOfCreateOrderRequestType()
        IntegrationSoaRequestType integrationSoaRequestType = new IntegrationSoaRequestType()
        integrationSoaRequestType.setSourceFrom(sourceFrom)
        integrationSoaRequestType.setTransferInfo(transferInfo)

        when:
        String channel = mapper.buildChannel(integrationSoaRequestType)

        then:
        channel == expectedChannel

        where:
        sourceFrom         | transferInfo                                                                         | expectedChannel
        SourceFrom.Offline | null                                                                                 | "Offline"
        SourceFrom.Online  | [new MapString("channel", "OnlineChannel")]                                          | "OnlineChannel"
        SourceFrom.Online  | [new MapString("subChannel", "SubChannel"), new MapString("channel", "MainChannel")] | "MainChannel"
        SourceFrom.Online  | []                                                                                   | null
        SourceFrom.Online  | [new MapString("subChannel", "SubChannel")]                                          | null
    }

    @Unroll
    def "testBuildAutoConfirm with different scenarios"() {
        given:
        MapperOfCreateOrderRequestType mapper = new MapperOfCreateOrderRequestType()
        OfflineInfo offlineInfo = new OfflineInfo(autoConfirm: autoConfirm)
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(new QueryCheckAvailContextResponseType(roomInfo:
                                new BookRoomInfoEntity(balanceType: "PP", roomType: roomType, tmcPriceType: tmcPriceType, gdsType: gdsType)))
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsId")))
                        .build().getCheckAvailContextInfo()

        when:
        boolean result = mapper.buildAutoConfirm(offlineInfo, checkAvailInfo)

        then:
        result == expectedAutoConfirm

        where:
        autoConfirm | roomType | tmcPriceType | gdsType   || expectedAutoConfirm
        "F"         | "M"      | "NONE"       | null      || true
        "F"         | "M"      | "M"          | null      || false
        "F"         | "M"      | "M"          | "Amadeus" || true
        "T"         | "M"      | "M"          | null      || true
    }

    MapperOfCreateOrderRequestType mapper = new MapperOfCreateOrderRequestType()

    @Unroll
    def "testBuildCreateOfflineExtEntity with different scenarios"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: SourceFrom.Offline),
                offlineInfo: new OfflineInfo(cookieId: "cookieId"))
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(new QueryCheckAvailContextResponseType(roomInfo:
                                new BookRoomInfoEntity(balanceType: "PP", roomType: "M")))
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsId")))
                        .build().getCheckAvailContextInfo()
        GetInboundParameterResponseType getInboundParameterResponseType = new GetInboundParameterResponseType(
                callInfo: new CallEntity(aNI: "aNI", uCID: "uCID"),
                membersInfo: new MembersEntity(userName: "userName"))

        when:
        CreateOfflineExtEntity result = mapper.buildCreateOfflineExtEntity(orderCreateRequestType, checkAvailInfo, getInboundParameterResponseType)

        then:
        result.getUcid() == "uCID"
        result.getUserName() == "userName"
        result.ani == "aNI"
    }

    @Unroll
    def "testBuildZoneId with different scenarios"() {
        given:

        when:
        String result = mapper.buildZoneId(null)

        then:
        result == null
    }

    @Unroll
    def "testBuildOffsetMinute with different scenarios"() {
        given:

        when:
        int result = mapper.buildOffsetMinute(null, null)

        then:
        result == 0
    }

    @Unroll
    def "testCheckIsPwcCase with different scenarios"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: sourceFrom),
                corpPayInfo: new CorpPayInfo(corpPayType: corpPayType),
                cityInput: new CityInput(cityId: 22249),
                hotelPayTypeInput: Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: payType)))
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("BillType", "A")
                        put("currency", "CNY")
                        put("IsHtlPrintTicketAfterConfirm", "T")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(new QueryCheckAvailContextResponseType(roomInfo:
                                new BookRoomInfoEntity(balanceType: "PP", roomType: roomType)))
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsId")))
                        .build().getCheckAvailContextInfo()

        when:
        boolean result = mapper.checkIsPwcCase(orderCreateRequestType, accountInfo, checkAvailInfo)

        then:
        result == expectedResult

        where:
        sourceFrom         | corpPayType | roomType | payType              || expectedResult
        SourceFrom.Online  | "public"    | "M"      | "CORP_PAY"           || true
        SourceFrom.Online  | "public"    | "M"      | "GUARANTEE_CORP_PAY" || true
        SourceFrom.Online  | "public"    | "M"      | "SELF_PAY"           || false
        SourceFrom.Offline | "public"    | "M"      | "GUARANTEE_CORP_PAY" || false
        SourceFrom.Online  | "private"   | "M"      | "GUARANTEE_CORP_PAY" || false
        SourceFrom.Online  | "public"    | "C"      | "GUARANTEE_CORP_PAY" || false
    }

    @Unroll
    def "testGetXProductList with different scenarios"() {
        given:
        List<XProductEntityType> xProductEntityTypeList = xProductEntityTypeListInput

        when:
        List<TokensEntity> result = mapper.getXProductList(xProductEntityTypeList)

        then:
        result == expectedTokensEntityList

        where:
        xProductEntityTypeListInput                                                          || expectedTokensEntityList
        []                                                                                   || []
        [new XProductEntityType(xProductToken: "token1")]                                    || [new TokensEntity(productToken: "token1", quantum: 1, productType: null, detailList: null)]
        [new XProductEntityType(xProductToken: "token2", xProductCount: 2, xProductType: 7)] || [new TokensEntity(productToken: "token2", quantum: 1, productType: 7, detailList: null)]
    }


    @Unroll
    def "testSupportMembershipPhonePoint with different scenarios"() {
        given:
        PhoneInfo phoneInfo = new PhoneInfo(countryCode: countryCode, transferPhoneNo: transferPhoneNo)
        HotelContactorInfo hotelContactorInfo = new HotelContactorInfo(
                phoneInfo: new PhoneInfo(countryCode: contactCountryCode, transferPhoneNo: contacttTransferPhoneNo))

        when:
        boolean result = mapper.supportMembershipPhonePoint(phoneInfo, hotelContactorInfo)

        then:
        result == expectedResult

        where:
        countryCode | transferPhoneNo | contactCountryCode | contacttTransferPhoneNo || expectedResult
        "86"        | "13718965555"   | "86"               | "13718965555"           || true
        "86"        | "13718965555"   | "86"               | "13718965558"           || false
        "87"        | "13718965555"   | "86"               | "13718965555"           || false
    }


    @Unroll
    def "testBuildArrivalTimeUtc with different scenarios"() {
        given:
        BookRoomInfoEntity roomInfo = new BookRoomInfoEntity(hourlyRoom: hourlyRoom)
        HourRoomToken hourRoomToken = new HourRoomToken(startTime: hourRoomTokenArriveTimeUTC)
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(cityInput: new CityInput(cityId: 22249))
        tester.buildZoneId(_ as CityInput) >> "8"
        tester.buildOffsetMinute(_ as String, _ as LocalDateTime) >> 0
        /*new MockUp<MapperOfCreateOrderRequestType>() {
            @Mock
            protected String buildZoneId(CityInput cityInput) {
                return "8"
            }
            @Mock
            protected int buildOffsetMinute(String timeZoneId, LocalDateTime earliestLocalDateTime) {
                return 0
            }
        }*/
        when:
        String result = tester.buildArrivalTimeUtc(inputArrivalTime, roomArrivalTime, roomInfo, hourRoomToken, orderCreateRequestType)

        then:
        result == expectedResult

        where:
        inputArrivalTime       | roomArrivalTime        | hourlyRoom | hourRoomTokenArriveTimeUTC                                                   || expectedResult
        "2024-06-23T10:00:00Z" | "2024-06-23T12:00:00Z" | false      | DateUtil.fromStringToCalendar("2024-06-23 11:00", DateUtil.YYYY_MM_DD_HH_MM) || "2024-06-23T10:00:00Z"
        "2024-06-23T10:00:00Z" | "2024-06-23T12:00:00Z" | true       | DateUtil.fromStringToCalendar("2024-06-23 11:00", DateUtil.YYYY_MM_DD_HH_MM) || "2024-06-23T11:00:00Z"
        null                   | null                   | false      | null                                                                         || null
    }

    @Unroll
    def "testBuildPointInfo with different scenarios"() {
        given:
        PhoneInfo phoneInfo = new PhoneInfo(countryCode: "86", transferPhoneNo: "13718965555")
        HotelContactorInfo hotelContactorInfo = new HotelContactorInfo(
                phoneInfo: new PhoneInfo(countryCode: "86", transferPhoneNo: "13718965555"))
        MembershipInfo membershipInfo = new MembershipInfo(phoneInfo: phoneInfo, membershipUid: "uid")

        when:
        PointInfo result = mapper.buildPointInfo(false, membershipInfo, [new HotelBookPassengerInput()], "pointMode", hotelContactorInfo,new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(dimensionId: "uid", primaryDimensionId: "uid")]))

        then:
        result != null
        result.pointCardNum == "13718965555"
    }

    @Unroll
    def "testBuildCreateOriOrderEntity with different scenarios"() {
        given:
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType()
        QueryHotelOrderDataResponseType queryHotelOrderDataResponseType = new QueryHotelOrderDataResponseType(orderGenericInfo: new OrderGenericInfoType(channelType: "Offline"))
        ResourceToken resourceToken = new ResourceToken(orderResourceToken: new OrderResourceToken(orderId: 678678L))
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: SourceFrom.Offline))

        when:
        CreateOriOrderEntity result = mapper.buildCreateOriOrderEntity(queryCheckAvailContextResponseType, queryHotelOrderDataResponseType, orderCreateRequestType, resourceToken)

        then:
        result != null
        result.oriOrderOffline
        result.orderId == null
    }

    def "testGetOrderModifyInfoCheckInTime"() {
        given:
        HotelDateRangeInfo hotelDateRangeInfo = new HotelDateRangeInfo(checkIn: "2024-06-21", checkOut: "2024-06-25")
        HotelModifyBookInput hotelModifyBookInput = new HotelModifyBookInput(hotelDateRangeInfo: hotelDateRangeInfo)
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                hotelModifyBookInput: hotelModifyBookInput,
                strategyInfos: Arrays.asList(new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "APPLY_MODIFY")))
        ResourceToken resourceToken = new ResourceToken()

        when:
        OrderModifyInfoType result = mapper.getOrderModifyInfo(orderCreateRequestType, resourceToken)

        then:
        result != null
        result.getCheckInTime() == "2024-06-21"
        result.getCheckOutTime() == "2024-06-25"
    }

    def "buildBizUid" () {
        expect:
        mapper.buildBizUid(null, null, null) == null
        mapper.buildBizUid(null, null, []) == null
        mapper.buildBizUid(null, null, [null]) == null
        mapper.buildBizUid(null, null, [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(employee: "T"))]) == null
        mapper.buildBizUid(null, "a", [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(employee: "F", uid: "a"))]) == "a"
        mapper.buildBizUid(null, "a", [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(employee: "F", infoId: "a"))]) == "a"
    }

    def "getPrimaryId"() {
        expect:
        mapper.buildBizUid(rep, uid) == res
        where:
        uid  | rep                                                                                                    || res
        null | null                                                                                                   || null
        "a"  | new QueryBizModeBindRelationResponseType()                                                             || null
        "b"  | new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [])                              || null
        "c"  | new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [null])                          || null
        "d"  | new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData()]) || null
        "a"  | new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(primaryDimensionId: "2")]) || null
        "a"  | new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(dimensionId: "a", primaryDimensionId: "2")]) || "2"


    }

    def "isUseNewPackageInfo"() {
        expect:
        !mapper.isUseNewPackageInfo(null)
        !mapper.isUseNewPackageInfo(new PackageExtendInfoType())
        !mapper.isUseNewPackageInfo(new PackageExtendInfoType())
        !mapper.isUseNewPackageInfo(new PackageExtendInfoType(["newXProduct": null]))
        !mapper.isUseNewPackageInfo(new PackageExtendInfoType(["newXProduct": "F"]))
        mapper.isUseNewPackageInfo(new PackageExtendInfoType(["newXProduct": "T"]))
    }

    def "buildXProductInfoList" () {
        expect:
        mapper.buildXProductInfoList(null) == null
        mapper.buildXProductInfoList([]) == null
        mapper.buildXProductInfoList([null]) == null
        mapper.buildXProductInfoList([new XProductStaticInfoType()]) == null
        mapper.buildXProductInfoList([new XProductStaticInfoType(productSpuInfoList: [])]) == null
        mapper.buildXProductInfoList([new XProductStaticInfoType(productSpuInfoList: [null])]) == null
        mapper.buildXProductInfoList([new XProductStaticInfoType(productSpuInfoList: [new XProductSpuInfoType()])]) == null
        mapper.buildXProductInfoList([new XProductStaticInfoType(productSpuInfoList: [new XProductSpuInfoType()])]) == null
        mapper.buildXProductInfoList([new XProductStaticInfoType(productSpuInfoList: [new XProductSpuInfoType()], packageToken: "NotEmpty")]) == []
        mapper.buildXProductInfoList([new XProductStaticInfoType(productSpuInfoList: [new XProductSpuInfoType(productSkuInfoList: [])], packageToken: "NotEmpty")]) == []
        mapper.buildXProductInfoList([new XProductStaticInfoType(productSpuInfoList: [new XProductSpuInfoType(productSkuInfoList: [null])], packageToken: "NotEmpty")]) == []
        mapper.buildXProductInfoList([new XProductStaticInfoType(productSpuInfoList: [new XProductSpuInfoType(productSkuInfoList: [new XProductSkuInfoType(quantity: 1)])], packageToken: "NotEmpty")])*.quantity == [1]
    }

    def "buildLocale" () {
        expect:
        mapper.buildLocale(null) == null
        mapper.buildLocale(new OrderCreateRequestType()) == null
        mapper.buildLocale(new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType())) == null
        mapper.buildLocale(new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(language: "zh-CN"))) == "zh-CN"
        mapper.buildLocale(new OrderCreateRequestType(strategyInfos: [new StrategyInfo("ORDER_LANGUAGE", "en-US")], integrationSoaRequestType: new IntegrationSoaRequestType(language: "zh-CN"))) == "en-US"

    }
    def "testNeedNewPackageProduct"(){
        when:
        new MockUp<QConfigOfCustomConfig>(){
            @Mock
            public static boolean isSupport(String key, String corpId) {
                return "123".equalsIgnoreCase(corpId)
            }
        }
        then:
        Assert.assertFalse(mapper.needNewPackageProduct(Arrays.asList(new StrategyInfo(strategyKey: "OLD_PKG_PRODUCT", strategyValue: "T")), "123"))
        Assert.assertFalse(mapper.needNewPackageProduct(Arrays.asList(new StrategyInfo(strategyKey: "OLD_PKG_PRODUCT", strategyValue: "F")), "1234"))
        Assert.assertFalse(mapper.needNewPackageProduct(Arrays.asList(new StrategyInfo(strategyKey: "OLD_PKG_PRODUCT", strategyValue: "T")), "123"))
    }
    def "testBuildPackageList"(){
        when:
        new MockUp<QConfigOfCustomConfig>(){
            @Mock
            public static boolean isSupport(String key, String corpId) {
                return "123".equalsIgnoreCase(corpId)
            }
        }
        List<XProductStaticInfoType> productStaticInfoList = new ArrayList<>()
        then:
        Assert.assertNull(mapper.buildPackageList(productStaticInfoList, null, "1234"))
        Assert.assertNull(mapper.buildPackageList(productStaticInfoList, null, "123"))

        when:
        productStaticInfoList.add(null)
        then:
        Assert.assertNull(mapper.buildPackageList(productStaticInfoList, null, "123"))

        when:
        XProductStaticInfoType xProductStaticInfoType = new XProductStaticInfoType()
        productStaticInfoList.add(xProductStaticInfoType)
        then:
        Assert.assertNull(mapper.buildPackageList(productStaticInfoList, null, "123"))

        when:
        productStaticInfoList = new ArrayList<>()
        xProductStaticInfoType = new XProductStaticInfoType()
        productStaticInfoList.add(xProductStaticInfoType)
        xProductStaticInfoType.setPackageId(123)
        then:
        Assert.assertNull(mapper.buildPackageList(productStaticInfoList, null, "123"))

        when:
        productStaticInfoList = new ArrayList<>()
        xProductStaticInfoType = new XProductStaticInfoType()
        productStaticInfoList.add(xProductStaticInfoType)
        xProductStaticInfoType.setPackageId(123)
        xProductStaticInfoType.setPackageToken("1234")
        List<XProductSpuInfoType> productSpuInfoList = new ArrayList<>()
        XProductSpuInfoType xProductSpuInfoType = new XProductSpuInfoType()
        productSpuInfoList.add(xProductSpuInfoType)
        xProductSpuInfoType.setProductSkuInfoList(Arrays.asList(new XProductSkuInfoType(quantity: 1,productId: 2)))
        xProductStaticInfoType.setProductSpuInfoList(productSpuInfoList)
        List<TokensEntity> tokensEntities = mapper.buildPackageList(productStaticInfoList, null, "123")
        GetPackageRoomListResponseType getPackageRoomListResponseType = new GetPackageRoomListResponseType()
        getPackageRoomListResponseType.setProductStaticInfoList(productStaticInfoList)
        List<TokensEntity> tokensEntityList = mapper.buildXProductList(null, null, getPackageRoomListResponseType,null,"123")
        then:
        Assert.assertNotNull(tokensEntities)
        Assert.assertEquals(1, tokensEntities.size())
        Assert.assertEquals(2, tokensEntities.get(0).getProductId())
        Assert.assertEquals(1, tokensEntities.get(0).getQuantum())
        Assert.assertEquals(4, tokensEntities.get(0).getProductType())
        Assert.assertEquals(1, tokensEntityList.size())
        Assert.assertEquals(2, tokensEntityList.get(0).getProductId())
        Assert.assertEquals(1, tokensEntityList.get(0).getQuantum())
        Assert.assertEquals(4, tokensEntityList.get(0).getProductType())
    }


    @Unroll
    def "testCheckCreateTripResponseType with #description"() {
        given: "Mocked dependencies"
        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            public static boolean requireCreateTrip(OrderCreateRequestType orderCreateRequestType,
                                                    WrapperOfAccount.AccountInfo accountInfo, OrderCreateToken orderCreateToken) {
                return true
            }
        }
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
        def createTripResponseType = Mock(CreateTripResponseType) {
            getTripId() >> 0
            getResponseCode() >> 78798
            getResponseDesc() >> "error"
        }
        def orderCreateRequestType = Mock(OrderCreateRequestType)
        def accountInfo = Mock(WrapperOfAccount.AccountInfo)
        def orderCreateToken = Mock(OrderCreateToken)
        when: "Calling checkCreateTripResponseType"
        mapper.checkCreateTripResponseType(createTripResponseType, orderCreateRequestType, accountInfo, orderCreateToken)

        then: "The expected behavior occurs"
        def ex = thrown(BusinessException)
        ex.getErrorCode() == 686
        ex.logErrorCode == "78798"
        ex.errorMessage == "78798error"
        ex.friendlyMessage == "78798error"
    }

    def "buildRoomInfo" () {
        expect:
        new MapperOfCreateOrderRequestType().buildRoomInfo(new OrderCreateRequestType(hotelBookPassengerInputs: []), new QueryCheckAvailContextResponseType(), new ResourceToken(), Mock(WrapperOfCheckAvail.CheckAvailContextInfo)) == null
        new MapperOfCreateOrderRequestType().buildRoomInfo(new OrderCreateRequestType(hotelBookPassengerInputs: []), new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity()), new ResourceToken(), Mock(WrapperOfCheckAvail.CheckAvailContextInfo)) == null
        new MapperOfCreateOrderRequestType().buildRoomInfo(new OrderCreateRequestType(hotelBookInput: new HotelBookInput(roomQuantity: 2, hotelDateRangeInfo: new HotelDateRangeInfo(checkIn: "2025-01-01")), hotelBookPassengerInputs: []), new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(), bookingRules: new QueryBookingRulesType(limitArrivalDateTime: new QueryLimitArrivalDateTimeType())), new ResourceToken(), Mock(WrapperOfCheckAvail.CheckAvailContextInfo)).roomQuantity == 2
    }

    def "toCertificate"() {
        given:
        new MockUp<CoreInfoUtil>() {
            @Mock
            public static String encrypt(InfoKey infoKey) {
                return "result"
            }
        }
        def input = new ClientEntity()
        when:
        new MapperOfCreateOrderRequestType().toCertificate(null, new HotelBookPassengerInput(), null)
        new MapperOfCreateOrderRequestType().toCertificate(input, new HotelBookPassengerInput(certificateInfo: new CertificateInfo(certificateType: type, transferCertificateNo: "no")), null)
        then:
        input.certificateType == res
        where:
        type                            || res
        "IDENTITY_CARD"                 || CertificateTypeEnum.IdentityCard
        "PASSPORT"                      || CertificateTypeEnum.Passport
        "MTP"                           || CertificateTypeEnum.TaiwaneseCertificate
        "TAIWANPASS"                    || CertificateTypeEnum.TaiwanPass
        "HKMACPASS"                     || CertificateTypeEnum.HKAndMacauPass
        "HOMEPERMIT"                    || CertificateTypeEnum.HometownPermit
        "RUSSIA_DOMESTIC_PASSPORT"      || CertificateTypeEnum.RussianDomesticPassport
        "OVERSEA_AND_LOCAL_TRAVEL_CARD" || CertificateTypeEnum.OverseaTravelCertificate
        "SEAMAN_CARD"                   || CertificateTypeEnum.InternationalSeamanPassport
        "RESIDENCEPERMITHKT"            || CertificateTypeEnum.HKAndMacaoResidenceCertificate
        "RESIDENCE_PERMIT_HKT"          || null
    }


    def "getRights" () {
        expect:
        new MapperOfCreateOrderRequestType().getRights(null) == null
        new MapperOfCreateOrderRequestType().getRights([]) == null
        new MapperOfCreateOrderRequestType().getRights([new AvailableRight(rightType: "Breakfast", quantity: 2)])*.quantum == [2]
    }


    def "getRCInfoList" () {
        given:
        new MockUp<TokenParseUtil>() {
            @Mock
            public static <T> T parseToken(String token, Class<T> clazz) {
                return new RcToken(type: "CONFLICT_BOOK")
            }
        }
        expect:
        new MapperOfCreateOrderRequestType().getRCInfoList(null, null) == null
        new MapperOfCreateOrderRequestType().getRCInfoList([], null) == null
        new MapperOfCreateOrderRequestType().getRCInfoList([new RCInput(), null], new OrderCreateRequestType())*.type == ["RepeatBooking"]
    }

    def "getRCInfoList_" () {
        given:
        new MockUp<TokenParseUtil>() {
            @Mock
            public static <T> T parseToken(String token, Class<T> clazz) {
                return new RcToken(type: "APPLY_MODIFY")
            }
        }
        expect:
        new MapperOfCreateOrderRequestType().getRCInfoList([new RCInput(), null], new OrderCreateRequestType(strategyInfos: [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "APPLY_MODIFY")]))*.type == ["ModifyApplyFrom"]
    }


    def "buildBaseInfo" () {
        expect:
        new MapperOfCreateOrderRequestType().buildBaseInfo(null, null, null) == null
        new MapperOfCreateOrderRequestType().buildBaseInfo(new OrderCreateRequestType(clientInfo: new ClientInfo(secondaryChannel: "a"),integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo())), new ResourceToken(reservationResourceToken: new ReservationResourceToken()), Mock(WrapperOfAccount.AccountInfo)).secondaryChannel == "a"
    }

    def "needGuarantee"() {
        expect:
        new MapperOfCreateOrderRequestType().needGuarantee(true, null, null, null)
        !new MapperOfCreateOrderRequestType().needGuarantee(false, null, null, null)
        new MapperOfCreateOrderRequestType().needGuarantee(false, null, new QueryCancelPolicyType(needGuarantee: true), null)
        !new MapperOfCreateOrderRequestType().needGuarantee(false, null, new QueryCancelPolicyType(needGuarantee: false), null)
    }

}
