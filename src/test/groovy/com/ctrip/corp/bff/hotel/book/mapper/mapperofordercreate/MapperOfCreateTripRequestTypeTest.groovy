package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.soa._21234.GetUserLiteInfoResponseType
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3
import spock.lang.Unroll;

class MapperOfCreateTripRequestTypeTest extends Specification {

    def tester = Spy(new MapperOfCreateTripRequestType())
    def savePoint = new mockit.internal.state.SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        given:
        expect:
        tester.convert(Tuple3.of(new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a"))), new GetUserLiteInfoResponseType(), [:])) .uid == "a"
    }


    @Unroll
    def "testBuildChannelType with #description"() {
        given: "A mocked IntegrationSoaRequestType"
        def integrationSoaRequestType = Mock(IntegrationSoaRequestType) {
            getSourceFrom() >> sourceFrom
        }

        when: "Calling buildChannelType"
        def result = tester.buildChannelType(integrationSoaRequestType)

        then: "The result should match the expected value"
        result == expectedChannelType

        where:
        description             | sourceFrom         | expectedChannelType
        "sourceFrom is null"    | null               | 3
        "sourceFrom is Offline" | SourceFrom.Offline | 2
        "sourceFrom is Online"  | SourceFrom.Online  | 1
        "sourceFrom is CRN"     | SourceFrom.CRN     | 3
        "sourceFrom is CRN"     | SourceFrom.H5      | 3
        "sourceFrom is CRN"     | SourceFrom.Native  | 3
    }
}
