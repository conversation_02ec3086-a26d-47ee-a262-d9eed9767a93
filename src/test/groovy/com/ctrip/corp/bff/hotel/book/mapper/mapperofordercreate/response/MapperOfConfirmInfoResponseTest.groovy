package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response

import com.ctrip.corp.agg.hotel.roomavailable.entity.BookHotelInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookRoomInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryBookingRulesType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryGroupMemberShipType
import com.ctrip.corp.bff.framework.hotel.entity.contract.ConfirmDetailExtend
import com.ctrip.corp.bff.framework.hotel.entity.contract.ConfirmDetailInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.ConfirmInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ReservationResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CertificateInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PassengerBasicInfo
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import ctrip.BBZ.members.bbzmbrCommonPassenger.CommonPassenger
import ctrip.BBZ.members.bbzmbrCommonPassenger.CommonPassengerCard
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2024/12/11 22:45
 *
 */
class MapperOfConfirmInfoResponseTest extends Specification {
    MapperOfConfirmInfoResponse mapper = new MapperOfConfirmInfoResponse()

    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }

    @Unroll
    def "testBuildNameEN with #description"() {
        when:
        String result = mapper.buildNameEN(firstName, lastName, middleName)

        then:
        result == expectedResult

        where:
        description                         | firstName | lastName | middleName | expectedResult
        "all names are blank"               | ""        | ""       | ""         | ""
        "only first name is provided"       | "John"    | ""       | ""         | ""
        "only last name is provided"        | ""        | "Doe"    | ""         | ""
        "only middle name is provided"      | ""        | ""       | "Smith"    | ""
        "first and last names are provided" | "John"    | "Doe"    | ""         | "Doe/John"
        "all names are provided"            | "John"    | "Doe"    | "Smith"    | "John Smith/Doe"
    }

    @Unroll
    def "testBuildConfirmDetailExtend with #description"() {
        when:
        ConfirmDetailExtend result = mapper.buildConfirmDetailExtend(key, value, infoId, certificateTypeInput)

        then:
        result.getKey() == expectedKey
        result.getValue() == value

        where:
        description                                     | key                        | value    | infoId | certificateTypeInput | expectedKey
        "all parameters are provided"                   | "{0}_REAL_CERTIFICATE.{1}" | "123456" | "001"  | "IDENTITY_CARD"      | "001_REAL_CERTIFICATE.IDENTITY_CARD"
        "infoId is null"                                | "{0}_REAL_NAME"            | "123456" | "001"  | ""                   | "001_REAL_NAME"
        "both infoId and certificateTypeInput are null" | "{0}_REAL_CERTIFICATE.{1}" | "123456" | null   | null                 | "{0}_REAL_CERTIFICATE.{1}"
    }

    @Unroll
    def "testBuildCertificateTypeEquals with #description"() {
        when:
        boolean result = mapper.buildCertificateTypeEquals(certificateTypeInput, cardType)

        then:
        result == expectedResult

        where:
        description                     | certificateTypeInput | cardType | expectedResult
        "both inputs are blank"         | ""                   | ""       | false
        "certificateTypeInput is blank" | ""                   | "1"      | false
        "cardType is blank"             | "IDENTITY_CARD"      | ""       | false
        "cardType is zero"              | "IDENTITY_CARD"      | "0"      | false
        "valid inputs match"            | "IDENTITY_CARD"      | "1"      | true
        "valid inputs do not match"     | "PASSPORT"           | "2"      | true
        "invalid cardType"              | "IDENTITY_CARD"      | "99"     | false
        "invalid certificateTypeInput"  | "INVALID_TYPE"       | "1"      | false
    }


    @Unroll
    def "testBuildPriceChangeInfo with #description"() {
        given:
        List<CommonPassenger> commonPassengerList = commonPassengers
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(hotelBookPassengerInputs: hotelBookPassengerInputs, cityInput: new CityInput(cityId: 1))
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(new QueryCheckAvailContextResponseType(
                                hotelInfo: new BookHotelInfoEntity(),
                                roomInfo: new BookRoomInfoEntity(balanceType: "PP"),
                                bookingRules: new QueryBookingRulesType()))
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsId")))
                        .build().getCheckAvailContextInfo()

        when:
        ConfirmInfo result = mapper.buildConfirmInfo(checkAvailInfo, commonPassengerList, orderCreateRequestType, null, null)

        then:
        result == expectedResult

        where:
        description                       | commonPassengers                                                                                                                                                                      | hotelBookPassengerInputs                                                                                                                                                                                                                                                                                 | expectedResult
        "empty commonPassengerList"       | []                                                                                                                                                                                    | [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(infoId: "1"))]                                                                                                                                                                                                                 | null
        "empty hotelBookPassengerInputs"  | [new CommonPassenger(passengerID: "1")]                                                                                                                                               | []                                                                                                                                                                                                                                                                                                       | null
        "mismatched infoId"               | [new CommonPassenger(passengerID: "1")]                                                                                                                                               | [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(infoId: "2"))]                                                                                                                                                                                                                 | null
        "matched infoId"                  | [new CommonPassenger(passengerID: "1", CNName: "张三", ENFirstName: "san", ENLastName: "zhang")]                                                                                      | [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(infoId: "1"), name: "李四", passengerBasicInfo: new PassengerBasicInfo(preferLastName: "li", preferFirstName: "si"))]                                                                                                          | new ConfirmInfo(confirmDetailInfos: [new ConfirmDetailInfo(code: "INFOID_REMIND", confirmDetailExtends: [new ConfirmDetailExtend(key: "1_REAL_NAME", value: "zhang/san"), new ConfirmDetailExtend(key: "1_ORDER_NAME", value: "li/si")])])
        "matched infoId with certificate" | [new CommonPassenger(passengerID: "1", CNName: "张三", ENFirstName: "san", ENLastName: "zhang", commonPassengerCardList: [new CommonPassengerCard(cardType: "1", cardNo: "123456")])] | [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(infoId: "1"), name: "李四", passengerBasicInfo: new PassengerBasicInfo(preferLastName: "li", preferFirstName: "si"), certificateInfo: new CertificateInfo(certificateType: "IDENTITY_CARD", transferCertificateNo: "654321"))] | new ConfirmInfo(confirmDetailInfos: [new ConfirmDetailInfo(code: "INFOID_REMIND", confirmDetailExtends: [new ConfirmDetailExtend(key: "1_REAL_NAME", value: "zhang/san"), new ConfirmDetailExtend(key: "1_ORDER_NAME", value: "li/si"), new ConfirmDetailExtend(key: "1_REAL_CERTIFICATE.IDENTITY_CARD", value: "123456"), new ConfirmDetailExtend(key: "1_ORDER_CERTIFICATE.IDENTITY_CARD", value: "654321")])])
    }

}
