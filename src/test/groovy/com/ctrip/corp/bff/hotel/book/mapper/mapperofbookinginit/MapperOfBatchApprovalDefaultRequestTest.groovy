package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.bff.framework.hotel.entity.contract.AddPriceInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.ArriveTimeInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.CouponDetailInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.CouponInfoInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.FlashStayInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceDetailInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HourRoomInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.MapString
import com.ctrip.corp.bff.framework.template.entity.PosEnum
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.AmountInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.BookAgentScopeInfoInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CertificateInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.EmailInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.IssuingCountryInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.MiceInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.NationalityInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PassengerBasicInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PassengerInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PhoneInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.RCInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ResourceTokenInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.SSOInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult
import com.ctrip.corp.bff.hotel.book.contract.ApprovalFlowInput
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import com.ctrip.corp.bff.hotel.book.contract.InvoiceInput
import com.ctrip.corp.bff.hotel.book.contract.TravelPolicyInfo
import com.ctrip.corp.bff.hotel.book.contract.TripInfoInput
import com.ctrip.corp.bff.specific.contract.BatchApprovalDefaultRequestType
import com.ctrip.corp.bff.specific.contract.ExtRelationItem
import com.ctrip.corp.frontend.basic.ct.service.ExtendFilter
import spock.lang.*
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint

class MapperOfBatchApprovalDefaultRequestTest extends Specification {
    @Subject
    def mapperOfBatchApprovalDefaultRequest = new MapperOfBatchApprovalDefaultRequest()

    def savePoint = new SavePoint()

    def cleanup() {
        savePoint.rollback()
    }

    @Unroll
    def "buildBookingType"() {
        given: "设定相关方法入参"

        List<StrategyInfo> strategyInfos = [new StrategyInfo(strategyKey:"BOOKING_SCENARIO", "strategyValue":"MODIFY")]
        List<StrategyInfo> strategyInfos2 = []


        when:
        def result = mapperOfBatchApprovalDefaultRequest.convertBookingType(strategyInfos)
        def result2 = mapperOfBatchApprovalDefaultRequest.convertBookingType(strategyInfos2)

        then: "验证返回结果里属性值是否符合预期"
        result == MapperOfApprovalTextInfoRequestType.HOTEL_CHANGE
        result2 == MapperOfApprovalTextInfoRequestType.NORMAL
    }
}

