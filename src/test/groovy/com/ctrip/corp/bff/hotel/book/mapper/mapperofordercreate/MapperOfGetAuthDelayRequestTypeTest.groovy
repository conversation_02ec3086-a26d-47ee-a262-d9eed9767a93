package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import mockit.internal.state.SavePoint
import spock.lang.Specification

class MapperOfGetAuthDelayRequestTypeTest extends Specification {


    def tester = Spy(new MapperOfGetAuthDelayRequestType())

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        given:
        expect:
        tester.convert(Tuple1.of(new IntegrationSoaRequestType(userInfo: new UserInfo()))).businessType == "H"
    }
}

