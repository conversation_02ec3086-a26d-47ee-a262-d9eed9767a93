package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceDetailInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import spock.lang.Specification

class MapperOfCalculateTravelRewardsRequestTypeTest extends Specification {

    def tester = Spy(MapperOfCalculateTravelRewardsRequestType)

    def savePoint = new mockit.internal.state.SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        given:
        1 * tester.getRequestBaseInfoType(_, _, _) >> null
        1 * tester.getCouponAmount(_) >> null
        1 * tester.buildCorpXProductInfoType(_) >> null
        1* tester.buildServiceAmount(_, _) >> null
        1 * tester.getGuestIngetfoList( _) >> null

        expect:
        tester.convert(Tuple4.of(null, Mock(WrapperOfCheckAvail.CheckAvailContextInfo), null, null)) != null


    }

    def "getCouponAmount" () {
        given:
        def input = Mock(WrapperOfCheckAvail.CheckAvailContextInfo)
        input.getCouponAmount() >> null >> 0 >> 2
        expect:
        tester.getCouponAmount(null) == null
        tester.getCouponAmount(input) == null
        tester.getCouponAmount(input) == null
        tester.getCouponAmount(input).price == 2
    }

    def "buildCorpXProductInfoType" () {
        expect:
        tester.buildCorpXProductInfoType(new OrderCreateRequestType()) == null
        tester.buildCorpXProductInfoType(new OrderCreateRequestType(hotelInsuranceInput: new HotelInsuranceInput())) == null
        tester.buildCorpXProductInfoType(new OrderCreateRequestType(hotelInsuranceInput: new HotelInsuranceInput(hotelInsuranceDetailInputs: []))) == null
        tester.buildCorpXProductInfoType(new OrderCreateRequestType(hotelInsuranceInput: new HotelInsuranceInput(hotelInsuranceDetailInputs: [new HotelInsuranceDetailInput(insuranceHotelBookPassengerInputs: [])]))) == []
        tester.buildCorpXProductInfoType(new OrderCreateRequestType(hotelInsuranceInput: new HotelInsuranceInput(hotelInsuranceDetailInputs: [new HotelInsuranceDetailInput(insuranceHotelBookPassengerInputs: [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput())])]))) *.ownerType == ["PERSON"]
    }


    def "getRequestBaseInfoType" () {
        expect:
        tester.getRequestBaseInfoType(new OrderCreateRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "private"),integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo())), "CNY", null).customCurrency == "CNY"
    }

    def "getPolicyUid" () {
        expect:
        tester.getPolicyUid(new PolicyInput(policyUid: "a"), null) == "a"
        tester.getPolicyUid(null, new UserInfo(userId: "a")) == "a"
    }
}
