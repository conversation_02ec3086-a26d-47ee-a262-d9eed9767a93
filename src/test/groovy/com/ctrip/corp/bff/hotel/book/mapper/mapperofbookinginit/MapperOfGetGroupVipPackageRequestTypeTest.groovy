package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import corp.user.service.corpUserInfoService.GetPlatformRelationByUidResponseType
import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;

class MapperOfGetGroupVipPackageRequestTypeTest extends Specification {

    def tester = Spy(new MapperOfGetGroupVipPackageRequestType())

    def savePoint = new SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        expect:
        tester.convert(Tuple3.of(new BookingInitRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(requestId: "a")), Mock(WrapperOfCheckAvail.CheckAvailInfo), null)).head.traceLogID == "a"
    }

    def "getPlatformRelationByUid" () {
        expect:
        tester.getPlatformRelationByUid(null) == null
        tester.getPlatformRelationByUid(new GetPlatformRelationByUidResponseType(accountId: "a")) == "a"
    }
}
