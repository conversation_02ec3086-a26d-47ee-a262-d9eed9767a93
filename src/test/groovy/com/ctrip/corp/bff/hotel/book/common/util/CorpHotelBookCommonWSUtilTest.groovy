package com.ctrip.corp.bff.hotel.book.common.util

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.MapString
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetPackageRoomListRequestType
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.MapperOfGetPackageRoomSnapshotRequestType
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @Date 2024/12/5 16:35
 */
class CorpHotelBookCommonWSUtilTest extends Specification {
    def "testBuildRequestBaseInfoTypeWithPolicyUidNotNull"() {
        when:
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType()
        bookingInitRequestType.setIntegrationSoaRequestType(new IntegrationSoaRequestType(userInfo: new UserInfo()))
        then:
        Assert.assertNotNull(CorpHotelBookCommonWSUtil.buildRequestBaseInfoTypeWithPolicyUidNotNull(bookingInitRequestType))
        Assert.assertNotNull(MapperOfGetPackageRoomListRequestType.buildRequestBaseInfoType(bookingInitRequestType))
        Assert.assertNotNull(MapperOfGetPackageRoomSnapshotRequestType.buildRequestBaseInfoType(bookingInitRequestType))
    }

    def "buildSubChannel" () {
        expect:
        CorpHotelBookCommonWSUtil.buildSubChannel(null) == null
        CorpHotelBookCommonWSUtil.buildSubChannel(new IntegrationSoaRequestType(transferInfo: [new MapString(key: "subChannel", value: "wecom")] )) == "WECOM"
        CorpHotelBookCommonWSUtil.buildSubChannel(new IntegrationSoaRequestType(transferInfo: [new MapString(key: "subChannel", value: "other"), new MapString(key: "channel", value:  "wechat")] )) == "WECHAT"
        CorpHotelBookCommonWSUtil.buildSubChannel(new IntegrationSoaRequestType(transferInfo: [new MapString(key: "subChannel", value: "other"), new MapString(key: "channel", value:  "OTHER")] )) == "other"
    }
    @Unroll
    def "testBuildPaymentMethod with #description"() {
        given: "A HotelPayTypeEnum value"
        // No additional setup required

        when: "Calling buildPaymentMethod"
        def result = CorpHotelBookCommonWSUtil.buildPaymentMethod(hotelPayTypeEnum)

        then: "The result should match the expected value"
        result == expectedResult

        where:
        description                     | hotelPayTypeEnum              || expectedResult
        "CORP_PAY scenario"             | HotelPayTypeEnum.CORP_PAY || "ACCOUNT_PAY"
        "ADVANCE_PAY scenario"          | HotelPayTypeEnum.ADVANCE_PAY  || "ACCOUNT_PAY"
        "FLASH_STAY_PAY scenario"       | HotelPayTypeEnum.FLASH_STAY_PAY || "ACCOUNT_PAY"
        "SELF_PAY scenario"             | HotelPayTypeEnum.SELF_PAY     || "INDIVIDUAL_PAY"
        "MIX_PAY scenario"              | HotelPayTypeEnum.MIX_PAY      || "MIX_PAY"
        "CASH scenario"                 | HotelPayTypeEnum.CASH         || "CASH_PAY"
        "UNION_PAY scenario"            | HotelPayTypeEnum.UNION_PAY    || "UNION_PAY"
        "PRBAL scenario"                | HotelPayTypeEnum.PRBAL        || hotelPayTypeEnum.getCode()
        "NONE scenario"                 | HotelPayTypeEnum.NONE         || ""
        "Null scenario"                 | null                          || ""
    }}
