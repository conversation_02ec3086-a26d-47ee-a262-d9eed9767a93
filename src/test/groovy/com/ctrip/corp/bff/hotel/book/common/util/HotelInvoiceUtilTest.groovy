package com.ctrip.corp.bff.hotel.book.common.util

import com.ctrip.corp.bff.hotel.book.contract.HotelInvoiceInfo
import spock.lang.Specification

class HotelInvoiceUtilTest extends Specification {



    void setup() {
    }

    void cleanup() {


    }

    def "getHotelInvoiceInfoInsurance" () {
        expect:
        HotelInvoiceUtil.getHotelInvoiceInfoInsurance(null) == null
        HotelInvoiceUtil.getHotelInvoiceInfoInsurance([]) == null
        HotelInvoiceUtil.getHotelInvoiceInfoInsurance([null]) == null
        HotelInvoiceUtil.getHotelInvoiceInfoInsurance([new HotelInvoiceInfo(hotelInvoiceType: "INSURANCE")]).hotelInvoiceType == "INSURANCE"
    }
}
