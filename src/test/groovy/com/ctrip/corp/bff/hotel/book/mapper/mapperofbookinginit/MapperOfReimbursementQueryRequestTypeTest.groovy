package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataRequestType
import spock.lang.Specification

class MapperOfReimbursementQueryRequestTypeTest extends Specification {

    def tester = Spy(MapperOfReimbursementQueryRequestType)

    def savePoint = new mockit.internal.state.SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert"() {
        expect:
        tester.convert(Tuple2.of(null, null)) == null
        tester.convert(Tuple2.of(new BookingInitRequestType(), 2l)) .orderId == 2
    }
}
