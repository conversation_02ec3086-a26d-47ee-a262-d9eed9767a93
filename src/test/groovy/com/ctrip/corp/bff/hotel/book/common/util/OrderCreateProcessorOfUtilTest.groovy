package com.ctrip.corp.bff.hotel.book.common.util

import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckOverStandardRcInfoType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckRcResultType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType
import com.ctrip.corp.approve.ws.contract.ApproveObj
import com.ctrip.corp.approve.ws.contract.ApproveObjInfo
import com.ctrip.corp.approve.ws.contract.CorpBookingInfo
import com.ctrip.corp.approve.ws.contract.CostCenter
import com.ctrip.corp.approve.ws.contract.FlowTmpl
import com.ctrip.corp.approve.ws.contract.HotelOrderInfo
import com.ctrip.corp.approve.ws.contract.MatchApprovalFlowResponseType
import com.ctrip.corp.approve.ws.contract.TravelerInfo
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfCityBaseInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.OriginalOrderInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.OrderCheckResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ApprovalInfoResult
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.CreateOrderResult
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.FollowApprovalResult
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.specific.common.entity.ApprovalFlowInfoEntity
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.common.log.enumeration.LogLevelEnum
import com.ctrip.corp.bff.framework.template.common.log.logging.LogUtil
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil
import com.ctrip.corp.bff.framework.template.common.serialize.ProtobufSerializerUtil
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.PosEnum
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalFlowInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalFlowReuseInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.Approver
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CertificateInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.InvoiceCompanyInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.InvoiceInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.NationalityInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PassengerBasicInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PassengerName
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CostCenterInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.MiceInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import com.ctrip.corp.bff.hotel.book.common.builder.BookingInitAssembleRequest
import com.ctrip.corp.bff.hotel.book.common.enums.DistinguishReservationEnum
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum
import com.ctrip.corp.bff.hotel.book.common.enums.RcTypeEnum
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.ContinueInfo
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import com.ctrip.corp.bff.hotel.book.contract.FollowApprovalInfoInput
import com.ctrip.corp.bff.hotel.book.contract.HotelInvoiceInfo
import com.ctrip.corp.bff.hotel.book.contract.MembershipInfo
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType
import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.response.MapperOfBookingInitResponse
import com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.MapperOfMatchApprovalFlowRequestType
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig
import com.ctrip.corp.bff.hotel.book.sharkmock.SharkMockUtil
import com.ctrip.corp.bff.specific.contract.ApprovalFlowComputeResponseType
import com.ctrip.corp.corpsz.configuration.common.contract.CustomConfig
import com.ctrip.corp.corpsz.configuration.common.contract.CustomConfigSearchResponseType
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType
import com.ctrip.corp.hotel.book.query.entity.CityBaseInfoEntity
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType
import com.ctrip.corp.hotelbooking.hotelws.entity.ClientEntity
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderRequestType
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType
import com.ctrip.soa._20183.CostCenterInfoType
import com.ctrip.soa._20183.PassengerCostCenterInfoType
import com.ctrip.soa._20183.SaveCommonDataRequestType
import com.ctrip.soa._20183.SaveCommonDataResponseType
import com.ctrip.soa._20184.CheckHotelAuthExtensionResponseType
import com.ctrip.soa._20184.QueryHotelAuthExtensionResponseType
import com.ctrip.soa._21234.BasicInfo
import com.ctrip.soa._21234.CreateTripResponseType
import com.ctrip.soa._21234.SearchTripDetailResponseType
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderDetailInfoType
import com.ctrip.soa.corp.order.orderindexextservice.v1.GetOrderFoundationDataResponseType
import com.ctrip.soa.corp.order.orderindexextservice.v1.OrderFoundationDataInfo
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType
import corp.user.service.CorpAccountQueryService.GetAuthDelayResponseType
import corp.user.service.CorpAccountQueryService.HotelAuthDelayConfig
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType
import corp.user.service.group4j.accounts.BizModeBindRelationData
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import org.junit.platform.commons.util.StringUtils
import spock.lang.Specification
import spock.lang.Unroll
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.BookInitResourceToken
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil;
import com.ctrip.corp.bff.hotel.book.common.constant.ApprovalFlowReuseConstant
import com.ctrip.corp.bff.framework.template.common.utils.StringUtil
import com.ctrip.corp.bff.framework.template.common.utils.TemplateNumberUtil
import com.ctrip.corp.bff.framework.hotel.common.util.CorpPayInfoUtil
import com.ctrip.soa._20184.OriginalOrderInfoType
import com.ctrip.corp.bff.framework.template.entity.PosEnum
import com.ctrip.corp.bff.framework.template.common.utils.BooleanUtil
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderBasicInfoType
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.HotelInfoType
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.HotelAreaInfoType
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderDetailResponseType
import com.ctrip.corp.bff.framework.template.common.utils.collection.CollectionUtil;
import spock.lang.*
import com.ctrip.corp.bff.hotel.book.common.constant.ApprovalFlowReuseConstant
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst

/**
 * <AUTHOR>
 * @date 2024/10/29 19:23
 *
 */
class OrderCreateProcessorOfUtilTest extends Specification {
    def savePoint = new SavePoint()

    void setup() {
        new MockUp<LogUtil>() {
            @Mock
            public static void loggingClogOnly(LogLevelEnum level,
                                               Class<?> type,
                                               String title,
                                               String message,
                                               Map<String, String> indexTags) {
                return;
            }

            @Mock
            public static void loggingClogOnly(LogLevelEnum level,
                                               Class<?> type,
                                               String title,
                                               String message,
                                               Throwable throwable,
                                               Map<String, String> indexTags) {
                return;
            }
        }
        new MockUp<JsonUtil>() {
            @Mock
            public static String toJson(Object obj) {
                return "mock";
            }
        }
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "buildCreateOrderResultByCreateOrder"() {
        given:
        CreateOrderResponseType createOrderResponseType = JsonUtil.fromJson("{\"wsId\":\"51ed8f76-28f1-4e1b-a03e-7960636ced20\",\"orderId\":1132566979114233,\"orderPaymentInfo\":{\"payType\":\"SELF_PAY\",\"orderAmountInfo\":{\"cnyTotalAmount\":335.00,\"personalPayAmountInfo\":{\"amount\":350.00,\"currency\":\"CNY\",\"exchangeRateToCNY\":1},\"paymentItemList\":[{\"prepayType\":\"SELF_PAY\",\"gatheringType\":\"D\",\"feeType\":\"HotelRoomFee\",\"amountInfo\":{\"amount\":335.00,\"currency\":\"CNY\",\"exchangeRateToCNY\":1}},{\"prepayType\":\"SELF_PAY\",\"gatheringType\":\"D\",\"feeType\":\"BookServiceFee\",\"amountInfo\":{\"amount\":15.00,\"currency\":\"CNY\",\"exchangeRateToCNY\":1}}]}},\"success\":true,\"supportPaymentBill\":true,\"paymentTransactionId\":\"296716091645313176\",\"orderTotalAmount\":{\"orderAmount\":{\"originAmount\":{\"amount\":335.00,\"currency\":\"CNY\",\"exchange\":1},\"cNYAmount\":335.00},\"paymentBillInfo\":{\"payAmount\":350.00,\"payCNYAmount\":350.00,\"payCurrency\":\"CNY\",\"payExchange\":1}},\"transactionInfo\":{\"transactionId\":\"454302d5-5c56-4dd0-a8eb-3122660e4e9b\",\"subTransactionIdList\":[{\"payType\":\"PERSONAL\",\"subTransactionId\":\"545341af-516f-4f13-b9d0-742bb2a28d5f\"}]},\"responseCode\":20000,\"responseStatus\":{\"timestamp\":\"2024-10-29 17:29:41.117+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", CreateOrderResponseType.class)
        OrderCreateRequestType orderCreateRequestType = JsonUtil.fromJson("{\"integrationSoaRequestType\":{\"userInfo\":{\"userId\":\"_SL2236978295\",\"corpId\":\"p15123007198\",\"groupId\":\"Gr_00007155\",\"pos\":\"CHINA\"},\"language\":\"zh-CN\",\"requestId\":\"3e46784b7b4a42a982311f1ed6e047ec\",\"sourceFrom\":\"Online\",\"transactionID\":\"TID.HOTELBOOK.1623.E74FF6459F184DE69DFDD526B405ABDD\",\"gatewayHost\":\"ct.ctrip.fat12024.qa.nt.ctripcorp.com\",\"timezoneOffsetMinutesNew\":\"480\",\"logIndices\":[{\"key\":\"groupId\",\"value\":\"Gr_00007155\"},{\"key\":\"timezoneOffsetMinutesNew\",\"value\":\"480\"},{\"key\":\"gatewayHost\",\"value\":\"ct.ctrip.fat12024.qa.nt.ctripcorp.com\"},{\"key\":\"pvid\",\"value\":\"204\"},{\"key\":\"subChannel\",\"value\":\"\"},{\"key\":\"CID\",\"value\":\"09031120410632660564\"},{\"key\":\"EID\"},{\"key\":\"CIP\",\"value\":\"*************\"},{\"key\":\"miceToken\",\"value\":\"\"},{\"key\":\"groupName\",\"value\":\"ph测试集团1\"},{\"key\":\"gatewayIdc\",\"value\":\"NTGXH\"},{\"key\":\"traceID\",\"value\":\"\"},{\"key\":\"A\",\"value\":\"ordercheck\"},{\"key\":\"CorpID\",\"value\":\"p15123007198\"},{\"key\":\"C\",\"value\":\"corpbookinghotelbookservice\"},{\"key\":\"M\",\"value\":\"_SL2236978295\"},{\"key\":\"sid\",\"value\":\"1623\"},{\"key\":\"S\",\"value\":\"7d501f33d25c4168a1bc9967d7a2dfb4\"},{\"key\":\"T\"},{\"key\":\"pos\",\"value\":\"CHINA\"},{\"key\":\"GE\",\"value\":\"1730194174923\"},{\"key\":\"sourceFrom\",\"value\":\"Online\"},{\"key\":\"corpName\",\"value\":\"p15123007198\"},{\"key\":\"RID\",\"value\":\"3e46784b7b4a42a982311f1ed6e047ec\"},{\"key\":\"TID\",\"value\":\"TID.HOTELBOOK.1623.E74FF6459F184DE69DFDD526B405ABDD\"},{\"key\":\"VID\",\"value\":\"1687762854054.17onzr\"}],\"ticket\":\"IqJyLFLcQJrSDuiEHJrwuHaQevJI+Q7OdPBgRaqYuQTSzVhk5Yw6xTK0K7HzXUx6VI6y74XCrMn+e6ZebEVYzovpPVjgTFSRO+rke+CBovk\\u003d\",\"transferInfo\":[{\"key\":\"iframe\",\"value\":\"F\"},{\"key\":\"channel\",\"value\":\"Online\"}]},\"hotelBookPassengerInputs\":[{\"hotelPassengerInput\":{\"uid\":\"_SL2236978295\",\"approvalPassengerId\":\"2454155933921633679\",\"employee\":\"T\",\"approvalInput\":{\"subApprovalNo\":\"差标模式不管控-管控出行人-成本中心\"},\"external\":\"F\",\"employeeId\":\"编XC295295123\"},\"passengerBasicInfo\":{\"gender\":\"U\",\"birth\":\"2002-01-01\"},\"name\":\"彭彭一\",\"isRegistrant\":\"F\",\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"13509877890\",\"transferPhoneNo\":\"13509877890\"},\"nationalityInfo\":{\"nationalityCode\":\"CN\"}}],\"hotelInvoiceInfos\":[],\"cityInput\":{\"cityId\":2},\"hotelContactorInfo\":{\"name\":\"peng/PENGYI\",\"phoneInfo\":{\"countryCode\":\"81\",\"phoneNo\":\"13509877890\",\"transferPhoneNo\":\"13509877890\"},\"emailInfo\":{\"email\":\"<EMAIL>\",\"transferEmail\":\"<EMAIL>\"}},\"approvalInput\":{\"masterApprovalNo\":\"审批单带1人\",\"subApprovalNo\":\"审批单带1人\",\"emergency\":\"F\"},\"miceInput\":{},\"hotelBookInput\":{\"hotelDateRangeInfo\":{\"checkIn\":\"2024-10-30\",\"checkOut\":\"2024-10-31\"},\"roomQuantity\":1},\"bookModeInfo\":{\"bookingType\":\"NONE\"},\"resourceTokenInfo\":{\"resourceToken\":\"{\\\"hotelResourceToken\\\":{\\\"masterHotelId\\\":374447,\\\"hotelId\\\":1619108,\\\"searchHotelTraceId\\\":\\\"776a0f921f8a405fa9d1bb67414dceb6\\\",\\\"hid\\\":\\\"ASIGMzM1LjAwQgFBSgYIABAAGABSaQpjdjFfQ0svdEZoQ2s2V0lhQVZNaUF6TXpOU29ETXpNMU1nMEtBME5PV1JJRFEwNVpHZ0V4T2lBM056WmhNR1k1TWpGbU9HRTBNRFZtWVRsa01XSmlOamMwTVRSa1kyVmlOZz09EAAYAVoDQ05ZYg0KA0NOWRIDQ05ZGgExYg0KA0NOWRIDQ05ZGgExaANyBjMwMC4wMHoEMC4wMIIBIDc3NmEwZjkyMWY4YTQwNWZhOWQxYmI2NzQxNGRjZWI2igEKMjE0NzQ4MzY5OZoBJDRjN2VkMzZiLTIyNjktNDRhOS1hMmQ1LWY1OWMyN2I0OThlMqIBAggA\\\",\\\"hotelType\\\":\\\"C\\\",\\\"bonusPoint\\\":\\\"F\\\",\\\"hotelGeoInfoResourceToken\\\":{\\\"cityId\\\":2,\\\"locationId\\\":112}},\\\"roomResourceToken\\\":{\\\"roomId\\\":2147483699,\\\"baseRoomId\\\":517573,\\\"ratePlanTraceLogId\\\":\\\"dcfd97ed-1650-4c8d-b762-103ec66f0d1f\\\",\\\"allRoomType\\\":\\\"EXIST_C\\\",\\\"bedName\\\":\\\"\\\",\\\"windowName\\\":\\\"无窗\\\",\\\"breakfastInfo\\\":\\\"2份早餐\\\",\\\"roomType\\\":\\\"C\\\",\\\"balanceType\\\":\\\"PP\\\",\\\"tmcPrice\\\":false,\\\"adult\\\":2,\\\"pId\\\":\\\"AQoGMzM1LjAwEgYzMzUuMDAicQpvdjFfQ0tUcFloQ3pnSUNBQ0JvRWJuVnNiQ0lBS2dGVE1nTXpNelU2QXpNek5VSU5DZ05EVGxrU0EwTk9XUm9CTVVvZ056YzJZVEJtT1RJeFpqaGhOREExWm1FNVpERmlZalkzTkRFMFpHTmxZalk9KgNDTlkyDQoDQ05ZEgNDTlkaATEyDQoDQ05ZEgNDTlkaATE6AggAQgcI6A8QChgeSgcI6A8QChgfUAFYAGIETk9ORWgCeAKAAaTpYg\\\\u003d\\\\u003d\\\",\\\"amadeus\\\":false,\\\"supportAccommodation\\\":\\\"F\\\",\\\"unKnowRoomDesc\\\":\\\"\\\",\\\"roomOriginCurrency\\\":\\\"CNY\\\",\\\"roomScenarioEnum\\\":\\\"ROOM_LIST\\\",\\\"ratePlanRequestToken\\\":\\\"ASi1L/0AUM0VAEZvpz8gDeUBAIzCRKCog8DgCYAhQA0aCG0gkW1LsiTLuvBgYBIayqxOElvWPi2pMN62TS/j/6Eel4/DmscoVs1nKQWYAJUAlACcTBV6CSjTYVdBy7zQIGNkY3v/fXr9pPTNKRqiv4e8/CVDYr3SN+k0o5N6r0d3EjUTCem0NNE+UxTOKRwEqZvN+Ewk83g5lop+KUQ48ACKEbkDzhxGzynyBp5zhpwvZFRudciCYFgaA7usq+Hp/rP4LwCkAJDgOQZIFQAC1+nNsiCXznRL4Mq6i9s6DXJdFk1Z4IHFCQ8HAu/nZVyoSNSxqtHHNKPfhJfRKcy4GLZyrg+xChW/MTM6dOy6xVHDk/S6d2/IraNDJ1GchC538d4LbqlnomSTdVWpnIsVtcSM63zat4O6ftsvEWVqyHx82PmSPh3yOLmnNtoXFRT1AJlIIZWnoJsKkUSUtnCCMr9BVNcij8rxkcmEkf6mSj2PxdH0LxGNnxPaMItESiz4V2ktMeDn5aY167oqHJHcsMirCP1ESVo7SdOr2/Q+T93OiWPHtzJxK3QIiRlpWFKquhN+d9Ez462bYc+Oqc2Mn0fBD6fSkJP+Gyj0uSTZPjOyFjXF/vhWimXCuRNX3aSyrWlEnG9rxQ3Y9fBDvulKYpgK5IiTC8z3I8bSPplpFTJzParUhO2zTijXRbVlzHwac5YkYzibwDkEziBw/oAzB5w14IwB5wsYhVsMwhSwiDF4gjlsYQdYAT6AB1y5BM4kdteAfcIvv8gCx86NBnnFP8m3lBiQg2XYvQHHzg0EG/Bryi9CwBHJjYq8uvwPlp7vl8vnHSy/SIXDMz8sb1G95NntiOVmqQOaaadk4YjkBoO8KonMFR2ZLaqfnU3LblaWHSVw7NxYkFfpJwgAVihA1EDIBmAnzLcb98ywsEhf9h2Hogc\\\\u003d\\\",\\\"ratePlanRoomToken\\\":\\\"ASi1L/0AUHkAAAoBQxAAGLOAgIAIIgJQUA\\\\u003d\\\\u003d\\\",\\\"roomUniqueKey\\\":\\\"V1-ASi1L/0AUJkAAAizgICACBIBQxoETk9ORSICUFA\\\\u003d\\\",\\\"adultNumber\\\":2,\\\"windowType\\\":0},\\\"reservationResourceToken\\\":{\\\"wsId\\\":\\\"51ed8f76-28f1-4e1b-a03e-7960636ced20\\\",\\\"policyToken\\\":\\\"v1KLUv/QBQNQoAlhRCJCCJqgBn81YG0HY1ofdM/ilwszzD3DSly5BbxSBtuTAGAQSBgjcAOQA4ANS+HYqYZ1kaxzgM9ndLwiqFb3ciBz0RonGOQSn83Yr3vbKlJnORMTPj9bYsANCheW8Y65TxdYYZF0x44mqzgI5Idrz3ozJk0TBKalNtXQlnfPvTQA4HOYvDeRAK5FSQ0lV1UFwVQcWxo+kKIv4+eyGz56VXwvbDugopo0KyhFYM4zcp73y3kciYwCjpZOM0uCglsJG+j7e+1b5DoOWGpQOzIQcLlComww0Jd+7tjHv74t4eMUd4ly3hWjAlS7LwWrRi2UxkLe2UIhSTkQVsvKpLTYYTnrLYSHSdWlYcFgA5G2djh7O1w31z8dRiNIYQpuagoQqpC5giqEOVqlksoSrPZ3EcqHKvYo8waqkCODeAlNeZg61sigE\\\\u003d\\\"},\\\"bookInitResourceToken\\\":{\\\"serviceChargeResourceToken\\\":{\\\"serviceChargeAmount\\\":15.00,\\\"serviceFeeByRoomNight\\\":15.00,\\\"serviceFeeItemResourceTokens\\\":[{\\\"chargeItemCode\\\":\\\"BookingOrderSC\\\",\\\"originalAmount\\\":15.00,\\\"settlementAmount\\\":15.00}]},\\\"aggBookPriceResourceToken\\\":{\\\"accountPaymentAmount\\\":0,\\\"individualPaymentAmount\\\":350.00,\\\"mixPayAccountExtraRatio\\\":0,\\\"mixPayAccountExtraAmount\\\":0},\\\"roomPriceResourceToken\\\":{}}}\"},\"corpPayInfo\":{\"corpPayType\":\"public\"},\"userActionInfo\":{\"userActionToken\":\"def\"},\"approvalFlowInput\":{\"password\":\"\",\"approvalFlowToken\":\"H4sIAAAAAAAAAONSMU8zMk1KTUnRNTA0SNQ1MTNP1k00tzDXNbAwMU82MrZMMklLFGI0lFJwcjZyNXQ2NnIzsXQ2MzExNnJxdXZycQZhF2dTQ0OFPQsbnq3dxAAAz6-8c1MAAAA\",\"approvers\":[{\"uid\":\"_SL2236978295\",\"level\":\"1\"}]},\"rcInfos\":[],\"followApprovalInfoInput\":{\"aiFollow\":\"F\",\"artificialFollow\":\"F\",\"followTripId\":\"\",\"followSelected\":\"F\"},\"bookUrlInfos\":[{\"urlType\":\"SUCCESS\"},{\"urlType\":\"S_BACK\"},{\"urlType\":\"E_BACK\"},{\"urlType\":\"FROM_URL\"}],\"paymentInfoInput\":{\"canWechatPayByOthers\":\"F\"},\"clientInfo\":{},\"offlineInfo\":{\"forciblyAssure\":\"F\",\"especialAuth\":\"F\",\"autoConfirm\":\"F\",\"exigentOrder\":\"F\",\"operationRule\":\"\"},\"roomInfoInput\":{\"roomRemark\":\"\",\"roomCustomRemark\":\"\"},\"sendInfo\":{\"sendSMS\":\"F\",\"sendEmail\":\"F\"},\"ssoInput\":{\"ssoKey\":\"21f4bf20-8f9d-11ef-8a71-c1975ddf3d64\"},\"customBedInput\":{},\"flashStayInput\":{\"flashPlatFrom\":\"Online\"},\"hotelPayTypeInput\":[{\"payCode\":\"ROOM\",\"payType\":\"SELF_PAY\"}],\"strategyInfos\":[{\"strategyKey\":\"HOTEL_LIST\",\"strategyValue\":\"T\"}],\"costCenterInfo\":{\"costCenterJsonString\":\"{\\\"items\\\":{\\\"fdefault\\\":{},\\\"_SL2236978295\\\":{\\\"name\\\":\\\"彭彭一\\\",\\\"cost1\\\":\\\"彭一成本中心内容1\\\",\\\"cost2\\\":\\\"编号123456编号123456\\\",\\\"cost3\\\":\\\"彭一成本中心内容3\\\",\\\"cost4\\\":\\\"彭一成本中心内容4\\\",\\\"cost5\\\":\\\"彭一成本中心内容5\\\",\\\"cost6\\\":\\\"彭一成本中心内容6\\\"}}}\"},\"orderCreateToken\":\"H4sIAAAAAAAAAG3PMQ7CMAwFUOYMCCEmTsBoJ3Fsj1VpUaWoqQKCMfe/BZ3jDl6e/PVtdx7LOudl/LRSn1N1t/eyvvLUhm2r5TvkNufyu1/dBTF4SklZEf0+vQWK1BkoYTR7SunA5MC8MVEwHRH6DmDmPrvfomotBWMsfQd6ArMnPrLJJul/AxXQx+kPbWOPPWgBAAA\\u003d\",\"useNewOrderCreate\":\"T\"}", OrderCreateRequestType.class)
        when:
        CreateOrderResult createOrderResult = OrderCreateProcessorOfUtil.buildCreateOrderResultByCreateOrder(createOrderResponseType, orderCreateRequestType)
        then:
        createOrderResult != null
    }

    def "buildCreateOrderResult"() {
        given:
        CreateOrderResponseType createOrderResponseType = JsonUtil.fromJson("{\"wsId\":\"51ed8f76-28f1-4e1b-a03e-7960636ced20\",\"orderId\":1132566979114233,\"orderPaymentInfo\":{\"payType\":\"SELF_PAY\",\"orderAmountInfo\":{\"cnyTotalAmount\":335.00,\"personalPayAmountInfo\":{\"amount\":350.00,\"currency\":\"CNY\",\"exchangeRateToCNY\":1},\"paymentItemList\":[{\"prepayType\":\"SELF_PAY\",\"gatheringType\":\"D\",\"feeType\":\"HotelRoomFee\",\"amountInfo\":{\"amount\":335.00,\"currency\":\"CNY\",\"exchangeRateToCNY\":1}},{\"prepayType\":\"SELF_PAY\",\"gatheringType\":\"D\",\"feeType\":\"BookServiceFee\",\"amountInfo\":{\"amount\":15.00,\"currency\":\"CNY\",\"exchangeRateToCNY\":1}}]}},\"success\":true,\"supportPaymentBill\":true,\"paymentTransactionId\":\"296716091645313176\",\"orderTotalAmount\":{\"orderAmount\":{\"originAmount\":{\"amount\":335.00,\"currency\":\"CNY\",\"exchange\":1},\"cNYAmount\":335.00},\"paymentBillInfo\":{\"payAmount\":350.00,\"payCNYAmount\":350.00,\"payCurrency\":\"CNY\",\"payExchange\":1}},\"transactionInfo\":{\"transactionId\":\"454302d5-5c56-4dd0-a8eb-3122660e4e9b\",\"subTransactionIdList\":[{\"payType\":\"PERSONAL\",\"subTransactionId\":\"545341af-516f-4f13-b9d0-742bb2a28d5f\"}]},\"responseCode\":20000,\"responseStatus\":{\"timestamp\":\"2024-10-29 17:29:41.117+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", CreateOrderResponseType.class)
        OrderCreateRequestType orderCreateRequestType = JsonUtil.fromJson("{\"integrationSoaRequestType\":{\"userInfo\":{\"userId\":\"_SL2236978295\",\"corpId\":\"p15123007198\",\"groupId\":\"Gr_00007155\",\"pos\":\"CHINA\"},\"language\":\"zh-CN\",\"requestId\":\"3e46784b7b4a42a982311f1ed6e047ec\",\"sourceFrom\":\"Online\",\"transactionID\":\"TID.HOTELBOOK.1623.E74FF6459F184DE69DFDD526B405ABDD\",\"gatewayHost\":\"ct.ctrip.fat12024.qa.nt.ctripcorp.com\",\"timezoneOffsetMinutesNew\":\"480\",\"logIndices\":[{\"key\":\"groupId\",\"value\":\"Gr_00007155\"},{\"key\":\"timezoneOffsetMinutesNew\",\"value\":\"480\"},{\"key\":\"gatewayHost\",\"value\":\"ct.ctrip.fat12024.qa.nt.ctripcorp.com\"},{\"key\":\"pvid\",\"value\":\"204\"},{\"key\":\"subChannel\",\"value\":\"\"},{\"key\":\"CID\",\"value\":\"09031120410632660564\"},{\"key\":\"EID\"},{\"key\":\"CIP\",\"value\":\"*************\"},{\"key\":\"miceToken\",\"value\":\"\"},{\"key\":\"groupName\",\"value\":\"ph测试集团1\"},{\"key\":\"gatewayIdc\",\"value\":\"NTGXH\"},{\"key\":\"traceID\",\"value\":\"\"},{\"key\":\"A\",\"value\":\"ordercheck\"},{\"key\":\"CorpID\",\"value\":\"p15123007198\"},{\"key\":\"C\",\"value\":\"corpbookinghotelbookservice\"},{\"key\":\"M\",\"value\":\"_SL2236978295\"},{\"key\":\"sid\",\"value\":\"1623\"},{\"key\":\"S\",\"value\":\"7d501f33d25c4168a1bc9967d7a2dfb4\"},{\"key\":\"T\"},{\"key\":\"pos\",\"value\":\"CHINA\"},{\"key\":\"GE\",\"value\":\"1730194174923\"},{\"key\":\"sourceFrom\",\"value\":\"Online\"},{\"key\":\"corpName\",\"value\":\"p15123007198\"},{\"key\":\"RID\",\"value\":\"3e46784b7b4a42a982311f1ed6e047ec\"},{\"key\":\"TID\",\"value\":\"TID.HOTELBOOK.1623.E74FF6459F184DE69DFDD526B405ABDD\"},{\"key\":\"VID\",\"value\":\"1687762854054.17onzr\"}],\"ticket\":\"IqJyLFLcQJrSDuiEHJrwuHaQevJI+Q7OdPBgRaqYuQTSzVhk5Yw6xTK0K7HzXUx6VI6y74XCrMn+e6ZebEVYzovpPVjgTFSRO+rke+CBovk\\u003d\",\"transferInfo\":[{\"key\":\"iframe\",\"value\":\"F\"},{\"key\":\"channel\",\"value\":\"Online\"}]},\"hotelBookPassengerInputs\":[{\"hotelPassengerInput\":{\"uid\":\"_SL2236978295\",\"approvalPassengerId\":\"2454155933921633679\",\"employee\":\"T\",\"approvalInput\":{\"subApprovalNo\":\"差标模式不管控-管控出行人-成本中心\"},\"external\":\"F\",\"employeeId\":\"编XC295295123\"},\"passengerBasicInfo\":{\"gender\":\"U\",\"birth\":\"2002-01-01\"},\"name\":\"彭彭一\",\"isRegistrant\":\"F\",\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"13509877890\",\"transferPhoneNo\":\"13509877890\"},\"nationalityInfo\":{\"nationalityCode\":\"CN\"}}],\"hotelInvoiceInfos\":[],\"cityInput\":{\"cityId\":2},\"hotelContactorInfo\":{\"name\":\"peng/PENGYI\",\"phoneInfo\":{\"countryCode\":\"81\",\"phoneNo\":\"13509877890\",\"transferPhoneNo\":\"13509877890\"},\"emailInfo\":{\"email\":\"<EMAIL>\",\"transferEmail\":\"<EMAIL>\"}},\"approvalInput\":{\"masterApprovalNo\":\"审批单带1人\",\"subApprovalNo\":\"审批单带1人\",\"emergency\":\"F\"},\"miceInput\":{},\"hotelBookInput\":{\"hotelDateRangeInfo\":{\"checkIn\":\"2024-10-30\",\"checkOut\":\"2024-10-31\"},\"roomQuantity\":1},\"bookModeInfo\":{\"bookingType\":\"NONE\"},\"resourceTokenInfo\":{\"resourceToken\":\"{\\\"hotelResourceToken\\\":{\\\"masterHotelId\\\":374447,\\\"hotelId\\\":1619108,\\\"searchHotelTraceId\\\":\\\"776a0f921f8a405fa9d1bb67414dceb6\\\",\\\"hid\\\":\\\"ASIGMzM1LjAwQgFBSgYIABAAGABSaQpjdjFfQ0svdEZoQ2s2V0lhQVZNaUF6TXpOU29ETXpNMU1nMEtBME5PV1JJRFEwNVpHZ0V4T2lBM056WmhNR1k1TWpGbU9HRTBNRFZtWVRsa01XSmlOamMwTVRSa1kyVmlOZz09EAAYAVoDQ05ZYg0KA0NOWRIDQ05ZGgExYg0KA0NOWRIDQ05ZGgExaANyBjMwMC4wMHoEMC4wMIIBIDc3NmEwZjkyMWY4YTQwNWZhOWQxYmI2NzQxNGRjZWI2igEKMjE0NzQ4MzY5OZoBJDRjN2VkMzZiLTIyNjktNDRhOS1hMmQ1LWY1OWMyN2I0OThlMqIBAggA\\\",\\\"hotelType\\\":\\\"C\\\",\\\"bonusPoint\\\":\\\"F\\\",\\\"hotelGeoInfoResourceToken\\\":{\\\"cityId\\\":2,\\\"locationId\\\":112}},\\\"roomResourceToken\\\":{\\\"roomId\\\":2147483699,\\\"baseRoomId\\\":517573,\\\"ratePlanTraceLogId\\\":\\\"dcfd97ed-1650-4c8d-b762-103ec66f0d1f\\\",\\\"allRoomType\\\":\\\"EXIST_C\\\",\\\"bedName\\\":\\\"\\\",\\\"windowName\\\":\\\"无窗\\\",\\\"breakfastInfo\\\":\\\"2份早餐\\\",\\\"roomType\\\":\\\"C\\\",\\\"balanceType\\\":\\\"PP\\\",\\\"tmcPrice\\\":false,\\\"adult\\\":2,\\\"pId\\\":\\\"AQoGMzM1LjAwEgYzMzUuMDAicQpvdjFfQ0tUcFloQ3pnSUNBQ0JvRWJuVnNiQ0lBS2dGVE1nTXpNelU2QXpNek5VSU5DZ05EVGxrU0EwTk9XUm9CTVVvZ056YzJZVEJtT1RJeFpqaGhOREExWm1FNVpERmlZalkzTkRFMFpHTmxZalk9KgNDTlkyDQoDQ05ZEgNDTlkaATEyDQoDQ05ZEgNDTlkaATE6AggAQgcI6A8QChgeSgcI6A8QChgfUAFYAGIETk9ORWgCeAKAAaTpYg\\\\u003d\\\\u003d\\\",\\\"amadeus\\\":false,\\\"supportAccommodation\\\":\\\"F\\\",\\\"unKnowRoomDesc\\\":\\\"\\\",\\\"roomOriginCurrency\\\":\\\"CNY\\\",\\\"roomScenarioEnum\\\":\\\"ROOM_LIST\\\",\\\"ratePlanRequestToken\\\":\\\"ASi1L/0AUM0VAEZvpz8gDeUBAIzCRKCog8DgCYAhQA0aCG0gkW1LsiTLuvBgYBIayqxOElvWPi2pMN62TS/j/6Eel4/DmscoVs1nKQWYAJUAlACcTBV6CSjTYVdBy7zQIGNkY3v/fXr9pPTNKRqiv4e8/CVDYr3SN+k0o5N6r0d3EjUTCem0NNE+UxTOKRwEqZvN+Ewk83g5lop+KUQ48ACKEbkDzhxGzynyBp5zhpwvZFRudciCYFgaA7usq+Hp/rP4LwCkAJDgOQZIFQAC1+nNsiCXznRL4Mq6i9s6DXJdFk1Z4IHFCQ8HAu/nZVyoSNSxqtHHNKPfhJfRKcy4GLZyrg+xChW/MTM6dOy6xVHDk/S6d2/IraNDJ1GchC538d4LbqlnomSTdVWpnIsVtcSM63zat4O6ftsvEWVqyHx82PmSPh3yOLmnNtoXFRT1AJlIIZWnoJsKkUSUtnCCMr9BVNcij8rxkcmEkf6mSj2PxdH0LxGNnxPaMItESiz4V2ktMeDn5aY167oqHJHcsMirCP1ESVo7SdOr2/Q+T93OiWPHtzJxK3QIiRlpWFKquhN+d9Ez462bYc+Oqc2Mn0fBD6fSkJP+Gyj0uSTZPjOyFjXF/vhWimXCuRNX3aSyrWlEnG9rxQ3Y9fBDvulKYpgK5IiTC8z3I8bSPplpFTJzParUhO2zTijXRbVlzHwac5YkYzibwDkEziBw/oAzB5w14IwB5wsYhVsMwhSwiDF4gjlsYQdYAT6AB1y5BM4kdteAfcIvv8gCx86NBnnFP8m3lBiQg2XYvQHHzg0EG/Bryi9CwBHJjYq8uvwPlp7vl8vnHSy/SIXDMz8sb1G95NntiOVmqQOaaadk4YjkBoO8KonMFR2ZLaqfnU3LblaWHSVw7NxYkFfpJwgAVihA1EDIBmAnzLcb98ywsEhf9h2Hogc\\\\u003d\\\",\\\"ratePlanRoomToken\\\":\\\"ASi1L/0AUHkAAAoBQxAAGLOAgIAIIgJQUA\\\\u003d\\\\u003d\\\",\\\"roomUniqueKey\\\":\\\"V1-ASi1L/0AUJkAAAizgICACBIBQxoETk9ORSICUFA\\\\u003d\\\",\\\"adultNumber\\\":2,\\\"windowType\\\":0},\\\"reservationResourceToken\\\":{\\\"wsId\\\":\\\"51ed8f76-28f1-4e1b-a03e-7960636ced20\\\",\\\"policyToken\\\":\\\"v1KLUv/QBQNQoAlhRCJCCJqgBn81YG0HY1ofdM/ilwszzD3DSly5BbxSBtuTAGAQSBgjcAOQA4ANS+HYqYZ1kaxzgM9ndLwiqFb3ciBz0RonGOQSn83Yr3vbKlJnORMTPj9bYsANCheW8Y65TxdYYZF0x44mqzgI5Idrz3ozJk0TBKalNtXQlnfPvTQA4HOYvDeRAK5FSQ0lV1UFwVQcWxo+kKIv4+eyGz56VXwvbDugopo0KyhFYM4zcp73y3kciYwCjpZOM0uCglsJG+j7e+1b5DoOWGpQOzIQcLlComww0Jd+7tjHv74t4eMUd4ly3hWjAlS7LwWrRi2UxkLe2UIhSTkQVsvKpLTYYTnrLYSHSdWlYcFgA5G2djh7O1w31z8dRiNIYQpuagoQqpC5giqEOVqlksoSrPZ3EcqHKvYo8waqkCODeAlNeZg61sigE\\\\u003d\\\"},\\\"bookInitResourceToken\\\":{\\\"serviceChargeResourceToken\\\":{\\\"serviceChargeAmount\\\":15.00,\\\"serviceFeeByRoomNight\\\":15.00,\\\"serviceFeeItemResourceTokens\\\":[{\\\"chargeItemCode\\\":\\\"BookingOrderSC\\\",\\\"originalAmount\\\":15.00,\\\"settlementAmount\\\":15.00}]},\\\"aggBookPriceResourceToken\\\":{\\\"accountPaymentAmount\\\":0,\\\"individualPaymentAmount\\\":350.00,\\\"mixPayAccountExtraRatio\\\":0,\\\"mixPayAccountExtraAmount\\\":0},\\\"roomPriceResourceToken\\\":{}}}\"},\"corpPayInfo\":{\"corpPayType\":\"public\"},\"userActionInfo\":{\"userActionToken\":\"def\"},\"approvalFlowInput\":{\"password\":\"\",\"approvalFlowToken\":\"H4sIAAAAAAAAAONSMU8zMk1KTUnRNTA0SNQ1MTNP1k00tzDXNbAwMU82MrZMMklLFGI0lFJwcjZyNXQ2NnIzsXQ2MzExNnJxdXZycQZhF2dTQ0OFPQsbnq3dxAAAz6-8c1MAAAA\",\"approvers\":[{\"uid\":\"_SL2236978295\",\"level\":\"1\"}]},\"rcInfos\":[],\"followApprovalInfoInput\":{\"aiFollow\":\"F\",\"artificialFollow\":\"F\",\"followTripId\":\"\",\"followSelected\":\"F\"},\"bookUrlInfos\":[{\"urlType\":\"SUCCESS\"},{\"urlType\":\"S_BACK\"},{\"urlType\":\"E_BACK\"},{\"urlType\":\"FROM_URL\"}],\"paymentInfoInput\":{\"canWechatPayByOthers\":\"F\"},\"clientInfo\":{},\"offlineInfo\":{\"forciblyAssure\":\"F\",\"especialAuth\":\"F\",\"autoConfirm\":\"F\",\"exigentOrder\":\"F\",\"operationRule\":\"\"},\"roomInfoInput\":{\"roomRemark\":\"\",\"roomCustomRemark\":\"\"},\"sendInfo\":{\"sendSMS\":\"F\",\"sendEmail\":\"F\"},\"ssoInput\":{\"ssoKey\":\"21f4bf20-8f9d-11ef-8a71-c1975ddf3d64\"},\"customBedInput\":{},\"flashStayInput\":{\"flashPlatFrom\":\"Online\"},\"hotelPayTypeInput\":[{\"payCode\":\"ROOM\",\"payType\":\"SELF_PAY\"}],\"strategyInfos\":[{\"strategyKey\":\"HOTEL_LIST\",\"strategyValue\":\"T\"}],\"costCenterInfo\":{\"costCenterJsonString\":\"{\\\"items\\\":{\\\"fdefault\\\":{},\\\"_SL2236978295\\\":{\\\"name\\\":\\\"彭彭一\\\",\\\"cost1\\\":\\\"彭一成本中心内容1\\\",\\\"cost2\\\":\\\"编号123456编号123456\\\",\\\"cost3\\\":\\\"彭一成本中心内容3\\\",\\\"cost4\\\":\\\"彭一成本中心内容4\\\",\\\"cost5\\\":\\\"彭一成本中心内容5\\\",\\\"cost6\\\":\\\"彭一成本中心内容6\\\"}}}\"},\"orderCreateToken\":\"H4sIAAAAAAAAAG3PMQ7CMAwFUOYMCCEmTsBoJ3Fsj1VpUaWoqQKCMfe/BZ3jDl6e/PVtdx7LOudl/LRSn1N1t/eyvvLUhm2r5TvkNufyu1/dBTF4SklZEf0+vQWK1BkoYTR7SunA5MC8MVEwHRH6DmDmPrvfomotBWMsfQd6ArMnPrLJJul/AxXQx+kPbWOPPWgBAAA\\u003d\",\"useNewOrderCreate\":\"T\"}", OrderCreateRequestType.class)
        OrderCreateToken orderCreateToken = TokenParseUtil.parseToken("H4sIAAAAAAAAAG3PMQ7CMAwFUOYMCCEmTsBoJ3Fsj1VpUaWoqQKCMfe/BZ3jDl6e/PVtdx7LOudl/LRSn1N1t/eyvvLUhm2r5TvkNufyu1/dBTF4SklZEf0+vQWK1BkoYTR7SunA5MC8MVEwHRH6DmDmPrvfomotBWMsfQd6ArMnPrLJJul/AxXQx+kPbWOPPWgBAAA=", OrderCreateToken.class)
        when:
        CreateOrderResult createOrderResult = OrderCreateProcessorOfUtil.buildCreateOrderResult(createOrderResponseType,
                orderCreateRequestType, orderCreateToken)
        then:
        createOrderResult != null
    }


    def "test getApprovalInfoType"() {
        given:
        ApprovalFlowComputeResponseType approvalFlowComputeResponseType = new ApprovalFlowComputeResponseType(
                "approvalAllDone": "T"
        )
        MatchApprovalFlowResponseType matchApprovalFlowResponseType = new MatchApprovalFlowResponseType(
                "flowTmplInfo": new FlowTmpl(
                        "externalId": "externalId"
                )
        )
        ResourceToken resourceToken = new ResourceToken(
                "orderCheckResourceToken": new OrderCheckResourceToken(
                        "followAuthPass": true
                )
        )
        OrderCreateToken orderCreateToken = new OrderCreateToken(
                approvalInfoResult: new ApprovalInfoResult(
                        "externalId": 1234
                ),
        )

        ApprovalFlowInfoEntity approvalFlowInfoEntity = new ApprovalFlowInfoEntity(
                "externalId": "extId from token",
                "expireTime": 9999999999999L,
        )
        def token = ProtobufSerializerUtil.pbSerialize(approvalFlowInfoEntity, ApprovalFlowInfoEntity.class)
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                "corpPayInfo": new CorpPayInfo(
                        "corpPayType": "public"
                ),
                "followApprovalInfoInput": new FollowApprovalInfoInput(
                        "followOrderId": "1234"
                ),
                "approvalFlowInput": new ApprovalFlowInput(
                        "approvalFlowToken": token,
                        "password": "password",
                        "approvers": [
                                new Approver(
                                        "uid": "uid1",
                                        "level": "0"
                                ),
                                new Approver(
                                        "uid": "uid2",
                                        "level": "2"
                                )
                        ]
                )
        )
        WrapperOfAccount.AccountInfo accountInfoPage = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("BillType", "E")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>()))
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();


        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("BillType", "A")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>()))
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();

        when:
        def result = OrderCreateProcessorOfUtil.buildApprovalInfoType(null,
                matchApprovalFlowResponseType, orderCreateToken, new OrderCreateRequestType(), accountInfoPage);

        then:
        result == null


        when:
        orderCreateRequestType.getCorpPayInfo().setCorpPayType("private")
        result = OrderCreateProcessorOfUtil.buildApprovalInfoType(approvalFlowComputeResponseType,
                matchApprovalFlowResponseType, orderCreateToken, orderCreateRequestType, accountInfo);
        then:
        result.getApprovalExtensionOrderId() == null


        when:
        resourceToken.getOrderCheckResourceToken().setFollowAuthPass(false)
        result = OrderCreateProcessorOfUtil.buildApprovalInfoType(approvalFlowComputeResponseType,
                matchApprovalFlowResponseType, orderCreateToken, orderCreateRequestType, accountInfo);

        then:
        result.getExternalId() == "externalId"
        result.getApprovalExtensionOrderId() == null


        when:
        approvalFlowComputeResponseType.setApprovalAllDone("F")
        result = OrderCreateProcessorOfUtil.buildApprovalInfoType(approvalFlowComputeResponseType,
                matchApprovalFlowResponseType, orderCreateToken, orderCreateRequestType, accountInfo);

        then:
        result.getExternalId() == "extId from token"
        result.getApprovalExtensionOrderId() == null
        result.getApprovalPassWord() == "password"
        result.getUserList().get(0).getUid() == "uid1"
        result.getUserList().get(0).getLevel() == 1
        result.getUserList().get(1).getUid() == "uid2"
        result.getUserList().get(1).getLevel() == 2
    }

    @Unroll
    def "testBuildFollowOrderNo with #description"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(integrationSoaRequestType:
                new IntegrationSoaRequestType(sourceFrom: sourceFrom), followApprovalInfoInput: new FollowApprovalInfoInput(followSelected: followSelected, followOrderId: followOrderId, artificialFollow: artificialFollow))
        OrderCreateToken orderCreateToken = new OrderCreateToken(followApprovalResult: followApprovalResult)

        when:
        Long result = OrderCreateProcessorOfUtil.buildFollowOrderNo(orderCreateRequestType, orderCreateToken)

        then:
        result == expectedResult

        where:
        description                  | sourceFrom         | followSelected | followOrderId | followApprovalResult                            | artificialFollow | expectedResult
        "followSelected is OPEN"     | SourceFrom.Online  | "T"            | "12345"       | new FollowApprovalResult(followOrderNo: "1234") | "F"              | 1234L
        "followSelected is not OPEN" | SourceFrom.Online  | "F"            | "5678"        | null                                            | "F"              | 0L
        "followSelected is OPEN"     | SourceFrom.Offline | "T"            | "12345"       | null                                            | "T"              | 12345L
    }

    @Unroll
    def "testBuildOfflineSingleArtificialFollow with #description"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: sourceFrom),
                followApprovalInfoInput: new FollowApprovalInfoInput(artificialFollow: artificialFollow, followOrderId: followOrderId)
        )
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("BillType", "A")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>()))
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();

        expect:
        OrderCreateProcessorOfUtil.buildOfflineSingleArtificialFollow(orderCreateRequestType, accountInfo) == expectedResult

        where:
        description                                | sourceFrom         | artificialFollow | followOrderId | packageEnabled | expectedResult
        "source is not offline"                    | SourceFrom.H5      | "T"              | "1234"        | "A"            | false
        "artificial follow is not OPEN"            | SourceFrom.Offline | "F"              | "1234"        | "A"            | false
        "follow order id is blank"                 | SourceFrom.Offline | "T"              | ""            | "A"            | false
        "all conditions met for artificial follow" | SourceFrom.Offline | "T"              | "1234"        | "A"            | true
    }

    @Unroll
    def "testRequireApprovalFlowMatch with #description"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                corpPayInfo: new CorpPayInfo(corpPayType: corpPayInfo),
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: corpId))
        )
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("BillType", "A")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>()))
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build()
        OrderCreateToken orderCreateToken = new OrderCreateToken(continueTypes: continueTypes)

        expect:
        OrderCreateProcessorOfUtil.requireApprovalFlowMatch(orderCreateRequestType, accountInfo, orderCreateToken, null, null, null) == expectedResult

        where:
        description                               | corpPayInfo | corpId | packageEnabled | continueTypes            || expectedResult
        "private corp pay info"                   | "private"   | "123"  | false          | []                       || true
        "private corp pay info"                   | "private"   | "123"  | false          | ["SINGLE_APPROVAL_FLOW"] || false
        "follow approval with continue type"      | "public"    | "123"  | false          | ["SINGLE_APPROVAL_FLOW"] || false
        "no follow approval and no continue type" | "public"    | "123"  | false          | []                       || true
    }


    @Unroll
    def "testBuildDistinguishReservationEnum with different scenarios"() {
        given:
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                if (corpId == "testclose") {
                    return false
                }
                return true
            }
        }
        CorpPayInfo corpPayInfo = new CorpPayInfo(corpPayType: isPrivate)
        UserInfo userInfo = new UserInfo(corpId: corpIdreq, userId: userId)
        IntegrationSoaRequestType integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: userInfo)
        List<HotelBookPassengerInput> hotelBookPassengerInputs = passengerInputs.collect {
            new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: it))
        }

        when: "calling buildDistinguishReservationEnum with specific parameters"
        def result = OrderCreateProcessorOfUtil.buildDistinguishReservationEnum(corpPayInfo, integrationSoaRequestType, hotelBookPassengerInputs)

        then: "the result should be as expected"
        result == expectedResult

        where:
        isPrivate | corpIdreq   | userId | passengerInputs || expectedResult
        "private" | "testclose" | "u1"   | ["u1"]          || DistinguishReservationEnum.NONE
        "public"  | "testopen"  | "u1"   | []              || DistinguishReservationEnum.NONE
        "public"  | "testopen"  | "u1"   | ["u1"]          || DistinguishReservationEnum.EMPLOYEE_TRAVEL
        "public"  | "testopen"  | "u1"   | ["u1", "u2"]    || DistinguishReservationEnum.OPTION
    }

    @Unroll
    def "testGetResponse with different scenarios"() {
        given:
        Tuple2<Boolean, OrderCreateResponseType> tuple2 = Tuple2.of(true, new OrderCreateResponseType())
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        orderCreateRequestType.cityInput = new CityInput()
        orderCreateRequestType.cityInput.cityId = 1
        GetCityBaseInfoResponseType getCityBaseInfoResponseType = new GetCityBaseInfoResponseType()
        getCityBaseInfoResponseType.cityBaseInfo = Arrays.asList(new CityBaseInfoEntity(cityId: 1, cityName: "Test City"))
        WrapperOfCityBaseInfo.CityBaseInfo cityBaseInfo = WrapperOfCityBaseInfo.builder()
                .getCityBaseInfoResponseType(getCityBaseInfoResponseType)
                .cityInput(orderCreateRequestType.getCityInput()).build()

        when: "calling getResponse with specific parameters"
        def result = OrderCreateProcessorOfUtil.getResponse(tuple2, cityBaseInfo)

        then:
        result.cityInfo != null


        when:
        Tuple2<Boolean, OrderCreateResponseType> tuple = Tuple2.of(true, null)
        def result2 = OrderCreateProcessorOfUtil.getResponse(tuple, cityBaseInfo)

        then:
        result2 == null
    }


    @Unroll
    def "testRequireSaveHabitPolicyUser with #description"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                hotelPolicyInput: new HotelPolicyInput(policyInput: new PolicyInput(policyUid: policyUid)),
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: userId)),
                corpPayInfo: new CorpPayInfo(corpPayType: "public")
        )
        /* WrapperOfAccount.AccountInfo accountInfo = new WrapperOfAccount.AccountInfo()
        accountInfo.isPackageEnabled() >> true*/
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("BillType", "E")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        OrderCreateToken orderCreateToken = new OrderCreateToken()

        when:
        boolean result = OrderCreateProcessorOfUtil.requireSaveHabitPolicyUser(orderCreateRequestType, accountInfo, orderCreateToken)

        then:
        result == expectedResult

        where:
        description | userId | policyUid | expectedResult
        "need"      | "9890" | "1234"    | true
    }


    @Unroll
    def "testRequireSyncTrip with different scenarios"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                corpPayInfo: corpPayInfo,
                cityInput: new CityInput(cityId: 2),
                approvalInput: new ApprovalInput(masterApprovalNo: "masterApprovalNo", subApprovalNo: "subApprovalNo"))
        CreateTripResponseType createTripResponseType = new CreateTripResponseType(tripId: tripId)
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("BillType", "E")
                        put("BillControlMode", "A")
                        put("isChkaheadapproveHotelI", "T")
                        put("isChkaheadapproveHotel", "T")
                    }
                }))
                .policyAccountInfo(null)
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build()
        when:
        boolean result = OrderCreateProcessorOfUtil.requireSyncTrip(orderCreateRequestType, accountInfo, createTripResponseType)

        then:
        result == expectedResult

        where:
        corpPayInfo                             | packageEnabled | tripId || expectedResult
        new CorpPayInfo(corpPayType: "public")  | true           | 123L   || true
        new CorpPayInfo(corpPayType: "private") | true           | 123L   || false
    }

    def "testBuildApprovalInputs with different scenarios"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                hotelBookPassengerInputs: hotelBookPassengerInputs
        )
        CheckTravelPolicyResponseType checkTravelPolicyResponseType = new CheckTravelPolicyResponseType(
                approvalBillControlDimension: approvalBillControlDimension
        )

        when:
        List<ApprovalInput> result = OrderCreateProcessorOfUtil.buildApprovalInputs(orderCreateRequestType, checkTravelPolicyResponseType)

        then:
        result.size() == expectedSize

        where:
        hotelBookPassengerInputs                                                                                                                                                                                                                                               | approvalBillControlDimension || expectedSize
        [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(approvalInput: new ApprovalInput(subApprovalNo: "sub"))), new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(approvalInput: new ApprovalInput(subApprovalNo: "sub")))] | "P"                          || 2
        [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(approvalInput: new ApprovalInput(subApprovalNo: "sub")))]                                                                                                                                    | "O"                          || 0
        []                                                                                                                                                                                                                                                                     | "P"                          || 0
        [new HotelBookPassengerInput()]                                                                                                                                                                                                                                        | null                         || 0
    }


    @Unroll
    def "testRequirePaymentOrderCreate with different scenarios"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                hotelPayTypeInput: Arrays.asList(hotelPayTypeInput)
        )
        ResourceToken resourceToken = new ResourceToken()
        OrderCreateToken orderCreateToken = new OrderCreateToken()

        when:
        boolean result = OrderCreateProcessorOfUtil.requirePaymentOrderCreate(orderCreateRequestType, resourceToken, orderCreateToken)

        then:
        result == expectedResult

        where:
        hotelPayTypeInput                                                 || expectedResult
        new HotelPayTypeInput(payType: "CORP_PAY", payCode: "ROOM")       || false
        new HotelPayTypeInput(payType: "SELF_PAY", payCode: "ROOM")       || true
        new HotelPayTypeInput(payType: "FLASH_STAY_PAY", payCode: "ROOM") || false
    }

    @Unroll
    def "testRequireQueryPaymentBill with different scenarios"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                hotelPayTypeInput: Arrays.asList(hotelPayTypeInput)
        )
        ResourceToken resourceToken = new ResourceToken()
        OrderCreateToken orderCreateToken = new OrderCreateToken()

        when:
        boolean result = OrderCreateProcessorOfUtil.requireQueryPaymentBill(orderCreateRequestType, resourceToken, orderCreateToken)

        then:
        result == expectedResult

        where:
        hotelPayTypeInput                                                             || expectedResult
        new HotelPayTypeInput(payType: "CORP_PAY", payCode: "ROOM")                   || false
        new HotelPayTypeInput(payType: "CORP_CREDIT_CARD_GUARANTEE", payCode: "ROOM") || false
        new HotelPayTypeInput(payType: "SELF_PAY", payCode: "ROOM")                   || true
        new HotelPayTypeInput(payType: "FLASH_STAY_PAY", payCode: "ROOM")             || true
    }

    @Unroll
    def "requireSubmitOrder with different scenarios"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                hotelPayTypeInput: Arrays.asList(hotelPayTypeInput)
        )
        ResourceToken resourceToken = new ResourceToken()
        OrderCreateToken orderCreateToken = new OrderCreateToken()

        when:
        boolean result = OrderCreateProcessorOfUtil.requireSubmitOrder(orderCreateRequestType, resourceToken, orderCreateToken)

        then:
        result == expectedResult

        where:
        hotelPayTypeInput                                                             || expectedResult
        new HotelPayTypeInput(payType: "CORP_CREDIT_CARD_GUARANTEE", payCode: "ROOM") || true
    }

    def "testGetCheckOverStandardRcInfoType"() {
        given: "Mock dependencies and inputs"
        def mapper = new MapperOfMatchApprovalFlowRequestType()
        def checkTravelPolicyResponseType = new CheckTravelPolicyResponseType()
        def orderCreateRequestType = new OrderCreateRequestType()
        def resourceToken = new ResourceToken()
        def checkRcResultType = new CheckRcResultType()
        def checkOverStandardRcInfoType = new CheckOverStandardRcInfoType()
        checkOverStandardRcInfoType.setServiceChargeType("CORP_PAY_SERVICE_CHARGE")
        def checkOverStandardRcInfoTypeNone = new CheckOverStandardRcInfoType()
        checkOverStandardRcInfoTypeNone.setServiceChargeType("NONE")
        checkRcResultType.setOverStandardRc([checkOverStandardRcInfoType, checkOverStandardRcInfoTypeNone])
        checkTravelPolicyResponseType.setCheckRcResult(checkRcResultType)

        when: "Calling getCheckOverStandardRcInfoType method"
        def result = OrderCreateProcessorOfUtil.getCheckOverStandardRcInfoType(checkTravelPolicyResponseType, orderCreateRequestType.hotelPayTypeInput, resourceToken)

        then: "Assert the expected outcomes"
        result != null
        result.getServiceChargeType() == "NONE"


        when: "Calling getCheckOverStandardRcInfoType method"
        checkTravelPolicyResponseType.checkRcResult = null
        result = OrderCreateProcessorOfUtil.getCheckOverStandardRcInfoType(checkTravelPolicyResponseType, orderCreateRequestType.hotelPayTypeInput, resourceToken)

        then: "Assert the expected outcomes"
        result != null
        result.getServiceChargeType() == null
    }

    @Unroll
    def "testGetServiceChargePayType"() {
        given:
        expect: "Calling getServiceChargePayType method"
        result == OrderCreateProcessorOfUtil.getServiceChargePayType(hotelPayTypeEnum)
        where:
        hotelPayTypeEnum          || result
        HotelPayTypeEnum.CORP_PAY || "CORP_PAY_SERVICE_CHARGE"
        HotelPayTypeEnum.SELF_PAY || "PERSONAL_PAY_SERVICE_CHARGE"
        HotelPayTypeEnum.NONE     || "NONE"
        null                      || "NONE"
    }


    @Unroll
    def "testNeedGetCommonPassenger"() {
        given: "Mock dependencies and inputs"
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                return true
            }
        }
        def userInfo = new UserInfo(corpId: "123")
        def integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: userInfo)
        def orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: integrationSoaRequestType,
                hotelBookPassengerInputs: hotelBookPassengerInputs,
                strategyInfos: strategyInfos)
        OrderCreateToken orderCreateToken = new OrderCreateToken(continueTypes: continueTypes)

        when: "Calling needGetCommonPassenger method"
        def result = OrderCreateProcessorOfUtil.needGetCommonPassenger(orderCreateRequestType, orderCreateToken)

        then: "The result should match the expected outcome"
        result == expectedResult

        where: "Different scenarios for testing"
        continueTypes | strategyInfos                                                                      | hotelBookPassengerInputs                                                                                  || expectedResult
        []            | [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "MODIFY")]       | [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(infoId: "789", employee: "F"))] || true
        []            | [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "MODIFY")]       | [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(infoId: "789", employee: "T"))] || false
        []            | [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "COPY_ORDER")]   | [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(infoId: "789", employee: "F"))] || true
        []            | [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "APPLY_MODIFY")] | [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(infoId: "789", employee: "F"))] || false
    }


    @Unroll
    def "testRequireOfflineNewPay with #description"() {
        given: "Mock dependencies and inputs"
        def userInfo = new UserInfo(corpId: corpIduser)
        def integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: userInfo, sourceFrom: sourceFrom)
        def orderCreateRequestType = new OrderCreateRequestType(integrationSoaRequestType: integrationSoaRequestType)
        def resourceToken = new ResourceToken()
        def orderCreateToken = new OrderCreateToken()
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                if (corpId == "123") {
                    return false
                } else {
                    return true
                }
            }
        }

        when: "Calling requireOfflineNewPay method"
        def result = OrderCreateProcessorOfUtil.requireOfflineNewPay(orderCreateRequestType, resourceToken, orderCreateToken)

        then: "The result should match the expected outcome"
        result == expectedResult

        where: "Different scenarios for testing"
        description                 | corpIduser | sourceFrom         || expectedResult
        "sourceFrom is not Offline" | "123"      | SourceFrom.H5      || false
        "corpId not supported"      | "123"      | SourceFrom.Offline || false
        "all conditions met"        | "456"      | SourceFrom.Offline || true
    }

    @Unroll
    def "testRequireSaveCommonData with #description"() {
        given: "An OrderCreateToken instance"
        def orderCreateToken = new OrderCreateToken(continueTypes: continueTypes)

        when: "Calling requireSaveCommonData method"
        def result = OrderCreateProcessorOfUtil.requireSaveCommonData(orderCreateToken)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                     | continueTypes                     | expectedResult
        "contains PRICE_CHANGE"         | [ContinueTypeConst.PRICE_CHANGE]  | false
        "does not contain PRICE_CHANGE" | [ContinueTypeConst.CONFIRM_ORDER] | true
        "empty continueTypes"           | []                                | true
    }

    @Unroll
    def "testRequireRepeatOrderControl with #description"() {
        given: "An AccountInfo instance and an OrderCreateToken instance"
        def accountInfo = Mock(WrapperOfAccount.AccountInfo)
        accountInfo.getRepeatOrderCheck() >> repeatOrderCheck
        def orderCreateToken = new OrderCreateToken(continueTypes: continueTypes)

        when: "Calling requireRepeatOrderControl method"
        def result = OrderCreateProcessorOfUtil.requireRepeatOrderControl(accountInfo, orderCreateToken)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                                        | repeatOrderCheck | continueTypes                       | expectedResult
        "repeatOrderCheck is null"                         | null             | []                                  | false
        "repeatOrderCheck is off"                          | "F"              | []                                  | false
        "repeatOrderCheck is on, no continueTypes"         | "T"              | []                                  | true
        "repeatOrderCheck is on, contains CONFLICT_ORDER"  | "T"              | [ContinueTypeConst.CONFLICT_ORDER]  | false
        "repeatOrderCheck is on, contains CONTROL_BOOKING" | "T"              | [ContinueTypeConst.CONTROL_BOOKING] | false
        "repeatOrderCheck is on, contains PRICE_CHANGE"    | "T"              | [ContinueTypeConst.PRICE_CHANGE]    | false
        "repeatOrderCheck is on, contains unrelated type"  | "T"              | ["UNRELATED_TYPE"]                  | true
    }

    @Unroll
    def "testRequireRCorRepeatOrder with #description"() {
        given: "An OrderCreateRequestType instance, AccountInfo instance, and OrderCreateToken instance"
        def orderCreateRequestType = new OrderCreateRequestType(corpPayInfo: new CorpPayInfo(corpPayType: corpPayType))
        def accountInfo = Mock(WrapperOfAccount.AccountInfo)
        accountInfo.getRepeatOrderCheck() >> repeatOrderCheck
        def orderCreateToken = new OrderCreateToken(continueTypes: continueTypes)

        when: "Calling requireRCorRepeatOrder method"
        def result = OrderCreateProcessorOfUtil.requireRCorRepeatOrder(orderCreateRequestType, accountInfo, orderCreateToken)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                                       | repeatOrderCheck | continueTypes                      | corpPayType | expectedResult
        "repeatOrderCheck is null"                        | null             | []                                 | "private"   | false
        "repeatOrderCheck is off"                         | "F"              | []                                 | "private"   | false
        "repeatOrderCheck is on, no continueTypes"        | "T"              | []                                 | "private"   | true
        "repeatOrderCheck is on, contains CONFLICT_ORDER" | "T"              | [ContinueTypeConst.CONFLICT_ORDER] | "public"    | true
    }

    def "testCheckSaveCommonData"() {
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
        given: "A SaveCommonDataResponseType with success response code"
        def successResponse = Mock(SaveCommonDataResponseType) {
            getResponseCode() >> 20000
        }

        when: "Calling checkSaveCommonData with success response"
        OrderCreateProcessorOfUtil.checkSaveCommonData(successResponse)

        then: "No exception is thrown"
        noExceptionThrown()


        when: "Calling checkSaveCommonData with failure response"
        def failureResponseDefault = Mock(SaveCommonDataResponseType) {
            getResponseCode() >> 50000
            getResponseDesc() >> "Error occurred"
        }
        OrderCreateProcessorOfUtil.checkSaveCommonData(failureResponseDefault)

        then: "A BusinessException is thrown"
        def exceptionDefault = thrown(BusinessException)
        exceptionDefault.errorCode == 687
        exceptionDefault.friendlyMessage == "预订信息获取失败，请稍后重试或联系商旅客服"
        exceptionDefault.errorMessage == "Error occurred"
        exceptionDefault.logErrorCode == "50000"


        when: "Calling checkSaveCommonData with failure response"
        def failureResponse = Mock(SaveCommonDataResponseType) {
            getResponseCode() >> 78787
            getResponseDesc() >> "Error occurred"
        }
        OrderCreateProcessorOfUtil.checkSaveCommonData(failureResponse)

        then: "A BusinessException is thrown"
        def exception = thrown(BusinessException)
        exception.errorCode == 687
        exception.friendlyMessage == "78787error"
        exception.errorMessage == "Error occurred"
        exception.logErrorCode == "78787"
    }

    @Unroll
    def "testBuildTripApprovalPassAndUnSuccess with #description"() {
        given: "Mocked SearchTripDetailResponseType"
        def searchTripDetailResponseType = new SearchTripDetailResponseType(basicInfo: new BasicInfo(authorizeStatus: authorizeStatus, tripStatus: tripStatus))

        when: "Calling buildTripApprovalPassAndUnSuccess"
        def result = OrderCreateProcessorOfUtil.buildTripApprovalPassAndUnSuccess(searchTripDetailResponseType)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                               | authorizeStatus | tripStatus      || expectedResult
        "Approval is P and trip is WaitAuthorize" | "P"             | "WaitAuthorize" || false
        "Approval is P and trip is Processing"    | "P"             | "Processing"    || false
        "Approval is P and trip is Submitted"     | "P"             | "Submitted"     || false
        "Approval is P and trip is N"             | "P"             | "N"             || false
        "Approval is P and trip is Canceled"      | "P"             | "Canceled"      || false
        "Approval is P and trip is Successful"    | "P"             | "Successful"    || false

        "Approval is S and trip is WaitAuthorize" | "S"             | "WaitAuthorize" || false
        "Approval is S and trip is Processing"    | "S"             | "Processing"    || false
        "Approval is S and trip is Submitted"     | "S"             | "Submitted"     || false
        "Approval is S and trip is N"             | "S"             | "N"             || false
        "Approval is S and trip is Canceled"      | "S"             | "Canceled"      || false
        "Approval is S and trip is Successful"    | "S"             | "Successful"    || false

        "Approval is T and trip is WaitAuthorize" | "T"             | "WaitAuthorize" || true
        "Approval is T and trip is Processing"    | "T"             | "Processing"    || true
        "Approval is T and trip is Submitted"     | "T"             | "Submitted"     || true
        "Approval is T and trip is N"             | "T"             | "N"             || false
        "Approval is T and trip is Canceled"      | "T"             | "Canceled"      || false
        "Approval is T and trip is Successful"    | "T"             | "Successful"    || false

        "Approval is A and trip is WaitAuthorize" | "A"             | "WaitAuthorize" || true
        "Approval is A and trip is Processing"    | "A"             | "Processing"    || true
        "Approval is A and trip is Submitted"     | "A"             | "Submitted"     || true
        "Approval is A and trip is N"             | "A"             | "N"             || false
        "Approval is A and trip is Canceled"      | "A"             | "Canceled"      || false
        "Approval is A and trip is Successful"    | "A"             | "Successful"    || false

        "Approval is C and trip is WaitAuthorize" | "C"             | "WaitAuthorize" || false
        "Approval is C and trip is Processing"    | "C"             | "Processing"    || false
        "Approval is C and trip is Submitted"     | "C"             | "Submitted"     || false
        "Approval is C and trip is N"             | "C"             | "N"             || false
        "Approval is C and trip is Canceled"      | "C"             | "Canceled"      || false
        "Approval is C and trip is Successful"    | "C"             | "Successful"    || false
    }

    def "needPolicyUidUserInfo"() {
        given:
        expect:
        result == OrderCreateProcessorOfUtil.needPolicyUidUserInfo(hotelPolicyInput)
        where:
        hotelPolicyInput                                                      || result
        null                                                                  || false
        new HotelPolicyInput()                                                || false
        new HotelPolicyInput(policyInput: new PolicyInput(policyUid: "1234")) || true
        new HotelPolicyInput(policyInput: new PolicyInput(policyUid: ""))     || false
    }

    def "needRetrieveTicketsByOrder"() {
        given:
        new MockUp<StrategyOfBookingInitUtil>() {
            @Mock
            public static boolean blockRetrieveTicket(Map<String, StrategyInfo> strategyInfoMap) {
                return false
            }
        }
        expect:
        result == OrderCreateProcessorOfUtil.needRetrieveTicketsByOrder(corpPayInfo, null)
        where:
        corpPayInfo                             || result
        new CorpPayInfo(corpPayType: "public")  || false
        new CorpPayInfo(corpPayType: "private") || true
    }

    @Unroll
    def "testNeedGetPolicyUsers with #description"() {
        given: "Mocked PolicyInput, IntegrationSoaRequestType, and AccountInfo"
        def policyInput = new PolicyInput(policyUid: policyUid)
        def integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: new UserInfo(userId: userId))
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isPackageEnabled() >> packageEnabled
            isPolicyModel() >> policyModel
        }

        when: "Calling needGetPolicyUsers"
        def result = OrderCreateProcessorOfUtil.needGetPolicyUsers(policyInput, integrationSoaRequestType, accountInfo)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                                             | policyUid | userId | packageEnabled | policyModel | expectedResult
        "PolicyInput is null"                                   | null      | "123"  | true           | true        | false
        "PolicyUid is blank"                                    | ""        | "123"  | true           | true        | false
        "AccountInfo does not have PackageEnabled"              | "456"     | "123"  | false          | true        | false
        "AccountInfo does not have PolicyModel"                 | "456"     | "123"  | true           | false       | false
        "PolicyUid matches UserId in IntegrationSoaRequestType" | "123"     | "123"  | true           | true        | false
        "Valid inputs where method should return true"          | "456"     | "123"  | true           | true        | true
    }


    @Unroll
    def "testNeedGetUserServedCardInfo with #description"() {
        given: "Mocked PolicyInput, IntegrationSoaRequestType, AccountInfo, CorpPayInfo, and CityInput"
        def policyInput = new PolicyInput(policyUid: policyUid)
        def integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: new UserInfo(userId: userId))
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isPackageEnabled() >> packageEnabled
            isPolicyModel() >> policyModel
            bookPolicyPsgMustSameTripApprove(_, _) >> mustSameTripApprove
        }
        def corpPayInfo = new CorpPayInfo()
        def cityInfo = new CityInput(cityId: 1)

        when: "Calling needGetUserServedCardInfo"
        def result = OrderCreateProcessorOfUtil.needGetUserServedCardInfo(policyInput, integrationSoaRequestType, accountInfo, corpPayInfo, cityInfo)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                                    | policyUid | userId | packageEnabled | policyModel | mustSameTripApprove | expectedResult
        "PolicyInput is null"                          | ""        | "123"  | true           | true        | false               | false
        "AccountInfo requires same trip approval"      | "456"     | "123"  | false          | true        | true                | false
        "PolicyUid is blank"                           | ""        | "123"  | true           | true        | false               | false
        "AccountInfo is not a policy model"            | "456"     | "123"  | true           | false       | false               | false
        "PolicyUid matches UserId"                     | "123"     | "123"  | true           | true        | false               | false
        "Valid inputs where method should return true" | "456"     | "123"  | true           | true        | false               | false
        "Valid inputs where method should return true" | "456"     | "123"  | false          | true        | false               | true
    }

    @Unroll
    def "testBuildUids with #description"() {
        given: "Mocked IntegrationSoaRequestType and MembershipInfo"
        def integrationSoaRequestType = integrationUserId ? new IntegrationSoaRequestType(userInfo: new UserInfo(userId: integrationUserId)) : null
        def membershipInfo = membershipUid ? new MembershipInfo(membershipUid: membershipUid) : null

        when: "Calling buildUids"
        def result = OrderCreateProcessorOfUtil.buildUids(integrationSoaRequestType, membershipInfo)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                            | integrationUserId | membershipUid | expectedResult
        "Both inputs are null"                 | null              | null          | []
        "IntegrationSoaRequestType has userId" | "user123"         | null          | ["user123"]
        "MembershipInfo has membershipUid"     | null              | "member456"   | ["member456"]
        "Both inputs have valid data"          | "user123"         | "member456"   | ["member456", "user123"]
    }

    @Unroll
    def "testNeedGetPlatformRelationByUid with #description"() {
        given: "Mocked IntegrationSoaRequestType and MembershipInfo"
        def integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: new UserInfo(userId: integrationUserId))
        QueryBizModeBindRelationResponseType queryBizModeBindRelationResponseType = new QueryBizModeBindRelationResponseType(
                bizModeBindRelationDataList: [new BizModeBindRelationData(dimensionId: membershipUid, primaryDimensionId: membershipUid)]
        )

        when: "Calling needGetPlatformRelationByUid"
        def result = OrderCreateProcessorOfUtil.needGetPlatformRelationByUid(integrationSoaRequestType, queryBizModeBindRelationResponseType)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                            | integrationUserId | membershipUid | expectedResult
        "Both inputs are null"                 | ""                | null          | false
        "IntegrationSoaRequestType has userId" | "user123"         | null          | false
        "MembershipInfo has membershipUid"     | ""                | "member456"   | false
        "Both inputs have valid data"          | "user123"         | "member456"   | false
        "Both inputs have valid data"          | "user123"         | "user123"     | true
    }


    def "buildCorpBookingInfoSignatureBO"() {
        expect:
        OrderCreateProcessorOfUtil.buildCorpBookingInfoSignatureBO(null) == null
        OrderCreateProcessorOfUtil.buildCorpBookingInfoSignatureBO(new CorpBookingInfo()) == null
        OrderCreateProcessorOfUtil.buildCorpBookingInfoSignatureBO(new CorpBookingInfo(hotelOrderInfo: new HotelOrderInfo(travelReason: 2))).travelReason == 2


    }


    def "requireSaveContactInvoiceDefaultInfo"() {
        expect:
        !OrderCreateProcessorOfUtil.requireSaveContactInvoiceDefaultInfo(null)
        !OrderCreateProcessorOfUtil.requireSaveContactInvoiceDefaultInfo([])
        !OrderCreateProcessorOfUtil.requireSaveContactInvoiceDefaultInfo([new HotelInvoiceInfo()])
        !OrderCreateProcessorOfUtil.requireSaveContactInvoiceDefaultInfo([new HotelInvoiceInfo(hotelInvoiceType: "ROOM")])
        !OrderCreateProcessorOfUtil.requireSaveContactInvoiceDefaultInfo([new HotelInvoiceInfo(hotelInvoiceType: "ROOM", invoiceInfo: new InvoiceInfo())])
        !OrderCreateProcessorOfUtil.requireSaveContactInvoiceDefaultInfo([new HotelInvoiceInfo(hotelInvoiceType: "ROOM", invoiceInfo: new InvoiceInfo(invoiceType: "a"))])
        !OrderCreateProcessorOfUtil.requireSaveContactInvoiceDefaultInfo([new HotelInvoiceInfo(hotelInvoiceType: "ROOM", invoiceInfo: new InvoiceInfo(invoiceType: "VAT_ELECTRONIC_INVOICE"))])
        OrderCreateProcessorOfUtil.requireSaveContactInvoiceDefaultInfo([new HotelInvoiceInfo(hotelInvoiceType: "ROOM", invoiceInfo: new InvoiceInfo(invoiceType: "VAT_ELECTRONIC_INVOICE", invoiceTitle: "a"))])
        !OrderCreateProcessorOfUtil.requireSaveContactInvoiceDefaultInfo([new HotelInvoiceInfo(hotelInvoiceType: "ROOM", invoiceInfo: new InvoiceInfo(invoiceType: "DInvoice"))])
        OrderCreateProcessorOfUtil.requireSaveContactInvoiceDefaultInfo([new HotelInvoiceInfo(hotelInvoiceType: "ROOM", invoiceInfo: new InvoiceInfo(invoiceType: "DInvoice", invoiceTitle: "a"))])
        !OrderCreateProcessorOfUtil.requireSaveContactInvoiceDefaultInfo([new HotelInvoiceInfo(hotelInvoiceType: "ROOM", invoiceInfo: new InvoiceInfo(invoiceType: "a", taxNumber: "a"))])
        !OrderCreateProcessorOfUtil.requireSaveContactInvoiceDefaultInfo([new HotelInvoiceInfo(hotelInvoiceType: "ROOM", invoiceInfo: new InvoiceInfo(invoiceType: "a", taxNumber: "a", invoiceCompanyInfo: new InvoiceCompanyInfo()))])
        !OrderCreateProcessorOfUtil.requireSaveContactInvoiceDefaultInfo([new HotelInvoiceInfo(hotelInvoiceType: "ROOM", invoiceInfo: new InvoiceInfo(invoiceType: "a", taxNumber: "a", invoiceCompanyInfo: new InvoiceCompanyInfo(companyBank: "a")))])
        !OrderCreateProcessorOfUtil.requireSaveContactInvoiceDefaultInfo([new HotelInvoiceInfo(hotelInvoiceType: "ROOM", invoiceInfo: new InvoiceInfo(invoiceType: "a", taxNumber: "a", invoiceCompanyInfo: new InvoiceCompanyInfo(companyBank: "a", companyBankAccount: "a")))])
        !OrderCreateProcessorOfUtil.requireSaveContactInvoiceDefaultInfo([new HotelInvoiceInfo(hotelInvoiceType: "ROOM", invoiceInfo: new InvoiceInfo(invoiceType: "a", taxNumber: "a", invoiceCompanyInfo: new InvoiceCompanyInfo(companyBank: "a", companyBankAccount: "a", companyTel: "a")))])


    }


    def "buildApproveObjSignatureBO"() {
        expect:
        OrderCreateProcessorOfUtil.buildApproveObjSignatureBO(null) == null
        OrderCreateProcessorOfUtil.buildApproveObjSignatureBO(new ApproveObjInfo()) == null
        OrderCreateProcessorOfUtil.buildApproveObjSignatureBO(new ApproveObjInfo(approveObj: new ApproveObj(submitter: "a"))).submitter == "a"
        OrderCreateProcessorOfUtil.buildApproveObjSignatureBO(new ApproveObjInfo(approveObj: new ApproveObj(costCenters: [null, new CostCenter(costCenterId: "a")]))).costCenterSignatureBOS*.costCenterId == ["a"]
        OrderCreateProcessorOfUtil.buildApproveObjSignatureBO(new ApproveObjInfo(approveObj: new ApproveObj(projectNames: ["a"]))).projectNames == ["a"]
        OrderCreateProcessorOfUtil.buildApproveObjSignatureBO(new ApproveObjInfo(approveObj: new ApproveObj(travelPurposeNames: ["a"]))).travelPurposeNames == ["a"]
        OrderCreateProcessorOfUtil.buildApproveObjSignatureBO(new ApproveObjInfo(approveObj: new ApproveObj(travelers: ["a"]))).travelers == ["a"]
        OrderCreateProcessorOfUtil.buildApproveObjSignatureBO(new ApproveObjInfo(approveObj: new ApproveObj(travelerUids: ["a"]))).travelerUids == ["a"]
        OrderCreateProcessorOfUtil.buildApproveObjSignatureBO(new ApproveObjInfo(approveObj: new ApproveObj(travelerInfoList: [null, new TravelerInfo(name: "a")]))).travelerInfoSignatureBOS*.name == ["a"]

    }

    def "requireSmartFollowApproval"() {
        expect:
        !OrderCreateProcessorOfUtil.requireSmartFollowApproval(new OrderCreateRequestType(corpPayInfo: new CorpPayInfo("private")), null, null, null)
        !OrderCreateProcessorOfUtil.requireSmartFollowApproval(new OrderCreateRequestType(corpPayInfo: new CorpPayInfo("public")), null, new OrderCreateToken(continueTypes: [ContinueTypeConst.FOLLOW_APPROVAL]), null)
        !OrderCreateProcessorOfUtil.requireSmartFollowApproval(new OrderCreateRequestType(corpPayInfo: new CorpPayInfo("public"), integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: SourceFrom.Offline)), null, new OrderCreateToken(continueTypes: []), null)
        OrderCreateProcessorOfUtil.requireSmartFollowApproval(new OrderCreateRequestType(corpPayInfo: new CorpPayInfo("public"), integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: SourceFrom.Offline)), null, new OrderCreateToken(continueTypes: []), new GetAuthDelayResponseType(hotelAuthDelayconfig: new HotelAuthDelayConfig(smartauthdelayCf: true)))
        !OrderCreateProcessorOfUtil.requireSmartFollowApproval(new OrderCreateRequestType(followApprovalInfoInput: new FollowApprovalInfoInput(followOrderId: "a"), corpPayInfo: new CorpPayInfo("public"), integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: SourceFrom.Offline)), null, new OrderCreateToken(continueTypes: []), new GetAuthDelayResponseType(hotelAuthDelayconfig: new HotelAuthDelayConfig(smartauthdelayCf: true)))
        !OrderCreateProcessorOfUtil.requireSmartFollowApproval(new OrderCreateRequestType(corpPayInfo: new CorpPayInfo("public"), integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: SourceFrom.Offline)), null, new OrderCreateToken(continueTypes: []), null)

    }

    def "checkRcInvalid"() {
        expect:
        OrderCreateProcessorOfUtil.checkRcInvalid(null, null)
        OrderCreateProcessorOfUtil.checkRcInvalid(new CheckRcResultType(invalidRcTypeList: []), null)
        OrderCreateProcessorOfUtil.checkRcInvalid(new CheckRcResultType(invalidRcTypeList: ["a"]), null)
        OrderCreateProcessorOfUtil.checkRcInvalid(new CheckRcResultType(invalidRcTypeList: ["LOW_PRICE"]), RcTypeEnum.MODIFY)
        OrderCreateProcessorOfUtil.checkRcInvalid(new CheckRcResultType(invalidRcTypeList: ["LOW_PRICE"]), RcTypeEnum.LOW_PRICE)
        !OrderCreateProcessorOfUtil.checkRcInvalid(new CheckRcResultType(invalidRcTypeList: ["OVER_STANDARD"]), RcTypeEnum.LOW_PRICE)
        OrderCreateProcessorOfUtil.checkRcInvalid(new CheckRcResultType(invalidRcTypeList: ["OVER_STANDARD"]), RcTypeEnum.AGREEMENT)
    }

    def "buildParamCheckResult"() {
        expect:
        !OrderCreateProcessorOfUtil.buildParamCheckResult(null, OrderCreateErrorEnum.ACTIVITY_DETAIL_URL_IS_NULL, null, null).result
    }


    def "requireArtificialFollowApproval"() {
        expect:
        !OrderCreateProcessorOfUtil.requireArtificialFollowApproval(new OrderCreateRequestType(corpPayInfo: new CorpPayInfo("private")), null)
        !OrderCreateProcessorOfUtil.requireArtificialFollowApproval(new OrderCreateRequestType(corpPayInfo: new CorpPayInfo("public")), new OrderCreateToken(continueTypes: [ContinueTypeConst.FOLLOW_APPROVAL]))
        !OrderCreateProcessorOfUtil.requireArtificialFollowApproval(new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: SourceFrom.Online), corpPayInfo: new CorpPayInfo("public")), new OrderCreateToken())
        !OrderCreateProcessorOfUtil.requireArtificialFollowApproval(new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: SourceFrom.Offline), corpPayInfo: new CorpPayInfo("public")), new OrderCreateToken())
        !OrderCreateProcessorOfUtil.requireArtificialFollowApproval(new OrderCreateRequestType(followApprovalInfoInput: new FollowApprovalInfoInput(artificialFollow: false), integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: SourceFrom.Offline), corpPayInfo: new CorpPayInfo("public")), new OrderCreateToken())
        !OrderCreateProcessorOfUtil.requireArtificialFollowApproval(new OrderCreateRequestType(followApprovalInfoInput: new FollowApprovalInfoInput(artificialFollow: true), integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: SourceFrom.Offline), corpPayInfo: new CorpPayInfo("public")), new OrderCreateToken())
        !OrderCreateProcessorOfUtil.requireArtificialFollowApproval(new OrderCreateRequestType(followApprovalInfoInput: new FollowApprovalInfoInput(followTripId: "2", artificialFollow: true), integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: SourceFrom.Offline), corpPayInfo: new CorpPayInfo("public")), new OrderCreateToken())
    }


    @Unroll
    def "testNeedExternalEmployeeId with #description"() {
        given: "A mocked CustomConfigSearchResponseType"
        def configResult = new HashMap();
        configResult.put("INPUT_EXTERNAL_EID", valueOfInputExternalEid)
        def customConfigSearchResponseType = Mock(CustomConfigSearchResponseType) {
            customConfig >> new CustomConfig(configType: "TENCENT_OFFLINE", configResult: configResult)
        }
        def orderCreateRequestType = new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: sourceFrom))
        when: "Calling needExternalEmployeeId"
        def result = OrderCreateProcessorOfUtil.needExternalEmployeeId(customConfigSearchResponseType, orderCreateRequestType.getIntegrationSoaRequestType())

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                         | sourceFrom         | valueOfInputExternalEid || expectedResult
        "valid external employee ID"        | SourceFrom.Offline | "1"                     || true
        "valid external employee ID Online" | SourceFrom.Online  | "1"                     || false
        "null external employee ID"         | SourceFrom.Offline | ""                      || false
        "empty external employee ID"        | SourceFrom.Offline | ""                      || false
    }

    @Unroll
    def "needExternalEmployeeId"() {
        given:
        def customConfigSearchResponseType = new CustomConfigSearchResponseType(customConfig: customConfig)
        def orderCreateRequestType = new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: sourceFrom))
        expect:
        result == OrderCreateProcessorOfUtil.needExternalEmployeeId(customConfigSearchResponseType, orderCreateRequestType.getIntegrationSoaRequestType())
        where:
        sourceFrom         | customConfig                                                                          || result
        SourceFrom.Offline | new CustomConfig(configType: "TENCENT_OFFLINE", configResult: new HashMap<String, String>() {
            {
                put("INPUT_EXTERNAL_EID", "1")
            }
        })                                                                                                         || true
        SourceFrom.Online  | new CustomConfig(configType: "TENCENT_OFFLINE", configResult: new HashMap<String, String>() {
            {
                put("INPUT_EXTERNAL_EID", "1")
            }
        })                                                                                                         || false
        SourceFrom.Offline | new CustomConfig(configType: "TENCENT_OFFLINE", configResult: null)                   || false
        SourceFrom.Offline | new CustomConfig(configType: "", configResult: new HashMap<String, String>() {
            {
                put("INPUT_EXTERNAL_EID", "1")
            }
        })                                                                                                         || false
        SourceFrom.Offline | new CustomConfig()                                                                    || false
        SourceFrom.Offline | null                                                                                  || false
    }

    def "buildBlueSpaceEname"() {
        given:
        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            private static String convertEnNameV2(String legalLastName, String legalMiddleName, String legalFirstName,
                                                  String nationality, String originEnName, QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig) {
                return "Converted Name"
            }
        }
        HotelBookPassengerInput hotelBookPassengerInput = new HotelBookPassengerInput()
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.BaseCheckAvailInfo)
        when:
        def result = OrderCreateProcessorOfUtil.buildBlueSpaceEname(hotelBookPassengerInput, checkAvailInfo, null)
        then:
        result == "Converted Name"


        when:
        hotelBookPassengerInput = new HotelBookPassengerInput(certificateInfos: [new CertificateInfo(defaultCertificateFlag: "T")])
        result = OrderCreateProcessorOfUtil.buildBlueSpaceEname(hotelBookPassengerInput, checkAvailInfo, null)
        then:
        result == "Converted Name"
    }

    def "buildBlueSpaceEname-usename"() {
        given:
        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            private static String convertEnNameV2(String legalLastName, String legalMiddleName, String legalFirstName,
                                                  String nationality, String originEnName, QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig) {
                return originEnName
            }
        }
        HotelBookPassengerInput hotelBookPassengerInput = new HotelBookPassengerInput(
                certificateInfos: [new CertificateInfo(defaultCertificateFlag: "T", legalFirstName: "legalFirstName", legalLastName: "legalLastName")],
                passengerBasicInfo: new PassengerBasicInfo(
                        enName: new PassengerName(firstName: "firstName", lastName: "lastName"),
                        localName: new PassengerName(firstName: "localNameFirstName", lastName: "localNameLastName"),
                        defaultNameType: "EN")
        )
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.BaseCheckAvailInfo)
        when:
        def result = OrderCreateProcessorOfUtil.buildBlueSpaceEname(hotelBookPassengerInput, checkAvailInfo, null)
        then:
        result == "legalLastName/legalFirstName"


        when:
        hotelBookPassengerInput.certificateInfos = null
        result = OrderCreateProcessorOfUtil.buildBlueSpaceEname(hotelBookPassengerInput, checkAvailInfo, null)
        then:
        result == "lastName/firstName"


        when:
        hotelBookPassengerInput.passengerBasicInfo.defaultNameType = "LOCAL"
        result = OrderCreateProcessorOfUtil.buildBlueSpaceEname(hotelBookPassengerInput, checkAvailInfo, null)
        then:
        result == "localNameLastName/localNameFirstName"
    }


    def "buildBlueSpaceEname-defaultNameType"() {
        given:
        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            private static String convertEnNameV2(String legalLastName, String legalMiddleName, String legalFirstName,
                                                  String nationality, String originEnName, QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig) {
                return originEnName
            }

            @Mock
            private static CertificateInfo getDefaultCertificateInfo(List<CertificateInfo> certificateInfoList,
                                                                     WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
                return new CertificateInfo(defaultCertificateFlag: "T", certificateType: "EN")
            }

        }
        HotelBookPassengerInput hotelBookPassengerInput = new HotelBookPassengerInput(
                passengerBasicInfo: new PassengerBasicInfo(defaultNameType: "EN", enName: new PassengerName(
                        lastName: "lastName",
                        firstName: "firstName"
                )),
                certificateInfos: [new CertificateInfo()]
        )
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.BaseCheckAvailInfo)
        when:
        def result = OrderCreateProcessorOfUtil.buildBlueSpaceEname(hotelBookPassengerInput, checkAvailInfo, null)
        then:
        result == "lastName/firstName"

        when:
        hotelBookPassengerInput = new HotelBookPassengerInput(
                passengerBasicInfo: new PassengerBasicInfo(defaultNameType: "LOCAL",
                        enName: new PassengerName(
                                lastName: "lastName",
                                firstName: "firstName"
                        ),
                        localName: new PassengerName(
                                lastName: "localLastName",
                                firstName: "localFirstName"
                        )
                ),
                certificateInfos: [new CertificateInfo()]
        )
        result = OrderCreateProcessorOfUtil.buildBlueSpaceEname(hotelBookPassengerInput, checkAvailInfo, null)
        then:
        result == "localLastName/localFirstName"
    }

    def "convertEnNameV2-originEnName"() {
        given:
        when:
        def result = OrderCreateProcessorOfUtil.convertEnNameV2("", "", "legalFirstName",
                "nationality", "originEnName", null)
        then:
        result == "originEnName"
    }

    def "getPassengerName"() {
        given:
        when:
        def result = OrderCreateProcessorOfUtil.getPassengerName("lastName", "", "firstName",
                "nation", null)
        then:
        result == "lastName/firstName"
    }

    def "getPassengerName-empty"() {
        given:
        when:
        def result = OrderCreateProcessorOfUtil.getPassengerName("", "", "",
                "nation", null)
        then:
        result == ""
    }

    def "getDefaultCertificateInfo"() {
        given:
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.BaseCheckAvailInfo) {
            getSupportCertificateType() >> ["EN"] >> null
        }
        List<CertificateInfo> certificateInfoList = [
                new CertificateInfo(defaultCertificateFlag: "T", certificateType: "PASSPORT"),
                new CertificateInfo(defaultCertificateFlag: "F", certificateType: "OTHERDOCUMENT")
        ];
        when:
        def result = OrderCreateProcessorOfUtil.getDefaultCertificateInfo(certificateInfoList, checkAvailInfo)
        then:
        result != null
        result.defaultCertificateFlag == "T"
        result.certificateType == "PASSPORT"


        when:
        result = OrderCreateProcessorOfUtil.getDefaultCertificateInfo(certificateInfoList, checkAvailInfo)
        then:
        result != null
        result.defaultCertificateFlag == "F"
        result.certificateType == "OTHERDOCUMENT"
    }

    def "buildNationalityInfo"() {
        given:
        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            private static CertificateInfo getDefaultCertificateInfo(List<CertificateInfo> certificateInfoList,
                                                                     WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo) {
                return new CertificateInfo(nationalityInfo: new NationalityInfo(nationalityCode: "HK"))
            }
        }
        HotelBookPassengerInput hotelBookPassengerInput = new HotelBookPassengerInput(
                nationalityInfo: new NationalityInfo(nationalityCode: "CN"),
                certificateInfos: []
        )
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.BaseCheckAvailInfo) {

        }
        when:
        def result = OrderCreateProcessorOfUtil.buildNationalityInfo(hotelBookPassengerInput, checkAvailInfo)
        then:
        result.nationalityCode == "CN"

        when:
        hotelBookPassengerInput = new HotelBookPassengerInput(
                nationalityInfo: new NationalityInfo(nationalityCode: "CN"),
                certificateInfos: [new CertificateInfo(nationalityInfo: new NationalityInfo(nationalityCode: "HK"))]
        )
        result = OrderCreateProcessorOfUtil.buildNationalityInfo(hotelBookPassengerInput, checkAvailInfo)
        then:
        result.nationalityCode == "HK"
    }

    def "getEname"() {
        given:
        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            protected static String buildBlueSpaceEname(HotelBookPassengerInput hotelBookPassengerInput,
                                                        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo,
                                                        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig) {
                return "Converted Name"
            }
        }
        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            private static String getEname(PassengerBasicInfo inputPsg) {
                return "EName"
            }
        }
        HotelBookPassengerInput hotelBookPassengerInput = new HotelBookPassengerInput()
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.BaseCheckAvailInfo) {

        }
        QconfigOfCertificateInitConfig qconfigOfCertificateInitConfig = null
        Map<String, StrategyInfo> strategyInfoMap = new HashMap<>()
        when:
        def result = OrderCreateProcessorOfUtil.getEname(null, checkAvailInfo, qconfigOfCertificateInitConfig, null)
        then:
        result == ""

        when:
        strategyInfoMap.put("PASSENGER_NAME_CONCAT_USE_CONFIG", new StrategyInfo(strategyKey: "PASSENGER_NAME_CONCAT_USE_CONFIG", strategyValue: "T"))
        result = OrderCreateProcessorOfUtil.getEname(hotelBookPassengerInput, checkAvailInfo, qconfigOfCertificateInitConfig, strategyInfoMap)
        then:
        result == "Converted Name"

        when:
        strategyInfoMap = new HashMap<>()
        result = OrderCreateProcessorOfUtil.getEname(hotelBookPassengerInput, checkAvailInfo, qconfigOfCertificateInitConfig, strategyInfoMap)
        then:
        result == "EName"
    }

    @Unroll
    def "needCustomConfigSearch"() {
        given:
        CorpPayInfo corpPayInfo = new CorpPayInfo(corpPayType: corpPayType)
        IntegrationSoaRequestType integrationSoaRequestType = new IntegrationSoaRequestType(sourceFrom: sourceFrom)
        expect:
        result == OrderCreateProcessorOfUtil.needCustomConfigSearch(corpPayInfo, integrationSoaRequestType)
        where:
        corpPayType | sourceFrom         || result
        "public"    | SourceFrom.Online  || false
        "public"    | SourceFrom.Offline || true
        "private"   | SourceFrom.Online  || false
        "private"   | SourceFrom.Offline || false
    }

    def "测试buildCheckCostCenterPassengers"() {
        given: "准备测试数据和Mock"
        def hotelPassengerInput = new HotelPassengerInput(
                uid: null,
                infoId: "info456",
                employee: "0",
                temporaryId: "temp789"
        )
        def hotelBookPassengerInput = new HotelBookPassengerInput(
                hotelPassengerInput: hotelPassengerInput
        )
        def hotelBookPassengerInputs = [hotelBookPassengerInput]

        def cityInput = Mock(CityInput) {
        }
        def checkAvailInfo = new WrapperOfCheckAvail.BaseCheckAvailInfo()

        when: "调用方法"
        def result = OrderCreateProcessorOfUtil.buildCheckCostCenterPassengers(
                hotelBookPassengerInputs, cityInput, checkAvailInfo, null, null)

        then: "验证结果使用infoId作为uid"
        result.size() == 1
        def passenger = result[0]
        passenger.uid == "info456"
        passenger.corpUser == "0"
        passenger.name != null
    }

    @Unroll
    def "测试buildCheckCostCenterPassengers - uid优先级逻辑: uid=#uid, infoId=#infoId, expectedUid=#expectedUid"() {
        given: "准备测试数据"
        def hotelPassengerInput = new HotelPassengerInput(
                uid: uid,
                infoId: infoId,
                employee: employee,
                temporaryId: temporaryId
        )
        def hotelBookPassengerInput = new HotelBookPassengerInput(
                hotelPassengerInput: hotelPassengerInput
        )
        def hotelBookPassengerInputs = [hotelBookPassengerInput]

        def cityInput = Mock(CityInput) {
        }
        def checkAvailInfo = new WrapperOfCheckAvail.BaseCheckAvailInfo()

        when: "调用方法"
        def result = OrderCreateProcessorOfUtil.buildCheckCostCenterPassengers(
                hotelBookPassengerInputs, cityInput, checkAvailInfo, null, null)

        then: "验证uid优先级逻辑"
        result.size() == 1
        result[0].uid == expectedUid
        result[0].corpUser == employee
        result[0].temporaryId == temporaryId

        where: "测试不同的uid和infoId组合"
        uid     | infoId  | employee | temporaryId | expectedUid
        "user1" | "info1" | "1"      | "temp1"     | "user1"
        null    | "info2" | "0"      | "temp2"     | "info2"
        ""      | "info3" | "1"      | "temp3"     | "info3"
        "   "   | "info4" | "0"      | null        | "info4"
    }

    def "测试buildCheckCostCenterPassengers - 多个乘客"() {
        given: "准备多个乘客数据"
        def passenger1 = new HotelBookPassengerInput(
                hotelPassengerInput: new HotelPassengerInput(
                        uid: "user1", infoId: "info1", employee: "1", temporaryId: "temp1"
                )
        )
        def passenger2 = new HotelBookPassengerInput(
                hotelPassengerInput: new HotelPassengerInput(
                        uid: null, infoId: "info2", employee: "0", temporaryId: "temp2"
                )
        )
        def hotelBookPassengerInputs = [passenger1, passenger2]

        def cityInput = Mock(CityInput) {
        }
        def checkAvailInfo = new WrapperOfCheckAvail.BaseCheckAvailInfo()

        when: "调用方法"
        def result = OrderCreateProcessorOfUtil.buildCheckCostCenterPassengers(
                hotelBookPassengerInputs, cityInput, checkAvailInfo, null, null)

        then: "验证多个乘客结果"
        result.size() == 2

        // 验证第一个乘客
        result[0].uid == "user1"
        result[0].corpUser == "1"
        result[0].temporaryId == "temp1"
        result[0].name != null

        // 验证第二个乘客
        result[1].uid == "info2"
        result[1].corpUser == "0"
        result[1].temporaryId == "temp2"
        result[1].name != null
    }

    def "测试buildCheckCostCenterPassengers - 空列表"() {
        given: "空的乘客列表"
        def hotelBookPassengerInputs = []
        def cityInput = Mock(CityInput) {
            getCityId() >> "001"
        }
        def checkAvailInfo = new WrapperOfCheckAvail.BaseCheckAvailInfo()

        when: "调用方法"
        def result = OrderCreateProcessorOfUtil.buildCheckCostCenterPassengers(
                hotelBookPassengerInputs, cityInput, checkAvailInfo, null, null)

        then: "返回空列表"
        result.size() == 0
        result.isEmpty()
    }


    @Unroll
    def "testRequireMatchCostCenter with #description"() {
        given: "准备测试数据"
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                corpPayInfo: new CorpPayInfo(corpPayType: corpPayType),
                miceInput: new MiceInput(miceToken: miceToken)
        )
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("BillType", "A")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build()
        ResourceToken resourceToken = new ResourceToken()
        Map<String, StrategyInfo> strategyInfoMap = [:]

        and: "Mock needCostCenterNew方法"
        GroovyMock(OrderCreateProcessorOfUtil, global: true)
        OrderCreateProcessorOfUtil.needCostCenterNew(resourceToken, orderCreateRequestType.getCorpPayInfo(), accountInfo, strategyInfoMap) >> needCostCenterNewResult

        and: "Mock accountInfo.isPackageEnabled()"
        accountInfo.isPackageEnabled() >> packageEnabled

        when: "调用requireMatchCostCenter方法"
        def result = OrderCreateProcessorOfUtil.requireMatchCostCenter(
                orderCreateRequestType,
                accountInfo,
                saveCommonDataCostCenter,
                resourceToken,
                strategyInfoMap
        )

        then: "验证结果"
        result == expectedResult

        where: "测试场景"
        description              | corpPayType | miceToken | saveCommonDataCostCenter | needCostCenterNewResult | packageEnabled || expectedResult
        "需要新的成本中心"       | "public"    | null      | false                    | true                    | false          || false
        "已保存通用数据成本中心" | "public"    | null      | true                     | false                   | false          || false
        "私人支付"               | "private"   | null      | false                    | false                   | false          || false
        "启用套餐"               | "public"    | null      | false                    | false                   | true           || false
        "有MICE令牌"             | "public"    | "token"   | false                    | false                   | false          || false
    }

    def "requireOrderPriceChange-modify返回true"() {
        given:
        def orderCreateRequestType = Mock(OrderCreateRequestType)
        def orderCreateToken = Mock(OrderCreateToken)
        orderCreateToken.containsContinueType(_) >> false
        def strategyInfo = Mock(StrategyInfo)
        orderCreateRequestType.getStrategyInfos() >> [strategyInfo]
        new MockUp<StrategyOfBookingInitUtil>() {
            @Mock
            boolean modify(List<StrategyInfo> list) { return true }

            @Mock
            boolean applyModify(List<StrategyInfo> list) { return false }

            @Mock
            boolean copyOrder(List<StrategyInfo> list) { return false }
        }
        expect:
        OrderCreateProcessorOfUtil.requireOrderPriceChange(orderCreateRequestType, orderCreateToken, null)
    }

    def "requireOrderPriceChange-applyModify返回true"() {
        given:
        def orderCreateRequestType = Mock(OrderCreateRequestType)
        def orderCreateToken = Mock(OrderCreateToken)
        orderCreateToken.containsContinueType(_) >> false
        def strategyInfo = Mock(StrategyInfo)
        orderCreateRequestType.getStrategyInfos() >> [strategyInfo]
        new MockUp<StrategyOfBookingInitUtil>() {
            @Mock
            boolean modify(List<StrategyInfo> list) { return false }

            @Mock
            boolean applyModify(List<StrategyInfo> list) { return true }

            @Mock
            boolean copyOrder(List<StrategyInfo> list) { return false }
        }
        expect:
        OrderCreateProcessorOfUtil.requireOrderPriceChange(orderCreateRequestType, orderCreateToken, null)
    }

    def "requireOrderPriceChange-copyOrder返回true"() {
        given:
        def orderCreateRequestType = Mock(OrderCreateRequestType)
        def orderCreateToken = Mock(OrderCreateToken)
        orderCreateToken.containsContinueType(_) >> false
        def strategyInfo = Mock(StrategyInfo)
        orderCreateRequestType.getStrategyInfos() >> [strategyInfo]
        new MockUp<StrategyOfBookingInitUtil>() {
            @Mock
            boolean modify(List<StrategyInfo> list) { return false }

            @Mock
            boolean applyModify(List<StrategyInfo> list) { return false }

            @Mock
            boolean copyOrder(List<StrategyInfo> list) { return true }
        }
        expect:
        OrderCreateProcessorOfUtil.requireOrderPriceChange(orderCreateRequestType, orderCreateToken, null)
    }

    def "requireOrderPriceChange-全部为false返回false"() {
        given:
        def orderCreateRequestType = Mock(OrderCreateRequestType)
        def orderCreateToken = Mock(OrderCreateToken)
        orderCreateToken.containsContinueType(_) >> false
        def strategyInfo = Mock(StrategyInfo)
        orderCreateRequestType.getStrategyInfos() >> [strategyInfo]
        new MockUp<StrategyOfBookingInitUtil>() {
            @Mock
            boolean modify(List<StrategyInfo> list) { return false }

            @Mock
            boolean applyModify(List<StrategyInfo> list) { return false }

            @Mock
            boolean copyOrder(List<StrategyInfo> list) { return false }
        }
        expect:
        !OrderCreateProcessorOfUtil.requireOrderPriceChange(orderCreateRequestType, orderCreateToken, null)
    }


    def "needCostCenterNew - 私人支付返回false"() {
        given:
        def resourceToken = Mock(ResourceToken)
        def corpPayInfo = new CorpPayInfo(corpPayType: "private")
        def accountInfo = Mock(WrapperOfAccount.AccountInfo)
        def strategyInfoMap = [:]

        when:
        def result = OrderCreateProcessorOfUtil.needCostCenterNew(resourceToken, corpPayInfo, accountInfo, strategyInfoMap)

        then:
        !result
    }

    def "needCostCenterNew - 打包单且需要成本中心且开关为T返回false"() {
        given:
        def bookInitResourceToken = Mock(BookInitResourceToken) {
            getCostCenterNewUse() >> "T"
        }
        def resourceToken = Mock(ResourceToken) {
            getBookInitResourceToken() >> bookInitResourceToken
        }
        def corpPayInfo = new CorpPayInfo(corpPayType: "public")
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isPackageEnabled() >> true
        }
        def strategyInfoMap = [:]

        and:
        GroovyMock(StrategyOfBookingInitUtil, global: true)
        StrategyOfBookingInitUtil.tripNeedCostCenter(_) >> true

        GroovyMock(BooleanUtil, global: true)
        BooleanUtil.parseStr(true) >> "T"

        when:
        def result = OrderCreateProcessorOfUtil.needCostCenterNew(resourceToken, corpPayInfo, accountInfo, strategyInfoMap)

        then:
        !result
    }

    def "needCostCenterNew - 打包单但不需要成本中心返回false"() {
        given:
        def resourceToken = Mock(ResourceToken)
        def corpPayInfo = new CorpPayInfo(corpPayType: "public")
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isPackageEnabled() >> true
        }
        def strategyInfoMap = [:]

        and:
        GroovyMock(StrategyOfBookingInitUtil, global: true)
        StrategyOfBookingInitUtil.tripNeedCostCenter(_) >> false

        when:
        def result = OrderCreateProcessorOfUtil.needCostCenterNew(resourceToken, corpPayInfo, accountInfo, strategyInfoMap)

        then:
        !result
    }

    def "needCostCenterNew - 非打包单且开关为F返回false"() {
        given:
        def bookInitResourceToken = Mock(BookInitResourceToken) {
            getCostCenterNewUse() >> "F"
        }
        def resourceToken = Mock(ResourceToken) {
            getBookInitResourceToken() >> bookInitResourceToken
        }
        def corpPayInfo = new CorpPayInfo(corpPayType: "public")
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isPackageEnabled() >> false
        }
        def strategyInfoMap = [:]

        and:
        GroovyMock(BooleanUtil, global: true)
        BooleanUtil.parseStr(true) >> "T"

        when:
        def result = OrderCreateProcessorOfUtil.needCostCenterNew(resourceToken, corpPayInfo, accountInfo, strategyInfoMap)

        then:
        !result
    }


    @Unroll
    def "testBuildFollowOrderNo"() {
        given:
        ContinueInfo continueInfoObj = null
        if (continueInfoReuse) {
            continueInfoObj = new ContinueInfo()
            continueInfoObj.setContinueCode(ApprovalFlowReuseConstant.REUSE)
        }
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: sourceFrom),
                followApprovalInfoInput: new FollowApprovalInfoInput(followSelected: followSelected, followOrderId: followOrderId, artificialFollow: artificialFollow),
                continueInfo: continueInfoObj
        )
        OrderCreateToken orderCreateToken = new OrderCreateToken(followApprovalResult: followApprovalResult)

        when:
        Long result = OrderCreateProcessorOfUtil.buildFollowOrderNo(orderCreateRequestType, orderCreateToken)

        then:
        result == expectedResult

        where:
        description                      | sourceFrom         | followSelected | followOrderId | followApprovalResult                            | artificialFollow | continueInfoReuse | expectedResult
        "followSelected is OPEN"         | SourceFrom.Online  | "T"            | "12345"       | new FollowApprovalResult(followOrderNo: "1234") | "F"              | false             | 1234L
        "followSelected is not OPEN"     | SourceFrom.Online  | "F"            | "5678"        | null                                            | "F"              | false             | 0L
        "followSelected is OPEN"         | SourceFrom.Offline | "T"            | "12345"       | null                                            | "T"              | false             | 12345L
        "REUSE分支且followOrderNo不为空" | SourceFrom.Online  | "T"            | "99999"       | new FollowApprovalResult(followOrderNo: "8888") | "F"              | true              | 8888L
    }

    @Unroll
    def "testBuildFollowOrderNoWithFollow"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: sourceFrom),
                followApprovalInfoInput: new FollowApprovalInfoInput(
                        followSelected: followSelected,
                        followOrderId: followOrderId,
                        artificialFollow: artificialFollow
                )
        )
        OrderCreateToken orderCreateToken = new OrderCreateToken(followApprovalResult: followApprovalResult)

        when:
        Long result = OrderCreateProcessorOfUtil.buildFollowOrderNo(orderCreateRequestType, orderCreateToken)

        then:
        result == expectedResult

        where:
        description                  | sourceFrom         | followSelected | followOrderId | followApprovalResult                            | artificialFollow | expectedResult
        "followSelected is OPEN"     | SourceFrom.Online  | "T"            | "12345"       | new FollowApprovalResult(followOrderNo: "1234") | "F"              | 1234L
        "followSelected is not OPEN" | SourceFrom.Online  | "F"            | "5678"        | null                                            | "F"              | 0L
        "followSelected is OPEN"     | SourceFrom.Offline | "T"            | "12345"       | null                                            | "T"              | 12345L
    }

    def "test buildIsTripFollow"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                continueInfo: continueInfo,
                followApprovalInfoInput: followApprovalInfoInput
        )
        OrderCreateToken orderCreateToken = new OrderCreateToken(
                followApprovalResult: followApprovalResult
        )

        expect:
        OrderCreateProcessorOfUtil.buildIsTripFollow(orderCreateRequestType, orderCreateToken) == expected

        where:
        continueInfo                            | followApprovalInfoInput                          | followApprovalResult                      | expected
        new ContinueInfo(continueCode: "REUSE") | null                                             | new FollowApprovalResult(tripId: "12345") | true
        null                                    | new FollowApprovalInfoInput(followSelected: "T") | new FollowApprovalResult(tripId: "99999") | true
        null                                    | new FollowApprovalInfoInput(followSelected: "F") | new FollowApprovalResult(tripId: "99999") | false
        new ContinueInfo(continueCode: "REUSE") | null                                             | new FollowApprovalResult(tripId: "")      | false
        null                                    | null                                             | new FollowApprovalResult(tripId: "88888") | false
        null                                    | new FollowApprovalInfoInput(followSelected: "T") | new FollowApprovalResult(tripId: "")      | false
    }

    def "test getfollowTripId REUSE分支-全分支"() {
        given:
        def continueInfo = new ContinueInfo(continueCode: "REUSE")
        def orderCreateRequestType = new OrderCreateRequestType(continueInfo: continueInfo)
        def orderCreateToken = new OrderCreateToken(followApprovalResult: followApprovalResult)

        expect:
        OrderCreateProcessorOfUtil.getfollowTripId(orderCreateRequestType, orderCreateToken) == expected

        where:
        followApprovalResult                          | expected
        new FollowApprovalResult(tripId: "123456789") | 123456789L   // tripId非空
        new FollowApprovalResult(tripId: "")          | 0L           // tripId为空字符串
        null                                          | 0L           // followApprovalResult为null
    }

    @Unroll
    def "needCheckHotelAuthExtensionOfAiContinue分支覆盖: #desc"() {
        given:
        def continueInfo = continueInfoCode == null ? null : new ContinueInfo(continueCode: continueInfoCode)
        def orderCreateRequestType = new OrderCreateRequestType(continueInfo: continueInfo)
        def orderCreateToken = new OrderCreateToken(followApprovalResult: followApprovalResult)
        def strategyInfoMap = [:]
        def getOrderFoundationDataResponseType = null

        and: "Mock静态方法"
        GroovyMock(StringUtil, global: true)
        GroovyMock(TemplateNumberUtil, global: true)
        GroovyMock(OrderCreateProcessorOfUtil, global: true)
        StringUtil.isBlank(_) >> isBlank
        TemplateNumberUtil.isNotZeroAndNull(_) >>> isNotZeroAndNullList
        OrderCreateProcessorOfUtil.buildApprovalReuseReBookOrderId(_, _) >> buildApprovalReuseReBookOrderId
        OrderCreateProcessorOfUtil.buildArtificialReuseNoOrderId(_, _) >> buildArtificialReuseNoOrderId
        OrderCreateProcessorOfUtil.buildArtificialReuseNoTripId(_, _) >> buildArtificialReuseNoTripId

        expect:
        OrderCreateProcessorOfUtil.needCheckHotelAuthExtensionOfAiContinue(
                orderCreateRequestType, strategyInfoMap, getOrderFoundationDataResponseType, orderCreateToken
        ) == expected

        where:
        desc                                  | continueInfoCode                      | followApprovalResult                         | isBlank | isNotZeroAndNullList  | buildApprovalReuseReBookOrderId | buildArtificialReuseNoOrderId | buildArtificialReuseNoTripId | expected
        "continueInfo为null"                  | null                                  | new FollowApprovalResult(followOrderNo: "1") | false   | [false, false, false] | 0L                              | 0L                            | 0L                           | false
        "continueCode不是APPROVAL_FLOW_REUSE" | "NOT_APPROVAL_FLOW_REUSE"             | new FollowApprovalResult(followOrderNo: "1") | false   | [false, false, false] | 0L                              | 0L                            | 0L                           | false
        "continueCode不是REUSE"               | ContinueTypeConst.APPROVAL_FLOW_REUSE | new FollowApprovalResult(followOrderNo: "1") | false   | [false, false, false] | 0L                              | 0L                            | 0L                           | false
        "followApprovalResult为null"          | ApprovalFlowReuseConstant.REUSE       | null                                         | false   | [false, false, false] | 0L                              | 0L                            | 0L                           | false
        "followOrderNo为空"                   | ApprovalFlowReuseConstant.REUSE       | new FollowApprovalResult(followOrderNo: "")  | true    | [false, false, false] | 0L                              | 0L                            | 0L                           | false
        "buildApprovalReuseReBookOrderId非零" | ApprovalFlowReuseConstant.REUSE       | new FollowApprovalResult(followOrderNo: "1") | false   | [true, false, false]  | 123L                            | 0L                            | 0L                           | false
        "buildArtificialReuseNoOrderId非零"   | ApprovalFlowReuseConstant.REUSE       | new FollowApprovalResult(followOrderNo: "1") | false   | [false, true, false]  | 0L                              | 456L                          | 0L                           | false
        "buildArtificialReuseNoTripId非零"    | ApprovalFlowReuseConstant.REUSE       | new FollowApprovalResult(followOrderNo: "1") | false   | [false, false, true]  | 0L                              | 0L                            | 789L                         | false
        "全部通过"                            | ApprovalFlowReuseConstant.REUSE       | new FollowApprovalResult(followOrderNo: "1") | false   | [false, false, false] | 0L                              | 0L                            | 0L                           | false
    }


    def "needCheckHotelAuthExtensionOfAiContinue buildApprovalReuseReBookOrderId返回非零"() {
        GroovyMock(StringUtil, global: true)
        GroovyMock(TemplateNumberUtil, global: true)
        GroovyMock(OrderCreateProcessorOfUtil, global: true)
        StringUtil.isBlank(_) >> false
        TemplateNumberUtil.isNotZeroAndNull(_) >> true
        OrderCreateProcessorOfUtil.buildApprovalReuseReBookOrderId(_, _) >> 123L
        expect:
        !OrderCreateProcessorOfUtil.needCheckHotelAuthExtensionOfAiContinue(
                new OrderCreateRequestType(continueInfo: new ContinueInfo(continueCode: ApprovalFlowReuseConstant.REUSE)), [:], null, new OrderCreateToken(followApprovalResult: new FollowApprovalResult(followOrderNo: "1"))
        )
    }

    def "needCheckHotelAuthExtensionOfAiContinue buildArtificialReuseNoOrderId返回非零"() {
        GroovyMock(StringUtil, global: true)
        GroovyMock(TemplateNumberUtil, global: true)
        GroovyMock(OrderCreateProcessorOfUtil, global: true)
        StringUtil.isBlank(_) >> false
        TemplateNumberUtil.isNotZeroAndNull(_) >>> [false, true] // 第一次false，第二次true
        OrderCreateProcessorOfUtil.buildApprovalReuseReBookOrderId(_, _) >> 0L
        OrderCreateProcessorOfUtil.buildArtificialReuseNoOrderId(_, _) >> 123L
        expect:
        !OrderCreateProcessorOfUtil.needCheckHotelAuthExtensionOfAiContinue(
                new OrderCreateRequestType(continueInfo: new ContinueInfo(continueCode: ApprovalFlowReuseConstant.REUSE)), [:], null, new OrderCreateToken(followApprovalResult: new FollowApprovalResult(followOrderNo: "1"))
        )
    }

    def "needCheckHotelAuthExtensionOfAiContinue buildArtificialReuseNoTripId返回非零"() {
        GroovyMock(StringUtil, global: true)
        GroovyMock(TemplateNumberUtil, global: true)
        GroovyMock(OrderCreateProcessorOfUtil, global: true)
        StringUtil.isBlank(_) >> false
        TemplateNumberUtil.isNotZeroAndNull(_) >>> [false, false, true] // 第三次true
        OrderCreateProcessorOfUtil.buildApprovalReuseReBookOrderId(_, _) >> 0L
        OrderCreateProcessorOfUtil.buildArtificialReuseNoOrderId(_, _) >> 0L
        OrderCreateProcessorOfUtil.buildArtificialReuseNoTripId(_, _) >> 123L
        expect:
        !OrderCreateProcessorOfUtil.needCheckHotelAuthExtensionOfAiContinue(
                new OrderCreateRequestType(continueInfo: new ContinueInfo(continueCode: ApprovalFlowReuseConstant.REUSE)), [:], null, new OrderCreateToken(followApprovalResult: new FollowApprovalResult(followOrderNo: "1"))
        )
    }

    @Unroll
    def "needOrderDetailOfApprovalFlowReuse 测试: #desc"() {
        given:
        def continueInfo = continueInfoCode == null ? null : new ContinueInfo(continueCode: continueInfoCode)
        def orderCreateRequestType = new OrderCreateRequestType(continueInfo: continueInfo)
        def strategyInfoMap = [:]
        def getOrderFoundationDataResponseType = null
        def queryHotelAuthExtensionResponseType = null

        and: "Mock静态方法"
        GroovyMock(TemplateNumberUtil, global: true)
        GroovyMock(OrderCreateProcessorOfUtil, global: true)
        TemplateNumberUtil.isNotZeroAndNull(_) >> isNotZeroAndNull
        OrderCreateProcessorOfUtil.orderIdOfApprovalFlowReuse(_, _, _, _) >> orderId

        expect:
        OrderCreateProcessorOfUtil.needOrderDetailOfApprovalFlowReuse(
                orderCreateRequestType, strategyInfoMap, getOrderFoundationDataResponseType, queryHotelAuthExtensionResponseType
        ) == expected

        where:
        desc                                 | continueInfoCode | orderId | isNotZeroAndNull | expected
        "continueCode为NOT_REUSE"            | "NOT_REUSE"      | 123L    | true             | false
        "continueCode为REUSE且orderId为0"    | "REUSE"          | 0L      | false            | false
        "continueCode为REUSE且orderId为null" | "REUSE"          | null    | false            | false
        "continueCode为其他值且orderId为0"   | "OTHER_CODE"     | 0L      | false            | false
    }


    def "needOrderDetailOfApprovalFlowReuse边界测试"() {
        given:
        def orderCreateRequestType = new OrderCreateRequestType(continueInfo: new ContinueInfo(continueCode: "NOT_REUSE"))
        def strategyInfoMap = [:]
        def getOrderFoundationDataResponseType = null
        def queryHotelAuthExtensionResponseType = null

        and: "Mock静态方法"
        GroovyMock(TemplateNumberUtil, global: true)
        GroovyMock(OrderCreateProcessorOfUtil, global: true)

        when:
        def result = OrderCreateProcessorOfUtil.needOrderDetailOfApprovalFlowReuse(
                orderCreateRequestType, strategyInfoMap, getOrderFoundationDataResponseType, queryHotelAuthExtensionResponseType
        )

        then:
        result == false
        0 * OrderCreateProcessorOfUtil.orderIdOfApprovalFlowReuse(_, _, _, _)
        0 * TemplateNumberUtil.isNotZeroAndNull(_)
    }


    @Unroll
    def "savePassengerName"() {
        given:
        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            protected static boolean needCompareSavePassengerName(ResourceToken resourceToken,
                                                                  OrderCreateRequestType orderCreateRequestType, WrapperOfAccount.AccountInfo accountInfo,
                                                                  Map<String, StrategyInfo> strategyInfoMap) {
                return true
            }
        }
        SaveCommonDataRequestType saveCommonDataRequestType = new SaveCommonDataRequestType(
                costCenter: new CostCenterInfoType(passengerCostCenterList: passengerCostCenterList)
        )
        CreateOrderRequestType createOrderRequestType = new CreateOrderRequestType(
                clientList: clientList,
        )
        ResourceToken resourceToken = new ResourceToken()
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo) {
        }
        Map<String, StrategyInfo> strategyInfoMap = new HashMap<>()
        expect:
        result == OrderCreateProcessorOfUtil.savePassengerName(saveCommonDataRequestType, createOrderRequestType, resourceToken, orderCreateRequestType, accountInfo, strategyInfoMap)
        where:
        passengerCostCenterList                                                  | clientList                                     || result
        [new PassengerCostCenterInfoType(passengerName: "name1"),
         new PassengerCostCenterInfoType(passengerName: "name2")]                | [new ClientEntity(name: "name1"),
                                                                                    new ClientEntity(name: "name2")]              || []
        [new PassengerCostCenterInfoType(passengerName: "name1"),
         new PassengerCostCenterInfoType(passengerName: "name2"),
         new PassengerCostCenterInfoType(passengerName: "name3"),
         new PassengerCostCenterInfoType(passengerName: "name4")]                | [new ClientEntity(name: "name1"),
                                                                                    new ClientEntity(name: "name2")]              || ["name3", "name4"]
        [new PassengerCostCenterInfoType(passengerName: "name1"),
         new PassengerCostCenterInfoType(passengerName: "name2")]                | [new ClientEntity(name: "name1"),
                                                                                    new ClientEntity(name: "name2"),
                                                                                    new ClientEntity(name: "name5"),
                                                                                    new ClientEntity(name: "name6")]              || ["name5", "name6"]
        [new PassengerCostCenterInfoType(passengerName: "name1"),
         new PassengerCostCenterInfoType(passengerName: "name2")]                | [new ClientEntity(name: "name3"),
                                                                                    new ClientEntity(name: "name4")]              || ["name3", "name4", "name1", "name2"]
        [new PassengerCostCenterInfoType(passengerName: "name2", passengerUid: "uid1"),
         new PassengerCostCenterInfoType(passengerName: "name1", infoId: "123")] | [new ClientEntity(name: "name1", uid: "uid1"),
                                                                                    new ClientEntity(name: "name2", infoId: 123)] || ["name1", "name2"]
    }

    @Unroll
    def "concatCnOrEnName"() {
        expect:
        result == OrderCreateProcessorOfUtil.concatCnOrEnName(lastName, firstName)
        where:
        lastName | firstName || result
        "张"     | "三"      || "张三"
        "张"     | "san"     || "张san"
        "zhang"  | "三"      || "zhang三"
        "zhang"  | "san"     || "zhang/san"
        ""       | "san"     || "san"
        "zhang"  | ""        || "zhang"
        "zhang"  | "san三"   || "zhangsan三"
        "zhang"  | "san-"    || "zhangsan-"
        "zhang"  | "san123"  || "zhangsan123"
    }

    @Unroll
    def "needQueryHotelAuthExtension分支覆盖: #desc"() {
        given:
        GroovyMock(CorpPayInfoUtil, global: true)
        CorpPayInfoUtil.isPrivate(_) >> isPrivate

        def orderCreateToken = new OrderCreateToken()
        orderCreateToken.continueTypes = containsContinueType ? [ContinueTypeConst.APPROVAL_FLOW_REUSE] : []
        orderCreateToken.metaClass.containsContinueType = { type -> containsContinueType }

        def corpPayInfo = new CorpPayInfo(corpPayType: corpPayType)
        def integrationSoaRequestType = new IntegrationSoaRequestType(sourceFrom: sourceFrom)
        def orderCreateRequestType = new OrderCreateRequestType(
                corpPayInfo: corpPayInfo,
                integrationSoaRequestType: integrationSoaRequestType
        )

        def hotelAuthDelayConfig = new HotelAuthDelayConfig(
                smartauthdelayCf: smartauthdelayCf,
                smartauthdelayCo: smartauthdelayCo
        )
        def getAuthDelayResponseType = getAuthDelayResponseTypeNull ? null :
                new GetAuthDelayResponseType(hotelAuthDelayconfig: hotelAuthDelayConfig)

        expect:
        OrderCreateProcessorOfUtil.needQueryHotelAuthExtension(
                orderCreateRequestType, getAuthDelayResponseType, orderCreateToken
                , new HashMap<String, StrategyInfo>()) == expected

        where:
        desc                                      | corpPayType | isPrivate | containsContinueType | sourceFrom         | getAuthDelayResponseTypeNull | smartauthdelayCf | smartauthdelayCo | expected
        "私有支付，直接返回false"                  | "private"   | true      | false                | SourceFrom.Online  | false                        | false            | false            | false
        "containsContinueType为true返回false"     | "public"    | false     | true                 | SourceFrom.Online  | false                        | false            | false            | false
        "Offline且smartauthdelayCf为true"         | "public"    | false     | false                | SourceFrom.Offline | false                        | true             | false            | true
        "Offline且smartauthdelayCf为false"        | "public"    | false     | false                | SourceFrom.Offline | false                        | false            | false            | false
        "Online且smartauthdelayCo为true"          | "public"    | false     | false                | SourceFrom.Online  | false                        | false            | true             | true
        "Online且smartauthdelayCo为false"         | "public"    | false     | false                | SourceFrom.Online  | false                        | false            | false            | false
        "Offline且getAuthDelayResponseType为null" | "public"    | false     | false                | SourceFrom.Offline | true                         | false            | false            | false
        "Online且getAuthDelayResponseType为null"  | "public"    | false     | false                | SourceFrom.Online  | true                         | false            | false            | false
    }

    @Unroll
    def "needSearchTripBasicInfoOfApprovalFlowReuse分支覆盖: #desc"() {
        given:
        // Mock TemplateNumberUtil.isNotZeroAndNull
        GroovyMock(TemplateNumberUtil, global: true)
        TemplateNumberUtil.isNotZeroAndNull(_) >> isNotZeroAndNull

        // 构造参数
        def checkHotelAuthExtensionResponseType = checkTripId == null ? null : new CheckHotelAuthExtensionResponseType(tripId: checkTripId)
        def originalOrderInfo = queryTripId == null ? null : new OriginalOrderInfoType(tripId: queryTripId)
        def queryHotelAuthExtensionResponseType = queryTripId == null ? null : new QueryHotelAuthExtensionResponseType(originalOrderInfo: originalOrderInfo)

        expect:
        OrderCreateProcessorOfUtil.needSearchTripBasicInfoOfApprovalFlowReuse(
                checkHotelAuthExtensionResponseType, queryHotelAuthExtensionResponseType
        ) == expected

        where:
        desc                               | checkTripId | queryTripId | isNotZeroAndNull | expected
        "checkTripId非零"                  | 123L        | null        | true             | true
        "checkTripId为零"                  | 0L          | 456L        | true             | true
        "checkTripId和queryTripId都为零"   | 0L          | 0L          | false            | false
        "checkTripId和queryTripId都为null" | null        | null        | false            | false
    }

    @Unroll
    def "searchTripIdOfApprovalFlowReuse"() {
        given:
        GroovyMock(TemplateNumberUtil, global: true)
        TemplateNumberUtil.isNotZeroAndNull(_) >> { Long val ->
            if (val == checkTripId) return checkTripIdIsNotZero
            if (val == queryTripId) return queryTripIdIsNotZero
            return false
        }

        def checkHotelAuthExtensionResponseType = checkTripId == null ? null : new CheckHotelAuthExtensionResponseType(tripId: checkTripId)
        def originalOrderInfo = queryTripId == null ? null : new OriginalOrderInfoType(tripId: queryTripId)
        def queryHotelAuthExtensionResponseType = queryTripId == null ? null : new QueryHotelAuthExtensionResponseType(originalOrderInfo: originalOrderInfo)

        expect:
        OrderCreateProcessorOfUtil.searchTripIdOfApprovalFlowReuse(
                checkHotelAuthExtensionResponseType, queryHotelAuthExtensionResponseType
        ) == expected

        where:
        desc                               | checkTripId | checkTripIdIsNotZero | queryTripId | queryTripIdIsNotZero | expected
        "checkTripId非零"                  | 123L        | true                 | 456L        | true                 | 123L
        "checkTripId为零，queryTripId非零"  | 0L          | false                | 456L        | true                 | 456L
        "checkTripId和queryTripId都为零"   | 0L          | false                | 0L          | false                | null
        "checkTripId和queryTripId都为null" | null        | false                | null        | false                | null
    }

    @Unroll
    def "needOrderDetailOfApprovalFlowReuse-分支覆盖: #desc"() {
        given:
        GroovyMock(OrderCreateProcessorOfUtil, global: true)
        GroovyMock(TemplateNumberUtil, global: true)
        OrderCreateProcessorOfUtil.orderIdOfApprovalFlowReuse(_, _, _, _) >> orderId
        TemplateNumberUtil.isNotZeroAndNull(_) >> { arg -> isNotZeroAndNullMap[arg] }

        def orderCreateRequestType = new OrderCreateRequestType()
        def strategyInfoMap = [:] as Map<String, StrategyInfo>
        def getOrderFoundationDataResponseType = new GetOrderFoundationDataResponseType()
        def queryHotelAuthExtensionResponseType = new QueryHotelAuthExtensionResponseType()

        expect:
        OrderCreateProcessorOfUtil.needOrderDetailOfApprovalFlowReuse(
                orderCreateRequestType, strategyInfoMap, getOrderFoundationDataResponseType, queryHotelAuthExtensionResponseType
        ) == expected

        where:
        desc                | orderId | isNotZeroAndNullMap | expected
        "orderId为null"     | null    | [(null): false]     | false
        "orderId为0"        | 0L      | [(0L): false]       | false
        "orderId非零-false" | 123L    | [(123L): false]     | false
    }

    @Unroll
    def "buildArtificialReuseNoOrderId分支覆盖: #desc"() {
        given:
        GroovyMock(StringUtil, global: true)
        GroovyMock(TemplateNumberUtil, global: true)
        //OrderCreateProcessorOfUtil.java里设置的
        def PRODUCT_LINE_TRIP = 10

        StringUtil.isBlank(_) >> isBlank
        TemplateNumberUtil.parseLong(_) >> { String val -> val == null ? null : Long.valueOf(val) }

        def approvalFlowReuseInput = artificialReuseNo == null ? null : new ApprovalFlowReuseInput(artificialReuseNo: artificialReuseNo)
        def orderCreateRequestType = new OrderCreateRequestType(approvalFlowReuseInput: approvalFlowReuseInput)
        def orderFoundationDataInfo = productLine == null ? null : new OrderFoundationDataInfo(productLine: productLine)
        def getOrderFoundationDataResponseType = productLine == null ? null : new GetOrderFoundationDataResponseType(orderFoundationDataInfo: orderFoundationDataInfo)

        expect:
        OrderCreateProcessorOfUtil.buildArtificialReuseNoOrderId(
                orderCreateRequestType, getOrderFoundationDataResponseType
        ) == expected

        where:
        desc                    | artificialReuseNo | isBlank | productLine || expected
        "artificialReuseNo为空" | null              | true    | null        || null
        "productLine不等于TRIP" | "67890"           | false   | 1           || 67890L
    }

    @Unroll
    def "buildArtificialReuseNoTripId分支覆盖: #desc"() {
        given:
        GroovyMock(StringUtil, global: true)
        GroovyMock(TemplateNumberUtil, global: true)
        // OrderCreateProcessorOfUtil.java 里设置的
        def PRODUCT_LINE_TRIP = 10

        StringUtil.isBlank(_) >> isBlank
        TemplateNumberUtil.parseLong(_) >> { String val -> val == null ? null : Long.valueOf(val) }

        def approvalFlowReuseInput = artificialReuseNo == null ? null : new ApprovalFlowReuseInput(artificialReuseNo: artificialReuseNo)
        def orderCreateRequestType = new OrderCreateRequestType(approvalFlowReuseInput: approvalFlowReuseInput)
        def orderFoundationDataInfo = productLine == null ? null : new OrderFoundationDataInfo(productLine: productLine)
        def getOrderFoundationDataResponseType = productLine == null ? null : new GetOrderFoundationDataResponseType(orderFoundationDataInfo: orderFoundationDataInfo)

        expect:
        OrderCreateProcessorOfUtil.buildArtificialReuseNoTripId(
                orderCreateRequestType, getOrderFoundationDataResponseType
        ) == expected

        where:
        desc                    | artificialReuseNo | isBlank | productLine || expected
        "artificialReuseNo为空" | null              | true    | null        || null
        "productLine不等于TRIP" | "67890"           | false   | 1           || null
        "productLine等于TRIP"   | "12345"           | false   | 10          || 12345L
    }

    @Unroll
    def "buildApprovalReuseAiBookOrderId分支覆盖: #desc"() {
        given:
        def orderCreateRequestType = new OrderCreateRequestType()
        def strategyInfoMap = [:] as Map<String, StrategyInfo>
        def originalOrderInfo = originalOrderId == null ? null : new OriginalOrderInfoType(originalOrderId: originalOrderId)
        def queryHotelAuthExtensionResponseType = queryHotelAuthExtensionResponseTypeNull ? null : new QueryHotelAuthExtensionResponseType(originalOrderInfo: originalOrderInfo)

        expect:
        OrderCreateProcessorOfUtil.buildApprovalReuseAiBookOrderId(
                orderCreateRequestType, strategyInfoMap, queryHotelAuthExtensionResponseType
        ) == expected

        where:
        desc                                        | queryHotelAuthExtensionResponseTypeNull | originalOrderId || expected
        "queryHotelAuthExtensionResponseType为null" | true                                    | null            || null
        "originalOrderInfo为null"                   | false                                   | null            || null
        "originalOrderId为null"                     | false                                   | null            || null
        "originalOrderId有值"                       | false                                   | 12345L          || 12345L
    }

    @Unroll
    def "buildApprovalReuseReBookOrderId分支覆盖: #desc"() {
        given:
        GroovyMock(StrategyOfBookingInitUtil, global: true)
        GroovyMock(TemplateNumberUtil, global: true)
        StrategyOfBookingInitUtil.approvalReuseReBook(_) >> approvalReuseReBook
        TemplateNumberUtil.parseLong(_) >> { String val -> val == null ? null : Long.valueOf(val) }

        def originalOrderInput = originalOrderId == null ? null : new OriginalOrderInput(originalOrderId: originalOrderId)
        def orderCreateRequestType = new OrderCreateRequestType(originalOrderInput: originalOrderInput)
        def strategyInfoMap = [:] as Map<String, StrategyInfo>

        expect:
        OrderCreateProcessorOfUtil.buildApprovalReuseReBookOrderId(
                orderCreateRequestType, strategyInfoMap
        ) == expected

        where:
        desc                                | approvalReuseReBook | originalOrderId || expected
        "approvalReuseReBook为false"        | false               | "12345"         || null
        "approvalReuseReBook为true且id为空" | true                | null            || null
    }

    @Unroll
    def "needGetOrderFoundationData分支覆盖: #desc"() {
        given:
        GroovyMock(StringUtil, global: true)
        StringUtil.isBlank(_) >> isBlank

        def approvalFlowReuseInput = artificialReuseNo == null ? null : new ApprovalFlowReuseInput(artificialReuseNo: artificialReuseNo)
        def orderCreateRequestType = new OrderCreateRequestType(approvalFlowReuseInput: approvalFlowReuseInput)

        expect:
        OrderCreateProcessorOfUtil.needGetOrderFoundationData(orderCreateRequestType) == expected

        where:
        desc                        | artificialReuseNo | isBlank || expected
        "artificialReuseNo为空"     | null              | true    || false
        "artificialReuseNo空字符串" | ""                | true    || false
        "artificialReuseNo有值"     | "12345"           | false   || true
    }

    @Unroll
    def "approvalFlowReuseNew分支覆盖: #desc"() {
        given:
        GroovyMock(StringUtil, global: true)
        GroovyMock(BooleanUtil, global: true)
        // GroovyMock(StrategyOfBookingInitUtil, global: true)

        // StrategyOfBookingInitUtil.approvalReuseReBook(_ as Map<String, StrategyInfo>) >> approvalReuseReBook
        StrategyOfBookingInitUtil.approvalFlowReuseNew(_ as Map<String, StrategyInfo>) >> approvalFlowReuseNew
        // approvalReuseReBook(approvalReuseReBook)

        def originalOrderInput = originalOrderId == null ? null : new OriginalOrderInput(originalOrderId: originalOrderId)
        def approvalFlowReuseInput = new ApprovalFlowReuseInput(
                artificialReuseNo: artificialReuseNo,
                aiReuse: aiReuse
        )
        def userInfo = new UserInfo(pos: pos)
        def integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: userInfo)
        def orderCreateRequestType = new OrderCreateRequestType(
                originalOrderInput: originalOrderInput,
                approvalFlowReuseInput: approvalFlowReuseInput,
                integrationSoaRequestType: integrationSoaRequestType
        )
        def strategyInfoMap = [:] as Map<String, StrategyInfo>
        def orderCreateToken = new OrderCreateToken()
        orderCreateToken.metaClass.containsContinueType = { type -> containsContinueType }

        expect:
        OrderCreateProcessorOfUtil.approvalFlowReuseNew(
                orderCreateRequestType, strategyInfoMap, orderCreateToken
        ) == expected

        where:
        desc                  | approvalReuseReBook | artificialReuseNo   | aiReuse | pos           | approvalFlowReuseNew | containsContinueType | originalOrderId | expected
        "人工沿用"            | false               | "artificialReuseNo" | "F"     | PosEnum.CHINA | false                | false                | null            | true
        "offline智能推荐沿用" | false               | null                | "T"     | PosEnum.CHINA | false                | false                | null            | true
        // "重新预订"            | true                | null                | "F"     | PosEnum.CHINA | false                | false                | "123"           | true
        "蓝色空间智能沿用"    | false               | null                | "F"     | PosEnum.JAPAN | false                | false                | null            | true
        // "国内站智能沿用"      | false               | null                | "F"     | PosEnum.CHINA | true                 | false                | null            | true
    }

    def approvalReuseReBook(approvalReuseReBook) {
        new MockUp<StrategyOfBookingInitUtil>() {
            @Mock
            public static boolean approvalReuseReBook(Map<String, StrategyInfo> strategyInfoMap) {
                return approvalReuseReBook
            }
        }
    }


    @Unroll
    def "needGetCityBaseInfoResponseTypeOfApprovalFlowReuse分支覆盖: #desc"() {
        given:
        GroovyMock(TemplateNumberUtil, global: true)
        TemplateNumberUtil.isNotZeroAndNull(_) >> isNotZeroAndNull

        def hotelAreaInfo = cityId == null ? null : new HotelAreaInfoType(cityId: cityId)
        def hotelInfo = hotelAreaInfo == null && cityId == null ? null : new HotelInfoType(hotelAreaInfo: hotelAreaInfo)
        def orderDetailInfoType = hotelInfo == null && cityId == null ? null : new OrderDetailInfoType(hotelInfo: hotelInfo)

        expect:
        OrderCreateProcessorOfUtil.needGetCityBaseInfoResponseTypeOfApprovalFlowReuse(orderDetailInfoType) == expected

        where:
        desc                        | cityId | isNotZeroAndNull || expected
        "orderDetailInfoType为null" | null   | false            || false
        "hotelInfo为null"           | null   | false            || false
        "cityId为null"              | null   | false            || false
        "cityId为0"                 | 0      | false            || false
        "cityId非零"                | 123    | true             || true
    }

    @Unroll
    def "buildOrderDetailInfoType分支覆盖: #desc"() {
        given:
        def orderDetailResponseTypeOfApprovalFlowReuse = Mock(OrderDetailResponseType) {
            getOrderDetailInfoList() >> orderDetailListOrNull
        }

        // Mock静态方法
        GroovyMock(OrderCreateProcessorOfUtil, global: true)
        OrderCreateProcessorOfUtil.buildApprovalReuseReBookOrderId(_, _) >> approvalReuseReBookOrderId
        OrderCreateProcessorOfUtil.buildArtificialReuseNoOrderId(_, _) >> approvalArtificialReuseNoOrderId
        OrderCreateProcessorOfUtil.buildApprovalReuseAiBookOrderId(_, _, _) >> approvalReuseAiBookOrderId

        GroovyMock(TemplateNumberUtil, global: true)
        TemplateNumberUtil.isNotZeroAndNull(_) >> { it != null && it != 0L }

        def checkHotelAuthExtensionResponseType = checkHotelAuthExtensionOrderId == null ? null : Mock(CheckHotelAuthExtensionResponseType) {
            getOrderId() >> checkHotelAuthExtensionOrderId
        }

        when:
        def result = OrderCreateProcessorOfUtil.buildOrderDetailInfoType(
                Mock(OrderCreateRequestType), [:], Mock(GetOrderFoundationDataResponseType),
                checkHotelAuthExtensionResponseType, orderDetailResponseTypeOfApprovalFlowReuse, Mock(QueryHotelAuthExtensionResponseType)
        )

        then:
        (result?.getOrderId() ?: null) == expectedOrderId

        where:
        desc                                               | orderDetailListOrNull                                                                                | approvalReuseReBookOrderId | approvalArtificialReuseNoOrderId | checkHotelAuthExtensionOrderId | approvalReuseAiBookOrderId | expectedOrderId
        "orderDetailResponseTypeOfApprovalFlowReuse为null" | null                                                                                                 | null                       | null                             | null                           | null                       | null
        "orderDetailInfoList为空"                          | []                                                                                                   | null                       | null                             | null                           | null                       | null
        "都不命中返回null"                                 | [Mock(OrderDetailInfoType) { getOrderId() >> 1L }, Mock(OrderDetailInfoType) { getOrderId() >> 2L }] | null                       | null                             | null                           | null                       | null
    }

    @Unroll
    def "needPolicyGetCorpUserInfoResponseTypeOfApprovalFlowReuse 测试: orderId=#orderId, isNotZeroAndNull=#isNotZeroAndNull, expected=#expected"() {
        given:
        def orderCreateRequestType = new OrderCreateRequestType()
        def strategyInfoMap = [:]
        def getOrderFoundationDataResponseType = null
        def queryHotelAuthExtensionResponseType = null

        and: "Mock静态方法"
        GroovyMock(TemplateNumberUtil, global: true)
        GroovyMock(OrderCreateProcessorOfUtil, global: true)
        TemplateNumberUtil.isNotZeroAndNull(orderId) >> isNotZeroAndNull
        OrderCreateProcessorOfUtil.orderIdOfApprovalFlowReuse(_, _, _, _) >> orderId

        expect:
        OrderCreateProcessorOfUtil.needPolicyGetCorpUserInfoResponseTypeOfApprovalFlowReuse(
                orderCreateRequestType, strategyInfoMap, getOrderFoundationDataResponseType, queryHotelAuthExtensionResponseType
        ) == expected

        where:
        orderId | isNotZeroAndNull | expected
        456L    | false            | false    // orderId非零但isNotZeroAndNull返回false
        0L      | false            | false    // orderId为0
        -1L     | false            | false    // orderId为负数
        null    | false            | false    // orderId为null
        100L    | false            | false    // orderId非零但isNotZeroAndNull返回false
    }

    @Unroll
    def "test orderIdOfApprovalFlowReuse"() {
        given:
        def orderCreateRequestType = new OrderCreateRequestType()
        def strategyInfoMap = [:]
        def getOrderFoundationDataResponseType = new GetOrderFoundationDataResponseType()
        def queryHotelAuthExtensionResponseType = queryHotelAuthExtensionResponseType_

        and: "Mock静态方法"
        GroovyMock(TemplateNumberUtil, global: true)
        GroovyMock(OrderCreateProcessorOfUtil, global: true)

        // Mock buildApprovalReuseReBookOrderId
        OrderCreateProcessorOfUtil.buildApprovalReuseReBookOrderId(_, _) >> approvalReuseReBookOrderId
        TemplateNumberUtil.isNotZeroAndNull(approvalReuseReBookOrderId) >> approvalReuseReBookOrderIdIsNotZero

        // Mock buildArtificialReuseNoOrderId
        OrderCreateProcessorOfUtil.buildArtificialReuseNoOrderId(_, _) >> artificialReuseNoOrderId
        TemplateNumberUtil.isNotZeroAndNull(artificialReuseNoOrderId) >> artificialReuseNoOrderIdIsNotZero

        // Mock queryOrderId
        TemplateNumberUtil.isNotZeroAndNull(queryOrderId) >> queryOrderIdIsNotZero

        expect:
        OrderCreateProcessorOfUtil.orderIdOfApprovalFlowReuse(
                orderCreateRequestType, strategyInfoMap, getOrderFoundationDataResponseType, queryHotelAuthExtensionResponseType
        ) == expected

        where:
        desc                                        | approvalReuseReBookOrderId | approvalReuseReBookOrderIdIsNotZero | artificialReuseNoOrderId | artificialReuseNoOrderIdIsNotZero | queryHotelAuthExtensionResponseType_                                                                       | queryOrderId | queryOrderIdIsNotZero | expected
        "都为零，返回null"                           | 0L                         | false                               | 0L                       | false                             | new QueryHotelAuthExtensionResponseType(originalOrderInfo: new OriginalOrderInfoType(originalOrderId: 0L)) | 0L           | false                 | null
        "queryHotelAuthExtensionResponseType为null" | 0L                         | false                               | 0L                       | false                             | null                                                                                                       | null         | false                 | null
        "originalOrderInfo为null"                   | 0L                         | false                               | 0L                       | false                             | new QueryHotelAuthExtensionResponseType(originalOrderInfo: null)                                           | null         | false                 | null
    }

    def "buildOrderDetailInfoType 测试orderDetailResponseTypeOfApprovalFlowReuse为null"() {
        given:
        def orderCreateRequestType = new OrderCreateRequestType()
        def strategyInfoMap = [:]
        def getOrderFoundationDataResponseType = new GetOrderFoundationDataResponseType()
        def checkHotelAuthExtensionResponseType = new CheckHotelAuthExtensionResponseType()
        def orderDetailResponseTypeOfApprovalFlowReuse = null
        def queryHotelAuthExtensionResponseType = new QueryHotelAuthExtensionResponseType()

        expect:
        OrderCreateProcessorOfUtil.buildOrderDetailInfoType(
                orderCreateRequestType, strategyInfoMap, getOrderFoundationDataResponseType,
                checkHotelAuthExtensionResponseType, orderDetailResponseTypeOfApprovalFlowReuse, queryHotelAuthExtensionResponseType
        ) == null
    }

    def "buildOrderDetailInfoType 测试orderDetailInfoList为空"() {
        given:
        def orderCreateRequestType = new OrderCreateRequestType()
        def strategyInfoMap = [:]
        def getOrderFoundationDataResponseType = new GetOrderFoundationDataResponseType()
        def checkHotelAuthExtensionResponseType = new CheckHotelAuthExtensionResponseType()
        def orderDetailResponseTypeOfApprovalFlowReuse = new OrderDetailResponseType(orderDetailInfoList: [])
        def queryHotelAuthExtensionResponseType = new QueryHotelAuthExtensionResponseType()

        expect:
        OrderCreateProcessorOfUtil.buildOrderDetailInfoType(
                orderCreateRequestType, strategyInfoMap, getOrderFoundationDataResponseType,
                checkHotelAuthExtensionResponseType, orderDetailResponseTypeOfApprovalFlowReuse, queryHotelAuthExtensionResponseType
        ) == null
    }

    def "buildOrderDetailInfoType 测试orderDetailInfoList为null"() {
        given:
        def orderCreateRequestType = new OrderCreateRequestType()
        def strategyInfoMap = [:]
        def getOrderFoundationDataResponseType = new GetOrderFoundationDataResponseType()
        def checkHotelAuthExtensionResponseType = new CheckHotelAuthExtensionResponseType()
        def orderDetailResponseTypeOfApprovalFlowReuse = new OrderDetailResponseType(orderDetailInfoList: null)
        def queryHotelAuthExtensionResponseType = new QueryHotelAuthExtensionResponseType()

        expect:
        OrderCreateProcessorOfUtil.buildOrderDetailInfoType(
                orderCreateRequestType, strategyInfoMap, getOrderFoundationDataResponseType,
                checkHotelAuthExtensionResponseType, orderDetailResponseTypeOfApprovalFlowReuse, queryHotelAuthExtensionResponseType
        ) == null
    }

    @Unroll
    def "savePassengerName-data"() {
        given:
        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            protected static boolean needCompareSavePassengerName(ResourceToken resourceToken,
                                                                  OrderCreateRequestType orderCreateRequestType, WrapperOfAccount.AccountInfo accountInfo,
                                                                  Map<String, StrategyInfo> strategyInfoMap) {
                return true
            }
        }
        SaveCommonDataRequestType saveCommonDataRequestType = JsonUtil.fromJson("{\"orderId\":****************,\"productLine\":\"C\",\"uid\":\"**********\",\"corpId\":\"testnet\",\"policyUid\":\"**********\",\"serverFrom\":\"*********\",\"language\":\"zh-CN\",\"orderSource\":\"Online\",\"costCenter\":{\"costCenterType\":\"P\",\"costCenterList\":[],\"costCenterExtendInfo\":{},\"passengerCostCenterList\":[{\"passengerName\":\"韩飞龙\",\"passengerUid\":\"**********\",\"costCenterList\":[{\"level\":1,\"costCenterValue\":\"商旅事业部\",\"costCenterNo\":\"商旅事业部\"}]}]},\"travelControlPreApprovalList\":[],\"attachmentInfoList\":[{\"attachmentName\":\"1iq1z12000jbbb2u55987.png\",\"attachmentUrl\":\"https://dimg04.c-ctrip.com/images/1iq1z12000jbbb2u55987.png\",\"attachmentType\":\"Approval\"}],\"extend\":{\"scene\":\"B\",\"ssoKey\":\"68a489f0-67c4-11f0-b777-2709e601f219\",\"approvalInfo\":{\"externalId\":\"d7df3e29-8c34-444d-938f-cbd73794f578\",\"approvalPassWord\":\"\",\"userList\":[{\"uid\":\"**********\",\"level\":1,\"byUser\":true},{\"uid\":\"**********\",\"level\":1,\"byUser\":true}]}},\"reservationInfo\":{\"type\":\"EmployeeTravel\"}}", SaveCommonDataRequestType.class)
        CreateOrderRequestType createOrderRequestType = JsonUtil.fromJson("{\"baseInfo\":{\"wsId\":\"da82dd1b-3492-4ac3-bbd0-67c8021071bf\",\"serverFrom\":\"ct.ctrip.com\",\"userSettings\":{\"travelPolicyControlMode\":\"C\",\"bookRoomMode\":\"NORMAL\"},\"locale\":\"zh-CN\",\"channel\":\"Online\",\"secondaryChannel\":\"Other-new\",\"teamRoom\":false,\"pos\":\"zh-CN\",\"platformType\":\"Online\"},\"roomInfo\":{\"roomQuantity\":2,\"guestQuantity\":2,\"latestArrivalTimeUTC\":\"2025-08-12T22:00:00Z\",\"earliestTimeUTC\":\"2025-08-12T06:00:00Z\",\"markUpFlag\":false,\"flashOrder\":false,\"latestCancelLocalTime\":\"2025-08-11 12:00:00\",\"latestCheckInLocalTime\":\"2025-08-13 06:00:00\",\"earliestCheckInLocalTime\":\"2025-08-12 14:00:00\"},\"clientList\":[{\"name\":\"韩飞龙\",\"uid\":\"**********\",\"infoId\":0,\"employeeID\":\"skskdk\",\"employee\":true,\"externalTraveller\":false,\"shareOrderAmount\":500.00,\"mobilePhone\":\"18817570781\",\"earnPoints\":false,\"countryCode\":\"86\",\"lastName\":\"\",\"fullName\":\"\",\"clientId\":\"**********\",\"allocationSettlementAmount\":500.00,\"allocationOriginAmount\":500.00,\"nationality\":\"CN\",\"mainGuest\":false,\"gender\":\"U\"},{\"name\":\"杨之恺\",\"uid\":\"2120593229\",\"infoId\":0,\"employeeID\":\"2120593229\",\"employee\":true,\"externalTraveller\":false,\"shareOrderAmount\":500.00,\"mobilePhone\":\"13395765723\",\"earnPoints\":false,\"countryCode\":\"86\",\"lastName\":\"\",\"fullName\":\"\",\"clientId\":\"2120593229\",\"allocationSettlementAmount\":500.00,\"allocationOriginAmount\":500.00,\"nationality\":\"CN\",\"mainGuest\":false,\"gender\":\"U\"}],\"contactorInfo\":{\"confirmType\":\"Email\",\"name\":\"商旅客户\",\"email\":\"<EMAIL>\",\"mobilePhone\":\"18817570781\",\"mobilePhoneCountryCode\":\"86\"},\"priceInfo\":{\"cNYAmount\":1000.00,\"originCurrency\":\"CNY\",\"originExchange\":1,\"originAmount\":1000.00,\"customCurrency\":\"CNY\",\"customExchange\":1,\"customAmount\":1000.00},\"corpOrderInfo\":{\"travelPolicyInfo\":{\"mustMatchBookTP\":false},\"policyToken\":\"v1KLUv/QBQ5QkA5pM/JDCp6AHEPcTYt01ji45wL0VXqPgk863PDIgYIP7TxVQKGgoqODIANwA2AOtNmb9rv/2n/nULH4nOt2sRDWutNYZp1i2M1S3ld1TOoNhjtIsxAMYYY9z2ixSiOFqGbil+VAwxfWqgK323LaE8YfSLFdqOhlU+j7u2NRDBYEADI90sjAPbwphzWA4JyJbmNIcExGnrAl5npPiWwLqQMi4kVyaVJN4lZXW3P8WC6kU5oY+OEM158FNXyeQ9N34aeE8SKHMunN+uniRHAg0L23atwK5N7FrEKriVXWH68Ac+/yqcVKJcQbV8f6YpycgEHyNIdTi/iiEqNhRNoZYQ9g4WADkbR2OHs7XDfHPx1GI0hhCm5qChCqkLmCKoQ5WqWSxU5Twr8u/PYq9ikzCqqQI6N6AU1ZkudO4cAw\\u003d\\u003d\",\"bookRCInfo\":{},\"allocationInfo\":{\"allocationMode\":\"ORDER\",\"allocationFlag\":true,\"allocationTotalAmount\":1000.00},\"sendMsg\":true,\"sendNotificationEmailToGuest\":false,\"approvalBudgetInfo\":{\"approvalBudgetVerifyId\":\"\",\"approvalTimesVerifyId\":\"\"}},\"remarkInfo\":{\"customRemark\":\"\",\"optionalRemarkList\":[]},\"paymentInfo\":{\"prepayType\":\"ACCNT\",\"corpPayInfo\":{}},\"extInfo\":{\"ssoInfo\":{\"costCenterFromSsoInfo\":false},\"aid\":\"\",\"sid\":\"\",\"orderTagList\":[{\"key\":\"NeedNotSaveCommonData\",\"value\":\"T\"},{\"key\":\"SERVICE_FEE_V2\",\"value\":\"T\"}],\"externalId\":\"d7df3e29-8c34-444d-938f-cbd73794f578\",\"customerProperty\":{}},\"deviceInfo\":{\"userIP\":\"***************\",\"deviceID\":\"09031088319758937635\",\"rmsToken\":\"fp\\u003d2F679A-EB4072-2C0B7D\\u0026vid\\u003d*************.3d58S7au7lXs\\u0026pageId\\u003d10650153220\\u0026r\\u003d3034664b850a4d459eb64156db397777\\u0026ip\\u003d***************\\u0026rg\\u003dfin\\u0026kpData\\u003d0_0_0\\u0026kpControl\\u003d0_0_0-0_0_0\\u0026kpEmp\\u003d0_0_0_0_0_0_0_0_0_0-0_0_0_0_0_0_0_0_0_0-0_0_0_0_0_0_0_0_0_0\\u0026screen\\u003d2560x1440\\u0026tz\\u003d+8\\u0026blang\\u003dzh-CN\\u0026oslang\\u003dzh-CN\\u0026ua\\u003dMozilla%2F5.0%20(Windows%20NT%2010.0%3B%20Win64%3B%20x64)%20AppleWebKit%2F537.36%20(KHTML%2C%20like%20Gecko)%20Chrome%2F138.0.0.0%20Safari%2F537.36\\u0026d\\u003dct.ctrip.com\\u0026v\\u003d25\\u0026kpg\\u003d0_0_0_0_0_0_0_0_0_0\\u0026adblock\\u003dF\\u0026cck\\u003dF\\u0026ftoken\\u003d\",\"vid\":\"*************.3d58S7au7lXs\"},\"insuranceInfo\":{\"insuranceBackToken\":\"WEB_1_windows_1753282146549_912_912_1.5_mbu_corp_hotel_online_booking_fc408c65d10a451a_5.4.3_1\"}}", CreateOrderRequestType.class)
        ResourceToken resourceToken = new ResourceToken()
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo) {
        }
        Map<String, StrategyInfo> strategyInfoMap = new HashMap<>()
        when:
        def result = OrderCreateProcessorOfUtil.savePassengerName(saveCommonDataRequestType, createOrderRequestType, resourceToken, orderCreateRequestType, accountInfo, strategyInfoMap)
        then:
        result != null
    }

    def "needGetPackageRoomList should throw NullPointerException when checkAvailInfo is null"() {
        when: "checkAvailInfo is null"
        OrderCreateProcessorOfUtil.needGetPackageRoomList(null)

        then: "should throw NullPointerException"
        thrown(NullPointerException)
    }

    def "needGetPackageRoomList should return true when packageId is positive"() {
        given: "a CheckAvailContextInfo with positive packageId"
        def checkAvailInfo = Mock(WrapperOfCheckAvail.CheckAvailContextInfo)
        checkAvailInfo.getPackageId() >> packageId

        when: "calling needGetPackageRoomList"
        def result = OrderCreateProcessorOfUtil.needGetPackageRoomList(checkAvailInfo)

        then: "should return true without checking aggPackageToken"
        result
        0 * checkAvailInfo.getAggPackageToken()

        where:
        packageId << [1, 999, Integer.MAX_VALUE]
    }

    def "needGetPackageRoomList should check aggPackageToken when packageId is not positive"() {
        given: "a CheckAvailContextInfo with non-positive packageId"
        def checkAvailInfo = Mock(WrapperOfCheckAvail.CheckAvailContextInfo)
        checkAvailInfo.getPackageId() >> packageId
        checkAvailInfo.getAggPackageToken() >> aggPackageToken

        when: "calling needGetPackageRoomList"
        def result = OrderCreateProcessorOfUtil.needGetPackageRoomList(checkAvailInfo)

        then: "should return expected result based on aggPackageToken"
        result == expectedResult

        where:
        packageId | aggPackageToken  | expectedResult | description
        null      | "validToken"     | true           | "null packageId, valid token"
        null      | null             | false          | "null packageId, null token"
        null      | ""               | false          | "null packageId, empty token"
        null      | "   "            | false          | "null packageId, blank token"
        0         | "validToken"     | true           | "zero packageId, valid token"
        0         | null             | false          | "zero packageId, null token"
        0         | ""               | false          | "zero packageId, empty token"
        0         | "  \t\n  "       | false          | "zero packageId, whitespace token"
        0         | "  validToken  " | true           | "zero packageId, token with spaces"
        -1        | "validToken"     | true           | "negative packageId, valid token"
        -1        | null             | false          | "negative packageId, null token"
        -10       | ""               | false          | "negative packageId, empty token"
    }


    def "needGetInboundParameter - complete path coverage"() {
        given: "mock objects for testing"
        def mockOrderType = Mock(OrderCreateRequestType)
        def mockOfflineInfo = Mock(OfflineInfo)
        mockOrderType.getOfflineInfo() >> mockOfflineInfo
        mockOfflineInfo.getCookieId() >> "validCookie"

        when: "calling needGetInboundParameter"
        def result = OrderCreateProcessorOfUtil.needGetInboundParameter(mockOrderType)

        then: "should return true for valid cookie"
        result
    }
}