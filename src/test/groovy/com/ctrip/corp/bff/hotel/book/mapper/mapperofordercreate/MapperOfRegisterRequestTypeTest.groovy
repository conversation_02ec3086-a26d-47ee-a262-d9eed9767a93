package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.template.entity.TemplateSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.contract.integration.EmailInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PhoneInfo
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import com.ctrip.corp.bff.hotel.book.contract.RegisterInputInfo
import mockit.internal.state.SavePoint
import spock.lang.Specification

class MapperOfRegisterRequestTypeTest extends Specification {
    def tester = Spy(MapperOfRegisterRequestType)

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "buildCustomerInfo" () {
        given:
        tester.getLocalName(_) >> new String[] {"first", "second"}
        tester.getNameEN(_) >> new String[] {"1", "2"}
        expect:
        with(tester.buildCustomerInfo(new RegisterInputInfo(emailInfo: new EmailInfo(transferEmail: "email"), phoneInfo: new PhoneInfo(transferPhoneNo: "phone")), "uid")) {
            uID == "uid"
            surnameCN == "first"
            givenNameCN == "second"
            surname == "1"
            givenName == "2"
            email == "email"
            mobilePhone == "phone"
        }

    }


    def "getLocalName"() {
        expect:
        tester.getLocalName(name) == res
        where:
        name         || res
        null         || null
        ""           || null
        "s"          || null
        "中"          || null
        "中国"         || new String[]{"中", "国"}
        "first/last" || new String[]{"first", "last"}
    }

    def "getNameEN"() {
        expect:
        tester.getNameEN(name) == res
        where:
        name         || res
        ""           || null
        null         || null
        "中"          || null
        "enname"     || null
        "first/last" || new String[]{"first", "last"}
    }

    def "isChinese"() {
        expect:
        tester.isChinese(str) == res
        where:
        str || res
        ""  || false
        "s" || false
        "中" || true
        "2" || false
    }
}
