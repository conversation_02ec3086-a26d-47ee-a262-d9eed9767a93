package com.ctrip.corp.bff.hotel.book.mapper.mapperofapprovaldetailsearch

import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3
import com.ctrip.corp.bff.hotel.book.contract.ApprovalDetailSearchRequestType
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryResponseType
import com.ctrip.corp.bff.specific.contract.BatchApprovalDefaultRequestType
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2024-12-11
 * */
class MapperOfBatchApprovalDefaultRequestByDetailTest extends Specification {

    def mapper = new MapperOfBatchApprovalDefaultRequestByDetail()
    def accountInfo = Mock(WrapperOfAccount.AccountInfo.class)

    def savePoint = new SavePoint()

    def setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert"() {
        given:
        accountInfo.isOaApprovalHead() >> true
        def approvalDetailSearchRequest = new ApprovalDetailSearchRequestType()
        def ssoInfoQueryResponseType = new SSOInfoQueryResponseType()
        def param = Tuple3.of(approvalDetailSearchRequest, accountInfo, ssoInfoQueryResponseType)

        when:
        def result = mapper.convert(param)

        then:
        result != null
        result instanceof BatchApprovalDefaultRequestType
    }

    def "check"() {
        given:
        def approvalDetailSearchRequest = new ApprovalDetailSearchRequestType()
        def accountInfo = new WrapperOfAccount.AccountInfo()
        def ssoInfoQueryResponseType = new SSOInfoQueryResponseType()
        def param = Tuple3.of(approvalDetailSearchRequest, accountInfo, ssoInfoQueryResponseType)

        when:
        def result = mapper.check(param)

        then:
        result == null
    }

    def "convertScene"() {
        given:
        accountInfo.isOaApprovalHead() >> true

        when:
        def result = mapper.convertScene(accountInfo)

        then:
        result == "PRE_POSITION"
    }

    def "convertScene_post"() {
        when:
        accountInfo.isOaApprovalHead() >> false
        def result = mapper.convertScene(accountInfo)

        then:
        result == "POST_POSITION"
    }
}
