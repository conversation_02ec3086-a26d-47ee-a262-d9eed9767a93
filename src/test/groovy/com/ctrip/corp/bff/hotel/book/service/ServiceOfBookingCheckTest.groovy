package com.ctrip.corp.bff.hotel.book.service

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import mockit.internal.state.SavePoint
import spock.lang.Specification

class ServiceOfBookingCheckTest extends Specification {

    def tester = Spy(new ServiceOfBookingCheck())

    def savePoint = new SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "validateRequest" () {
        given:

        when:
        tester.validateRequest(req)
        throw new Success()

        then:
        thrown(ex)
        where:
        req | ex
        new BookingCheckRequestType() | BusinessException
        new BookingCheckRequestType(cityInput: new CityInput(cityId: 2)) | BusinessException
        new BookingCheckRequestType(hotelBookInput: new HotelBookInput(hotelDateRangeInfo: new HotelDateRangeInfo()),cityInput: new CityInput(cityId: 2)) | BusinessException
        new BookingCheckRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(sourceFrom: SourceFrom.Offline),hotelBookInput: new HotelBookInput(hotelDateRangeInfo: new HotelDateRangeInfo(checkIn: "2025-01-01", checkOut: "2025-01-03")),cityInput: new CityInput(cityId: 2)) | Success
    }

    class Success extends Exception {}

}
