package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.corp.order.service.orderfoundationcentercostinfoservice.SaveOrderCostCenterRequestType
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookHotelInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookRoomInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryBookingRulesType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextRequestType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType
import com.ctrip.corp.bff.framework.hotel.common.util.WaitFutureUtil
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ReservationResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.specific.common.entity.costcenter.old.SaveCostCenterInputItem
import com.ctrip.corp.bff.framework.specific.common.entity.costcenter.old.SaveCostCenterInputVo
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.hotel.book.common.enums.CostCenterResourceEnum
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfSaveCostCenter
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.sharkmock.SharkMockUtil
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryResponseType
import com.ctrip.corp.bff.specific.contract.ApprovalTextInfoResponseType
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType
import com.ctrip.ibu.platform.shark.sdk.api.Shark
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType
import corp.user.service.costcenterService.matchCostCenter.CostCenterExtItemType
import corp.user.service.costcenterService.matchCostCenter.CostCenterInfoType
import corp.user.service.costcenterService.matchCostCenter.MatchCostCenterResponseType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2024/12/27 10:59
 *
 */
class MapperOfSaveOrderCostCenterRequestTypeTest extends Specification {

    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }

    MapperOfSaveOrderCostCenterRequestType mapper = new MapperOfSaveOrderCostCenterRequestType()

    @Unroll
    def "testBuildSaveOrderCostCenterRequest with different scenarios"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                cityInput: new CityInput(cityId: 1),
                hotelBookPassengerInputs: [new HotelBookPassengerInput(
                        hotelPassengerInput: new HotelPassengerInput(employee: "F", uid: "uid3", infoId: "infoId3"))],
                corpPayInfo: new CorpPayInfo(corpPayType: "public"),
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "userId")))
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("RepeatOrderControlCorp", "F")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(new QueryCheckAvailContextResponseType(
                                hotelInfo: new BookHotelInfoEntity(),
                                roomInfo: new BookRoomInfoEntity(balanceType: "PP"),
                                bookingRules: new QueryBookingRulesType()))
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsId")))
                        .build().getCheckAvailContextInfo()
        OrderCreateToken orderCreateToken = new OrderCreateToken()
        CreateOrderResponseType createOrderResponseType = new CreateOrderResponseType()
        CostCenterInfoType costCenterInfoType = new CostCenterInfoType()
        ApprovalTextInfoResponseType approvalTextInfoResponseType = new ApprovalTextInfoResponseType()
        SSOInfoQueryResponseType ssoInfoQueryResponseType = new SSOInfoQueryResponseType()
        ResourceToken resourceToken = new ResourceToken()

        when:
        SaveOrderCostCenterRequestType result = mapper.buildSaveOrderCostCenterRequest(orderCreateRequestType, accountInfo, checkAvailInfo, orderCreateToken, createOrderResponseType, costCenterInfoType, approvalTextInfoResponseType, ssoInfoQueryResponseType, resourceToken, null, null)

        then:
        result != null
        result.getSaveList().size() == expectedSaveListSize

        where:
        costCenterJsonString                                                                  || expectedSaveListSize
        '{"items": {"fdefault": {"journeyNo": "journey123"}}}'                                || 1
        '{"items": {"fdefault": {"journeyNo": "journey456"}, "other": {"journeyNo": "789"}}}' || 1
        '{"items": {}}'                                                                       || 1
    }

    @Unroll
    def "testBuildSaveOrderCostCenterRequest with different"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                cityInput: new CityInput(cityId: 1),
                hotelBookPassengerInputs: [new HotelBookPassengerInput(
                        hotelPassengerInput: new HotelPassengerInput(employee: "F", uid: "uid3", infoId: "infoId3"))],
                corpPayInfo: new CorpPayInfo(corpPayType: "public"),
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "userId")))
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("RepeatOrderControlCorp", "F")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(new QueryCheckAvailContextResponseType(
                                hotelInfo: new BookHotelInfoEntity(),
                                roomInfo: new BookRoomInfoEntity(balanceType: "PP"),
                                bookingRules: new QueryBookingRulesType()))
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsId")))
                        .build().getCheckAvailContextInfo()
        OrderCreateToken orderCreateToken = new OrderCreateToken()
        WrapperOfSaveCostCenter wrapperOfSaveCostCenter =
                WrapperOfSaveCostCenter.builder().setOrderCreateRequestType(orderCreateRequestType)
                        .setAccountInfo(accountInfo).setCheckAvailInfo(checkAvailInfo).setOrderCreateToken(orderCreateToken)
                        .setCreateOrderResponseType(new CreateOrderResponseType())
                        .setApprovalTextInfoResponseType(null)
                        .setSsoInfoQueryResponseType(null)
                        .setMatchCostCenterResponseType(new MatchCostCenterResponseType())
                        .setResourceToken(new ResourceToken()).build();

        when:
        SaveOrderCostCenterRequestType result = mapper.convert(com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1.of(wrapperOfSaveCostCenter))

        then:
        result != null
        result.getSaveList().size() == expectedSaveListSize

        where:
        costCenterJsonString                                                                  || expectedSaveListSize
        '{"items": {"fdefault": {"journeyNo": "journey123"}}}'                                || 1
        '{"items": {"fdefault": {"journeyNo": "journey456"}, "other": {"journeyNo": "789"}}}' || 1
        '{"items": {}}'                                                                       || 1
    }

    @Unroll
    def "testBuildSaveOrderCostCenterRequest with realData"() {
        given:
        new MockUp<Shark>() {
            @Mock
            public static String getByLocale(String key, String locale) {
                if ("en-US".equals(locale)) {
                    return SharkMockUtil.mapSharks().get(key + ".en-US");
                }
                return SharkMockUtil.mapSharks().get(key);
            }
        }
        OrderCreateRequestType orderCreateRequestType = JsonUtil.fromJson("{\"integrationSoaRequestType\":{\"userInfo\":{\"userId\":\"**********\",\"corpId\":\"QiluWanhe\",\"groupId\":\"Gr_00004762\",\"pos\":\"CHINA\"},\"token\":\"021CAB32DBA26CAF65D1A41531C7441FF72C038A1F460EAA6340B9C25D9C5480\",\"language\":\"zh-CN\",\"requestId\":\"bf4ace1fb9a24150948facdcf75ebc5e\",\"sourceFrom\":\"H5\",\"transactionID\":\"TID.SEARCHHOTEL.89.C2CBEFBBA85842A6B7BEA3A5AFE8F8F9\",\"gatewayHost\":\"ct.ctrip.com\",\"timezoneOffsetMinutesNew\":\"480\",\"logIndices\":[{\"key\":\"groupId\",\"value\":\"Gr_00004762\"},{\"key\":\"timezoneOffsetMinutesNew\",\"value\":\"480\"},{\"key\":\"gatewayHost\",\"value\":\"ct.ctrip.com\"},{\"key\":\"pvid\",\"value\":\"14\"},{\"key\":\"subChannel\",\"value\":\"\"},{\"key\":\"CID\",\"value\":\"51121019491263903089\"},{\"key\":\"EID\"},{\"key\":\"CIP\",\"value\":\"240e:426:b62:3b1:975:ca4c:4dd1:f451\"},{\"key\":\"miceToken\",\"value\":\"\"},{\"key\":\"groupName\",\"value\":\"齐鲁制药集团\"},{\"key\":\"gatewayIdc\",\"value\":\"SHAXY\"},{\"key\":\"traceID\",\"value\":\"\"},{\"key\":\"A\",\"value\":\"ordercheck\"},{\"key\":\"CorpID\",\"value\":\"QiluWanhe\"},{\"key\":\"C\",\"value\":\"corpbookinghotelbookservice\"},{\"key\":\"M\",\"value\":\"**********\"},{\"key\":\"sid\",\"value\":\"89\"},{\"key\":\"S\",\"value\":\"87371581453f4ac0a89ee7bc189add73\"},{\"key\":\"T\",\"value\":\"021CAB32DBA26CAF65D1A41531C7441FF72C038A1F460EAA6340B9C25D9C5480\"},{\"key\":\"pos\",\"value\":\"CHINA\"},{\"key\":\"GE\",\"value\":\"1735275015526\"},{\"key\":\"sourceFrom\",\"value\":\"H5\"},{\"key\":\"corpName\",\"value\":\"齐鲁制药集团有限公司\"},{\"key\":\"RID\",\"value\":\"bf4ace1fb9a24150948facdcf75ebc5e\"},{\"key\":\"TID\",\"value\":\"TID.SEARCHHOTEL.89.C2CBEFBBA85842A6B7BEA3A5AFE8F8F9\"},{\"key\":\"VID\",\"value\":\"1726025690142.71585PRgWhx8\"}],\"ticket\":\"QBiaCKLUkLYf6CUc/050kIFdBx+t8gxNKnMTwuAo4VZMs5MU8HSll6ASXtD6thPwXFYIW3pO794rnNeTBLkBbU44pLwxT2s60LjJLA0CJs4\\u003d\",\"transferInfo\":[{\"key\":\"iframe\",\"value\":\"F\"},{\"key\":\"channel\",\"value\":\"Native\"}]},\"hotelBookPassengerInputs\":[{\"hotelPassengerInput\":{\"roomIndex\":1,\"uid\":\"**********\",\"employee\":\"T\",\"external\":\"F\",\"employeeId\":\"086877\"},\"passengerBasicInfo\":{\"gender\":\"U\"},\"name\":\"宫涛\",\"isRegistrant\":\"F\",\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"18634401667\",\"transferPhoneNo\":\"18634401667\"}}],\"hotelInvoiceInfos\":[],\"cityInput\":{\"cityId\":513},\"hotelContactorInfo\":{\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"18634401667\",\"transferPhoneNo\":\"18634401667\"}},\"miceInput\":{},\"hotelBookInput\":{\"hotelDateRangeInfo\":{\"checkIn\":\"2024-12-27\",\"checkOut\":\"2024-12-28\"},\"roomQuantity\":1},\"membershipInfo\":{},\"bookModeInfo\":{\"bookingType\":\"NONE\"},\"costAllocationInfo\":{\"costAllocationToken\":\"H4sIAAAAAAAAAOMS4JhwmlGAQYJFic3IzEDPwEBIjovLyNzA3NjUzNjUXAhDXoqAvBIBeQATZYaWcgAAAA\\u003d\\u003d\",\"shareAmounts\":[{\"costAllocationAmount\":{\"amount\":\"260\"},\"shareAmountKey\":\"**********\"}]},\"resourceTokenInfo\":{\"resourceToken\":\"{\\\"hotelResourceToken\\\":{\\\"masterHotelId\\\":*********,\\\"hotelId\\\":*********,\\\"searchHotelTraceId\\\":\\\"113b91add52447c390b2b3491443d6b1\\\",\\\"hid\\\":\\\"ASIFMjU0LjFCAUJKBggAEAAYAFJtCmd2MV9DS0hDOERVUW9zTHdOUm9DUTBjaUF6STJNaW9ETWpZeU1nMEtBME5PV1JJRFEwNVpHZ0V4T2lBeE1UTmlPVEZoWkdRMU1qUTBOMk16T1RCaU1tSXpORGt4TkRRelpEWmlNUT09EAAYAVoDQ05ZYg0KA0NOWRIDQ05ZGgExYg0KA0NOWRIDQ05ZGgExaApyBjI2MC4wMHoEMC4wMIIBIDExM2I5MWFkZDUyNDQ3YzM5MGIyYjM0OTE0NDNkNmIxigEKMTQ1NTc3MTYyMZoBJGY4ZWM5YmU5LTVlNWMtNDBkYi1hNjg1LTg5ZDVmNzc4MGExMqIBAggAqgEkN2M1MTE5ZTAtN2EzMy00MTgyLWFjNzEtZTlmOGQwNDA2MTgy\\\",\\\"hotelType\\\":\\\"C\\\",\\\"bonusPoint\\\":\\\"T\\\",\\\"hotelGeoInfoResourceToken\\\":{\\\"cityId\\\":513,\\\"locationId\\\":2385}},\\\"roomResourceToken\\\":{\\\"roomId\\\":**********,\\\"baseRoomId\\\":459029785,\\\"ratePlanTraceLogId\\\":\\\"9b527ec6c9f24a80a0026e18113cf08f_\\\",\\\"allRoomType\\\":\\\"EXIST_C\\\",\\\"bedName\\\":\\\"1张2米特大床\\\",\\\"windowName\\\":\\\"有窗\\\",\\\"breakfastInfo\\\":\\\"2份早餐\\\",\\\"confirmType\\\":\\\"IMMEDIATE\\\",\\\"roomType\\\":\\\"C\\\",\\\"balanceType\\\":\\\"PP\\\",\\\"tmcPrice\\\":false,\\\"adult\\\":2,\\\"pId\\\":\\\"AQoGMjYyLjAwEgYyNjIuMDAieQp3djFfQ0tMQzhEVVErOHlDdHdVYUJ6RXlOakk1TWpZaUFsZElLZ0pEUnpJRE1qWXlPZ015TmpKQ0RRb0RRMDVaRWdORFRsa2FBVEZLSURFeE0ySTVNV0ZrWkRVeU5EUTNZek01TUdJeVlqTTBPVEUwTkROa05tSXgqA0NOWTINCgNDTlkSA0NOWRoBMTINCgNDTlkSA0NOWRoBMToCCABCBwjoDxAMGBtKBwjoDxAMGBxQAVgAYgJXSGgaeAKAAaLC8DU\\\\u003d\\\",\\\"amadeus\\\":false,\\\"supportAccommodation\\\":\\\"F\\\",\\\"roomOriginCurrency\\\":\\\"CNY\\\",\\\"roomScenarioEnum\\\":\\\"ROOM_LIST\\\",\\\"ratePlanRequestToken\\\":\\\"ASi1L/0AUIUVABZupD8QDVUAAABAcQWKW3EFUPjcPgJbwSAqAUjbtE3atEnbdiZ/JCmSJEkxlyNJSubaNIlxV5u91989tHU8kqNY1n2VAJQAkwBtvSlRCeZU1VLoqt2I3MicCSpeeqr3sH8fru7WV7OAHq0yL7LemahHZ1QxP7Qlxdf5trOnSjqtWgG7i8c0b5xF5YvpDiJfUiMfJ00GAzkxNjT2BKPDc9ifYQ03xmPY/i38U/j6Z8pnwh0c4ojwi1vuOHZGaQEgQrg5XlWAB5xX1KamGCYF8EmGSTJsck245SArEUIDRan7gVAbB/qs6ws+SKJbW84NJkXcbXOtqczkuTEGwnxjmK7mZ0VVcBVTtYrgJUI5V/fxfiiSGkE1or7pFjxVQb8YuRxxGYgywe9jpusJXyd3UTIk7G0jmaaV/ZLIpROWuT7lif4oENFigkEf9/RSdW/Dd41pNE5d8FFoUXGdQ5Uk6WOUAjeYNqJtRjTBQi/ZuYfLLAIwiTPXnac9Z/kMyTLpQKNDWOxyQCAOjF+cPc3uZLV+oKG9peBXECCgQnz1LuDJlOq0JtJIlF4vNJsafyh40Y7qzhZ6IU7Ko0vIApJsFtjUzKaM+binqke1w6mT0kXkG1fmOurxnLqUvECuaE15ZHyE7xvzzcPkUZyST5gW6S5mVspbeUWaDgrJ0K5yeTSnIsKloxclGWY8if0k9oPY/2G/h/0Z9l/YX2E/hV3veb9bwlb3uC+bsq/dYCfYFHuC/wh4AsCLeAqLwIfonVl2qMh+moJGf4vBr7bPImbYsdxKNu1CAo3+loGzFE320wY0uLcK/AoUxZLlwg4Exa0r2U8r0OhvIfgVZ4K8zXVHu30myRuHbksOqKURNLS3FvyKFnFCAQkA48SQFQoQNRCyAdgJ87ubzgT/KaaXHcyhkAE\\\\u003d\\\",\\\"ratePlanRoomToken\\\":\\\"ASi1L/0AUHkAAAoBQxAAGPvMgrcFIgJQUA\\\\u003d\\\\u003d\\\",\\\"roomUniqueKey\\\":\\\"V1-ASi1L/0AULkAAAj7zIK3BRIBQxoETk9ORSICUFA4zopN\\\",\\\"adultNumber\\\":2,\\\"windowType\\\":2},\\\"reservationResourceToken\\\":{\\\"wsId\\\":\\\"d4e9216f-c8a4-4425-8d77-ff39a91abf72\\\",\\\"policyToken\\\":\\\"v1KLUv/QBQbQkAtpI8JDCp6AFE1ETSVtJoUoTsniq2dlFq18brSCKuW2mt6R8UNBRUcDEAMgAyAHOXbekZQqEfbNv3UDK6XdZhBwQ7OMtptu1z2lbydFSeUfl92mUtgIVo2rZzCVWeliF1oCvIkbYtkSwx2jlFtqMfhe59l2URc5Z7B+ZEw6znJBgWBQgwEYs006xmxqK1bb8nMZApUVIU7wIh/baz8oKKcxih6Z8wxYgwq6aSXZrK2VZykxszGnxLkEmMi2dX+VFhiOUMDQsKbWFTWD7+ec909EVJ1VfUnv9UkYLOBNOniDoqDFVvKFpCDSP+fhonxWzJowsIfREVADobZ2OHs7XDfXPxzF2IKoSpUSiGC6kGmHqoQ2mFNCfxqacMVIlXCUcYtVQBnBtAyuvMHFY2mw\\\\u003d\\\\u003d\\\"},\\\"bookInitResourceToken\\\":{\\\"aggBookPriceResourceToken\\\":{\\\"currency\\\":\\\"CNY\\\",\\\"accountPaymentAmount\\\":260.00,\\\"individualPaymentAmount\\\":2.00,\\\"mixPayAccountExtraAmount\\\":0.00},\\\"roomPriceResourceToken\\\":{},\\\"cancelTip\\\":\\\"2024年12月27日18:00前可免费取消。若未入住或过时取消将收取您首日房费¥262（若用优惠券则以券后支付价为准），修改结果请以酒店回复结果为准。携程会根据您的付款方式扣除房费，订单需等酒店或供应商确认后生效，如订单不确认将全额退款至您的付款账户，订单确认结果以携程短信或邮件通知为准。\\\"}}\"},\"corpPayInfo\":{\"corpPayType\":\"public\"},\"userActionInfo\":{\"userActionToken\":\"PRD-299-00708ca1-94d2-4c33-a976-33e64bd9bd11-********-4.1.15-WEB\"},\"approvalFlowInput\":{\"password\":\"\",\"approvalFlowToken\":\"H4sIAAAAAAAA_wXBsRGAMAgAQG0tLa0sbLkLBEgYJwfJIi7hHE7gIPau4f9yDHXpjQgChYELGjQnAbQYQZ66Z1tn2vaggpndh0VVTlVbDeNsDaV01rxf5_c-9_QDsAfWKVMAAAA\",\"approvers\":[{\"uid\":\"**********\",\"level\":\"1\"}]},\"rcInfos\":[],\"bookUrlInfos\":[{\"urlType\":\"SUCCESS\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayState/PayState?site\\u003d2\\u0026LimitStandard\\u003dfalse\\u0026needFinishResult\\u003dtrue\\u0026\"},{\"urlType\":\"S_BACK\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayState/PayState?site\\u003d2\\u0026LimitStandard\\u003dfalse\\u0026needFinishResult\\u003dtrue\\u0026\"},{\"urlType\":\"E_BACK\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayMent/PayMent\"},{\"urlType\":\"FROM_URL\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayMent/PayMent?leomacmd://navi?%7b%22backward%22%3atrue%2c%22navi%22%3a0%2c%22now%22%3atrue%2c%22platform%22%3a%7b%7d%7d\"},{\"urlType\":\"S_BACK_MICE\",\"urlValue\":\"https://ct.ctrip.com/mice-activities/welfareTourism\"},{\"urlType\":\"FROM_URL_ORDER_DETAIL\",\"urlValue\":\"https://ct.ctrip.com/webapp/hotel/process/detail?orderId\\u003d{0}\\u0026language\\u003d{1}\\u0026from\\u003dfinish\"},{\"urlType\":\"OFFLINE_RETURN_URL\",\"urlValue\":\"http://corpint.ct.ctripcorp.com/hotel-online/offlineOrder/success\"},{\"urlType\":\"ORDER_DETAIL_URL\",\"urlValue\":\"https://ct.ctrip.com/m/Detail/Hotel/{0}\"}],\"paymentInfoInput\":{\"canWechatPayByOthers\":\"T\",\"clientType\":\"APP\"},\"clientInfo\":{\"clientType\":\"APP\",\"rmsToken\":\"fp\\u003d\\u0026vid\\u003d1726025690142.71585PRgWhx8\\u0026pageId\\u003d10650065356\\u0026r\\u003d9b8ab4f32c7f4bcf948dd60157c977a7\\u0026ip\\u003dundefined\\u0026rg\\u003db4\\u0026screen\\u003d428x926\\u0026tz\\u003d+8\\u0026blang\\u003dzh-CN\\u0026oslang\\u003dzh-CN\\u0026ua\\u003dMozilla%2F5.0%20(iPhone%3B%20CPU%20iPhone%20OS%2017_6_1%20like%20Mac%20OS%20X)%20AppleWebKit%2F605.1.15%20(KHTML%2C%20like%20Gecko)%20Mobile%2F21G93%2Cios%2CCoreInside%2C17.6.1%2CiPhone%2012%20Pro%20Max%2C9.59.1%2C959.001.000%2CCorpCtrip%2CStatusBar%2CScreenFringe%2CiPhoneX%2C5G%2Ccompany%20Titans%2F9.59.1%20ScreenWidth%2F428%20ScreenHeight%2F926%20scale%2F3%20SafeAreaTop%2F47%20SafeArea\\u0026v\\u003dm17\\u0026bl\\u003dfalse\\u0026clientid\\u003d\",\"secondaryChannel\":\"iphone9.59.1-new\"},\"offlineInfo\":{\"forciblyAssure\":\"F\",\"especialAuth\":\"F\",\"autoConfirm\":\"F\",\"exigentOrder\":\"F\"},\"roomInfoInput\":{},\"sendInfo\":{\"sendSMS\":\"F\",\"sendEmail\":\"F\"},\"flashStayInput\":{\"flashPlatFrom\":\"APP\"},\"hotelPayTypeInput\":[{\"payCode\":\"ROOM\",\"payType\":\"MIX_PAY\"}],\"strategyInfos\":[{\"strategyKey\":\"BOOKING_SCENARIO\",\"strategyValue\":\"HOTEL_LIST\"},{\"strategyKey\":\"BOOKING_WITH_PERSONAL_ACCOUNT\",\"strategyValue\":\"F\"}],\"costCenterInfo\":{\"costCenterJsonString\":\"{\\\"items\\\":{\\\"fdefault\\\":{\\\"cost1\\\":\\\"齐鲁制药有限公司\\\",\\\"cost2\\\":\\\"零售事业部(virtual-007324)\\\",\\\"cost3\\\":\\\"拜访客户\\\",\\\"cost4\\\":\\\"是\\\",\\\"cost5\\\":\\\"\\\"}}}\"},\"orderCreateToken\":\"H4sIAAAAAAAAAOMSCfb0c/dxjXcMCAjyD3P0iXfz8Q9X0uBSSTNLNk1NNDLSTTE0NdE1MTe01E1MNjLVNbRMSUsxSjZITTa2FGDQYAAAj9yHWUIAAAA\\u003d\",\"useNewOrderCreate\":\"F\"}", OrderCreateRequestType.class)
        orderCreateRequestType.cityInput.cityId = 22249
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("HotelBookPolicy", "C")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        QueryCheckAvailContextResponseType queryCheckAvailContextResponseType = JsonUtil.fromJson("{\"baseInfo\":{\"userInfo\":{\"uid\":\"**********\",\"policyUid\":\"\",\"corpId\":\"QiluWanhe\",\"eid\":\"\",\"usersCityId\":105,\"userCountryCode\":\"\"},\"customCurrency\":\"CNY\",\"website\":\"APP\",\"feeType\":\"C\",\"quantity\":1,\"startTime\":\"2024-12-27T00:00:00+08\",\"endTime\":\"2024-12-28T00:00:00+08\",\"language\":\"CN\",\"locale\":\"zh-CN\",\"startTimeUTC\":\"2024-12-26T16:00:00Z\",\"endTimeUTC\":\"2024-12-27T16:00:00Z\",\"pOS\":\"zh-CN\",\"pOSType\":{\"pOS\":\"zh-CN\",\"countryId\":1},\"platform\":\"ios\",\"bookingWithPersonalAccount\":false,\"bookingChannel\":\"APP\"},\"hotelInfo\":{\"subHotelId\":*********,\"masterHotelId\":*********,\"star\":4,\"rStar\":0,\"starLicence\":false,\"customerEval\":0.0,\"hotelGroupId\":565,\"hotelBrandId\":1662,\"balancePeriod\":\"GW\",\"oversea\":false,\"tripBusinessHotel\":false,\"cuHotel\":false,\"hasServiceFee\":false,\"localTimeOffsetHours\":0,\"telephone\":\"+86-350-3091666\",\"geographicalInfo\":{\"locationInfo\":{\"id\":2385},\"cityInfo\":{\"id\":513,\"name\":{\"textGB\":\"忻州\",\"textEn\":\"Xinzhou\"}},\"provinceInfo\":{\"id\":11},\"countryInfo\":{\"id\":1},\"coordinateInfo\":{}},\"address\":{\"textGB\":\"长征街道雁门大道雁门公园6号楼01号东商务楼\",\"textEn\":\"\"},\"hotelName\":{\"textGB\":\"维也纳国际酒店(忻州雁门大道店)\",\"textEn\":\"Vienna International Hotel (Luzhou Lumen Avenue)\"},\"currentCountry\":true,\"timeZoneId\":\"Asia/Shanghai\"},\"roomInfo\":{\"subRoomId\":**********,\"roomName\":{\"textGB\":\"商务大床房\",\"textEn\":\"Business Queen Room\"},\"basicRoomName\":{\"textGB\":\"商务大床房\",\"textEn\":\"Business Queen Room\"},\"masterBasicRoomId\":459029785,\"masterBasicRoomName\":{\"textGB\":\"商务大床房\"},\"roomType\":\"C\",\"tmcPriceType\":\"NONE\",\"balanceType\":\"PP\",\"gdsType\":\"WH\",\"mealType\":4,\"priceSuitPropertyValueId\":1262926,\"ratePlanKey\":{\"checkAvlID\":0,\"ratePlanID\":\"0\"},\"addPrice\":false,\"invoiceType\":\"Special\",\"invoiceLimitType\":\"NotSupported\",\"applyAreaPropertyId\":0,\"maxGuestCountPerRoom\":2,\"maskRoom\":false,\"hourlyRoom\":false,\"corpPrice\":true,\"manualGds\":false,\"joined30MinutesFreeCancel\":false,\"premiumRoom\":false,\"welfareRoom\":false,\"groupMemberShip\":false,\"earnHotelPoints\":false,\"bonusPointInfo\":{\"bonusPointRoom\":true,\"bonusPointCode\":\"WHZSF\",\"pointsMode\":\"XXMS\"},\"earlyArrivalTime\":\"2024-12-27T14:00:00+08\",\"lastCancelTime\":\"2024-12-27T18:00:00+08\",\"cnyAmount\":262.00,\"originAmountInfo\":{\"amount\":262.00,\"currency\":\"CNY\",\"exchange\":1},\"customAmountInfo\":{\"amount\":262.00,\"currency\":\"CNY\",\"exchange\":1},\"originCostInfo\":{\"amount\":262,\"currency\":\"CNY\",\"exchange\":1},\"originCostBeforeTaxInfo\":{\"amount\":262,\"currency\":\"CNY\",\"exchange\":1},\"roomDiscountInfo\":{\"promotionSwitchOn\":false,\"roomDailyDiscountInfo\":[],\"originPromotionAmount\":{\"price\":0,\"currency\":\"CNY\"},\"customPromotionAmount\":{\"price\":0,\"currency\":\"CNY\"}},\"roomDailyInfo\":[{\"effectDate\":\"2024-12-27T00:00:00+08\",\"cnyAmount\":262.00,\"amount\":262.00,\"customAmount\":262.00,\"afterPromotionCnyAmount\":262.00,\"afterPromotionCustomAmount\":262.00,\"cost\":262,\"costBeforeTax\":262,\"customizedCnyAmount\":0,\"mealNumber\":2,\"holdDeadline\":0,\"afterPromotionCustomAmountExcludeTax\":262.00,\"afterPromotionCnyAmountExcludeTax\":262.00,\"customAmountExcludeTax\":262.00,\"cnyAmountExcludeTax\":262.00}],\"paymentRulesInfo\":{\"paymentGuaranteePoly\":\"TRIP\",\"canTravelMoneyPay\":false,\"forceVccPay\":false,\"canCreditCardPay\":false},\"longRentalInfo\":{\"longRental\":false,\"needInquire\":false},\"holdRoomInfo\":{\"holdRoom\":false},\"extraPayTaxMultiCurrencyAmountInfo\":{\"customExtraPayTaxTotalAmount\":0,\"cnyExtraPayTaxTotalAmount\":0},\"pricedRoom\":false,\"generalizedPremiumRoom\":false,\"customAmountExcludeTaxInfo\":{\"amount\":262.00,\"currency\":\"CNY\",\"exchange\":1},\"supportAnticipation\":false,\"roomRightInfoList\":[],\"invoiceTypeExcludeAddPrice\":\"Special\",\"roomMealInfo\":{\"mealType\":4,\"mealDescList\":[\"2份早餐\"],\"dailyMealInfoList\":[{\"effectDateUTC\":\"2024-12-26T16:00:00Z\",\"mealDescList\":[\"\"]},{\"effectDateUTC\":\"2024-12-27T16:00:00Z\",\"mealDescList\":[\"2份早餐\"]}]},\"saleRoomTags\":[{\"tagCode\":\"XYJ\",\"tagDesc\":\"\",\"configInfoList\":[{\"position\":301,\"priority\":2,\"style\":\"1\"}]}],\"taxDetails\":[],\"pid\":\"AQoGMjYyLjAwEgYyNjIuMDAieQp3djFfQ0tMQzhEVVErOHlDdHdVYUJ6RXlOakk1TWpZaUFsZElLZ0pEUnpJRE1qWXlPZ015TmpKQ0RRb0RRMDVaRWdORFRsa2FBVEZLSURFeE0ySTVNV0ZrWkRVeU5EUTNZek01TUdJeVlqTTBPVEUwTkROa05tSXgqA0NOWTINCgNDTlkSA0NOWRoBMTINCgNDTlkSA0NOWRoBMToCCABCBwjoDxAMGBtKBwjoDxAMGBxQAVgAYgJXSGgaeAKAAaLC8DU\\u003d\",\"onlyGroupMemberCanBook\":false},\"bookingRules\":{\"cancelPenalties\":{\"paymentGuaranteePoly\":\"TRIP\",\"cancelDeductDetail\":{\"originDeductionPrice\":{\"price\":262.00,\"currency\":\"CNY\"},\"customDeductionPrice\":{\"price\":262.00,\"currency\":\"CNY\"}},\"guaranteePolicyInfo\":{}},\"authorizationRestriction\":{\"latestAuthorizeTime\":\"2024-12-27T17:00:00+08\"},\"confirmRules\":{\"justifyConfirm\":true,\"forbidNotImConfirm\":false},\"welfareRoomRules\":{},\"limitDepartureDateTime\":{\"departureEnd\":\"2024-12-28T12:00:00+08\",\"departureEndUTC\":\"2024-12-28T04:00:00Z\"},\"certificateInfo\":{\"needCertificate\":false},\"minLOS\":1,\"cancelPolicyInfo\":{\"cancelType\":\"LIMIT\",\"freeCancelPolicySceneType\":\"NOT_SUPPORTED\",\"lastCancelTimeUTC\":\"2024-12-27T10:00:00Z\",\"guaranteePolicyInfo\":{\"guaranteeType\":\"FIRST_DAY_PREPAY\",\"paymentGuaranteePoly\":\"TRIP\",\"guaranteeReason\":\"NONE\",\"guaranteePriceInfo\":{\"originGuaranteePrice\":{\"price\":262.00,\"currency\":\"CNY\"},\"customGuaranteePrice\":{\"price\":262.00,\"currency\":\"CNY\"}}},\"localLastCancelTime\":\"2024-12-27 18:00:00\"},\"limitArrivalDateTime\":{\"arrivalFromUTC\":\"2024-12-27T06:00:00Z\",\"arrivalEndUTC\":\"2024-12-27T22:00:00Z\",\"defaultArrivalTimeEnd\":false,\"localEarlyArrivalTime\":\"2024-12-27 14:00:00\",\"localLastArrivalTime\":\"2024-12-28 06:00:00\"},\"rPLimitInfo\":{\"minLos\":1,\"maxLos\":0,\"minAdvancedBookingDays\":0,\"maxAdvancedBookingDays\":0},\"billingGuestInfo\":{\"guestsNameLanguages\":[\"zh\",\"en\"]},\"restrictRuleList\":[]},\"bookExtInfo\":{\"featureControlFlagMap\":{\"EnableMemberPoints\":false,\"EnableXMinutesFreeCancel\":false,\"BrandNewCustomer\":true,\"EnableShadowDiscount\":false,\"EnableYXH\":false,\"EnablePrepayDiscount\":false,\"EnableNewGroupMember\":false,\"ForbidMultiNationality\":false,\"NewUserCountryCode\":true,\"EnableCNYTWODecimal\":true,\"ShieldMaskRoom\":true,\"EnableAddPrice\":false,\"IgnoreSpecialCancelPolicyNotMatchingFilter\":false,\"StrictCancellationPolicy\":false,\"IgnoreRemitTax\":false,\"ShieldMultiplePrice\":false,\"Enable30MinuteFreeCancel\":false,\"EnablePromotionSubTag\":true,\"EnableHotelNewCustomer\":false,\"DisableIPBlock\":false,\"EnableDirectRate\":false,\"EnableOutputPackageRoom\":false,\"ShieldMaskNakedSale\":false,\"EnableMobileRate\":true,\"EnableMatchPolicyWithExtraPayTax\":false}},\"userRightsInfo\":{\"userChosenCouponInfo\":[],\"hotelNewCustomer\":true,\"userSelectedCountryCode\":\"\"},\"reservationToken\":\"AQgBEgoyMDI0LTEyLTI3GhcKAUMSAlBQGgRUUklQIgJXSEj7zIK3BSICCAAqBwoFemgtQ04\\u003d\",\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2024-12-27 12:50:15.745+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", QueryCheckAvailContextResponseType.class)
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsId")))
                        .build().getCheckAvailContextInfo()
        OrderCreateToken orderCreateToken = new OrderCreateToken()
        CreateOrderResponseType createOrderResponseType = JsonUtil.fromJson("{\"wsId\":\"d4e9216f-c8a4-4425-8d77-ff39a91abf72\",\"orderId\":****************,\"orderPaymentInfo\":{\"payType\":\"MIX_PAY\",\"orderAmountInfo\":{\"cnyTotalAmount\":262.00,\"personalPayAmountInfo\":{\"amount\":2.00,\"currency\":\"CNY\",\"exchangeRateToCNY\":1},\"paymentItemList\":[{\"prepayType\":\"ACCNT\",\"gatheringType\":\"D\",\"feeType\":\"HotelRoomFee\",\"amountInfo\":{\"amount\":260.00,\"currency\":\"CNY\",\"exchangeRateToCNY\":1}},{\"prepayType\":\"SELF_PAY\",\"gatheringType\":\"D\",\"feeType\":\"HotelRoomFee\",\"amountInfo\":{\"amount\":2.00,\"currency\":\"CNY\",\"exchangeRateToCNY\":1}}]}},\"success\":true,\"supportPaymentBill\":true,\"paymentTransactionId\":\"307401827130572926\",\"orderTotalAmount\":{\"orderAmount\":{\"originAmount\":{\"amount\":262.00,\"currency\":\"CNY\",\"exchange\":1},\"cNYAmount\":262.00},\"paymentBillInfo\":{\"payAmount\":262.00,\"payCNYAmount\":262.00,\"payCurrency\":\"CNY\",\"payExchange\":1}},\"transactionInfo\":{\"transactionId\":\"b37b6097-8c4e-4c0b-9623-1972dec54b9e\",\"subTransactionIdList\":[{\"payType\":\"PERSONAL\",\"subTransactionId\":\"8771adc5-accd-4acf-84e3-63a4efed2dad\"},{\"payType\":\"ACCNT\",\"subTransactionId\":\"9ebe5b44-a349-43c1-a193-dd1168ba5f3e\"}]},\"responseCode\":20000,\"responseStatus\":{\"timestamp\":\"2024-12-27 12:50:16.783+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", CreateOrderResponseType.class)
        MatchCostCenterResponseType matchCostCenterResponseType = JsonUtil.fromJson("{\"result\":{\"costCenterDescription\":\"\",\"costCenterDescriptionEn\":\"\",\"costCenterContentlist\":[{\"groupID\":1,\"orderOrPerson\":\"O\",\"costCenterConfigs\":[{\"level\":1,\"costCenterTitle\":\"费用所属公司\",\"costCenterTitleEn\":\"费用所属公司\",\"required\":true,\"canChange\":true,\"linked\":false,\"selectHidden\":false,\"readUserCostCenter\":0,\"needAddress\":false}],\"costCenterItems\":[]},{\"groupID\":2,\"orderOrPerson\":\"O\",\"costCenterConfigs\":[{\"level\":2,\"costCenterTitle\":\"费用所属部门\",\"costCenterTitleEn\":\"费用所属部门\",\"required\":true,\"canChange\":true,\"linked\":false,\"selectHidden\":false,\"readUserCostCenter\":0,\"needAddress\":false}],\"costCenterItems\":[]},{\"groupID\":3,\"orderOrPerson\":\"O\",\"costCenterConfigs\":[{\"level\":3,\"costCenterTitle\":\"出差事由\",\"costCenterTitleEn\":\"出差事由\",\"required\":true,\"canChange\":true,\"linked\":false,\"selectHidden\":false,\"readUserCostCenter\":0,\"needAddress\":false}],\"costCenterItems\":[]},{\"groupID\":4,\"orderOrPerson\":\"O\",\"costCenterConfigs\":[{\"level\":4,\"costCenterTitle\":\"是否为员工预订\",\"costCenterTitleEn\":\"是否为员工预订\",\"required\":true,\"canChange\":true,\"linked\":false,\"selectHidden\":false,\"readUserCostCenter\":0,\"needAddress\":false}],\"costCenterItems\":[]},{\"groupID\":5,\"orderOrPerson\":\"O\",\"costCenterConfigs\":[{\"level\":5,\"costCenterTitle\":\" 会议编号（填写以MID或GDS开头的17位编号）\",\"costCenterTitleEn\":\" 会议编号（填写以MID或GDS开头的17位编号）\",\"required\":false,\"canChange\":true,\"linked\":false,\"selectHidden\":false,\"readUserCostCenter\":0,\"needAddress\":false}],\"costCenterItems\":[]}],\"costCenterExtlist\":[{\"costCenterExtType\":\"TP\",\"costCenterExtTitle\":\"出行目的\",\"costCenterExtTitleEn\":\"JounaryReason\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"PN\",\"costCenterExtTitle\":\"项目号\",\"costCenterExtTitleEn\":\"Project\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"FT\",\"costCenterExtTitle\":\"机票关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of flight\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"HT\",\"costCenterExtTitle\":\"酒店关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of hotel\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"TT\",\"costCenterExtTitle\":\"火车票关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of train\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"CT\",\"costCenterExtTitle\":\"用车关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of car\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"BT\",\"costCenterExtTitle\":\"汽车票关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No.\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"D1\",\"costCenterExtTitle\":\"自定义字段1\",\"costCenterExtTitleEn\":\"Self-defined title No.1\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"D2\",\"costCenterExtTitle\":\"自定义字段2\",\"costCenterExtTitleEn\":\"Self-defined title No.2\",\"required\":false,\"enabled\":false,\"selectHidden\":false}],\"frequentlyUsedCostCenter\":0},\"costCenterDefaultTitles\":[{\"costCenterDefaultType\":\"TP\",\"costCenterDefaultTitle\":\"出行目的\",\"costCenterDefaultTitleEn\":\"JounaryReason\"},{\"costCenterDefaultType\":\"PN\",\"costCenterDefaultTitle\":\"项目号\",\"costCenterDefaultTitleEn\":\"Project\"},{\"costCenterDefaultType\":\"FT\",\"costCenterDefaultTitle\":\"机票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of flight\"},{\"costCenterDefaultType\":\"HT\",\"costCenterDefaultTitle\":\"酒店关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of hotel\"},{\"costCenterDefaultType\":\"TT\",\"costCenterDefaultTitle\":\"火车票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of train\"},{\"costCenterDefaultType\":\"CT\",\"costCenterDefaultTitle\":\"用车关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of car\"},{\"costCenterDefaultType\":\"BT\",\"costCenterDefaultTitle\":\"汽车票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No.\"},{\"costCenterDefaultType\":\"D1\",\"costCenterDefaultTitle\":\"自定义字段1\",\"costCenterDefaultTitleEn\":\"Self-defined title No.1\"},{\"costCenterDefaultType\":\"D2\",\"costCenterDefaultTitle\":\"自定义字段2\",\"costCenterDefaultTitleEn\":\"Self-defined title No.2\"},{\"costCenterDefaultType\":\"CC1\",\"costCenterDefaultTitle\":\"成本中心1\",\"costCenterDefaultTitleEn\":\"Cost center No.1\"},{\"costCenterDefaultType\":\"CC2\",\"costCenterDefaultTitle\":\"成本中心2\",\"costCenterDefaultTitleEn\":\"Cost center No.2\"},{\"costCenterDefaultType\":\"CC3\",\"costCenterDefaultTitle\":\"成本中心3\",\"costCenterDefaultTitleEn\":\"Cost center No.3\"},{\"costCenterDefaultType\":\"CC4\",\"costCenterDefaultTitle\":\"成本中心4\",\"costCenterDefaultTitleEn\":\"Cost center No.4\"},{\"costCenterDefaultType\":\"CC5\",\"costCenterDefaultTitle\":\"成本中心5\",\"costCenterDefaultTitleEn\":\"Cost center No.5\"},{\"costCenterDefaultType\":\"CC6\",\"costCenterDefaultTitle\":\"成本中心6\",\"costCenterDefaultTitleEn\":\"Cost center No.6\"}],\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2024-12-27 12:50:16.846+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}\n", MatchCostCenterResponseType.class)
        CostCenterInfoType costCenterInfoType = matchCostCenterResponseType.getResult()
        ApprovalTextInfoResponseType approvalTextInfoResponseType = new ApprovalTextInfoResponseType()
        SSOInfoQueryResponseType ssoInfoQueryResponseType = new SSOInfoQueryResponseType()
        ResourceToken resourceToken = new ResourceToken()

        when: // 按单走 取样来源：bf4ace1fb9a24150948facdcf75ebc5e 时间：2024-12-27T12:50:16.000+08:00
        SaveOrderCostCenterRequestType resultOld = JsonUtil.fromJson("{\"saveList\":[{\"bizType\":4,\"orderID\":****************,\"uid\":\"**********\",\"pid\":\"**********\",\"corpId\":\"QiluWanhe\",\"eid\":\"**********\",\"costCenterDesc\":\"\",\"costCenterDescEn\":\"\",\"orderCostCenters\":[{\"costCenterKey\":4,\"costCenterValue\":\"是\",\"costCenterTitle\":\"是否为员工预订\",\"costCenterTitleEn\":\"是否为员工预订\"},{\"costCenterKey\":3,\"costCenterValue\":\"拜访客户\",\"costCenterTitle\":\"出差事由\",\"costCenterTitleEn\":\"出差事由\"},{\"costCenterKey\":2,\"costCenterValue\":\"零售事业部(virtual-007324)\",\"costCenterTitle\":\"费用所属部门\",\"costCenterTitleEn\":\"费用所属部门\"},{\"costCenterKey\":1,\"costCenterValue\":\"齐鲁制药有限公司\",\"costCenterTitle\":\"费用所属公司\",\"costCenterTitleEn\":\"费用所属公司\"},{\"costCenterKey\":5,\"costCenterValue\":\"\",\"costCenterTitle\":\" 会议编号（填写以MID或GDS开头的17位编号）\",\"costCenterTitleEn\":\" 会议编号（填写以MID或GDS开头的17位编号）\"}],\"passengers\":[{\"passengerId\":\"**********\",\"passengerName\":\"宫涛\",\"isEmployee\":1}],\"frequentlyUsedCostCenter\":0,\"createTimeStr\":\"2024-12-27 12:50:16\"}]}", SaveOrderCostCenterRequestType.class)
        SaveOrderCostCenterRequestType resultNew = mapper.buildSaveOrderCostCenterRequest(orderCreateRequestType, accountInfo, checkAvailInfo, orderCreateToken, createOrderResponseType, costCenterInfoType, approvalTextInfoResponseType, ssoInfoQueryResponseType, resourceToken, null, null)
        then:
        resultNew != null
        resultNew.saveList.size() == 1
        resultNew.saveList.get(0).bizType == 4
        resultNew.saveList.get(0).orderID == ****************


        when: // 仅存在关联行程号 48ae4d64aa6b4dd489f8c31f76c10372 2024-12-27T15:47:56
        orderCreateRequestType = JsonUtil.fromJson("{\"integrationSoaRequestType\":{\"userInfo\":{\"userId\":\"**********\",\"corpId\":\"XMXY\",\"groupId\":\"\",\"pos\":\"CHINA\"},\"token\":\"9254D6BD55F9C8810213344E12F3F3DB6DB8F76034A417B45E792D10DC586C7A\",\"language\":\"zh-CN\",\"requestId\":\"48ae4d64aa6b4dd489f8c31f76c10372\",\"sourceFrom\":\"H5\",\"transactionID\":\"TID.HOTELBOOK.17.C952CC296BE24A329BE5558D6BF129F0\",\"gatewayHost\":\"ct.ctrip.com\",\"timezoneOffsetMinutesNew\":\"480\",\"logIndices\":[{\"key\":\"groupId\",\"value\":\"\"},{\"key\":\"timezoneOffsetMinutesNew\",\"value\":\"480\"},{\"key\":\"gatewayHost\",\"value\":\"ct.ctrip.com\"},{\"key\":\"pvid\",\"value\":\"11\"},{\"key\":\"subChannel\",\"value\":\"\"},{\"key\":\"CID\",\"value\":\"09031026310068516070\"},{\"key\":\"EID\"},{\"key\":\"CIP\",\"value\":\"2408:8431:b11:f47c:b822:adff:fe92:e6d6\"},{\"key\":\"miceToken\",\"value\":\"\"},{\"key\":\"groupName\",\"value\":\"\"},{\"key\":\"gatewayIdc\",\"value\":\"SHAXY\"},{\"key\":\"traceID\",\"value\":\"\"},{\"key\":\"A\",\"value\":\"ordercheck\"},{\"key\":\"CorpID\",\"value\":\"XMXY\"},{\"key\":\"C\",\"value\":\"corpbookinghotelbookservice\"},{\"key\":\"M\",\"value\":\"**********\"},{\"key\":\"sid\",\"value\":\"17\"},{\"key\":\"S\",\"value\":\"33984d08a7084bc79038b8ff4957da7e6cea00aa88cd\"},{\"key\":\"T\",\"value\":\"9254D6BD55F9C8810213344E12F3F3DB6DB8F76034A417B45E792D10DC586C7A\"},{\"key\":\"pos\",\"value\":\"CHINA\"},{\"key\":\"GE\",\"value\":\"1735285674345\"},{\"key\":\"sourceFrom\",\"value\":\"H5\"},{\"key\":\"corpName\",\"value\":\"厦门象屿股份有限公司\"},{\"key\":\"RID\",\"value\":\"48ae4d64aa6b4dd489f8c31f76c10372\"},{\"key\":\"TID\",\"value\":\"TID.HOTELBOOK.17.C952CC296BE24A329BE5558D6BF129F0\"},{\"key\":\"VID\",\"value\":\"1731502329729.57398OXnC2Il\"}],\"ticket\":\"FOCIo5uPv+eBEhjJpxYORz5UTIR453Obxv6ifgmpXIYsihee4BqAL+GtDpvXhv06CI37OsSW5pxClgMCdjx3b044pLwxT2s60LjJLA0CJs4\\u003d\",\"transferInfo\":[{\"key\":\"iframe\",\"value\":\"F\"},{\"key\":\"channel\",\"value\":\"sso\"}]},\"hotelBookPassengerInputs\":[{\"hotelPassengerInput\":{\"roomIndex\":1,\"uid\":\"**********\",\"approvalPassengerId\":\"2824721403217959597\",\"employee\":\"T\",\"external\":\"F\",\"employeeId\":\"233065\"},\"passengerBasicInfo\":{\"gender\":\"U\"},\"name\":\"陈方亮\",\"isRegistrant\":\"F\",\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"15941498636\",\"transferPhoneNo\":\"15941498636\"}}],\"hotelInvoiceInfos\":[],\"cityInput\":{\"cityId\":1155},\"hotelContactorInfo\":{\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"15941498636\",\"transferPhoneNo\":\"15941498636\"},\"emailInfo\":{\"email\":\"<EMAIL>\",\"transferEmail\":\"<EMAIL>\"}},\"approvalInput\":{\"masterApprovalNo\":\"SQZD202412230100\",\"subApprovalNo\":\"SQZD202412230100\",\"emergency\":\"F\"},\"miceInput\":{},\"hotelBookInput\":{\"hotelDateRangeInfo\":{\"checkIn\":\"2024-12-27\",\"checkOut\":\"2024-12-29\"},\"roomQuantity\":1},\"membershipInfo\":{},\"bookModeInfo\":{\"bookingType\":\"NONE\"},\"resourceTokenInfo\":{\"resourceToken\":\"{\\\"hotelResourceToken\\\":{\\\"masterHotelId\\\":*********,\\\"hotelId\\\":*********,\\\"searchHotelTraceId\\\":\\\"b3a5e81beb0140ff9256ea2607a4de34\\\",\\\"hid\\\":\\\"ASIGMjQ2LjAwQgFHSgYIABAAGABSbQpndjFfQ0ozMm1qSVF4ZmFhTWhvQ1EwY2lBekkwTmlvRE1qUTJNZzBLQTBOT1dSSURRMDVaR2dFeE9pQmlNMkUxWlRneFltVmlNREUwTUdabU9USTFObVZoTWpZd04yRTBaR1V6TkE9PRAAGAFaA0NOWWINCgNDTlkSA0NOWRoBMWINCgNDTlkSA0NOWRoBMWgDcgYzMDAuMDB6BDAuMDCCASBiM2E1ZTgxYmViMDE0MGZmOTI1NmVhMjYwN2E0ZGUzNIoBCjE1NzYzODQ2NzCaASQ4MTMyYTAxNy1mOWI2LTQ0NTItYmVlMC1lYTg1NTEyOTA5NjmiAQIIAA\\\\u003d\\\\u003d\\\",\\\"hotelType\\\":\\\"C\\\",\\\"bonusPoint\\\":\\\"T\\\",\\\"hotelGeoInfoResourceToken\\\":{\\\"cityId\\\":1155,\\\"locationId\\\":1083}},\\\"roomResourceToken\\\":{\\\"roomId\\\":1576384668,\\\"baseRoomId\\\":*********,\\\"ratePlanTraceLogId\\\":\\\"dc9f7223cc334ec899adf122f1bf87be_\\\",\\\"allRoomType\\\":\\\"EXIST_C\\\",\\\"bedName\\\":\\\"1张1.8米大床\\\",\\\"windowName\\\":\\\"有窗\\\",\\\"breakfastInfo\\\":\\\"2份早餐\\\",\\\"confirmType\\\":\\\"IMMEDIATE\\\",\\\"roomType\\\":\\\"C\\\",\\\"balanceType\\\":\\\"PP\\\",\\\"tmcPrice\\\":false,\\\"adult\\\":2,\\\"pId\\\":\\\"AQoGMjYyLjAwEgYyNjIuMDAieQp3djFfQ01YMm1qSVFuUEhXN3dVYUJ6RXlOak13TkRJaUFsSktLZ0pEUnpJRE1qWXlPZ015TmpKQ0RRb0RRMDVaRWdORFRsa2FBVEZLSUdJellUVmxPREZpWldJd01UUXdabVk1TWpVMlpXRXlOakEzWVRSa1pUTTAqA0NOWTINCgNDTlkSA0NOWRoBMTINCgNDTlkSA0NOWRoBMToCCABCBwjoDxAMGBtKBwjoDxAMGB1QAVgAYgJSSmgTeAKAAcX2mjI\\\\u003d\\\",\\\"amadeus\\\":false,\\\"supportAccommodation\\\":\\\"F\\\",\\\"roomOriginCurrency\\\":\\\"CNY\\\",\\\"roomScenarioEnum\\\":\\\"ROOM_LIST\\\",\\\"ratePlanRequestToken\\\":\\\"ASi1L/0AUGUVAJZto0IgTVUAAAyAEgGF7cGUiyB4RSSAQhwABptMNADaLSMCWaTFXQzD0azsHF5tsftJK0L16t3fs01gAbTIkcxU4q1t9T+UAJMAkQCYEgpMVayYnthMJ69crXHRIoU1ITFRs/Sq0dvTzpXjZ6XDcC9ArIoVTSrZWF60PZN8NJGjPTuLjZtygN/GBjWl7TzMFrqfNpEhkiMvNjxiJtt4bIA8xaPDe8DP4dx0j8fQ/Vv4p/Dvt5aMLUdyFtEylq/ROOpHiQEQYdNF2JYACCdIAwqQLZm2ZUsF2/L1IGc4oaEiE88EfeYI00mPOycY0aKzNpYHKoVYr/Oedkv1ml7MTj8jSneuIs/SaWk8BLVKOdouSpvcTIyPB7UnHzmGq4qm84al3GQZedVYeByMZdXN+kI9hHEBX4xjQitULyGK5MmS7aOUXaKa1BCY0YYTTM6k5kemuBZn1XhSlXZ1SjLfXPQZYmz8HVFj60tZI43cylYu6l13DL7DPakTSim1ffcq6rS9M6H3FAKIzlAJFsmyiCRjGe1wdgflNAII99eC79i4kww/zVTD43jcW1GiVwv3KvOiTA6VqpNekqlSWnN7UBlr2/UV2Usmbzk9E+av9Y4SkgfaMJDndtQ9bYav+nZTSbIQWVaDwvqylXJaaauKB+xa7mounqqQpXTykD5Z6LMtGMZV1Wmpp9KTaMCbwG8Cfwj8IPB/wM8B/wX8FfBTwC9uMYslYBAeMYgrj2EHWAGuwBT5SzxGUDyexFdcBV6E3pxyfzgacgsg+q8G39GFFy6iY9mVqLk3BSD6rwOdknPQkCMA8f4y8B0azkvU22yNhne7REPOAET/leA71LR37bvdkW5TvVNdUamaRG4CCPdXCQDWxJAVChA1ELIB2Anzu5vOxP4pppcdwqGQAQ\\\\u003d\\\\u003d\\\",\\\"ratePlanRoomToken\\\":\\\"ASi1L/0AUHkAAAoBQxAAGJzx1u8FIgJQUA\\\\u003d\\\\u003d\\\",\\\"roomUniqueKey\\\":\\\"V1-ASi1L/0AULkAAAic8dbvBRIBQxoETk9ORSICUFA4wotN\\\",\\\"adultNumber\\\":2,\\\"windowType\\\":2},\\\"reservationResourceToken\\\":{\\\"wsId\\\":\\\"787fe300-336d-49cd-b876-f3ecfe7f265c\\\",\\\"policyToken\\\":\\\"v1KLUv/QBQZQkAFtI7JUCJugG7bZwc/eUbimpMkQaXl68wgjNZFacUg5RKDI91RlGUAwEyADEAMQC9EOcqi+CgHNv2TZeMbpWFDk6igRRMotm2z2lbyZOR/aLyu7UKwwAwDKJtO5dVZWsZArMGNAYZ0rYjkiWLdj6RzehHoXtfZbkh/Bpqdp7HAjWQYkE0j7Pw05Clce0e58Db9gOPShC6BHkS5XXxKhDSbzsxH8icw1hNfwMrxoKJNY1I3GRqRoJvycFCjNKzq9zIuGIZDQoJbWFPWDZ+etN09EV51Y9qz5+weEFngenWgzKeXcZVdSpqWTV88PfTaEWzIwEWADobp2OH07XDf3PxzF2IKoSpOWioQuoCpgjqUEWp6eWfxfosswWqxKuEI4xaqgDODSDldWYOK5tN\\\"},\\\"bookInitResourceToken\\\":{\\\"aggBookPriceResourceToken\\\":{\\\"currency\\\":\\\"CNY\\\",\\\"accountPaymentAmount\\\":524.00,\\\"individualPaymentAmount\\\":0,\\\"mixPayAccountExtraAmount\\\":0},\\\"roomPriceResourceToken\\\":{},\\\"cancelTip\\\":\\\"2024年12月27日18:00前可免费取消。若未入住或过时取消将收取您首日房费¥262（若用优惠券则以券后支付价为准），修改结果请以酒店回复结果为准。携程会根据您的付款方式扣除房费，订单需等酒店或供应商确认后生效，如订单不确认将全额退款至您的付款账户，订单确认结果以携程短信或邮件通知为准。\\\"}}\"},\"corpPayInfo\":{\"corpPayType\":\"public\"},\"userActionInfo\":{\"userActionToken\":\"PRD-22-40f0de4a-c4e3-4418-a7bb-9b5c721ba753-********-4.1.15-WEB\"},\"approvalFlowInput\":{\"password\":\"\",\"approvalFlowToken\":\"H4sIAAAAAAAA_wXBsRGAMAgAQG0tLa0sbLkjJCYwDhgYMBPYOYcjuIH_y2FBKugZGLNDqSFgwQo5naWmEkkC15m2PTEja7hZd2pKnJVKlasJOVq3_Rnfe4_pB4B2JlpTAAAA\",\"approvers\":[]},\"rcInfos\":[],\"bookUrlInfos\":[{\"urlType\":\"SUCCESS\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayState/PayState?site\\u003d2\\u0026LimitStandard\\u003dfalse\\u0026needFinishResult\\u003dtrue\\u0026\"},{\"urlType\":\"S_BACK\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayState/PayState?site\\u003d2\\u0026LimitStandard\\u003dfalse\\u0026needFinishResult\\u003dtrue\\u0026\"},{\"urlType\":\"E_BACK\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayMent/PayMent\"},{\"urlType\":\"FROM_URL\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayMent/PayMent?leomacmd://navi?%7b%22backward%22%3atrue%2c%22navi%22%3a0%2c%22now%22%3atrue%2c%22platform%22%3a%7b%7d%7d\"},{\"urlType\":\"S_BACK_MICE\",\"urlValue\":\"https://ct.ctrip.com/mice-activities/welfareTourism\"},{\"urlType\":\"FROM_URL_ORDER_DETAIL\",\"urlValue\":\"https://ct.ctrip.com/webapp/hotel/process/detail?orderId\\u003d{0}\\u0026language\\u003d{1}\\u0026from\\u003dfinish\"},{\"urlType\":\"OFFLINE_RETURN_URL\",\"urlValue\":\"http://corpint.ct.ctripcorp.com/hotel-online/offlineOrder/success\"},{\"urlType\":\"ORDER_DETAIL_URL\",\"urlValue\":\"https://ct.ctrip.com/m/Detail/Hotel/{0}\"}],\"paymentInfoInput\":{\"canWechatPayByOthers\":\"F\",\"clientType\":\"H5\"},\"clientInfo\":{\"clientType\":\"H5\",\"rmsToken\":\"fp\\u003d\\u0026vid\\u003d1731502329729.57398OXnC2Il\\u0026pageId\\u003d10650064424\\u0026r\\u003d138c5699c14c4650bac345b7e36e607b\\u0026ip\\u003d2408:8431:b11:f47c:b822:adff:fe92:e6d6\\u0026rg\\u003dfin\\u0026screen\\u003d363x800\\u0026tz\\u003d+8\\u0026blang\\u003dzh-CN\\u0026oslang\\u003dzh-CN\\u0026ua\\u003dMozilla%2F5.0%20(Linux%3B%20Android%2012%3B%20ABR-AL60%20Build%2FHUAWEIABR-AL60%3B%20wv)%20AppleWebKit%2F537.36%20(KHTML%2C%20like%20Gecko)%20Version%2F4.0%20Chrome%2F114.0.5735.196%20Mobile%20Safari%2F537.36%20E-Mobile7%2F7.0.46.20210617%20Language%2Fzh%20Qiyuesuo%2FphysicalSDK\\u0026v\\u003dm17\\u0026bl\\u003dfalse\\u0026clientid\\u003d\",\"secondaryChannel\":\"Android-new\"},\"offlineInfo\":{\"forciblyAssure\":\"F\",\"especialAuth\":\"F\",\"autoConfirm\":\"F\",\"exigentOrder\":\"F\"},\"roomInfoInput\":{},\"sendInfo\":{\"sendSMS\":\"F\",\"sendEmail\":\"F\"},\"ssoInput\":{\"ssoKey\":\"7b864c3fbb17442b9226b7ed584e10d1\"},\"flashStayInput\":{\"flashPlatFrom\":\"H5\"},\"hotelPayTypeInput\":[{\"payCode\":\"ROOM\",\"payType\":\"CORP_PAY\"}],\"strategyInfos\":[{\"strategyKey\":\"BOOKING_SCENARIO\",\"strategyValue\":\"HOTEL_LIST\"},{\"strategyKey\":\"BOOKING_WITH_PERSONAL_ACCOUNT\",\"strategyValue\":\"F\"}],\"costCenterInfo\":{\"costCenterJsonString\":\"{\\\"items\\\":{\\\"fdefault\\\":{}}}\"},\"orderCreateToken\":\"H4sIAAAAAAAAAOMSCfb0c/dxjXcMCAjyD3P0iXfz8Q9X0uBSSUozSrQ0SDXWtTAwTtU1MUuz1E1Ks0jUNTY0NTEzNEkztEwzEGDQYAAAaUdsq0IAAAA\\u003d\",\"useNewOrderCreate\":\"F\"}", OrderCreateRequestType.class)
        orderCreateRequestType.cityInput.cityId = 22249
        resultOld = JsonUtil.fromJson("{\"saveList\":[{\"bizType\":4,\"orderID\":****************,\"uid\":\"**********\",\"pid\":\"**********\",\"corpId\":\"XMXY\",\"eid\":\"**********\",\"costCenterDesc\":\"\",\"costCenterDescEn\":\"\",\"jounaryNo\":\"SQZD202412230100\",\"jounaryNoTitle\":\"酒店关联行程号\",\"jounaryNoTitleEn\":\"Associated itinerary No. of hotel\",\"orderCostCenters\":[],\"passengers\":[{\"passengerId\":\"**********\",\"passengerName\":\"陈方亮\",\"isEmployee\":1}],\"frequentlyUsedCostCenter\":0,\"createTimeStr\":\"2024-12-27 15:47:56\"}]}", SaveOrderCostCenterRequestType.class)
        matchCostCenterResponseType = JsonUtil.fromJson("{\"result\":{\"costCenterDescription\":\"\",\"costCenterDescriptionEn\":\"\",\"costCenterExtlist\":[{\"costCenterExtType\":\"TP\",\"costCenterExtTitle\":\"出行目的\",\"costCenterExtTitleEn\":\"JounaryReason\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"PN\",\"costCenterExtTitle\":\"项目号\",\"costCenterExtTitleEn\":\"Project\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"FT\",\"costCenterExtTitle\":\"机票关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of flight\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"HT\",\"costCenterExtTitle\":\"酒店关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of hotel\",\"required\":false,\"enabled\":true,\"selectHidden\":false},{\"costCenterExtType\":\"TT\",\"costCenterExtTitle\":\"火车票关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of train\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"CT\",\"costCenterExtTitle\":\"用车关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of car\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"BT\",\"costCenterExtTitle\":\"汽车票关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No.\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"D1\",\"costCenterExtTitle\":\"自定义字段1\",\"costCenterExtTitleEn\":\"Self-defined title No.1\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"D2\",\"costCenterExtTitle\":\"自定义字段2\",\"costCenterExtTitleEn\":\"Self-defined title No.2\",\"required\":false,\"enabled\":false,\"selectHidden\":false}],\"frequentlyUsedCostCenter\":0},\"costCenterDefaultTitles\":[{\"costCenterDefaultType\":\"TP\",\"costCenterDefaultTitle\":\"出行目的\",\"costCenterDefaultTitleEn\":\"JounaryReason\"},{\"costCenterDefaultType\":\"PN\",\"costCenterDefaultTitle\":\"项目号\",\"costCenterDefaultTitleEn\":\"Project\"},{\"costCenterDefaultType\":\"FT\",\"costCenterDefaultTitle\":\"机票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of flight\"},{\"costCenterDefaultType\":\"HT\",\"costCenterDefaultTitle\":\"酒店关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of hotel\"},{\"costCenterDefaultType\":\"TT\",\"costCenterDefaultTitle\":\"火车票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of train\"},{\"costCenterDefaultType\":\"CT\",\"costCenterDefaultTitle\":\"用车关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of car\"},{\"costCenterDefaultType\":\"BT\",\"costCenterDefaultTitle\":\"汽车票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No.\"},{\"costCenterDefaultType\":\"D1\",\"costCenterDefaultTitle\":\"自定义字段1\",\"costCenterDefaultTitleEn\":\"Self-defined title No.1\"},{\"costCenterDefaultType\":\"D2\",\"costCenterDefaultTitle\":\"自定义字段2\",\"costCenterDefaultTitleEn\":\"Self-defined title No.2\"},{\"costCenterDefaultType\":\"CC1\",\"costCenterDefaultTitle\":\"成本中心1\",\"costCenterDefaultTitleEn\":\"Cost center No.1\"},{\"costCenterDefaultType\":\"CC2\",\"costCenterDefaultTitle\":\"成本中心2\",\"costCenterDefaultTitleEn\":\"Cost center No.2\"},{\"costCenterDefaultType\":\"CC3\",\"costCenterDefaultTitle\":\"成本中心3\",\"costCenterDefaultTitleEn\":\"Cost center No.3\"},{\"costCenterDefaultType\":\"CC4\",\"costCenterDefaultTitle\":\"成本中心4\",\"costCenterDefaultTitleEn\":\"Cost center No.4\"},{\"costCenterDefaultType\":\"CC5\",\"costCenterDefaultTitle\":\"成本中心5\",\"costCenterDefaultTitleEn\":\"Cost center No.5\"},{\"costCenterDefaultType\":\"CC6\",\"costCenterDefaultTitle\":\"成本中心6\",\"costCenterDefaultTitleEn\":\"Cost center No.6\"}],\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2024-12-27 15:47:56.825+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", MatchCostCenterResponseType.class)
        costCenterInfoType = matchCostCenterResponseType.getResult()
        resultNew = mapper.buildSaveOrderCostCenterRequest(orderCreateRequestType, accountInfo, checkAvailInfo, orderCreateToken, createOrderResponseType, costCenterInfoType, approvalTextInfoResponseType, ssoInfoQueryResponseType, resourceToken, null, null)
        then:
        resultNew != null
        resultNew.saveList.size() == 1
        resultNew.saveList.get(0).bizType == 4
        resultNew.saveList.get(0).orderID == ****************


        // 对比发现的问题，取样单测
        when: // 后台配置成本中心1,但是未填写页成本中心1,也需要落地成本中心1数据 c10046ce9cbc4136b66691d04f6265d1  2024-12-30T10:44:45
        queryCheckAvailContextResponseType = JsonUtil.fromJson("{\"baseInfo\":{\"userInfo\":{\"uid\":\"**********\",\"policyUid\":\"\",\"corpId\":\"ZHIENBIO\",\"eid\":\"\",\"usersCityId\":606,\"userCountryCode\":\"CN\"},\"customCurrency\":\"CNY\",\"website\":\"APP\",\"feeType\":\"C\",\"quantity\":1,\"startTime\":\"2024-12-30T00:00:00+08\",\"endTime\":\"2024-12-31T00:00:00+08\",\"language\":\"CN\",\"locale\":\"zh-CN\",\"startTimeUTC\":\"2024-12-29T16:00:00Z\",\"endTimeUTC\":\"2024-12-30T16:00:00Z\",\"pOS\":\"zh-CN\",\"pOSType\":{\"pOS\":\"zh-CN\",\"countryId\":1},\"platform\":\"android\",\"bookingWithPersonalAccount\":false,\"bookingChannel\":\"APP\"},\"hotelInfo\":{\"subHotelId\":479871,\"masterHotelId\":479870,\"star\":4,\"rStar\":0,\"starLicence\":true,\"customerEval\":0.0,\"hotelGroupId\":0,\"oversea\":false,\"tripBusinessHotel\":false,\"cuHotel\":false,\"hasServiceFee\":false,\"localTimeOffsetHours\":0,\"telephone\":\"+86-591-********\",\"geographicalInfo\":{\"locationInfo\":{\"id\":326},\"cityInfo\":{\"id\":258,\"name\":{\"textGB\":\"福州\",\"textEn\":\"Fuzhou\"}},\"provinceInfo\":{\"id\":19},\"countryInfo\":{\"id\":1},\"coordinateInfo\":{\"googleLat\":26.092728,\"googleLng\":119.305947,\"gdLat\":26.092728,\"gdLng\":119.305947,\"bdLat\":26.098805,\"bdLng\":119.312438}},\"address\":{\"textGB\":\"五四路130号\",\"textEn\":\"No.130 Wusi Road\"},\"hotelName\":{\"textGB\":\"福建省闽江饭店\",\"textEn\":\"Min Jiang Hotel\"},\"currentCountry\":true,\"timeZoneId\":\"Asia/Shanghai\",\"vendorId\":0},\"roomInfo\":{\"subRoomId\":*********,\"roomName\":{\"textGB\":\"豪华温泉大床房\",\"textEn\":\"Deluxe Hot Spring Double Room\"},\"basicRoomName\":{\"textGB\":\"豪华温泉大床房\",\"textEn\":\"Deluxe Hot Spring Double Room\"},\"masterBasicRoomId\":249974643,\"masterBasicRoomName\":{\"textGB\":\"豪华温泉大床房\"},\"roomType\":\"M\",\"tmcPriceType\":\"NONE\",\"balanceType\":\"PP\",\"mealType\":4,\"addPrice\":false,\"invoiceType\":\"Special\",\"invoiceLimitType\":\"ByCtrip\",\"applyAreaPropertyId\":0,\"maxGuestCountPerRoom\":2,\"maskRoom\":false,\"hourlyRoom\":false,\"corpPrice\":false,\"manualGds\":false,\"joined30MinutesFreeCancel\":false,\"premiumRoom\":false,\"welfareRoom\":false,\"groupMemberShip\":false,\"earnHotelPoints\":false,\"bonusPointInfo\":{\"bonusPointRoom\":false},\"earlyArrivalTime\":\"2024-12-30T14:00:00+08\",\"lastCancelTime\":\"2024-12-30T18:00:00+08\",\"cnyAmount\":480,\"originAmountInfo\":{\"amount\":480,\"currency\":\"CNY\",\"exchange\":1},\"customAmountInfo\":{\"amount\":480,\"currency\":\"CNY\",\"exchange\":1},\"originCostInfo\":{\"amount\":397.75,\"currency\":\"CNY\",\"exchange\":1},\"originCostBeforeTaxInfo\":{\"amount\":397.75,\"currency\":\"CNY\",\"exchange\":1},\"roomDiscountInfo\":{\"promotionSwitchOn\":true,\"customerTagTypes\":[{\"key\":\"EnableNewGroupMember\",\"value\":\"T\"},{\"key\":\"MaskCampaignRoomWhiteList\",\"value\":\"1\"},{\"key\":\"enableMobileRateRoom\",\"value\":\"T\"},{\"key\":\"IsPrepayDiscountMoney\",\"value\":\"T\"},{\"key\":\"PrepayDiscountCtripNewCustomer\",\"value\":\"F\"},{\"key\":\"PrepayDiscountHotelNewCustomer\",\"value\":\"F\"},{\"key\":\"BrandNewCustomer\",\"value\":\"T\"},{\"key\":\"CtripGroupID\",\"value\":\"-1\"},{\"key\":\"CtripGroupLevel\",\"value\":\"10001\"},{\"key\":\"SwitchList\",\"value\":\"53,44,92,50,121,136,69,94,103,122,123,150,156,159,157\"},{\"key\":\"UserCountryCode\",\"value\":\"CN\"}],\"bookRulePromotionList\":[],\"roomDailyDiscountInfo\":[{\"effectDate\":\"2024-12-30T00:00:00+08\",\"tagId\":300,\"ruleConfigId\":*********,\"prepayCampaignId\":5,\"typeConfigId\":6,\"discountType\":1,\"discountCnyAmount\":96,\"discountAmount\":96,\"discountCustomAmount\":96},{\"effectDate\":\"2024-12-30T00:00:00+08\",\"tagId\":300,\"ruleConfigId\":0,\"prepayCampaignId\":774,\"typeConfigId\":0,\"discountType\":4,\"discountCnyAmount\":29,\"discountAmount\":29,\"discountCustomAmount\":29}],\"originPromotionAmount\":{\"price\":125,\"currency\":\"CNY\"},\"customPromotionAmount\":{\"price\":125,\"currency\":\"CNY\"},\"customerPropertys\":[{\"propertyKey\":\"146\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"167\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"168\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"177\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"180\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"185\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"107\",\"propertyValue\":\"222\",\"propertyType\":0},{\"propertyKey\":\"111\",\"propertyValue\":\"222\",\"propertyType\":0},{\"propertyKey\":\"347\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"332\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"104\",\"propertyValue\":\"100\",\"propertyType\":0},{\"propertyKey\":\"176\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"595\",\"propertyValue\":\"0\",\"propertyType\":0,\"propertyExtensionInfo\":{\"extensionItemList\":[{\"itemKey\":\"10\",\"flag\":0},{\"itemKey\":\"21\",\"flag\":0},{\"itemKey\":\"32\",\"flag\":0},{\"itemKey\":\"319\",\"flag\":0}],\"type\":4}},{\"propertyKey\":\"597\",\"propertyValue\":\"0\",\"propertyType\":0,\"propertyExtensionInfo\":{\"extensionItemList\":[{\"itemKey\":\"321\",\"flag\":0},{\"itemKey\":\"741\",\"flag\":0}],\"type\":4}},{\"propertyKey\":\"599\",\"propertyValue\":\"0\",\"propertyType\":0,\"propertyExtensionInfo\":{\"extensionItemList\":[{\"itemKey\":\"576\",\"flag\":0},{\"itemKey\":\"11\",\"flag\":0},{\"itemKey\":\"587\",\"flag\":0},{\"itemKey\":\"14\",\"flag\":0},{\"itemKey\":\"724\",\"flag\":0},{\"itemKey\":\"344\",\"flag\":0},{\"itemKey\":\"281\",\"flag\":0},{\"itemKey\":\"1692\",\"flag\":0},{\"itemKey\":\"418\",\"flag\":0},{\"itemKey\":\"363\",\"flag\":0},{\"itemKey\":\"939\",\"flag\":0},{\"itemKey\":\"570\",\"flag\":0}],\"type\":4}},{\"propertyKey\":\"601\",\"propertyValue\":\"0\",\"propertyType\":0,\"propertyExtensionInfo\":{\"extensionItemList\":[{\"itemKey\":\"897\",\"flag\":0},{\"itemKey\":\"770\",\"flag\":0},{\"itemKey\":\"325\",\"flag\":0},{\"itemKey\":\"347\",\"flag\":0},{\"itemKey\":\"414\",\"flag\":0},{\"itemKey\":\"1128\",\"flag\":0},{\"itemKey\":\"1961\",\"flag\":0},{\"itemKey\":\"2997\",\"flag\":0},{\"itemKey\":\"1974\",\"flag\":0}],\"type\":4}},{\"propertyKey\":\"100\",\"propertyValue\":\"0\",\"propertyType\":1}]},\"roomDailyInfo\":[{\"effectDate\":\"2024-12-30T00:00:00+08\",\"cnyAmount\":480,\"amount\":480.0,\"customAmount\":480,\"afterPromotionCnyAmount\":355,\"afterPromotionCustomAmount\":355,\"customizedCnyAmount\":0,\"mealNumber\":2,\"afterPromotionCustomAmountExcludeTax\":355.00,\"afterPromotionCnyAmountExcludeTax\":355.00,\"customAmountExcludeTax\":480.00,\"cnyAmountExcludeTax\":480.00}],\"paymentRulesInfo\":{\"paymentGuaranteePoly\":\"TRIP\",\"canTravelMoneyPay\":true,\"forceVccPay\":false,\"canCreditCardPay\":true},\"longRentalInfo\":{\"longRental\":false,\"needInquire\":false},\"holdRoomInfo\":{\"holdRoom\":false},\"extraPayTaxMultiCurrencyAmountInfo\":{\"customExtraPayTaxTotalAmount\":0,\"cnyExtraPayTaxTotalAmount\":0},\"pricedRoom\":false,\"generalizedPremiumRoom\":false,\"customAmountExcludeTaxInfo\":{\"amount\":480,\"currency\":\"CNY\",\"exchange\":1},\"supportAnticipation\":true,\"roomRightInfoList\":[],\"invoiceTypeExcludeAddPrice\":\"Special\",\"roomMealInfo\":{\"mealType\":4,\"mealDescList\":[\"2份早餐\"],\"dailyMealInfoList\":[{\"effectDateUTC\":\"2024-12-29T16:00:00Z\",\"mealDescList\":[\"\"]},{\"effectDateUTC\":\"2024-12-30T16:00:00Z\",\"mealDescList\":[\"2份早餐\"]}]},\"saleRoomTags\":[{\"tagCode\":\"QWZD\",\"tagName\":\"商旅特惠\",\"tagDesc\":\"十亿豪补，每间夜立减125元\",\"configInfoList\":[]}],\"packageRoomInfo\":{\"packageRoom\":false,\"packageId\":0,\"xProductId\":[]},\"taxDetails\":[],\"pid\":\"AQoDNDgwEgY0ODAuMDAqA0NOWTINCgNDTlkSA0NOWRoBMTINCgNDTlkSA0NOWRoBMToCCABCBwjoDxAMGB5KBwjoDxAMGB9QAVgAaAN4AoAB/6Qd\",\"supplierChannel\":\"0\",\"onlyGroupMemberCanBook\":false},\"bookingRules\":{\"cancelPenalties\":{\"paymentGuaranteePoly\":\"TRIP\",\"ladderDeduct\":true,\"cancelDeductDetail\":{\"originDeductionPrice\":{\"price\":355,\"currency\":\"CNY\"},\"customDeductionPrice\":{\"price\":355,\"currency\":\"CNY\"}}},\"authorizationRestriction\":{},\"confirmRules\":{\"justifyConfirm\":true,\"confirmDuration\":0,\"forbidNotImConfirm\":false},\"welfareRoomRules\":{},\"limitDepartureDateTime\":{\"departureEnd\":\"2024-12-31T12:00:00+08\",\"departureEndUTC\":\"2024-12-31T04:00:00Z\"},\"certificateInfo\":{},\"minLOS\":1,\"cancelPolicyInfo\":{\"cancelType\":\"LADDER_FREE\",\"freeCancelPolicySceneType\":\"NOT_SUPPORTED\",\"lastCancelTimeUTC\":\"2024-12-30T10:00:00Z\",\"guaranteePolicyInfo\":{\"guaranteeType\":\"FIRST_DAY_PREPAY\",\"paymentGuaranteePoly\":\"TRIP\",\"guaranteeReason\":\"NONE\",\"guaranteePriceInfo\":{\"originGuaranteePrice\":{\"price\":355,\"currency\":\"CNY\"},\"customGuaranteePrice\":{\"price\":355,\"currency\":\"CNY\"}},\"cancelDeductDetailInfo\":[{\"deductionStartTimeUTC\":\"2024-12-30T02:44:34Z\",\"deductionEndTimeUTC\":\"2024-12-30T10:00:00Z\",\"deductionRatio\":0.0,\"deductionType\":\"RADIO\",\"originDeductionPrice\":{\"price\":0,\"currency\":\"CNY\"},\"customDeductionPrice\":{\"price\":0,\"currency\":\"CNY\"}},{\"deductionStartTimeUTC\":\"2024-12-30T10:00:00Z\",\"deductionEndTimeUTC\":\"0001-12-29T16:00:00Z\",\"deductionRatio\":1,\"deductionType\":\"NIGHT\",\"originDeductionPrice\":{\"price\":355,\"currency\":\"CNY\"},\"customDeductionPrice\":{\"price\":355,\"currency\":\"CNY\"}}]},\"localLastCancelTime\":\"2024-12-30 18:00:00\"},\"limitArrivalDateTime\":{\"arrivalFromUTC\":\"2024-12-30T06:00:00Z\",\"arrivalEndUTC\":\"2024-12-30T22:00:00Z\",\"defaultArrivalTimeEnd\":true,\"localEarlyArrivalTime\":\"2024-12-30 14:00:00\",\"localLastArrivalTime\":\"2024-12-31 06:00:00\"},\"rPLimitInfo\":{\"minLos\":1,\"maxLos\":999,\"minAdvancedBookingDays\":0,\"maxAdvancedBookingDays\":999},\"nationalityRestrictionInfo\":{\"allowCountryCodeList\":[],\"blockCountryCodeList\":[]},\"billingGuestInfo\":{\"guestsNameLanguages\":[\"zh\",\"en\"]},\"restrictRuleList\":[]},\"bookExtInfo\":{\"featureControlFlagMap\":{\"EnableMemberPoints\":false,\"EnableXMinutesFreeCancel\":false,\"BrandNewCustomer\":true,\"EnableShadowDiscount\":true,\"EnableYXH\":true,\"EnablePrepayDiscount\":true,\"EnableNewGroupMember\":true,\"ForbidMultiNationality\":false,\"NewUserCountryCode\":true,\"EnableCNYTWODecimal\":true,\"ShieldMaskRoom\":false,\"EnableAddPrice\":false,\"IgnoreSpecialCancelPolicyNotMatchingFilter\":false,\"StrictCancellationPolicy\":false,\"IgnoreRemitTax\":false,\"ShieldMultiplePrice\":true,\"Enable30MinuteFreeCancel\":true,\"EnablePromotionSubTag\":true,\"EnableHotelNewCustomer\":true,\"DisableIPBlock\":false,\"EnableDirectRate\":false,\"EnableOutputPackageRoom\":true,\"ShieldMaskNakedSale\":false,\"EnableMobileRate\":true,\"EnableMatchPolicyWithExtraPayTax\":false},\"prepayDiscountCampaignIdWhiteList\":[],\"prepayDiscountCampaignIdBlackList\":[],\"customTagKeysToken\":\"V11-d85f958d6176dee854037ca1e5e6cf948b40423c571dc921234c28b0d2e57553\",\"switchListValuesToken\":\"V4-766aefe9110e8a58f5f2d09e7234a8b9339711f60855cffb3f07b90b4796133b\"},\"userRightsInfo\":{\"baseGroupLevel\":\"10001\",\"userChosenCouponInfo\":[],\"hotelNewCustomer\":false,\"userSelectedCountryCode\":\"CN\",\"yxhGroupId\":\"-1\",\"multiCouponTotalCustomAmount\":0},\"reservationToken\":\"AQgBEgoyMDI0LTEyLTMwGhsKAU0SAlBQGgRUUklQMgNDTlk6ATFI0sKt3AIiAggAKg8KBXpoLUNOEgNDTlkaATE\\u003d\",\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2024-12-30 10:44:42.226+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", QueryCheckAvailContextResponseType.class)
        checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsId")))
                        .build().getCheckAvailContextInfo()
        orderCreateRequestType = JsonUtil.fromJson("{\"integrationSoaRequestType\":{\"userInfo\":{\"userId\":\"**********\",\"corpId\":\"ZHIENBIO\",\"groupId\":\"Gr_00008120\",\"pos\":\"CHINA\"},\"token\":\"BDBF3DE6B19F643181BDB6D6AC41F63C99968BEC5D842214A93EACFAACDA4551\",\"language\":\"zh-CN\",\"requestId\":\"c10046ce9cbc4136b66691d04f6265d1\",\"sourceFrom\":\"H5\",\"transactionID\":\"TID.SEARCHHOTEL.5.AB23F3323B3A4079814EB5625DB3084B\",\"gatewayHost\":\"ct.ctrip.com\",\"timezoneOffsetMinutesNew\":\"480\",\"logIndices\":[{\"key\":\"groupId\",\"value\":\"Gr_00008120\"},{\"key\":\"timezoneOffsetMinutesNew\",\"value\":\"480\"},{\"key\":\"gatewayHost\",\"value\":\"ct.ctrip.com\"},{\"key\":\"pvid\",\"value\":\"1\"},{\"key\":\"subChannel\",\"value\":\"\"},{\"key\":\"CID\",\"value\":\"09031072316984571192\"},{\"key\":\"EID\"},{\"key\":\"CIP\",\"value\":\"2408:844b:9a80:27a7:30e2:c9ff:fe51:b346\"},{\"key\":\"miceToken\",\"value\":\"\"},{\"key\":\"groupName\",\"value\":\"植恩生物技术股份有限公司\"},{\"key\":\"gatewayIdc\",\"value\":\"SHAXY\"},{\"key\":\"traceID\",\"value\":\"\"},{\"key\":\"A\",\"value\":\"ordercheck\"},{\"key\":\"CorpID\",\"value\":\"ZHIENBIO\"},{\"key\":\"C\",\"value\":\"corpbookinghotelbookservice\"},{\"key\":\"M\",\"value\":\"**********\"},{\"key\":\"sid\",\"value\":\"5\"},{\"key\":\"S\",\"value\":\"e44043a2e1464490a3f41962718b46ea39b313db233e\"},{\"key\":\"T\",\"value\":\"BDBF3DE6B19F643181BDB6D6AC41F63C99968BEC5D842214A93EACFAACDA4551\"},{\"key\":\"pos\",\"value\":\"CHINA\"},{\"key\":\"GE\",\"value\":\"1735526681528\"},{\"key\":\"sourceFrom\",\"value\":\"H5\"},{\"key\":\"corpName\",\"value\":\"植恩生物技术股份有限公司\"},{\"key\":\"RID\",\"value\":\"c10046ce9cbc4136b66691d04f6265d1\"},{\"key\":\"TID\",\"value\":\"TID.SEARCHHOTEL.5.AB23F3323B3A4079814EB5625DB3084B\"},{\"key\":\"VID\",\"value\":\"1677637092830.tu7mdk\"}],\"ticket\":\"mV6LL9DenQ8l2eQe/yOeZBgSvOAjYsEUw8mMZMx7SM/cFPPvA7BC18G1HOEmVNblAqLsKktTX7Eqhqp+s1P4k044pLwxT2s60LjJLA0CJs4\\u003d\",\"transferInfo\":[{\"key\":\"iframe\",\"value\":\"F\"},{\"key\":\"channel\",\"value\":\"Native\"}]},\"hotelBookPassengerInputs\":[{\"hotelPassengerInput\":{\"roomIndex\":1,\"uid\":\"**********\",\"approvalPassengerId\":\"5039449223975546303\",\"employee\":\"T\",\"external\":\"F\",\"employeeId\":\"102228\"},\"passengerBasicInfo\":{\"preferFirstName\":\"qiyou\",\"preferLastName\":\"huang\",\"gender\":\"U\"},\"name\":\"黄启友\",\"isRegistrant\":\"F\",\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"13218393025\",\"transferPhoneNo\":\"13218393025\"},\"nationalityInfo\":{\"nationalityCode\":\"CN\"}}],\"hotelInvoiceInfos\":[],\"cityInput\":{\"cityId\":258},\"hotelContactorInfo\":{\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"13218393025\",\"transferPhoneNo\":\"13218393025\"},\"emailInfo\":{\"email\":\"<EMAIL>\",\"transferEmail\":\"<EMAIL>\"}},\"approvalInput\":{\"masterApprovalNo\":\"C202412280000022\",\"subApprovalNo\":\"C202412280000022\",\"emergency\":\"F\"},\"miceInput\":{},\"hotelBookInput\":{\"hotelDateRangeInfo\":{\"checkIn\":\"2024-12-30\",\"checkOut\":\"2024-12-31\"},\"roomQuantity\":1},\"membershipInfo\":{},\"bookModeInfo\":{\"bookingType\":\"NONE\"},\"resourceTokenInfo\":{\"resourceToken\":\"{\\\"hotelResourceToken\\\":{\\\"masterHotelId\\\":479870,\\\"hotelId\\\":479871,\\\"searchHotelTraceId\\\":\\\"5c83e1ecbfc5470687923ccf7fc074e9\\\",\\\"hid\\\":\\\"ASIDMjcyQgFHSgYIABAAGABSBBAAGABaA0NOWWINCgNDTlkSA0NOWRoBMWIPCgNDTlkSA0NOWRoDMS4waAhyBjQwMC4wMHoEMC4wMIIBIDVjODNlMWVjYmZjNTQ3MDY4NzkyM2NjZjdmYzA3NGU5igEKMTE3MDkzNzY4MpoBJDE3NTkxMmJlLWIwOWUtNGI1Yy1iYTUzLTM5MGZiODczZDRhMqIBAggAqgEkZTA0YjZkMGYtY2I2Mi00OGMxLTk5MGQtYjE4OWRjOTIyN2E4\\\",\\\"hotelType\\\":\\\"M\\\",\\\"bonusPoint\\\":\\\"F\\\",\\\"hotelGeoInfoResourceToken\\\":{\\\"cityId\\\":258,\\\"locationId\\\":326}},\\\"roomResourceToken\\\":{\\\"roomId\\\":*********,\\\"baseRoomId\\\":249974643,\\\"ratePlanTraceLogId\\\":\\\"3f2b02b022cd433a97514c5c38a9dbd8_\\\",\\\"bedName\\\":\\\"1张1.5米双人床\\\",\\\"windowName\\\":\\\"有窗\\\",\\\"breakfastInfo\\\":\\\"2份早餐\\\",\\\"confirmType\\\":\\\"IMMEDIATE\\\",\\\"roomType\\\":\\\"M\\\",\\\"balanceType\\\":\\\"PP\\\",\\\"tmcPrice\\\":false,\\\"adult\\\":2,\\\"pId\\\":\\\"AQoDNDgwEgY0ODAuMDAqA0NOWTINCgNDTlkSA0NOWRoBMTINCgNDTlkSA0NOWRoBMToCCABCBwjoDxAMGB5KBwjoDxAMGB9QAVgAaAN4AoAB/6Qd\\\",\\\"amadeus\\\":false,\\\"supportAccommodation\\\":\\\"T\\\",\\\"roomOriginCurrency\\\":\\\"CNY\\\",\\\"roomScenarioEnum\\\":\\\"ROOM_LIST\\\",\\\"ratePlanRequestToken\\\":\\\"ASi1L/0AUAUWAEZvqEAwTTTAwMA4jDiqDop2QqGtOOSaufCRJnEYRyaIAbYIkIlYlmWDJDZB2kRS/n48EAPy5YguoOXa6TIaPJIVt/r/lwCXAJUAGbWOM89rjljTIvtfw3kY2849zkbZg2k638m8NWfee+/BsaZL+vRgTeuuq4s/UtWmBgULsYt03D1OrcFqD/uUXgy8+G5Ee1aDofmaC3nEcJbs8PDYFA4QnMPmGs4Oc/AMGOcXOK/Aaf5aoioC6RZE0qtOcb7PO8ALAAkdBgHVAiJ42++0zbKh6NZlTa4KpWJ1CkKDDScqAlw1z221caVkJtsLO/Kcm88ayyTlikeK2fm85PghmejLZTN+j1npa13XC8aVsYIcTJiymXDNyDYUn6Nl9pCfvUIKFtDZq1ETgR3KCaQ1H3aw8Ls+rJ0RCujykECzqWaEscyCqUYEjxsKhth2MqJ2E9O0CIMZ/V9XnIgUsuZrUun4E2hifdzH0v0EphdpoXFBXqxn9z+shQ3rS2ufVHxdsXVdGMHDW2ikglgWJNKrjoqhJqLfI3h8Ti3gG1084byzVEdZmHnZp5TSku2UuUgp9d+mk0+uic0okdxYEn1ezDVp3FDpvaHLbT2jpj+SJzOv2eJ1EMfNfCnBWCvbqFRDyynBwCTz3MQujMuu+dMFtmTG7sEZ04QrYTdSshrZljQTh2wj4khBrWV5VefLDGrIhVVSOoCX2LzE5iE2/7B5h801bI5hcwubV9j0fjdoT9jm/rZlV3a1H2wFu2JTdE4igAkKBx/BK6gCXITXQlAjPXT7Cx6eUw74BrZpeBj0AzEQLbWuwMNz6oAIeiG6fQKPjFMG+EbG80A067RMxsM0EN2+gYfnVAK+saou9tJazPEv7GJrnIIrm8YJHp9TDPhGTFrbEBwJAPLEkBUKEDUQsgHYCfO7m86E/ymmlx3IoZAB\\\",\\\"ratePlanRoomToken\\\":\\\"ASi1L/0AUFkAAAoBTRAAGNLCrdwC\\\",\\\"roomUniqueKey\\\":\\\"V1-ASi1L/0AUJkAAAjSwq3cAhIBTRoETk9ORSICUFA\\\\u003d\\\",\\\"adultNumber\\\":2,\\\"windowType\\\":2},\\\"reservationResourceToken\\\":{\\\"wsId\\\":\\\"043cb21e-7d30-450c-8c16-a5b8e3d5a1ba\\\",\\\"policyToken\\\":\\\"v1KLUv/QBQ/QkAxpNAJQCrqgCe1CQzyCIoZPwlA8d2fy9+y97zrEovMRxQsL/a////X0I1ADcANgCf3UDt2w2oqbGgCBTuDims0vf2JmoQkQRJjXN3GK97ZeuoTEXGM15vDAPAMI97v1irjK87BBk/KlxxtdmiJ5Ih7h2pLFH0i5TajoZXvhnf3jwMbB4LASOnWazIsRYY8DyLhXAgC0MQiSUGugv0StiOwDtJGSfJFFoxjN+kvNPdTmJB5UVJKxsnwkXpgZ30jbz1rdZd8iwzLG3MliQ0UDpNfgEPDBd4u/T26O3QF9xlU7g+JgUp+y6kFctmIms7qQnFZOQBGy/rOia/CldZZCS6Vi0tnj1vFgA6G4djh8O1w35z8cxdiCqEqTloqELqAqYI6lClahZLqMrzWTwGqtyr2COMWqoAzg0g5XVmDiubTQ\\\\u003d\\\\u003d\\\"},\\\"bookInitResourceToken\\\":{\\\"aggBookPriceResourceToken\\\":{\\\"currency\\\":\\\"CNY\\\",\\\"accountPaymentAmount\\\":355,\\\"individualPaymentAmount\\\":0,\\\"mixPayAccountExtraAmount\\\":0},\\\"roomPriceResourceToken\\\":{}}}\"},\"corpPayInfo\":{\"corpPayType\":\"public\"},\"userActionInfo\":{\"userActionToken\":\"PRD-1358-8b09cada-8f40-4913-b3e3-437e53c71cfb-********-4.1.15-WEB\"},\"rcInfos\":[],\"bookUrlInfos\":[{\"urlType\":\"SUCCESS\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayState/PayState?site\\u003d2\\u0026LimitStandard\\u003dfalse\\u0026needFinishResult\\u003dtrue\\u0026\"},{\"urlType\":\"S_BACK\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayState/PayState?site\\u003d2\\u0026LimitStandard\\u003dfalse\\u0026needFinishResult\\u003dtrue\\u0026\"},{\"urlType\":\"E_BACK\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayMent/PayMent\"},{\"urlType\":\"FROM_URL\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayMent/PayMent?leomacmd://navi?%7b%22backward%22%3atrue%2c%22navi%22%3a0%2c%22now%22%3atrue%2c%22platform%22%3a%7b%7d%7d\"},{\"urlType\":\"S_BACK_MICE\",\"urlValue\":\"https://ct.ctrip.com/mice-activities/welfareTourism\"},{\"urlType\":\"FROM_URL_ORDER_DETAIL\",\"urlValue\":\"https://ct.ctrip.com/webapp/hotel/process/detail?orderId\\u003d{0}\\u0026language\\u003d{1}\\u0026from\\u003dfinish\"},{\"urlType\":\"OFFLINE_RETURN_URL\",\"urlValue\":\"http://corpint.ct.ctripcorp.com/hotel-online/offlineOrder/success\"},{\"urlType\":\"ORDER_DETAIL_URL\",\"urlValue\":\"https://ct.ctrip.com/m/Detail/Hotel/{0}\"}],\"paymentInfoInput\":{\"canWechatPayByOthers\":\"T\",\"clientType\":\"APP\"},\"clientInfo\":{\"clientType\":\"APP\",\"rmsToken\":\"fp\\u003d\\u0026vid\\u003d1677637092830.tu7mdk\\u0026pageId\\u003d10650065356\\u0026r\\u003d69077811a3b14823b0d66dfd7afd91fd\\u0026ip\\u003d2408:844b:9a80:27a7:30e2:c9ff:fe51:b346\\u0026rg\\u003dfin\\u0026screen\\u003d360x800\\u0026tz\\u003d+8\\u0026blang\\u003dzh-CN\\u0026oslang\\u003dzh-CN\\u0026ua\\u003dMozilla%2F5.0%20(Linux%3B%20Android%2014%3B%20V2232A%20Build%2FUP1A.231005.007%3B%20wv)%20AppleWebKit%2F537.36%20(KHTML%2C%20like%20Gecko)%20Version%2F4.0%20Chrome%2F116.0.0.0%20Mobile%20Safari%2F537.36%5Cvivo%7CPD2232%7Cvivo%7CPD2232%7CV2232A%2CAndroid%2C14%2C9.26.0%2CCorpCtrip%2CCoreInside_CorpWireless%2C5G%2Ccom.ctrip.ct.vivo%20Titans%2F9.26.0%20NetType%2F5G%7Ccorp_ch_0000\\u0026v\\u003dm17\\u0026bl\\u003dfalse\\u0026clientid\\u003d\",\"secondaryChannel\":\"android9.26.0-new\"},\"offlineInfo\":{\"forciblyAssure\":\"F\",\"especialAuth\":\"F\",\"autoConfirm\":\"F\",\"exigentOrder\":\"F\"},\"roomInfoInput\":{},\"sendInfo\":{\"sendSMS\":\"F\",\"sendEmail\":\"F\"},\"flashStayInput\":{\"flashPlatFrom\":\"H5\"},\"hotelPayTypeInput\":[{\"payCode\":\"ROOM\",\"payType\":\"CORP_PAY\"}],\"strategyInfos\":[{\"strategyKey\":\"BOOKING_SCENARIO\",\"strategyValue\":\"HOTEL_LIST\"},{\"strategyKey\":\"BOOKING_WITH_PERSONAL_ACCOUNT\",\"strategyValue\":\"F\"}],\"costCenterInfo\":{\"costCenterJsonString\":\"{\\\"items\\\":{\\\"fdefault\\\":{\\\"cost1\\\":\\\"\\\"}}}\"},\"useNewOrderCreate\":\"F\"}", OrderCreateRequestType.class)
        orderCreateRequestType.cityInput.cityId = 22249
        resultOld = JsonUtil.fromJson("{\"saveList\":[{\"bizType\":64,\"orderID\":****************,\"uid\":\"**********\",\"pid\":\"**********\",\"corpId\":\"ZHIENBIO\",\"eid\":\"**********\",\"costCenterDesc\":\"\",\"costCenterDescEn\":\"\",\"jounaryNo\":\"C202412280000022\",\"jounaryNoTitle\":\"酒店关联行程号\",\"jounaryNoTitleEn\":\"Associated itinerary No. of hotel\",\"orderCostCenters\":[{\"costCenterKey\":1,\"costCenterValue\":\"\",\"costCenterTitle\":\"外部人员\",\"costCenterTitleEn\":\"外部人员\"}],\"passengers\":[{\"passengerId\":\"**********\",\"passengerName\":\"黄启友\",\"isEmployee\":1}],\"frequentlyUsedCostCenter\":1,\"createTimeStr\":\"2024-12-30 10:44:45\"}]}", SaveOrderCostCenterRequestType.class)
        matchCostCenterResponseType = JsonUtil.fromJson("{\"result\":{\"costCenterDescription\":\"\",\"costCenterDescriptionEn\":\"\",\"costCenterContentlist\":[{\"groupID\":1,\"orderOrPerson\":\"O\",\"costCenterConfigs\":[{\"level\":1,\"costCenterTitle\":\"外部人员\",\"costCenterTitleEn\":\"外部人员\",\"required\":false,\"canChange\":true,\"linked\":false,\"selectHidden\":false,\"readUserCostCenter\":0,\"needAddress\":false}],\"costCenterItems\":[]}],\"costCenterExtlist\":[{\"costCenterExtType\":\"TP\",\"costCenterExtTitle\":\"出行目的\",\"costCenterExtTitleEn\":\"JounaryReason\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"PN\",\"costCenterExtTitle\":\"项目号\",\"costCenterExtTitleEn\":\"Project\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"FT\",\"costCenterExtTitle\":\"机票关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of flight\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"HT\",\"costCenterExtTitle\":\"酒店关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of hotel\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"TT\",\"costCenterExtTitle\":\"火车票关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of train\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"CT\",\"costCenterExtTitle\":\"用车关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of car\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"BT\",\"costCenterExtTitle\":\"汽车票关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No.\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"D1\",\"costCenterExtTitle\":\"自定义字段1\",\"costCenterExtTitleEn\":\"Self-defined title No.1\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"D2\",\"costCenterExtTitle\":\"自定义字段2\",\"costCenterExtTitleEn\":\"Self-defined title No.2\",\"required\":false,\"enabled\":false,\"selectHidden\":false}],\"frequentlyUsedCostCenter\":1},\"costCenterDefaultTitles\":[{\"costCenterDefaultType\":\"TP\",\"costCenterDefaultTitle\":\"出行目的\",\"costCenterDefaultTitleEn\":\"JounaryReason\"},{\"costCenterDefaultType\":\"PN\",\"costCenterDefaultTitle\":\"项目号\",\"costCenterDefaultTitleEn\":\"Project\"},{\"costCenterDefaultType\":\"FT\",\"costCenterDefaultTitle\":\"机票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of flight\"},{\"costCenterDefaultType\":\"HT\",\"costCenterDefaultTitle\":\"酒店关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of hotel\"},{\"costCenterDefaultType\":\"TT\",\"costCenterDefaultTitle\":\"火车票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of train\"},{\"costCenterDefaultType\":\"CT\",\"costCenterDefaultTitle\":\"用车关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of car\"},{\"costCenterDefaultType\":\"BT\",\"costCenterDefaultTitle\":\"汽车票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No.\"},{\"costCenterDefaultType\":\"D1\",\"costCenterDefaultTitle\":\"自定义字段1\",\"costCenterDefaultTitleEn\":\"Self-defined title No.1\"},{\"costCenterDefaultType\":\"D2\",\"costCenterDefaultTitle\":\"自定义字段2\",\"costCenterDefaultTitleEn\":\"Self-defined title No.2\"},{\"costCenterDefaultType\":\"CC1\",\"costCenterDefaultTitle\":\"成本中心1\",\"costCenterDefaultTitleEn\":\"Cost center No.1\"},{\"costCenterDefaultType\":\"CC2\",\"costCenterDefaultTitle\":\"成本中心2\",\"costCenterDefaultTitleEn\":\"Cost center No.2\"},{\"costCenterDefaultType\":\"CC3\",\"costCenterDefaultTitle\":\"成本中心3\",\"costCenterDefaultTitleEn\":\"Cost center No.3\"},{\"costCenterDefaultType\":\"CC4\",\"costCenterDefaultTitle\":\"成本中心4\",\"costCenterDefaultTitleEn\":\"Cost center No.4\"},{\"costCenterDefaultType\":\"CC5\",\"costCenterDefaultTitle\":\"成本中心5\",\"costCenterDefaultTitleEn\":\"Cost center No.5\"},{\"costCenterDefaultType\":\"CC6\",\"costCenterDefaultTitle\":\"成本中心6\",\"costCenterDefaultTitleEn\":\"Cost center No.6\"}],\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2024-12-30 10:44:45.070+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", MatchCostCenterResponseType.class)
        costCenterInfoType = matchCostCenterResponseType.getResult()
        createOrderResponseType = JsonUtil.fromJson("{\"wsId\":\"043cb21e-7d30-450c-8c16-a5b8e3d5a1ba\",\"orderId\":****************,\"orderPaymentInfo\":{\"payType\":\"ACCNT\",\"orderAmountInfo\":{\"cnyTotalAmount\":480,\"paymentItemList\":[{\"prepayType\":\"ACCNT\",\"gatheringType\":\"D\",\"feeType\":\"HotelRoomFee\",\"amountInfo\":{\"amount\":355,\"currency\":\"CNY\",\"exchangeRateToCNY\":1}}]}},\"success\":true,\"supportPaymentBill\":true,\"paymentTransactionId\":\"307929560073961499\",\"orderTotalAmount\":{\"orderAmount\":{\"originAmount\":{\"amount\":480,\"currency\":\"CNY\",\"exchange\":1},\"cNYAmount\":480,\"customAmount\":{\"amount\":480.00,\"currency\":\"CNY\",\"exchange\":1}},\"paymentBillInfo\":{\"payAmount\":355,\"payCNYAmount\":355,\"payCurrency\":\"CNY\",\"payExchange\":1},\"guaranteeAmount\":{\"originGuaranteeAmount\":{\"amount\":355,\"currency\":\"CNY\",\"exchange\":1},\"cNYGuaranteeAmount\":355}},\"transactionInfo\":{\"transactionId\":\"3c75ad50-1c2f-463d-864a-0d38be901f32\",\"subTransactionIdList\":[{\"payType\":\"ACCNT\",\"subTransactionId\":\"********-84ef-4acd-8244-9307619ae771\"}]},\"responseCode\":20000,\"responseStatus\":{\"timestamp\":\"2024-12-30 10:44:44.589+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", CreateOrderResponseType.class)
        resultNew = mapper.buildSaveOrderCostCenterRequest(orderCreateRequestType, accountInfo, checkAvailInfo, orderCreateToken, createOrderResponseType, costCenterInfoType, approvalTextInfoResponseType, ssoInfoQueryResponseType, resourceToken, null, null)
        // 抹平变量
        resultOld.saveList.get(0).createTimeStr = "2024-12-30 10:44:45"
        resultNew.saveList.get(0).createTimeStr = "2024-12-30 10:44:45"
        then:
        resultNew != null
        // 对比保存的内容需要完全一致
        resultNew == resultOld


        when: // 仅存在人下成本中心 c10046ce9cbc4136b66691d04f6265d1  2024-12-30T10:44:45
        queryCheckAvailContextResponseType = JsonUtil.fromJson("{\"baseInfo\":{\"userInfo\":{\"uid\":\"**********\",\"policyUid\":\"\",\"corpId\":\"LSHM\",\"eid\":\"\",\"usersCityId\":3884,\"userCountryCode\":\"\"},\"customCurrency\":\"CNY\",\"website\":\"APP\",\"feeType\":\"C\",\"quantity\":1,\"startTime\":\"2024-12-30T00:00:00+08\",\"endTime\":\"2024-12-31T00:00:00+08\",\"language\":\"CN\",\"locale\":\"zh-CN\",\"startTimeUTC\":\"2024-12-29T16:00:00Z\",\"endTimeUTC\":\"2024-12-30T16:00:00Z\",\"pOS\":\"zh-CN\",\"pOSType\":{\"pOS\":\"zh-CN\",\"countryId\":1},\"platform\":\"ios\",\"bookingWithPersonalAccount\":false},\"hotelInfo\":{\"subHotelId\":*********,\"masterHotelId\":*********,\"star\":3,\"rStar\":0,\"starLicence\":false,\"customerEval\":0.0,\"hotelGroupId\":5,\"hotelBrandId\":48,\"balancePeriod\":\"GM\",\"oversea\":false,\"tripBusinessHotel\":false,\"cuHotel\":false,\"hasServiceFee\":false,\"localTimeOffsetHours\":0,\"telephone\":\"+86-794-7272588\",\"geographicalInfo\":{\"locationInfo\":{\"id\":0},\"cityInfo\":{\"id\":21844,\"name\":{\"textGB\":\"南城\",\"textEn\":\"Nancheng\"}},\"provinceInfo\":{\"id\":18},\"countryInfo\":{\"id\":1},\"coordinateInfo\":{}},\"address\":{\"textGB\":\"威汕线与胜利路交叉路口往北约210米\",\"textEn\":\"\"},\"hotelName\":{\"textGB\":\"汉庭酒店(抚州南城火车站翡翠广场店)\",\"textEn\":\"\"},\"currentCountry\":true,\"timeZoneId\":\"Asia/Shanghai\"},\"roomInfo\":{\"subRoomId\":**********,\"roomName\":{\"textGB\":\"高级大床房A（高清可投屏电视+记忆枕）\",\"textEn\":\"Superior Queen RoomA\"},\"basicRoomName\":{\"textGB\":\"高级大床房A（高清可投屏电视+记忆枕）\",\"textEn\":\"Superior Queen RoomA\"},\"masterBasicRoomId\":503125856,\"masterBasicRoomName\":{\"textGB\":\"高级大床房A（高清可投屏电视+记忆枕）\"},\"roomType\":\"C\",\"tmcPriceType\":\"NONE\",\"balanceType\":\"PP\",\"gdsType\":\"HZ\",\"mealType\":4,\"priceSuitPropertyValueId\":1262918,\"ratePlanKey\":{\"checkAvlID\":0,\"ratePlanID\":\"0\"},\"addPrice\":false,\"invoiceType\":\"Special\",\"invoiceLimitType\":\"NotSupported\",\"applyAreaPropertyId\":0,\"maxGuestCountPerRoom\":2,\"maskRoom\":false,\"hourlyRoom\":false,\"corpPrice\":true,\"manualGds\":false,\"joined30MinutesFreeCancel\":false,\"premiumRoom\":false,\"welfareRoom\":false,\"groupMemberShip\":false,\"earnHotelPoints\":true,\"bonusPointInfo\":{\"bonusPointRoom\":true,\"bonusPointCode\":\"HZZSF\",\"pointsMode\":\"SJHMS\"},\"earlyArrivalTime\":\"2024-12-30T10:00:00+08\",\"lastCancelTime\":\"2024-12-30T20:00:00+08\",\"cnyAmount\":177.84,\"originAmountInfo\":{\"amount\":177.84,\"currency\":\"CNY\",\"exchange\":1},\"customAmountInfo\":{\"amount\":177.84,\"currency\":\"CNY\",\"exchange\":1},\"originCostInfo\":{\"amount\":169,\"currency\":\"CNY\",\"exchange\":1},\"originCostBeforeTaxInfo\":{\"amount\":169,\"currency\":\"CNY\",\"exchange\":1},\"roomDiscountInfo\":{\"promotionSwitchOn\":false,\"roomDailyDiscountInfo\":[],\"originPromotionAmount\":{\"price\":0,\"currency\":\"CNY\"},\"customPromotionAmount\":{\"price\":0,\"currency\":\"CNY\"}},\"roomDailyInfo\":[{\"effectDate\":\"2024-12-30T00:00:00+08\",\"cnyAmount\":177.84,\"amount\":177.84,\"customAmount\":177.84,\"afterPromotionCnyAmount\":177.84,\"afterPromotionCustomAmount\":177.84,\"cost\":169,\"costBeforeTax\":169,\"customizedCnyAmount\":0,\"mealNumber\":2,\"holdDeadline\":0,\"afterPromotionCustomAmountExcludeTax\":177.84,\"afterPromotionCnyAmountExcludeTax\":177.84,\"customAmountExcludeTax\":177.84,\"cnyAmountExcludeTax\":177.84}],\"paymentRulesInfo\":{\"paymentGuaranteePoly\":\"TRIP\",\"canTravelMoneyPay\":false,\"forceVccPay\":false,\"canCreditCardPay\":false},\"longRentalInfo\":{\"longRental\":false,\"needInquire\":false},\"holdRoomInfo\":{\"holdRoom\":false},\"agreementGiftsToken\":\"ChAI7PsBEgpIWlFZMTg4Njk4\",\"extraPayTaxMultiCurrencyAmountInfo\":{\"customExtraPayTaxTotalAmount\":0,\"cnyExtraPayTaxTotalAmount\":0},\"pricedRoom\":false,\"generalizedPremiumRoom\":false,\"customAmountExcludeTaxInfo\":{\"amount\":177.84,\"currency\":\"CNY\",\"exchange\":1},\"supportAnticipation\":false,\"roomRightInfoList\":[],\"invoiceTypeExcludeAddPrice\":\"Special\",\"roomMealInfo\":{\"mealType\":4,\"mealDescList\":[\"2份早餐\"],\"dailyMealInfoList\":[{\"effectDateUTC\":\"2024-12-29T16:00:00Z\",\"mealDescList\":[\"\"]},{\"effectDateUTC\":\"2024-12-30T16:00:00Z\",\"mealDescList\":[\"2份早餐\"]}]},\"saleRoomTags\":[{\"tagCode\":\"XYJ\",\"tagDesc\":\"\",\"configInfoList\":[{\"position\":301,\"priority\":2,\"style\":\"1\"}]}],\"taxDetails\":[],\"pid\":\"AQoGMTc3Ljg0EgYxNzcuODQigQEKf3YxX0NOYVI2em9RMVB2UDlnVWFCekV5TmpJNU1UZ2lBa2hhS2dKRFJ6SUdNVGMzTGpnME9nWXhOemN1T0RSQ0RRb0RRMDVaRWdORFRsa2FBVEZLSURBd056QTVNVGd6WVdaaE9EUmlPV1U0TW1ZM01UZzVaR00zTm1ZM04yVmkqA0NOWTINCgNDTlkSA0NOWRoBMTINCgNDTlkSA0NOWRoBMToCCABCBwjoDxAMGB5KBwjoDxAMGB9QAVgAYgJIWmgNeAKAAdaR6zo\\u003d\",\"onlyGroupMemberCanBook\":false},\"bookingRules\":{\"cancelPenalties\":{\"paymentGuaranteePoly\":\"TRIP\",\"cancelDeductDetail\":{\"originDeductionPrice\":{\"price\":177.84,\"currency\":\"CNY\"},\"customDeductionPrice\":{\"price\":177.84,\"currency\":\"CNY\"}},\"guaranteePolicyInfo\":{}},\"authorizationRestriction\":{\"latestAuthorizeTime\":\"2024-12-30T19:00:00+08\"},\"confirmRules\":{\"justifyConfirm\":true,\"forbidNotImConfirm\":false},\"welfareRoomRules\":{},\"limitDepartureDateTime\":{\"departureEnd\":\"2024-12-31T12:00:00+08\",\"departureEndUTC\":\"2024-12-31T04:00:00Z\"},\"certificateInfo\":{\"needCertificate\":false},\"minLOS\":1,\"cancelPolicyInfo\":{\"cancelType\":\"LIMIT\",\"freeCancelPolicySceneType\":\"NOT_SUPPORTED\",\"lastCancelTimeUTC\":\"2024-12-30T12:00:00Z\",\"guaranteePolicyInfo\":{\"guaranteeType\":\"FIRST_DAY_PREPAY\",\"paymentGuaranteePoly\":\"TRIP\",\"guaranteeReason\":\"NONE\",\"guaranteePriceInfo\":{\"originGuaranteePrice\":{\"price\":177.84,\"currency\":\"CNY\"},\"customGuaranteePrice\":{\"price\":177.84,\"currency\":\"CNY\"}}}},\"limitArrivalDateTime\":{\"arrivalFromUTC\":\"2024-12-30T02:00:00Z\",\"arrivalEndUTC\":\"2024-12-30T22:00:00Z\",\"defaultArrivalTimeEnd\":false},\"rPLimitInfo\":{\"minLos\":1,\"maxLos\":0,\"minAdvancedBookingDays\":0,\"maxAdvancedBookingDays\":0},\"billingGuestInfo\":{\"guestsNameLanguages\":[\"zh\",\"en\"]},\"restrictRuleList\":[]},\"bookExtInfo\":{\"featureControlFlagMap\":{\"EnableMemberPoints\":false,\"EnableXMinutesFreeCancel\":false,\"BrandNewCustomer\":true,\"EnableShadowDiscount\":false,\"EnableYXH\":false,\"EnablePrepayDiscount\":false,\"EnableNewGroupMember\":false,\"ForbidMultiNationality\":false,\"NewUserCountryCode\":true,\"EnableCNYTWODecimal\":true,\"ShieldMaskRoom\":true,\"EnableAddPrice\":false,\"IgnoreSpecialCancelPolicyNotMatchingFilter\":false,\"StrictCancellationPolicy\":false,\"IgnoreRemitTax\":false,\"ShieldMultiplePrice\":false,\"Enable30MinuteFreeCancel\":false,\"EnablePromotionSubTag\":true,\"EnableHotelNewCustomer\":false,\"DisableIPBlock\":false,\"EnableDirectRate\":false,\"EnableOutputPackageRoom\":false,\"ShieldMaskNakedSale\":false,\"EnableMobileRate\":true,\"EnableMatchPolicyWithExtraPayTax\":false}},\"userRightsInfo\":{\"userChosenCouponInfo\":[],\"hotelNewCustomer\":true,\"userSelectedCountryCode\":\"\"},\"reservationToken\":\"AQgBEgoyMDI0LTEyLTMwGhcKAUMSAlBQGgRUUklQIgJIWkjU+8/2BSICCAAqBwoFemgtQ04\\u003d\",\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2024-12-30 11:30:59.341+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", QueryCheckAvailContextResponseType.class)
        checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsId")))
                        .build().getCheckAvailContextInfo()
        orderCreateRequestType = JsonUtil.fromJson("{\"integrationSoaRequestType\":{\"userInfo\":{\"userId\":\"**********\",\"corpId\":\"LSHM\",\"groupId\":\"\",\"pos\":\"CHINA\"},\"token\":\"E7A0308D18BD18E5BF5728BF648AB24B35ABAC70561F25CD4A660BA39CC31798\",\"language\":\"zh-CN\",\"requestId\":\"feb380fffff34dc1a5c23fc4f91246fa\",\"sourceFrom\":\"H5\",\"transactionID\":\"TID.HOTELBOOK.2.521C82BC11C54C8886F4E622A3AFE0D6\",\"gatewayHost\":\"ct.ctrip.com\",\"timezoneOffsetMinutesNew\":\"480\",\"logIndices\":[{\"key\":\"groupId\",\"value\":\"\"},{\"key\":\"timezoneOffsetMinutesNew\",\"value\":\"480\"},{\"key\":\"gatewayHost\",\"value\":\"ct.ctrip.com\"},{\"key\":\"pvid\",\"value\":\"6\"},{\"key\":\"subChannel\",\"value\":\"WECOM\"},{\"key\":\"CID\",\"value\":\"09031145313113467225\"},{\"key\":\"EID\"},{\"key\":\"CIP\",\"value\":\"2409:8938:b2a4:2837:f447:1b50:6eae:fd8c\"},{\"key\":\"miceToken\",\"value\":\"\"},{\"key\":\"groupName\"},{\"key\":\"gatewayIdc\",\"value\":\"SHAXY\"},{\"key\":\"traceID\",\"value\":\"\"},{\"key\":\"A\",\"value\":\"ordercheck\"},{\"key\":\"CorpID\",\"value\":\"LSHM\"},{\"key\":\"C\",\"value\":\"corpbookinghotelbookservice\"},{\"key\":\"M\",\"value\":\"**********\"},{\"key\":\"sid\",\"value\":\"2\"},{\"key\":\"S\",\"value\":\"67e0379dc5994a82ae47b970126d9cd71431bb92d02d\"},{\"key\":\"T\",\"value\":\"E7A0308D18BD18E5BF5728BF648AB24B35ABAC70561F25CD4A660BA39CC31798\"},{\"key\":\"pos\",\"value\":\"CHINA\"},{\"key\":\"GE\",\"value\":\"1735529458479\"},{\"key\":\"sourceFrom\",\"value\":\"H5\"},{\"key\":\"corpName\",\"value\":\"湖南鸣鸣很忙商业连锁有限公司\"},{\"key\":\"RID\",\"value\":\"feb380fffff34dc1a5c23fc4f91246fa\"},{\"key\":\"TID\",\"value\":\"TID.HOTELBOOK.2.521C82BC11C54C8886F4E622A3AFE0D6\"},{\"key\":\"VID\",\"value\":\"1735292045219.3318b9u88fYk\"}],\"ticket\":\"dY1imGnGsEQNR4GQ60XldGegPaqJo8oFHXK+AgC5b9y9qaZs0lOQbK5O9+0ErU6X9ozINeC/AHVM3Hj91wj6O044pLwxT2s60LjJLA0CJs4\\u003d\",\"transferInfo\":[{\"key\":\"iframe\",\"value\":\"F\"},{\"key\":\"channel\",\"value\":\"wechat\"},{\"key\":\"subChannel\",\"value\":\"WECOM\"}]},\"hotelBookPassengerInputs\":[{\"hotelPassengerInput\":{\"roomIndex\":1,\"uid\":\"**********\",\"approvalPassengerId\":\"4552865117048882935\",\"employee\":\"T\",\"approvalInput\":{\"subApprovalNo\":\"S24112486:ID01Fa2RbwzSWP\"},\"external\":\"F\",\"employeeId\":\"ID01yKBAgmntHV\"},\"passengerBasicInfo\":{\"gender\":\"U\"},\"name\":\"黄紫嫣\",\"isRegistrant\":\"F\",\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"15279444085\",\"transferPhoneNo\":\"15279444085\"}}],\"hotelInvoiceInfos\":[],\"cityInput\":{\"cityId\":21844},\"hotelContactorInfo\":{\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"15279444085\",\"transferPhoneNo\":\"15279444085\"},\"emailInfo\":{\"email\":\"<EMAIL>\",\"transferEmail\":\"<EMAIL>\"}},\"approvalInput\":{\"masterApprovalNo\":\"S24112486:ID01Fa2RbwzSWP\",\"subApprovalNo\":\"S24112486:ID01Fa2RbwzSWP\",\"emergency\":\"F\"},\"miceInput\":{},\"hotelBookInput\":{\"hotelDateRangeInfo\":{\"checkIn\":\"2024-12-30\",\"checkOut\":\"2024-12-31\"},\"roomQuantity\":1},\"membershipInfo\":{\"membershipUid\":\"**********\",\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"15279444085\",\"transferPhoneNo\":\"15279444085\"}},\"bookModeInfo\":{\"bookingType\":\"NONE\"},\"costAllocationInfo\":{\"costAllocationToken\":\"H4sIAAAAAAAAAOMS4HjRwijAIMGixGZobqBnYCAkx8VlZG5gZmhuYWxoKIQhL0VAXomAPAAkqTXBcgAAAA\\u003d\\u003d\",\"shareAmounts\":[{\"costAllocationAmount\":{\"amount\":\"170\"},\"shareAmountKey\":\"**********\"}]},\"resourceTokenInfo\":{\"resourceToken\":\"{\\\"hotelResourceToken\\\":{\\\"masterHotelId\\\":*********,\\\"hotelId\\\":*********,\\\"searchHotelTraceId\\\":\\\"00709183afa84b9e82f7189dc76f77eb\\\",\\\"hid\\\":\\\"ASIGMTc3Ljg0QgFCSgYIABAAGABSdQpvdjFfQ0pLeDZqb1ExcEhyT2hvQ1EwY2lCakUzTnk0NE5Db0dNVGMzTGpnME1nMEtBME5PV1JJRFEwNVpHZ0V4T2lBd01EY3dPVEU0TTJGbVlUZzBZamxsT0RKbU56RTRPV1JqTnpabU56ZGxZZz09EAAYAVoDQ05ZYg0KA0NOWRIDQ05ZGgExYg0KA0NOWRIDQ05ZGgExaANyBjE3MC4wMHoEMC4wMIIBIDAwNzA5MTgzYWZhODRiOWU4MmY3MTg5ZGM3NmY3N2ViigEKMTU5MDk1MTM4MJoBJDczNzk3MTc2LWY3NmUtNGUwMC1hYzQ2LWI2NDgyMzQzNDM5ZKIBAggA\\\",\\\"hotelType\\\":\\\"C\\\",\\\"bonusPoint\\\":\\\"T\\\",\\\"hotelGeoInfoResourceToken\\\":{\\\"cityId\\\":21844,\\\"locationId\\\":0}},\\\"roomResourceToken\\\":{\\\"roomId\\\":**********,\\\"baseRoomId\\\":503125856,\\\"ratePlanTraceLogId\\\":\\\"416e3e02f2c14cbf9285df74ebbdef25_\\\",\\\"allRoomType\\\":\\\"EXIST_C\\\",\\\"bedName\\\":\\\"1张1.8米大床\\\",\\\"windowName\\\":\\\"有窗\\\",\\\"breakfastInfo\\\":\\\"2份早餐\\\",\\\"confirmType\\\":\\\"IMMEDIATE\\\",\\\"roomType\\\":\\\"C\\\",\\\"balanceType\\\":\\\"PP\\\",\\\"tmcPrice\\\":false,\\\"adult\\\":2,\\\"pId\\\":\\\"AQoGMTc3Ljg0EgYxNzcuODQigQEKf3YxX0NOYVI2em9RMVB2UDlnVWFCekV5TmpJNU1UZ2lBa2hhS2dKRFJ6SUdNVGMzTGpnME9nWXhOemN1T0RSQ0RRb0RRMDVaRWdORFRsa2FBVEZLSURBd056QTVNVGd6WVdaaE9EUmlPV1U0TW1ZM01UZzVaR00zTm1ZM04yVmkqA0NOWTINCgNDTlkSA0NOWRoBMTINCgNDTlkSA0NOWRoBMToCCABCBwjoDxAMGB5KBwjoDxAMGB9QAVgAYgJIWmgNeAKAAdaR6zo\\\\u003d\\\",\\\"amadeus\\\":false,\\\"supportAccommodation\\\":\\\"F\\\",\\\"roomOriginCurrency\\\":\\\"CNY\\\",\\\"roomScenarioEnum\\\":\\\"ROOM_LIST\\\",\\\"ratePlanRequestToken\\\":\\\"ASi1L/0AUK0VAIaupT8AbcgA/L8vu6//p30MTemO7pKt/2kfH2NEo0bypDBl2Ab4bGphW8ClwaaWcKQSwGf0FfRtPjsA1ggy/a6R+R+WAJQAkwDWZ3syJi8F6zCJcX3UJcawSkaKHd16h9d719+M82Z1c6kmtyZTlZ5OM6dXB3U9kTln0BGL+YtJ7tbYulpr5hcJ67TH5IfMyAoQegEp0AMDhE88NDwG/hdWUXu8Be2fwr+Ev7+mOpMlqSjJVU2VtAlHjVMlMhhMf5QfAAiiJoFFEg9Ci3kJUFGUVaFQk4lSSQ0ejOBggSKiDkw7fMb2a54bISbDtDwYhvpVyGlI3cC3Q3TFsgZWOGDEnqgT90JlLC7SD0jOcHehuuu7q9Cj5NkUaZkUcVRO5DZetLVv7UI7ZeM0b8sprhcZsTJhBAM3i1yhHB5236fHviJnvG5eED1fKyl3OEIgmLLYMD7tTRFRXNjDtkNFa+UWbVAEN6AL0TVeo+VEQ/skVxsL9c29mrOmz5mmPwYgnWKtSUJREquaKvY0uZPNugFI9lKCH200nbQNVSbgNnVt11OKc5dfm3JQO5Spu4pvQlfNBnvcVUN6LqsHljIIe971IrRUtJjY2iL3WjVseOzo3bEr89lezBEir3SrQ/RUZjodBIWqfVkodHXN9MaOJx2RjPB10MEUVL4xzmJjtRT5tNqIrIlhGzTn8SL4i+DvgX8H/hv4X+Bfgf8E/hL4zWsO8wj8gmO4hVf4xBlwBJyCT1QfYkGGiceDeAppwH/onFly6ch6WQJIf2nBjzSvGy8sptRKVuXEAZD+UoGzHE3WywRAtpcI/Cgcx5LdRC4MR40rWS8jAOkvHfiRi0Fr9c3Zm15/aM21NaUba+wDkOwlBT+yNdyZ83EDCQDpxJAVChA1ELIB2Anzu5vOJP8pZi97hEMhAw\\\\u003d\\\\u003d\\\",\\\"ratePlanRoomToken\\\":\\\"ASi1L/0AUHkAAAoBQxAAGNT7z/YFIgJQUA\\\\u003d\\\\u003d\\\",\\\"roomUniqueKey\\\":\\\"V1-ASi1L/0AULkAAAjU+8/2BRIBQxoETk9ORSICUFA4xopN\\\",\\\"adultNumber\\\":2,\\\"windowType\\\":2},\\\"reservationResourceToken\\\":{\\\"wsId\\\":\\\"2038deca-e281-484d-b602-1a07eb03f47b\\\",\\\"policyToken\\\":\\\"v1KLUv/QBQtQkAVtM+JSCpWADnxEQkJuFdEf9Ky4eK11d3eGmViQ6xoBs49FoYBBAECgIzADUANQDP3WdPqD27g2Bq3B1GVaXv7E3jJFqDYNrh7jBe98p2qkzGxTNSZwwDwLTGvV+s0sXUGQL8Ln5UWOJqL4G2JC+4d6SyhNEvUmpPDa98M579AHVjHITjMPQsEDYvh/c8C/Q0tDiNSbTnBJdSCtsReBdSxoVkCq1VxWdS3ulurdiovCipZOM8oCg1sFbfyFnfat0lz7LD0oLZEoQJpYsZDQoPOHvi7O3siCe4y6ZwfcwIRvZdSGt1TcXVdkYWrWTkARspyJ0qLF3RoegqtYTw7BkVADobZ2OHs7XDfXPxzF2IKoSpUSiGC6kGmHqoQ2mFNCfxqWcLVIlXCUcYtVQBnBtAyuvMHFY2mw\\\\u003d\\\\u003d\\\"},\\\"bookInitResourceToken\\\":{\\\"aggBookPriceResourceToken\\\":{\\\"currency\\\":\\\"CNY\\\",\\\"accountPaymentAmount\\\":170.00,\\\"individualPaymentAmount\\\":7.84,\\\"mixPayAccountExtraAmount\\\":0.00},\\\"roomPriceResourceToken\\\":{},\\\"cancelTip\\\":\\\"2024年12月30日20:00前可免费取消。若未入住或过时取消将收取您首日房费¥177.84（若用优惠券则以券后支付价为准），修改结果请以酒店回复结果为准。携程会根据您的付款方式扣除房费，订单需等酒店或供应商确认后生效，如订单不确认将全额退款至您的付款账户，订单确认结果以携程短信或邮件通知为准。\\\"}}\"},\"corpPayInfo\":{\"corpPayType\":\"public\"},\"userActionInfo\":{\"userActionToken\":\"PRD-2-edd2abab-246d-464f-8f3b-740df4d05f0a-********-4.1.15-WEB\"},\"rcInfos\":[],\"bookUrlInfos\":[{\"urlType\":\"SUCCESS\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayState/PayState?site\\u003d2\\u0026LimitStandard\\u003dfalse\\u0026needFinishResult\\u003dtrue\\u0026\"},{\"urlType\":\"S_BACK\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayState/PayState?site\\u003d2\\u0026LimitStandard\\u003dfalse\\u0026needFinishResult\\u003dtrue\\u0026\"},{\"urlType\":\"E_BACK\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayMent/PayMent\"},{\"urlType\":\"FROM_URL\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayMent/PayMent?leomacmd://navi?%7b%22backward%22%3atrue%2c%22navi%22%3a0%2c%22now%22%3atrue%2c%22platform%22%3a%7b%7d%7d\"},{\"urlType\":\"S_BACK_MICE\",\"urlValue\":\"https://ct.ctrip.com/mice-activities/welfareTourism\"},{\"urlType\":\"FROM_URL_ORDER_DETAIL\",\"urlValue\":\"https://ct.ctrip.com/webapp/hotel/process/detail?orderId\\u003d{0}\\u0026language\\u003d{1}\\u0026from\\u003dfinish\"},{\"urlType\":\"OFFLINE_RETURN_URL\",\"urlValue\":\"http://corpint.ct.ctripcorp.com/hotel-online/offlineOrder/success\"},{\"urlType\":\"ORDER_DETAIL_URL\",\"urlValue\":\"https://ct.ctrip.com/m/Detail/Hotel/{0}\"}],\"paymentInfoInput\":{\"canWechatPayByOthers\":\"F\",\"clientType\":\"H5\"},\"clientInfo\":{\"clientType\":\"H5\",\"rmsToken\":\"fp\\u003d\\u0026vid\\u003d1735292045219.3318b9u88fYk\\u0026pageId\\u003d10650103810\\u0026r\\u003dd67f1730df7a4df4bda5db7590895622\\u0026ip\\u003d2409:8938:b2a4:2837:f447:1b50:6eae:fd8c\\u0026rg\\u003dfin\\u0026screen\\u003d393x852\\u0026tz\\u003d+8\\u0026blang\\u003dzh-CN\\u0026oslang\\u003dzh-CN\\u0026ua\\u003dMozilla%2F5.0%20(iPhone%3B%20CPU%20iPhone%20OS%2018_1_1%20like%20Mac%20OS%20X)%20AppleWebKit%2F605.1.15%20(KHTML%2C%20like%20Gecko)%20%20Mobile%2F15E148%20wxwork%2F4.1.32%20MicroMessenger%2F7.0.1%20Language%2Fzh%20ColorScheme%2FLight%20wwmver%2F3.26.15.613\\u0026v\\u003dm17\\u0026bl\\u003dfalse\\u0026clientid\\u003d\",\"secondaryChannel\":\"IOS-new\"},\"offlineInfo\":{\"forciblyAssure\":\"F\",\"especialAuth\":\"F\",\"autoConfirm\":\"F\",\"exigentOrder\":\"F\"},\"roomInfoInput\":{},\"sendInfo\":{\"sendSMS\":\"F\",\"sendEmail\":\"F\"},\"ssoInput\":{\"ssoKey\":\"47b68e9101484a7fa93e90be189edde3\"},\"flashStayInput\":{\"flashPlatFrom\":\"H5\"},\"hotelPayTypeInput\":[{\"payCode\":\"ROOM\",\"payType\":\"MIX_PAY\"}],\"strategyInfos\":[{\"strategyKey\":\"BOOKING_SCENARIO\",\"strategyValue\":\"HOTEL_LIST\"},{\"strategyKey\":\"BOOKING_WITH_PERSONAL_ACCOUNT\",\"strategyValue\":\"F\"}],\"costCenterInfo\":{\"costCenterJsonString\":\"{\\\"items\\\":{\\\"**********\\\":{\\\"name\\\":\\\"黄紫嫣\\\",\\\"cost1\\\":\\\"宜春赵一鸣食品科技有限公司(7000-0001)\\\",\\\"cost2\\\":\\\"南昌仓5区\\\"},\\\"fdefault\\\":{}}}\"},\"useNewOrderCreate\":\"F\"}", OrderCreateRequestType.class)
        orderCreateRequestType.cityInput.cityId = 22249
        resultOld = JsonUtil.fromJson("{\"saveList\":[{\"bizType\":4,\"orderID\":****************,\"uid\":\"**********\",\"pid\":\"**********\",\"corpId\":\"LSHM\",\"eid\":\"**********\",\"jounaryNo\":\"S24112486:ID01Fa2RbwzSWP\",\"jounaryNoTitle\":\"酒店关联行程号\",\"jounaryNoTitleEn\":\"Associated itinerary No. of hotel\",\"orderCostCenters\":[],\"passengers\":[{\"costCenters\":[{\"costCenterKey\":2,\"costCenterValue\":\"南昌仓5区\",\"costCenterTitle\":\"成本中心2\",\"costCenterTitleEn\":\"Cost center No.2\"},{\"costCenterKey\":1,\"costCenterValue\":\"宜春赵一鸣食品科技有限公司(7000-0001)\",\"costCenterTitle\":\"成本中心1\",\"costCenterTitleEn\":\"Cost center No.1\"}],\"passengerId\":\"**********\",\"passengerName\":\"黄紫嫣\",\"isEmployee\":1}],\"frequentlyUsedCostCenter\":1,\"createTimeStr\":\"2024-12-30 11:31:01\"}]}", SaveOrderCostCenterRequestType.class)
        matchCostCenterResponseType = JsonUtil.fromJson("{\"costCenterDefaultTitles\":[{\"costCenterDefaultType\":\"TP\",\"costCenterDefaultTitle\":\"出行目的\",\"costCenterDefaultTitleEn\":\"JounaryReason\"},{\"costCenterDefaultType\":\"PN\",\"costCenterDefaultTitle\":\"项目号\",\"costCenterDefaultTitleEn\":\"Project\"},{\"costCenterDefaultType\":\"FT\",\"costCenterDefaultTitle\":\"机票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of flight\"},{\"costCenterDefaultType\":\"HT\",\"costCenterDefaultTitle\":\"酒店关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of hotel\"},{\"costCenterDefaultType\":\"TT\",\"costCenterDefaultTitle\":\"火车票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of train\"},{\"costCenterDefaultType\":\"CT\",\"costCenterDefaultTitle\":\"用车关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of car\"},{\"costCenterDefaultType\":\"BT\",\"costCenterDefaultTitle\":\"汽车票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No.\"},{\"costCenterDefaultType\":\"D1\",\"costCenterDefaultTitle\":\"自定义字段1\",\"costCenterDefaultTitleEn\":\"Self-defined title No.1\"},{\"costCenterDefaultType\":\"D2\",\"costCenterDefaultTitle\":\"自定义字段2\",\"costCenterDefaultTitleEn\":\"Self-defined title No.2\"},{\"costCenterDefaultType\":\"CC1\",\"costCenterDefaultTitle\":\"成本中心1\",\"costCenterDefaultTitleEn\":\"Cost center No.1\"},{\"costCenterDefaultType\":\"CC2\",\"costCenterDefaultTitle\":\"成本中心2\",\"costCenterDefaultTitleEn\":\"Cost center No.2\"},{\"costCenterDefaultType\":\"CC3\",\"costCenterDefaultTitle\":\"成本中心3\",\"costCenterDefaultTitleEn\":\"Cost center No.3\"},{\"costCenterDefaultType\":\"CC4\",\"costCenterDefaultTitle\":\"成本中心4\",\"costCenterDefaultTitleEn\":\"Cost center No.4\"},{\"costCenterDefaultType\":\"CC5\",\"costCenterDefaultTitle\":\"成本中心5\",\"costCenterDefaultTitleEn\":\"Cost center No.5\"},{\"costCenterDefaultType\":\"CC6\",\"costCenterDefaultTitle\":\"成本中心6\",\"costCenterDefaultTitleEn\":\"Cost center No.6\"}],\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2024-12-30 11:31:01.271+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", MatchCostCenterResponseType.class)
        costCenterInfoType = Optional.ofNullable(matchCostCenterResponseType.getResult()).orElse(new CostCenterInfoType())
        createOrderResponseType = JsonUtil.fromJson("{\"wsId\":\"2038deca-e281-484d-b602-1a07eb03f47b\",\"orderId\":****************,\"orderPaymentInfo\":{\"payType\":\"MIX_PAY\",\"orderAmountInfo\":{\"cnyTotalAmount\":177.84,\"personalPayAmountInfo\":{\"amount\":7.84,\"currency\":\"CNY\",\"exchangeRateToCNY\":1},\"paymentItemList\":[{\"prepayType\":\"ACCNT\",\"gatheringType\":\"D\",\"feeType\":\"HotelRoomFee\",\"amountInfo\":{\"amount\":170.00,\"currency\":\"CNY\",\"exchangeRateToCNY\":1}},{\"prepayType\":\"SELF_PAY\",\"gatheringType\":\"D\",\"feeType\":\"HotelRoomFee\",\"amountInfo\":{\"amount\":7.84,\"currency\":\"CNY\",\"exchangeRateToCNY\":1}}]}},\"success\":true,\"supportPaymentBill\":true,\"paymentTransactionId\":\"307935425113915432\",\"orderTotalAmount\":{\"orderAmount\":{\"originAmount\":{\"amount\":177.84,\"currency\":\"CNY\",\"exchange\":1},\"cNYAmount\":177.84},\"paymentBillInfo\":{\"payAmount\":177.84,\"payCNYAmount\":177.84,\"payCurrency\":\"CNY\",\"payExchange\":1}},\"transactionInfo\":{\"transactionId\":\"bab61955-9222-43ff-a4e1-d8b2cf5424a6\",\"subTransactionIdList\":[{\"payType\":\"PERSONAL\",\"subTransactionId\":\"ae8154f6-aa4c-464e-b158-a56dd0d94727\"},{\"payType\":\"ACCNT\",\"subTransactionId\":\"c10e7451-b1de-48a7-bdfe-930adfac3280\"}]},\"responseCode\":20000,\"responseStatus\":{\"timestamp\":\"2024-12-30 11:31:00.960+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", CreateOrderResponseType.class)
        resultNew = mapper.buildSaveOrderCostCenterRequest(orderCreateRequestType, accountInfo, checkAvailInfo, orderCreateToken, createOrderResponseType, costCenterInfoType, approvalTextInfoResponseType, ssoInfoQueryResponseType, resourceToken, null, null)
        // 抹平变量
        resultOld.saveList.get(0).createTimeStr = "2024-12-30 10:44:45"
        resultNew.saveList.get(0).createTimeStr = "2024-12-30 10:44:45"
        then:
        resultNew != null
        // 对比保存的内容需要完全一致
        resultNew == resultOld

        when: // 619c97a642b44d869a52cb6b3414a034  2024-12-30T11:31:35
        queryCheckAvailContextResponseType = JsonUtil.fromJson("{\"baseInfo\":{\"userInfo\":{\"uid\":\"**********\",\"policyUid\":\"\",\"corpId\":\"bluemoon\",\"eid\":\"\",\"usersCityId\":1342,\"userCountryCode\":\"\"},\"customCurrency\":\"CNY\",\"website\":\"APP\",\"feeType\":\"C\",\"quantity\":1,\"startTime\":\"2024-12-30T00:00:00+08\",\"endTime\":\"2024-12-31T00:00:00+08\",\"language\":\"CN\",\"locale\":\"zh-CN\",\"startTimeUTC\":\"2024-12-29T16:00:00Z\",\"endTimeUTC\":\"2024-12-30T16:00:00Z\",\"pOS\":\"zh-CN\",\"pOSType\":{\"pOS\":\"zh-CN\",\"countryId\":1},\"platform\":\"android\",\"bookingWithPersonalAccount\":false},\"hotelInfo\":{\"subHotelId\":********,\"masterHotelId\":********,\"star\":2,\"rStar\":0,\"starLicence\":false,\"customerEval\":2.5,\"hotelGroupId\":565,\"hotelBrandId\":1640,\"balancePeriod\":\"GW\",\"oversea\":false,\"tripBusinessHotel\":false,\"cuHotel\":false,\"hasServiceFee\":false,\"localTimeOffsetHours\":0,\"telephone\":\"+86-876-2626313\",\"geographicalInfo\":{\"locationInfo\":{\"id\":0},\"cityInfo\":{\"id\":1342,\"name\":{\"textGB\":\"文山市\",\"textEn\":\"Wenshan\"}},\"provinceInfo\":{\"id\":25},\"countryInfo\":{\"id\":1},\"coordinateInfo\":{}},\"address\":{\"textGB\":\"金泰路102号泰康小区11组团\",\"textEn\":\"Group11, Taikang Community, 102 Jintai Road\"},\"hotelName\":{\"textGB\":\"IU酒店(文山三七国际交易市场店)\",\"textEn\":\"IU Hotel(Wenshan Sanqi lnternational Trade Market)\"},\"currentCountry\":true,\"timeZoneId\":\"Asia/Shanghai\"},\"roomInfo\":{\"subRoomId\":**********,\"roomName\":{\"textGB\":\"小U·超级大床房【酒店公区提供自助洗衣机+微波炉+安静舒适】\",\"textEn\":\"Xiao U Super Double Bed Room (City View)\"},\"basicRoomName\":{\"textGB\":\"小U·超级大床房【酒店公区提供自助洗衣机+微波炉+安静舒适】\",\"textEn\":\"Xiao U Super Double Bed Room (City View)\"},\"masterBasicRoomId\":301156486,\"masterBasicRoomName\":{\"textGB\":\"小U·超级大床房【酒店公区提供自助洗衣机+微波炉+安静舒适】\"},\"roomType\":\"C\",\"tmcPriceType\":\"NONE\",\"balanceType\":\"PP\",\"gdsType\":\"WH\",\"mealType\":4,\"priceSuitPropertyValueId\":1262925,\"ratePlanKey\":{\"checkAvlID\":0,\"ratePlanID\":\"0\"},\"addPrice\":false,\"invoiceType\":\"Special\",\"invoiceLimitType\":\"NotSupported\",\"applyAreaPropertyId\":0,\"maxGuestCountPerRoom\":2,\"maskRoom\":false,\"hourlyRoom\":false,\"corpPrice\":true,\"manualGds\":false,\"joined30MinutesFreeCancel\":false,\"premiumRoom\":false,\"welfareRoom\":false,\"groupMemberShip\":false,\"earnHotelPoints\":false,\"bonusPointInfo\":{\"bonusPointRoom\":true,\"bonusPointCode\":\"WHZSF\",\"pointsMode\":\"XXMS\"},\"earlyArrivalTime\":\"2024-12-30T12:00:00+08\",\"lastCancelTime\":\"2024-12-30T18:00:00+08\",\"cnyAmount\":139.00,\"originAmountInfo\":{\"amount\":139.00,\"currency\":\"CNY\",\"exchange\":1},\"customAmountInfo\":{\"amount\":139.00,\"currency\":\"CNY\",\"exchange\":1},\"originCostInfo\":{\"amount\":139,\"currency\":\"CNY\",\"exchange\":1},\"originCostBeforeTaxInfo\":{\"amount\":139,\"currency\":\"CNY\",\"exchange\":1},\"roomDiscountInfo\":{\"promotionSwitchOn\":false,\"roomDailyDiscountInfo\":[],\"originPromotionAmount\":{\"price\":0,\"currency\":\"CNY\"},\"customPromotionAmount\":{\"price\":0,\"currency\":\"CNY\"}},\"roomDailyInfo\":[{\"effectDate\":\"2024-12-30T00:00:00+08\",\"cnyAmount\":139.00,\"amount\":139.00,\"customAmount\":139.00,\"afterPromotionCnyAmount\":139.00,\"afterPromotionCustomAmount\":139.00,\"cost\":139,\"costBeforeTax\":139,\"customizedCnyAmount\":0,\"mealNumber\":2,\"holdDeadline\":0,\"afterPromotionCustomAmountExcludeTax\":139.00,\"afterPromotionCnyAmountExcludeTax\":139.00,\"customAmountExcludeTax\":139.00,\"cnyAmountExcludeTax\":139.00}],\"paymentRulesInfo\":{\"paymentGuaranteePoly\":\"TRIP\",\"canTravelMoneyPay\":false,\"forceVccPay\":false,\"canCreditCardPay\":false},\"longRentalInfo\":{\"longRental\":false,\"needInquire\":false},\"holdRoomInfo\":{\"holdRoom\":false},\"extraPayTaxMultiCurrencyAmountInfo\":{\"customExtraPayTaxTotalAmount\":0,\"cnyExtraPayTaxTotalAmount\":0},\"pricedRoom\":false,\"generalizedPremiumRoom\":false,\"customAmountExcludeTaxInfo\":{\"amount\":139.00,\"currency\":\"CNY\",\"exchange\":1},\"supportAnticipation\":false,\"roomRightInfoList\":[],\"invoiceTypeExcludeAddPrice\":\"Special\",\"roomMealInfo\":{\"mealType\":4,\"mealDescList\":[\"2份早餐\"],\"dailyMealInfoList\":[{\"effectDateUTC\":\"2024-12-29T16:00:00Z\",\"mealDescList\":[\"\"]},{\"effectDateUTC\":\"2024-12-30T16:00:00Z\",\"mealDescList\":[\"2份早餐\"]}]},\"saleRoomTags\":[{\"tagCode\":\"XYJ\",\"tagDesc\":\"\",\"configInfoList\":[{\"position\":301,\"priority\":2,\"style\":\"1\"}]}],\"taxDetails\":[],\"pid\":\"AQoGMTM5LjAwEgYxMzkuMDAieQp3djFfQ0xPenpTRVE0cU9JL3dVYUJ6RXlOakk1TWpVaUFsZElLZ0pEUnpJRE1UTTVPZ014TXpsQ0RRb0RRMDVaRWdORFRsa2FBVEZLSURBNE16RmtNalZoWTJFM01EUXdPREJpWmpWbE1UUmhOVEUzTVRZd1lUQTQqA0NOWTINCgNDTlkSA0NOWRoBMTINCgNDTlkSA0NOWRoBMToCCABCBwjoDxAMGB5KBwjoDxAMGB9QAVgAYgJXSGgSeAKAAbOzzSE\\u003d\",\"onlyGroupMemberCanBook\":false},\"bookingRules\":{\"cancelPenalties\":{\"paymentGuaranteePoly\":\"TRIP\",\"cancelDeductDetail\":{\"originDeductionPrice\":{\"price\":139.00,\"currency\":\"CNY\"},\"customDeductionPrice\":{\"price\":139.00,\"currency\":\"CNY\"}},\"guaranteePolicyInfo\":{}},\"authorizationRestriction\":{\"latestAuthorizeTime\":\"2024-12-30T17:00:00+08\"},\"confirmRules\":{\"justifyConfirm\":true,\"forbidNotImConfirm\":false},\"welfareRoomRules\":{},\"limitDepartureDateTime\":{\"departureEnd\":\"2024-12-31T12:00:00+08\",\"departureEndUTC\":\"2024-12-31T04:00:00Z\"},\"certificateInfo\":{\"needCertificate\":false},\"minLOS\":1,\"cancelPolicyInfo\":{\"cancelType\":\"LIMIT\",\"freeCancelPolicySceneType\":\"NOT_SUPPORTED\",\"lastCancelTimeUTC\":\"2024-12-30T10:00:00Z\",\"guaranteePolicyInfo\":{\"guaranteeType\":\"FIRST_DAY_PREPAY\",\"paymentGuaranteePoly\":\"TRIP\",\"guaranteeReason\":\"NONE\",\"guaranteePriceInfo\":{\"originGuaranteePrice\":{\"price\":139.00,\"currency\":\"CNY\"},\"customGuaranteePrice\":{\"price\":139.00,\"currency\":\"CNY\"}}}},\"limitArrivalDateTime\":{\"arrivalFromUTC\":\"2024-12-30T04:00:00Z\",\"arrivalEndUTC\":\"2024-12-30T22:00:00Z\",\"defaultArrivalTimeEnd\":false},\"rPLimitInfo\":{\"minLos\":1,\"maxLos\":0,\"minAdvancedBookingDays\":0,\"maxAdvancedBookingDays\":0},\"billingGuestInfo\":{\"guestsNameLanguages\":[\"zh\",\"en\"]},\"restrictRuleList\":[]},\"bookExtInfo\":{\"featureControlFlagMap\":{\"EnableMemberPoints\":false,\"EnableXMinutesFreeCancel\":false,\"BrandNewCustomer\":true,\"EnableShadowDiscount\":false,\"EnableYXH\":false,\"EnablePrepayDiscount\":false,\"EnableNewGroupMember\":false,\"ForbidMultiNationality\":false,\"NewUserCountryCode\":true,\"EnableCNYTWODecimal\":true,\"ShieldMaskRoom\":true,\"EnableAddPrice\":false,\"IgnoreSpecialCancelPolicyNotMatchingFilter\":false,\"StrictCancellationPolicy\":false,\"IgnoreRemitTax\":false,\"ShieldMultiplePrice\":false,\"Enable30MinuteFreeCancel\":false,\"EnablePromotionSubTag\":true,\"EnableHotelNewCustomer\":false,\"DisableIPBlock\":false,\"EnableDirectRate\":false,\"EnableOutputPackageRoom\":false,\"ShieldMaskNakedSale\":false,\"EnableMobileRate\":true,\"EnableMatchPolicyWithExtraPayTax\":false}},\"userRightsInfo\":{\"userChosenCouponInfo\":[],\"hotelNewCustomer\":true,\"userSelectedCountryCode\":\"\"},\"reservationToken\":\"AQgBEgoyMDI0LTEyLTMwGhcKAUMSAlBQGgRUUklQIgJXSEjio4j/BSICCAAqBwoFemgtQ04\\u003d\",\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2024-12-30 11:31:33.805+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", QueryCheckAvailContextResponseType.class)
        checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsId")))
                        .build().getCheckAvailContextInfo()
        orderCreateRequestType = JsonUtil.fromJson("{\"integrationSoaRequestType\":{\"userInfo\":{\"userId\":\"**********\",\"corpId\":\"bluemoon\",\"groupId\":\"Gr_00000712\",\"pos\":\"CHINA\"},\"token\":\"024FC8C86307A088CC83F30B4934F4C10DD93974C18E6CE97384F550AC76D40B\",\"language\":\"zh-CN\",\"requestId\":\"619c97a642b44d869a52cb6b3414a034\",\"sourceFrom\":\"H5\",\"transactionID\":\"TID.SEARCHHOTEL.30.D17F527CE10543B4ADA613A78EC401A7\",\"gatewayHost\":\"ct.ctrip.com\",\"timezoneOffsetMinutesNew\":\"480\",\"logIndices\":[{\"key\":\"groupId\",\"value\":\"Gr_00000712\"},{\"key\":\"timezoneOffsetMinutesNew\",\"value\":\"480\"},{\"key\":\"gatewayHost\",\"value\":\"ct.ctrip.com\"},{\"key\":\"pvid\",\"value\":\"9\"},{\"key\":\"subChannel\",\"value\":\"\"},{\"key\":\"CID\",\"value\":\"09031173313514243177\"},{\"key\":\"EID\"},{\"key\":\"CIP\",\"value\":\"2409:896d:421b:812a:75bd:cf6:69fc:2f4f\"},{\"key\":\"miceToken\",\"value\":\"\"},{\"key\":\"groupName\",\"value\":\"广州蓝月亮实业有限公司\"},{\"key\":\"gatewayIdc\",\"value\":\"SHAXY\"},{\"key\":\"traceID\",\"value\":\"\"},{\"key\":\"A\",\"value\":\"ordercheck\"},{\"key\":\"CorpID\",\"value\":\"bluemoon\"},{\"key\":\"C\",\"value\":\"corpbookinghotelbookservice\"},{\"key\":\"M\",\"value\":\"**********\"},{\"key\":\"sid\",\"value\":\"30\"},{\"key\":\"S\",\"value\":\"238f61f6c297493091a384bdb60aedf8961127f6804d\"},{\"key\":\"T\",\"value\":\"024FC8C86307A088CC83F30B4934F4C10DD93974C18E6CE97384F550AC76D40B\"},{\"key\":\"pos\",\"value\":\"CHINA\"},{\"key\":\"GE\",\"value\":\"1735529492826\"},{\"key\":\"sourceFrom\",\"value\":\"H5\"},{\"key\":\"corpName\",\"value\":\"广州蓝月亮实业有限公司\"},{\"key\":\"RID\",\"value\":\"619c97a642b44d869a52cb6b3414a034\"},{\"key\":\"TID\",\"value\":\"TID.SEARCHHOTEL.30.D17F527CE10543B4ADA613A78EC401A7\"},{\"key\":\"VID\",\"value\":\"1712905284030.512d6s1mLc8x\"}],\"ticket\":\"eBEpyLSQa1ei0gCdYRG3jlBvWAV+Keq6PE3QtgIwcLNydlrhzBLH1BzFQGWWm51Vo5uqyw6ccebmPAJ2qca7pE44pLwxT2s60LjJLA0CJs4\\u003d\",\"transferInfo\":[{\"key\":\"iframe\",\"value\":\"F\"},{\"key\":\"channel\",\"value\":\"sso\"}]},\"hotelBookPassengerInputs\":[{\"hotelPassengerInput\":{\"uid\":\"**********\",\"employee\":\"T\",\"external\":\"F\",\"employeeId\":\"81009730\"},\"passengerBasicInfo\":{\"gender\":\"U\"},\"name\":\"刘观观\",\"isRegistrant\":\"F\",\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"18819293135\",\"transferPhoneNo\":\"18819293135\"}}],\"hotelInvoiceInfos\":[],\"cityInput\":{\"cityId\":1342},\"hotelContactorInfo\":{\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"18819293135\",\"transferPhoneNo\":\"18819293135\"}},\"approvalInput\":{\"masterApprovalNo\":\"*********:ID01FxJM0ALJ1B\",\"subApprovalNo\":\"*********:ID01FxJM0ALJ1B\",\"emergency\":\"F\"},\"miceInput\":{},\"hotelBookInput\":{\"hotelDateRangeInfo\":{\"checkIn\":\"2024-12-30\",\"checkOut\":\"2024-12-31\"},\"roomQuantity\":1},\"membershipInfo\":{},\"bookModeInfo\":{\"bookingType\":\"NONE\"},\"resourceTokenInfo\":{\"resourceToken\":\"{\\\"hotelResourceToken\\\":{\\\"masterHotelId\\\":********,\\\"hotelId\\\":********,\\\"searchHotelTraceId\\\":\\\"0831d25aca704080bf5e14a517160a08\\\",\\\"hid\\\":\\\"ASIGMTE0LjAwQgBKBggAEAAYAFJtCmd2MV9DTEN6elNFUXM3UE5JUm9DUTBjaUF6RXhOQ29ETVRFME1nMEtBME5PV1JJRFEwNVpHZ0V4T2lBd09ETXhaREkxWVdOaE56QTBNRGd3WW1ZMVpURTBZVFV4TnpFMk1HRXdPQT09EAAYAVoDQ05ZYg0KA0NOWRIDQ05ZGgExYg0KA0NOWRIDQ05ZGgExaIgBcgYxNzAuMDB6BDAuMDCCASAwODMxZDI1YWNhNzA0MDgwYmY1ZTE0YTUxNzE2MGEwOIoBCjE2MDg2NTEyMzWaASRjZjIzYjNmNy03YmQ4LTRkZWYtYTIxMy04NDc1NTA1ODAyZWWiAQIIAA\\\\u003d\\\\u003d\\\",\\\"hotelType\\\":\\\"C\\\",\\\"bonusPoint\\\":\\\"T\\\",\\\"hotelGeoInfoResourceToken\\\":{\\\"cityId\\\":1342,\\\"locationId\\\":0}},\\\"roomResourceToken\\\":{\\\"roomId\\\":**********,\\\"baseRoomId\\\":301156486,\\\"ratePlanTraceLogId\\\":\\\"d599544b01704c4eb29141fcca7528ad_\\\",\\\"allRoomType\\\":\\\"EXIST_C\\\",\\\"bedName\\\":\\\"1张1.8米大床\\\",\\\"windowName\\\":\\\"有窗\\\",\\\"breakfastInfo\\\":\\\"2份早餐\\\",\\\"confirmType\\\":\\\"IMMEDIATE\\\",\\\"roomType\\\":\\\"C\\\",\\\"balanceType\\\":\\\"PP\\\",\\\"tmcPrice\\\":false,\\\"adult\\\":2,\\\"pId\\\":\\\"AQoGMTM5LjAwEgYxMzkuMDAieQp3djFfQ0xPenpTRVE0cU9JL3dVYUJ6RXlOakk1TWpVaUFsZElLZ0pEUnpJRE1UTTVPZ014TXpsQ0RRb0RRMDVaRWdORFRsa2FBVEZLSURBNE16RmtNalZoWTJFM01EUXdPREJpWmpWbE1UUmhOVEUzTVRZd1lUQTQqA0NOWTINCgNDTlkSA0NOWRoBMTINCgNDTlkSA0NOWRoBMToCCABCBwjoDxAMGB5KBwjoDxAMGB9QAVgAYgJXSGgSeAKAAbOzzSE\\\\u003d\\\",\\\"amadeus\\\":false,\\\"supportAccommodation\\\":\\\"F\\\",\\\"roomOriginCurrency\\\":\\\"CNY\\\",\\\"roomScenarioEnum\\\":\\\"ROOM_LIST\\\",\\\"ratePlanRequestToken\\\":\\\"ASi1L/0AUFUVAPatoz0ADVYAvI5PdK8f7PRPRAGgClWggnEbhCPGSQogPEswTkgaJEkCBAGCj49pwLsETQLqt0PBtW0Pjnidht0HlACSAJQAol06rWIO/7AuFB6xXJyOdFo5Yq1dWota87mgaIcSVumwQLrmfCkTx8kSXHrB9ANbYMRvLZmOztPhOK/oFbw/DAXoatpUuqVXQmI0E2oxQ0MjTzA4/Ib8GTziZjyG/W/hn8K3HtZKWeSxaQ/Jlm1YiCQ1w8QAkCDux+gVIMLb+6lduaGJbbJ1XMd1FG3DIDwY4aFDgTvMyvCT0SqbRCytYr3fPtltagMxnRzvh1vYcKTFX/uGJCWk4CAlqGSzVBbRuKLMTTMVJtL6EZGlx5HBWbiCqOSjoOtH4dpo3S6JqTnZibdYvDKxWEMwZCtamzRjsfHemJNc+r28g0FDiljR8YaoTK1BUSGYs3bhxYK6aNG39hCnIyMWt0UhlneC4U4TE9S7Lk8zLdziItAG7ptT7ykEDa2pkeyhaY/Ilm3q5uQb1dMIGt5jC/5CVE3C0ZOy7nd+4ydCgVoS0BOHERRIkx8xdvWVqYLihCiqJIJ3X5WdlI7w2UNwq7xa5FvxdTrWZdxGof7plJeWF2y9Ugp3vjTLSap0WCUKjGgRc6GLu5oV3oqVRFwu19GZOppUSjE5xDPzpvvzQkcsNCypp9gXJOBL5C+RP0T+D/k75M+Q/0L+Cvkp5FaG8yhPyJ1M5kumfJYdZAW5QJ7YngQGYoLxIjQ/Tc4ZVdBb0NCPNfiLDVrGjEqa21RTLhJo6McObMzxVEFvQON7zMBfaMbRVD+RazTj5qYKegYa+rEEf9GyfcMtrgppONV3qwKZrQmkStDwHmPwF6q3PZFSAQgAVihA1EDIBmAnzO9uOhP8p5hediiHQgY\\\\u003d\\\",\\\"ratePlanRoomToken\\\":\\\"ASi1L/0AUHkAAAoBQxAAGOKjiP8FIgJQUA\\\\u003d\\\\u003d\\\",\\\"roomUniqueKey\\\":\\\"V1-ASi1L/0AULkAAAjio4j/BRIBQxoETk9ORSICUFA4zYpN\\\",\\\"adultNumber\\\":2,\\\"windowType\\\":2},\\\"reservationResourceToken\\\":{\\\"wsId\\\":\\\"9501a484-6fb1-4fa5-ba06-f741a8aefa06\\\",\\\"policyToken\\\":\\\"v1KLUv/QBQlQkA1lI9JDCJ6gHEdIj0bNI+XKxwKwWvMHDnjLx7dqGYZ5mhSskHCwNjHDIANAAzAAs/V1n/p5rGadu+qJLvVlmneRJP4oGYZts+SNtKno/LG5UdUqswBsAepm07KKXK1DIELGcNaCuyQtuOSJY02sGJ7Ec/Gp3DVdbxgRXPYQmmeBYExPDCmINYGuhwWJieEEw70Lb1DjFCRuTJy6riVWKk3nZacOByML40oRtQ/wXTahpZpamcbSVusmM+gbfkYOGf4ewqQy6WAg0JCG1jT1hCPHrRdPS9rKovak+PrK8iZIEJqYk+nN3FUoWORUup48TeTp9azI48yggWADobh2OHw7XDfnPxzF2IKoSpOWioQuoCpgjqUKVqFkuoyvNZPAaq3KvYI4xaqgDODSDldWYOK5tN\\\"},\\\"searchResourceToken\\\":{\\\"showedHotelIds\\\":[*********],\\\"suggestInfoTokens\\\":[{\\\"key\\\":\\\"hotelId\\\",\\\"value\\\":\\\"*********\\\"},{\\\"key\\\":\\\"startPrice\\\",\\\"value\\\":\\\"189\\\"},{\\\"key\\\":\\\"searchHotelLon\\\",\\\"value\\\":\\\"104.269136\\\"},{\\\"key\\\":\\\"searchHotelLat\\\",\\\"value\\\":\\\"23.353105\\\"},{\\\"key\\\":\\\"mapType\\\",\\\"value\\\":\\\"GAODE\\\"}]},\\\"bookInitResourceToken\\\":{\\\"aggBookPriceResourceToken\\\":{\\\"currency\\\":\\\"CNY\\\",\\\"accountPaymentAmount\\\":139.00,\\\"individualPaymentAmount\\\":0,\\\"mixPayAccountExtraAmount\\\":0},\\\"roomPriceResourceToken\\\":{},\\\"cancelTip\\\":\\\"2024年12月30日18:00前可免费取消。若未入住或过时取消将收取您首日房费¥139（若用优惠券则以券后支付价为准），修改结果请以酒店回复结果为准。携程会根据您的付款方式扣除房费，订单需等酒店或供应商确认后生效，如订单不确认将全额退款至您的付款账户，订单确认结果以携程短信或邮件通知为准。\\\"}}\"},\"corpPayInfo\":{\"corpPayType\":\"public\"},\"userActionInfo\":{\"userActionToken\":\"PRD-38-4ef38d8d-5f08-4e78-903c-e6200af4ac5a-********-4.1.15-WEB\"},\"approvalFlowInput\":{\"password\":\"\",\"approvalFlowToken\":\"H4sIAAAAAAAA_w3JsRGAIAwAQG0tLa0obHNHQpA4DklgFns3cQJdwnn0259WJWwlZ4MdmwKrGOiWCLzz5kxsUXweaQlJCkY3cqc_Sq3cxVixq5fEpOE-zve5hg9K0u9QUwAAAA\",\"approvers\":[]},\"rcInfos\":[],\"bookUrlInfos\":[{\"urlType\":\"SUCCESS\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayState/PayState?site\\u003d2\\u0026LimitStandard\\u003dfalse\\u0026needFinishResult\\u003dtrue\\u0026\"},{\"urlType\":\"S_BACK\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayState/PayState?site\\u003d2\\u0026LimitStandard\\u003dfalse\\u0026needFinishResult\\u003dtrue\\u0026\"},{\"urlType\":\"E_BACK\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayMent/PayMent\"},{\"urlType\":\"FROM_URL\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayMent/PayMent?leomacmd://navi?%7b%22backward%22%3atrue%2c%22navi%22%3a0%2c%22now%22%3atrue%2c%22platform%22%3a%7b%7d%7d\"},{\"urlType\":\"S_BACK_MICE\",\"urlValue\":\"https://ct.ctrip.com/mice-activities/welfareTourism\"},{\"urlType\":\"FROM_URL_ORDER_DETAIL\",\"urlValue\":\"https://ct.ctrip.com/webapp/hotel/process/detail?orderId\\u003d{0}\\u0026language\\u003d{1}\\u0026from\\u003dfinish\"},{\"urlType\":\"OFFLINE_RETURN_URL\",\"urlValue\":\"http://corpint.ct.ctripcorp.com/hotel-online/offlineOrder/success\"},{\"urlType\":\"ORDER_DETAIL_URL\",\"urlValue\":\"https://ct.ctrip.com/m/Detail/Hotel/{0}\"}],\"paymentInfoInput\":{\"canWechatPayByOthers\":\"F\",\"clientType\":\"H5\"},\"clientInfo\":{\"clientType\":\"H5\",\"rmsToken\":\"fp\\u003d\\u0026vid\\u003d1712905284030.512d6s1mLc8x\\u0026pageId\\u003d10650045599\\u0026r\\u003d627c42a9bea7404eafe3e12fb5fff6b7\\u0026ip\\u003d2409:896d:421b:812a:75bd:cf6:69fc:2f4f\\u0026rg\\u003dfin\\u0026screen\\u003d363x797\\u0026tz\\u003d+8\\u0026blang\\u003dzh-CN\\u0026oslang\\u003dzh-CN\\u0026ua\\u003dMozilla%2F5.0%20(Linux%3B%20Android%2012%3B%20ELS-AN00%20Build%2FHUAWEIELS-AN00%3B%20wv)%20AppleWebKit%2F537.36%20(KHTML%2C%20like%20Gecko)%20Version%2F4.0%20Chrome%2F89.0.4389.72%20MQQBrowser%2F6.2%20TBS%2F046291%20Mobile%20Safari%2F537.36%20bluemoon%2Fangel\\u0026v\\u003dm17\\u0026bl\\u003dfalse\\u0026clientid\\u003d\",\"secondaryChannel\":\"Android-new\"},\"offlineInfo\":{\"forciblyAssure\":\"F\",\"especialAuth\":\"F\",\"autoConfirm\":\"F\",\"exigentOrder\":\"F\"},\"roomInfoInput\":{},\"sendInfo\":{\"sendSMS\":\"F\",\"sendEmail\":\"F\"},\"ssoInput\":{\"ssoKey\":\"ecdb20a1a9ea4fb295276a8616bc6326\"},\"flashStayInput\":{\"flashPlatFrom\":\"H5\"},\"hotelPayTypeInput\":[{\"payCode\":\"ROOM\",\"payType\":\"CORP_PAY\"}],\"strategyInfos\":[{\"strategyKey\":\"BOOKING_SCENARIO\",\"strategyValue\":\"HOTEL_LIST\"},{\"strategyKey\":\"BOOKING_WITH_PERSONAL_ACCOUNT\",\"strategyValue\":\"F\"}],\"costCenterInfo\":{\"costCenterJsonString\":\"{\\\"items\\\":{\\\"fdefault\\\":{\\\"cost1\\\":\\\"内部员工\\\",\\\"cost2\\\":\\\"本职工作\\\",\\\"cost3\\\":\\\"新营销-抖音-达播组\\\",\\\"cost4\\\":\\\"4000\\\",\\\"cost5\\\":\\\"新营销-抖音-达播组\\\",\\\"cost6\\\":\\\"\\\"}}}\"},\"orderCreateToken\":\"H4sIAAAAAAAAAOMSCfb0c/dxjXcMCAjyD3P0iXfz8Q9X0uBSSTIyTDU3NU3WtTRMTdI1SbJI1k0yMzbSTUkzMUsxMTJJNrBIEWDQYAAAjvHLGUIAAAA\\u003d\",\"useNewOrderCreate\":\"F\"}", OrderCreateRequestType.class)
        orderCreateRequestType.cityInput.cityId = 22249
        resultOld = JsonUtil.fromJson("{\"saveList\":[{\"bizType\":4,\"orderID\":****************,\"uid\":\"**********\",\"pid\":\"**********\",\"corpId\":\"bluemoon\",\"eid\":\"**********\",\"costCenterDesc\":\"\",\"costCenterDescEn\":\"\",\"jounaryNo\":\"*********:ID01FxJM0ALJ1B\",\"jounaryNoTitle\":\"酒店关联行程号\",\"jounaryNoTitleEn\":\"Associated itinerary No. of hotel\",\"orderCostCenters\":[{\"costCenterKey\":4,\"costCenterValue\":\"4000\",\"costCenterTitle\":\"费用归属公司\",\"costCenterTitleEn\":\"费用归属公司\"},{\"costCenterKey\":3,\"costCenterValue\":\"新营销-抖音-达播组\",\"costCenterTitle\":\"项目名称（若选择外包/外部人员，必须填写）\",\"costCenterTitleEn\":\"项目名称（若选择外包/外部人员，必须填写）\"},{\"costCenterKey\":2,\"costCenterValue\":\"本职工作\",\"costCenterTitle\":\"出差事由\",\"costCenterTitleEn\":\"出差事由\"},{\"costCenterKey\":1,\"costCenterValue\":\"内部员工\",\"costCenterTitle\":\"人员类型\",\"costCenterTitleEn\":\"人员类型\"},{\"costCenterKey\":6,\"costCenterValue\":\"\",\"costCenterTitle\":\"备注\",\"costCenterTitleEn\":\"备注\"},{\"costCenterKey\":5,\"costCenterValue\":\"新营销-抖音-达播组\",\"costCenterTitle\":\"部门\",\"costCenterTitleEn\":\"部门\"}],\"passengers\":[{\"passengerId\":\"**********\",\"passengerName\":\"刘观观\",\"isEmployee\":1}],\"frequentlyUsedCostCenter\":1,\"createTimeStr\":\"2024-12-30 11:31:35\"}]}", SaveOrderCostCenterRequestType.class)
        matchCostCenterResponseType = JsonUtil.fromJson("{\"result\":{\"costCenterDescription\":\"\",\"costCenterDescriptionEn\":\"\",\"costCenterContentlist\":[{\"groupID\":1,\"orderOrPerson\":\"O\",\"costCenterConfigs\":[{\"level\":1,\"costCenterTitle\":\"人员类型\",\"costCenterTitleEn\":\"人员类型\",\"required\":true,\"canChange\":true,\"linked\":false,\"selectHidden\":false,\"readUserCostCenter\":0,\"needAddress\":false}],\"costCenterItems\":[]},{\"groupID\":2,\"orderOrPerson\":\"O\",\"costCenterConfigs\":[{\"level\":2,\"costCenterTitle\":\"出差事由\",\"costCenterTitleEn\":\"出差事由\",\"required\":true,\"canChange\":true,\"linked\":false,\"selectHidden\":false,\"readUserCostCenter\":0,\"needAddress\":false}],\"costCenterItems\":[]},{\"groupID\":3,\"orderOrPerson\":\"O\",\"costCenterConfigs\":[{\"level\":3,\"costCenterTitle\":\"项目名称（若选择外包/外部人员，必须填写）\",\"costCenterTitleEn\":\"项目名称（若选择外包/外部人员，必须填写）\",\"required\":false,\"canChange\":true,\"linked\":false,\"selectHidden\":false,\"readUserCostCenter\":0,\"needAddress\":false}],\"costCenterItems\":[]},{\"groupID\":4,\"orderOrPerson\":\"O\",\"costCenterConfigs\":[{\"level\":4,\"costCenterTitle\":\"费用归属公司\",\"costCenterTitleEn\":\"费用归属公司\",\"required\":true,\"canChange\":true,\"linked\":false,\"selectHidden\":false,\"readUserCostCenter\":0,\"needAddress\":false}],\"costCenterItems\":[]},{\"groupID\":5,\"orderOrPerson\":\"O\",\"costCenterConfigs\":[{\"level\":5,\"costCenterTitle\":\"部门\",\"costCenterTitleEn\":\"部门\",\"required\":true,\"canChange\":true,\"linked\":false,\"selectHidden\":false,\"readUserCostCenter\":0,\"needAddress\":false}],\"costCenterItems\":[]},{\"groupID\":6,\"orderOrPerson\":\"O\",\"costCenterConfigs\":[{\"level\":6,\"costCenterTitle\":\"备注\",\"costCenterTitleEn\":\"备注\",\"required\":false,\"canChange\":true,\"linked\":false,\"selectHidden\":false,\"readUserCostCenter\":0,\"needAddress\":false}],\"costCenterItems\":[]}],\"costCenterExtlist\":[{\"costCenterExtType\":\"TP\",\"costCenterExtTitle\":\"出行目的\",\"costCenterExtTitleEn\":\"JounaryReason\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"PN\",\"costCenterExtTitle\":\"项目号\",\"costCenterExtTitleEn\":\"Project\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"FT\",\"costCenterExtTitle\":\"机票关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of flight\",\"required\":false,\"enabled\":true,\"selectHidden\":false},{\"costCenterExtType\":\"HT\",\"costCenterExtTitle\":\"酒店关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of hotel\",\"required\":false,\"enabled\":true,\"selectHidden\":false},{\"costCenterExtType\":\"TT\",\"costCenterExtTitle\":\"火车票关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of train\",\"required\":false,\"enabled\":true,\"selectHidden\":false},{\"costCenterExtType\":\"CT\",\"costCenterExtTitle\":\"用车关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of car\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"BT\",\"costCenterExtTitle\":\"汽车票关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No.\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"D1\",\"costCenterExtTitle\":\"自定义字段1\",\"costCenterExtTitleEn\":\"Self-defined title No.1\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"D2\",\"costCenterExtTitle\":\"自定义字段2\",\"costCenterExtTitleEn\":\"Self-defined title No.2\",\"required\":false,\"enabled\":false,\"selectHidden\":false}],\"frequentlyUsedCostCenter\":1},\"costCenterDefaultTitles\":[{\"costCenterDefaultType\":\"TP\",\"costCenterDefaultTitle\":\"出行目的\",\"costCenterDefaultTitleEn\":\"JounaryReason\"},{\"costCenterDefaultType\":\"PN\",\"costCenterDefaultTitle\":\"项目号\",\"costCenterDefaultTitleEn\":\"Project\"},{\"costCenterDefaultType\":\"FT\",\"costCenterDefaultTitle\":\"机票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of flight\"},{\"costCenterDefaultType\":\"HT\",\"costCenterDefaultTitle\":\"酒店关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of hotel\"},{\"costCenterDefaultType\":\"TT\",\"costCenterDefaultTitle\":\"火车票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of train\"},{\"costCenterDefaultType\":\"CT\",\"costCenterDefaultTitle\":\"用车关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of car\"},{\"costCenterDefaultType\":\"BT\",\"costCenterDefaultTitle\":\"汽车票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No.\"},{\"costCenterDefaultType\":\"D1\",\"costCenterDefaultTitle\":\"自定义字段1\",\"costCenterDefaultTitleEn\":\"Self-defined title No.1\"},{\"costCenterDefaultType\":\"D2\",\"costCenterDefaultTitle\":\"自定义字段2\",\"costCenterDefaultTitleEn\":\"Self-defined title No.2\"},{\"costCenterDefaultType\":\"CC1\",\"costCenterDefaultTitle\":\"成本中心1\",\"costCenterDefaultTitleEn\":\"Cost center No.1\"},{\"costCenterDefaultType\":\"CC2\",\"costCenterDefaultTitle\":\"成本中心2\",\"costCenterDefaultTitleEn\":\"Cost center No.2\"},{\"costCenterDefaultType\":\"CC3\",\"costCenterDefaultTitle\":\"成本中心3\",\"costCenterDefaultTitleEn\":\"Cost center No.3\"},{\"costCenterDefaultType\":\"CC4\",\"costCenterDefaultTitle\":\"成本中心4\",\"costCenterDefaultTitleEn\":\"Cost center No.4\"},{\"costCenterDefaultType\":\"CC5\",\"costCenterDefaultTitle\":\"成本中心5\",\"costCenterDefaultTitleEn\":\"Cost center No.5\"},{\"costCenterDefaultType\":\"CC6\",\"costCenterDefaultTitle\":\"成本中心6\",\"costCenterDefaultTitleEn\":\"Cost center No.6\"}],\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2024-12-30 11:31:35.286+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", MatchCostCenterResponseType.class)
        costCenterInfoType = Optional.ofNullable(matchCostCenterResponseType.getResult()).orElse(new CostCenterInfoType())
        createOrderResponseType = JsonUtil.fromJson("{\"wsId\":\"9501a484-6fb1-4fa5-ba06-f741a8aefa06\",\"orderId\":****************,\"orderPaymentInfo\":{\"payType\":\"ACCNT\",\"orderAmountInfo\":{\"cnyTotalAmount\":139.00,\"paymentItemList\":[{\"prepayType\":\"ACCNT\",\"gatheringType\":\"D\",\"feeType\":\"HotelRoomFee\",\"amountInfo\":{\"amount\":139.00,\"currency\":\"CNY\",\"exchangeRateToCNY\":1}}]}},\"success\":true,\"supportPaymentBill\":true,\"paymentTransactionId\":\"307935493128765535\",\"orderTotalAmount\":{\"orderAmount\":{\"originAmount\":{\"amount\":139.00,\"currency\":\"CNY\",\"exchange\":1},\"cNYAmount\":139.00},\"paymentBillInfo\":{\"payAmount\":139.00,\"payCNYAmount\":139.00,\"payCurrency\":\"CNY\",\"payExchange\":1}},\"transactionInfo\":{\"transactionId\":\"6473b09a-cb5c-4095-ab36-858a79e75820\",\"subTransactionIdList\":[{\"payType\":\"ACCNT\",\"subTransactionId\":\"ce64d86d-1001-4d1d-85c2-3ad459c451f1\"}]},\"responseCode\":20000,\"responseStatus\":{\"timestamp\":\"2024-12-30 11:31:34.956+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", CreateOrderResponseType.class)
        resultNew = mapper.buildSaveOrderCostCenterRequest(orderCreateRequestType, accountInfo, checkAvailInfo, orderCreateToken, createOrderResponseType, costCenterInfoType, approvalTextInfoResponseType, ssoInfoQueryResponseType, resourceToken, null, null)
        // 抹平变量
        resultOld.saveList.get(0).createTimeStr = "2024-12-30 10:44:45"
        resultNew.saveList.get(0).createTimeStr = "2024-12-30 10:44:45"
        then:
        resultNew != null
        // 对比保存的内容需要完全一致
        resultNew == resultOld



        when: // 仅审批单号 人下无成本中心 7530f3b50f9b4e6188825062ebb1cb2d  2024-12-30T14:01:16
        queryCheckAvailContextResponseType = JsonUtil.fromJson("{\"baseInfo\":{\"userInfo\":{\"uid\":\"**********\",\"policyUid\":\"\",\"corpId\":\"JOUAV\",\"eid\":\"\",\"usersCityId\":268,\"userCountryCode\":\"\"},\"customCurrency\":\"CNY\",\"website\":\"APP\",\"feeType\":\"C\",\"quantity\":1,\"startTime\":\"2024-12-30T00:00:00+08\",\"endTime\":\"2024-12-31T00:00:00+08\",\"language\":\"CN\",\"locale\":\"zh-CN\",\"startTimeUTC\":\"2024-12-29T16:00:00Z\",\"endTimeUTC\":\"2024-12-30T16:00:00Z\",\"pOS\":\"zh-CN\",\"pOSType\":{\"pOS\":\"zh-CN\",\"countryId\":1},\"platform\":\"android\",\"bookingWithPersonalAccount\":false},\"hotelInfo\":{\"subHotelId\":*********,\"masterHotelId\":*********,\"star\":2,\"rStar\":0,\"starLicence\":false,\"customerEval\":0.0,\"hotelGroupId\":5,\"hotelBrandId\":48,\"balancePeriod\":\"GM\",\"oversea\":false,\"tripBusinessHotel\":false,\"cuHotel\":false,\"hasServiceFee\":false,\"localTimeOffsetHours\":0,\"telephone\":\"+86-797-8156718-0\",\"geographicalInfo\":{\"locationInfo\":{\"id\":764},\"cityInfo\":{\"id\":268,\"name\":{\"textGB\":\"赣州\",\"textEn\":\"Ganzhou\"}},\"provinceInfo\":{\"id\":18},\"countryInfo\":{\"id\":1},\"coordinateInfo\":{}},\"address\":{\"textGB\":\"文明大道126号\",\"textEn\":\"126 Wenming Avenue\"},\"hotelName\":{\"textGB\":\"汉庭酒店(赣州江西理工大学店)\",\"textEn\":\"Hanting Hotel (Ganzhou Jiangxi University of Science and Technology Branch)\"},\"currentCountry\":true,\"timeZoneId\":\"Asia/Shanghai\"},\"roomInfo\":{\"subRoomId\":**********,\"roomName\":{\"textGB\":\"高级大床房\",\"textEn\":\"Superior Queen Room\"},\"basicRoomName\":{\"textGB\":\"高级大床房\",\"textEn\":\"Superior Queen Room\"},\"masterBasicRoomId\":443849231,\"masterBasicRoomName\":{\"textGB\":\"高级大床房\"},\"roomType\":\"C\",\"tmcPriceType\":\"NONE\",\"balanceType\":\"FG\",\"gdsType\":\"HZ\",\"mealType\":4,\"priceSuitPropertyValueId\":1262917,\"ratePlanKey\":{\"checkAvlID\":0,\"ratePlanID\":\"0\"},\"addPrice\":false,\"invoiceType\":\"Special\",\"invoiceLimitType\":\"NotSupported\",\"applyAreaPropertyId\":0,\"maxGuestCountPerRoom\":2,\"maskRoom\":false,\"hourlyRoom\":false,\"corpPrice\":true,\"manualGds\":false,\"joined30MinutesFreeCancel\":false,\"premiumRoom\":false,\"welfareRoom\":false,\"groupMemberShip\":false,\"earnHotelPoints\":true,\"bonusPointInfo\":{\"bonusPointRoom\":true,\"bonusPointCode\":\"HZZSF\",\"pointsMode\":\"SJHMS\"},\"earlyArrivalTime\":\"2024-12-30T14:00:00+08\",\"lastCancelTime\":\"2024-12-30T20:00:00+08\",\"latestHoldTime\":\"2024-12-30T20:00:00+08\",\"cnyAmount\":202.00,\"originAmountInfo\":{\"amount\":202.00,\"currency\":\"CNY\",\"exchange\":1},\"customAmountInfo\":{\"amount\":202.00,\"currency\":\"CNY\",\"exchange\":1},\"originCostInfo\":{\"amount\":202,\"currency\":\"CNY\",\"exchange\":1},\"originCostBeforeTaxInfo\":{\"amount\":202,\"currency\":\"CNY\",\"exchange\":1},\"roomDiscountInfo\":{\"promotionSwitchOn\":false,\"roomDailyDiscountInfo\":[],\"originPromotionAmount\":{\"price\":0,\"currency\":\"CNY\"},\"customPromotionAmount\":{\"price\":0,\"currency\":\"CNY\"}},\"roomDailyInfo\":[{\"effectDate\":\"2024-12-30T00:00:00+08\",\"cnyAmount\":202.00,\"amount\":202.00,\"customAmount\":202.00,\"afterPromotionCnyAmount\":202.00,\"afterPromotionCustomAmount\":202.00,\"cost\":202,\"costBeforeTax\":202,\"customizedCnyAmount\":0,\"mealNumber\":2,\"holdDeadline\":0,\"afterPromotionCustomAmountExcludeTax\":202.00,\"afterPromotionCnyAmountExcludeTax\":202.00,\"customAmountExcludeTax\":202.00,\"cnyAmountExcludeTax\":202.00}],\"paymentRulesInfo\":{\"canTravelMoneyPay\":false,\"forceVccPay\":false,\"canCreditCardPay\":false},\"longRentalInfo\":{\"longRental\":false,\"needInquire\":false},\"holdRoomInfo\":{\"holdRoom\":false},\"agreementGiftsToken\":\"ChAI7PsBEgpIWlFZMTg4Njk4\",\"extraPayTaxMultiCurrencyAmountInfo\":{\"customExtraPayTaxTotalAmount\":0,\"cnyExtraPayTaxTotalAmount\":0},\"pricedRoom\":false,\"generalizedPremiumRoom\":false,\"customAmountExcludeTaxInfo\":{\"amount\":202.00,\"currency\":\"CNY\",\"exchange\":1},\"supportAnticipation\":false,\"roomRightInfoList\":[],\"invoiceTypeExcludeAddPrice\":\"Special\",\"roomMealInfo\":{\"mealType\":4,\"mealDescList\":[\"2份早餐\"],\"dailyMealInfoList\":[{\"effectDateUTC\":\"2024-12-29T16:00:00Z\",\"mealDescList\":[\"\"]},{\"effectDateUTC\":\"2024-12-30T16:00:00Z\",\"mealDescList\":[\"2份早餐\"]}]},\"saleRoomTags\":[{\"tagCode\":\"XYJ\",\"tagDesc\":\"\",\"configInfoList\":[{\"position\":301,\"priority\":2,\"style\":\"1\"}]}],\"taxDetails\":[],\"pid\":\"AQoGMjAyLjAwEgYyMDIuMDAieQp3djFfQ01UcHhUTVF4OEcrbndVYUJ6RXlOakk1TVRjaUFraGFLZ0pEUnpJRE1qQXlPZ015TURKQ0RRb0RRMDVaRWdORFRsa2FBVEZLSURKak1UaG1OekZrWWpsbVlUUmlPRGhoWm1OaE4yTmxaV1kwTVRJMk1XVmwqA0NOWTINCgNDTlkSA0NOWRoBMTINCgNDTlkSA0NOWRoBMToCCABCBwjoDxAMGB5KBwjoDxAMGB9QAVgAYgJIWmgPeAKAAcTpxTM\\u003d\",\"onlyGroupMemberCanBook\":false},\"bookingRules\":{\"cancelPenalties\":{\"guaranteePolicyInfo\":{}},\"authorizationRestriction\":{\"latestAuthorizeTime\":\"2024-12-30T19:00:00+08\"},\"confirmRules\":{\"justifyConfirm\":true,\"forbidNotImConfirm\":false},\"welfareRoomRules\":{},\"limitDepartureDateTime\":{\"departureEnd\":\"2024-12-31T12:00:00+08\",\"departureEndUTC\":\"2024-12-31T04:00:00Z\"},\"certificateInfo\":{\"needCertificate\":false},\"minLOS\":1,\"cancelPolicyInfo\":{\"cancelType\":\"FREE\",\"freeCancelPolicySceneType\":\"NOT_SUPPORTED\",\"lastCancelTimeUTC\":\"2024-12-30T12:00:00Z\"},\"limitArrivalDateTime\":{\"arrivalFromUTC\":\"2024-12-30T06:00:00Z\",\"arrivalEndUTC\":\"2024-12-30T12:00:00Z\",\"defaultArrivalTimeEnd\":false},\"rPLimitInfo\":{\"minLos\":1,\"maxLos\":0,\"minAdvancedBookingDays\":0,\"maxAdvancedBookingDays\":0},\"billingGuestInfo\":{\"guestsNameLanguages\":[\"zh\",\"en\"]},\"restrictRuleList\":[]},\"bookExtInfo\":{\"featureControlFlagMap\":{\"EnableMemberPoints\":false,\"EnableXMinutesFreeCancel\":false,\"BrandNewCustomer\":true,\"EnableShadowDiscount\":false,\"EnableYXH\":false,\"EnablePrepayDiscount\":false,\"EnableNewGroupMember\":false,\"ForbidMultiNationality\":false,\"NewUserCountryCode\":true,\"EnableCNYTWODecimal\":true,\"ShieldMaskRoom\":true,\"EnableAddPrice\":false,\"IgnoreSpecialCancelPolicyNotMatchingFilter\":false,\"StrictCancellationPolicy\":false,\"IgnoreRemitTax\":false,\"ShieldMultiplePrice\":false,\"Enable30MinuteFreeCancel\":false,\"EnablePromotionSubTag\":true,\"EnableHotelNewCustomer\":false,\"DisableIPBlock\":false,\"EnableDirectRate\":false,\"EnableOutputPackageRoom\":false,\"ShieldMaskNakedSale\":false,\"EnableMobileRate\":true,\"EnableMatchPolicyWithExtraPayTax\":false}},\"userRightsInfo\":{\"userChosenCouponInfo\":[],\"hotelNewCustomer\":true,\"userSelectedCountryCode\":\"\"},\"reservationToken\":\"AQgBEgoyMDI0LTEyLTMwGhcKAUMSAkZHGgRUUklQIgJIWkjHwb6fBSICCAAqBwoFemgtQ04\\u003d\",\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2024-12-30 14:01:14.374+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", QueryCheckAvailContextResponseType.class)
        checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsId")))
                        .build().getCheckAvailContextInfo()
        orderCreateRequestType = JsonUtil.fromJson("{\"integrationSoaRequestType\":{\"userInfo\":{\"userId\":\"**********\",\"corpId\":\"JOUAV\",\"groupId\":\"\",\"pos\":\"CHINA\"},\"token\":\"3D42E256058FB9C69E2A7D7300FD1603472F79647204D253ADC43A0359F0C929\",\"language\":\"zh-CN\",\"requestId\":\"7530f3b50f9b4e6188825062ebb1cb2d\",\"sourceFrom\":\"H5\",\"transactionID\":\"TID.FLIGHTDOMESTICBOOK.39.87D76FB16EBA4D388587C5CD426BCCFA\",\"gatewayHost\":\"ct.ctrip.com\",\"timezoneOffsetMinutesNew\":\"480\",\"logIndices\":[{\"key\":\"groupId\",\"value\":\"\"},{\"key\":\"timezoneOffsetMinutesNew\",\"value\":\"480\"},{\"key\":\"gatewayHost\",\"value\":\"ct.ctrip.com\"},{\"key\":\"pvid\",\"value\":\"11\"},{\"key\":\"subChannel\",\"value\":\"DING_TALK\"},{\"key\":\"CID\",\"value\":\"09031039315487629054\"},{\"key\":\"EID\"},{\"key\":\"CIP\",\"value\":\"2409:8938:4cb4:59d2:5012:db32:6ec1:a9e4\"},{\"key\":\"miceToken\",\"value\":\"\"},{\"key\":\"groupName\",\"value\":\"\"},{\"key\":\"gatewayIdc\",\"value\":\"SHARB\"},{\"key\":\"traceID\",\"value\":\"\"},{\"key\":\"A\",\"value\":\"ordercheck\"},{\"key\":\"CorpID\",\"value\":\"JOUAV\"},{\"key\":\"C\",\"value\":\"corpbookinghotelbookservice\"},{\"key\":\"M\",\"value\":\"**********\"},{\"key\":\"sid\",\"value\":\"46\"},{\"key\":\"S\",\"value\":\"2026e1cdf361443389089e20e826416d170f78a9a548\"},{\"key\":\"T\",\"value\":\"3D42E256058FB9C69E2A7D7300FD1603472F79647204D253ADC43A0359F0C929\"},{\"key\":\"pos\",\"value\":\"CHINA\"},{\"key\":\"GE\",\"value\":\"1735538472754\"},{\"key\":\"sourceFrom\",\"value\":\"H5\"},{\"key\":\"corpName\",\"value\":\"成都纵横自动化技术股份有限公司\"},{\"key\":\"RID\",\"value\":\"7530f3b50f9b4e6188825062ebb1cb2d\"},{\"key\":\"TID\",\"value\":\"TID.FLIGHTDOMESTICBOOK.39.87D76FB16EBA4D388587C5CD426BCCFA\"},{\"key\":\"VID\",\"value\":\"1672738821215.x6ak6w\"}],\"ticket\":\"449DFCnrHm8aL2ufgXRdVU85nV4EEqWvQHWcGfKBtRXMkZHRSB6eQoeoRkNpc5cNEFMuOG4UhC1NsmjQU2BcxU44pLwxT2s60LjJLA0CJs4\\u003d\",\"transferInfo\":[{\"key\":\"iframe\",\"value\":\"F\"},{\"key\":\"channel\",\"value\":\"sso\"},{\"key\":\"subChannel\",\"value\":\"DING_TALK\"}]},\"hotelBookPassengerInputs\":[{\"hotelPassengerInput\":{\"uid\":\"**********\",\"approvalPassengerId\":\"8011450668585620017\",\"employee\":\"T\",\"external\":\"F\",\"employeeId\":\"01508\"},\"passengerBasicInfo\":{\"gender\":\"U\",\"birth\":\"1992-06-19\"},\"name\":\"谢天正\",\"isRegistrant\":\"F\",\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"15797960619\",\"transferPhoneNo\":\"15797960619\"}}],\"hotelInvoiceInfos\":[],\"cityInput\":{\"cityId\":268},\"hotelContactorInfo\":{\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"15797960619\",\"transferPhoneNo\":\"15797960619\"},\"emailInfo\":{\"email\":\"<EMAIL>\",\"transferEmail\":\"<EMAIL>\"}},\"approvalInput\":{\"masterApprovalNo\":\"TRC131536480152588336@2001_01\",\"subApprovalNo\":\"TRC131536480152588336@2001_01\",\"emergency\":\"F\"},\"miceInput\":{},\"hotelBookInput\":{\"hotelDateRangeInfo\":{\"checkIn\":\"2024-12-30\",\"checkOut\":\"2024-12-31\"},\"roomQuantity\":1},\"membershipInfo\":{\"membershipUid\":\"**********\",\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"15797960619\",\"transferPhoneNo\":\"15797960619\"}},\"bookModeInfo\":{\"bookingType\":\"NONE\"},\"resourceTokenInfo\":{\"resourceToken\":\"{\\\"hotelResourceToken\\\":{\\\"masterHotelId\\\":*********,\\\"hotelId\\\":*********,\\\"searchHotelTraceId\\\":\\\"2c18f71db9fa4b88afca7ceef41261ee\\\",\\\"hid\\\":\\\"ASIGMjAyLjAwQgFDSgYIABAAGABSbQpndjFfQ0o3cHhUTVF4T25GTXhvQ1EwY2lBekl3TWlvRE1qQXlNZzBLQTBOT1dSSURRMDVaR2dFeE9pQXlZekU0WmpjeFpHSTVabUUwWWpnNFlXWmpZVGRqWldWbU5ERXlOakZsWlE9PRAAGAFaA0NOWWINCgNDTlkSA0NOWRoBMWINCgNDTlkSA0NOWRoBMWgIcgYyMDAuMDB6BDAuMDCCASAyYzE4ZjcxZGI5ZmE0Yjg4YWZjYTdjZWVmNDEyNjFlZYoBCjE0MDgyMTMxOTGaASQ2Njc0ZWQ2Yy00MmQyLTRhYTUtYmNiOS1mNTA2ZDllNGJlZGWiAQIIAA\\\\u003d\\\\u003d\\\",\\\"hotelType\\\":\\\"C\\\",\\\"bonusPoint\\\":\\\"T\\\",\\\"hotelGeoInfoResourceToken\\\":{\\\"cityId\\\":268,\\\"locationId\\\":764}},\\\"roomResourceToken\\\":{\\\"roomId\\\":**********,\\\"baseRoomId\\\":443849231,\\\"ratePlanTraceLogId\\\":\\\"93ffb44a76444c5d8baed2e97326059a_\\\",\\\"allRoomType\\\":\\\"EXIST_C\\\",\\\"bedName\\\":\\\"1张1.8米大床\\\",\\\"windowName\\\":\\\"有窗\\\",\\\"breakfastInfo\\\":\\\"2份早餐\\\",\\\"confirmType\\\":\\\"IMMEDIATE\\\",\\\"roomType\\\":\\\"C\\\",\\\"balanceType\\\":\\\"FG\\\",\\\"tmcPrice\\\":false,\\\"adult\\\":2,\\\"pId\\\":\\\"AQoGMjAyLjAwEgYyMDIuMDAieQp3djFfQ01UcHhUTVF4OEcrbndVYUJ6RXlOakk1TVRjaUFraGFLZ0pEUnpJRE1qQXlPZ015TURKQ0RRb0RRMDVaRWdORFRsa2FBVEZLSURKak1UaG1OekZrWWpsbVlUUmlPRGhoWm1OaE4yTmxaV1kwTVRJMk1XVmwqA0NOWTINCgNDTlkSA0NOWRoBMTINCgNDTlkSA0NOWRoBMToCCABCBwjoDxAMGB5KBwjoDxAMGB9QAVgAYgJIWmgPeAKAAcTpxTM\\\\u003d\\\",\\\"amadeus\\\":false,\\\"supportAccommodation\\\":\\\"F\\\",\\\"roomOriginCurrency\\\":\\\"CNY\\\",\\\"roomScenarioEnum\\\":\\\"ROOM_LIST\\\",\\\"ratePlanRequestToken\\\":\\\"ASi1L/0AUI0VACaupD0gDTQAbACgyfYuBplJiAZg3AUobDMgf4A3iG9B+FqzWAQGZgXsUMIPAhlBifSNwEZ/VePvZE6hJ99nPN4HlgCUAJIAHHdEX28ae7QrVirWJbn44C8mf/h90hp6H2pViZNPfsyYGKOi+lumQTOyWYFo9S/RkNNOSBOMM3APFblQvj9dmStd5QG6UMYL1OhAdYrGB++hew5r6TW8Bs/7C95X8CJ/X7OVObDLoWEWNm1Gyk/ZSRl+AzTn+RuoCGBC6TlkUoAI6r+YYgAMsmXclUW2C5sgZCNObDAQlFDo+0LYjI1f5Yr15EJK4FabywceulQlhmWTNNxD/rMqGZGuc9TN3wi/wIBB2qczrlbEGEVvV3ld2VI6pQdXOGkmDmny/CSRvIYfyndx4XWt6FiIco59Zye9seBvCIpsExTNSO23xUO7At3P6HJePBsJxvJY5xqiGeOWnry458Q0oc9ckblLkNopzzr9HuSjnvjbSPAtAuM2ve20tm08AfUvNrMc1+XIMAtjUwuYst1PQHV+YuBLEiyrxrVYc6lmWGrk93gk8xxzHhdvO7dzbeMcsWBszF25Ta8e44gnjy2EHmVPvGZIi4dblW7+XWirRYZs/rXrXBGXcuporbT8E7q84o2F4PGjmtv9aVQuE28+M89wFmjNwbqVSF7SmbsbZFYzEkwaL8fG304rwJvo3kT3IroP0T2I7jl0n6F7C91X6KJ+d7lT6Jl+dou+das/6Au6gU6B+RIAMFA0PIkPWlBQNdmwY0B9PznwJS+cmBT2RD2UxYBlAer7CUGKmhY27AhQn58O+BJpmij7lSBJmh6IsmF3gPp+UuBL8Gt7twjMN//GbQ/OWxSm21wB1fmpgS/lHbjm4AEJAFYoQNRAyAZgJ8zvbjoT8KfMhrunl53FoZAB\\\",\\\"ratePlanRoomToken\\\":\\\"ASi1L/0AUHkAAAoBQxAAGMfBvp8FIgJGRw\\\\u003d\\\\u003d\\\",\\\"roomUniqueKey\\\":\\\"V1-ASi1L/0AULkAAAjHwb6fBRIBQxoETk9ORSICRkc4xYpN\\\",\\\"adultNumber\\\":2,\\\"windowType\\\":2},\\\"reservationResourceToken\\\":{\\\"wsId\\\":\\\"13b01f74-8f07-4ec6-afcf-4436028a01df\\\",\\\"policyToken\\\":\\\"v1KLUv/QBQnQkAppI9JTCpugHEM62kt0qLDRpTcloYyqD1Jh9+W5a+adp5D4rgwMLAGAczADQAMgDeFvnLLMIUMRjYto+6pnTL7EERLZ4IoXigbfsgbTv5H5k3Kju0lnEcAMeB2raDs6psLUMVz68BjUWetG3K5EqjHbzMfvSj0TlcZp+JlHgS1C5ip2kJsWgSiEhqDtHkaViK3FMsJNs2MUJG5Iuc18XLxEi97WOQIHMwyuqhRywpDx5rSlmm6fy2FffseFngXUmwSMlwdpUhGQcNCwptY194Qpx61Dv65LyqT7XfKUxeEbLAQ0tCH84u46pCx9Sz6ihhb6fSmr4pjzICFgA6G4djh8O1w35z8cxdiCqEqTloqELqAqYI6lClahZLqMrzWdwFqtyr2COMWqoAzg0g5XVmDiubTQ\\\\u003d\\\\u003d\\\",\\\"arriveTimeKey\\\":\\\"H4sIAAAAAAAAAOPi4miYsXtS81MGARYhESMDIxNdQyNdY4MQQyMrAwMgigIAIYDk8SIAAAA\\\\u003d\\\"},\\\"bookInitResourceToken\\\":{\\\"roomPriceResourceToken\\\":{},\\\"cancelTip\\\":\\\"2024年12月30日20:00前可免费取消。担保是酒店对您的银行卡进行预授权或扣款，用于提前锁定房源，离店后由酒店取消预授权或退款。房费需到店另付，部分酒店可能会直接扣取担保金作为房费，订单确认结果以携程短信或邮件通知为准。\\\"}}\"},\"corpPayInfo\":{\"corpPayType\":\"public\"},\"userActionInfo\":{\"userActionToken\":\"PRD-25-0eb91c36-72af-4a12-9a22-10342b3c0af1-********-4.1.15-WEB\"},\"rcInfos\":[{\"rcToken\":\"H4sIAAAAAAAAAOPi9PEPjw8I8nR2FWJydJZiUGJ0AwCbOjK8FAAAAA\"}],\"bookUrlInfos\":[{\"urlType\":\"SUCCESS\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayState/PayState?site\\u003d2\\u0026LimitStandard\\u003dfalse\\u0026needFinishResult\\u003dtrue\\u0026\"},{\"urlType\":\"S_BACK\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayState/PayState?site\\u003d2\\u0026LimitStandard\\u003dfalse\\u0026needFinishResult\\u003dtrue\\u0026\"},{\"urlType\":\"E_BACK\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayMent/PayMent\"},{\"urlType\":\"FROM_URL\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayMent/PayMent?leomacmd://navi?%7b%22backward%22%3atrue%2c%22navi%22%3a0%2c%22now%22%3atrue%2c%22platform%22%3a%7b%7d%7d\"},{\"urlType\":\"S_BACK_MICE\",\"urlValue\":\"https://ct.ctrip.com/mice-activities/welfareTourism\"},{\"urlType\":\"FROM_URL_ORDER_DETAIL\",\"urlValue\":\"https://ct.ctrip.com/webapp/hotel/process/detail?orderId\\u003d{0}\\u0026language\\u003d{1}\\u0026from\\u003dfinish\"},{\"urlType\":\"OFFLINE_RETURN_URL\",\"urlValue\":\"http://corpint.ct.ctripcorp.com/hotel-online/offlineOrder/success\"},{\"urlType\":\"ORDER_DETAIL_URL\",\"urlValue\":\"https://ct.ctrip.com/m/Detail/Hotel/{0}\"}],\"paymentInfoInput\":{\"canWechatPayByOthers\":\"F\",\"clientType\":\"H5\"},\"clientInfo\":{\"clientType\":\"H5\",\"rmsToken\":\"fp\\u003d\\u0026vid\\u003d1672738821215.x6ak6w\\u0026pageId\\u003d10650045599\\u0026r\\u003df3783b89225a4996ba1a344e7ff63c2b\\u0026ip\\u003d2409:8938:4cb4:59d2:5012:db32:6ec1:a9e4\\u0026rg\\u003dfin\\u0026screen\\u003d360x800\\u0026tz\\u003d+8\\u0026blang\\u003dzh-CN\\u0026oslang\\u003dzh-CN\\u0026ua\\u003dMozilla%2F5.0%20(Linux%3B%20U%3B%20Android%2012%3B%20zh-CN%3B%20JEF-AN20%20Build%2FHUAWEIJEF-AN20)%20AppleWebKit%2F537.36%20(KHTML%2C%20like%20Gecko)%20Version%2F4.0%20Chrome%2F100.0.4896.58%20UWS%2F5.12.5.0%20Mobile%20Safari%2F537.36%20AliApp(DingTalk%2F7.6.36)%20com.alibaba.android.rimet%2F42315347%20Channel%2F227200%20language%2Fzh-CN%20abi%2F64%20Hmos%2F4.2.0%20xpn%2Fhuawei%20UT4\\u0026v\\u003dm17\\u0026bl\\u003dfalse\\u0026clientid\\u003d\",\"secondaryChannel\":\"Android-new\"},\"offlineInfo\":{\"forciblyAssure\":\"F\",\"especialAuth\":\"F\",\"autoConfirm\":\"F\",\"exigentOrder\":\"F\"},\"roomInfoInput\":{},\"sendInfo\":{\"sendSMS\":\"F\",\"sendEmail\":\"F\"},\"ssoInput\":{\"ssoKey\":\"ed966e7dca364e35bcdc9bd5554afeaa\"},\"arriveTimeInput\":{\"arriveTimeToken\":\"H4sIAAAAAAAAAOPi4miYsXtS81MGARYhESMDIxNdQyNdY4MQQyMrAwMgigIAIYDk8SIAAAA\\u003d\"},\"flashStayInput\":{\"flashPlatFrom\":\"H5\"},\"hotelPayTypeInput\":[{\"payCode\":\"ROOM\",\"payType\":\"CASH\"}],\"strategyInfos\":[{\"strategyKey\":\"BOOKING_SCENARIO\",\"strategyValue\":\"HOTEL_LIST\"},{\"strategyKey\":\"BOOKING_WITH_PERSONAL_ACCOUNT\",\"strategyValue\":\"F\"}],\"useNewOrderCreate\":\"F\"}", OrderCreateRequestType.class)
        orderCreateRequestType.cityInput.cityId = 22249
        resultOld = JsonUtil.fromJson("{\"saveList\":[{\"bizType\":4,\"orderID\":****************,\"uid\":\"**********\",\"pid\":\"**********\",\"corpId\":\"JOUAV\",\"eid\":\"**********\",\"jounaryNo\":\"TRC131536480152588336@2001_01\",\"jounaryNoTitle\":\"酒店关联行程号\",\"jounaryNoTitleEn\":\"Associated itinerary No. of hotel\",\"orderCostCenters\":[],\"passengers\":[{\"passengerId\":\"**********\",\"passengerName\":\"谢天正\",\"isEmployee\":1}],\"frequentlyUsedCostCenter\":1,\"createTimeStr\":\"2024-12-30 14:01:15\"}]}", SaveOrderCostCenterRequestType.class)
        matchCostCenterResponseType = JsonUtil.fromJson("{\"costCenterDefaultTitles\":[{\"costCenterDefaultType\":\"TP\",\"costCenterDefaultTitle\":\"出行目的\",\"costCenterDefaultTitleEn\":\"JounaryReason\"},{\"costCenterDefaultType\":\"PN\",\"costCenterDefaultTitle\":\"项目号\",\"costCenterDefaultTitleEn\":\"Project\"},{\"costCenterDefaultType\":\"FT\",\"costCenterDefaultTitle\":\"机票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of flight\"},{\"costCenterDefaultType\":\"HT\",\"costCenterDefaultTitle\":\"酒店关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of hotel\"},{\"costCenterDefaultType\":\"TT\",\"costCenterDefaultTitle\":\"火车票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of train\"},{\"costCenterDefaultType\":\"CT\",\"costCenterDefaultTitle\":\"用车关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of car\"},{\"costCenterDefaultType\":\"BT\",\"costCenterDefaultTitle\":\"汽车票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No.\"},{\"costCenterDefaultType\":\"D1\",\"costCenterDefaultTitle\":\"自定义字段1\",\"costCenterDefaultTitleEn\":\"Self-defined title No.1\"},{\"costCenterDefaultType\":\"D2\",\"costCenterDefaultTitle\":\"自定义字段2\",\"costCenterDefaultTitleEn\":\"Self-defined title No.2\"},{\"costCenterDefaultType\":\"CC1\",\"costCenterDefaultTitle\":\"成本中心1\",\"costCenterDefaultTitleEn\":\"Cost center No.1\"},{\"costCenterDefaultType\":\"CC2\",\"costCenterDefaultTitle\":\"成本中心2\",\"costCenterDefaultTitleEn\":\"Cost center No.2\"},{\"costCenterDefaultType\":\"CC3\",\"costCenterDefaultTitle\":\"成本中心3\",\"costCenterDefaultTitleEn\":\"Cost center No.3\"},{\"costCenterDefaultType\":\"CC4\",\"costCenterDefaultTitle\":\"成本中心4\",\"costCenterDefaultTitleEn\":\"Cost center No.4\"},{\"costCenterDefaultType\":\"CC5\",\"costCenterDefaultTitle\":\"成本中心5\",\"costCenterDefaultTitleEn\":\"Cost center No.5\"},{\"costCenterDefaultType\":\"CC6\",\"costCenterDefaultTitle\":\"成本中心6\",\"costCenterDefaultTitleEn\":\"Cost center No.6\"}],\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2024-12-30 14:01:15.994+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", MatchCostCenterResponseType.class)
        costCenterInfoType = Optional.ofNullable(matchCostCenterResponseType.getResult()).orElse(new CostCenterInfoType())
        createOrderResponseType = JsonUtil.fromJson("{\"wsId\":\"13b01f74-8f07-4ec6-afcf-4436028a01df\",\"orderId\":****************,\"orderPaymentInfo\":{\"payType\":\"CASH\",\"orderAmountInfo\":{\"cnyTotalAmount\":202.00}},\"success\":true,\"supportPaymentBill\":true,\"paymentTransactionId\":\"307954303709593620\",\"orderTotalAmount\":{\"orderAmount\":{\"originAmount\":{\"amount\":202.00,\"currency\":\"CNY\",\"exchange\":1},\"cNYAmount\":202.00},\"paymentBillInfo\":{\"payAmount\":202.00,\"payCNYAmount\":202.00,\"payCurrency\":\"CNY\",\"payExchange\":1}},\"responseCode\":20000,\"responseStatus\":{\"timestamp\":\"2024-12-30 14:01:15.643+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", CreateOrderResponseType.class)
        resultNew = mapper.buildSaveOrderCostCenterRequest(orderCreateRequestType, accountInfo, checkAvailInfo, orderCreateToken, createOrderResponseType, costCenterInfoType, approvalTextInfoResponseType, ssoInfoQueryResponseType, resourceToken, null, null)
        // 抹平变量
        resultOld.saveList.get(0).createTimeStr = "2024-12-30 10:44:45"
        resultNew.saveList.get(0).createTimeStr = "2024-12-30 10:44:45"
        then:
        System.out.println("resultOld:::::" + JsonUtil.toJson(resultOld))
        System.out.println("resultNew:::::" + JsonUtil.toJson(resultNew))
        resultNew != null
        // 对比保存的内容需要完全一致
        resultNew == resultOld


        when: // 700报错问题 09583140d96e415aab6766ad9ce5c9d0 12-30 18:09:25
        queryCheckAvailContextResponseType = JsonUtil.fromJson("{\"baseInfo\":{\"userInfo\":{\"uid\":\"**********\",\"policyUid\":\"\",\"corpId\":\"McK\",\"eid\":\"\",\"usersCityId\":30,\"userCountryCode\":\"CN\"},\"customCurrency\":\"CNY\",\"website\":\"APP\",\"feeType\":\"C\",\"quantity\":1,\"startTime\":\"2025-01-05T00:00:00+08\",\"endTime\":\"2025-01-10T00:00:00+08\",\"language\":\"CN\",\"locale\":\"zh-CN\",\"startTimeUTC\":\"2025-01-04T16:00:00Z\",\"endTimeUTC\":\"2025-01-09T16:00:00Z\",\"pOS\":\"zh-CN\",\"pOSType\":{\"pOS\":\"zh-CN\",\"countryId\":1},\"platform\":\"ios\",\"bookingWithPersonalAccount\":false,\"bookingChannel\":\"APP\"},\"hotelInfo\":{\"subHotelId\":********,\"masterHotelId\":374912,\"star\":5,\"rStar\":0,\"starLicence\":false,\"customerEval\":4.5,\"hotelGroupId\":12,\"hotelBrandId\":56,\"balancePeriod\":\"M\",\"oversea\":false,\"tripBusinessHotel\":false,\"cuHotel\":false,\"hasServiceFee\":false,\"localTimeOffsetHours\":0,\"telephone\":\"+86-21-********\",\"geographicalInfo\":{\"locationInfo\":{\"id\":115},\"cityInfo\":{\"id\":2,\"name\":{\"textGB\":\"上海\",\"textEn\":\"Shanghai\"}},\"provinceInfo\":{\"id\":2},\"countryInfo\":{\"id\":1},\"coordinateInfo\":{}},\"address\":{\"textGB\":\"嵩山路88号\",\"textEn\":\"No.88 Songshan Road\"},\"hotelName\":{\"textGB\":\"上海新天地安达仕酒店\",\"textEn\":\"Andaz Xintiandi Shanghai, by Hyatt\"},\"currentCountry\":true,\"timeZoneId\":\"Asia/Shanghai\"},\"roomInfo\":{\"subRoomId\":**********,\"roomName\":{\"textGB\":\"安达仕大床房\",\"textEn\":\"Standard Room\"},\"basicRoomName\":{\"textGB\":\"安达仕大床房\",\"textEn\":\"Standard Room\"},\"masterBasicRoomId\":*********,\"masterBasicRoomName\":{\"textGB\":\"安达仕大床房\"},\"roomType\":\"C\",\"tmcPriceType\":\"NONE\",\"balanceType\":\"FG\",\"gdsType\":\"Amadeus\",\"mealType\":4,\"priceSuitPropertyValueId\":1263082,\"ratePlanKey\":{\"checkAvlID\":24538290613,\"ratePlanID\":\"1\"},\"addPrice\":false,\"invoiceType\":\"Special\",\"invoiceLimitType\":\"NotSupported\",\"applyAreaPropertyId\":0,\"maxGuestCountPerRoom\":1,\"maskRoom\":false,\"hourlyRoom\":false,\"corpPrice\":true,\"manualGds\":false,\"joined30MinutesFreeCancel\":false,\"premiumRoom\":false,\"welfareRoom\":false,\"groupMemberShip\":false,\"earnHotelPoints\":false,\"bonusPointInfo\":{\"bonusPointRoom\":true,\"bonusPointCode\":\"KYJF\",\"pointsMode\":\"HYKMS\"},\"earlyArrivalTime\":\"2025-01-05T15:00:00+08\",\"lastCancelTime\":\"2025-01-05T18:00:00+08\",\"cnyAmount\":5917.45,\"originAmountInfo\":{\"amount\":5917.45,\"currency\":\"CNY\",\"exchange\":1},\"customAmountInfo\":{\"amount\":5917.45,\"currency\":\"CNY\",\"exchange\":1},\"originCostInfo\":{\"amount\":5917.45,\"currency\":\"CNY\",\"exchange\":1},\"originCostBeforeTaxInfo\":{\"amount\":5075,\"currency\":\"CNY\",\"exchange\":1},\"roomDiscountInfo\":{\"promotionSwitchOn\":false,\"roomDailyDiscountInfo\":[],\"originPromotionAmount\":{\"price\":0,\"currency\":\"CNY\"},\"customPromotionAmount\":{\"price\":0,\"currency\":\"CNY\"}},\"roomDailyInfo\":[{\"effectDate\":\"2025-01-05T00:00:00+08\",\"cnyAmount\":1183.49,\"amount\":1183.49,\"customAmount\":1183.49,\"afterPromotionCnyAmount\":1183.49,\"afterPromotionCustomAmount\":1183.49,\"cost\":1183.49,\"costBeforeTax\":1015,\"customizedCnyAmount\":0,\"mealNumber\":1,\"holdDeadline\":0,\"afterPromotionCustomAmountExcludeTax\":1015.00,\"afterPromotionCnyAmountExcludeTax\":1015.00,\"customAmountExcludeTax\":1015.00,\"cnyAmountExcludeTax\":1015.00},{\"effectDate\":\"2025-01-06T00:00:00+08\",\"cnyAmount\":1183.49,\"amount\":1183.49,\"customAmount\":1183.49,\"afterPromotionCnyAmount\":1183.49,\"afterPromotionCustomAmount\":1183.49,\"cost\":1183.49,\"costBeforeTax\":1015,\"customizedCnyAmount\":0,\"mealNumber\":1,\"holdDeadline\":0,\"afterPromotionCustomAmountExcludeTax\":1015.00,\"afterPromotionCnyAmountExcludeTax\":1015.00,\"customAmountExcludeTax\":1015.00,\"cnyAmountExcludeTax\":1015.00},{\"effectDate\":\"2025-01-07T00:00:00+08\",\"cnyAmount\":1183.49,\"amount\":1183.49,\"customAmount\":1183.49,\"afterPromotionCnyAmount\":1183.49,\"afterPromotionCustomAmount\":1183.49,\"cost\":1183.49,\"costBeforeTax\":1015,\"customizedCnyAmount\":0,\"mealNumber\":1,\"holdDeadline\":0,\"afterPromotionCustomAmountExcludeTax\":1015.00,\"afterPromotionCnyAmountExcludeTax\":1015.00,\"customAmountExcludeTax\":1015.00,\"cnyAmountExcludeTax\":1015.00},{\"effectDate\":\"2025-01-08T00:00:00+08\",\"cnyAmount\":1183.49,\"amount\":1183.49,\"customAmount\":1183.49,\"afterPromotionCnyAmount\":1183.49,\"afterPromotionCustomAmount\":1183.49,\"cost\":1183.49,\"costBeforeTax\":1015,\"customizedCnyAmount\":0,\"mealNumber\":1,\"holdDeadline\":0,\"afterPromotionCustomAmountExcludeTax\":1015.00,\"afterPromotionCnyAmountExcludeTax\":1015.00,\"customAmountExcludeTax\":1015.00,\"cnyAmountExcludeTax\":1015.00},{\"effectDate\":\"2025-01-09T00:00:00+08\",\"cnyAmount\":1183.49,\"amount\":1183.49,\"customAmount\":1183.49,\"afterPromotionCnyAmount\":1183.49,\"afterPromotionCustomAmount\":1183.49,\"cost\":1183.49,\"costBeforeTax\":1015,\"customizedCnyAmount\":0,\"mealNumber\":1,\"holdDeadline\":0,\"afterPromotionCustomAmountExcludeTax\":1015.00,\"afterPromotionCnyAmountExcludeTax\":1015.00,\"customAmountExcludeTax\":1015.00,\"cnyAmountExcludeTax\":1015.00}],\"paymentRulesInfo\":{\"paymentGuaranteePoly\":\"HOTEL\",\"canTravelMoneyPay\":false,\"enabledPayWay\":\"GC_AmericanExpress,GC_MasterCard,GC_DinersClub,GC_JCB,GC_VISA\",\"forceVccPay\":false,\"canCreditCardPay\":true},\"longRentalInfo\":{\"longRental\":false,\"needInquire\":false},\"holdRoomInfo\":{\"holdRoom\":false},\"extraPayTaxMultiCurrencyAmountInfo\":{\"customExtraPayTaxTotalAmount\":0,\"cnyExtraPayTaxTotalAmount\":0},\"pricedRoom\":false,\"generalizedPremiumRoom\":false,\"customAmountExcludeTaxInfo\":{\"amount\":5917.45,\"currency\":\"CNY\",\"exchange\":1},\"supportAnticipation\":false,\"roomRightInfoList\":[],\"invoiceTypeExcludeAddPrice\":\"Special\",\"roomMealInfo\":{\"mealType\":4,\"mealDescList\":[\"1份早餐\"],\"dailyMealInfoList\":[{\"effectDateUTC\":\"2025-01-04T16:00:00Z\",\"mealDescList\":[\"\"]},{\"effectDateUTC\":\"2025-01-05T16:00:00Z\",\"mealDescList\":[\"1份早餐\"]},{\"effectDateUTC\":\"2025-01-06T16:00:00Z\",\"mealDescList\":[\"1份早餐\"]},{\"effectDateUTC\":\"2025-01-07T16:00:00Z\",\"mealDescList\":[\"1份早餐\"]},{\"effectDateUTC\":\"2025-01-08T16:00:00Z\",\"mealDescList\":[\"1份早餐\"]},{\"effectDateUTC\":\"2025-01-09T16:00:00Z\",\"mealDescList\":[\"1份早餐\"]}]},\"saleRoomTags\":[{\"tagCode\":\"XYJ\",\"tagDesc\":\"\",\"configInfoList\":[{\"position\":301,\"priority\":2,\"style\":\"1\"}]}],\"taxDetails\":[],\"pid\":\"AQoHMTE4My40ORIHMTE4My40OSKWAQqTAXYxX0NKR2doQ2dRMjRLTTRRUWFCekV5TmpNd09ESWlCMkZ0WVdSbGRYTXFDRUZOUVY5RFQxSlFNZ2N4TVRnekxqUTVPZ2N4TVRnekxqUTVRZzBLQTBOT1dSSURRMDVaR2dFeFNpQTVOR0kyT1RVNU1HUm1OamswWTJVNFlXWXpaREUwWXpFeE5EUXlOV1ptTmc9PSoDQ05ZMg0KA0NOWRIDQ05ZGgExMg0KA0NOWRIDQ05ZGgExOgIIAEIHCOkPEAEYBUoHCOkPEAEYClABWABiB0FNQURFVVNoAngBgAGRoIQo\",\"onlyGroupMemberCanBook\":false},\"bookingRules\":{\"cancelPenalties\":{\"paymentGuaranteePoly\":\"HOTEL\",\"cancelDeductDetail\":{\"originDeductionPrice\":{\"price\":5917.45,\"currency\":\"CNY\"},\"customDeductionPrice\":{\"price\":5917.45,\"currency\":\"CNY\"}},\"guaranteePolicyInfo\":{\"amaduesPaymentCode\":\"3\",\"amaduesPaymentType\":\"GUARANTEE\"}},\"authorizationRestriction\":{\"latestAuthorizeTime\":\"\"},\"confirmRules\":{\"justifyConfirm\":true,\"forbidNotImConfirm\":false},\"welfareRoomRules\":{},\"limitDepartureDateTime\":{\"departureEnd\":\"2025-01-10T12:00:00+08\",\"departureEndUTC\":\"2025-01-10T04:00:00Z\"},\"certificateInfo\":{\"needCertificate\":false},\"minLOS\":1,\"cancelPolicyInfo\":{\"cancelType\":\"LIMIT\",\"freeCancelPolicySceneType\":\"NOT_SUPPORTED\",\"lastCancelTimeUTC\":\"2025-01-05T10:00:00Z\",\"guaranteePolicyInfo\":{\"guaranteeType\":\"FULL_ALWAYS\",\"paymentGuaranteePoly\":\"HOTEL\",\"guaranteeReason\":\"NONE\",\"guaranteePriceInfo\":{\"originGuaranteePrice\":{\"price\":5917.45,\"currency\":\"CNY\"},\"customGuaranteePrice\":{\"price\":5917.45,\"currency\":\"CNY\"}}},\"localLastCancelTime\":\"2025-01-05 18:00:00\"},\"limitArrivalDateTime\":{\"arrivalFromUTC\":\"2025-01-05T07:00:00Z\",\"arrivalEndUTC\":\"2025-01-05T16:00:00Z\",\"defaultArrivalTimeEnd\":false,\"localEarlyArrivalTime\":\"2025-01-05 15:00:00\",\"localLastArrivalTime\":\"2025-01-06 00:00:00\"},\"rPLimitInfo\":{\"minLos\":1,\"maxLos\":0,\"minAdvancedBookingDays\":0,\"maxAdvancedBookingDays\":0},\"billingGuestInfo\":{\"guestsNameLanguages\":[\"en\"]},\"restrictRuleList\":[]},\"bookExtInfo\":{\"featureControlFlagMap\":{\"EnableMemberPoints\":false,\"EnableXMinutesFreeCancel\":false,\"BrandNewCustomer\":true,\"EnableShadowDiscount\":false,\"EnableYXH\":false,\"EnablePrepayDiscount\":false,\"EnableNewGroupMember\":false,\"ForbidMultiNationality\":false,\"NewUserCountryCode\":true,\"EnableCNYTWODecimal\":true,\"ShieldMaskRoom\":true,\"EnableAddPrice\":false,\"IgnoreSpecialCancelPolicyNotMatchingFilter\":false,\"StrictCancellationPolicy\":false,\"IgnoreRemitTax\":false,\"ShieldMultiplePrice\":false,\"Enable30MinuteFreeCancel\":false,\"EnablePromotionSubTag\":true,\"EnableHotelNewCustomer\":false,\"DisableIPBlock\":false,\"EnableDirectRate\":false,\"EnableOutputPackageRoom\":false,\"ShieldMaskNakedSale\":false,\"EnableMobileRate\":true,\"EnableMatchPolicyWithExtraPayTax\":false}},\"userRightsInfo\":{\"userChosenCouponInfo\":[],\"hotelNewCustomer\":true,\"userSelectedCountryCode\":\"CN\"},\"reservationToken\":\"AQgFEgoyMDI1LTAxLTA1Gh8KAUMSAkZHGgVIT1RFTCIHQW1hZGV1cyoASNuCjOEEIgIIACoHCgV6aC1DTg\\u003d\\u003d\",\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2024-12-30 18:09:24.970+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", QueryCheckAvailContextResponseType.class)
        checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsId")))
                        .build().getCheckAvailContextInfo()
        orderCreateRequestType = JsonUtil.fromJson("{\"integrationSoaRequestType\":{\"userInfo\":{\"userId\":\"**********\",\"corpId\":\"McK\",\"groupId\":\"Gr_00004045\",\"pos\":\"CHINA\"},\"token\":\"D42A4A6C605DEAC21125F2DC1EB53729116A3EDF40A56237435C0B544DC47DED\",\"language\":\"zh-CN\",\"requestId\":\"09583140d96e415aab6766ad9ce5c9d0\",\"sourceFrom\":\"H5\",\"transactionID\":\"TID.HOTELBOOK.768.68C12623B466405D9BF97F3E70C36A66\",\"gatewayHost\":\"ct.ctrip.com\",\"timezoneOffsetMinutesNew\":\"480\",\"logIndices\":[{\"key\":\"groupId\",\"value\":\"Gr_00004045\"},{\"key\":\"timezoneOffsetMinutesNew\",\"value\":\"480\"},{\"key\":\"gatewayHost\",\"value\":\"ct.ctrip.com\"},{\"key\":\"pvid\",\"value\":\"5\"},{\"key\":\"subChannel\",\"value\":\"\"},{\"key\":\"CID\",\"value\":\"51121086311098347810\"},{\"key\":\"EID\"},{\"key\":\"CIP\",\"value\":\"************\"},{\"key\":\"miceToken\",\"value\":\"\"},{\"key\":\"groupName\",\"value\":\"麦肯锡\"},{\"key\":\"gatewayIdc\",\"value\":\"SHARB\"},{\"key\":\"traceID\",\"value\":\"\"},{\"key\":\"A\",\"value\":\"ordercheck\"},{\"key\":\"CorpID\",\"value\":\"McK\"},{\"key\":\"C\",\"value\":\"corpbookinghotelbookservice\"},{\"key\":\"M\",\"value\":\"**********\"},{\"key\":\"sid\",\"value\":\"768\"},{\"key\":\"S\",\"value\":\"d0df015c71a047a6839bdca56c2df20b\"},{\"key\":\"T\",\"value\":\"D42A4A6C605DEAC21125F2DC1EB53729116A3EDF40A56237435C0B544DC47DED\"},{\"key\":\"pos\",\"value\":\"CHINA\"},{\"key\":\"GE\",\"value\":\"1735553364577\"},{\"key\":\"sourceFrom\",\"value\":\"H5\"},{\"key\":\"corpName\",\"value\":\"麦肯锡\"},{\"key\":\"RID\",\"value\":\"09583140d96e415aab6766ad9ce5c9d0\"},{\"key\":\"TID\",\"value\":\"TID.HOTELBOOK.768.68C12623B466405D9BF97F3E70C36A66\"},{\"key\":\"VID\",\"value\":\"1670897933888.ix318o\"}],\"ticket\":\"JF2zzy7UDLwlVYfG5BwKJ+E/q6o2BCURvuqk4BDAZSKSOpbF8i/tZywAzqxC6uxV/m3W+xI2SpOXJw1ulfJVn044pLwxT2s60LjJLA0CJs4\\u003d\",\"transferInfo\":[{\"key\":\"iframe\",\"value\":\"F\"},{\"key\":\"channel\",\"value\":\"Native\"}]},\"hotelBookPassengerInputs\":[{\"hotelPassengerInput\":{\"uid\":\"**********\",\"employee\":\"T\",\"external\":\"F\",\"employeeId\":\"307322\"},\"passengerBasicInfo\":{\"preferFirstName\":\"YUXI\",\"preferLastName\":\"WEI\",\"gender\":\"U\",\"birth\":\"1999-03-26\"},\"name\":\"韦俣兮\",\"isRegistrant\":\"F\",\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"18926003010\",\"transferPhoneNo\":\"18926003010\"},\"nationalityInfo\":{\"nationalityCode\":\"CN\"}}],\"hotelInvoiceInfos\":[],\"cityInput\":{\"cityId\":2},\"hotelContactorInfo\":{\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"18926003010\",\"transferPhoneNo\":\"18926003010\"},\"emailInfo\":{\"email\":\"<EMAIL>\",\"transferEmail\":\"<EMAIL>\"}},\"miceInput\":{},\"hotelBookInput\":{\"hotelDateRangeInfo\":{\"checkIn\":\"2025-01-05\",\"checkOut\":\"2025-01-10\"},\"roomQuantity\":1},\"membershipInfo\":{\"membershipNo\":\"555692139O\",\"membershipUid\":\"**********\"},\"bookModeInfo\":{\"bookingType\":\"NONE\"},\"resourceTokenInfo\":{\"resourceToken\":\"{\\\"hotelResourceToken\\\":{\\\"masterHotelId\\\":374912,\\\"hotelId\\\":********,\\\"searchHotelTraceId\\\":\\\"94b69590df694ce8af3d14c114425ff6\\\",\\\"hid\\\":\\\"ASIHMTE4My40OUIBQUoGCAAQABgAUn0Kd3YxX0NJRHhGaENSb0lRb0dnaEJUVUZmUTA5U1VDSUhNVEU0TXk0ME9Tb0hNVEU0TXk0ME9USU5DZ05EVGxrU0EwTk9XUm9CTVRvZ09UUmlOamsxT1RCa1pqWTVOR05sT0dGbU0yUXhOR014TVRRME1qVm1aalk9EAAYAVoDQ05ZYg0KA0NOWRIDQ05ZGgExYg0KA0NOWRIDQ05ZGgExaBByBzE3NDkuMDB6BDAuMDCCASA5NGI2OTU5MGRmNjk0Y2U4YWYzZDE0YzExNDQyNWZmNooBCjEyNzczNjI1MjSaASQ2YTk4ZjQ0Yy1lNjVhLTQ2ZTMtYWJjOS1lM2IyYzc0NWQ1MmaiAQIIAA\\\\u003d\\\\u003d\\\",\\\"hotelType\\\":\\\"C\\\",\\\"bonusPoint\\\":\\\"T\\\",\\\"hotelGeoInfoResourceToken\\\":{\\\"cityId\\\":2,\\\"locationId\\\":115}},\\\"roomResourceToken\\\":{\\\"roomId\\\":**********,\\\"baseRoomId\\\":*********,\\\"ratePlanTraceLogId\\\":\\\"6be8e7eee4cc45ec829b0f31581daba7_\\\",\\\"allRoomType\\\":\\\"EXIST_C\\\",\\\"bedName\\\":\\\"1张2米特大床\\\",\\\"windowName\\\":\\\"有窗户但不能打开通风\\\",\\\"breakfastInfo\\\":\\\"1份早餐\\\",\\\"confirmType\\\":\\\"IMMEDIATE\\\",\\\"roomType\\\":\\\"C\\\",\\\"balanceType\\\":\\\"FG\\\",\\\"tmcPrice\\\":false,\\\"adult\\\":1,\\\"pId\\\":\\\"AQoHMTE4My40ORIHMTE4My40OSKWAQqTAXYxX0NKR2doQ2dRMjRLTTRRUWFCekV5TmpNd09ESWlCMkZ0WVdSbGRYTXFDRUZOUVY5RFQxSlFNZ2N4TVRnekxqUTVPZ2N4TVRnekxqUTVRZzBLQTBOT1dSSURRMDVaR2dFeFNpQTVOR0kyT1RVNU1HUm1OamswWTJVNFlXWXpaREUwWXpFeE5EUXlOV1ptTmc9PSoDQ05ZMg0KA0NOWRIDQ05ZGgExMg0KA0NOWRIDQ05ZGgExOgIIAEIHCOkPEAEYBUoHCOkPEAEYClABWABiB0FNQURFVVNoAngBgAGRoIQo\\\",\\\"amadeus\\\":true,\\\"supportAccommodation\\\":\\\"F\\\",\\\"roomOriginCurrency\\\":\\\"CNY\\\",\\\"roomScenarioEnum\\\":\\\"ROOM_LIST\\\",\\\"ratePlanRequestToken\\\":\\\"ASi1L/0AUB0VABZuo0AgS1UAAAwUzGCQDYRVhiEUBphQA4GGRElOIimKorQkR0IPbdskKc+y7uo+OwcYUVXbd/ciM32o1spYSgnmGvM/lgCSAJIAld2ltA+cmDwng77pjC41Em/U6CTBHNVSb4faTvEQu5DYBu4sqEUJ845SEAz1INm3T8n5TUYS5uUYlMlkCs+zUpGiMpkm02AbuXsNRyEWQmBNx0XghgVHhmhg+Av5K3i+rPEUsn8J/xkfv3tFRJTrEbkeFH1p6wTB3RAz/+DLHheqAA44kNfQFopJ5Zpckoi2Fkh8yIgIFKmEFFHYrLswtkfRpiCqtllVnukZ+FlMjW7wCb4+Ic2ZWAy4Zp24vwYjkQiG6sbTle6qp1DcUX3UtbOG8ZWiPE1E2ZKZXjgWUytGFNC6cTiVG7kZu/XQEpqsVulUqMfdy0o8LAny75MS2Q9UMcWPwyiBoLhxzHND4RYGqItqi9ru9Ptdage3FAwFJtGShjNdSmSsWkLdmKcm6bYk27YlA4791sSvIqXkekQiD4m+dEmOnCyxbcCBvTXxq/jUOOmnhzbjqHVtfdV4y3hOnOTlbNLng1Mtar28G07zilI3a1Fhq3K6O7bcpi+qGCRZaJ0KBqbri7PVNqdtW45q8elVQxqSikhaxFOamPxMMCATknJQ0F45zsKDusAXfSks40ixWDmuE6ZWbbyH/B7yb8ivIX+G/BXyT8gfIX9GjrObLzKE3MlgxmRLvrKCfCBHZAj9HRJAAHgOm48i3/RywyYc+60Ev8q2it6VoJiJJYd/Axz7rQK/8noXS1LsY7lhAhzaWwR+5fUultrHPa9nXCw3jIBjvyXxKzTUZm7MT+d2S22GnkwRXc2ThAN7C8GvTiQ0BgBWKEDUQMgGAI4TZvKi7RxKHg\\\\u003d\\\\u003d\\\",\\\"ratePlanRoomToken\\\":\\\"ASi1L/0AUHkAAAoBQxAAGNuCjOEEIgJGRw\\\\u003d\\\\u003d\\\",\\\"roomUniqueKey\\\":\\\"V1-ASi1L/0AULkAAAjbgozhBBIBQxoETk9ORSICRkc46otN\\\",\\\"adultNumber\\\":1,\\\"windowType\\\":6},\\\"reservationResourceToken\\\":{\\\"wsId\\\":\\\"f0827949-4ff5-4044-8642-de416ede3446\\\",\\\"policyToken\\\":\\\"v1KLUv/QBQjQkAphI9JCCL6gFnbFRsPoO83UC2X5fez/Kq1uGafS+mH8A9rQATSADIrDMAMwAyAN4N8lfZAufYtg+qpnSrrELASTAO07CAbfsgbTv5H5c3KjMOqVUYBuBxEqxtOzijytQ6BBHPrwNtRZ7admSypNEOTmY/+tHoHK6yj3sKxYRgTUKSlmZinqXpx1kk7mdhPDHGGsS2DTFCRuSJnFXFq8RIve1b0OByMMrooSdQUiK81TSyStP5bSXu2fHywFui0CAlw9lVhlwcDwwLbWNPeEIcetA7+uSsqi9qv0OWrCJkgoeURB/O7uKoQseiZ9RRYm+nklp8Rx5lBBYAOhuHY4fDtcN+c/HMXYgqhKk5aKhC6gKmCOpQpWoWS6jK81mcBarcq1gkjGKqQM4NJJV05mBYNvE\\\\u003d\\\",\\\"arriveTimeKey\\\":\\\"H4sIAAAAAAAAAOPi4mhYcKqv/SmDAIuQiJGBkamugaGugWmIoZmVgQEQRQEAqk/hrCIAAAA\\\\u003d\\\"},\\\"bookInitResourceToken\\\":{\\\"roomPriceResourceToken\\\":{},\\\"cancelTip\\\":\\\"2025年1月5日18:00前可免费取消。若您没有入住或逾时取消或更改订单，担保费用将不予退还。\\\"}}\"},\"corpPayInfo\":{\"corpPayType\":\"public\"},\"userActionInfo\":{\"userActionToken\":\"PRD-1495-e2a4efb2-8044-4f36-a62d-afcb5c5c1b7c-********-4.1.15-WEB\"},\"approvalFlowInput\":{\"password\":\"\",\"approvalFlowToken\":\"H4sIAAAAAAAA_w3HsRGAIAwAQG0tLa0obLlLQoAwDgQyj7O4gXcu4hpW-t0ve2RFsICeCpvnf74CqA9Khla6ZEnrTJsjHqaIAw1Beqst9VBMKuc8Igi597if65w-shoHAlMAAAA\",\"approvers\":[{\"uid\":\"2119835381\",\"level\":\"2\"}]},\"rcInfos\":[],\"bookUrlInfos\":[{\"urlType\":\"SUCCESS\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayState/PayState?site\\u003d2\\u0026LimitStandard\\u003dfalse\\u0026needFinishResult\\u003dtrue\\u0026\"},{\"urlType\":\"S_BACK\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayState/PayState?site\\u003d2\\u0026LimitStandard\\u003dfalse\\u0026needFinishResult\\u003dtrue\\u0026\"},{\"urlType\":\"E_BACK\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayMent/PayMent\"},{\"urlType\":\"FROM_URL\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayMent/PayMent?leomacmd://navi?%7b%22backward%22%3atrue%2c%22navi%22%3a0%2c%22now%22%3atrue%2c%22platform%22%3a%7b%7d%7d\"},{\"urlType\":\"S_BACK_MICE\",\"urlValue\":\"https://ct.ctrip.com/mice-activities/welfareTourism\"},{\"urlType\":\"FROM_URL_ORDER_DETAIL\",\"urlValue\":\"https://ct.ctrip.com/webapp/hotel/process/detail?orderId\\u003d{0}\\u0026language\\u003d{1}\\u0026from\\u003dfinish\"},{\"urlType\":\"OFFLINE_RETURN_URL\",\"urlValue\":\"http://corpint.ct.ctripcorp.com/hotel-online/offlineOrder/success\"},{\"urlType\":\"ORDER_DETAIL_URL\",\"urlValue\":\"https://ct.ctrip.com/m/Detail/Hotel/{0}\"}],\"paymentInfoInput\":{\"canWechatPayByOthers\":\"T\",\"clientType\":\"APP\"},\"clientInfo\":{\"clientType\":\"APP\",\"rmsToken\":\"fp\\u003d\\u0026vid\\u003d1670897933888.ix318o\\u0026pageId\\u003d10650065356\\u0026r\\u003d61998559111e4216ad548ca3bd41b721\\u0026ip\\u003d************\\u0026rg\\u003dundefined\\u0026screen\\u003d430x932\\u0026tz\\u003d+8\\u0026blang\\u003dzh-CN\\u0026oslang\\u003dzh-CN\\u0026ua\\u003dMozilla%2F5.0%20(iPhone%3B%20CPU%20iPhone%20OS%2017_6_1%20like%20Mac%20OS%20X)%20AppleWebKit%2F605.1.15%20(KHTML%2C%20like%20Gecko)%20Mobile%2F21G93%2Cios%2CCoreInside%2C17.6.1%2CiPhone%2014%20Pro%20Max%2C9.59.1%2C959.001.000%2CCorpCtrip%2CStatusBar%2CScreenFringe%2CiPhoneX%2CWIFI%2Ccompany%20Titans%2F9.59.1%20ScreenWidth%2F430%20ScreenHeight%2F932%20scale%2F3%20SafeAreaTop%2F59%20SafeAr\\u0026v\\u003dm17\\u0026bl\\u003dfalse\\u0026clientid\\u003d\",\"secondaryChannel\":\"iphone9.59.1-new\"},\"offlineInfo\":{\"forciblyAssure\":\"F\",\"especialAuth\":\"F\",\"autoConfirm\":\"F\",\"exigentOrder\":\"F\"},\"roomInfoInput\":{},\"sendInfo\":{\"sendSMS\":\"F\",\"sendEmail\":\"F\"},\"arriveTimeInput\":{\"arriveTimeToken\":\"H4sIAAAAAAAAAOPi4mhYcKqv/SmDAIuQiJGBkamugaGugWmIoZmVgQEQRQEAqk/hrCIAAAA\\u003d\"},\"flashStayInput\":{\"flashPlatFrom\":\"APP\"},\"hotelPayTypeInput\":[{\"payCode\":\"ROOM\",\"payType\":\"GUARANTEE_SELF_PAY\"}],\"strategyInfos\":[{\"strategyKey\":\"BOOKING_SCENARIO\",\"strategyValue\":\"HOTEL_LIST\"},{\"strategyKey\":\"BOOKING_WITH_PERSONAL_ACCOUNT\",\"strategyValue\":\"F\"}],\"costCenterInfo\":{\"costCenterJsonString\":\"{\\\"items\\\":{\\\"fdefault\\\":{\\\"cost1\\\":\\\"single\\\",\\\"cost2\\\":\\\"\\\",\\\"cost3\\\":\\\"\\\",\\\"ProjectNo\\\":\\\"1097bp01\\\"}}}\"},\"orderCreateToken\":\"H4sIAAAAAAAAAOMSCfb0c/dxjXcMCAjyD3P0iXfz8Q9X0uBSMTVJNjRIMzbUNbI0SdM1AfJ0Ew0MknWNk43SDNMsUyzMLcwEGDQYAGo++ndCAAAA\",\"useNewOrderCreate\":\"F\"}", OrderCreateRequestType.class)
        orderCreateRequestType.cityInput.cityId = 22249
        resultOld = JsonUtil.fromJson("{\"saveList\":[{\"bizType\":4,\"orderID\":****************,\"uid\":\"**********\",\"pid\":\"**********\",\"corpId\":\"McK\",\"eid\":\"**********\",\"costCenterDesc\":\"\",\"costCenterDescEn\":\"\",\"projectId\":0,\"projectCode\":\"\",\"project\":\"1097bp01\",\"projectTitle\":\"Primary Charge Code\",\"projectTitleEn\":\"Primary Charge Code\",\"orderCostCenters\":[{\"costCenterKey\":3,\"costCenterValue\":\"\",\"costCenterTitle\":\"Secondary Charge Code (Optional）\",\"costCenterTitleEn\":\"Secondary Charge Code (Optional）\"},{\"costCenterKey\":2,\"costCenterValue\":\"\",\"costCenterTitle\":\"Secondary Code Allocation (Optional）\",\"costCenterTitleEn\":\"Secondary Code Allocation (Optional）\"},{\"costCenterKey\":1,\"costCenterValue\":\"single\",\"costCenterTitle\":\"Single or multiple charge code\",\"costCenterTitleEn\":\"Single or multiple charge code\"}],\"passengers\":[{\"passengerId\":\"**********\",\"passengerName\":\"WEI/YUXI\",\"isEmployee\":1}],\"frequentlyUsedCostCenter\":0,\"createTimeStr\":\"2024-12-30 18:09:28\"}]}", SaveOrderCostCenterRequestType.class)
        matchCostCenterResponseType = JsonUtil.fromJson("{\"result\":{\"costCenterDescription\":\"\",\"costCenterDescriptionEn\":\"\",\"costCenterContentlist\":[{\"groupID\":1,\"orderOrPerson\":\"O\",\"costCenterConfigs\":[{\"level\":1,\"costCenterTitle\":\"Single or multiple charge code\",\"costCenterTitleEn\":\"Single or multiple charge code\",\"required\":true,\"canChange\":true,\"linked\":false,\"selectHidden\":false,\"readUserCostCenter\":0,\"needAddress\":false}],\"costCenterItems\":[]},{\"groupID\":2,\"orderOrPerson\":\"O\",\"costCenterConfigs\":[{\"level\":2,\"costCenterTitle\":\"Secondary Code Allocation (Optional）\",\"costCenterTitleEn\":\"Secondary Code Allocation (Optional）\",\"required\":false,\"canChange\":true,\"linked\":false,\"selectHidden\":false,\"readUserCostCenter\":0,\"needAddress\":false}],\"costCenterItems\":[]},{\"groupID\":3,\"orderOrPerson\":\"O\",\"costCenterConfigs\":[{\"level\":3,\"costCenterTitle\":\"Secondary Charge Code (Optional）\",\"costCenterTitleEn\":\"Secondary Charge Code (Optional）\",\"required\":false,\"canChange\":true,\"linked\":false,\"selectHidden\":false,\"readUserCostCenter\":0,\"needAddress\":false}],\"costCenterItems\":[]}],\"costCenterExtlist\":[{\"costCenterExtType\":\"TP\",\"costCenterExtTitle\":\"出行目的\",\"costCenterExtTitleEn\":\"JounaryReason\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"PN\",\"costCenterExtTitle\":\"Primary Charge Code\",\"costCenterExtTitleEn\":\"Primary Charge Code\",\"required\":true,\"enabled\":true,\"selectHidden\":false},{\"costCenterExtType\":\"FT\",\"costCenterExtTitle\":\"机票关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of flight\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"HT\",\"costCenterExtTitle\":\"酒店关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of hotel\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"TT\",\"costCenterExtTitle\":\"火车票关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of train\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"CT\",\"costCenterExtTitle\":\"用车关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of car\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"BT\",\"costCenterExtTitle\":\"汽车票关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No.\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"D1\",\"costCenterExtTitle\":\"自定义字段1\",\"costCenterExtTitleEn\":\"Self-defined title No.1\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"D2\",\"costCenterExtTitle\":\"自定义字段2\",\"costCenterExtTitleEn\":\"Self-defined title No.2\",\"required\":false,\"enabled\":false,\"selectHidden\":false}],\"frequentlyUsedCostCenter\":0},\"costCenterDefaultTitles\":[{\"costCenterDefaultType\":\"TP\",\"costCenterDefaultTitle\":\"出行目的\",\"costCenterDefaultTitleEn\":\"JounaryReason\"},{\"costCenterDefaultType\":\"PN\",\"costCenterDefaultTitle\":\"项目号\",\"costCenterDefaultTitleEn\":\"Project\"},{\"costCenterDefaultType\":\"FT\",\"costCenterDefaultTitle\":\"机票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of flight\"},{\"costCenterDefaultType\":\"HT\",\"costCenterDefaultTitle\":\"酒店关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of hotel\"},{\"costCenterDefaultType\":\"TT\",\"costCenterDefaultTitle\":\"火车票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of train\"},{\"costCenterDefaultType\":\"CT\",\"costCenterDefaultTitle\":\"用车关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of car\"},{\"costCenterDefaultType\":\"BT\",\"costCenterDefaultTitle\":\"汽车票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No.\"},{\"costCenterDefaultType\":\"D1\",\"costCenterDefaultTitle\":\"自定义字段1\",\"costCenterDefaultTitleEn\":\"Self-defined title No.1\"},{\"costCenterDefaultType\":\"D2\",\"costCenterDefaultTitle\":\"自定义字段2\",\"costCenterDefaultTitleEn\":\"Self-defined title No.2\"},{\"costCenterDefaultType\":\"CC1\",\"costCenterDefaultTitle\":\"成本中心1\",\"costCenterDefaultTitleEn\":\"Cost center No.1\"},{\"costCenterDefaultType\":\"CC2\",\"costCenterDefaultTitle\":\"成本中心2\",\"costCenterDefaultTitleEn\":\"Cost center No.2\"},{\"costCenterDefaultType\":\"CC3\",\"costCenterDefaultTitle\":\"成本中心3\",\"costCenterDefaultTitleEn\":\"Cost center No.3\"},{\"costCenterDefaultType\":\"CC4\",\"costCenterDefaultTitle\":\"成本中心4\",\"costCenterDefaultTitleEn\":\"Cost center No.4\"},{\"costCenterDefaultType\":\"CC5\",\"costCenterDefaultTitle\":\"成本中心5\",\"costCenterDefaultTitleEn\":\"Cost center No.5\"},{\"costCenterDefaultType\":\"CC6\",\"costCenterDefaultTitle\":\"成本中心6\",\"costCenterDefaultTitleEn\":\"Cost center No.6\"}],\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2024-12-30 18:09:28.813+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", MatchCostCenterResponseType.class)
        costCenterInfoType = Optional.ofNullable(matchCostCenterResponseType.getResult()).orElse(new CostCenterInfoType())
        createOrderResponseType = JsonUtil.fromJson("{\"wsId\":\"f0827949-4ff5-4044-8642-de416ede3446\",\"orderId\":****************,\"orderPaymentInfo\":{\"payType\":\"CASH\",\"orderAmountInfo\":{\"cnyTotalAmount\":5917.45,\"personalPayAmountInfo\":{\"amount\":5917.45,\"currency\":\"CNY\",\"exchangeRateToCNY\":1},\"paymentItemList\":[{\"prepayType\":\"CASH\",\"gatheringType\":\"D\",\"feeType\":\"HotelRoomFee\",\"amountInfo\":{\"amount\":5917.45,\"currency\":\"CNY\",\"exchangeRateToCNY\":1}}]}},\"success\":true,\"supportPaymentBill\":true,\"paymentTransactionId\":\"307985577484533831\",\"orderTotalAmount\":{\"orderAmount\":{\"originAmount\":{\"amount\":5917.45,\"currency\":\"CNY\",\"exchange\":1},\"cNYAmount\":5917.45},\"paymentBillInfo\":{\"payAmount\":5917.45,\"payCNYAmount\":5917.45,\"payCurrency\":\"CNY\",\"payExchange\":1},\"guaranteeAmount\":{\"originGuaranteeAmount\":{\"amount\":5917.45,\"currency\":\"CNY\",\"exchange\":1},\"cNYGuaranteeAmount\":5917.45,\"customGuaranteeAmount\":{\"amount\":5917.45,\"currency\":\"CNY\",\"exchange\":1}}},\"transactionInfo\":{\"transactionId\":\"62e33c2d-80ff-4699-9277-9d96981bed94\",\"subTransactionIdList\":[{\"payType\":\"PERSONAL\",\"subTransactionId\":\"da4b379b-35ab-4edb-8ac9-228e9d2e85ce\"}]},\"responseCode\":20000,\"responseStatus\":{\"timestamp\":\"2024-12-30 18:09:28.743+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", CreateOrderResponseType.class)
        resultNew = mapper.buildSaveOrderCostCenterRequest(orderCreateRequestType, accountInfo, checkAvailInfo, orderCreateToken, createOrderResponseType, costCenterInfoType, approvalTextInfoResponseType, ssoInfoQueryResponseType, resourceToken, null, null)
        // 抹平变量
        resultOld.saveList.get(0).createTimeStr = "2024-12-30 10:44:45"
        resultNew.saveList.get(0).createTimeStr = "2024-12-30 10:44:45"
        then:
        resultNew != null
        // 对比保存的内容需要完全一致
        resultNew == resultOld



        when: // 入住人单维度、人维度都没有成本中心 落一条不带成本中心的空数据 7ebcab8b8f994eb2b21e2137589fae1f 2024-12-30T22:11:46
        queryCheckAvailContextResponseType = JsonUtil.fromJson("{\"baseInfo\":{\"userInfo\":{\"uid\":\"**********\",\"policyUid\":\"\",\"corpId\":\"whzbyh\",\"eid\":\"\",\"usersCityId\":477,\"userCountryCode\":\"CN\"},\"customCurrency\":\"CNY\",\"website\":\"APP\",\"feeType\":\"C\",\"quantity\":1,\"startTime\":\"2024-12-31T00:00:00+08\",\"endTime\":\"2025-01-01T00:00:00+08\",\"language\":\"CN\",\"locale\":\"zh-CN\",\"startTimeUTC\":\"2024-12-30T16:00:00Z\",\"endTimeUTC\":\"2024-12-31T16:00:00Z\",\"pOS\":\"zh-CN\",\"pOSType\":{\"pOS\":\"zh-CN\",\"countryId\":1},\"platform\":\"android\",\"bookingWithPersonalAccount\":false,\"bookingChannel\":\"APP\"},\"hotelInfo\":{\"subHotelId\":********,\"masterHotelId\":********,\"star\":4,\"rStar\":0,\"starLicence\":false,\"customerEval\":0.0,\"hotelGroupId\":5,\"hotelBrandId\":362,\"balancePeriod\":\"GM\",\"oversea\":false,\"tripBusinessHotel\":false,\"cuHotel\":false,\"hasServiceFee\":false,\"localTimeOffsetHours\":0,\"telephone\":\"+86-27-********-0\",\"geographicalInfo\":{\"locationInfo\":{\"id\":428},\"cityInfo\":{\"id\":477,\"name\":{\"textGB\":\"武汉\",\"textEn\":\"Wuhan\"}},\"provinceInfo\":{\"id\":20},\"countryInfo\":{\"id\":1},\"coordinateInfo\":{}},\"address\":{\"textGB\":\"建设大道632号\",\"textEn\":\"No.632 Jianshe Avenue\"},\"hotelName\":{\"textGB\":\"武汉汉口金融中心万象城漫心酒店\",\"textEn\":\"Wuhan Hankou Financial Center MixC Manxin Hotel\"},\"currentCountry\":true,\"timeZoneId\":\"Asia/Shanghai\"},\"roomInfo\":{\"subRoomId\":**********,\"roomName\":{\"textGB\":\"心怡标准大床房-中央空调+MINI吧+65寸电视投屏\",\"textEn\":\"Delightful Double Bed Room\"},\"basicRoomName\":{\"textGB\":\"心怡标准大床房-中央空调+MINI吧+65寸电视投屏\",\"textEn\":\"Delightful Double Bed Room\"},\"masterBasicRoomId\":354678158,\"masterBasicRoomName\":{\"textGB\":\"心怡标准大床房-中央空调+MINI吧+65寸电视投屏\"},\"roomType\":\"C\",\"tmcPriceType\":\"NONE\",\"balanceType\":\"PP\",\"gdsType\":\"HZ\",\"mealType\":4,\"priceSuitPropertyValueId\":1262917,\"ratePlanKey\":{\"checkAvlID\":0,\"ratePlanID\":\"0\"},\"addPrice\":false,\"invoiceType\":\"Special\",\"invoiceLimitType\":\"NotSupported\",\"applyAreaPropertyId\":0,\"maxGuestCountPerRoom\":2,\"maskRoom\":false,\"hourlyRoom\":false,\"corpPrice\":true,\"manualGds\":false,\"joined30MinutesFreeCancel\":false,\"premiumRoom\":false,\"welfareRoom\":false,\"groupMemberShip\":false,\"earnHotelPoints\":true,\"bonusPointInfo\":{\"bonusPointRoom\":true,\"bonusPointCode\":\"HZZSF\",\"pointsMode\":\"SJHMS\"},\"earlyArrivalTime\":\"2024-12-31T14:00:00+08\",\"lastCancelTime\":\"2024-12-31T12:00:00+08\",\"cnyAmount\":449.00,\"originAmountInfo\":{\"amount\":449.00,\"currency\":\"CNY\",\"exchange\":1},\"customAmountInfo\":{\"amount\":449.00,\"currency\":\"CNY\",\"exchange\":1},\"originCostInfo\":{\"amount\":449,\"currency\":\"CNY\",\"exchange\":1},\"originCostBeforeTaxInfo\":{\"amount\":449,\"currency\":\"CNY\",\"exchange\":1},\"roomDiscountInfo\":{\"promotionSwitchOn\":false,\"roomDailyDiscountInfo\":[],\"originPromotionAmount\":{\"price\":0,\"currency\":\"CNY\"},\"customPromotionAmount\":{\"price\":0,\"currency\":\"CNY\"}},\"roomDailyInfo\":[{\"effectDate\":\"2024-12-31T00:00:00+08\",\"cnyAmount\":449.00,\"amount\":449.00,\"customAmount\":449.00,\"afterPromotionCnyAmount\":449.00,\"afterPromotionCustomAmount\":449.00,\"cost\":449,\"costBeforeTax\":449,\"customizedCnyAmount\":0,\"mealNumber\":2,\"holdDeadline\":0,\"afterPromotionCustomAmountExcludeTax\":449.00,\"afterPromotionCnyAmountExcludeTax\":449.00,\"customAmountExcludeTax\":449.00,\"cnyAmountExcludeTax\":449.00}],\"paymentRulesInfo\":{\"paymentGuaranteePoly\":\"TRIP\",\"canTravelMoneyPay\":false,\"forceVccPay\":false,\"canCreditCardPay\":false},\"longRentalInfo\":{\"longRental\":false,\"needInquire\":false},\"holdRoomInfo\":{\"holdRoom\":false},\"agreementGiftsToken\":\"ChAI7PsBEgpIWlFZMTg4Njk4ChAIiPwBEgpIWlFZNTU2ODk3ChAI8/sBEgpIWlFZODczNDgy\",\"extraPayTaxMultiCurrencyAmountInfo\":{\"customExtraPayTaxTotalAmount\":0,\"cnyExtraPayTaxTotalAmount\":0},\"pricedRoom\":false,\"generalizedPremiumRoom\":false,\"customAmountExcludeTaxInfo\":{\"amount\":449.00,\"currency\":\"CNY\",\"exchange\":1},\"supportAnticipation\":false,\"roomRightInfoList\":[],\"invoiceTypeExcludeAddPrice\":\"Special\",\"roomMealInfo\":{\"mealType\":4,\"mealDescList\":[\"2份早餐\"],\"dailyMealInfoList\":[{\"effectDateUTC\":\"2024-12-30T16:00:00Z\",\"mealDescList\":[\"\"]},{\"effectDateUTC\":\"2024-12-31T16:00:00Z\",\"mealDescList\":[\"2份早餐\"]}]},\"saleRoomTags\":[{\"tagCode\":\"XYJ\",\"tagDesc\":\"\",\"configInfoList\":[{\"position\":301,\"priority\":2,\"style\":\"1\"}]}],\"taxDetails\":[],\"pid\":\"AQoGNDQ5LjAwEgY0NDkuMDAieQp3djFfQ0o3Vnl5c1FoN3F2OFFVYUJ6RXlOakk1TVRjaUFraGFLZ0pEUnpJRE5EUTVPZ00wTkRsQ0RRb0RRMDVaRWdORFRsa2FBVEZLSURGbFlqRmtNbVU0T0RBM056UXhObU00WWpCbU1HSmtNamMzWXpjNU5HWTMqA0NOWTINCgNDTlkSA0NOWRoBMTINCgNDTlkSA0NOWRoBMToCCABCBwjoDxAMGB9KBwjpDxABGAFQAVgAYgJIWmgZeAKAAZ7Vyys\\u003d\",\"onlyGroupMemberCanBook\":false},\"bookingRules\":{\"cancelPenalties\":{\"paymentGuaranteePoly\":\"TRIP\",\"cancelDeductDetail\":{\"originDeductionPrice\":{\"price\":449.00,\"currency\":\"CNY\"},\"customDeductionPrice\":{\"price\":449.00,\"currency\":\"CNY\"}},\"guaranteePolicyInfo\":{}},\"authorizationRestriction\":{\"latestAuthorizeTime\":\"2024-12-31T11:00:00+08\"},\"confirmRules\":{\"justifyConfirm\":true,\"forbidNotImConfirm\":false},\"welfareRoomRules\":{},\"limitDepartureDateTime\":{\"departureEnd\":\"2025-01-01T12:00:00+08\",\"departureEndUTC\":\"2025-01-01T04:00:00Z\"},\"certificateInfo\":{\"needCertificate\":false},\"minLOS\":1,\"cancelPolicyInfo\":{\"cancelType\":\"LIMIT\",\"freeCancelPolicySceneType\":\"NOT_SUPPORTED\",\"lastCancelTimeUTC\":\"2024-12-31T04:00:00Z\",\"guaranteePolicyInfo\":{\"guaranteeType\":\"FULL_PREPAY\",\"paymentGuaranteePoly\":\"TRIP\",\"guaranteeReason\":\"NONE\",\"guaranteePriceInfo\":{\"originGuaranteePrice\":{\"price\":449.00,\"currency\":\"CNY\"},\"customGuaranteePrice\":{\"price\":449.00,\"currency\":\"CNY\"}}},\"localLastCancelTime\":\"2024-12-31 12:00:00\"},\"limitArrivalDateTime\":{\"arrivalFromUTC\":\"2024-12-31T06:00:00Z\",\"arrivalEndUTC\":\"2024-12-31T22:00:00Z\",\"defaultArrivalTimeEnd\":false,\"localEarlyArrivalTime\":\"2024-12-31 14:00:00\",\"localLastArrivalTime\":\"2025-01-01 06:00:00\"},\"rPLimitInfo\":{\"minLos\":1,\"maxLos\":0,\"minAdvancedBookingDays\":0,\"maxAdvancedBookingDays\":0},\"billingGuestInfo\":{\"guestsNameLanguages\":[\"zh\",\"en\"]},\"restrictRuleList\":[]},\"bookExtInfo\":{\"featureControlFlagMap\":{\"EnableMemberPoints\":false,\"EnableXMinutesFreeCancel\":false,\"BrandNewCustomer\":true,\"EnableShadowDiscount\":false,\"EnableYXH\":false,\"EnablePrepayDiscount\":false,\"EnableNewGroupMember\":false,\"ForbidMultiNationality\":false,\"NewUserCountryCode\":true,\"EnableCNYTWODecimal\":true,\"ShieldMaskRoom\":true,\"EnableAddPrice\":false,\"IgnoreSpecialCancelPolicyNotMatchingFilter\":false,\"StrictCancellationPolicy\":false,\"IgnoreRemitTax\":false,\"ShieldMultiplePrice\":false,\"Enable30MinuteFreeCancel\":false,\"EnablePromotionSubTag\":true,\"EnableHotelNewCustomer\":false,\"DisableIPBlock\":false,\"EnableDirectRate\":false,\"EnableOutputPackageRoom\":false,\"ShieldMaskNakedSale\":false,\"EnableMobileRate\":true,\"EnableMatchPolicyWithExtraPayTax\":false}},\"userRightsInfo\":{\"userChosenCouponInfo\":[],\"hotelNewCustomer\":true,\"userSelectedCountryCode\":\"CN\"},\"reservationToken\":\"AQgBEgoyMDI0LTEyLTMxGhcKAUMSAlBQGgRUUklQIgJIWkiHuq/xBSICCAAqBwoFemgtQ04\\u003d\",\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2024-12-30 22:11:44.913+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", QueryCheckAvailContextResponseType.class)
        checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsId")))
                        .build().getCheckAvailContextInfo()
        orderCreateRequestType = JsonUtil.fromJson("{\"integrationSoaRequestType\":{\"userInfo\":{\"userId\":\"**********\",\"corpId\":\"whzbyh\",\"groupId\":\"\",\"pos\":\"CHINA\"},\"token\":\"E3DA0A82832F811159F0F993C13CF567FD1B46F7FCD86762DE235EA6ADDE109C\",\"language\":\"zh-CN\",\"requestId\":\"7ebcab8b8f994eb2b21e2137589fae1f\",\"sourceFrom\":\"H5\",\"transactionID\":\"TID.SEARCHHOTEL.1.47C1CF3DBE0D4FE8A78FD9AD1D2DCD78\",\"gatewayHost\":\"ct.ctrip.com\",\"timezoneOffsetMinutesNew\":\"480\",\"logIndices\":[{\"key\":\"groupId\",\"value\":\"\"},{\"key\":\"timezoneOffsetMinutesNew\",\"value\":\"480\"},{\"key\":\"gatewayHost\",\"value\":\"ct.ctrip.com\"},{\"key\":\"pvid\",\"value\":\"17\"},{\"key\":\"subChannel\",\"value\":\"\"},{\"key\":\"CID\",\"value\":\"09031094213294129647\"},{\"key\":\"EID\"},{\"key\":\"CIP\",\"value\":\"**************\"},{\"key\":\"miceToken\",\"value\":\"\"},{\"key\":\"groupName\",\"value\":\"\"},{\"key\":\"gatewayIdc\",\"value\":\"SHARB\"},{\"key\":\"traceID\",\"value\":\"\"},{\"key\":\"A\",\"value\":\"ordercheck\"},{\"key\":\"CorpID\",\"value\":\"whzbyh\"},{\"key\":\"C\",\"value\":\"corpbookinghotelbookservice\"},{\"key\":\"M\",\"value\":\"**********\"},{\"key\":\"sid\",\"value\":\"1\"},{\"key\":\"S\",\"value\":\"94c5ae209f824b33a7a3ce1660430f34d8ffe614f782\"},{\"key\":\"T\",\"value\":\"E3DA0A82832F811159F0F993C13CF567FD1B46F7FCD86762DE235EA6ADDE109C\"},{\"key\":\"pos\",\"value\":\"CHINA\"},{\"key\":\"GE\",\"value\":\"1735567903153\"},{\"key\":\"sourceFrom\",\"value\":\"H5\"},{\"key\":\"corpName\",\"value\":\"武汉众邦银行股份有限公司\"},{\"key\":\"RID\",\"value\":\"7ebcab8b8f994eb2b21e2137589fae1f\"},{\"key\":\"TID\",\"value\":\"TID.SEARCHHOTEL.1.47C1CF3DBE0D4FE8A78FD9AD1D2DCD78\"},{\"key\":\"VID\",\"value\":\"1735567381801.4b3cSQciS5zL\"}],\"ticket\":\"UCgrkOQBHDr5o8p/QZT+xv6VVkSf9FVx5vCINqCmKbco9RRpHfw2XTb7EDSiBXodpzuRJVNRKit97VMxig7Gl044pLwxT2s60LjJLA0CJs4\\u003d\",\"transferInfo\":[{\"key\":\"iframe\",\"value\":\"F\"},{\"key\":\"channel\",\"value\":\"sso\"}]},\"hotelBookPassengerInputs\":[{\"hotelPassengerInput\":{\"uid\":\"**********\",\"employee\":\"T\",\"external\":\"F\",\"employeeId\":\"lishan\"},\"passengerBasicInfo\":{\"gender\":\"U\",\"birth\":\"1984-07-21\"},\"name\":\"李姗\",\"isRegistrant\":\"F\",\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"13476001366\",\"transferPhoneNo\":\"13476001366\"},\"nationalityInfo\":{\"nationalityCode\":\"CN\"}}],\"hotelInvoiceInfos\":[{\"hotelInvoiceType\":\"ROOM\",\"invoiceInfo\":{\"invoiceType\":\"DInvoice\",\"invoiceTitleType\":\"C\",\"invoiceTitle\":\"武汉众邦银行股份有限公司\",\"taxNumber\":\"91420100MA4KTMYN5A\",\"invoiceCompanyInfo\":{},\"emailInfo\":{\"email\":\"<EMAIL>\",\"transferEmail\":\"<EMAIL>\"}},\"hotelRemark\":\"F\",\"checkInAndOutTime\":\"F\"}],\"cityInput\":{\"cityId\":477},\"hotelContactorInfo\":{\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"13476001366\",\"transferPhoneNo\":\"13476001366\"},\"emailInfo\":{\"email\":\"<EMAIL>\",\"transferEmail\":\"<EMAIL>\"}},\"miceInput\":{},\"hotelBookInput\":{\"hotelDateRangeInfo\":{\"checkIn\":\"2024-12-31\",\"checkOut\":\"2025-01-01\"},\"roomQuantity\":1},\"membershipInfo\":{\"membershipUid\":\"**********\",\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"13476001366\",\"transferPhoneNo\":\"13476001366\"}},\"bookModeInfo\":{\"bookingType\":\"NONE\"},\"resourceTokenInfo\":{\"resourceToken\":\"{\\\"hotelResourceToken\\\":{\\\"masterHotelId\\\":********,\\\"hotelId\\\":********,\\\"searchHotelTraceId\\\":\\\"1eb1d2e88077416c8b0f0bd277c794f7\\\",\\\"hid\\\":\\\"ASIGNDQ5LjAwQgFGSgYIABAAGABSbQpndjFfQ0puVnl5c1FudFhMS3hvQ1EwY2lBelEwT1NvRE5EUTVNZzBLQTBOT1dSSURRMDVaR2dFeE9pQXhaV0l4WkRKbE9EZ3dOemMwTVRaak9HSXdaakJpWkRJM04yTTNPVFJtTnc9PRAAGAFaA0NOWWINCgNDTlkSA0NOWRoBMWINCgNDTlkSA0NOWRoBMWiDBHIGNTAwLjAwegQwLjAwggEgMWViMWQyZTg4MDc3NDE2YzhiMGYwYmQyNzdjNzk0ZjeKAQoxNTc5OTMyOTM1mgEkNTc4ZWZiMWItNzkzNy00Y2VkLWJlOTUtNzZjNDA0YzRiMDU4ogECCAA\\\\u003d\\\",\\\"hotelType\\\":\\\"C\\\",\\\"bonusPoint\\\":\\\"T\\\",\\\"hotelGeoInfoResourceToken\\\":{\\\"cityId\\\":477,\\\"locationId\\\":428}},\\\"roomResourceToken\\\":{\\\"roomId\\\":**********,\\\"baseRoomId\\\":354678158,\\\"ratePlanTraceLogId\\\":\\\"1ddc7bdf27744a2c8a37c5aa0b406473_\\\",\\\"allRoomType\\\":\\\"EXIST_C\\\",\\\"bedName\\\":\\\"1张1.8米大床\\\",\\\"windowName\\\":\\\"有窗\\\",\\\"breakfastInfo\\\":\\\"2份早餐\\\",\\\"confirmType\\\":\\\"IMMEDIATE\\\",\\\"roomType\\\":\\\"C\\\",\\\"balanceType\\\":\\\"PP\\\",\\\"tmcPrice\\\":false,\\\"adult\\\":2,\\\"pId\\\":\\\"AQoGNDQ5LjAwEgY0NDkuMDAieQp3djFfQ0o3Vnl5c1FoN3F2OFFVYUJ6RXlOakk1TVRjaUFraGFLZ0pEUnpJRE5EUTVPZ00wTkRsQ0RRb0RRMDVaRWdORFRsa2FBVEZLSURGbFlqRmtNbVU0T0RBM056UXhObU00WWpCbU1HSmtNamMzWXpjNU5HWTMqA0NOWTINCgNDTlkSA0NOWRoBMTINCgNDTlkSA0NOWRoBMToCCABCBwjoDxAMGB9KBwjpDxABGAFQAVgAYgJIWmgZeAKAAZ7Vyys\\\\u003d\\\",\\\"amadeus\\\":false,\\\"supportAccommodation\\\":\\\"F\\\",\\\"roomOriginCurrency\\\":\\\"CNY\\\",\\\"roomScenarioEnum\\\":\\\"ROOM_LIST\\\",\\\"ratePlanRequestToken\\\":\\\"ASi1L/0AUMUVALYvqT0QTWUIEqEcISBKkw4BEQQbERBSohgcWJCQoUmtIzgylQBZxAK2Fg6Nv5rfB5C5EU5l82du8iIvIvqmZv8HmgCYAJcAE5AkgrO3wnnrVvS0F2lLSxjfJH26+Q2NLfXee5PTynjsRi28qF5lrqrc0bioNJnSK8NbiNPa36Vim1ftf4KQsQZ9bJZMzZSUUjG0So4Njz7h6NA18J6BQ35Hv/B7r9D7hH732qoiVhWhXNVFgaxJ5LIi2FVdVAeD8Y4TBBCB/BHYFOABvNExwRoCLgzDKBkFy6yLcgAJMQIDRZL5YzAvWkD1Zd6Zz+JdxZFlE6vZMgnhUJf+kHeDIplml4yYrtTM9UJrh6GxwTQD5sRQl5HahRkvIRcy+pgLftBRx429iw60qrO8wvEGSV4fMGPavDH2xjCGbIFN6NGSKcHpnc0gTL2BVkLfGn5kTq1kn9hRYOuyrbbnrtzW2G50yBS716ekQQoprSuWTp1wIC1EN59XryKLbn1zb4vWNCrTtI3g4TtV0U8WO6kilhWZXNXFohkOZTWP4KF1qqKfIFSVbGPvamnKj8RGLxJdQCATE3F2q5lMH8hFslLquT3Nuvxy+GYfDltKHMKil7MZPcmFhLwgoRgYZ86AS8/RvLDWgCqtg4wyWrTMlB9nwo7H1XH1+TCpFG6taGSZGWKF+6fccu5m8yeoJvbc1zZ7ZZ4PbXvmdkAnwTsJ3j/w7oF3Drxn4N0C7xR4n8BvXnOYS+AbjuEet/CKN+AKOAWfuLoIR4kJRwfhuczJaUr20yoevlMM+ul/XSmymPOfrMqRBR6+Uwf6aSzLk0XPMsN+GoHH1ikD/TSW5cluJDeO5edO9tMMPHynEvST66F/fXOkjq9t+ruk9nTjlpTgoXVqQT+RZlf+2AEFAFYoQNRAyAZgkhNmGrFCBg\\\\u003d\\\\u003d\\\",\\\"ratePlanRoomToken\\\":\\\"ASi1L/0AUHkAAAoBQxAAGIe6r/EFIgJQUA\\\\u003d\\\\u003d\\\",\\\"roomUniqueKey\\\":\\\"V1-ASi1L/0AULkAAAiHuq/xBRIBQxoETk9ORSICUFA4xYpN\\\",\\\"adultNumber\\\":2,\\\"windowType\\\":2},\\\"reservationResourceToken\\\":{\\\"wsId\\\":\\\"a8b9994f-69e3-4a8b-9a47-c3713db9c334\\\",\\\"policyToken\\\":\\\"v1KLUv/QBQxQkAJtM+JSCJqgA4CepiYwnqU62f5PxK6fE4eq0Fm+gEBggg5cIYBBAECgIzADUANQCfLaH27VT2WKAU7g4lq9L3dsdxHuhxIKa5O4zXvbJtZHOB8YzV27IALMvj3i/WCePqDAGOjCeuFgvoSzLEvSOVqUW/OKndaHjlm/HtkAgBRR01EXRglAgurDDjOEoDG0jhBjo8DOPcBbPnpVXCdgRehZRRIXlCq2Xxm5R3utsrDmQvSjrZOA0qSgvs1TdCPcsMSwlmUwcSShWTH8YBDQlHvD16++Ht0BHcZU+4OqYEJfsupNXCZgJrO6WLWjKygI3V4zYmv4wnLDISXaeWDw4WADobp2OH07XDf3PxzF2IKoSpOWioQuoCpgjqUEWp6eWfxfos8wWqxKuEI4xaqgDODSDldWYOK5tN\\\"},\\\"bookInitResourceToken\\\":{\\\"aggBookPriceResourceToken\\\":{\\\"currency\\\":\\\"CNY\\\",\\\"accountPaymentAmount\\\":0,\\\"individualPaymentAmount\\\":449.00,\\\"mixPayAccountExtraAmount\\\":0},\\\"roomPriceResourceToken\\\":{},\\\"cancelTip\\\":\\\"2024年12月31日12:00前可免费取消。若未入住或过时取消将收取您全额房费¥449（若用优惠券则以券后支付价为准），修改结果以酒店回复为准。携程会根据您的付款方式扣除房费，订单需等酒店或供应商确认后生效，如订单不确认将全额退款至您的付款账户，订单确认结果以携程短信或邮件通知为准。\\\"}}\"},\"corpPayInfo\":{\"corpPayType\":\"public\"},\"userActionInfo\":{\"userActionToken\":\"PRD-2-f78ad86d-6e88-480b-aee5-599a9b77b6d2-********-4.1.15-WEB\"},\"rcInfos\":[],\"bookUrlInfos\":[{\"urlType\":\"SUCCESS\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayState/PayState?site\\u003d2\\u0026LimitStandard\\u003dfalse\\u0026needFinishResult\\u003dtrue\\u0026\"},{\"urlType\":\"S_BACK\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayState/PayState?site\\u003d2\\u0026LimitStandard\\u003dfalse\\u0026needFinishResult\\u003dtrue\\u0026\"},{\"urlType\":\"E_BACK\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayMent/PayMent\"},{\"urlType\":\"FROM_URL\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayMent/PayMent?leomacmd://navi?%7b%22backward%22%3atrue%2c%22navi%22%3a0%2c%22now%22%3atrue%2c%22platform%22%3a%7b%7d%7d\"},{\"urlType\":\"S_BACK_MICE\",\"urlValue\":\"https://ct.ctrip.com/mice-activities/welfareTourism\"},{\"urlType\":\"FROM_URL_ORDER_DETAIL\",\"urlValue\":\"https://ct.ctrip.com/webapp/hotel/process/detail?orderId\\u003d{0}\\u0026language\\u003d{1}\\u0026from\\u003dfinish\"},{\"urlType\":\"OFFLINE_RETURN_URL\",\"urlValue\":\"http://corpint.ct.ctripcorp.com/hotel-online/offlineOrder/success\"},{\"urlType\":\"ORDER_DETAIL_URL\",\"urlValue\":\"https://ct.ctrip.com/m/Detail/Hotel/{0}\"}],\"paymentInfoInput\":{\"canWechatPayByOthers\":\"F\",\"clientType\":\"H5\"},\"clientInfo\":{\"clientType\":\"H5\",\"rmsToken\":\"fp\\u003d\\u0026vid\\u003d1735567381801.4b3cSQciS5zL\\u0026pageId\\u003d10650045599\\u0026r\\u003dd7fffc40f4a340cd9a0531a01f89879f\\u0026ip\\u003d**************\\u0026rg\\u003dfin\\u0026screen\\u003d354x792\\u0026tz\\u003d+8\\u0026blang\\u003dzh-CN\\u0026oslang\\u003dzh-CN\\u0026ua\\u003dMozilla%2F5.0%20(Linux%3B%20Android%2015%3B%20FLC-AN00%20Build%2FHONORFLC-AN00%3B%20wv)%20AppleWebKit%2F537.36%20(KHTML%2C%20like%20Gecko)%20Version%2F4.0%20Chrome%2F129.0.6668.70%20Mobile%20Safari%2F537.36%20E-Mobile7%2F7.60.20231109%20Language%2Fzh%20Qiyuesuo%2FphysicalSDK\\u0026v\\u003dm17\\u0026bl\\u003dfalse\\u0026clientid\\u003d\",\"secondaryChannel\":\"Android-new\"},\"offlineInfo\":{\"forciblyAssure\":\"F\",\"especialAuth\":\"F\",\"autoConfirm\":\"F\",\"exigentOrder\":\"F\"},\"roomInfoInput\":{},\"sendInfo\":{\"sendSMS\":\"F\",\"sendEmail\":\"F\"},\"ssoInput\":{\"ssoKey\":\"64674c48f895472e876c309b599491e6\"},\"flashStayInput\":{\"flashPlatFrom\":\"H5\"},\"hotelPayTypeInput\":[{\"payCode\":\"ROOM\",\"payType\":\"SELF_PAY\"}],\"strategyInfos\":[{\"strategyKey\":\"BOOKING_SCENARIO\",\"strategyValue\":\"HOTEL_LIST\"},{\"strategyKey\":\"BOOKING_WITH_PERSONAL_ACCOUNT\",\"strategyValue\":\"F\"}],\"costCenterInfo\":{\"costCenterJsonString\":\"{\\\"items\\\":{\\\"fdefault\\\":{}}}\"},\"useNewOrderCreate\":\"F\"}", OrderCreateRequestType.class)
        orderCreateRequestType.cityInput.cityId = 22249
        resultOld = JsonUtil.fromJson("{\"saveList\":[{\"bizType\":4,\"orderID\":****************,\"uid\":\"**********\",\"pid\":\"**********\",\"corpId\":\"whzbyh\",\"eid\":\"**********\",\"costCenterDesc\":\"\",\"costCenterDescEn\":\"\",\"frequentlyUsedCostCenter\":0,\"createTimeStr\":\"2024-12-30 22:11:46\"}]}", SaveOrderCostCenterRequestType.class)
        matchCostCenterResponseType = JsonUtil.fromJson("{\"result\":{\"costCenterDescription\":\"\",\"costCenterDescriptionEn\":\"\",\"costCenterExtlist\":[{\"costCenterExtType\":\"TP\",\"costCenterExtTitle\":\"出行目的\",\"costCenterExtTitleEn\":\"JounaryReason\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"PN\",\"costCenterExtTitle\":\"项目号\",\"costCenterExtTitleEn\":\"Project\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"FT\",\"costCenterExtTitle\":\"机票关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of flight\",\"required\":false,\"enabled\":true,\"selectHidden\":false},{\"costCenterExtType\":\"HT\",\"costCenterExtTitle\":\"酒店关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of hotel\",\"required\":false,\"enabled\":true,\"selectHidden\":false},{\"costCenterExtType\":\"TT\",\"costCenterExtTitle\":\"火车票关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of train\",\"required\":false,\"enabled\":true,\"selectHidden\":false},{\"costCenterExtType\":\"CT\",\"costCenterExtTitle\":\"用车关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of car\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"BT\",\"costCenterExtTitle\":\"汽车票关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No.\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"D1\",\"costCenterExtTitle\":\"自定义字段1\",\"costCenterExtTitleEn\":\"Self-defined title No.1\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"D2\",\"costCenterExtTitle\":\"自定义字段2\",\"costCenterExtTitleEn\":\"Self-defined title No.2\",\"required\":false,\"enabled\":false,\"selectHidden\":false}],\"frequentlyUsedCostCenter\":0},\"costCenterDefaultTitles\":[{\"costCenterDefaultType\":\"TP\",\"costCenterDefaultTitle\":\"出行目的\",\"costCenterDefaultTitleEn\":\"JounaryReason\"},{\"costCenterDefaultType\":\"PN\",\"costCenterDefaultTitle\":\"项目号\",\"costCenterDefaultTitleEn\":\"Project\"},{\"costCenterDefaultType\":\"FT\",\"costCenterDefaultTitle\":\"机票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of flight\"},{\"costCenterDefaultType\":\"HT\",\"costCenterDefaultTitle\":\"酒店关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of hotel\"},{\"costCenterDefaultType\":\"TT\",\"costCenterDefaultTitle\":\"火车票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of train\"},{\"costCenterDefaultType\":\"CT\",\"costCenterDefaultTitle\":\"用车关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of car\"},{\"costCenterDefaultType\":\"BT\",\"costCenterDefaultTitle\":\"汽车票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No.\"},{\"costCenterDefaultType\":\"D1\",\"costCenterDefaultTitle\":\"自定义字段1\",\"costCenterDefaultTitleEn\":\"Self-defined title No.1\"},{\"costCenterDefaultType\":\"D2\",\"costCenterDefaultTitle\":\"自定义字段2\",\"costCenterDefaultTitleEn\":\"Self-defined title No.2\"},{\"costCenterDefaultType\":\"CC1\",\"costCenterDefaultTitle\":\"成本中心1\",\"costCenterDefaultTitleEn\":\"Cost center No.1\"},{\"costCenterDefaultType\":\"CC2\",\"costCenterDefaultTitle\":\"成本中心2\",\"costCenterDefaultTitleEn\":\"Cost center No.2\"},{\"costCenterDefaultType\":\"CC3\",\"costCenterDefaultTitle\":\"成本中心3\",\"costCenterDefaultTitleEn\":\"Cost center No.3\"},{\"costCenterDefaultType\":\"CC4\",\"costCenterDefaultTitle\":\"成本中心4\",\"costCenterDefaultTitleEn\":\"Cost center No.4\"},{\"costCenterDefaultType\":\"CC5\",\"costCenterDefaultTitle\":\"成本中心5\",\"costCenterDefaultTitleEn\":\"Cost center No.5\"},{\"costCenterDefaultType\":\"CC6\",\"costCenterDefaultTitle\":\"成本中心6\",\"costCenterDefaultTitleEn\":\"Cost center No.6\"}],\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2024-12-30 22:11:44.945+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", MatchCostCenterResponseType.class)
        costCenterInfoType = Optional.ofNullable(matchCostCenterResponseType.getResult()).orElse(new CostCenterInfoType())
        createOrderResponseType = JsonUtil.fromJson("{\"wsId\":\"a8b9994f-69e3-4a8b-9a47-c3713db9c334\",\"orderId\":****************,\"orderPaymentInfo\":{\"payType\":\"SELF_PAY\",\"orderAmountInfo\":{\"cnyTotalAmount\":449.00,\"personalPayAmountInfo\":{\"amount\":449.00,\"currency\":\"CNY\",\"exchangeRateToCNY\":1},\"paymentItemList\":[{\"prepayType\":\"SELF_PAY\",\"gatheringType\":\"D\",\"feeType\":\"HotelRoomFee\",\"amountInfo\":{\"amount\":449.00,\"currency\":\"CNY\",\"exchangeRateToCNY\":1}}]}},\"success\":true,\"supportPaymentBill\":true,\"paymentTransactionId\":\"308016031736594557\",\"orderTotalAmount\":{\"orderAmount\":{\"originAmount\":{\"amount\":449.00,\"currency\":\"CNY\",\"exchange\":1},\"cNYAmount\":449.00},\"paymentBillInfo\":{\"payAmount\":449.00,\"payCNYAmount\":449.00,\"payCurrency\":\"CNY\",\"payExchange\":1}},\"transactionInfo\":{\"transactionId\":\"994526a8-df6f-4e15-8556-13840de390f3\",\"subTransactionIdList\":[{\"payType\":\"PERSONAL\",\"subTransactionId\":\"b6310280-a64d-4760-a1d7-b2f03723e6ce\"}]},\"responseCode\":20000,\"responseStatus\":{\"timestamp\":\"2024-12-30 22:11:46.662+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", CreateOrderResponseType.class)
        resultNew = mapper.buildSaveOrderCostCenterRequest(orderCreateRequestType, accountInfo, checkAvailInfo, orderCreateToken, createOrderResponseType, costCenterInfoType, approvalTextInfoResponseType, ssoInfoQueryResponseType, resourceToken, null, null)
        // 抹平变量
        resultOld.saveList.get(0).createTimeStr = "2024-12-30 10:44:45"
        resultNew.saveList.get(0).createTimeStr = "2024-12-30 10:44:45"
        then:
        resultNew != null
        // 对比保存的内容需要完全一致
        resultNew == resultOld



        when: // 出差申請緊急預定 631a4880528b46969b1a9c07f6526669 2024-12-31 09:53:50
        GeneralSearchAccountInfoResponseType generalSearchAccountInfoResponseType = JsonUtil.fromJson("{\"uid\":\"**********\",\"rid\":\"631a4880528b46969b1a9c07f6526669\",\"results\":{\"currency\":\"CNY\",\"AuthDelayM\":\"F\",\"AuthDelayC\":\"F\",\"OverSeaAuthDelayM\":\"F\",\"OverSeaAuthDelayC\":\"F\",\"RepeatOrderCheck\":\"T\",\"overSeaNeedAuditHY\":\"F\",\"overSeaNeedAuditXY\":\"F\",\"needAuditHY\":\"F\",\"needAuditXY\":\"F\",\"BillType\":\"A\",\"CorpGuaranteeFlag\":\"F\",\"IsHtlPrintTicketAfterConfirm\":\"T\",\"OverStandCanMixPay\":\"F\",\"AgreementHtlOverStandCanMixPay\":\"F\",\"IsShieldPayTypePersonal_HotelI\":\"T\",\"IsShieldPayTypePersonal_HotelN\":\"T\",\"HotelTravelSendType\":\"A\",\"AgreementHotelTravelSendType\":\"A\",\"HotelVATType\":\"C\",\"AgreementHotelVATType\":\"C\",\"accountType\":\"D\",\"hIsMonthlyC\":\"T\",\"hIsMonthly\":\"T\",\"overSeaHIsMonthlyC\":\"F\",\"overSeaHIsMonthly\":\"T\",\"isShieldPayTypePersonal_AgreementHotelI\":\"F\",\"isShieldPayTypePersonal_AgreementHotelN\":\"F\",\"htlPrepayHTIsBlockHY\":\"F\",\"nCPTaxAgreementHotelInvoiceType_Cash\":\"A,B\",\"agreementHotelInvoiceType_Cash\":\"A\",\"nCPTaxOSHotelInvoiceType\":\"B\",\"hotelInvoiceTypeM\":\"B\",\"nCPTaxHotelInvoiceType_Cash\":\"A,B\",\"hotelInvoiceType_Cash\":\"A,B,C\",\"hotelVATTypeAddPrice\":\"\",\"oSIsShowCanPayRoomTypeByCorp\":\"F\",\"isShowCanPayRoomTypeByCorp\":\"F\",\"privateOrderFlag\":\"T\",\"oSIsDefaultSelectHotelAgreement\":\"F\",\"isDefaultSelectHotelAgreement\":\"F\",\"hotelPro\":\"A,B,ZJTH,G,D,E,F\",\"hotelBookingFilterByPolicy\":\"F\",\"htlMixPayWithRoomPriceUp\":\"F\",\"htlMixPayWithRoomPriceUpProp\":\"0.25\",\"canMixPay\":\"T\",\"agreementHtlMixPay\":\"T\",\"isNeedDomAgreementRC\":\"F\",\"BookPolicy\":\"C\",\"HotelBookPolicy\":\"C\",\"IsCostCenRequire\":\"F\",\"hotelAgreementSelectAllowLayout\":\"DefaultNotSelect\",\"IsDomLowPriceRCXY\":\"T\",\"IsNeedIntLowPriceRC\":\"T\",\"IsNeedDomLowPriceRC\":\"T\",\"HotelRoomLayoutRule\":\"D\",\"PolicyUserCTLScope\":\"\",\"hotelTravelStandControlModel\":\"C\",\"isChkaheadapproveHotel\":\"T\",\"isChkaheadapproveHotelI\":\"T\",\"VerifyAuthorization\":\"T\",\"ChkaheadapproveHotelI\":\"3\",\"ChkaheadapproveHotel\":\"3\",\"AllowAlipayWechatPayControl\":\"TrainTicketAllowAlipayPay,TrainAllowWeChatAgentPay,FlightAllowWechatPay,HotelAllowWechatPay,FltOnlBank,CarAllowWechatPay,HotelAllowWeChatAgentPay,CarAllowAlipayPay,TrainTicketAllowWechatPay,FlightAllowWeChatAgentPay,FlightAllowAlipayPay,HotelAllowAlipayPay\",\"BookPolicyTravelMode\":\"N\",\"htlInvoiceExpressNeedCharge\":\"T\",\"HtlFeeAllocationType\":\"A\",\"HotelPassengersControl\":\"O\",\"HotelPolicyScope\":\"A,B\",\"IsMustHotelPolicy\":\"T\",\"HotelLiveTogetherMF\":\"F\",\"corpMemberGradeIsOpen\":\"H\",\"ISHtlShareControlEdit\":\"F\",\"PayTypeHotle\":\"D\",\"PayTypeFlight\":\"D\",\"intlHotePro\":\"A\",\"AgreeHtlInvoiceExpressNeedCharge\":\"T\",\"AgreeHtlInvoiceExpressNum\":\"10\",\"IsHtlHideDayUnitPrice\":\"F\",\"PLCSMore\":\"T\",\"AllowCrossPromotePro\":\"A,C\",\"IsNeedHotelPunchCard\":\"F\",\"HtlStandardSharePercentage\":\"1\",\"isNeedIntAgreementRC\":\"F\",\"NeedAgreeBeforeDaysRC\":\"F\",\"NeedAgreeBeforeDaysRCNum\":\"0\",\"NeedAgreeIntBeforeDaysRC\":\"F\",\"NeedAgreeIntBeforeDaysRCNum\":\"0\",\"NeedBeforeDaysRC\":\"F\",\"NeedBeforeDaysRCDaysNum\":\"0\",\"NeedIntBeforeDaysRC\":\"F\",\"NeedIntBeforeDaysRCNum\":\"0\",\"NCPTaxHotelTravelMSendType\":\"A\",\"NCPTaxHotelTravelASendType\":\"A\",\"NCPTaxHotelVATType\":\"C\",\"hotelInvoiceTypeC\":\"B\",\"OSHotelInvoiceType\":\"B\",\"canUseAccntWithoutWorkTime\":\"T\",\"LCSSChinese\":\"T\",\"LCSEnglish\":\"T\",\"IsShowHighPrice\":\"T\",\"PLCSHotle\":\"T\",\"PLCSFlight\":\"T\",\"HConfirmVerbal1\":\"F\",\"HConfirmVerbal2\":\"F\",\"OverSeaHConfirmVerbal1\":\"F\",\"OverSeaHConfirmVerbal2\":\"F\",\"OverSeaHotelConfirmTypeNew\":\"C11;C6;C4\",\"HotelConfirmTypeNew\":\"C11;C6;C4\",\"HotelRecommendation\":\"F\",\"IsSignGDSSuppleAgreement\":\"F\",\"isHtlRecommend\":\"F\",\"htlRecommendTagName\":\"公司推荐\",\"BillControlMode\":\"A\",\"checkInRequiredApproval\":\"\",\"IsChkaheadapproveQueryResource\":\"F\",\"accountName\":\"562332_主账户1\",\"isHotelBookPrompt\":\"T\",\"LCSTChinese\":\"T\",\"accountID\":\"554645\",\"CompanyName\":\"伟视得电子贸易（上海）有限公司\",\"fundAccount.status\":\"A\",\"IsHtlReleateJourneyNoInput\":\"F\",\"CorpCorporation\":\"562332\",\"ComparePrice\":\"T\",\"HotelBookCall\":\"\",\"CorporationFlag\":\"B\",\"CompanyType\":\"8\",\"fundSubAccount.status\":\"A\",\"subAccountID\":\"802686\",\"hotelPunchCardRange\":\"\",\"TravelerUidChangeTicketCtl\":\"C,D,E\",\"HtlMixPayRatio\":\"\",\"serviceVersion\":\"V2\",\"WelfareResources\":\"\",\"HtlFeeShareControl\":\"F\",\"RepeatOrderControlCorp\":\"A\",\"RepeatBookingReason\":\"F\",\"oSHotelAgreementSelectAllowLayout\":\"DefaultNotSelect\",\"OSHotelRoomLayoutRule\":\"D\",\"PolicyUserCTLScopeH\":\"F\",\"HtlOrderFeeDistribution\":\"\",\"NationPunchRangeLimit\":\"F\",\"IntlPunchRangeLimit\":\"F\",\"NationPunchRangeLimitKM\":\"5\",\"IntlPunchRangeLimitKM\":\"5\",\"NationPunchTimeLimit\":\"Period\",\"IntlPunchTimeLimit\":\"Period\",\"OfflPLCSHotle\":\"T\",\"OfflPayTypeHotle\":\"D\",\"HotelListControl\":\"B\",\"IsNWhiteHotelOpen\":\"F\",\"PunchRangeLimit\":\"F\",\"PunchTimeLimit\":\"Period\",\"IsMustFillEmail\":\"F\",\"hzMemberInterests\":\"F\",\"basicMemberBenefits\":\"T\",\"showMemberLevel\":\"T\",\"showCorpMemberLevel\":\"T\",\"hotelFlashService\":\"F\",\"IsRecommendRoomTypeByNoResult\":\"T\",\"H_HMT_IsMonthly\":\"T\",\"IsCheckInMustSameTripApprove\":\"\",\"isAutoChooseApproveFormHtlI\":\"F\",\"isAutoChooseApproveFormHtlN\":\"F\",\"isSameJourneyCheckHtlI\":\"F\",\"isSameJourneyCheckHtlN\":\"F\",\"IsShieldPayTypePersonal_AgreementHotelN_HMT\":\"F\",\"IsShieldPayTypePersonal_HotelN_HMT\":\"T\",\"H_HMT_IsMonthly_C\":\"F\",\"docChooseDimensionHtlN\":\"O\",\"docChooseDimensionHtlI\":\"O\",\"Corp_Corporation\":\"562332\"},\"retCode\":0,\"responseStatus\":{\"timestamp\":\"2024-12-31 09:53:50.601+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", GeneralSearchAccountInfoResponseType.class)
        queryCheckAvailContextResponseType = JsonUtil.fromJson("{\"baseInfo\":{\"userInfo\":{\"uid\":\"**********\",\"policyUid\":\"\",\"corpId\":\"562332\",\"eid\":\"\",\"usersCityId\":30,\"userCountryCode\":\"CN\"},\"customCurrency\":\"CNY\",\"website\":\"Online\",\"feeType\":\"C\",\"quantity\":1,\"startTime\":\"2025-01-10T00:00:00+08\",\"endTime\":\"2025-01-11T00:00:00+08\",\"language\":\"CN\",\"locale\":\"zh-CN\",\"startTimeUTC\":\"2025-01-09T16:00:00Z\",\"endTimeUTC\":\"2025-01-10T16:00:00Z\",\"pOS\":\"zh-CN\",\"pOSType\":{\"pOS\":\"zh-CN\",\"countryId\":1},\"platform\":\"online\",\"bookingWithPersonalAccount\":false},\"hotelInfo\":{\"subHotelId\":*********,\"masterHotelId\":4690345,\"star\":4,\"rStar\":0,\"starLicence\":false,\"customerEval\":0.0,\"hotelGroupId\":12,\"oversea\":false,\"tripBusinessHotel\":false,\"cuHotel\":false,\"hasServiceFee\":false,\"localTimeOffsetHours\":0,\"telephone\":\"+86-755-********\",\"geographicalInfo\":{\"locationInfo\":{\"id\":214},\"cityInfo\":{\"id\":30,\"name\":{\"textGB\":\"深圳\",\"textEn\":\"Shenzhen\"}},\"provinceInfo\":{\"id\":23},\"countryInfo\":{\"id\":1},\"coordinateInfo\":{\"googleLat\":22.623653,\"googleLng\":113.815809,\"gdLat\":22.623653,\"gdLng\":113.815809,\"bdLat\":22.629319,\"bdLng\":113.822372}},\"address\":{\"textGB\":\"宝安国际机场地面交通中心14号门3楼\",\"textEn\":\"3F, Gate 14, Ground Transportation Center, Bao\\u0027an International Airport\"},\"hotelName\":{\"textGB\":\"深圳机场凯悦嘉轩酒店\",\"textEn\":\"Hyatt Place Shenzhen Airport\"},\"currentCountry\":true,\"timeZoneId\":\"Asia/Shanghai\"},\"roomInfo\":{\"subRoomId\":1344025565,\"roomName\":{\"textGB\":\"凯悦嘉轩大床间\",\"textEn\":\"Hyatt Place King (No Sofa Bed)\"},\"basicRoomName\":{\"textGB\":\"凯悦嘉轩大床间\",\"textEn\":\"Hyatt Place King (No Sofa Bed)\"},\"masterBasicRoomId\":16773346,\"masterBasicRoomName\":{\"textGB\":\"凯悦嘉轩大床间\"},\"roomType\":\"M\",\"tmcPriceType\":\"NONE\",\"balanceType\":\"PP\",\"mealType\":4,\"addPrice\":false,\"invoiceType\":\"Special\",\"invoiceLimitType\":\"ByCtrip\",\"applyAreaPropertyId\":0,\"maxGuestCountPerRoom\":1,\"maskRoom\":false,\"hourlyRoom\":false,\"corpPrice\":false,\"manualGds\":false,\"joined30MinutesFreeCancel\":false,\"premiumRoom\":true,\"welfareRoom\":false,\"groupMemberShip\":false,\"earnHotelPoints\":false,\"bonusPointInfo\":{\"bonusPointRoom\":false},\"earlyArrivalTime\":\"2025-01-10T14:00:00+08\",\"lastCancelTime\":\"2025-01-10T18:00:00+08\",\"cnyAmount\":580,\"originAmountInfo\":{\"amount\":580,\"currency\":\"CNY\",\"exchange\":1},\"customAmountInfo\":{\"amount\":580,\"currency\":\"CNY\",\"exchange\":1},\"originCostInfo\":{\"amount\":522,\"currency\":\"CNY\",\"exchange\":1},\"originCostBeforeTaxInfo\":{\"amount\":522,\"currency\":\"CNY\",\"exchange\":1},\"roomDiscountInfo\":{\"promotionSwitchOn\":true,\"customerTagTypes\":[{\"key\":\"EnableNewGroupMember\",\"value\":\"T\"},{\"key\":\"enableMobileRateRoom\",\"value\":\"T\"},{\"key\":\"FilterPrepayDiscountCampaignIDBlackList\",\"value\":\"1178,738\"},{\"key\":\"IsPrepayDiscountMoney\",\"value\":\"T\"},{\"key\":\"PrepayDiscountCtripNewCustomer\",\"value\":\"F\"},{\"key\":\"PrepayDiscountHotelNewCustomer\",\"value\":\"T\"},{\"key\":\"BrandNewCustomer\",\"value\":\"T\"},{\"key\":\"CtripGroupID\",\"value\":\"-1\"},{\"key\":\"SwitchList\",\"value\":\"53,44,92,50,121,136,69,94,103,122,123,150,156,159,157\"},{\"key\":\"UserCountryCode\",\"value\":\"CN\"},{\"key\":\"CtripGroupLevel\",\"value\":\"10003\"}],\"bookRulePromotionList\":[],\"roomDailyDiscountInfo\":[{\"effectDate\":\"2025-01-10T00:00:00+08\",\"tagId\":300,\"ruleConfigId\":0,\"prepayCampaignId\":774,\"typeConfigId\":0,\"discountType\":4,\"discountCnyAmount\":22,\"discountAmount\":22,\"discountCustomAmount\":22}],\"originPromotionAmount\":{\"price\":22,\"currency\":\"CNY\"},\"customPromotionAmount\":{\"price\":22,\"currency\":\"CNY\"},\"customerPropertys\":[{\"propertyKey\":\"146\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"167\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"168\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"171\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"180\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"185\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"111\",\"propertyValue\":\"222\",\"propertyType\":0},{\"propertyKey\":\"332\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"102\",\"propertyValue\":\"100\",\"propertyType\":0},{\"propertyKey\":\"475\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"329\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"174\",\"propertyValue\":\"0\",\"propertyType\":0},{\"propertyKey\":\"595\",\"propertyValue\":\"0\",\"propertyType\":0,\"propertyExtensionInfo\":{\"extensionItemList\":[{\"itemKey\":\"10\",\"flag\":0},{\"itemKey\":\"14\",\"flag\":0},{\"itemKey\":\"21\",\"flag\":0},{\"itemKey\":\"32\",\"flag\":0},{\"itemKey\":\"363\",\"flag\":0},{\"itemKey\":\"319\",\"flag\":0}],\"type\":4}},{\"propertyKey\":\"597\",\"propertyValue\":\"0\",\"propertyType\":0,\"propertyExtensionInfo\":{\"extensionItemList\":[{\"itemKey\":\"576\",\"flag\":0},{\"itemKey\":\"321\",\"flag\":0},{\"itemKey\":\"325\",\"flag\":0},{\"itemKey\":\"11\",\"flag\":0},{\"itemKey\":\"587\",\"flag\":0},{\"itemKey\":\"724\",\"flag\":0},{\"itemKey\":\"344\",\"flag\":0},{\"itemKey\":\"347\",\"flag\":0},{\"itemKey\":\"1692\",\"flag\":0},{\"itemKey\":\"414\",\"flag\":0},{\"itemKey\":\"418\",\"flag\":0},{\"itemKey\":\"741\",\"flag\":0},{\"itemKey\":\"1128\",\"flag\":0},{\"itemKey\":\"939\",\"flag\":0},{\"itemKey\":\"2997\",\"flag\":0},{\"itemKey\":\"1974\",\"flag\":0}],\"type\":4}},{\"propertyKey\":\"599\",\"propertyValue\":\"0\",\"propertyType\":0,\"propertyExtensionInfo\":{\"extensionItemList\":[{\"itemKey\":\"897\",\"flag\":0},{\"itemKey\":\"281\",\"flag\":0}],\"type\":4}},{\"propertyKey\":\"601\",\"propertyValue\":\"0\",\"propertyType\":0,\"propertyExtensionInfo\":{\"extensionItemList\":[{\"itemKey\":\"770\",\"flag\":0},{\"itemKey\":\"1961\",\"flag\":0}],\"type\":4}},{\"propertyKey\":\"100\",\"propertyValue\":\"0\",\"propertyType\":1}]},\"roomDailyInfo\":[{\"effectDate\":\"2025-01-10T00:00:00+08\",\"cnyAmount\":580,\"amount\":580.0,\"customAmount\":580,\"afterPromotionCnyAmount\":558,\"afterPromotionCustomAmount\":558,\"customizedCnyAmount\":0,\"mealNumber\":1,\"afterPromotionCustomAmountExcludeTax\":558.00,\"afterPromotionCnyAmountExcludeTax\":558.00,\"customAmountExcludeTax\":580.00,\"cnyAmountExcludeTax\":580.00}],\"paymentRulesInfo\":{\"paymentGuaranteePoly\":\"TRIP\",\"canTravelMoneyPay\":true,\"forceVccPay\":false,\"canCreditCardPay\":true},\"longRentalInfo\":{\"longRental\":false,\"needInquire\":false},\"holdRoomInfo\":{\"holdRoom\":false},\"extraPayTaxMultiCurrencyAmountInfo\":{\"customExtraPayTaxTotalAmount\":0,\"cnyExtraPayTaxTotalAmount\":0},\"pricedRoom\":false,\"generalizedPremiumRoom\":true,\"customAmountExcludeTaxInfo\":{\"amount\":580,\"currency\":\"CNY\",\"exchange\":1},\"supportAnticipation\":false,\"roomRightInfoList\":[],\"invoiceTypeExcludeAddPrice\":\"Special\",\"roomMealInfo\":{\"mealType\":4,\"mealDescList\":[\"1份早餐\"],\"dailyMealInfoList\":[{\"effectDateUTC\":\"2025-01-09T16:00:00Z\",\"mealDescList\":[\"\"]},{\"effectDateUTC\":\"2025-01-10T16:00:00Z\",\"mealDescList\":[\"1份早餐\"]}]},\"saleRoomTags\":[{\"tagCode\":\"QWZD\",\"tagName\":\"商旅特惠\",\"tagDesc\":\"十亿豪补，每间夜立减22元\",\"configInfoList\":[]},{\"tagCode\":\"ZXJ\",\"tagName\":\"尊享价\",\"tagDesc\":\"携程商旅客户专享价，更贴近商旅出行需求\",\"configInfoList\":[{\"position\":301,\"priority\":6,\"style\":\"EXCLUSIVE_ENHANCED\"}]}],\"packageRoomInfo\":{\"packageRoom\":false,\"packageId\":0,\"xProductId\":[]},\"taxDetails\":[],\"pid\":\"AQoDNTgwEgY1ODAuMDAqA0NOWTINCgNDTlkSA0NOWRoBMTINCgNDTlkSA0NOWRoBMToCCABCBwjpDxABGApKBwjpDxABGAtQAVgAaAN4AYAB5vnlMQ\\u003d\\u003d\",\"supplierChannel\":\"0\",\"onlyGroupMemberCanBook\":false},\"bookingRules\":{\"cancelPenalties\":{\"paymentGuaranteePoly\":\"TRIP\",\"ladderDeduct\":true,\"cancelDeductDetail\":{\"originDeductionPrice\":{\"price\":558,\"currency\":\"CNY\"},\"customDeductionPrice\":{\"price\":558,\"currency\":\"CNY\"}}},\"authorizationRestriction\":{},\"confirmRules\":{\"justifyConfirm\":true,\"confirmDuration\":0,\"forbidNotImConfirm\":false},\"welfareRoomRules\":{},\"limitDepartureDateTime\":{\"departureEnd\":\"2025-01-11T12:00:00+08\",\"departureEndUTC\":\"2025-01-11T04:00:00Z\"},\"certificateInfo\":{},\"minLOS\":1,\"cancelPolicyInfo\":{\"cancelType\":\"LADDER_FREE\",\"freeCancelPolicySceneType\":\"NOT_SUPPORTED\",\"lastCancelTimeUTC\":\"2025-01-10T10:00:00Z\",\"guaranteePolicyInfo\":{\"guaranteeType\":\"FIRST_DAY_PREPAY\",\"paymentGuaranteePoly\":\"TRIP\",\"guaranteeReason\":\"NONE\",\"guaranteePriceInfo\":{\"originGuaranteePrice\":{\"price\":558,\"currency\":\"CNY\"},\"customGuaranteePrice\":{\"price\":558,\"currency\":\"CNY\"}},\"cancelDeductDetailInfo\":[{\"deductionStartTimeUTC\":\"2024-12-31T01:53:33Z\",\"deductionEndTimeUTC\":\"2025-01-10T10:00:00Z\",\"deductionRatio\":0.0,\"deductionType\":\"RADIO\",\"originDeductionPrice\":{\"price\":0,\"currency\":\"CNY\"},\"customDeductionPrice\":{\"price\":0,\"currency\":\"CNY\"}},{\"deductionStartTimeUTC\":\"2025-01-10T10:00:00Z\",\"deductionEndTimeUTC\":\"0001-12-29T16:00:00Z\",\"deductionRatio\":1,\"deductionType\":\"NIGHT\",\"originDeductionPrice\":{\"price\":558,\"currency\":\"CNY\"},\"customDeductionPrice\":{\"price\":558,\"currency\":\"CNY\"}}]}},\"limitArrivalDateTime\":{\"arrivalFromUTC\":\"2025-01-10T06:00:00Z\",\"arrivalEndUTC\":\"2025-01-10T22:00:00Z\",\"defaultArrivalTimeEnd\":true},\"rPLimitInfo\":{\"minLos\":1,\"maxLos\":999,\"minAdvancedBookingDays\":0,\"maxAdvancedBookingDays\":999},\"nationalityRestrictionInfo\":{\"allowCountryCodeList\":[],\"blockCountryCodeList\":[]},\"billingGuestInfo\":{\"guestsNameLanguages\":[\"zh\",\"en\"]},\"restrictRuleList\":[]},\"bookExtInfo\":{\"featureControlFlagMap\":{\"EnableMemberPoints\":false,\"EnableXMinutesFreeCancel\":false,\"BrandNewCustomer\":true,\"EnableShadowDiscount\":true,\"EnableYXH\":true,\"EnablePrepayDiscount\":true,\"EnableNewGroupMember\":true,\"ForbidMultiNationality\":false,\"NewUserCountryCode\":true,\"EnableCNYTWODecimal\":true,\"ShieldMaskRoom\":false,\"EnableAddPrice\":false,\"IgnoreSpecialCancelPolicyNotMatchingFilter\":false,\"StrictCancellationPolicy\":false,\"IgnoreRemitTax\":false,\"ShieldMultiplePrice\":true,\"Enable30MinuteFreeCancel\":true,\"EnablePromotionSubTag\":true,\"EnableHotelNewCustomer\":true,\"DisableIPBlock\":false,\"EnableDirectRate\":false,\"EnableOutputPackageRoom\":true,\"ShieldMaskNakedSale\":true,\"EnableMobileRate\":true,\"EnableMatchPolicyWithExtraPayTax\":false},\"prepayDiscountCampaignIdWhiteList\":[],\"prepayDiscountCampaignIdBlackList\":[1178,738],\"customTagKeysToken\":\"V11-d85f958d6176dee854037ca1e5e6cf948b40423c571dc921234c28b0d2e57553\",\"switchListValuesToken\":\"V4-766aefe9110e8a58f5f2d09e7234a8b9339711f60855cffb3f07b90b4796133b\"},\"userRightsInfo\":{\"baseGroupLevel\":\"10003\",\"userChosenCouponInfo\":[],\"hotelNewCustomer\":true,\"userSelectedCountryCode\":\"CN\",\"yxhGroupId\":\"-1\",\"multiCouponTotalCustomAmount\":0},\"reservationToken\":\"AQgBEgoyMDI1LTAxLTEwGhsKAU0SAlBQGgRUUklQMgNDTlk6ATFI3efwgAUiAggAKg8KBXpoLUNOEgNDTlkaATE\\u003d\",\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2024-12-31 09:53:50.857+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", QueryCheckAvailContextResponseType.class)
        accountInfo = WrapperOfAccount.builder()
                .accountInfo(generalSearchAccountInfoResponseType)
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsId")))
                        .build().getCheckAvailContextInfo()
        orderCreateRequestType = JsonUtil.fromJson("{\"integrationSoaRequestType\":{\"userInfo\":{\"userId\":\"**********\",\"corpId\":\"562332\",\"groupId\":\"\",\"pos\":\"CHINA\"},\"token\":\"4C2EC5CD974982C8974E29E1343A96A6FBE535D8572FCFC88FB3235C10DDF1C6\",\"language\":\"zh-CN\",\"requestId\":\"631a4880528b46969b1a9c07f6526669\",\"sourceFrom\":\"Online\",\"transactionID\":\"8227c2e9e9264dc48833b632095214fa\",\"gatewayHost\":\"ct.ctrip.com\",\"timezoneOffsetMinutesNew\":\"480\",\"logIndices\":[{\"key\":\"groupId\",\"value\":\"\"},{\"key\":\"timezoneOffsetMinutesNew\",\"value\":\"480\"},{\"key\":\"gatewayHost\",\"value\":\"ct.ctrip.com\"},{\"key\":\"pvid\",\"value\":\"25\"},{\"key\":\"subChannel\",\"value\":\"\"},{\"key\":\"CID\",\"value\":\"09031136111887930140\"},{\"key\":\"EID\"},{\"key\":\"CIP\",\"value\":\"**************\"},{\"key\":\"miceToken\",\"value\":\"\"},{\"key\":\"groupName\"},{\"key\":\"gatewayIdc\",\"value\":\"SHARB\"},{\"key\":\"traceID\",\"value\":\"\"},{\"key\":\"A\",\"value\":\"ordercheck\"},{\"key\":\"CorpID\",\"value\":\"562332\"},{\"key\":\"C\",\"value\":\"corpbookinghotelbookservice\"},{\"key\":\"M\",\"value\":\"**********\"},{\"key\":\"sid\",\"value\":\"1564\"},{\"key\":\"S\",\"value\":\"dab4b452b85a4919a9e41504f55bf9fb\"},{\"key\":\"T\"},{\"key\":\"pos\",\"value\":\"CHINA\"},{\"key\":\"GE\",\"value\":\"1735610032541\"},{\"key\":\"sourceFrom\",\"value\":\"Online\"},{\"key\":\"corpName\",\"value\":\"伟视得电子贸易（上海）有限公司\"},{\"key\":\"RID\",\"value\":\"631a4880528b46969b1a9c07f6526669\"},{\"key\":\"TID\",\"value\":\"8227c2e9e9264dc48833b632095214fa\"},{\"key\":\"VID\",\"value\":\"1690947080611.2qc4rr\"}],\"ticket\":\"mwCEcfIUweGxFP1V/b+I//EKj9BXB8B5GWuwSpCxv/pPpVam8fPV2kA4yHQoR5gW/lV8VsRiA+SWp8+AZgL1Yk44pLwxT2s60LjJLA0CJs4\\u003d\",\"transferInfo\":[{\"key\":\"iframe\",\"value\":\"F\"},{\"key\":\"channel\",\"value\":\"Online\"}]},\"hotelBookPassengerInputs\":[{\"hotelPassengerInput\":{\"uid\":\"2196175330\",\"employee\":\"T\",\"external\":\"T\"},\"passengerBasicInfo\":{\"gender\":\"U\",\"birth\":\"1977-10-08\"},\"name\":\"陶志杰\",\"isRegistrant\":\"F\",\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"13816524209\",\"transferPhoneNo\":\"13816524209\"},\"nationalityInfo\":{\"nationalityCode\":\"CN\"}}],\"hotelInvoiceInfos\":[],\"cityInput\":{\"cityId\":30},\"hotelContactorInfo\":{\"name\":\"商旅客户\",\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"13662597999\",\"transferPhoneNo\":\"13662597999\"},\"emailInfo\":{\"email\":\"<EMAIL>\",\"transferEmail\":\"<EMAIL>\"}},\"approvalInput\":{\"emergency\":\"T\"},\"miceInput\":{},\"hotelBookInput\":{\"hotelDateRangeInfo\":{\"checkIn\":\"2025-01-10\",\"checkOut\":\"2025-01-11\"},\"roomQuantity\":1},\"membershipInfo\":{\"membershipUid\":\"**********\"},\"bookModeInfo\":{\"bookingType\":\"NONE\"},\"resourceTokenInfo\":{\"resourceToken\":\"{\\\"hotelResourceToken\\\":{\\\"masterHotelId\\\":4690345,\\\"hotelId\\\":*********,\\\"searchHotelTraceId\\\":\\\"2ef3b17af60a4251badb9e0fa929ce35\\\",\\\"hid\\\":\\\"ASIDNTgwQgFHSgYIABAAGABSBBAAGABaA0NOWWINCgNDTlkSA0NOWRoBMWIPCgNDTlkSA0NOWRoDMS4waAFyATB6ATCCASAyZWYzYjE3YWY2MGE0MjUxYmFkYjllMGZhOTI5Y2UzNYoBCjEzNDQwMjU1NjWaASRlNmJlMjg5Zi02ZWFjLTQ2YWQtYjg3YS04ZWM3MTUzYzY3YjGiAQIIAKoBJGY2M2ViMWRhLTRkYzgtNDViYi1hMjE2LTQ5NjIxMTU2OWY1Nw\\\\u003d\\\\u003d\\\",\\\"hotelType\\\":\\\"M\\\",\\\"bonusPoint\\\":\\\"F\\\",\\\"hotelGeoInfoResourceToken\\\":{\\\"cityId\\\":30,\\\"locationId\\\":214}},\\\"roomResourceToken\\\":{\\\"roomId\\\":1344025565,\\\"baseRoomId\\\":16773346,\\\"ratePlanTraceLogId\\\":\\\"e9eea79abfd7477e81e9b65372fa01c6_\\\",\\\"bedName\\\":\\\"1张1.8米大床\\\",\\\"windowName\\\":\\\"有窗户但不能打开通风\\\",\\\"breakfastInfo\\\":\\\"1份早餐\\\",\\\"confirmType\\\":\\\"IMMEDIATE\\\",\\\"roomType\\\":\\\"M\\\",\\\"balanceType\\\":\\\"PP\\\",\\\"tmcPrice\\\":false,\\\"adult\\\":1,\\\"pId\\\":\\\"AQoDNTgwEgY1ODAuMDAqA0NOWTINCgNDTlkSA0NOWRoBMTINCgNDTlkSA0NOWRoBMToCCABCBwjpDxABGApKBwjpDxABGAtQAVgAaAN4AYAB5vnlMQ\\\\u003d\\\\u003d\\\",\\\"amadeus\\\":false,\\\"supportAccommodation\\\":\\\"F\\\",\\\"roomOriginCurrency\\\":\\\"CNY\\\",\\\"roomScenarioEnum\\\":\\\"ROOM_LIST\\\",\\\"ratePlanRequestToken\\\":\\\"ASi1L/0AUFUVAJZtoz/wbMoATMGPhW/KfyPGTfdM2rrjccKGsbHADLIFQ16QpH6CRRF1NMs1yWlb22Gu1hVD5Al3TVy3idKP5sXG8D+SAJMAkgAqHMNclLXvaLoz6ensjm9Y5DKPQc45C2czct0ObVNWT4/Fu1jh6mlceDaZXUgRdSiLZp6zk/XecY934y77Y1k+GslRGyeoTQZjoSgyRLQFxYb/oJ/DGW4Uj2H7t/BP4eV/LY7KkliVhHJVF8TTwy8TihXkZUJCuEnYFIAHF4ZdGaXiolSyLNtYxbogDCY0dPAwIJYFZMctPDlOeO0TZ/F4jOmsnEM3hjW4gOxkznzwpku53RhXHuY5CFkdzGhN2reivBsuT5t4NHs8WzU3GaS6FXYFS46Ro+NmZSj6wT3VbXxhxge2tH3GiDzWFSVeTJ5m9GJr3DMw7xzicFCfLnY9C+jbXGjqNHl8zpn10HhdSoWUUfmwnw2v1Gbtq30o6FMqoSwmE/9xctcOUWl95R5Sy5dUdpBIpYsIt1KskliWBLuqKy01vUw1fhHRHoLxHxF4KjWSLnCHNhoZc0wZ+QR38YaZKoFwBmXVdmuz74Q5nOkwqS3hDm8ikPzZKQoWW+JmOUAZXsZ5hzLJF80GvY+puUwNdJ98jSsl5yk18EQXcnbAJ3BnZMrjRJ3BDg3mfDe4apZZ8gIDqytqXVLFl9BfwqomELxFxZPQL0J/CP0g9HPQf0F/Bf0UtKxfzeoJuqM1tEVvXekF+oAeoC0gPAkFFhM/4gKDdw3ZOWL6aRgR/hAF/7F9nphJNciNTM0eCihQkqIm/bQAEf4QAv8BiiKZcmEHQXHrZPppBCL9IQb+YwWRtlfuJw9/S6RtPankOlJPBiLaQxIECQBWKECUHQQsThgCmHQ3nUn9U8wvm41DeQI\\\\u003d\\\",\\\"ratePlanRoomToken\\\":\\\"ASi1L/0AUFkAAAoBTRAAGN3n8IAF\\\",\\\"roomUniqueKey\\\":\\\"V1-ASi1L/0AUJkAAAjd5/CABRIBTRoETk9ORSICUFA\\\\u003d\\\",\\\"adultNumber\\\":1,\\\"windowType\\\":6},\\\"reservationResourceToken\\\":{\\\"wsId\\\":\\\"61335899-fd39-422b-9ee8-46b370deee55\\\",\\\"policyToken\\\":\\\"v1KLUv/QBQNQkAhpI7JDCp1gFE1HTsf+UxMly6jMMrnHqS+YdHApUOYmZH+5kGBgaqBDAAMQAxAOq8AF8ZdZRylkKZukeKsCmjisVaC0AtEI4ydS3jPmSu98Udc70jZQ5MuXWE5kbKENDGnwI8vZ0wZUrcioNbWyJD2MnXvJVRx4pddgrBAdsgnCVpWkIlGqcSq5motoddrCyvG28tMErSKvOedKY/NcGl9YPQtx9EEBr8xJKiDMv4TCvt9/GwwLmCsABhw7fNjVw2DQoJTB9bNkb8cc836WCUpjMmvz8TlF7vwHckkRDfdllo9oPBEeInWWeXMGI8U5xsvBQAOhuHY4fDtcN+c/HMXYgqhKk5aKhC6gKmCOpQpWoWS6jK81n8BarcS7FcEQVIPDNqEM8w\\\"},\\\"bookInitResourceToken\\\":{\\\"aggBookPriceResourceToken\\\":{\\\"currency\\\":\\\"CNY\\\",\\\"accountPaymentAmount\\\":558,\\\"individualPaymentAmount\\\":0,\\\"mixPayAccountExtraAmount\\\":0},\\\"roomPriceResourceToken\\\":{}}}\"},\"corpPayInfo\":{\"corpPayType\":\"public\"},\"userActionInfo\":{\"userActionToken\":\"WEB_1_windows_1735610004864_1056_1056_0.8333333134651184_mbu_hotel_online_booking_aed2dd3955a0414f_5.1.1_906\"},\"approvalFlowInput\":{\"password\":\"\",\"approvalFlowToken\":\"H4sIAAAAAAAA_wXBsRGAMAgAQG0tLa0sbHMHJBgyDgQc0A1snMEpnMDC_2lzyJyxldQiJJVWKZlDT8KGejhC9JhHWlYDEKIaSuZijs2rOLOXnndR1PV97u86hx-npbdmUwAAAA\",\"approvers\":[{\"uid\":\"**********\",\"level\":\"1\"}]},\"rcInfos\":[],\"bookUrlInfos\":[{\"urlType\":\"SUCCESS\",\"urlValue\":\"https://ct.ctrip.com/hotel-online/order/success?InterfaceName\\u003dCreate_PaymentBill_New\\u0026\"},{\"urlType\":\"S_BACK\",\"urlValue\":\"https://ct.ctrip.com/hotel-online/order/success?InterfaceName\\u003dCreate_PaymentBill_New\\u0026\"},{\"urlType\":\"E_BACK\"},{\"urlType\":\"FROM_URL\",\"urlValue\":\"https://ct.ctrip.com/hotel-online-after-booking/pages/orderdetail/index?orderId\\u003d\"},{\"urlType\":\"S_BACK_MICE\",\"urlValue\":\"https://ct.ctrip.com/mice-activities/welfareTourism\"},{\"urlType\":\"FROM_URL_ORDER_DETAIL\"},{\"urlType\":\"OFFLINE_RETURN_URL\",\"urlValue\":\"http://corpint.ct.ctripcorp.com/hotel-online/offlineOrder/success\"},{\"urlType\":\"ORDER_DETAIL_URL\",\"urlValue\":\"https://ct.ctrip.com/hotel-online-after-booking/pages/orderdetail/index?\"}],\"paymentInfoInput\":{\"canWechatPayByOthers\":\"F\",\"clientType\":\"H5\"},\"clientInfo\":{\"clientType\":\"H5\",\"rmsToken\":\"fp\\u003dC9A79A-0B9732-80429C\\u0026vid\\u003d1690947080611.2qc4rr\\u0026pageId\\u003d10650065360\\u0026r\\u003dc0051b21f00a4d34b29a064a9e3ad0aa\\u0026ip\\u003d**************\\u0026rg\\u003dfin\\u0026kpData\\u003d0_0_0\\u0026kpControl\\u003d0_0_0-0_0_0\\u0026kpEmp\\u003d0_0_0_0_0_0_0_0_0_0-0_0_0_0_0_0_0_0_0_0-0_0_0_0_0_0_0_0_0_0\\u0026screen\\u003d1536x864\\u0026tz\\u003d+8\\u0026blang\\u003dzh-CN\\u0026oslang\\u003dzh-CN\\u0026ua\\u003dMozilla%2F5.0%20(Windows%20NT%2010.0%3B%20Win64%3B%20x64)%20AppleWebKit%2F537.36%20(KHTML%2C%20like%20Gecko)%20Chrome%2F131.0.0.0%20Safari%2F537.36%20Edg%2F131.0.0.0\\u0026d\\u003dct.ctrip.com\\u0026v\\u003d25\\u0026kpg\\u003d0_0_0_0_0_0_0_0_0_0\\u0026adblock\\u003dF\\u0026cck\\u003dF\",\"secondaryChannel\":\"Other-new\"},\"offlineInfo\":{\"forciblyAssure\":\"F\",\"especialAuth\":\"F\",\"autoConfirm\":\"F\",\"exigentOrder\":\"F\",\"operationRule\":\"\"},\"roomInfoInput\":{\"roomRemark\":\"\",\"roomCustomRemark\":\"\"},\"sendInfo\":{\"sendSMS\":\"T\",\"sendEmail\":\"F\"},\"ssoInput\":{\"ssoKey\":\"1ebd2eb0-4d4d-11ef-a7ba-fd6d3a88e30c\"},\"flashStayInput\":{\"flashPlatFrom\":\"Online\"},\"hotelPayTypeInput\":[{\"payCode\":\"ROOM\",\"payType\":\"CORP_PAY\"}],\"strategyInfos\":[{\"strategyKey\":\"BOOKING_SCENARIO\",\"strategyValue\":\"HOTEL_LIST\"},{\"strategyKey\":\"BOOKING_WITH_PERSONAL_ACCOUNT\",\"strategyValue\":\"F\"}],\"costCenterInfo\":{\"costCenterJsonString\":\"{\\\"items\\\":{\\\"fdefault\\\":{\\\"cost1\\\":\\\"伟视得电子贸易（上海）有限公司深圳分公司\\\",\\\"cost2\\\":\\\"Product Testing 181009\\\"}}}\"},\"orderCreateToken\":\"H4sIAAAAAAAAAOMSCfb0c/dxjXcMCAjyD3P0iXfz8Q9X0uBSSTEwNjU2tDTRtUxNtdA1sTQ30k1KMUjWtTBNMkxMSzE0SE1OFWDQYAAAWHUyMEIAAAA\\u003d\",\"useNewOrderCreate\":\"F\"}", OrderCreateRequestType.class)
        orderCreateRequestType.cityInput.cityId = 22249
        resultOld = JsonUtil.fromJson("{\"saveList\":[{\"bizType\":64,\"orderID\":****************,\"uid\":\"**********\",\"pid\":\"**********\",\"corpId\":\"562332\",\"eid\":\"**********\",\"costCenterDesc\":\"Travel Policy Shenzhen\",\"costCenterDescEn\":\"\",\"orderCostCenters\":[{\"costCenterKey\":2,\"costCenterValue\":\"Product Testing 181009\",\"costCenterTitle\":\"Department\",\"costCenterTitleEn\":\"Department\"},{\"costCenterKey\":1,\"costCenterValue\":\"伟视得电子贸易（上海）有限公司深圳分公司\",\"costCenterTitle\":\"Company\",\"costCenterTitleEn\":\"Company\"}],\"passengers\":[{\"passengerId\":\"2196175330\",\"passengerName\":\"陶志杰\",\"isEmployee\":1}],\"frequentlyUsedCostCenter\":1,\"createTimeStr\":\"2024-12-31 09:53:52\"}]}", SaveOrderCostCenterRequestType.class)
        matchCostCenterResponseType = JsonUtil.fromJson("{\"result\":{\"costCenterDescription\":\"Travel Policy Shenzhen\",\"costCenterDescriptionEn\":\"\",\"costCenterContentlist\":[{\"groupID\":1,\"orderOrPerson\":\"O\",\"costCenterConfigs\":[{\"level\":1,\"costCenterTitle\":\"Company\",\"costCenterTitleEn\":\"Company\",\"required\":true,\"canChange\":true,\"linked\":false,\"selectHidden\":false,\"readUserCostCenter\":0,\"needAddress\":false},{\"level\":2,\"costCenterTitle\":\"Department\",\"costCenterTitleEn\":\"Department\",\"required\":true,\"canChange\":true,\"linked\":true,\"selectHidden\":false,\"readUserCostCenter\":0,\"needAddress\":false}],\"costCenterItems\":[]}],\"costCenterExtlist\":[{\"costCenterExtType\":\"TP\",\"costCenterExtTitle\":\"出行目的\",\"costCenterExtTitleEn\":\"出行目的\",\"required\":true,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"PN\",\"costCenterExtTitle\":\"项目\",\"costCenterExtTitleEn\":\"项目\",\"required\":true,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"FT\",\"costCenterExtTitle\":\"机票关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of flight\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"HT\",\"costCenterExtTitle\":\"酒店关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of hotel\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"TT\",\"costCenterExtTitle\":\"火车票关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of train\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"CT\",\"costCenterExtTitle\":\"用车关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No. of car\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"BT\",\"costCenterExtTitle\":\"汽车票关联行程号\",\"costCenterExtTitleEn\":\"Associated itinerary No.\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"D1\",\"costCenterExtTitle\":\"自定义字段1\",\"costCenterExtTitleEn\":\"Self-defined title No.1\",\"required\":false,\"enabled\":false,\"selectHidden\":false},{\"costCenterExtType\":\"D2\",\"costCenterExtTitle\":\"自定义字段2\",\"costCenterExtTitleEn\":\"Self-defined title No.2\",\"required\":false,\"enabled\":false,\"selectHidden\":false}],\"frequentlyUsedCostCenter\":1},\"costCenterDefaultTitles\":[{\"costCenterDefaultType\":\"TP\",\"costCenterDefaultTitle\":\"出行目的\",\"costCenterDefaultTitleEn\":\"JounaryReason\"},{\"costCenterDefaultType\":\"PN\",\"costCenterDefaultTitle\":\"项目号\",\"costCenterDefaultTitleEn\":\"Project\"},{\"costCenterDefaultType\":\"FT\",\"costCenterDefaultTitle\":\"机票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of flight\"},{\"costCenterDefaultType\":\"HT\",\"costCenterDefaultTitle\":\"酒店关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of hotel\"},{\"costCenterDefaultType\":\"TT\",\"costCenterDefaultTitle\":\"火车票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of train\"},{\"costCenterDefaultType\":\"CT\",\"costCenterDefaultTitle\":\"用车关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of car\"},{\"costCenterDefaultType\":\"BT\",\"costCenterDefaultTitle\":\"汽车票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No.\"},{\"costCenterDefaultType\":\"D1\",\"costCenterDefaultTitle\":\"自定义字段1\",\"costCenterDefaultTitleEn\":\"Self-defined title No.1\"},{\"costCenterDefaultType\":\"D2\",\"costCenterDefaultTitle\":\"自定义字段2\",\"costCenterDefaultTitleEn\":\"Self-defined title No.2\"},{\"costCenterDefaultType\":\"CC1\",\"costCenterDefaultTitle\":\"成本中心1\",\"costCenterDefaultTitleEn\":\"Cost center No.1\"},{\"costCenterDefaultType\":\"CC2\",\"costCenterDefaultTitle\":\"成本中心2\",\"costCenterDefaultTitleEn\":\"Cost center No.2\"},{\"costCenterDefaultType\":\"CC3\",\"costCenterDefaultTitle\":\"成本中心3\",\"costCenterDefaultTitleEn\":\"Cost center No.3\"},{\"costCenterDefaultType\":\"CC4\",\"costCenterDefaultTitle\":\"成本中心4\",\"costCenterDefaultTitleEn\":\"Cost center No.4\"},{\"costCenterDefaultType\":\"CC5\",\"costCenterDefaultTitle\":\"成本中心5\",\"costCenterDefaultTitleEn\":\"Cost center No.5\"},{\"costCenterDefaultType\":\"CC6\",\"costCenterDefaultTitle\":\"成本中心6\",\"costCenterDefaultTitleEn\":\"Cost center No.6\"}],\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2024-12-31 09:53:50.622+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", MatchCostCenterResponseType.class)
        costCenterInfoType = Optional.ofNullable(matchCostCenterResponseType.getResult()).orElse(new CostCenterInfoType())
        createOrderResponseType = JsonUtil.fromJson("{\"wsId\":\"61335899-fd39-422b-9ee8-46b370deee55\",\"orderId\":****************,\"orderPaymentInfo\":{\"payType\":\"ACCNT\",\"orderAmountInfo\":{\"cnyTotalAmount\":580,\"paymentItemList\":[{\"prepayType\":\"ACCNT\",\"gatheringType\":\"D\",\"feeType\":\"HotelRoomFee\",\"amountInfo\":{\"amount\":558,\"currency\":\"CNY\",\"exchangeRateToCNY\":1}}]}},\"success\":true,\"supportPaymentBill\":true,\"paymentTransactionId\":\"308104355998400613\",\"orderTotalAmount\":{\"orderAmount\":{\"originAmount\":{\"amount\":580,\"currency\":\"CNY\",\"exchange\":1},\"cNYAmount\":580,\"customAmount\":{\"amount\":580.00,\"currency\":\"CNY\",\"exchange\":1}},\"paymentBillInfo\":{\"payAmount\":558,\"payCNYAmount\":558,\"payCurrency\":\"CNY\",\"payExchange\":1},\"guaranteeAmount\":{\"originGuaranteeAmount\":{\"amount\":558,\"currency\":\"CNY\",\"exchange\":1},\"cNYGuaranteeAmount\":558}},\"transactionInfo\":{\"transactionId\":\"281699f4-2b61-456b-91f9-cf90b182a711\",\"subTransactionIdList\":[{\"payType\":\"ACCNT\",\"subTransactionId\":\"2245b443-8c71-4ccd-8304-bef692887ee8\"}]},\"responseCode\":20000,\"responseStatus\":{\"timestamp\":\"2024-12-31 09:53:52.492+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", CreateOrderResponseType.class)
        resultNew = mapper.buildSaveOrderCostCenterRequest(orderCreateRequestType, accountInfo, checkAvailInfo, orderCreateToken, createOrderResponseType, costCenterInfoType, approvalTextInfoResponseType, ssoInfoQueryResponseType, resourceToken, null, null)
        // 抹平变量
        resultOld.saveList.get(0).createTimeStr = "2024-12-30 10:44:45"
        resultNew.saveList.get(0).createTimeStr = "2024-12-30 10:44:45"
        then:
        resultNew != null
        // 对比保存的内容需要完全一致
        resultNew == resultOld

        when: // 长安合住账号
        generalSearchAccountInfoResponseType = JsonUtil.fromJson("{\"uid\":\"**********\",\"rid\":\"f8011a3ede1945d280abe5418f063d24\",\"results\":{\"currency\":\"CNY\",\"AuthDelayM\":\"F\",\"AuthDelayC\":\"F\",\"OverSeaAuthDelayM\":\"F\",\"OverSeaAuthDelayC\":\"F\",\"RepeatOrderCheck\":\"T\",\"overSeaNeedAuditHY\":\"F\",\"overSeaNeedAuditXY\":\"F\",\"needAuditHY\":\"F\",\"needAuditXY\":\"F\",\"BillType\":\"A\",\"CorpGuaranteeFlag\":\"F\",\"IsHtlPrintTicketAfterConfirm\":\"T\",\"OverStandCanMixPay\":\"T\",\"AgreementHtlOverStandCanMixPay\":\"T\",\"IsShieldPayTypePersonal_HotelI\":\"F\",\"IsShieldPayTypePersonal_HotelN\":\"F\",\"HotelTravelSendType\":\"A\",\"AgreementHotelTravelSendType\":\"A\",\"HotelVATType\":\"C\",\"AgreementHotelVATType\":\"C\",\"accountType\":\"D\",\"hIsMonthlyC\":\"T\",\"hIsMonthly\":\"T\",\"overSeaHIsMonthlyC\":\"F\",\"overSeaHIsMonthly\":\"F\",\"isShieldPayTypePersonal_AgreementHotelI\":\"F\",\"isShieldPayTypePersonal_AgreementHotelN\":\"F\",\"htlPrepayHTIsBlockHY\":\"F\",\"nCPTaxAgreementHotelInvoiceType_Cash\":\"A,B\",\"agreementHotelInvoiceType_Cash\":\"A\",\"nCPTaxOSHotelInvoiceType\":\"A\",\"hotelInvoiceTypeM\":\"B\",\"nCPTaxHotelInvoiceType_Cash\":\"A,B\",\"hotelInvoiceType_Cash\":\"A,B,C\",\"hotelVATTypeAddPrice\":\"N\",\"oSIsShowCanPayRoomTypeByCorp\":\"T\",\"isShowCanPayRoomTypeByCorp\":\"T\",\"privateOrderFlag\":\"T\",\"oSIsDefaultSelectHotelAgreement\":\"F\",\"isDefaultSelectHotelAgreement\":\"F\",\"hotelPro\":\"A,B,D,E,F,G,ZJTH\",\"hotelBookingFilterByPolicy\":\"F\",\"htlMixPayWithRoomPriceUp\":\"F\",\"htlMixPayWithRoomPriceUpProp\":\"0.25\",\"canMixPay\":\"F\",\"agreementHtlMixPay\":\"F\",\"isNeedDomAgreementRC\":\"F\",\"BookPolicy\":\"C\",\"HotelBookPolicy\":\"C\",\"IsCostCenRequire\":\"F\",\"hotelAgreementSelectAllowLayout\":\"DefaultNotSelect\",\"IsDomLowPriceRCXY\":\"T\",\"IsNeedIntLowPriceRC\":\"T\",\"IsNeedDomLowPriceRC\":\"T\",\"HotelRoomLayoutRule\":\"D\",\"PolicyUserCTLScope\":\"\",\"hotelTravelStandControlModel\":\"P\",\"isChkaheadapproveHotel\":\"F\",\"isChkaheadapproveHotelI\":\"T\",\"VerifyAuthorization\":\"T\",\"ChkaheadapproveHotelI\":\"1\",\"ChkaheadapproveHotel\":\"1\",\"AllowAlipayWechatPayControl\":\"TrainTicketAllowAlipayPay,TrainAllowWeChatAgentPay,FlightAllowWechatPay,HotelAllowWechatPay,FltOnlBank,CarAllowWechatPay,HotelAllowWeChatAgentPay,CarAllowAlipayPay,TrainTicketAllowWechatPay,FlightAllowWeChatAgentPay,FlightAllowAlipayPay,HotelAllowAlipayPay\",\"BookPolicyTravelMode\":\"N\",\"htlInvoiceExpressNeedCharge\":\"T\",\"HtlFeeAllocationType\":\"C\",\"HotelPassengersControl\":\"O\",\"HotelPolicyScope\":\"\",\"IsMustHotelPolicy\":\"T\",\"HotelLiveTogetherMF\":\"F\",\"corpMemberGradeIsOpen\":\"H\",\"ISHtlShareControlEdit\":\"T\",\"PayTypeHotle\":\"D\",\"PayTypeFlight\":\"D\",\"intlHotePro\":\"A\",\"AgreeHtlInvoiceExpressNeedCharge\":\"T\",\"AgreeHtlInvoiceExpressNum\":\"10\",\"IsHtlHideDayUnitPrice\":\"F\",\"PLCSMore\":\"F\",\"AllowCrossPromotePro\":\"C\",\"IsNeedHotelPunchCard\":\"F\",\"HtlStandardSharePercentage\":\"1\",\"isNeedIntAgreementRC\":\"F\",\"NeedAgreeBeforeDaysRC\":\"F\",\"NeedAgreeBeforeDaysRCNum\":\"0\",\"NeedAgreeIntBeforeDaysRC\":\"F\",\"NeedAgreeIntBeforeDaysRCNum\":\"0\",\"NeedBeforeDaysRC\":\"F\",\"NeedBeforeDaysRCDaysNum\":\"0\",\"NeedIntBeforeDaysRC\":\"F\",\"NeedIntBeforeDaysRCNum\":\"0\",\"NCPTaxHotelTravelMSendType\":\"A\",\"NCPTaxHotelTravelASendType\":\"A\",\"NCPTaxHotelVATType\":\"C\",\"hotelInvoiceTypeC\":\"B\",\"OSHotelInvoiceType\":\"C\",\"canUseAccntWithoutWorkTime\":\"T\",\"LCSSChinese\":\"T\",\"LCSEnglish\":\"T\",\"IsShowHighPrice\":\"T\",\"PLCSHotle\":\"T\",\"PLCSFlight\":\"T\",\"HConfirmVerbal1\":\"T\",\"HConfirmVerbal2\":\"F\",\"OverSeaHConfirmVerbal1\":\"T\",\"OverSeaHConfirmVerbal2\":\"F\",\"OverSeaHotelConfirmTypeNew\":\"C6;C10;C11;A2\",\"HotelConfirmTypeNew\":\"C6;C10;C11;A2\",\"HotelRecommendation\":\"F\",\"IsSignGDSSuppleAgreement\":\"F\",\"isHtlRecommend\":\"F\",\"htlRecommendTagName\":\"公司推荐\",\"BillControlMode\":\"B\",\"checkInRequiredApproval\":\"N\",\"IsChkaheadapproveQueryResource\":\"T\",\"accountName\":\"CHANGANQICHE_月结\",\"isHotelBookPrompt\":\"T\",\"LCSTChinese\":\"T\",\"accountID\":\"792342\",\"CompanyName\":\"重庆长安汽车股份有限公司_月结\",\"fundAccount.status\":\"A\",\"IsHtlReleateJourneyNoInput\":\"F\",\"CorpCorporation\":\"CHANGANQICHE\",\"ComparePrice\":\"T\",\"HotelBookCall\":\"酒店700606\",\"CorporationFlag\":\"A\",\"CompanyType\":\"9\",\"fundSubAccount.status\":\"A\",\"subAccountID\":\"880135\",\"OfflPLCSHotle\":\"T\",\"OSHotelRoomLayoutRule\":\"D\",\"serviceVersion\":\"V2\",\"IntlPunchRangeLimitKM\":\"5\",\"HtlFeeShareControl\":\"T\",\"IsIntDomLowPriceRCXY \":\"T\",\"RepeatOrderControlCorp\":\"A\",\"basicMemberBenefits\":\"T\",\"hotelPunchCardRange\":\"\",\"IntlPunchRangeLimit\":\"F\",\"IsRecommendRoomTypeByNoResult\":\"T\",\"IntlPunchTimeLimit\":\"Period\",\"IsAllowClockRange\":\"\",\"NationPunchTimeLimit\":\"Period\",\"oSHotelAgreementSelectAllowLayout\":\"DefaultNotSelect\",\"IntlHotelPunchCardRange\":\"\",\"HtlOrderFeeDistribution\":\"A,B\",\"NationPunchRangeLimitKM\":\"5\",\"NationHotelPunchCardRange\":\"\",\"PunchRangeLimit\":\"F\",\"WelfareResources\":\"F\",\"IsNWhiteHotelOpen\":\"F\",\"NationPunchRangeLimit\":\"F\",\"OfflPayTypeHotle\":\"D\",\"PunchTimeLimit\":\"Period\",\"TravelerUidChangeTicketCtl\":\"C,D,E\",\"PolicyUserCTLScopeH\":\"F\",\"RepeatBookingReason\":\"F\",\"IsMustFillEmail\":\"F\",\"hotelFlashService\":\"F\",\"HotelListControl\":\"B\",\"hzMemberInterests\":\"F\",\"showMemberLevel\":\"T\",\"showCorpMemberLevel\":\"T\",\"IsShieldPayTypePersonal_AgreementHotelN_HMT\":\"F\",\"docChooseDimensionHtlN\":\"P\",\"docChooseDimensionHtlI\":\"O\",\"H_HMT_IsMonthly_C\":\"F\",\"CustomerInterfaceCheckPay\":\"\",\"H_HMT_IsMonthly\":\"T\",\"IsCheckInMustSameTripApprove\":\"\",\"isSameJourneyCheckHtlI\":\"F\",\"isSameJourneyCheckHtlN\":\"T\",\"isAutoChooseApproveFormHtlN\":\"F\",\"isAutoChooseApproveFormHtlI\":\"F\",\"IsShieldPayTypePersonal_HotelN_HMT\":\"F\",\"Corp_Corporation\":\"CHANGANQICHE\"},\"retCode\":0,\"responseStatus\":{\"timestamp\":\"2025-01-15 17:01:57.717+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", GeneralSearchAccountInfoResponseType.class)
        queryCheckAvailContextResponseType = JsonUtil.fromJson("{\"baseInfo\":{\"userInfo\":{\"uid\":\"**********\",\"policyUid\":\"\",\"corpId\":\"CHANGANQICHE\",\"eid\":\"\",\"usersCityId\":447,\"userCountryCode\":\"\"},\"customCurrency\":\"CNY\",\"website\":\"APP\",\"feeType\":\"C\",\"quantity\":1,\"startTime\":\"2025-01-17T00:00:00+08\",\"endTime\":\"2025-01-18T00:00:00+08\",\"language\":\"CN\",\"locale\":\"zh-CN\",\"startTimeUTC\":\"2025-01-16T16:00:00Z\",\"endTimeUTC\":\"2025-01-17T16:00:00Z\",\"pOS\":\"zh-CN\",\"pOSType\":{\"pOS\":\"zh-CN\",\"countryId\":1},\"platform\":\"ios\",\"bookingWithPersonalAccount\":false,\"bookingChannel\":\"APP\"},\"hotelInfo\":{\"subHotelId\":987433,\"masterHotelId\":987432,\"star\":3,\"rStar\":0,\"starLicence\":false,\"customerEval\":2.5,\"hotelGroupId\":5,\"hotelBrandId\":1137,\"balancePeriod\":\"GM\",\"oversea\":false,\"tripBusinessHotel\":false,\"cuHotel\":false,\"hasServiceFee\":false,\"localTimeOffsetHours\":0,\"telephone\":\"+86-571-********\",\"geographicalInfo\":{\"locationInfo\":{\"id\":408},\"cityInfo\":{\"id\":17,\"name\":{\"textGB\":\"杭州\",\"textEn\":\"Hangzhou\"}},\"provinceInfo\":{\"id\":16},\"countryInfo\":{\"id\":1},\"coordinateInfo\":{}},\"address\":{\"textGB\":\"庆春路85号锦和大厦A座一楼\",\"textEn\":\"Floor 1, Building A, Jinhe Building, No. 85 Qingchun Road\"},\"hotelName\":{\"textGB\":\"宜必思酒店(杭州西湖庆春路店)\",\"textEn\":\"Ibis Hotel (Hangzhou West Lake Qingchun Road)\"},\"currentCountry\":true,\"timeZoneId\":\"Asia/Shanghai\"},\"roomInfo\":{\"subRoomId\":**********,\"roomName\":{\"textGB\":\"商务大床房\",\"textEn\":\"Business Queen Room\"},\"basicRoomName\":{\"textGB\":\"商务大床房\",\"textEn\":\"Business Queen Room\"},\"masterBasicRoomId\":2369593,\"masterBasicRoomName\":{\"textGB\":\"商务大床房\"},\"roomType\":\"C\",\"tmcPriceType\":\"M\",\"balanceType\":\"PP\",\"gdsType\":\"HZ\",\"mealType\":4,\"priceSuitPropertyValueId\":1262924,\"ratePlanKey\":{\"checkAvlID\":0,\"ratePlanID\":\"0\"},\"addPrice\":false,\"invoiceType\":\"Special\",\"invoiceLimitType\":\"NotSupported\",\"applyAreaPropertyId\":0,\"maxGuestCountPerRoom\":2,\"maskRoom\":false,\"hourlyRoom\":false,\"corpPrice\":false,\"manualGds\":false,\"joined30MinutesFreeCancel\":false,\"premiumRoom\":false,\"welfareRoom\":false,\"groupMemberShip\":false,\"earnHotelPoints\":false,\"bonusPointInfo\":{\"bonusPointRoom\":false},\"earlyArrivalTime\":\"2025-01-17T14:00:00+08\",\"lastCancelTime\":\"2025-01-17T18:00:00+08\",\"cnyAmount\":264.00,\"originAmountInfo\":{\"amount\":264.00,\"currency\":\"CNY\",\"exchange\":1},\"customAmountInfo\":{\"amount\":264.00,\"currency\":\"CNY\",\"exchange\":1},\"originCostInfo\":{\"amount\":256,\"currency\":\"CNY\",\"exchange\":1},\"originCostBeforeTaxInfo\":{\"amount\":256,\"currency\":\"CNY\",\"exchange\":1},\"roomDiscountInfo\":{\"promotionSwitchOn\":false,\"roomDailyDiscountInfo\":[],\"originPromotionAmount\":{\"price\":0,\"currency\":\"CNY\"},\"customPromotionAmount\":{\"price\":0,\"currency\":\"CNY\"}},\"roomDailyInfo\":[{\"effectDate\":\"2025-01-17T00:00:00+08\",\"cnyAmount\":264.00,\"amount\":264.00,\"customAmount\":264.00,\"afterPromotionCnyAmount\":264.00,\"afterPromotionCustomAmount\":264.00,\"cost\":256,\"costBeforeTax\":256,\"customizedCnyAmount\":0,\"mealNumber\":2,\"holdDeadline\":0,\"afterPromotionCustomAmountExcludeTax\":264.00,\"afterPromotionCnyAmountExcludeTax\":264.00,\"customAmountExcludeTax\":264.00,\"cnyAmountExcludeTax\":264.00}],\"paymentRulesInfo\":{\"paymentGuaranteePoly\":\"TRIP\",\"canTravelMoneyPay\":false,\"forceVccPay\":false,\"canCreditCardPay\":false},\"longRentalInfo\":{\"longRental\":false,\"needInquire\":false},\"holdRoomInfo\":{\"holdRoom\":false},\"agreementGiftsToken\":\"ChAIwaICEgpIWlFZMTg4Njk4\",\"extraPayTaxMultiCurrencyAmountInfo\":{\"customExtraPayTaxTotalAmount\":0,\"cnyExtraPayTaxTotalAmount\":0},\"pricedRoom\":false,\"generalizedPremiumRoom\":false,\"customAmountExcludeTaxInfo\":{\"amount\":264.00,\"currency\":\"CNY\",\"exchange\":1},\"supportAnticipation\":false,\"roomRightInfoList\":[],\"invoiceTypeExcludeAddPrice\":\"Special\",\"roomMealInfo\":{\"mealType\":4,\"mealDescList\":[\"2份早餐\"],\"dailyMealInfoList\":[{\"effectDateUTC\":\"2025-01-16T16:00:00Z\",\"mealDescList\":[\"\"]},{\"effectDateUTC\":\"2025-01-17T16:00:00Z\",\"mealDescList\":[\"2份早餐\"]}]},\"saleRoomTags\":[{\"tagCode\":\"ZXJ\",\"tagName\":\"尊享价\",\"tagDesc\":\"携程商旅客户专享价，更贴近商旅出行需求\",\"configInfoList\":[{\"position\":301,\"priority\":6,\"style\":\"EXCLUSIVE_ENHANCED\"}]}],\"taxDetails\":[],\"pid\":\"AQoGMjY0LjAwEgYyNjQuMDAieQp3djFfQ0ttaVBCRHcrSXZkQkJvSE1USTJNamt5TkNJQ1NGb3FBbFJITWdNeU5qUTZBekkyTkVJTkNnTkRUbGtTQTBOT1dSb0JNVW9nWWpjME1EUXpZMlEzTVRFek5EQTROR0k1T1RnMk5HVTRZakZpT0dJd016az0qA0NOWTINCgNDTlkSA0NOWRoBMTINCgNDTlkSA0NOWRoBMToCCABCBwjpDxABGBFKBwjpDxABGBJQAVgAYgJIWmgOeAKAAamiPA\\u003d\\u003d\",\"onlyGroupMemberCanBook\":false},\"bookingRules\":{\"cancelPenalties\":{\"paymentGuaranteePoly\":\"TRIP\",\"cancelDeductDetail\":{\"originDeductionPrice\":{\"price\":264.00,\"currency\":\"CNY\"},\"customDeductionPrice\":{\"price\":264.00,\"currency\":\"CNY\"}},\"guaranteePolicyInfo\":{}},\"authorizationRestriction\":{\"latestAuthorizeTime\":\"2025-01-17T17:00:00+08\"},\"confirmRules\":{\"justifyConfirm\":true,\"forbidNotImConfirm\":false},\"welfareRoomRules\":{},\"limitDepartureDateTime\":{\"departureEnd\":\"2025-01-18T12:00:00+08\",\"departureEndUTC\":\"2025-01-18T04:00:00Z\"},\"certificateInfo\":{\"needCertificate\":false},\"minLOS\":1,\"cancelPolicyInfo\":{\"cancelType\":\"LIMIT\",\"freeCancelPolicySceneType\":\"NOT_SUPPORTED\",\"lastCancelTimeUTC\":\"2025-01-17T10:00:00Z\",\"guaranteePolicyInfo\":{\"guaranteeType\":\"FIRST_DAY_PREPAY\",\"paymentGuaranteePoly\":\"TRIP\",\"guaranteeReason\":\"NONE\",\"guaranteePriceInfo\":{\"originGuaranteePrice\":{\"price\":264.00,\"currency\":\"CNY\"},\"customGuaranteePrice\":{\"price\":264.00,\"currency\":\"CNY\"}}},\"localLastCancelTime\":\"2025-01-17 18:00:00\"},\"limitArrivalDateTime\":{\"arrivalFromUTC\":\"2025-01-17T06:00:00Z\",\"arrivalEndUTC\":\"2025-01-17T22:00:00Z\",\"defaultArrivalTimeEnd\":false,\"localEarlyArrivalTime\":\"2025-01-17 14:00:00\",\"localLastArrivalTime\":\"2025-01-18 06:00:00\"},\"rPLimitInfo\":{\"minLos\":1,\"maxLos\":0,\"minAdvancedBookingDays\":0,\"maxAdvancedBookingDays\":0},\"billingGuestInfo\":{\"guestsNameLanguages\":[\"zh\",\"en\"]},\"restrictRuleList\":[]},\"bookExtInfo\":{\"featureControlFlagMap\":{\"EnableMemberPoints\":false,\"EnableXMinutesFreeCancel\":false,\"BrandNewCustomer\":true,\"EnableShadowDiscount\":false,\"EnableYXH\":false,\"EnablePrepayDiscount\":false,\"EnableNewGroupMember\":false,\"ForbidMultiNationality\":false,\"NewUserCountryCode\":true,\"EnableCNYTWODecimal\":true,\"ShieldMaskRoom\":true,\"EnableAddPrice\":true,\"IgnoreSpecialCancelPolicyNotMatchingFilter\":false,\"StrictCancellationPolicy\":false,\"IgnoreRemitTax\":false,\"ShieldMultiplePrice\":false,\"Enable30MinuteFreeCancel\":false,\"EnablePromotionSubTag\":true,\"EnableHotelNewCustomer\":false,\"DisableIPBlock\":false,\"EnableDirectRate\":false,\"EnableOutputPackageRoom\":false,\"ShieldMaskNakedSale\":false,\"EnableMobileRate\":true,\"EnableMatchPolicyWithExtraPayTax\":false}},\"userRightsInfo\":{\"userChosenCouponInfo\":[],\"hotelNewCustomer\":true,\"userSelectedCountryCode\":\"\"},\"reservationToken\":\"AQgBEgoyMDI1LTAxLTE3GhcKAUMSAlBQGgRUUklQIgJIWkjw+IvdBCICCAAqBwoFemgtQ04\\u003d\",\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2025-01-15 17:01:57.697+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", QueryCheckAvailContextResponseType.class)
        accountInfo = WrapperOfAccount.builder()
                .accountInfo(generalSearchAccountInfoResponseType)
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsId")))
                        .build().getCheckAvailContextInfo()
        orderCreateRequestType = JsonUtil.fromJson("{\"integrationSoaRequestType\":{\"userInfo\":{\"userId\":\"**********\",\"corpId\":\"CHANGANQICHE\",\"groupId\":\"Gr_00007810\",\"pos\":\"CHINA\"},\"token\":\"276BF932991E361D1F54ECDCF2A0C1CA269BDE19C7FD31A2C4A9C3BBC67290AF\",\"language\":\"zh-CN\",\"requestId\":\"f8011a3ede1945d280abe5418f063d24\",\"sourceFrom\":\"H5\",\"transactionID\":\"TID.SEARCHHOTEL.1341.B3CC17CDC45B4CACB8E8CEC92CBBA541\",\"gatewayHost\":\"ct.ctrip.com\",\"timezoneOffsetMinutesNew\":\"480\",\"logIndices\":[{\"key\":\"groupId\",\"value\":\"Gr_00007810\"},{\"key\":\"timezoneOffsetMinutesNew\",\"value\":\"480\"},{\"key\":\"gatewayHost\",\"value\":\"ct.ctrip.com\"},{\"key\":\"pvid\",\"value\":\"155\"},{\"key\":\"subChannel\",\"value\":\"\"},{\"key\":\"CID\",\"value\":\"51121111411253159615\"},{\"key\":\"EID\"},{\"key\":\"CIP\",\"value\":\"2408:8256:408d:231f:549d:9649:4e7:8cf1\"},{\"key\":\"miceToken\",\"value\":\"\"},{\"key\":\"groupName\",\"value\":\"中国兵器装备集团有限公司\"},{\"key\":\"gatewayIdc\",\"value\":\"SHARB\"},{\"key\":\"traceID\",\"value\":\"\"},{\"key\":\"A\",\"value\":\"ordercheck\"},{\"key\":\"CorpID\",\"value\":\"CHANGANQICHE\"},{\"key\":\"C\",\"value\":\"corpbookinghotelbookservice\"},{\"key\":\"M\",\"value\":\"**********\"},{\"key\":\"sid\",\"value\":\"1341\"},{\"key\":\"S\",\"value\":\"15d2116a817d4abca07d0b4318c4fe19\"},{\"key\":\"T\",\"value\":\"276BF932991E361D1F54ECDCF2A0C1CA269BDE19C7FD31A2C4A9C3BBC67290AF\"},{\"key\":\"pos\",\"value\":\"CHINA\"},{\"key\":\"GE\",\"value\":\"1736931717469\"},{\"key\":\"sourceFrom\",\"value\":\"H5\"},{\"key\":\"corpName\",\"value\":\"重庆长安汽车股份有限公司\"},{\"key\":\"RID\",\"value\":\"f8011a3ede1945d280abe5418f063d24\"},{\"key\":\"TID\",\"value\":\"TID.SEARCHHOTEL.1341.B3CC17CDC45B4CACB8E8CEC92CBBA541\"},{\"key\":\"VID\",\"value\":\"1691836400844.15ug957\"}],\"ticket\":\"r/mn1Oe6zxr+RVGEN16IBoKMzoLk7/Oj5P3mtEqTF5LOGV0px+CCr3HHnh+F8MsGkB57vvMmDxqKx00XqwJzC044pLwxT2s60LjJLA0CJs4\\u003d\",\"transferInfo\":[{\"key\":\"iframe\",\"value\":\"F\"},{\"key\":\"channel\",\"value\":\"Native\"}]},\"hotelBookPassengerInputs\":[{\"hotelPassengerInput\":{\"roomIndex\":1,\"uid\":\"**********\",\"approvalPassengerId\":\"6805233074116937788\",\"employee\":\"T\",\"approvalInput\":{\"subApprovalNo\":\"2024002183361\"},\"external\":\"F\",\"employeeId\":\"ca202226839\"},\"passengerBasicInfo\":{\"gender\":\"U\",\"birth\":\"1992-05-24\"},\"name\":\"杨智宇\",\"isRegistrant\":\"F\",\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"15924918666\",\"transferPhoneNo\":\"15924918666\"}}],\"hotelInvoiceInfos\":[],\"cityInput\":{\"cityId\":17},\"hotelContactorInfo\":{\"phoneInfo\":{\"countryCode\":\"86\",\"phoneNo\":\"15924918666\",\"transferPhoneNo\":\"15924918666\"},\"emailInfo\":{\"email\":\"<EMAIL>\",\"transferEmail\":\"<EMAIL>\"}},\"miceInput\":{},\"hotelBookInput\":{\"hotelDateRangeInfo\":{\"checkIn\":\"2025-01-17\",\"checkOut\":\"2025-01-18\"},\"roomQuantity\":1},\"membershipInfo\":{},\"bookModeInfo\":{\"bookingType\":\"NONE\"},\"costAllocationInfo\":{\"costAllocationToken\":\"H4sIAAAAAAAAAOMS4FhwjlGAQYJFic3IzETPwEBIjovLyNDCzNjExMzMSAhDXoqAvBIBeQCpidwocgAAAA\\u003d\\u003d\",\"shareAmounts\":[{\"costAllocationAmount\":{\"amount\":\"264\"},\"shareAmountKey\":\"**********\"}]},\"resourceTokenInfo\":{\"resourceToken\":\"{\\\"hotelResourceToken\\\":{\\\"masterHotelId\\\":987432,\\\"hotelId\\\":987433,\\\"searchHotelTraceId\\\":\\\"b74043cd71134084b99864e8b1b8b039\\\",\\\"hid\\\":\\\"ASIGMjM0LjAwQgFDSgYIABAAGABSaQpjdjFfQ0tpaVBCQ3BvandhQWxSSElnTXlNelFxQXpJek5ESU5DZ05EVGxrU0EwTk9XUm9CTVRvZ1lqYzBNRFF6WTJRM01URXpOREE0TkdJNU9UZzJOR1U0WWpGaU9HSXdNems9EAAYAFoDQ05ZYg0KA0NOWRIDQ05ZGgExYg0KA0NOWRIDQ05ZGgExaARyBjMwMC4wMHoEMC4wMIIBIGI3NDA0M2NkNzExMzQwODRiOTk4NjRlOGIxYjhiMDM5igEKMTI2ODk3MjY1OZoBJGQ2Nzk3MTY0LTEwYmUtNGZmYi04YzQ5LTM1M2Y4N2IxYWJiZqIBAggA\\\",\\\"hotelType\\\":\\\"M\\\",\\\"bonusPoint\\\":\\\"F\\\",\\\"hotelGeoInfoResourceToken\\\":{\\\"cityId\\\":17,\\\"locationId\\\":408}},\\\"roomResourceToken\\\":{\\\"roomId\\\":**********,\\\"baseRoomId\\\":2369593,\\\"ratePlanTraceLogId\\\":\\\"bf28d8228c4442dcb331198fc4447425_\\\",\\\"bedName\\\":\\\"1张1.8米大床\\\",\\\"windowName\\\":\\\"有窗\\\",\\\"breakfastInfo\\\":\\\"2份早餐\\\",\\\"confirmType\\\":\\\"IMMEDIATE\\\",\\\"roomType\\\":\\\"M\\\",\\\"balanceType\\\":\\\"PP\\\",\\\"tmcPrice\\\":true,\\\"adult\\\":2,\\\"pId\\\":\\\"AQoGMjY0LjAwEgYyNjQuMDAieQp3djFfQ0ttaVBCRHcrSXZkQkJvSE1USTJNamt5TkNJQ1NGb3FBbFJITWdNeU5qUTZBekkyTkVJTkNnTkRUbGtTQTBOT1dSb0JNVW9nWWpjME1EUXpZMlEzTVRFek5EQTROR0k1T1RnMk5HVTRZakZpT0dJd016az0qA0NOWTINCgNDTlkSA0NOWRoBMTINCgNDTlkSA0NOWRoBMToCCABCBwjpDxABGBFKBwjpDxABGBJQAVgAYgJIWmgOeAKAAamiPA\\\\u003d\\\\u003d\\\",\\\"amadeus\\\":false,\\\"supportAccommodation\\\":\\\"F\\\",\\\"roomOriginCurrency\\\":\\\"CNY\\\",\\\"roomScenarioEnum\\\":\\\"ROOM_LIST\\\",\\\"ratePlanRequestToken\\\":\\\"ASi1L/0AUOUWAOZxrz7wTqwAzMHlGIyvZREWJVAOLEqgvLCEqA2S0heKJUgdFGV1Y2EIhgjDsPF79NACAJLOMrkat5i+dtaS1Vv1P6AAnwCfAChvVUp1jhZLMlBwqbpgsNDmTDCNvrtJNGMoYfbtOdyhzJbMmb333mO7tjNXz0/FyldT4swMhqTpNiBmxSRlTSSxSM4SuQq/nORspLvtPmrGbcRQZXydTOUQ8dAVDhPvQf+GUMgdr4H/Y/i38Kt/L6wli2RYhKJh2kXyeLaD5ACQEPLIygrwgNtwbwx3F+4Nt9C0SmUykVQumXY5gMChw0Wc1FPCUjuloFGaMWYHG3e3gbSfnrFxHVITT3h1M5UScsoqS/eqTE4Kuwp6WNCJ9g3CbYtpkHTjljrVG3cyTiuncTWaq2EZzxNKhQHpZtEG5Fr1ZF3MdzFN9fOtpu5DtygVghy2OYhIwrJsGbMpxmvCZvNTuiz2WVrXq2iaCAK9u+UtqXWgMF4zCyW0z92lWCgRmKIgW4G4ssTyQVGArGUySqYtKht4bH8p+Ja8kZ5McfquOBU1326eeo8LPHY2kmCRLItINEwbmSVnjrZd4LH9heBbESKMJillypw+IIqKqazd4RMUuRZXkOFuJp2sGIQ/0Yq63K14IXjDHwd314Q5mJtKK40LiyKZM/GysqVJV9mqBptjzIQkyegLGU1vmDpdEpRsaE4h1eydVNTkyJizk50yiuGp6MNlInichKWobnJ8Cv0p9JPQD0L/B/0b/jPov6Dfgl7pV696ggZpj97oy2O6gT6gL3SF9igcIyocf+IvLgFfwvzHqXcyTvVNGvcN1ZPjXp56J1MUuiW4GHyIzZUk9xFH3obgsf9a8C3udeI38pCcHJ1cKOCx/yJgkmI58jaLR+avAd8CRZEcZUIOBEXOkSNvG/DYfxn4Vlrq/F1xMmm/cedpCQAGKeatUICogZANwE6Y3910Jv5PMb3sAA5FDw\\\\u003d\\\\u003d\\\",\\\"ratePlanRoomToken\\\":\\\"ASi1L/0AUHkAAAoBTRABGPD4i90EIgJQUA\\\\u003d\\\\u003d\\\",\\\"roomUniqueKey\\\":\\\"V1-ASi1L/0AUKEAAAjw+IvdBBIBQxoBTSICUFA4zIpN\\\",\\\"adultNumber\\\":2,\\\"windowType\\\":2},\\\"reservationResourceToken\\\":{\\\"wsId\\\":\\\"3f529776-9bf7-4499-996c-b378bd1492af\\\",\\\"policyToken\\\":\\\"v1KLUv/QBQDQoABhRBJRCpqgCOzCQzbD2csT2T+nGFb9DTEdFpS0hshAOoZAQEAAAAAA83ADYANgATau+uWEwUQUMSaEiL6e8XlVUK7+48z+FgeKDG+fsV73tl66jMaeMZsTuGAWAYw3vDWKuNsSsEjwpXXO2maE0y5L0jlSVTw0ip7ejrSjjj3a1+LChyIkgD8QIjGC0I6DksjdP08kQNGX7g32cHmGQ7ct2ElDEhmUJrlvGdlHe+Wy0OVGCUtLJxGmCUFlitb+Sub7XvEmiZYWljtuRgQmliMtw4CwmP7g7dHXH35wjvsilcH5O6KAvvRWu2TWlrOymNZjJygI2YdR2TYYWrLTKkrlVLi2fPSwMWADobh2OHw7XDfnPxzF2IKoSpOWioQuoCpgjqUKVqFkuoyvNZvAaq3KvYI4xaqgDODSDldWYOK5tN\\\"},\\\"bookInitResourceToken\\\":{\\\"aggBookPriceResourceToken\\\":{\\\"currency\\\":\\\"CNY\\\",\\\"accountPaymentAmount\\\":264.00,\\\"individualPaymentAmount\\\":0,\\\"mixPayAccountExtraRatio\\\":0,\\\"mixPayAccountExtraAmount\\\":0},\\\"roomPriceResourceToken\\\":{},\\\"cancelTip\\\":\\\"2025年1月17日18:00前可免费取消。若未入住或过时取消将收取您首日房费¥264（若用优惠券则以券后支付价为准），修改结果请以酒店回复结果为准。携程会根据您的付款方式扣除房费，订单需等酒店或供应商确认后生效，如订单不确认将全额退款至您的付款账户，订单确认结果以携程短信或邮件通知为准。\\\"}}\"},\"corpPayInfo\":{\"corpPayType\":\"public\"},\"userActionInfo\":{\"userActionToken\":\"PRD-3940-4543f2ec-2733-4149-99ae-af63291c5506-********-4.1.15-WEB\"},\"rcInfos\":[],\"bookUrlInfos\":[{\"urlType\":\"SUCCESS\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayState/PayState?site\\u003d2\\u0026LimitStandard\\u003dfalse\\u0026needFinishResult\\u003dtrue\\u0026\"},{\"urlType\":\"S_BACK\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayState/PayState?site\\u003d2\\u0026LimitStandard\\u003dfalse\\u0026needFinishResult\\u003dtrue\\u0026\"},{\"urlType\":\"E_BACK\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayMent/PayMent\"},{\"urlType\":\"FROM_URL\",\"urlValue\":\"https://ct.ctrip.com/m/dy_3_PayMent/PayMent/PayMent?leomacmd://navi?%7b%22backward%22%3atrue%2c%22navi%22%3a0%2c%22now%22%3atrue%2c%22platform%22%3a%7b%7d%7d\"},{\"urlType\":\"S_BACK_MICE\",\"urlValue\":\"https://ct.ctrip.com/mice-activities/welfareTourism\"},{\"urlType\":\"FROM_URL_ORDER_DETAIL\",\"urlValue\":\"https://ct.ctrip.com/webapp/hotel/process/detail?orderId\\u003d{0}\\u0026language\\u003d{1}\\u0026from\\u003dfinish\"},{\"urlType\":\"OFFLINE_RETURN_URL\",\"urlValue\":\"http://corpint.ct.ctripcorp.com/hotel-online/offlineOrder/success\"},{\"urlType\":\"ORDER_DETAIL_URL\",\"urlValue\":\"https://ct.ctrip.com/m/Detail/Hotel/{0}\"}],\"paymentInfoInput\":{\"canWechatPayByOthers\":\"T\",\"clientType\":\"APP\"},\"clientInfo\":{\"clientType\":\"APP\",\"rmsToken\":\"fp\\u003d\\u0026vid\\u003d1691836400844.15ug957\\u0026pageId\\u003d10650065356\\u0026r\\u003d90eab5c8020b40909662b0ab83a9d67a\\u0026ip\\u003dundefined\\u0026rg\\u003dundefined\\u0026screen\\u003d440x956\\u0026tz\\u003d+8\\u0026blang\\u003dzh-CN\\u0026oslang\\u003dzh-CN\\u0026ua\\u003dMozilla%2F5.0%20(iPhone%3B%20CPU%20iPhone%20OS%2018_1_1%20like%20Mac%20OS%20X)%20AppleWebKit%2F605.1.15%20(KHTML%2C%20like%20Gecko)%20Mobile%2F22B91%2Cios%2CCoreInside%2C18.1.1%2CiPhone%2016%20Pro%20Max%2C9.60.0%2C960.000.002%2CCorpCtrip%2CStatusBar%2CScreenFringe%2CiPhoneX%2CWIFI%2Ccompany%20Titans%2F9.60.0%20ScreenWidth%2F440%20ScreenHeight%2F956%20scale%2F3%20SafeAreaTop%2F62%20SafeAr\\u0026v\\u003dm17\\u0026bl\\u003dfalse\\u0026clientid\\u003d\",\"secondaryChannel\":\"iphone9.60.0-new\"},\"offlineInfo\":{\"forciblyAssure\":\"F\",\"especialAuth\":\"F\",\"autoConfirm\":\"F\",\"exigentOrder\":\"F\"},\"roomInfoInput\":{},\"sendInfo\":{\"sendSMS\":\"F\",\"sendEmail\":\"F\"},\"flashStayInput\":{\"flashPlatFrom\":\"APP\"},\"hotelPayTypeInput\":[{\"payCode\":\"ROOM\",\"payType\":\"CORP_PAY\"}],\"strategyInfos\":[{\"strategyKey\":\"BOOKING_SCENARIO\",\"strategyValue\":\"HOTEL_LIST\"},{\"strategyKey\":\"BOOKING_WITH_PERSONAL_ACCOUNT\",\"strategyValue\":\"F\"}],\"costCenterInfo\":{\"costCenterJsonString\":\"{\\\"items\\\":{\\\"**********\\\":{\\\"name\\\":\\\"杨智宇\\\",\\\"cost1\\\":\\\"1091-2110010-********-0-0-0\\\",\\\"cost2\\\":\\\"重庆长安新能源汽车科技有限公司-营销服务中心-销售费用-差旅费-一般国内差旅-null-0-0\\\",\\\"cost3\\\":\\\"2110010\\\",\\\"cost4\\\":\\\"新能源科技公司—营销服务中心\\\",\\\"cost5\\\":\\\"002\\\"},\\\"fdefault\\\":{}}}\"},\"useNewOrderCreate\":\"T\"}",OrderCreateRequestType.class)
        orderCreateRequestType.cityInput.cityId = 22249
        matchCostCenterResponseType = JsonUtil.fromJson("{\"costCenterDefaultTitles\":[{\"costCenterDefaultType\":\"TP\",\"costCenterDefaultTitle\":\"出行目的\",\"costCenterDefaultTitleEn\":\"JounaryReason\"},{\"costCenterDefaultType\":\"PN\",\"costCenterDefaultTitle\":\"项目号\",\"costCenterDefaultTitleEn\":\"Project\"},{\"costCenterDefaultType\":\"FT\",\"costCenterDefaultTitle\":\"机票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of flight\"},{\"costCenterDefaultType\":\"HT\",\"costCenterDefaultTitle\":\"酒店关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of hotel\"},{\"costCenterDefaultType\":\"TT\",\"costCenterDefaultTitle\":\"火车票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of train\"},{\"costCenterDefaultType\":\"CT\",\"costCenterDefaultTitle\":\"用车关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No. of car\"},{\"costCenterDefaultType\":\"BT\",\"costCenterDefaultTitle\":\"汽车票关联行程号\",\"costCenterDefaultTitleEn\":\"Associated itinerary No.\"},{\"costCenterDefaultType\":\"D1\",\"costCenterDefaultTitle\":\"自定义字段1\",\"costCenterDefaultTitleEn\":\"Self-defined title No.1\"},{\"costCenterDefaultType\":\"D2\",\"costCenterDefaultTitle\":\"自定义字段2\",\"costCenterDefaultTitleEn\":\"Self-defined title No.2\"},{\"costCenterDefaultType\":\"CC1\",\"costCenterDefaultTitle\":\"成本中心1\",\"costCenterDefaultTitleEn\":\"Cost center No.1\"},{\"costCenterDefaultType\":\"CC2\",\"costCenterDefaultTitle\":\"成本中心2\",\"costCenterDefaultTitleEn\":\"Cost center No.2\"},{\"costCenterDefaultType\":\"CC3\",\"costCenterDefaultTitle\":\"成本中心3\",\"costCenterDefaultTitleEn\":\"Cost center No.3\"},{\"costCenterDefaultType\":\"CC4\",\"costCenterDefaultTitle\":\"成本中心4\",\"costCenterDefaultTitleEn\":\"Cost center No.4\"},{\"costCenterDefaultType\":\"CC5\",\"costCenterDefaultTitle\":\"成本中心5\",\"costCenterDefaultTitleEn\":\"Cost center No.5\"},{\"costCenterDefaultType\":\"CC6\",\"costCenterDefaultTitle\":\"成本中心6\",\"costCenterDefaultTitleEn\":\"Cost center No.6\"}],\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2025-01-15 17:01:57.730+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", MatchCostCenterResponseType.class)
        costCenterInfoType = Optional.ofNullable(matchCostCenterResponseType.getResult()).orElse(new CostCenterInfoType())
        createOrderResponseType = JsonUtil.fromJson("{\"wsId\":\"3f529776-9bf7-4499-996c-b378bd1492af\",\"orderId\":1132534040640787,\"orderPaymentInfo\":{\"payType\":\"ACCNT\",\"orderAmountInfo\":{\"cnyTotalAmount\":264.00,\"paymentItemList\":[{\"prepayType\":\"ACCNT\",\"gatheringType\":\"D\",\"feeType\":\"HotelRoomFee\",\"amountInfo\":{\"amount\":264.00,\"currency\":\"CNY\",\"exchangeRateToCNY\":1}}]}},\"success\":true,\"supportPaymentBill\":true,\"paymentTransactionId\":\"310876171552653363\",\"orderTotalAmount\":{\"orderAmount\":{\"originAmount\":{\"amount\":264.00,\"currency\":\"CNY\",\"exchange\":1},\"cNYAmount\":264.00},\"paymentBillInfo\":{\"payAmount\":264.00,\"payCNYAmount\":264.00,\"payCurrency\":\"CNY\",\"payExchange\":1}},\"transactionInfo\":{\"transactionId\":\"d2c85aaa-9f49-4c5c-bc10-07a62c27801c\",\"subTransactionIdList\":[{\"payType\":\"ACCNT\",\"subTransactionId\":\"9f4c79b6-1889-46e1-906c-9b83e6820412\"}]},\"responseCode\":20000,\"responseStatus\":{\"timestamp\":\"2025-01-15 17:01:59.210+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}", CreateOrderResponseType.class)
        resultNew = mapper.buildSaveOrderCostCenterRequest(orderCreateRequestType, accountInfo, checkAvailInfo, orderCreateToken, createOrderResponseType, costCenterInfoType, approvalTextInfoResponseType, ssoInfoQueryResponseType, resourceToken, null, null)
        then:
        resultNew != null
        resultNew.getSaveList().size() == 1
    }


    def "testBuildCostCenterExtItem"() {
        given: "Mock dependencies and inputs"
        def mapper = new MapperOfSaveOrderCostCenterRequestType()
        def costCenterInfoResponse = new CostCenterInfoType()
        def costCenterResourceEnum = CostCenterResourceEnum.COST_CENTER_CC_1
        def costCenterValue = "TestValue"
        def orderCreateRequestType = new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(language: "zh-CN"))
        orderCreateRequestType.integrationSoaRequestType.language = "zh-CN"

        when: "Calling buildCostCenterExtItem method"
        def result = mapper.buildCostCenterExtItem(costCenterInfoResponse, costCenterResourceEnum, costCenterValue, orderCreateRequestType)

        then: "Assert the expected outcomes"
        result == null
    }

    @Unroll
    def "testBuildNeedCostCenterOrder with #description"() {
        given: "Mock dependencies and inputs"
        def mapper = new MapperOfSaveOrderCostCenterRequestType()
        def orderCostCenterInputItemOrder = new SaveCostCenterInputItem(
                cost1: cost1, cost2: cost2, cost3: cost3, cost4: cost4, cost5: cost5, cost6: cost6,
                travelPurpose: travelPurpose, projectNo: projectNo, journeyNo: journeyNo
        )

        expect: "Calling buildNeedCostCenterOrder method returns expected result"
        mapper.buildNeedCostCenterOrder(orderCostCenterInputItemOrder) == expectedResult

        where: "Different scenarios for testing"
        description                 | cost1 | cost2 | cost3 | cost4 | cost5 | cost6 | travelPurpose | projectNo | journeyNo || expectedResult
        "all null values"           | null  | null  | null  | null  | null  | null  | null          | null      | null      || false
        "cost1 is not null"         | "val" | null  | null  | null  | null  | null  | null          | null      | null      || true
        "cost2 is not null"         | null  | "val" | null  | null  | null  | null  | null          | null      | null      || true
        "travelPurpose is not null" | null  | null  | null  | null  | null  | null  | "purpose"     | null      | null      || true
        "projectNo is not null"     | null  | null  | null  | null  | null  | null  | null          | "proj"    | null      || true
        "journeyNo is not null"     | null  | null  | null  | null  | null  | null  | null          | null      | "journey" || true
        "multiple non-null values"  | "val" | "val" | null  | null  | null  | null  | "purpose"     | "proj"    | "journey" || true
    }

    @Unroll
    def "buildNeedCostCenter with #description"() {
        given: "Mock dependencies and inputs"
        expect: "Calling buildNeedCostCenter method returns expected result"
        mapper.buildNeedCostCenter(costCenterInfoType, saveCostCenterInputVo, null) == expectedResult

        where: "Different scenarios for testing"
        description               | costCenterInfoType | saveCostCenterInputVo      || expectedResult
        "null costCenterInfoType" | null               | null                       || false
        "null costCenterInfoType" | null               | new SaveCostCenterInputVo(items: [
                "fdefault": new SaveCostCenterInputItem(cost1: "value1"),
                "78978979": new SaveCostCenterInputItem(cost2: "value2")
        ])                                                                          || true
    }
}
