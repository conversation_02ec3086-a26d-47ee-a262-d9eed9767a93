package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CostCenterInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PassengerBasicInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.SSOInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.TripInput
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple6
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2025/6/4 22:39
 *
 */
class MapperOfCostCenterCheckRequestTypeTest extends Specification {

    def tester = Spy(MapperOfCostCenterCheckRequestType)

    def savePoint = new SavePoint()
    void cleanup() {
        savePoint.rollback()
    }

    def "convert"() {
        given:
        new MockUp<OrderCreateProcessorOfUtil>() {
            @Mock
            public static boolean needCostCenterNew(ResourceToken resourceToken, CorpPayInfo corpPayInfo,
                                                    WrapperOfAccount.AccountInfo accountInfo, Map<String, StrategyInfo> strategyInfoMap) {
                return false
            }
        }
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                corpPayInfo: new CorpPayInfo(corpPayType: "public"),
                cityInput: new CityInput(cityId: 7897),
                tripInput: new TripInput(tripId: "12345"),
                approvalInput: new ApprovalInput(subApprovalNo: "subApprovalNo"),
                ssoInput: new SSOInput(ssoKey: "testSsoKey"),
                hotelPolicyInput: new HotelPolicyInput(policyInput: new PolicyInput(policyUid: "policyUid")),
                costCenterInputs: [new CostCenterInput(key: "costCenterKey", value: "costCenterValue")],
                hotelBookPassengerInputs: [
                        new HotelBookPassengerInput(
                                passengerBasicInfo: new PassengerBasicInfo(preferLastName: "preferLastName", preferFirstName: "preferFirstName"),
                                hotelPassengerInput: new HotelPassengerInput(uid:  "678", employee: "T"))],
        )
        ResourceToken resourceToken = new ResourceToken()
        WrapperOfCheckAvail.BaseCheckAvailInfo baseCheckAvailInfo = Mock(WrapperOfCheckAvail.BaseCheckAvailInfo) {
        }
        def tuple = Tuple6.of(
                orderCreateRequestType,
                resourceToken,
                baseCheckAvailInfo,
                null, null, null
        )
        when:
        def request = tester.convert(tuple as Tuple6<OrderCreateRequestType, ResourceToken, WrapperOfCheckAvail.BaseCheckAvailInfo, QconfigOfCertificateInitConfig, WrapperOfAccount.AccountInfo, Map<String, StrategyInfo>>)
        then:
        request != null
        request.tripInput.tripId == "12345"
        request.approvalInput.subApprovalNo == "subApprovalNo"
        request.getSSOInput().ssoKey == "testSsoKey"
        request.getPolicyInput().policyUid == "policyUid"
        request.productType == "INTERNATIONAL_HOTEL"
        request.costCenterInputs.size() == 1
        request.costCenterInputs[0].key == "costCenterKey"
        request.costCenterInputs[0].value == "costCenterValue"
        request.costCenterPassengers.size() == 1
        request.costCenterPassengers[0].passengerName == "preferLastName/preferFirstName"
        request.costCenterPassengers[0].uid == "678"
        request.costCenterPassengers[0].employee == "T"
    }

}
