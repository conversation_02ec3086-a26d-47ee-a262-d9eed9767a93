package com.ctrip.corp.bff.hotel.book.common.util

import spock.lang.Specification

import java.time.LocalDateTime

class DateUtilsTest extends Specification {
    void setup() {
    }

    void cleanup() {
    }

    def "durationHour" () {
        expect:
        DateUtils.durationHour(LocalDateTime.of(2025, 5, 13, 22, 30), LocalDateTime.of(2025, 5, 13, 23, 59)) == "1.4"
        DateUtils.durationHour(LocalDateTime.of(2025, 5, 13, 22, 00), LocalDateTime.of(2025, 5, 13, 23, 59)) == "1.9"
        DateUtils.durationHour(LocalDateTime.of(2025, 5, 13, 22, 00), LocalDateTime.of(2025, 5, 13, 23, 00)) == "1"
        DateUtils.durationHour(LocalDateTime.of(2025, 5, 13, 22, 00), LocalDateTime.of(2025, 5, 13, 22, 00)) == "0"
    }
}
