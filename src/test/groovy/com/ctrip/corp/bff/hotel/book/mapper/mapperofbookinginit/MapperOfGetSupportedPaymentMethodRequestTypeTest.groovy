package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.AddPriceInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.AmountInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import com.ctrip.corp.hotelbook.commonws.entity.PriceType
import com.ctrip.corp.order.data.aggregation.query.contract.HotelInfoType
import com.ctrip.corp.order.data.aggregation.query.contract.HotelOrderType
import com.ctrip.corp.order.data.aggregation.query.contract.OrderBasicInfoType
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataResponseType
import com.ctrip.corp.order.data.aggregation.query.contract.ServiceFeeConfigType
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple6;

/**
 * <AUTHOR>
 * @date 2024/12/4 16:14
 *
 */
class MapperOfGetSupportedPaymentMethodRequestTypeTest extends Specification {

    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }

    @Unroll
    def "testBuildPriceType with different scenarios"() {
        given:
        MapperOfGetSupportedPaymentMethodRequestType mapper = new MapperOfGetSupportedPaymentMethodRequestType()

        when: "calling buildPriceType with specific parameters"
        def result = mapper.buildPriceType(price, currency)

        then: "the result should be as expected"
        result == expectedResult

        where:
        price                    | currency || expectedResult
        new BigDecimal("100.00") | "USD"    || new PriceType(price: new BigDecimal("100.00"), currency: "USD")
        new BigDecimal("200.50") | "EUR"    || new PriceType(price: new BigDecimal("200.50"), currency: "EUR")
        null                     | "USD"    || null
        new BigDecimal("0.00")   | "JPY"    || new PriceType(price: new BigDecimal("0.00"), currency: "JPY")
    }


    @Unroll
    def "testBuildAmsFeeConfigVersion with modify"() {
        given:
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo)
        QueryHotelOrderDataResponseType queryHotelOrderDataResponseType = new QueryHotelOrderDataResponseType(
                hotelInfo: new HotelInfoType(
                        hotelOrder: new HotelOrderType(
                                serviceFeeConfig: new ServiceFeeConfigType(amsFeeConfigVersion: "v2-snapshot")
                        )
                )
        )
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType()
        bookingInitRequestType.setStrategyInfos(List.of(new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "MODIFY")))
        MapperOfGetSupportedPaymentMethodRequestType mapper = new MapperOfGetSupportedPaymentMethodRequestType()

        when:

        when:
        def result = mapper.buildAmsFeeConfigVersion(accountInfo, queryHotelOrderDataResponseType, bookingInitRequestType)

        then:
        result == "v2-snapshot"


    }


    def "buildAddPriceAmount" () {
        expect:
        new MapperOfGetSupportedPaymentMethodRequestType().buildAddPriceAmount(null, null, false, null) == null
        new MapperOfGetSupportedPaymentMethodRequestType().buildAddPriceAmount(null, null, true, null) == null
        new MapperOfGetSupportedPaymentMethodRequestType().buildAddPriceAmount(null, new AddPriceInput(), true, null) == null
        new MapperOfGetSupportedPaymentMethodRequestType().buildAddPriceAmount(null, new AddPriceInput(amountInfo: new AmountInfo()), true, null) == null
        new MapperOfGetSupportedPaymentMethodRequestType().buildAddPriceAmount(null, new AddPriceInput(amountInfo: new AmountInfo(amount: "2")), true, new HotelBookInput(hotelDateRangeInfo: new HotelDateRangeInfo(checkIn: "2025-05-01", checkOut: "2025-05-02"), roomQuantity: 1)).price == 2
        new MapperOfGetSupportedPaymentMethodRequestType().buildAddPriceAmount("CNY", new AddPriceInput(amountInfo: new AmountInfo(amount: "-1")), true,  new HotelBookInput(hotelDateRangeInfo: new HotelDateRangeInfo(checkIn: "2025-05-01", checkOut: "2025-05-02"), roomQuantity: 1)) == null
        new MapperOfGetSupportedPaymentMethodRequestType().buildAddPriceAmount("CNY", new AddPriceInput(amountInfo: new AmountInfo(amount: "2")), true,  new HotelBookInput(hotelDateRangeInfo: new HotelDateRangeInfo(checkIn: "2025-05-01", checkOut: "2025-05-02"), roomQuantity: 1)).price == 2
        new MapperOfGetSupportedPaymentMethodRequestType().buildAddPriceAmount("CNY", new AddPriceInput(amountInfo: new AmountInfo(amount: "2")), true,  new HotelBookInput(hotelDateRangeInfo: new HotelDateRangeInfo(checkIn: "2025-05-01", checkOut: "2025-05-02"), roomQuantity: -1))?.price == null
        new MapperOfGetSupportedPaymentMethodRequestType().buildAddPriceAmount("CNY", new AddPriceInput(amountInfo: new AmountInfo(amount: "2")), true,  new HotelBookInput(hotelDateRangeInfo: new HotelDateRangeInfo(checkIn: "2025-05-01", checkOut: "2025-05-02"), roomQuantity: 2))?.price == 4
    }

    def "convert" () {
        expect:
        new MapperOfGetSupportedPaymentMethodRequestType().convert(Tuple6.of(new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "private"),integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo())), Mock(WrapperOfCheckAvail.CheckAvailInfo), null, Mock(WrapperOfAccount.AccountInfo), null, null)).scene == "FILL_ORDER"
    }

    def "buildOriOrderInfoType" () {
        expect:
        new MapperOfGetSupportedPaymentMethodRequestType().buildOriOrderInfoType(null, new BookingInitRequestType()) == null
        new MapperOfGetSupportedPaymentMethodRequestType().buildOriOrderInfoType(new QueryHotelOrderDataResponseType(orderBasicInfo: new OrderBasicInfoType(orderId: 2)), new BookingInitRequestType(strategyInfos: [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "MODIFY")])) .orderId == "2"
    }
}

