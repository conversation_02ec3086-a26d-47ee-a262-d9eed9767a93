package com.ctrip.corp.bff.hotel.book.service

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ResourceTokenInfo
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import mockit.internal.state.SavePoint
import spock.lang.Specification

class ServiceOfBookingInitTest extends Specification {

    def tester = Spy(new ServiceOfBookingInit())

    def savePoint = new SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "validateRequest" () {
        given:

        when:
        tester.validateRequest(req)
        throw new Success()

        then:
        thrown(ex)
        where:
        req | ex
        new BookingInitRequestType() | BusinessException
        new BookingInitRequestType(hotelBookInput: new HotelBookInput())  | BusinessException
        new BookingInitRequestType(hotelBookInput: new HotelBookInput(), corpPayInfo: new CorpPayInfo(corpPayType: "private")) | BusinessException
        new BookingInitRequestType(resourceTokenInfo: new ResourceTokenInfo(),hotelBookInput: new HotelBookInput(), corpPayInfo: new CorpPayInfo(corpPayType: "private")) | BusinessException
        new BookingInitRequestType(resourceTokenInfo: new ResourceTokenInfo(resourceToken: "a"),hotelBookInput: new HotelBookInput(), corpPayInfo: new CorpPayInfo(corpPayType: "private")) | Success
    }

    class Success extends Exception {}
}
