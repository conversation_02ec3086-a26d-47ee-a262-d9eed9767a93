package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelGeoInfoResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import com.ctrip.corp.order.data.aggregation.query.contract.ApprovalType
import com.ctrip.corp.order.data.aggregation.query.contract.HotelInfoType
import com.ctrip.corp.order.data.aggregation.query.contract.HotelProductType
import com.ctrip.corp.order.data.aggregation.query.contract.HotelType
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataResponseType
import com.ctrip.corp.order.data.aggregation.query.contract.TravelInfoType
import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;

class MapperOfHotelSearchApprovalRequestTest extends Specification {

    def tester = new MapperOfHotelSearchApprovalRequest()
    
    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert"() {
        expect:
        tester.convert(Tuple4.of(new BookingInitRequestType(), new QueryHotelOrderDataResponseType(travelInfo: new TravelInfoType(approval: new ApprovalType(subPreApprovalNo: "a"))), null, Mock(WrapperOfAccount.AccountInfo))) .approvalInput.subApprovalNo == "a"
    }

    def "getCityInput" () {
        expect:
        tester.getCityInput(null, null).cityId == -1
        tester.getCityInput(new QueryHotelOrderDataResponseType(hotelInfo: new HotelInfoType(hotelProduct: new HotelProductType(hotel: new HotelType(cityId: 1)))), null).cityId == 1
        tester.getCityInput(null, new ResourceToken(hotelResourceToken: new HotelResourceToken(hotelGeoInfoResourceToken: new HotelGeoInfoResourceToken(cityId: 2)))).cityId == 2
    }

    def "getMainApprovalNo" () {
        expect:
        tester.getMainApprovalNo(null) == null
        tester.getMainApprovalNo(new QueryHotelOrderDataResponseType()) == null
        tester.getMainApprovalNo(new QueryHotelOrderDataResponseType(travelInfo: new TravelInfoType())) == null
        tester.getMainApprovalNo(new QueryHotelOrderDataResponseType(travelInfo: new TravelInfoType(approval: new ApprovalType()))) == null
        tester.getMainApprovalNo(new QueryHotelOrderDataResponseType(travelInfo: new TravelInfoType(approval: new ApprovalType(prepareApprovalNo: "a"))))  == "a"
    }

    def "getSubApprovalNo" () {
        expect:
        tester.getSubApprovalNo(null) == null
        tester.getSubApprovalNo(new QueryHotelOrderDataResponseType()) == null
        tester.getSubApprovalNo(new QueryHotelOrderDataResponseType(travelInfo: new TravelInfoType())) == null
        tester.getSubApprovalNo(new QueryHotelOrderDataResponseType(travelInfo: new TravelInfoType(approval: new ApprovalType()))) == null
        tester.getSubApprovalNo(new QueryHotelOrderDataResponseType(travelInfo: new TravelInfoType(approval: new ApprovalType(subPreApprovalNo: "b"))))  == "b"
    }
}
