package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response

import com.ctrip.corp.bff.mice.basic.auth.contract.TmsCreateOrderVerifyResponseType
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;

class MapperOfMiceControlResponseTest extends Specification {

    def tester = Spy(new MapperOfMiceControlResponse())

    def savePoint = new mockit.internal.state.SavePoint()


    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "check" () {
        expect:
        !tester.check(Tuple1.of(null)) .result
        !tester.check(Tuple1.of(new TmsCreateOrderVerifyResponseType())) .result
        tester.check(Tuple1.of(new TmsCreateOrderVerifyResponseType(activityDetailUrl: [null])))  == null
    }
}
