package com.ctrip.corp.bff.hotel.book.common.util

import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.hotel.book.contract.BookUrlInfo
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @description
 * @date 2025/3/18
 */
class CompletionPageUtilTest extends Specification {
    def savePoint = new SavePoint()

    void setup() {
        new MockUp<WrapperOfAccount.AccountInfo>() {
            @Mock
            public boolean bookPolicyPsgMustSameTripApprove(Boolean isOverSea, CorpPayInfo corpPayInfo) {
                if (isOverSea) {
                    return false;
                }
                return true;
            }

            @Mock
            public boolean preApprovalSameTrip(String corpId, Boolean oversea, CorpPayInfo corpPayInfo) {
                if (corpId == "true") {
                    return true;
                }
                return false;
            }

            @Mock
            public boolean isCCardCanNotBookingForNoEmp() {
                return false;
            }
        }
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "buildCompletionPageUrl returns correct new completion page url when supported"() {
        given:
        OrderCreateRequestType orderCreateRequestType = Mock(OrderCreateRequestType) {
            getIntegrationSoaRequestType() >> Mock(IntegrationSoaRequestType) {
                getSourceFrom() >> SourceFrom.Online
            }
            getBookUrlInfos() >> [new BookUrlInfo("COMPLETION", "http://completion.url")]
        }
        Long orderId = 12345L

        and:
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                true
            }
        }

        when:
        String result = CompletionPageUtil.buildCompletionPageUrl(orderCreateRequestType, orderId)

        then:
        result == "http://completion.url?orderId=12345&orderNumber=12345"
    }

    def "buildCompletionPageUrl returns null when new completion page is not supported"() {
        given:
        OrderCreateRequestType orderCreateRequestType = Mock(OrderCreateRequestType) {
            getIntegrationSoaRequestType() >> Mock(IntegrationSoaRequestType) {
                getSourceFrom() >> SourceFrom.Online
            }
        }
        Long orderId = 12345L

        and:
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            boolean isSupport(String key, boolean defaultValue) {
                return false
            }
        }

        when:
        String result = CompletionPageUtil.buildCompletionPageUrl(orderCreateRequestType, orderId)

        then:
        result == null
    }

    def "buildCompletionPageUrl returns null when orderCreateRequestType is null"() {
        when:
        String result = CompletionPageUtil.buildCompletionPageUrl(null, 12345L)

        then:
        result == null
    }

    def "buildSBackUrl returns correct sBack url when new completion page is not supported"() {
        given:
        OrderCreateRequestType orderCreateRequestType = Mock(OrderCreateRequestType) {
            getBookUrlInfos() >> [new BookUrlInfo("COMPLETION", "http://completion.url"), new BookUrlInfo("S_BACK", "http://sback.url")]
            getIntegrationSoaRequestType() >> Mock(IntegrationSoaRequestType) {
                getSourceFrom() >> SourceFrom.Online
            }
        }
        Long orderId = 12345L

        when:
        String result = CompletionPageUtil.buildSBackUrl(orderCreateRequestType, orderId)

        then:
        result == "http://sback.url"
    }

    def "buildSBackUrl returns correct completion page url when new completion page is supported"() {
        given:
        OrderCreateRequestType orderCreateRequestType = Mock(OrderCreateRequestType) {
            getBookUrlInfos() >> [new BookUrlInfo("COMPLETION", "http://completion.url"), new BookUrlInfo("S_BACK", "http://sback.url")]
            getIntegrationSoaRequestType() >> Mock(IntegrationSoaRequestType) {
                getSourceFrom() >> SourceFrom.Online
            }
        }
        Long orderId = 12345L

        and:
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                true
            }
        }

        when:
        String result = CompletionPageUtil.buildSBackUrl(orderCreateRequestType, orderId)

        then:
        result == "http://completion.url?orderId=12345&orderNumber=12345&"
    }

    def "buildSBackUrl returns null when orderCreateRequestType is null"() {
        when:
        String result = CompletionPageUtil.buildSBackUrl(null, 12345L)

        then:
        result == null
    }

    def "buildSBackUrl returns null when bookUrlInfos is empty"() {
        given:
        OrderCreateRequestType orderCreateRequestType = Mock(OrderCreateRequestType) {
            getBookUrlInfos() >> []
        }
        Long orderId = 12345L

        when:
        String result = CompletionPageUtil.buildSBackUrl(orderCreateRequestType, orderId)

        then:
        result == null
    }

    def "buildSBackUrl returns null when both completionPageUrl and sBack are null"() {
        given:
        OrderCreateRequestType orderCreateRequestType = Mock(OrderCreateRequestType) {
            getBookUrlInfos() >> [new BookUrlInfo("COMPLETION", null), new BookUrlInfo("S_BACK", null)]
        }
        Long orderId = 12345L

        when:
        String result = CompletionPageUtil.buildSBackUrl(orderCreateRequestType, orderId)

        then:
        result == null
    }


    def "getNewCompletionPageUrl returns correct url for offline source"() {
        given:
        OrderCreateRequestType orderCreateRequestType = Mock(OrderCreateRequestType) {
            getBookUrlInfos() >> [new BookUrlInfo("COMPLETION", "http://completion.url")]
            getIntegrationSoaRequestType() >> Mock(IntegrationSoaRequestType) {
                getSourceFrom() >> SourceFrom.Offline
            }
        }
        Long orderId = 12345L

        when:
        String result = CompletionPageUtil.getNewCompletionPageUrl(orderCreateRequestType, orderId)

        then:
        result == "http://completion.url?orderId=12345&orderNumber=12345&offlineUid=null"
    }

    def "getNewCompletionPageUrl returns correct url for H5 source"() {
        given:
        OrderCreateRequestType orderCreateRequestType = Mock(OrderCreateRequestType) {
            getBookUrlInfos() >> [new BookUrlInfo("COMPLETION", "http://completion.url")]
            getIntegrationSoaRequestType() >> Mock(IntegrationSoaRequestType) {
                getSourceFrom() >> SourceFrom.H5
            }
        }
        Long orderId = 12345L

        when:
        String result = CompletionPageUtil.getNewCompletionPageUrl(orderCreateRequestType, orderId)

        then:
        result == "http://completion.url?orderId=12345&orderNumber=12345&from=backhome"
    }

    def "getNewCompletionPageUrl returns correct url for other sources"() {
        given:
        OrderCreateRequestType orderCreateRequestType = Mock(OrderCreateRequestType) {
            getBookUrlInfos() >> [new BookUrlInfo("COMPLETION", "http://completion.url")]
            getIntegrationSoaRequestType() >> Mock(IntegrationSoaRequestType) {
                getSourceFrom() >> SourceFrom.Online
            }
        }
        Long orderId = 12345L

        when:
        String result = CompletionPageUtil.getNewCompletionPageUrl(orderCreateRequestType, orderId)

        then:
        result == "http://completion.url?orderId=12345&orderNumber=12345"
    }

    def "getNewCompletionPageUrl returns null when orderCreateRequestType is null"() {
        when:
        String result = CompletionPageUtil.getNewCompletionPageUrl(null, 12345L)

        then:
        result == null
    }

    def "getNewCompletionPageUrl returns null when bookUrlInfos is empty"() {
        given:
        OrderCreateRequestType orderCreateRequestType = Mock(OrderCreateRequestType) {
            getBookUrlInfos() >> []
        }
        Long orderId = 12345L

        when:
        String result = CompletionPageUtil.getNewCompletionPageUrl(orderCreateRequestType, orderId)

        then:
        result == null
    }


    def "getUrlByType returns correct url when urlType matches"() {
        given:
        BookUrlInfo bookUrlInfo = new BookUrlInfo("COMPLETION", "http://completion.url")
        List<BookUrlInfo> bookUrlInfos = Collections.singletonList(bookUrlInfo)

        when:
        String result = CompletionPageUtil.getUrlByType(bookUrlInfos, "COMPLETION")

        then:
        result == "http://completion.url"
    }

    def "getUrlByType returns null when urlType does not match"() {
        given:
        BookUrlInfo bookUrlInfo = new BookUrlInfo("COMPLETION", "http://completion.url")
        List<BookUrlInfo> bookUrlInfos = Collections.singletonList(bookUrlInfo)

        when:
        String result = CompletionPageUtil.getUrlByType(bookUrlInfos, "S_BACK")

        then:
        result == null
    }

    def "getUrlByType returns null when bookUrlInfos is empty"() {
        given:
        List<BookUrlInfo> bookUrlInfos = Collections.emptyList()

        when:
        String result = CompletionPageUtil.getUrlByType(bookUrlInfos, "COMPLETION")

        then:
        result == null
    }

    def "getUrlByType returns null when urlType is empty"() {
        given:
        BookUrlInfo bookUrlInfo = new BookUrlInfo("COMPLETION", "http://completion.url")
        List<BookUrlInfo> bookUrlInfos = Collections.singletonList(bookUrlInfo)

        when:
        String result = CompletionPageUtil.getUrlByType(bookUrlInfos, "")

        then:
        result == null
    }

    def "getUrlByType returns null when bookUrlInfos is null"() {
        when:
        String result = CompletionPageUtil.getUrlByType(null, "COMPLETION")

        then:
        result == null
    }

    def "getUrlByType returns null when urlType is null"() {
        given:
        BookUrlInfo bookUrlInfo = new BookUrlInfo("COMPLETION", "http://completion.url")
        List<BookUrlInfo> bookUrlInfos = Collections.singletonList(bookUrlInfo)

        when:
        String result = CompletionPageUtil.getUrlByType(bookUrlInfos, null)

        then:
        result == null
    }


    def "buildUrlWithParams - 各种url和参数情况"() {
        expect:
        CompletionPageUtil.buildUrlWithParams(url, *params) == result

        where:
        url                          | params                                 || result
        // 无参数
        "http://abc.com/def"         | []                                     || "http://abc.com/def"
        "http://abc.com/def"         | [null]                                 || "http://abc.com/def"
        "http://abc.com/def"         | [""]                                   || "http://abc.com/def"
        // 基础拼接
        "http://abc.com/def"         | ["a=1"]                                || "http://abc.com/def?a=1"
        "http://abc.com/def"         | ["a=1", "b=2"]                         || "http://abc.com/def?a=1&b=2"
        // url已带?
        "http://abc.com/def?"        | ["a=1"]                                || "http://abc.com/def?a=1"
        "http://abc.com/def?"        | ["a=1", "b=2"]                         || "http://abc.com/def?a=1&b=2"
        // url已带参数
        "http://abc.com/def?a=1"     | ["b=2"]                                || "http://abc.com/def?a=1&b=2"
        "http://abc.com/def?a=1"     | ["&b=2"]                               || "http://abc.com/def?a=1&b=2"
        "http://abc.com/def?a=1"     | ["?b=2"]                               || "http://abc.com/def?a=1&b=2"
        "http://abc.com/def?a=1"     | ["b=2", "c=3"]                         || "http://abc.com/def?a=1&b=2&c=3"
        // url结尾&
        "http://abc.com/def?a=1&"    | ["b=2"]                                || "http://abc.com/def?a=1&b=2"
        // 参数带前缀
        "http://abc.com/def"         | ["&a=1", "?b=2"]                       || "http://abc.com/def?a=1&b=2"
        // 参数全空
        "http://abc.com/def"         | [null, "", "   "]                      || "http://abc.com/def"
        // url为null
        null                         | ["a=1"]                                || null
        // 参数为null
        "http://abc.com/def"         | null                                   || "http://abc.com/def"
    }

}
