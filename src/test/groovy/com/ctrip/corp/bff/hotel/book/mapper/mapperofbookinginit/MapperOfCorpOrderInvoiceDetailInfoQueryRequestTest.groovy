package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceDetailInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple6
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import corp.settlement.service.invoice.corpinvoice.CorpOrderInvoiceDetailInfoQueryRequest
import corp.settlement.service.invoice.corpinvoice.HotelFeeInfo
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2025/3/6 10:22
 *
 */
class MapperOfCorpOrderInvoiceDetailInfoQueryRequestTest extends Specification {
    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    @Unroll
    def "test convert with #description"() {
        given:
        MapperOfCorpOrderInvoiceDetailInfoQueryRequest mapper = new MapperOfCorpOrderInvoiceDetailInfoQueryRequest()
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
                corpPayInfo: new CorpPayInfo(corpPayType: "public"),
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "123456", userId: "654444")),
                policyInput: new PolicyInput(policyUid: "policy123"))
        ResourceToken resourceToken = new ResourceToken()
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isPolicyModel() >> true
        }
        accountInfo.getAccountId() >> 789789L
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        checkAvailInfo.getRoomType() >> "M"
        checkAvailInfo.getWsId() >> "transaction123"

        when:
        CorpOrderInvoiceDetailInfoQueryRequest result =
                mapper.convert(Tuple6.of(bookingInitRequestType, resourceToken, accountInfo, checkAvailInfo, HotelPayTypeEnum.SELF_PAY,
                        HotelPayTypeEnum.CORP_PAY))

        then:
        result != null
        result.getHotelBasicInfo().getCorpId() == "123456"
        result.getHotelBasicInfo().getBookUid() == "654444"
        result.getHotelBasicInfo().getAccountId() == 789789L
        result.getHotelBasicInfo().getHotelType() == "M"
        result.getHotelBasicInfo().getHotelClass() == "I"
        result.getHotelBasicInfo().getCorpPayType() == "Pub"
        result.getHotelBasicInfo().getHotelFeeInfoList().size() == 2
        result.getHotelBasicInfo().getPolicyUid() == "policy123"
        result.getHotelBasicInfo().getTransactionId() == "transaction123"
        result.getProductLine() == 2
    }


    @Unroll
    def "test buildHotelFeeInfoList with #roomPayType and #servicePayType"() {
        given:
        MapperOfCorpOrderInvoiceDetailInfoQueryRequest mapper = new MapperOfCorpOrderInvoiceDetailInfoQueryRequest()
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
                corpPayInfo: new CorpPayInfo(corpPayType: "public"),
                policyInput: new PolicyInput(policyUid: "policy123"))

        when:
        List<HotelFeeInfo> result = mapper.buildHotelFeeInfoList(roomPayType, servicePayType, bookingInitRequestType)

        then:
        result.size() == expectedSize
        result*.feeType.containsAll(expectedFeeTypes)
        result*.transactionFlag.containsAll(expectedTransactionFlags)

        where:
        roomPayType               | servicePayType            | expectedSize || expectedFeeTypes           | expectedTransactionFlags
        HotelPayTypeEnum.SELF_PAY | HotelPayTypeEnum.SELF_PAY | 2            || ["HotelFee", "ServiceFee"] | ["PERSONAL", "PERSONAL"]
        HotelPayTypeEnum.CORP_PAY | HotelPayTypeEnum.CORP_PAY | 2            || ["HotelFee", "ServiceFee"] | ["ACCNT", "ACCNT"]
        HotelPayTypeEnum.MIX_PAY  | HotelPayTypeEnum.CORP_PAY | 2            || ["HotelFee", "ServiceFee"] | ["MIX", "ACCNT"]
        HotelPayTypeEnum.CASH     | HotelPayTypeEnum.SELF_PAY | 1            || ["ServiceFee"]             | ["PERSONAL"]
        HotelPayTypeEnum.CASH     | HotelPayTypeEnum.CORP_PAY | 1            || ["ServiceFee"]             | ["ACCNT"]
    }

    @Unroll
    def "test buildInsuranceFeeInfo"() {
        given:
        MapperOfCorpOrderInvoiceDetailInfoQueryRequest mapper = new MapperOfCorpOrderInvoiceDetailInfoQueryRequest()
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
                hotelInsuranceInput: hotelInsuranceInput)
        when:
        HotelFeeInfo result = mapper.buildInsuranceFeeInfo(roomPayType, bookingInitRequestType)

        then:
        result.feeType == expectedFeeType
        result.transactionFlag == expectedTransactionFlag

        where:
        roomPayType               | hotelInsuranceInput                                                                    || expectedFeeType | expectedTransactionFlag
        HotelPayTypeEnum.SELF_PAY | new HotelInsuranceInput(hotelInsuranceDetailInputs: [new HotelInsuranceDetailInput()]) || "InsuranceFee"  | "PERSONAL"
        HotelPayTypeEnum.CORP_PAY | new HotelInsuranceInput(hotelInsuranceDetailInputs: [new HotelInsuranceDetailInput()]) || "InsuranceFee"  | "ACCNT"
        HotelPayTypeEnum.MIX_PAY  | new HotelInsuranceInput(hotelInsuranceDetailInputs: [new HotelInsuranceDetailInput()]) || "InsuranceFee"  | "ACCNT"
    }

    @Unroll
    def "test buildInsuranceFeeInfo null"() {
        given:
        MapperOfCorpOrderInvoiceDetailInfoQueryRequest mapper = new MapperOfCorpOrderInvoiceDetailInfoQueryRequest()
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(
                hotelInsuranceInput: hotelInsuranceInput)
        expect:
        result == mapper.buildInsuranceFeeInfo(roomPayType, bookingInitRequestType)

        where:
        roomPayType               | hotelInsuranceInput                                     || result
        HotelPayTypeEnum.SELF_PAY | new HotelInsuranceInput(hotelInsuranceDetailInputs: []) || null
        HotelPayTypeEnum.SELF_PAY | null                                                    || null
    }
}
