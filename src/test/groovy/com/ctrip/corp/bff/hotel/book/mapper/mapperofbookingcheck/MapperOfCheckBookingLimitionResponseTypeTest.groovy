package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookingcheck

import com.ctrip.corp.agg.hotel.checkbookinglimition.entity.CheckBookingLimitionResponseType
import com.ctrip.corp.agg.hotel.checkbookinglimition.entity.ResultType
import com.ctrip.corp.agg.hotel.checkbookinglimition.entity.TipInfoType
import com.ctrip.corp.bff.framework.template.common.serialize.JsonUtil
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.common.threadlocal.ThreadLocalProvider
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.hotel.book.qconfig.entity.CustomizedSharkConfig
import com.ctrip.corp.bff.hotel.book.service.BookingCheckRequestType
import com.ctrip.corp.bff.hotel.book.sharkmock.SharkMockUtil
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import org.mockito.InjectMocks
import org.mockito.MockitoAnnotations
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2024/11/27 13:26
 *
 */
class MapperOfCheckBookingLimitionResponseTypeTest extends Specification {
    @InjectMocks
    MapperOfCheckBookingLimitionResponseType test
    def savePoint = new SavePoint()

    void setup() {
        MockitoAnnotations.openMocks(this)
    }

    void cleanup() {
        savePoint.rollback()
    }

    /*def "buildFriendlyMessage"() {
        given:
        ThreadLocalProvider.getOrCreate().setLanguage("en-US")
        CheckBookingLimitionResponseType checkBookingLimitionResponseType = JsonUtil.fromJson("{\"resultList\":[{\"type\":\"RESOURCE\",\"tipInfo\":{\"code\":\"F0005\",\"extInfo\":{\"HOTEL_BLACK_LIST\":\"北京西单美爵酒店\"}}}],\"responseCode\":20000,\"responseDesc\":\"Success\",\"responseStatus\":{\"timestamp\":\"2024-11-27 13:01:40.381+0800\",\"ack\":\"Success\",\"errors\":[],\"extension\":[]}}",CheckBookingLimitionResponseType.class)
        when:
        String friendlyMessage = test.buildFriendlyMessage(checkBookingLimitionResponseType)
        then:
        friendlyMessage == null
    }*/

    @Unroll
    def "buildFriendlyMessage"() {
        given:
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
        List<CustomizedSharkConfig> customizedSharkConfigs = Arrays.asList(JsonUtil.fromJson("{\n" +
                "        \"sceneDes\": \"资源管控校验\",\n" +
                "        \"scene\": \"bookingCheck\",\n" +
                "        \"corpIds\": [\n" +
                "            \"customId\",\n" +
                "        ],\n" +
                "        \"sharkKeyPres\": [\n" +
                "            \"key.corp.hotel.CorpAggHotelSaleStrategyService.checkBookingLimition.errorMsg.SHIELD\",\n" +
                "            \"key.corp.hotel.booking.biz.text.lowprice.price\",\n" +
                "        ]\n" +
                "    }", CustomizedSharkConfig.class))
        BookingCheckRequestType bookingCheckRequestType = new BookingCheckRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(
                        userInfo: new UserInfo(corpId: corpId)))
        Map<String, String> extInfo = new HashMap<String, String>()
        extInfo.put("SHIELD_CITY", "上海")
        CheckBookingLimitionResponseType checkBookingLimitionResponseType = new CheckBookingLimitionResponseType(
                resultList: Arrays.asList(new ResultType(type: "RESOURCE", tipInfo: new TipInfoType(code: "F0003", extInfo: extInfo))))
        expect:
        friendlyMessage == test.buildFriendlyMessage(checkBookingLimitionResponseType, customizedSharkConfigs, bookingCheckRequestType)
        where:
        limitScene | corpId     || friendlyMessage
        "SHIELD"   | "normal"   || "因贵司差旅政策，不可预订上海地区的酒店。"
        "SHIELD"   | "customId" || "此页面不支持预订国内酒店"
    }
}
