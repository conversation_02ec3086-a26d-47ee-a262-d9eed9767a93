package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;

class MapperOfGetPolicyUsersRequestTypeTest extends Specification {

    def savePoint = new SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    @Unroll
    def "convert handles #description"() {
        given: "A MapperOfGetPolicyUsersRequestType instance"
        def mapper = new MapperOfGetPolicyUsersRequestType()

        and: "Input parameters"
        def policyInput = new PolicyInput(policyUid: policyUid)
        def userInfo = new UserInfo(userId: userId)
        def integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: userInfo)
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isPackageEnabled() >> packageEnabled
            isPolicyModel() >> policyModel
        }
        def input = Tuple3.of(policyInput, integrationSoaRequestType, accountInfo)

        when: "Calling convert method"
        def result = mapper.convert(input)

        then: "The result matches the expected outcome"
        result?.policyUID == expectedPolicyUID
        result?.uid == expectedUid

        where:
        description                          | policyUid | userId  | packageEnabled | policyModel | expectedPolicyUID | expectedUid | expectedAppId
        "null input"                         | null      | null    | false          | false       | null              | null        | null
        "null policyInput"                   | null      | "user1" | true           | true        | null              | null        | null
        "blank policyUid"                    | ""        | "user1" | true           | true        | null              | null        | null
        "null accountInfo"                   | "policy1" | "user1" | null           | null        | null              | null        | null
        "null integrationSoaRequestType"     | "policy1" | null    | true           | true        | null              | null        | null
        "null userInfo"                      | "policy1" | null    | true           | true        | null              | null        | null
        "blank userId"                       | "policy1" | ""      | true           | true        | null              | null        | null
        "package not enabled"                | "policy1" | "user1" | false          | true        | null              | null        | null
        "policy model not enabled"           | "policy1" | "user1" | true           | false       | null              | null        | null
        "valid inputs"                       | "policy1" | "user1" | true           | true        | "policy1"         | "user1"     | "*********"
    }
}
