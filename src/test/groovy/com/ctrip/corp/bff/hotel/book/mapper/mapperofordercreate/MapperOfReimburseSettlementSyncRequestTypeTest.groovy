package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.agg.hotel.roomavailable.entity.BookRoomInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.InvoiceInfo
import com.ctrip.corp.bff.hotel.book.common.enums.InvoiceTitleTypeEnum
import com.ctrip.corp.bff.hotel.book.contract.HotelInvoiceInfo
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType
import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4;

class MapperOfReimburseSettlementSyncRequestTypeTest extends Specification {

    def tester = Spy(new MapperOfReimburseSettlementSyncRequestType())

    def savePoint = new SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        expect:
        tester.convert(Tuple4.of(new OrderCreateRequestType(hotelInvoiceInfos: [new HotelInvoiceInfo(hotelInvoiceType: "INSURANCE", invoiceInfo: new InvoiceInfo())],hotelInsuranceInput: new HotelInsuranceInput(hotelInsuranceDetailInputs: []),hotelBookInput: new HotelBookInput(hotelDateRangeInfo: new HotelDateRangeInfo(checkIn: "2025-01-01", checkOut: "2025-01-02")),integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo())), new CreateOrderResponseType(), new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity()), new OrderCreateToken())).requestSource == "App"
    }

    def "getHeadTypeEnum" () {
        expect:
        new MapperOfReimburseSettlementSyncRequestType().getHeadTypeEnum(InvoiceTitleTypeEnum.ENTERPRISE, null) == "C"
        new MapperOfReimburseSettlementSyncRequestType().getHeadTypeEnum(InvoiceTitleTypeEnum.GOVERNMENT, null) == "P"
        new MapperOfReimburseSettlementSyncRequestType().getHeadTypeEnum(InvoiceTitleTypeEnum.PERSONAL, null) == "I"
        new MapperOfReimburseSettlementSyncRequestType().getHeadTypeEnum(null, null) == null
    }

    def "getServerFromEnum" () {
        expect:
        new MapperOfReimburseSettlementSyncRequestType().getServerFromEnum(SourceFrom.Online) == "Online"
        new MapperOfReimburseSettlementSyncRequestType().getServerFromEnum(SourceFrom.Offline) == "Offline"
    }
}
