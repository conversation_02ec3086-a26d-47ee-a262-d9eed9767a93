package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;
/**
 * <AUTHOR>
 * @Date 2024/12/16 18:29
 */
class MapperOfGetCorpUserInfoByPolicyRequestTest extends Specification {

    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }
    def "testMapperOfGetCorpUserInfoByPolicyRequestConvert"() {
        MapperOfGetCorpUserInfoByPolicyRequest mapper = new MapperOfGetCorpUserInfoByPolicyRequest()
        when:
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType()
        bookingInitRequestType.setIntegrationSoaRequestType(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "11")))
        then:
        mapper.convert(Tuple1.of(bookingInitRequestType)) != null
    }
}
