package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import spock.lang.Specification

/**
 * <AUTHOR>
 * @Date 2025/7/29 20:42
 */
class MapperOfQueryAuthRequestTypeTest extends Specification {
    def "testConvert"() {
        when:
        IntegrationSoaRequestType integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "111"))
        then:
        new MapperOfQueryAuthRequestType().convert(Tuple1.of(integrationSoaRequestType)).getCenterAuthParam().getUserValue()== "111"
    }
}
