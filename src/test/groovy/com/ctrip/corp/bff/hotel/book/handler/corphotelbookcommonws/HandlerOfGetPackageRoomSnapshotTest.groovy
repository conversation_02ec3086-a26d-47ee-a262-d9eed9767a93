package com.ctrip.corp.bff.hotel.book.handler.corphotelbookcommonws

import com.ctrip.corp.agg.commonws.pkg.room.snapshot.entity.GetPackageRoomSnapshotResponseType
import com.ctrip.corp.hotelbook.commonws.entity.ErrorCodeInfo
import org.junit.Assert
import spock.lang.Specification

/**
 * <AUTHOR>
 * @Date 2024/12/9 18:42
 */
class HandlerOfGetPackageRoomSnapshotTest extends Specification {
    def "testGetLogErrorCode"() {
        HandlerOfGetPackageRoomSnapshot handler = new HandlerOfGetPackageRoomSnapshot()
        when:
        GetPackageRoomSnapshotResponseType response = new GetPackageRoomSnapshotResponseType()
        String errorCode = handler.getLogErrorCode(response)
        then:
        Assert.assertNull(errorCode)

        when:
        response = new GetPackageRoomSnapshotResponseType(errorCodeInfoList: [null,new ErrorCodeInfo(errorCode: 1000)])
         errorCode = handler.getLogErrorCode(response)
        then:
        Assert.assertEquals('1000', errorCode)
    }
}
