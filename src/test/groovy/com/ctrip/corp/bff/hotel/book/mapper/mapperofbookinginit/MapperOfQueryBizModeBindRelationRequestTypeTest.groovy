package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.hotel.book.contract.MembershipInfo
import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;

class MapperOfQueryBizModeBindRelationRequestTypeTest extends Specification {

    def tester = Spy(MapperOfQueryBizModeBindRelationRequestType)

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "buildUids" () {
        expect:
        tester.buildUids(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "1")), new MembershipInfo(membershipUid: "2")) == ["1", "2"]
        tester.buildUids(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "1")), new MembershipInfo(membershipUid: "1")) == ["1"]
    }

    def "convert" () {
        expect:
        tester.convert(null) == null
        tester.convert(Tuple2.of(null, null)) == null
        tester.convert(Tuple2.of(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "1")), null)).dimensionIds == ["1"]
        tester.convert(Tuple2.of(null, new MembershipInfo(membershipUid: "2"))).dimensionIds == ["2"]
    }
}
