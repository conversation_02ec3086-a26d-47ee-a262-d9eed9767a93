package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import mockit.internal.state.SavePoint
import spock.lang.Specification

class MapperOfGetHotelDetailInfoRequestTest extends Specification {

    def tester = Spy( new MapperOfGetHotelDetailInfoRequest() )
    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        expect:
        tester.convert(Tuple2.of(new BookingInitRequestType(hotelBookInput: new HotelBookInput(hotelDateRangeInfo: new HotelDateRangeInfo(checkIn: "2025-01-01", checkOut: "2025-01-02"))), null)).localCheckInDate == "2025-01-01"
    }

    def "buildBaseInfo" () {
        expect:
        tester.buildBaseInfo(null, null) == null
        tester.buildBaseInfo(new IntegrationSoaRequestType(language: "a"), null).locale == "a"

    }

    def "buildUserInfo" () {
        expect:
        tester.buildUserInfo(null) == null
        tester.buildUserInfo(new UserInfo(userId: "a")).uid == "a"
    }

    def "check" () {
        expect:
        !tester.check(null).result
        !tester.check(Tuple2.of(null, null)).result
        !tester.check(Tuple2.of(new BookingInitRequestType(), null)).result
        !tester.check(Tuple2.of(new BookingInitRequestType(integrationSoaRequestType: new IntegrationSoaRequestType()), null)).result
        !tester.check(Tuple2.of(new BookingInitRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(), hotelBookInput: new HotelBookInput(hotelDateRangeInfo: new HotelDateRangeInfo())), null)).result
        tester.check(Tuple2.of(new BookingInitRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(), hotelBookInput: new HotelBookInput(hotelDateRangeInfo: new HotelDateRangeInfo())), new ResourceToken(hotelResourceToken: new HotelResourceToken(masterHotelId: 2)))) == null
    }
}
