package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response

import com.ctrip.corp.bff.framework.hotel.entity.contract.ConfirmInfo
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateResponseType
import com.ctrip.corp.bff.hotel.book.contract.RegisterInputInfo
import com.ctrip.model.RegisterResponseType
import corp.user.service.corpUserInfoService.CorpUserHotelVipCard
import corp.user.service.corpUserInfoService.GetCorpUserHotelVipCardResponseType
import corp.user.service.group4j.accounts.BizModeBindRelationData
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple8
import spock.lang.Unroll;


class MapperOfRegisterInfoResponseTest extends Specification {

    def tester = Spy(MapperOfRegisterInfoResponse)

    def savePoint = new SavePoint()

    void setup() {
        new MockUp<TokenParseUtil>() {
            @Mock
            String generateToken(OrderCreateToken pidEntity, Class<OrderCreateToken> tClass) {
                return ""
            }
        }
    }

    void cleanup() {
        savePoint.rollback()

    }

    def "buildMembershipNo"() {
        given:
        def check = Mock(WrapperOfCheckAvail.BaseCheckAvailInfo)
        check.getGroupId() >> 2
        expect:
        tester.buildMembershipNo(null, null, null, null) == null
        tester.buildMembershipNo(new GetCorpUserHotelVipCardResponseType(), null, null, null) == null
        tester.buildMembershipNo(new GetCorpUserHotelVipCardResponseType(corpUserHotelVipCardList: [null]), null, null, null) == null
        tester.buildMembershipNo(new GetCorpUserHotelVipCardResponseType(corpUserHotelVipCardList: [null]), null, null, new QueryBizModeBindRelationResponseType()) == null
        tester.buildMembershipNo(new GetCorpUserHotelVipCardResponseType(corpUserHotelVipCardList: [null]), new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "1")), check, new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [])) == null
        tester.buildMembershipNo(new GetCorpUserHotelVipCardResponseType(corpUserHotelVipCardList: [new CorpUserHotelVipCard(uid: "1", hotelGroupID: 1, htlVipCardID: "2")]), new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "1")), check, new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(dimensionId: "2", primaryDimensionId: "2")])) == null
        tester.buildMembershipNo(new GetCorpUserHotelVipCardResponseType(corpUserHotelVipCardList: [new CorpUserHotelVipCard(uid: "1", hotelGroupID: 1, htlVipCardID: "2")]), new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "1")), check, new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(dimensionId: "1", primaryDimensionId: "2")])) == null
        tester.buildMembershipNo(new GetCorpUserHotelVipCardResponseType(corpUserHotelVipCardList: [new CorpUserHotelVipCard(uid: "2", hotelGroupID: 1, htlVipCardID: "2")]), new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "1")), check, new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(dimensionId: "1", primaryDimensionId: "2")])) == null
        tester.buildMembershipNo(new GetCorpUserHotelVipCardResponseType(corpUserHotelVipCardList: [new CorpUserHotelVipCard(uid: "2", hotelGroupID: 2, htlVipCardID: "2")]), new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "1")), check, new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(dimensionId: "1", primaryDimensionId: "2")])) == "2"
    }


    def "buildConfirmInfo"() {
        expect:
        with(tester.buildConfirmInfo("type", 2)) {
            confirmDetailInfos*.code == ["type"]
            confirmDetailInfos*.confirmDetailExtends*.key == [["GROUP_ID"]]
            confirmDetailInfos*.confirmDetailExtends*.value == [["2"]]
        }
    }

    def "buildSuccessRegisterResponse"() {
        expect:
        tester.buildSuccessRegisterResponse(new OrderCreateToken(), "2").registerOutputInfo.membershipNo == "2"
    }

    def "buildFailRegisterResponse"() {
        given:
        1 * tester.buildConfirmInfo("REGISTRATION_FAIL", 2) >> new ConfirmInfo()
        expect:
        tester.buildFailRegisterResponse(new OrderCreateToken(), 2).confirmInfo != null
    }

    def "buildRepeatRegisterResponse"() {
        given:
        1 * tester.buildConfirmInfo("DUPLICATE_REGISTRATION", 1) >> new ConfirmInfo()

        expect:
        tester.buildRepeatRegisterResponse("2", new OrderCreateToken(), 1).registerOutputInfo.membershipNo == "2"
    }

    @Unroll
    def "convert"() {
        given:
        tester.buildMembershipNo(_, _, _, _) >> No
        repeat * tester.buildRepeatRegisterResponse(_, _, _) >> new OrderCreateResponseType()
        fail * tester.buildFailRegisterResponse(_, _) >> new OrderCreateResponseType()
        success * tester.buildSuccessRegisterResponse(_, _) >> new OrderCreateResponseType()
        expect:
        !tester.convert(Tuple8.of(null, null, null, null, null, null, null, null)).getT1()
        res == tester.convert(Tuple8.of(new RegisterInputInfo(), null, registerRes, null, null, null, null, null)).getT1()
        where:
        No  | registerRes                                                            || repeat | fail | success | res
        "2" | null                                                                   || 1      | 0    | 0       | true
        ""  | null                                                                   || 0      | 1    | 0       | true
        ""  | new RegisterResponseType()                                             || 0      | 1    | 0       | true
        ""  | new RegisterResponseType(registerStatusCode: 500)                      || 0      | 1    | 0       | true
        ""  | new RegisterResponseType(registerStatusCode: 200, venderMemberNO: "2") || 0      | 0    | 1       | true
        ""  | new RegisterResponseType(registerStatusCode: 201)                      || 0      | 0    | 0       | false
        ""  | new RegisterResponseType(registerStatusCode: 501, venderMemberNO: "2") || 1      | 0    | 0       | true

    }
}
