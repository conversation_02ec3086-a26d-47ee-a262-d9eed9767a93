package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response

import com.ctrip.corp.agg.hotel.roomavailable.entity.BookRoomInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryBonusPointInfoType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PhoneInfo
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.HotelContactorInfo
import com.ctrip.corp.bff.hotel.book.contract.MembershipInfo
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;

class MapperOfCheckMembershipPhoneResponseTest extends Specification {

    def tester = Spy(MapperOfCheckMembershipPhoneResponse)

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert"() {
        given:
        def ava = Mock(WrapperOfCheckAvail.BaseCheckAvailInfo)
        ava.getPointsMode() >> "SJHMS"
        expect:
        tester.convert(null)?.t1 == null
        tester.convert(Tuple3.of(null, null, null)  )?.t1 == null
        tester.convert(Tuple3.of(new OrderCreateRequestType(), ava, null))?.t1 == null
        tester.convert(Tuple3.of(new OrderCreateRequestType(membershipInfo: new MembershipInfo()), ava, null) )?.t1 == null
        tester.convert(Tuple3.of(new OrderCreateRequestType(membershipInfo: new MembershipInfo(phoneInfo: new PhoneInfo())), ava, null))?.t1 == null
        tester.convert(Tuple3.of(new OrderCreateRequestType(membershipInfo: new MembershipInfo(phoneInfo: new PhoneInfo(transferPhoneNo: "Not", countryCode: "Not"))), ava, null))?.t1 == null
        tester.convert(Tuple3.of(new OrderCreateRequestType(membershipInfo: new MembershipInfo(phoneInfo: new PhoneInfo(transferPhoneNo: "Not", countryCode: "Not")), hotelContactorInfo: new HotelContactorInfo()), ava, null))?.t1 == null
        tester.convert(Tuple3.of(new OrderCreateRequestType(membershipInfo: new MembershipInfo(phoneInfo: new PhoneInfo(transferPhoneNo: "Not", countryCode: "Not")), hotelContactorInfo: new HotelContactorInfo(phoneInfo: new PhoneInfo())), ava, null))?.t1 == null
        tester.convert(Tuple3.of(new OrderCreateRequestType(membershipInfo: new MembershipInfo(phoneInfo: new PhoneInfo(transferPhoneNo: "Not", countryCode: "Not")), hotelContactorInfo: new HotelContactorInfo(phoneInfo: new PhoneInfo(transferPhoneNo: "Not", countryCode: "Not"))), ava, null) )?.t1 == null
        tester.convert(Tuple3.of(new OrderCreateRequestType(membershipInfo: new MembershipInfo(phoneInfo: new PhoneInfo(transferPhoneNo: "a", countryCode: "Not")), hotelContactorInfo: new HotelContactorInfo(phoneInfo: new PhoneInfo(transferPhoneNo: "Not", countryCode: "Not"))), ava, null))?.t1
    }

    def "isValidPhoneInfo" () {
        expect:
        !tester.isValidPhoneInfo(null)
        !tester.isValidPhoneInfo(new PhoneInfo())
        !tester.isValidPhoneInfo(new PhoneInfo(countryCode: "a"))
        tester.isValidPhoneInfo(new PhoneInfo(countryCode: "a", transferPhoneNo: "b"))

    }

    def "buildConfirmInfo" () {
        expect:
        tester.buildConfirmInfo().confirmDetailInfos*.code == ["PHONE_POINT_CHECK_FAIL"]
    }
}
