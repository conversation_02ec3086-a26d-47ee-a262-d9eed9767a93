package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import ctrip.BBZ.members.bbzmbrCommonPassenger.GetCommonPassengerRequestType
import ctrip.BBZ.members.bbzmbrCommonPassenger.ParameterItem
import ctrip.BBZ.members.bbzmbrCommonPassenger.QueryCondition
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2024/12/11 22:42
 *
 */
class MapperOfGetCommonPassengerRequestTypeTest extends Specification {

    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }
    MapperOfGetCommonPassengerRequestType mapper = new MapperOfGetCommonPassengerRequestType()

    def "testBuildParameterList"() {
        when:
        List<ParameterItem> result = mapper.buildParameterList()

        then:
        result.size() == 1
        result[0].getKey() == "BizType"
        result[0].getValue() == "CRP"
    }


    @Unroll
    def "testBuildQueryConditionList with #description"() {
        when:
        List<QueryCondition> result = mapper.buildQueryConditionList(infoids)

        then:
        result == expectedResult

        where:
        description                     | infoids        | expectedResult
        "infoids is empty"              | []             | null
        "infoids has one element"       | ["123"]        | [new QueryCondition(key: "ListPassengerID", value: "123")]
        "infoids has multiple elements" | ["123", "456"] | [new QueryCondition(key: "ListPassengerID", value: "123,456")]
    }

    @Unroll
    def "testConvert with #description"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        orderCreateRequestType.setIntegrationSoaRequestType(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "12345")))
        Tuple2<OrderCreateRequestType, List<String>> tuple = Tuple2.of(orderCreateRequestType, infoids)

        when:
        GetCommonPassengerRequestType result = mapper.convert(tuple as Tuple2<OrderCreateRequestType, List<String>>)

        then:
        result.getUID() == "12345"
        result.getParameterList().size() == 1
        result.getParameterList().get(0).getKey() == "BizType"
        result.getParameterList().get(0).getValue() == "CRP"
        result.getQueryConditionList() == expectedQueryConditionList

        where:
        description                        | infoids                | expectedQueryConditionList
        "infoids is empty"                 | []                     | null
        "infoids has one element"          | ["123"]                | [new QueryCondition(key: "ListPassengerID", value: "123")]
        "infoids has multiple elements"    | ["123", "456"]         | [new QueryCondition(key: "ListPassengerID", value: "123,456")]
    }
}
