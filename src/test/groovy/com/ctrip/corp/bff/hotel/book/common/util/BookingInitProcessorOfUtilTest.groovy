package com.ctrip.corp.bff.hotel.book.common.util

import com.ctrip.corp.agg.hotel.expense.contract.model.ApprovalType
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookingRulesType
import com.ctrip.corp.agg.hotel.roomavailable.entity.CheckAvailResponseType
import com.ctrip.corp.agg.hotel.roomavailable.entity.GroupMemberShipType
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelBrandItem
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelItem
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelRatePlan
import com.ctrip.corp.agg.hotel.roomavailable.entity.PackageRoomInfoType
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomAttributesType
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomItem
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelGeoInfoResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.framework.template.handler.WaitFuture
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import com.ctrip.corp.bff.hotel.book.contract.TripInfoInput
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataResponseType
import com.ctrip.corp.order.data.aggregation.query.contract.TravelInfoType
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType
import corp.user.service.group4j.accounts.BizModeBindRelationData
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType
import org.junit.Assert
import spock.lang.Specification
import com.ctrip.corp.order.data.aggregation.query.contract.OrderBasicInfoType;

/**
 * <AUTHOR>
 * @Date 2024/12/5 17:08
 */
class BookingInitProcessorOfUtilTest extends Specification {
    void testRequireRegisterGroupMemberMember_GroupMemberShipTypeAndRoomItemNull() {
        when:
        CheckAvailResponseType checkAvailResponseType = new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: new RoomItem(balanceType: "PP"),hotelInfo: new HotelItem(hotelBrandInfo: new HotelBrandItem(groupId: 12))));

        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();
        then:
        Assert.assertFalse(BookingInitProcessorOfUtil.needQueryMrgMemberUserInfo(checkAvailInfo));
        Assert.assertFalse(BookingInitProcessorOfUtil.needSearchRegistrationFields(checkAvailInfo));
    }

    void testRequireRegisterGroupMemberMember_RoomAttributesNotSupportGroupMemberShip() {
        when:
        RoomItem roomItem = new RoomItem();
        roomItem.setBalanceType("PP")
        CheckAvailResponseType checkAvailResponseType = new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: roomItem));

        RoomAttributesType roomAttributes = new RoomAttributesType();
        roomAttributes.setGroupMemberShip(false);
        roomItem.setRoomAttributes(roomAttributes);
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();
        then:
        Assert.assertFalse(BookingInitProcessorOfUtil.needQueryMrgMemberUserInfo(checkAvailInfo));
    }

    void testRequireRegisterGroupMemberMember_GroupMemberShipTypeNotNeedRegister() {

        when:

        BookingRulesType bookingRules = new BookingRulesType();
        GroupMemberShipType groupMemberShipType = new GroupMemberShipType();
        groupMemberShipType.setNeedRegister(false);
        bookingRules.setGroupMemberShipInfo(groupMemberShipType);

        RoomItem roomItem = new RoomItem();
        roomItem.setBalanceType("PP")
        CheckAvailResponseType checkAvailResponseType = new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: roomItem, bookingRules: bookingRules));

        RoomAttributesType roomAttributes = new RoomAttributesType();
        roomAttributes.setGroupMemberShip(true);
        roomItem.setRoomAttributes(roomAttributes);
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();
        then:
        Assert.assertFalse(BookingInitProcessorOfUtil.needQueryMrgMemberUserInfo(checkAvailInfo));

        when :
        groupMemberShipType = new GroupMemberShipType();
        groupMemberShipType.setNeedRegister(true);
        groupMemberShipType.setGroupRegisterRule(BookingInitProcessorOfUtil.NO_REGISTER);
        bookingRules.setGroupMemberShipInfo(groupMemberShipType);
        checkAvailResponseType = new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: roomItem, bookingRules: bookingRules));
        checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();
        then:
        Assert.assertFalse(BookingInitProcessorOfUtil.needQueryMrgMemberUserInfo(checkAvailInfo));

        when :
        groupMemberShipType = new GroupMemberShipType();
        groupMemberShipType.setNeedRegister(true);
        groupMemberShipType.setGroupRegisterRule(null);
        bookingRules.setGroupMemberShipInfo(groupMemberShipType);
        checkAvailResponseType = new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: roomItem, bookingRules: bookingRules, hotelInfo: new HotelItem(hotelBrandInfo: new HotelBrandItem(groupId: 12))));
        checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();
        then:
        Assert.assertTrue(BookingInitProcessorOfUtil.needQueryMrgMemberUserInfo(checkAvailInfo));
    }

    def "requireDefaultApproval"() {
        given:
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public"))
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("BillType", "A")
                        put("BillControlMode", "A")
                        put("isChkaheadapproveHotelI", "T")
                        put("isChkaheadapproveHotel", "T")
                    }
                }))
                .policyAccountInfo(null)
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build()
        ResourceToken resourceToken = new ResourceToken(hotelResourceToken: new HotelResourceToken(hotelGeoInfoResourceToken: new HotelGeoInfoResourceToken(cityId: 1)))
        when:
        def result = BookingInitProcessorOfUtil.needBatchApprovalDefault(bookingInitRequestType, accountInfo, resourceToken)
        then:
        result

        when:
        resourceToken = new ResourceToken(hotelResourceToken: new HotelResourceToken(hotelGeoInfoResourceToken: new HotelGeoInfoResourceToken(cityId: 22249)))
        result = BookingInitProcessorOfUtil.needBatchApprovalDefault(bookingInitRequestType, accountInfo, resourceToken)
        then:
        result



        when:
        accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("BillType", "A")
                        put("BillControlMode", "A")
                        put("isChkaheadapproveHotelI", "F")
                        put("isChkaheadapproveHotel", "F")
                    }
                }))
                .policyAccountInfo(null)
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build()
        resourceToken = new ResourceToken(hotelResourceToken: new HotelResourceToken(hotelGeoInfoResourceToken: new HotelGeoInfoResourceToken(cityId: 22249)))
        result = BookingInitProcessorOfUtil.needBatchApprovalDefault(bookingInitRequestType, accountInfo, resourceToken)
        then:
        !result
    }

    def "requireQueryPackageRoom" () {
        expect:
        !BookingInitProcessorOfUtil.needGetPackageRoomList(WrapperOfCheckAvail.checkAvailBuilder().setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).setCheckAvailResponseType(new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: new RoomItem(balanceType: "pp", packageRoomInfo: new PackageRoomInfoType(packageId: 0))))).build().getCheckAvailInfo())
        BookingInitProcessorOfUtil.needGetPackageRoomList(WrapperOfCheckAvail.checkAvailBuilder().setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).setCheckAvailResponseType(new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: new RoomItem(balanceType: "pp", packageRoomInfo: new PackageRoomInfoType(packageId: 1))))).build().getCheckAvailInfo())
        !BookingInitProcessorOfUtil.needGetPackageRoomList(WrapperOfCheckAvail.checkAvailBuilder().setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).setCheckAvailResponseType(new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: new RoomItem(balanceType: "pp", packageRoomInfo: new PackageRoomInfoType(packageId: 0, packageRoomToken: ""))))).build().getCheckAvailInfo())
        BookingInitProcessorOfUtil.needGetPackageRoomList(WrapperOfCheckAvail.checkAvailBuilder().setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).setCheckAvailResponseType(new CheckAvailResponseType(hotelRatePlan: new HotelRatePlan(roomInfo: new RoomItem(balanceType: "pp", packageRoomInfo: new PackageRoomInfoType(packageId: 0, packageRoomToken: "NotEmpty"))))).build().getCheckAvailInfo())
    }


    def "needGeneralSearchAccountInfoOfPolicy" () {
        expect:
        !BookingInitProcessorOfUtil.needGeneralSearchAccountInfoOfPolicy(null)
        !BookingInitProcessorOfUtil.needGeneralSearchAccountInfoOfPolicy(new PolicyInput())
        BookingInitProcessorOfUtil.needGeneralSearchAccountInfoOfPolicy(new PolicyInput(policyUid: "a"))
    }

    def "needGetReasoncodes" () {
        expect:
        !BookingInitProcessorOfUtil.needGetReasoncodes(null)
        !BookingInitProcessorOfUtil.needGetReasoncodes([:])
        !BookingInitProcessorOfUtil.needGetReasoncodes(["HOTEL_CHECK_AVAIL":new StrategyInfo(strategyValue: "F")])
        BookingInitProcessorOfUtil.needGetReasoncodes(["HOTEL_CHECK_AVAIL":new StrategyInfo(strategyValue: "T")])
        !BookingInitProcessorOfUtil.needGetReasoncodes(["NEED_RC_INFO":new StrategyInfo(strategyValue: "F")])
        BookingInitProcessorOfUtil.needGetReasoncodes(["NEED_RC_INFO":new StrategyInfo(strategyValue: "T")])
    }
    def "needSearchTripBasicInfoOfOriginalOrderId - 参数为null返回false"() {
        when:
        def result = BookingInitProcessorOfUtil.needSearchTripBasicInfoOfOriginalOrderId(null)
        then:
        !result
    }

    def "needSearchTripBasicInfoOfOriginalOrderId - OrderBasicInfo为null返回false"() {
        given:
        def order = new QueryHotelOrderDataResponseType()
        when:
        def result = BookingInitProcessorOfUtil.needSearchTripBasicInfoOfOriginalOrderId(order)
        then:
        !result
    }

    def "needSearchTripBasicInfoOfOriginalOrderId - TripOrderId为null返回false"() {
        given:
        def order = new QueryHotelOrderDataResponseType()
        def orderBasicInfo = new OrderBasicInfoType()
        order.setOrderBasicInfo(orderBasicInfo)
        // 不设置TripOrderId，默认null
        when:
        def result = BookingInitProcessorOfUtil.needSearchTripBasicInfoOfOriginalOrderId(order)
        then:
        !result
    }

    def "needSearchTripBasicInfoOfOriginalOrderId - TripOrderId为0返回false"() {
        given:
        def order = new QueryHotelOrderDataResponseType()
        def orderBasicInfo = new OrderBasicInfoType()
        order.setOrderBasicInfo(orderBasicInfo)
        orderBasicInfo.setTripOrderId(0L)
        when:
        def result = BookingInitProcessorOfUtil.needSearchTripBasicInfoOfOriginalOrderId(order)
        then:
        !result
    }

    def "needSearchTripBasicInfoOfOriginalOrderId - TripOrderId为非0返回true"() {
        given:
        def order = new QueryHotelOrderDataResponseType()
        def orderBasicInfo = new OrderBasicInfoType()
        order.setOrderBasicInfo(orderBasicInfo)
        orderBasicInfo.setTripOrderId(123L)
        when:
        def result = BookingInitProcessorOfUtil.needSearchTripBasicInfoOfOriginalOrderId(order)
        then:
        result
    }

    def "needSearchApproval" () {
        given:
        def queryHotelOrder = Mock(WaitFuture)
        queryHotelOrder.get() >> new QueryHotelOrderDataResponseType(travelInfo: new TravelInfoType(approval: new com.ctrip.corp.order.data.aggregation.query.contract.ApprovalType(subPreApprovalNo: "a")))
        def account = Mock(WrapperOfAccount.AccountInfo)
        def query =Mock(WaitFuture)
        account.isPreApprovalRequired(_, _) >> true
        expect:
        !BookingInitProcessorOfUtil.needSearchApproval(
                new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "private")),
                queryHotelOrder, new ResourceToken(), account)
        !BookingInitProcessorOfUtil.needSearchApproval(
                new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public")),
                query, new ResourceToken(), account)
        BookingInitProcessorOfUtil.needSearchApproval(
                new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public"), approvalInput: new ApprovalInput(subApprovalNo: "a")),
                queryHotelOrder, new ResourceToken(), account)
        BookingInitProcessorOfUtil.needSearchApproval(
                new BookingInitRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public")),
                queryHotelOrder, new ResourceToken(), account)
    }

    def "needGetCorpUserHotelVipCard" () {
        expect:
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(null, null, [new StrategyInfo(strategyKey: "FIRST_PASSENGER_MEMBERSHIP_CARD", strategyValue: "T")], null)
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(null, null,
                [new StrategyInfo(strategyKey: "FIRST_PASSENGER_MEMBERSHIP_CARD", strategyValue: "T")], [])
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(null, null,
                [new StrategyInfo(strategyKey: "FIRST_PASSENGER_MEMBERSHIP_CARD", strategyValue: "T")], [null])
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(null, null,
                [new StrategyInfo(strategyKey: "FIRST_PASSENGER_MEMBERSHIP_CARD", strategyValue: "T")], [new HotelBookPassengerInput()])
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(null, null,
                [new StrategyInfo(strategyKey: "FIRST_PASSENGER_MEMBERSHIP_CARD", strategyValue: "T")], [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput())])
        BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(null, null,
                [new StrategyInfo(strategyKey: "FIRST_PASSENGER_MEMBERSHIP_CARD", strategyValue: "T")], [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "A"))])
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(null, null,
                null, null)
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(new IntegrationSoaRequestType(), new QueryBizModeBindRelationResponseType(),
                null, null)
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(new IntegrationSoaRequestType(), new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: []),
                null, null)
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(new IntegrationSoaRequestType(), new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData()]),
                null, null)
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")),
                new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData()]),
                null, null)
        !BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")),
                new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(dimensionId: "b", primaryDimensionId: "b")]),
                null, null)
        BookingInitProcessorOfUtil.needGetCorpUserHotelVipCard(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")),
                new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(dimensionId: "a", primaryDimensionId: "b")]),
                null, null)
    }

    def "needSearchTripDetail" () {
        expect:
        !BookingInitProcessorOfUtil.needSearchTripDetail(null)
        !BookingInitProcessorOfUtil.needSearchTripDetail(new BookingInitRequestType())
        !BookingInitProcessorOfUtil.needSearchTripDetail(new BookingInitRequestType(tripInfoInput: new TripInfoInput()))
        !BookingInitProcessorOfUtil.needSearchTripDetail(new BookingInitRequestType(tripInfoInput: new TripInfoInput(tripId: 0)))
        BookingInitProcessorOfUtil.needSearchTripDetail(new BookingInitRequestType(tripInfoInput: new TripInfoInput(tripId: 1)))
    }

    def "needReimbursementQuery" () {
        expect:
        !BookingInitProcessorOfUtil.needReimbursementQuery(null)
        BookingInitProcessorOfUtil.needReimbursementQuery(1l)
    }

    def "needGetPlatformRelationByUid" () {
        expect:
        !BookingInitProcessorOfUtil.needGetPlatformRelationByUid(null,null)
        !BookingInitProcessorOfUtil.needGetPlatformRelationByUid(new IntegrationSoaRequestType(),null)
        !BookingInitProcessorOfUtil.needGetPlatformRelationByUid(new IntegrationSoaRequestType(userInfo: new UserInfo()),null)
        !BookingInitProcessorOfUtil.needGetPlatformRelationByUid(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")),null)
        !BookingInitProcessorOfUtil.needGetPlatformRelationByUid(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")),
                new QueryBizModeBindRelationResponseType())
        !BookingInitProcessorOfUtil.needGetPlatformRelationByUid(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")),
                new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: []))
        !BookingInitProcessorOfUtil.needGetPlatformRelationByUid(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")),
                new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [null]))
        !BookingInitProcessorOfUtil.needGetPlatformRelationByUid(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")),
                new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData()]))
        !BookingInitProcessorOfUtil.needGetPlatformRelationByUid(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")),
                new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(dimensionId: "b", primaryDimensionId: "b")]))
        BookingInitProcessorOfUtil.needGetPlatformRelationByUid(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")),
                new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(dimensionId: "a", primaryDimensionId: "b")]))
    }

}
