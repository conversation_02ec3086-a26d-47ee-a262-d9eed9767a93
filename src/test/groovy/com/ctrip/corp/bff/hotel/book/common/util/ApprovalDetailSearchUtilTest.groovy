package com.ctrip.corp.bff.hotel.book.common.util

import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant
import com.ctrip.corp.bff.hotel.book.common.enums.approvalinfo.ControlInfoTypeEnum
import com.ctrip.corp.bff.hotel.book.contract.ApprovalBaseInfoInput
import com.ctrip.corp.bff.hotel.book.contract.ApprovalDetailSearchRequestType
import com.ctrip.corp.bff.profile.contract.SSOExtendInfo
import com.ctrip.corp.bff.profile.contract.SSOInfoQueryResponseType
import com.ctrip.corp.bff.specific.contract.ApprovalInfo
import com.ctrip.corp.bff.specific.contract.BatchApprovalDefaultResponseType
import com.ctrip.corp.bff.specific.contract.ControlInfo
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2024-12-11
 * */
class ApprovalDetailSearchUtilTest extends Specification {

    def savePoint = new SavePoint()

    void cleanup() {
        savePoint.rollback()
    }

    def "buildDefaultApprovalInfo"() {
        given:
        def batchApprovalDefaultResponseType = new BatchApprovalDefaultResponseType()
        def approvalInfo = new ApprovalInfo(defaultApproval: CommonConstant.OPEN)
        batchApprovalDefaultResponseType.setApprovalInfos([approvalInfo])

        when:
        def result = ApprovalDetailSearchUtil.buildDefaultApprovalInfo(batchApprovalDefaultResponseType)

        then:
        result == approvalInfo
    }

    def "buildDefaultApprovalInfo_null"() {
        given:
        def batchApprovalDefaultResponseType = new BatchApprovalDefaultResponseType()

        when:
        def result = ApprovalDetailSearchUtil.buildDefaultApprovalInfo(batchApprovalDefaultResponseType)

        then:
        result == null
    }

    def "buildDefaultCityIds"() {
        given:
        def controlInfo = new ControlInfo(type: ControlInfoTypeEnum.CITY_ID.getCode(), content: "1,2,3")
        def approvalInfo = new ApprovalInfo(defaultApproval: CommonConstant.OPEN, controlInfos: [controlInfo])
        def batchApprovalDefaultResponseType = new BatchApprovalDefaultResponseType(approvalInfos: [approvalInfo])

        when:
        def result = ApprovalDetailSearchUtil.buildDefaultCityIds(batchApprovalDefaultResponseType)

        then:
        result == ["1", "2", "3"]
    }

    def "buildDefaultCityIds_null"() {
        given:
        def batchApprovalDefaultResponseType = new BatchApprovalDefaultResponseType()

        when:
        def result = ApprovalDetailSearchUtil.buildDefaultCityIds(batchApprovalDefaultResponseType)

        then:
        result == null
    }

    def "buildControlInfo"() {
        given:
        def controlInfo = new ControlInfo(type: "CITY_ID", content: "1,2,3")
        def controlInfoList = [controlInfo]

        when:
        def result = ApprovalDetailSearchUtil.buildControlInfo(controlInfoList, "CITY_ID")

        then:
        result == controlInfo
    }

    def "buildControlInfo_null"() {
        given:
        def controlInfoList = []

        when:
        def result = ApprovalDetailSearchUtil.buildControlInfo(controlInfoList, "CITY_ID")

        then:
        result == null
    }

    def "getExtendInfoValue"() {
        given:
        def extendInfo = ["cityId": "1"]
        def ssoExtendInfo = new SSOExtendInfo(productType: "Hotel", extendInfo: extendInfo)
        def ssoInfoQueryResponseType = new SSOInfoQueryResponseType(ssoExtendInfoList: [ssoExtendInfo])

        when:
        def result = ApprovalDetailSearchUtil.getExtendInfoValue(ssoInfoQueryResponseType, "cityId")

        then:
        result == "1"
    }

    def "getExtendInfoValue_null"() {
        given:
        def ssoInfoQueryResponseType = new SSOInfoQueryResponseType()

        when:
        def result = ApprovalDetailSearchUtil.getExtendInfoValue(ssoInfoQueryResponseType, "cityId")

        then:
        result == null
    }

    def "buildProductType_empty"() {
        given:
        def approvalDetailSearchRequest = new ApprovalDetailSearchRequestType()
        def ssoInfoQueryResponseType = new SSOInfoQueryResponseType()

        when:
        def result = ApprovalDetailSearchUtil.buildProductType(approvalDetailSearchRequest, ssoInfoQueryResponseType)

        then:
        result != null
        result.size() > 0
    }

    def "buildProductType"() {
        given:
        def approvalDetailSearchRequest = new ApprovalDetailSearchRequestType()
        def ssoInfoQueryResponseType = new SSOInfoQueryResponseType()
        def ssoExtendInfo = new SSOExtendInfo(productType: "Transfer", extendInfo: [(ApprovalDetailSearchUtil.SEARCH_TYPE): searchType])
        ssoInfoQueryResponseType.setSsoExtendInfoList([ssoExtendInfo])
        approvalDetailSearchRequest.setApprovalBaseInfoInput(new ApprovalBaseInfoInput(cityType: cityType))

        when:
        def result = ApprovalDetailSearchUtil.buildProductType(approvalDetailSearchRequest, ssoInfoQueryResponseType)

        then:
        result == expectedProductTypes

        where:
        searchType                | cityType       | expectedProductTypes
        "oversea_hotel"           | null           | [ApprovalDetailSearchUtil.INTERNATIONAL_HOTEL]
        "domestic_hotel"          | null           | [ApprovalDetailSearchUtil.CN_HOTEL]
        null                      | null           | [ApprovalDetailSearchUtil.CN_HOTEL, ApprovalDetailSearchUtil.INTERNATIONAL_HOTEL]
        null                      | "DOMESTIC"     | [ApprovalDetailSearchUtil.CN_HOTEL]
        null                      | "INTERNATIONAL"| [ApprovalDetailSearchUtil.INTERNATIONAL_HOTEL]
    }
}
