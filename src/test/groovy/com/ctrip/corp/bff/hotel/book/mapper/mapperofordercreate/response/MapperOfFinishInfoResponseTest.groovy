package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response

import com.ctrip.corp.bff.framework.hotel.common.util.WaitFutureUtil
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.CreateOrderResult
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.PayAmountResult
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.hotel.book.common.enums.HotelBalanceTypeEnum
import com.ctrip.corp.bff.hotel.book.common.enums.RoomTypeEnum
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfFinishResponse
import com.ctrip.corp.bff.hotel.book.contract.FinishInfoOutput
import com.ctrip.corp.bff.hotel.book.contract.NativePayInfo
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.contract.PayMentInfoInput
import com.ctrip.corp.bff.hotel.book.contract.UnionPayInfo
import com.ctrip.corp.bff.payment.contract.PaymentOrderCreateResponseType
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType
import com.ctrip.corp.order.paymentcenter.bill.contract.QueryPaymentBillConfigResponseType
import com.ctrip.microfinance.giftcardpay.ws.contract.RetrieveTicketsByOrderTypeResponseType
import com.ctrip.microfinance.giftcardpay.ws.contract.dtotypes.TicketItemTypeVo
import com.ctrip.soa._21685.TransactionPayUrlResponseType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import org.junit.Assert
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @Date 2024/12/16 18:50
 */
class MapperOfFinishInfoResponseTest extends Specification {

    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }
    def "testBuildUnionPayInfo"() {
        when:
        MapperOfFinishInfoResponse mapper = new MapperOfFinishInfoResponse();
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType();
        PaymentOrderCreateResponseType paymentOrderCreateResponseType = new PaymentOrderCreateResponseType();

        // Test case where buildUnionPay returns false
        orderCreateRequestType.setHotelPayTypeInput(Arrays.asList(new HotelPayTypeInput(payCode:"ROOM", payType:"UNION_PAY")));
        then:
        Assert.assertNotNull(mapper.buildUnionPayInfo(orderCreateRequestType, paymentOrderCreateResponseType));
    }

    @Unroll
    def "testBuildPayType with different scenarios"() {
        given:
        MapperOfFinishInfoResponse mapper = new MapperOfFinishInfoResponse()
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        orderCreateRequestType.setIntegrationSoaRequestType(new IntegrationSoaRequestType(sourceFrom: sourceFrom, language: language))
        orderCreateRequestType.setHotelPayTypeInput(hotelPayTypeInput)
        orderCreateRequestType.setPaymentInfoInput(new PayMentInfoInput(clientType: clientType))

        CreateOrderResponseType createOrderResponseType = new CreateOrderResponseType()
        PaymentOrderCreateResponseType paymentOrderCreateResponseType = new PaymentOrderCreateResponseType(payToken: payToken)
        OrderCreateToken orderCreateToken = new OrderCreateToken()
        orderCreateToken.addContinueTypes(ContinueTypeConst.PRICE_CHANGE)
        orderCreateToken.createOrderResult = new CreateOrderResult(payAmountResult: new PayAmountResult(currency: "CNY"))
        QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType = new QueryPaymentBillConfigResponseType()

        when:
        String result = mapper.buildPayType(orderCreateRequestType, createOrderResponseType, paymentOrderCreateResponseType, orderCreateToken, queryPaymentBillConfigResponseType)

        then:
        result == expectedPayType

        where:
        sourceFrom         | language | hotelPayTypeInput                                              | clientType | payToken | expectedPayType
        SourceFrom.Offline | "zh-CN"  | [new HotelPayTypeInput(payCode: "ROOM", payType: "UNION_PAY")] | "APP"      | "token"  | "NORMAL_PAY"
        SourceFrom.H5      | "zh-CN"  | [new HotelPayTypeInput(payCode: "ROOM", payType: "UNION_PAY")] | "H5"       | "token"  | "UNION_PAY"
        SourceFrom.Online  | "zh-CN"  | [new HotelPayTypeInput(payCode: "ROOM", payType: "UNION_PAY")] | "APP"      | "token"  | "NORMAL_PAY"
        SourceFrom.H5      | "zh-CN"  | [new HotelPayTypeInput(payCode: "ROOM", payType: "OTHER_PAY")] | "APP"      | "token"  | "NATIVE_PAY"
        SourceFrom.Online  | "en-US"  | [new HotelPayTypeInput(payCode: "ROOM", payType: "UNION_PAY")] | "APP"      | "token"  | "NORMAL_PAY"
    }

    @Unroll
    def "testBuildNativePayInfo with different scenarios"() {
        given:
        MapperOfFinishInfoResponse mapper = new MapperOfFinishInfoResponse()
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        orderCreateRequestType.setIntegrationSoaRequestType(new IntegrationSoaRequestType(sourceFrom: SourceFrom.H5, language: "zh-CN"))
        orderCreateRequestType.setPaymentInfoInput(new PayMentInfoInput(clientType: "APP"))
        CreateOrderResponseType createOrderResponseType = new CreateOrderResponseType()
        PaymentOrderCreateResponseType paymentOrderCreateResponseType = new PaymentOrderCreateResponseType(payToken: "token")
        OrderCreateToken orderCreateToken = new OrderCreateToken()
        orderCreateToken.addContinueTypes(ContinueTypeConst.PRICE_CHANGE)
        orderCreateToken.createOrderResult = new CreateOrderResult(payAmountResult: new PayAmountResult(currency: "CNY"))
        QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType = new QueryPaymentBillConfigResponseType()

        when:
        NativePayInfo result = mapper.buildNativePayInfo(orderCreateRequestType, createOrderResponseType, paymentOrderCreateResponseType, orderCreateToken, queryPaymentBillConfigResponseType)

        then:
        result.nativePay != null
    }


    def "testBuildFinishInfoOutputSelfPay"() {
        given:
        MapperOfFinishInfoResponse mapper = new MapperOfFinishInfoResponse()
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        orderCreateRequestType.setIntegrationSoaRequestType(new IntegrationSoaRequestType(sourceFrom: SourceFrom.H5, language: "zh-CN"))
        orderCreateRequestType.setPaymentInfoInput(new PayMentInfoInput(clientType: "APP"))
        CreateOrderResponseType createOrderResponseType = new CreateOrderResponseType()
        PaymentOrderCreateResponseType paymentOrderCreateResponseType = new PaymentOrderCreateResponseType(payToken: "token", payLinkUrl: "link")
        OrderCreateToken orderCreateToken = new OrderCreateToken()
        orderCreateToken.addContinueTypes(ContinueTypeConst.PRICE_CHANGE)
        orderCreateToken.createOrderResult = new CreateOrderResult(payAmountResult: new PayAmountResult(currency: "CNY"))
        QueryPaymentBillConfigResponseType queryPaymentBillConfigResponseType = new QueryPaymentBillConfigResponseType(merchantId: "merchant", busType: "bus")

        when:
        FinishInfoOutput result = mapper.buildFinishInfoOutputSelfPay(orderCreateRequestType, createOrderResponseType, paymentOrderCreateResponseType, orderCreateToken, queryPaymentBillConfigResponseType)

        then:
        result.payMentInfo.merchantId == "merchant"
        result.payMentInfo.busType == "bus"
        result.payMentInfo.nativePayInfo != null
        result.payMentInfo.unionPayInfo == null
        result.payMentInfo.payType == "NATIVE_PAY"
        result.payMentInfo.redirectUrl == "link"
        result.payMentInfo.payToken == "token"
    }

    @Unroll
    def "testBuildFinishInfoOutputDoublePay"() {
        given:
        WrapperOfFinishResponse wrapperOfFinishResponse =
                WrapperOfFinishResponse.builder().setOfflineNewPay(true);
        CreateOrderResponseType createOrderResponseType = new CreateOrderResponseType()
        OrderCreateToken orderCreateToken = new OrderCreateToken()
        TransactionPayUrlResponseType transactionPayUrlResponseType = new TransactionPayUrlResponseType(payUrl: payUrl)
        new MockUp<com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                return false
            }
        }
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()



        when:
        FinishInfoOutput result = new MapperOfFinishInfoResponse().buildFinishInfoOutputDoublePay(
                createOrderResponseType, orderCreateToken, orderCreateRequestType, transactionPayUrlResponseType)

        then:
        result != null
        result.getFinishType() == "PAY"
        result.getPayMentInfo() != null
        result.getPayMentInfo().getPayType() == "DOUBLE_PAY"
        result.getPayMentInfo().getRedirectUrl() == expectedPayUrl
        wrapperOfFinishResponse.offlineNewPay

        where:
        payUrl          || expectedPayUrl
        "http://pay.url" || "http://pay.url"
        null            || null
    }


    def "buildTravelMoneyPay"() {
        given:
        MapperOfFinishInfoResponse mapper = new MapperOfFinishInfoResponse()
        WrapperOfAccount.AccountInfo accountInfo = Mock(WrapperOfAccount.AccountInfo)
        accountInfo.settlementCurrencyForAgg() >> settlementCurrencyForAgg
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        checkAvailInfo.getRoomTypeEnum() >> roomTypeEnum
        checkAvailInfo.isTmcPrice() >> tmcPrice
        checkAvailInfo.getHotelBalanceTypeEnum() >> hotelBalanceTypeEnum
        checkAvailInfo.isCanTravelMoneyPay() >> isCanTravelMoneyPay
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(corpPayInfo: new CorpPayInfo(corpPayType: corpPayType))
        RetrieveTicketsByOrderTypeResponseType retrieveTicketsByOrderTypeResponseType = new RetrieveTicketsByOrderTypeResponseType(tickets: Arrays.asList(new TicketItemTypeVo(availableAmount: availableAmount)))

        when:

        expect:
        result == mapper.buildTravelMoneyPay(accountInfo, checkAvailInfo, orderCreateRequestType, retrieveTicketsByOrderTypeResponseType)

        where:
        corpPayType | settlementCurrencyForAgg | roomTypeEnum   | tmcPrice | hotelBalanceTypeEnum       | isCanTravelMoneyPay | availableAmount   || result
        "private"   | "CNY"                    | RoomTypeEnum.M | false    | HotelBalanceTypeEnum.PP    | true                | new BigDecimal(2) || true
        "private"   | "CNY"                    | RoomTypeEnum.M | false    | HotelBalanceTypeEnum.USEFG | true                | new BigDecimal(2) || true
        "private"   | "CNY"                    | RoomTypeEnum.M | false    | HotelBalanceTypeEnum.USEFG | true                | null || false
        "private"   | "CNY"                    | RoomTypeEnum.M | false    | HotelBalanceTypeEnum.PP    | true                | new BigDecimal(0) || false
        "private"   | "CNY"                    | RoomTypeEnum.M | false    | HotelBalanceTypeEnum.PP    | false               | new BigDecimal(2) || false
        "private"   | "CNY"                    | RoomTypeEnum.M | false    | HotelBalanceTypeEnum.FG    | true                | new BigDecimal(2) || false
        "private"   | "CNY"                    | RoomTypeEnum.M | true     | HotelBalanceTypeEnum.PP    | true                | new BigDecimal(2) || false
        "private"   | "CNY"                    | RoomTypeEnum.C | false    | HotelBalanceTypeEnum.PP    | true                | new BigDecimal(2) || false
        "private"   | "SGD"                    | RoomTypeEnum.M | false    | HotelBalanceTypeEnum.PP    | true                | new BigDecimal(2) || false
        "public"    | "CNY"                    | RoomTypeEnum.M | false    | HotelBalanceTypeEnum.PP    | true                | new BigDecimal(2) || false
    }
}
