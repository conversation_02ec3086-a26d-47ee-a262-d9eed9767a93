package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;

class MapperOfGeneralBatchSearchAccountInfoRequestTypeTest extends Specification {


    def tester = Spy(new MapperOfGeneralBatchSearchAccountInfoRequestType())

    def savePoint = new mockit.internal.state.SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        expect:
        tester.convert(Tuple2.of(null, new IntegrationSoaRequestType(requestId: "a", userInfo: new UserInfo()))).rid == "a"
    }

}
