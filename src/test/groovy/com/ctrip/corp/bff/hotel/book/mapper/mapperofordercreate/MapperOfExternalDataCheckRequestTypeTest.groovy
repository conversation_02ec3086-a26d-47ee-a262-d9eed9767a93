package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.corpsz.corpcustomrequirementservice.common.contract.DataType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2025/6/16 15:43
 *
 */
class MapperOfExternalDataCheckRequestTypeTest extends Specification {

    def savePoint = new SavePoint()

    def setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "testConvert with valid inputs"() {
        given: "A valid OrderCreateRequestType with HotelBookPassengerInputs"
        def hotelPassengerInput = Mock(HotelPassengerInput) {
            getExternalEmployeeId() >> "employee123"
        }
        def hotelBookPassengerInput = Mock(HotelBookPassengerInput) {
            getHotelPassengerInput() >> hotelPassengerInput
        }
        def orderCreateRequestType = Mock(OrderCreateRequestType) {
            getHotelBookPassengerInputs() >> [hotelBookPassengerInput]
            getIntegrationSoaRequestType() >> Mock(IntegrationSoaRequestType) {
                getUserInfo() >> Mock(UserInfo) {
                    getCorpId() >> "corp123"
                }
            }
        }
        def mapper = new MapperOfExternalDataCheckRequestType()

        when: "Calling convert with valid inputs"
        def result = mapper.convert(Tuple1.of(orderCreateRequestType) as Tuple1<OrderCreateRequestType>)

        then: "The result should match the expected ExternalDataCheckRequestType"
        result != null
        result.getDataType() == DataType.TencentEID
        result.getDataList() == ["employee123"]
        result.getCorpId() == "corp123"
    }



    def "check"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "testCorpId")),
                hotelBookPassengerInputs: [
                        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(externalEmployeeId: "test1")),
                        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(externalEmployeeId: "test2"))]
        )
        def mapper = new MapperOfExternalDataCheckRequestType()
        when:
        def result = mapper.check(Tuple1.of(orderCreateRequestType))
        then:
        result == null
    }

    def "check-haserror"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "testCorpId")),
                hotelBookPassengerInputs: [
                        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(externalEmployeeId: "")),
                        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(externalEmployeeId: "test2"))]
        )
        def mapper = new MapperOfExternalDataCheckRequestType()
        when:
        def result = mapper.check(Tuple1.of(orderCreateRequestType))
        then:
        def ex = thrown(BusinessException)
        ex.errorCode == 468
    }
}
