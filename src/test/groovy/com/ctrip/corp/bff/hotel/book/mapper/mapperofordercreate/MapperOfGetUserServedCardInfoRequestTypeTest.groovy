package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5;

class MapperOfGetUserServedCardInfoRequestTypeTest extends Specification {

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    @Unroll
    def "convert handles #description"() {
        given: "A MapperOfGetUserServedCardInfoRequestType instance"
        def mapper = new MapperOfGetUserServedCardInfoRequestType()

        and: "Input parameters"
        def policyInput = new PolicyInput(policyUid: policyUid)
        def userInfo = new UserInfo(userId: userId, corpId: corpId)
        def integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: userInfo)
        def accountInfo = Mock(WrapperOfAccount.AccountInfo) {
            isPackageEnabled() >> packageEnabled
            isPolicyModel() >> policyModel
            bookPolicyPsgMustSameTripApprove(_, _) >> sameTripApprove
        }
        def corpPayInfo = new CorpPayInfo()
        def cityInput = new CityInput(cityId: cityId)
        def input = Tuple5.of(policyInput, integrationSoaRequestType, accountInfo, corpPayInfo, cityInput)

        when: "Calling convert method"
        def result = mapper.convert(input)

        then: "The result matches the expected outcome"
        result?.uid == expectedUid
        result?.corpId == expectedCorpId

        where:
        description                        | policyUid | userId  | corpId  | packageEnabled | policyModel | sameTripApprove | cityId | expectedUid | expectedCorpId
        "null input"                       | null      | null    | null    | false          | false       | false           | null   | null        | null
        "null policyInput"                 | null      | "user1" | "corp1" | true           | true        | false           | 1      | null        | null
        "blank policyUid"                  | ""        | "user1" | "corp1" | true           | true        | false           | 1      | null        | null
        "null integrationSoaRequestType"   | "policy1" | null    | null    | true           | true        | false           | 1      | null        | null
        "null userInfo"                    | "policy1" | null    | null    | true           | true        | false           | 1      | null        | null
        "blank userId"                     | "policy1" | ""      | "corp1" | true           | true        | false           | 1      | null        | null
        "null cityInput"                   | "policy1" | "user1" | "corp1" | true           | true        | false           | null   | null        | null
        "package enabled and policy model" | "policy1" | "user1" | "corp1" | true           | true        | false           | 1      | null        | null
        "same trip approval required"      | "policy1" | "user1" | "corp1" | false          | false       | true            | 1      | null        | null
        "valid inputs"                     | "policy1" | "user1" | "corp1" | false          | true       | false           | 1      | "user1"     | "corp1"
    }


}
