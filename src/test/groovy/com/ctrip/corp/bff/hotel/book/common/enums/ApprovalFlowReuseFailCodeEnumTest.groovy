package com.ctrip.corp.bff.hotel.book.common.enums

import spock.lang.Specification
import spock.lang.Unroll

class ApprovalFlowReuseFailCodeEnumTest extends Specification{

    @Unroll
    def "test getCode for #enumName"() {
        expect:
        enumValue.getCode() == code

        where:
        enumValue << ApprovalFlowReuseFailCodeEnum.values()
        code      << ApprovalFlowReuseFailCodeEnum.values()*.getCode()
        enumName  << ApprovalFlowReuseFailCodeEnum.values()*.name()
    }

    @Unroll
    def "test getApprovalFlowReuseFailCodeEnumByCode with valid code #code"() {
        expect:
        ApprovalFlowReuseFailCodeEnum.getApprovalFlowReuseFailCodeEnumByCode(code) == expectedEnum

        where:
        expectedEnum << ApprovalFlowReuseFailCodeEnum.values()
        code         << ApprovalFlowReuseFailCodeEnum.values()*.getCode()
    }

    def "test getApprovalFlowReuseFailCodeEnumByCode with blank code"() {
        expect:
        ApprovalFlowReuseFailCodeEnum.getApprovalFlowReuseFailCodeEnumByCode("") == ApprovalFlowReuseFailCodeEnum.UNKNOWN
        ApprovalFlowReuseFailCodeEnum.getApprovalFlowReuseFailCodeEnumByCode(null) == ApprovalFlowReuseFailCodeEnum.UNKNOWN
    }

    def "test getApprovalFlowReuseFailCodeEnumByCode with invalid code"() {
        expect:
        ApprovalFlowReuseFailCodeEnum.getApprovalFlowReuseFailCodeEnumByCode("not-exist") == ApprovalFlowReuseFailCodeEnum.UNKNOWN
        ApprovalFlowReuseFailCodeEnum.getApprovalFlowReuseFailCodeEnumByCode("999") == ApprovalFlowReuseFailCodeEnum.UNKNOWN
    }
}
