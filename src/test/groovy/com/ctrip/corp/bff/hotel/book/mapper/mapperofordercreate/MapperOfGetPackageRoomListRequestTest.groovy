package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3;

class MapperOfGetPackageRoomListRequestTest extends Specification {


    def tester = Spy(new MapperOfGetPackageRoomListRequest())

    def savePoint = new mockit.internal.state.SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert"  () {
        given:
        def input = Mock(WrapperOfCheckAvail.CheckAvailContextInfo)
        input.getAggPackageToken() >> "aggPackageToken"
        expect:
        tester.convert(Tuple3.of(new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo())), new ResourceToken(), input)).packageTokenList == ["aggPackageToken"]
    }
}
