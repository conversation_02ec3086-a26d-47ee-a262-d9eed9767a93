package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response

import com.ctrip.corp.agg.hotel.roomavailable.entity.BookHotelInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookRoomInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.MultipleLanguageText
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import spock.lang.Specification

class MapperOfConfirmOrderInfoResponseTest extends Specification {

    def tester = Spy(new MapperOfConfirmOrderInfoResponse())

    def savePoint = new mockit.internal.state.SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "buildHotelNameInfo" () {
        expect:
        new MapperOfConfirmOrderInfoResponse().buildHotelNameInfo(new QueryCheckAvailContextResponseType(hotelInfo: new BookHotelInfoEntity(hotelName: new MultipleLanguageText(textEn: "a"))), new OrderCreateRequestType()).enResourceName.name == "a"
    }

    def "buildRoomName" () {
        expect:
        new MapperOfConfirmOrderInfoResponse().buildRoomName(new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(roomName: new MultipleLanguageText(textGB: "a"))), new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType())).localeResourceName.name == "a"
    }
}
