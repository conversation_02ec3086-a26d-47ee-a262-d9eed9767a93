package com.ctrip.corp.bff.hotel.book.common.wrapper

import spock.lang.Specification
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfCityBaseInfo
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig
import com.ctrip.corp.hotel.book.query.entity.GetCityBaseInfoResponseType
import com.ctrip.soa._20184.CheckHotelAuthExtensionResponseType
import com.ctrip.soa._20184.QueryHotelAuthExtensionResponseType
import com.ctrip.soa._21234.SearchTripBasicInfoResponseType
import com.ctrip.soa.corp.hotelorder.corphotelorderdetail.v1.OrderDetailResponseType
import com.ctrip.soa.corp.order.orderindexextservice.v1.GetOrderFoundationDataResponseType
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType
import spock.lang.Specification


class WrapperOfApprovalFlowReuseTest extends Specification{
    def "builder should set all fields correctly"() {
        given:
        def queryHotelAuth = Mock(QueryHotelAuthExtensionResponseType)
        def checkHotelAuth = Mock(CheckHotelAuthExtensionResponseType)
        def orderCreateRequest = Mock(OrderCreateRequestType)
        def cityBaseInfo = Mock(WrapperOfCityBaseInfo.CityBaseInfo)
        def orderCreateToken = Mock(OrderCreateToken)
        def accountInfo = Mock(WrapperOfAccount.AccountInfo)
        def orderDetail = Mock(OrderDetailResponseType)
        def strategyInfoMap = [k1: Mock(StrategyInfo)]
        def getOrderFoundationData = Mock(GetOrderFoundationDataResponseType)
        def searchTripBasicInfo = Mock(SearchTripBasicInfoResponseType)
        def checkAvailInfo = Mock(WrapperOfCheckAvail.BaseCheckAvailInfo)
        def getCityBaseInfo = Mock(GetCityBaseInfoResponseType)
        def getCityBaseInfoOfReuse = Mock(GetCityBaseInfoResponseType)
        def qconfig = Mock(QconfigOfCertificateInitConfig)
        def policyGetCorpUserInfoOfReuse = Mock(GetCorpUserInfoResponseType)
        def policyGetCorpUserInfo = Mock(GetCorpUserInfoResponseType)

        when:
        def wrapper = WrapperOfApprovalFlowReuse.builder()
                .withQueryHotelAuthExtensionResponseType(queryHotelAuth)
                .withCheckHotelAuthExtensionResponseType(checkHotelAuth)
                .withOrderCreateRequestType(orderCreateRequest)
                .withCityBaseInfo(cityBaseInfo)
                .withOrderCreateToken(orderCreateToken)
                .withAccountInfo(accountInfo)
                .withOrderDetailResponseTypeOfApprovalFlowReuse(orderDetail)
                .withStrategyInfoMap(strategyInfoMap)
                .withGetOrderFoundationDataResponseType(getOrderFoundationData)
                .withSearchTripBasicInfoResponseTypeOfApprovalFlowReuse(searchTripBasicInfo)
                .withCheckAvailInfo(checkAvailInfo)
                .withGetCityBaseInfoResponseType(getCityBaseInfo)
                .withGetCityBaseInfoResponseTypeOfApprovalFlowReuse(getCityBaseInfoOfReuse)
                .withQconfigOfCertificateInitConfig(qconfig)
                .withPolicyGetCorpUserInfoResponseTypeOfApprovalFlowReuse(policyGetCorpUserInfoOfReuse)
                .withPolicyGetCorpUserInfoResponseType(policyGetCorpUserInfo)
                .build()

        then:
        wrapper.getQueryHotelAuthExtensionResponseType() == queryHotelAuth
        wrapper.getCheckHotelAuthExtensionResponseType() == checkHotelAuth
        wrapper.getOrderCreateRequestType() == orderCreateRequest
        wrapper.getCityBaseInfo() == cityBaseInfo
        wrapper.getOrderCreateToken() == orderCreateToken
        wrapper.getAccountInfo() == accountInfo
        wrapper.getOrderDetailResponseTypeOfApprovalFlowReuse() == orderDetail
        wrapper.getStrategyInfoMap() == strategyInfoMap
        wrapper.getGetOrderFoundationDataResponseType() == getOrderFoundationData
        wrapper.getSearchTripBasicInfoResponseTypeOfApprovalFlowReuse() == searchTripBasicInfo
        wrapper.getCheckAvailInfo() == checkAvailInfo
        wrapper.getGetCityBaseInfoResponseType() == getCityBaseInfo
        wrapper.getGetCityBaseInfoResponseTypeOfApprovalFlowReuse() == getCityBaseInfoOfReuse
        wrapper.getQconfigOfCertificateInitConfig() == qconfig
        wrapper.getPolicyGetCorpUserInfoResponseTypeOfApprovalFlowReuse() == policyGetCorpUserInfoOfReuse
        wrapper.getPolicyGetCorpUserInfoResponseType() == policyGetCorpUserInfo
    }

    def "builder should allow partial build"() {
        when:
        def wrapper = WrapperOfApprovalFlowReuse.builder()
                .withOrderCreateRequestType(Mock(OrderCreateRequestType))
                .build()

        then:
        wrapper.getOrderCreateRequestType() != null
        wrapper.getQueryHotelAuthExtensionResponseType() == null
    }
}
