package com.ctrip.corp.bff.hotel.book.common.wrapper

import com.ctrip.corp.agg.hotel.roomavailable.entity.BookRoomInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ReservationResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.hotel.book.common.mapper.MapperOfCheckTravelPolicyRequestType
import com.ctrip.corp.bff.hotel.book.common.util.HotelPayTypeUtil
import com.ctrip.corp.bff.hotel.book.contract.CostCenterInfo
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2024/12/9 21:15
 *
 */
class WrapperOfCheckTravelPolicyTest extends Specification {
    def "checkTravelPolicy-build"() {
        given:
        def queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(gdsType: "", balanceType: "PP"))
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo()
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>(){
                    {
                        put("RepeatOrderControlCorp", "T")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType();
        when:
        WrapperOfCheckTravelPolicy checkTravelPolicy = WrapperOfCheckTravelPolicy.builder()
                .setResourceToken(new ResourceToken())
                .setCheckAvailInfo(checkAvailInfo)
                .setAccountInfo(accountInfo)
                .setHotelPolicyInput(orderCreateRequestType.getHotelPolicyInput())
                .setIntegrationSoaRequestType(orderCreateRequestType.getIntegrationSoaRequestType())
                .setApprovalInput(orderCreateRequestType.getApprovalInput())
                .setAddPriceInput(orderCreateRequestType.getAddPriceInput())
                .setHotelBookInput(orderCreateRequestType.getHotelBookInput())
                .setScene(MapperOfCheckTravelPolicyRequestType.SCENE_PAY_TYPE_FROM_INPUT)
                .setHotelInsuranceInput(orderCreateRequestType.getHotelInsuranceInput())
                .setHotelPayTypeInputs(orderCreateRequestType.getHotelPayTypeInput())
                .setGetSupportedPaymentMethodResponseType(null)
                .setFlashStayInput(orderCreateRequestType.getFlashStayInput())
                .setCalculateServiceChargeV2ResponseType(null)
                .setRcInfos(orderCreateRequestType.getRcInfos())
                .setAllocationResultToken(null)
                .setHotelBookPassengerInputs(orderCreateRequestType.getHotelBookPassengerInputs())
                .setCityInput(orderCreateRequestType.getCityInput())
                .setCorpPayInfo(orderCreateRequestType.getCorpPayInfo())
                .setCostCenterStr(
                        Optional.ofNullable(orderCreateRequestType.getCostCenterInfo())
                                .map(CostCenterInfo::getCostCenterJsonString).orElse(null))
                .setRoomPayType(HotelPayTypeUtil.getRoomPayType(orderCreateRequestType.getHotelPayTypeInput()))
                .setStrategyInfos(Arrays.asList(new StrategyInfo(strategyKey: "hotelcheck", strategyValue: "T")))
                .build();
        then:
        checkTravelPolicy != null
        checkTravelPolicy.strategyInfos.get(0).strategyValue == "T"
    }


    @Unroll
    def "testBuildPaymentTypeHotelCheckAvail with #description"() {
        given: "A MapperOfCheckTravelPolicyRequestType instance"
        def mapper = new MapperOfCheckTravelPolicyRequestType()
        def hotelPayTypeInputs = hotelPayTypeInputsList
        def accountInfo = Mock(WrapperOfAccount.AccountInfo)
        accountInfo.isOpenPersonalPayment() >> openPersonalPayment
        accountInfo.supportCorpPay(_ as CorpPayInfo, _ as Boolean, _ as Boolean, _ as Boolean) >> supportCorpPay
        def corpPayInfo = new CorpPayInfo()
        def resourceToken = new ResourceToken()

        when: "Calling buildPaymentTypeHotelCheckAvail method"
        def result = mapper.buildPaymentTypeHotelCheckAvail(hotelPayTypeInputs, accountInfo, corpPayInfo, resourceToken)

        then: "The result should match the expected outcome"
        result == expectedResult

        where:
        description                | hotelPayTypeInputsList                                       | supportCorpPay | openPersonalPayment || expectedResult
        "empty hotelPayTypeInputs" | []                                                           | false          | false                 || "UNKNOWN"
        "MIX_PAY type"             | [new HotelPayTypeInput(payType: "MIX_PAY", payCode: "ROOM")] | false          | false                 || "MIX_PAY"
        "CORP_PAY type"            | []                                                           | true           | false                 || "CORP_PAY"
    }
}
