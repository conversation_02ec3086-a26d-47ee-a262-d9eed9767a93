package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType
import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;

class MapperOfSubmitOrderRequestTypeTest extends Specification {

    def tester = Spy(new MapperOfSubmitOrderRequestType())
    def savePoint = new SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        given:
        expect:
        tester.convert(Tuple2.of(new CreateOrderResponseType(orderId: 2), new OrderCreateToken())).orderId == 2
    }
}
