package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import mockit.internal.state.SavePoint
import spock.lang.Specification

class MapperOfQueryHotelOrderDataRequestTest extends Specification {

    def tester = Spy( new MapperOfQueryHotelOrderDataRequest() )
    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        expect:
        tester.convert(Tuple2.of(new IntegrationSoaRequestType(), 2l)).orderId == 2
    }
}
