package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import corp.user.service.costcenterService.matchCostCenter.MatchCostCenterRequestType
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2024/12/30 15:06
 *
 */
class MapperOfMatchCostCenterInfoRequestTypeTest extends Specification {


    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }


    def "testConvert"() {
        given: "Mock dependencies and inputs"
        def mapper = new MapperOfMatchCostCenterInfoRequestType()
        def orderCreateRequestType = new OrderCreateRequestType()
        def integrationSoaRequestType = new IntegrationSoaRequestType()
        integrationSoaRequestType.setRequestId("testRequestId")
        integrationSoaRequestType.setUserInfo(new UserInfo(userId: "testUserId"))
        orderCreateRequestType.setIntegrationSoaRequestType(integrationSoaRequestType)
        def hotelPolicyInput = new HotelPolicyInput(policyInput: new PolicyInput(policyUid: "testPolicyUid"))
        orderCreateRequestType.setHotelPolicyInput(hotelPolicyInput)

        when: "Calling convert method"
        def result = mapper.convert(Tuple1.of(orderCreateRequestType))

        then: "Assert the expected outcomes"
        result != null
        result instanceof MatchCostCenterRequestType
        result.getRid() == "testRequestId"
        result.getUid() == "testPolicyUid"
        result.getFilter() == 1
    }
}
