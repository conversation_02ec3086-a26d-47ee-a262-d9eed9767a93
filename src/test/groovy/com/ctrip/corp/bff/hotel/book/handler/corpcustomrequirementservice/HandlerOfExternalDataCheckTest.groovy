package com.ctrip.corp.bff.hotel.book.handler.corpcustomrequirementservice

import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2025/6/24 23:06
 *
 */
class HandlerOfExternalDataCheckTest extends Specification {

    def savePoint = new SavePoint()

    def setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "testHandle"() {
        given:
        def handler = new HandlerOfExternalDataCheck()

        when:
        def response = handler.getMethodName()

        then:
        response == "externalDataCheck"
    }
}
