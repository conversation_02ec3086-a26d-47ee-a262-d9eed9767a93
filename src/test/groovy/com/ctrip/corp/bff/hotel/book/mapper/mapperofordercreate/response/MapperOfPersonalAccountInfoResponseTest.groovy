package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response

import com.ctrip.corp.agg.hotel.salestrategy.entity.CalculateTravelRewardsResponseType
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.hotel.book.common.enums.HotelBalanceTypeEnum
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import corp.user.service.CorpAccountQueryService.CashBackConfigs
import corp.user.service.CorpAccountQueryService.HotelControlConfigs
import corp.user.service.CorpAccountQueryService.IAccountLocale
import corp.user.service.CorpAccountQueryService.IndividualAccountRule
import corp.user.service.CorpAccountQueryService.QueryIndividualAccountResponseType
import mockit.internal.state.SavePoint
import org.junit.Ignore
import spock.lang.Shared
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4
import spock.lang.Unroll;

class MapperOfPersonalAccountInfoResponseTest extends Specification {

    def tester = Spy(MapperOfPersonalAccountInfoResponse)

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    @Shared
    def onlySelfQueryIndividual = new QueryIndividualAccountResponseType(individualAccountConfig: new IndividualAccountRule(hotelControlConfigs: new HotelControlConfigs(hotelGuestAccess: "B")))

    @Unroll
    def "Convert"() {
        given:
        tester.isBookingWithPersonalAccount(_) >> isBookingWithPersonalAccount
        tester.isClientOnlySelf(_, _) >> isClientContainSelf
        tester.isAlreadyAlert(_) >> isAlreadyAlert
        def input = Mock(CalculateTravelRewardsResponseType)
        input.isCanCalculateTravelRewards() >> isSupportBonusPersonalAccount
        input.getNoCalculateReasonCode() >> (isNotSelf ? "R0001" : null)
        expect:
        tester.convert(null) == null
        tester.convert(Tuple4.of(null, null, onlySelfQueryIndividual, null)) == null
        def temp = tester.convert(Tuple4.of(new OrderCreateRequestType(), input, onlySelfQueryIndividual, new OrderCreateToken()))
        block == temp?.t1
        confirmCode == temp?.t2?.confirmInfo?.confirmDetailInfos?.get(0)?.code
        where:
        isBookingWithPersonalAccount | isSupportBonusPersonalAccount | isNotSelf | isClientContainSelf | isAlreadyAlert || block | confirmCode                                     | desc
        true                         | false                         | false    | false               | Ignore         || true  | "NOT_ALLOW_BOOKING_WITH_PERSONAL_ACCOUNT"       | "使用心程贝但入住人不包含本人"
        true                         | false                         | false    | true                | false          || true  | "BOOKING_WITH_PERSONAL_ACCOUNT_TIP"             | "使用心程贝+入住人包含本人+未提示过"
        true                         | false                         | false    | true                | true           || null  | null                                            | "使用心程贝+入住人包含本人+已经提示过"
        true                         | true                          | false    | false               | Ignore         || true  | "NOT_ALLOW_BOOKING_BONUS_WITH_PERSONAL_ACCOUNT" | "使用心程贝+奖励心程贝+入住人不包含本人"
        true                         | true                          | false    | true                | false          || true  | "BOOKING_WITH_PERSONAL_ACCOUNT_TIP"             | "使用心程贝+奖励心程贝+入住人包含本人+未提示过"
        true                         | true                          | false    | true                | true           || null  | null                                            | "使用心程贝+奖励心程贝+入住人包含本人+已提示过"
        false                        | true                          | false    | true                | Ignore         || null  | null                                            | "奖励心程贝+入住人包含本人"
        false                        | false                         | true      | Ignore              | false          || true  | "NOT_ALLOW_BONUS_WITH_PERSONAL_ACCOUNT"         | "奖励心程贝+入住人不包含本人+未提示过"
        false                        | true                          | false    | false               | true           || null  | null                                            | "奖励心程贝+入住人不包含本人+已提示过"
        false                        | false                         | false    | Ignore              | Ignore         || null  | null                                            | "非奖励+非使用心程贝"

    }

    def "isAlreadyAlert"() {
        expect:
        !tester.isAlreadyAlert(null)
        !tester.isAlreadyAlert(new OrderCreateToken())
        !tester.isAlreadyAlert(new OrderCreateToken(continueTypes: []))
        !tester.isAlreadyAlert(new OrderCreateToken(continueTypes: [null]))
        tester.isAlreadyAlert(new OrderCreateToken(continueTypes: ["PERSONAL_ACCOUNT_TIP"]))
    }

    def "isBookingWithPersonalAccount"() {
        expect:
        !tester.isBookingWithPersonalAccount(null)
        !tester.isBookingWithPersonalAccount([])
        !tester.isBookingWithPersonalAccount([new StrategyInfo()])
        !tester.isBookingWithPersonalAccount([null, new StrategyInfo()])
        !tester.isBookingWithPersonalAccount([null, new StrategyInfo(strategyKey: "BOOKING_WITH_PERSONAL_ACCOUNT")])
        !tester.isBookingWithPersonalAccount([null, new StrategyInfo(strategyKey: "BOOKING_WITH_PERSONAL_ACCOUNT", strategyValue: "F")])
        tester.isBookingWithPersonalAccount([null, new StrategyInfo(strategyKey: "BOOKING_WITH_PERSONAL_ACCOUNT", strategyValue: "T")])
    }

    @Unroll
    def "isClientContainSelf"() {
        expect:
        !tester.isClientOnlySelf(null, null)
        !tester.isClientOnlySelf([], null)
        !tester.isClientOnlySelf([], new IntegrationSoaRequestType())
        !tester.isClientOnlySelf([null], new IntegrationSoaRequestType())
        !tester.isClientOnlySelf([null, new HotelBookPassengerInput()], new IntegrationSoaRequestType(userInfo: new UserInfo()))
        !tester.isClientOnlySelf([null, new HotelBookPassengerInput()], new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "")))
        !tester.isClientOnlySelf([null, new HotelBookPassengerInput()], new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "uid")))
        !tester.isClientOnlySelf([null, new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput()), null], new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "uid")))
        !tester.isClientOnlySelf([null, new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "uid2")), null], new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "uid")))
        tester.isClientOnlySelf([null, new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "uid")), null], new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "uid")))
    }


    def "BuildConfirmDetailExtend"() {
        expect:
        tester.buildConfirmDetailExtend(null, null) == null
        tester.buildConfirmDetailExtend(new QueryIndividualAccountResponseType(), null) == null
        tester.buildConfirmDetailExtend(new QueryIndividualAccountResponseType(individualAccountConfig: new IndividualAccountRule()), null) == null
        tester.buildConfirmDetailExtend(new QueryIndividualAccountResponseType(individualAccountConfig: new IndividualAccountRule(nameList: [])), null) == null
        tester.buildConfirmDetailExtend(new QueryIndividualAccountResponseType(individualAccountConfig: new IndividualAccountRule(nameList: [new IAccountLocale(locale: "zh-CN", localeValue: "res")])), null) == null
        tester.buildConfirmDetailExtend(new QueryIndividualAccountResponseType(individualAccountConfig: new IndividualAccountRule(nameList: [new IAccountLocale(locale: "zh-CN", localeValue: "res")])), new IntegrationSoaRequestType(language: "en-US")) == null
        tester.buildConfirmDetailExtend(new QueryIndividualAccountResponseType(individualAccountConfig: new IndividualAccountRule(nameList: [new IAccountLocale(locale: "zh-CN", localeValue: "res")])), new IntegrationSoaRequestType(language: "zh-CN")).value == "res"
    }

    def "buildAccountLocale"() {
        expect:
        tester.buildAccountLocale(req) == res
        where:
        req                                              || res
        null                                             || null
        new IntegrationSoaRequestType()                  || null
        new IntegrationSoaRequestType(language: "")      || null
        new IntegrationSoaRequestType(language: "zh-CN") || "zh-CN"
        new IntegrationSoaRequestType(language: "en-US") || "en"
        new IntegrationSoaRequestType(language: "other") || "en"
    }

}
