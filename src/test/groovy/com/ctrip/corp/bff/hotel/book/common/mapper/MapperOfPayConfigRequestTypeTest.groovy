package com.ctrip.corp.bff.hotel.book.common.mapper

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;

class MapperOfPayConfigRequestTypeTest extends Specification {

    def tester = Spy(new MapperOfPayConfigRequestType())

    def savePoint = new mockit.internal.state.SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        expect:
        tester.convert(Tuple1.of(new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "a")))).corpId == "a"
    }
}
