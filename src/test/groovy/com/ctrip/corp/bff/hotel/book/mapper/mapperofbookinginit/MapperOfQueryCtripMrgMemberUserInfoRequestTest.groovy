package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.response.MapperOfBookingInitResponse
import corp.user.service.corpUserInfoService.GetPlatformRelationByUidResponseType
import corp.user.service.group4j.accounts.BizModeBindRelationData
import corp.user.service.group4j.accounts.QueryBizModeBindRelationResponseType
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @Date 2024/12/5 16:41
 */
class MapperOfQueryCtripMrgMemberUserInfoRequestTest extends Specification {

    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }
    void testGetRegisterUid_TripRegister() {
        given:
        MapperOfQueryCtripMrgMemberUserInfoRequest mapper = new MapperOfQueryCtripMrgMemberUserInfoRequest();
        expect:
        mapper.getRegisterUid(registerRule, "uid", response) == result;
        where:
        registerRule               | response                                                         || result
        "TRIP_REGISTER"            | new GetPlatformRelationByUidResponseType(accountId: "accpuntId") || "accpuntId"
        "NO_REGISTER"              | new GetPlatformRelationByUidResponseType(accountId: "accpuntId") || null
        "BUSINESS_TRAVEL_REGISTER" | new GetPlatformRelationByUidResponseType(accountId: "accpuntId") || "uid"
        "123"                      | new GetPlatformRelationByUidResponseType(accountId: "accpuntId") || "uid"
    }


    def "getPrimaryId"() {
        given:
        MapperOfQueryCtripMrgMemberUserInfoRequest mapper = new MapperOfQueryCtripMrgMemberUserInfoRequest();

        expect:
        mapper.getPrimaryId(rep, uid) == res
        where:
        uid  | rep                                                                                                                           || res
        null | null                                                                                                                          || null
        "a"  | new QueryBizModeBindRelationResponseType()                                                             || null
        "b"  | new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [])                                                     || null
        "c"  | new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [null])                                                 || null
        "d"  | new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData()]) || null
        "a"  | new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(primaryDimensionId: "2")]) || null
        "a"  | new QueryBizModeBindRelationResponseType(bizModeBindRelationDataList: [new BizModeBindRelationData(dimensionId: "a", primaryDimensionId: "2")]) || "2"


    }
}
