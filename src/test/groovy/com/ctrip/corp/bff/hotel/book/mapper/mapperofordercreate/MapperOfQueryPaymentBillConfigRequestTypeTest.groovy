package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ReservationResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.CreateOrderResult
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.PosEnum
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput
import com.ctrip.corp.bff.framework.template.mapper.param.ParamCheckResult
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple5
import com.ctrip.corp.bff.hotel.book.common.enums.OrderCreateErrorEnum
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.contract.PayMentInfoInput
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType
import com.ctrip.corp.order.paymentcenter.bill.contract.ExtendParamType
import com.ctrip.corp.order.paymentcenter.bill.contract.HotelConfigRequestType
import com.ctrip.corp.order.paymentcenter.bill.contract.QueryPaymentBillConfigRequestType
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2025/1/3 18:56
 *
 */
class MapperOfQueryPaymentBillConfigRequestTypeTest extends Specification {


    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }

    @Unroll
    def "testCheck with #description"() {
        given: "Mock dependencies and inputs"
        def mapper = new MapperOfQueryPaymentBillConfigRequestType()
        def resourceToken = new ResourceToken()
        def createOrderResponseType = new CreateOrderResponseType()
        def orderCreateRequestType = new OrderCreateRequestType(hotelPayTypeInput: Arrays.asList(new HotelPayTypeInput(payCode: "ROOM", payType: "FLASH_STAY_PAY")))
        def orderCreateToken = new OrderCreateToken(useOrderCreate: useOrderCreate, createOrderResult: createOrderResult)

        when: "Calling check method"
        def result = mapper.check(Tuple5.of(resourceToken, createOrderResponseType, orderCreateRequestType, orderCreateToken, false))

        then: "The result should match the expected outcome"
        result == expectedResult

        where: "Different scenarios for testing"
        description                                                       | useOrderCreate | createOrderResult                            || expectedResult
        "useOrderCreate is false"                                         | false          | null                                         || null
        "requirePaymentOrderCreate is false"                              | true           | null                                         || null
    }

    @Unroll
    def "test buildExtendParamList with #description"() {
        given: "MapperOfQueryPaymentBillConfigRequestType instance"
        def mapper = new MapperOfQueryPaymentBillConfigRequestType()

        when: "Calling buildExtendParamList method"
        List<ExtendParamType> result = mapper.buildExtendParamList(offlineNewPay)

        then: "The result should match the expected outcome"
        result == expectedResult

        where: "Different scenarios for testing"
        description                       | offlineNewPay || expectedResult
        "offlineNewPay is true"           | true          || [new ExtendParamType(key: "formGrayFlag", value: "true")]
        "offlineNewPay is false"          | false         || null
    }


    @Unroll
    def "test convert with #description"() {
        given: "Mock dependencies and inputs"
        def mapper = new MapperOfQueryPaymentBillConfigRequestType()
        def resourceToken = new ResourceToken(
                reservationResourceToken: new ReservationResourceToken(wsId: "wsId"),
                roomResourceToken: new RoomResourceToken(balanceType: "PP"))
        def createOrderResponseType = new CreateOrderResponseType()
        def orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(
                        sourceFrom: sourceFrom,
                        userInfo: new UserInfo(corpId: "corpId", userId: "userId", pos: PosEnum.CHINA),
                        requestId: "requestId",
                        language: "language"
                ),
                corpPayInfo: new CorpPayInfo(corpPayType: "private"),
                hotelPolicyInput: new HotelPolicyInput(policyInput: new PolicyInput(policyUid: "policyUid"))
        )
        def orderCreateToken = new OrderCreateToken(useOrderCreate: useOrderCreate)
        def tuple = Tuple5.of(resourceToken, createOrderResponseType, orderCreateRequestType, orderCreateToken, offlineNewPay)

        when: "Calling convert method"
        QueryPaymentBillConfigRequestType result = mapper.convert(tuple)

        then: "The result should match the expected outcome"
        result.extendParamList == extendParamList

        where: "Different scenarios for testing"
        description      | sourceFrom         | useOrderCreate | offlineNewPay || extendParamList
        "online source"  | SourceFrom.Online  | true           | true          || [new ExtendParamType(key: "formGrayFlag", value: "true")]
        "offline source" | SourceFrom.Offline | true           | false         || null
    }


    def "testGetHotelConfigRequestType"() {
        given: "Mocked inputs and dependencies"
        def mapper = new MapperOfQueryPaymentBillConfigRequestType()
        def createOrderResponseType = Mock(CreateOrderResponseType)
        def orderCreateRequestType = Mock(OrderCreateRequestType) {
            getHotelPolicyInput() >> Mock(HotelPolicyInput)
        }
        def resourceToken = new ResourceToken(
                reservationResourceToken: new ReservationResourceToken(wsId: "wsId"),
                roomResourceToken: new RoomResourceToken(balanceType: "PP"))
        def orderCreateToken = Mock(OrderCreateToken)

        when: "Calling getHotelConfigRequestType"
        HotelConfigRequestType result = mapper.getHotelConfigRequestType(createOrderResponseType, orderCreateRequestType, resourceToken, orderCreateToken)

        then: "The result should match the expected outcome"
        result.transactionId == "wsId"
    }
}
