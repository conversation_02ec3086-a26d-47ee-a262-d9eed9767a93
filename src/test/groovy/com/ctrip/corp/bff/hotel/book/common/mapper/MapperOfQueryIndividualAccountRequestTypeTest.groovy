package com.ctrip.corp.bff.hotel.book.common.mapper

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;

class MapperOfQueryIndividualAccountRequestTypeTest extends Specification {

    def tester = Spy(new MapperOfQueryIndividualAccountRequestType())

    def savePoint = new mockit.internal.state.SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        expect:
        tester.convert(Tuple1.of(new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")))) .searchKey == "a"
    }
}
