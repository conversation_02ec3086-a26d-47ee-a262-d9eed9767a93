package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import corp.user.service.corpUserInfoService.GetCorpUserInfoRequestType
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2024/12/13 14:09
 *
 */
class MapperOfGetCorpUserInfoRequestPolicyTest extends Specification {

    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }
    MapperOfGetCorpUserInfoRequestPolicy mapper = new MapperOfGetCorpUserInfoRequestPolicy()

    def "testConvert with default scenario"() {
        given:
        PolicyInput policyInput = new PolicyInput(policyUid: "policy123")
        HotelPolicyInput hotelPolicyInput = new HotelPolicyInput(policyInput: policyInput)

        when:
        GetCorpUserInfoRequestType result = mapper.convert(Tuple1.of(hotelPolicyInput) as Tuple1<HotelPolicyInput>)

        then:
        result.getUid() == "policy123"
    }

}
