package com.ctrip.corp.bff.hotel.book.common.enums

import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2024/12/2 15:08
 *
 */
class InvoiceTitleTypeEnumTest extends Specification {

    @Unroll
    def "testGetAllShowTitleType with #description"() {
        expect:
        InvoiceTitleTypeEnum.getAllShowTitleType(defaultInvoice) == expectedResult

        where:
        description                       | defaultInvoice            | expectedResult
        "defaultInvoice is D_VAT_INVOICE" | InvoiceEnum.D_VAT_INVOICE | ["TITLE_TYPE_E"]
        "defaultInvoice is S_VAT"         | InvoiceEnum.S_VAT         | ["TITLE_TYPE_E", "TITLE_TYPE_G"]
        "defaultInvoice is null"          | null                      | ["TITLE_TYPE_E", "TITLE_TYPE_G", "TITLE_TYPE_P"]
    }
}

