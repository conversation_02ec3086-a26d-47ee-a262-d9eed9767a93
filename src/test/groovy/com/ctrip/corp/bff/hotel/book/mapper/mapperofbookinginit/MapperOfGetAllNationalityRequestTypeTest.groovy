package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import mockit.internal.state.SavePoint
import spock.lang.Specification

class MapperOfGetAllNationalityRequestTypeTest extends Specification {

    def tester = Spy(MapperOfGetAllNationalityRequestType)

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        expect:
        tester.convert(null) == null
        tester.convert(Tuple1.of(null)) == null
        tester.convert(Tuple1.of(new IntegrationSoaRequestType())) == null
        tester.convert(Tuple1.of(new IntegrationSoaRequestType(language: "en-US"))).locale == "en-US"
    }
}
