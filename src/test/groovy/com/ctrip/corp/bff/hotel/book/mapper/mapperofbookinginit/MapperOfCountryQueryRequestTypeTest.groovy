package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.agg.hotel.roomavailable.entity.NationalityRestrictionType
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;

class MapperOfCountryQueryRequestTypeTest extends Specification {

    def tester = Spy(MapperOfCountryQueryRequestType)

    def savePoint = new SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "buildCountryCodeList" () {
        expect:
        tester.buildCountryCodeList(null, null) == []
        tester.buildCountryCodeList(null, ["en"]) == ["en"]
        tester.buildCountryCodeList(["CN"], null) == ["CN"]
    }

    def "convert" () {
        expect:
        tester.convert(null) == null
        tester.convert(Tuple2.of(new IntegrationSoaRequestType(), null)) == null
        tester.convert(Tuple2.of(new IntegrationSoaRequestType(), new NationalityRestrictionType())) == null
        tester.convert(Tuple2.of(new IntegrationSoaRequestType(), new NationalityRestrictionType(blockCountryCodeList: ["CN"]))).countryCodeList == ["CN"]

    }
}
