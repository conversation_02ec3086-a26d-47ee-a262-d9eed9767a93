package com.ctrip.corp.bff.hotel.book.common.constant

import com.ctrip.corp.bff.framework.template.entity.ActionInfo
import com.ctrip.corp.bff.framework.template.entity.MapString
import com.ctrip.corp.bff.hotel.book.qconfig.QConfigOfCodeMappingConfig
import com.ctrip.corp.bff.hotel.book.qconfig.entity.CodeMappingConfig
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2025/7/15 17:08
 *
 */
class SoaErrorSharkKeyConstantTest extends Specification {
    @Unroll
    def "testBuildActionInfo with #description"() {
        given: "Mocked dependencies"
        def mappingActionRelations = new HashMap<String, String>()
        mappingActionRelations.put("6003", "ReloadHotelBaseOrToHotelDetail")
        def qConfigOfCodeMappingConfig = new QConfigOfCodeMappingConfig(
                codeMappingEntities: [new CodeMappingConfig(
                        actionName: "corphotelbookserviceclient.createorder",
                        mappingActionRelations: mappingActionRelations)
                ]
        )
        when: "Calling buildActionInfo"
        def result = SoaErrorSharkKeyConstant.buildActionInfo(responseCode, qConfigOfCodeMappingConfig, "corphotelbookserviceclient.createorder", [new MapString("key", "value")])

        then: "The result should match the expected value"
        result == expectedResult

        where:
        description                          | responseCode                 | expectedResult
        "responseCode is null"               | null                         | null
        "responseCode is SUCCESS_20000"      | CommonConstant.SUCCESS_20000 | null
        "qConfigOfCodeMappingConfig is null" | 3001                         | null
        "valid inputs"                       | 6003                         | new ActionInfo(actionType: "ReloadHotelBaseOrToHotelDetail", dataExtInfo: [new MapString("key", "value")])
    }
}
