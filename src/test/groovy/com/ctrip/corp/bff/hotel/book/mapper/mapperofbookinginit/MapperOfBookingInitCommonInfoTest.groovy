package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.bff.framework.hotel.common.util.HotelDateRangeUtil
import com.ctrip.corp.bff.framework.hotel.entity.contract.AddPriceInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.contract.integration.AmountInfo
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification

class MapperOfBookingInitCommonInfoTest extends Specification{

    def mapperOfBookingInitCommonInfo = Spy(new MapperOfBookingInitCommonInfo())

    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        given:
        HotelDateRangeInfo rangeInfo = new HotelDateRangeInfo();
        rangeInfo.setCheckIn("2024-08-28");
        rangeInfo.setCheckOut("2024-08-30");
        HotelBookInput hotelBookInput = new HotelBookInput();
        hotelBookInput.hotelDateRangeInfo = new HotelDateRangeInfo();
        hotelBookInput.hotelDateRangeInfo.setCheckIn("2024-08-28")
        hotelBookInput.hotelDateRangeInfo.setCheckOut("2024-08-30")
        hotelBookInput.roomQuantity = 1
        when:

        expect:
        2 == HotelDateRangeUtil.getRoomNights(hotelBookInput);
    }


    def "getRoomPayType" () {
        expect:
        mapperOfBookingInitCommonInfo.getRoomPayType(defaultPayment, hotelBookingFilterByPolicy,payList ) == res
        where:
        defaultPayment | hotelBookingFilterByPolicy | payList | res
        null | null | [HotelPayTypeEnum.UNION_PAY] | HotelPayTypeEnum.UNION_PAY
        HotelPayTypeEnum.CORP_PAY | null | [HotelPayTypeEnum.CORP_PAY] || HotelPayTypeEnum.CORP_PAY
        null | null | [HotelPayTypeEnum.FLASH_STAY_PAY] || HotelPayTypeEnum.FLASH_STAY_PAY
        null | "S" | [HotelPayTypeEnum.MIX_PAY] || HotelPayTypeEnum.MIX_PAY
        null | null | [HotelPayTypeEnum.CORP_PAY] || HotelPayTypeEnum.CORP_PAY
        null | null | [HotelPayTypeEnum.SELF_PAY] || HotelPayTypeEnum.SELF_PAY
        null | null | [HotelPayTypeEnum.ADVANCE_PAY] || HotelPayTypeEnum.ADVANCE_PAY

    }

    def "getAddPriceAmount" () {
        given:
        mapperOfBookingInitCommonInfo.canAddPrice(_,_)>> true
        new MockUp<HotelDateRangeUtil>() {
            @Mock
            public static int getRoomNights(HotelBookInput hotelBookInput) {
                return 1
            }
            }
        expect:
        mapperOfBookingInitCommonInfo.getAddPriceAmount(new BookingInitRequestType(addPriceInput: new AddPriceInput(amountInfo: new AmountInfo(amount: 2)), integrationSoaRequestType: new IntegrationSoaRequestType()), Mock(WrapperOfCheckAvail.CheckAvailInfo), null).price == 2

    }


}
