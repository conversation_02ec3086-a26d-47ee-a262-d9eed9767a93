package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType
import com.ctrip.corp.pub.endorsement.contract.synctrip.SyncTripRequestType
import com.ctrip.soa._21234.CreateTripResponseType
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2024/12/13 13:15
 *
 */
class MapperOfSyncTripRequestTypeTest extends Specification {

    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }

    MapperOfSyncTripRequestType mapper = new MapperOfSyncTripRequestType()

    def "testConvert with default scenario"() {
        given:
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>(){
                    {
                        put("BillType", "E")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        CreateTripResponseType createTripResponseType = new CreateTripResponseType(tripId: 89089L)
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                approvalInput: new ApprovalInput(masterApprovalNo: "12345"),
                integrationSoaRequestType: new IntegrationSoaRequestType(requestId: "7897"))
        OrderCreateToken orderCreateToken = new OrderCreateToken()

        when:
        SyncTripRequestType result = mapper.convert(Tuple4.of(createTripResponseType, orderCreateRequestType, accountInfo, orderCreateToken) as
                Tuple4<CreateTripResponseType, OrderCreateRequestType, WrapperOfAccount.AccountInfo, OrderCreateToken>)

        then:
        result.getEndorsementId() == 12345L
        result.getTripIds() == [89089L]
        result.getAppId() == "*********"
        result.getSessionId() == "7897"
    }
}
