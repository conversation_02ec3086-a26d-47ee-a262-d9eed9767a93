package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceDetailInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.CorpXProductInfoToken
import com.ctrip.corp.bff.hotel.book.common.enums.HotelBalanceTypeEnum
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.order.data.aggregation.query.contract.AmountType
import com.ctrip.corp.order.data.aggregation.query.contract.FeeType
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataResponseType
import mockit.Mock
import mockit.MockUp
import spock.lang.Specification

class MapperOfOrderPriceChangeInfoResponseTest extends Specification {

    def tester = Spy(new MapperOfOrderPriceChangeInfoResponse())

    def savePoint = new mockit.internal.state.SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "calcPrice" () {
        expect:
        new MapperOfOrderPriceChangeInfoResponse().calcPrice(null, null) == 0
        new MapperOfOrderPriceChangeInfoResponse().calcPrice([BigDecimal.ONE], null) == 1
        new MapperOfOrderPriceChangeInfoResponse().calcPrice([BigDecimal.ONE], [BigDecimal.ONE]) == 0

    }

    def "buildPriceOfOrderPP" () {
        expect:
        new MapperOfOrderPriceChangeInfoResponse().buildPriceOfOrderPP(null, null) == 0
    }

    def "buildPriceChangeInfo" () {
        given:
        def input = Mock(WrapperOfCheckAvail.BaseCheckAvailInfo)
        input.getOriginCurrency() >> "CNY"
        input.getHotelBalanceTypeEnum() >> HotelBalanceTypeEnum.FG
        expect:
        new MapperOfOrderPriceChangeInfoResponse().buildPriceChangeInfo(input, null, null, null) == null
        new MapperOfOrderPriceChangeInfoResponse().buildPriceChangeInfo(input, new QueryHotelOrderDataResponseType(orderAmountInfo: new FeeType(totalAmountInfo: new AmountType(settlementCurrency: "CNY"))), null, new OrderCreateRequestType()) == null
    }

    def "buildXProductPrice" () {
        given:
        new MockUp<TokenParseUtil>() {
            @Mock
            public static <T> T parseToken(String token, Class<T> clazz) {
                return new CorpXProductInfoToken(priceMark: "");
            }
            }
        expect:
        new MapperOfOrderPriceChangeInfoResponse().buildXProductPrice(new OrderCreateRequestType(hotelInsuranceInput: null)) == 0
        new MapperOfOrderPriceChangeInfoResponse().buildXProductPrice(new OrderCreateRequestType(hotelInsuranceInput: new HotelInsuranceInput())) == 0
        new MapperOfOrderPriceChangeInfoResponse().buildXProductPrice(new OrderCreateRequestType(hotelInsuranceInput: new HotelInsuranceInput(hotelInsuranceDetailInputs: []))) == 0
        new MapperOfOrderPriceChangeInfoResponse().buildXProductPrice(new OrderCreateRequestType(hotelInsuranceInput: new HotelInsuranceInput(hotelInsuranceDetailInputs: [new HotelInsuranceDetailInput()]))) == 0
    }
}
