package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.OrderResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;

class MapperOfQueryHotelOrderDataRequestTypeTest extends Specification {

    def tester = Spy(new MapperOfQueryHotelOrderDataRequestType())

    def savePoint = new mockit.internal.state.SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }
    def "convert" () {
        given:
        expect:
        tester.convert(Tuple2.of(new ResourceToken(orderResourceToken: new OrderResourceToken()), new IntegrationSoaRequestType(sourceFrom: SourceFrom.Offline,userInfo: new UserInfo(userId: "a")))).uid == "a"
    }
}
