package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookingcheck

import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfSearchApproval
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple3
import com.ctrip.corp.bff.hotel.book.common.constant.CommonConstant
import com.ctrip.corp.bff.hotel.book.common.enums.exception.BookingCheckErrorEnum
import com.ctrip.corp.bff.hotel.book.service.ApprovalCheckInfo
import com.ctrip.corp.bff.hotel.book.service.BookingCheckRequestType
import com.ctrip.corp.corpsz.preapprovalws.common.contract.ResponseStatus
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.ApprovalConfigInfo
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.HotelApprovalInfo
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.HotelProductInfo
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.SearchApprovalResponseType
import com.ctrip.corp.corpsz.preapprovalws.common.contract.searchapproval.SearchApprovalResult
import com.ctrip.corp.foundation.common.util.StringUtilsExt
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2024/12/4 18:07
 *
 */
class MapperOfApprovalCheckInfoTest extends Specification {

    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }
    @Unroll
    def "testConvert with different scenarios"() {
        given:
        MapperOfApprovalCheckInfo mapper = new MapperOfApprovalCheckInfo()
        BookingCheckRequestType requestType = new BookingCheckRequestType(
                cityInput: new CityInput(cityId: 7),
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "corpId")),
                hotelBookInput: new HotelBookInput(hotelDateRangeInfo: new HotelDateRangeInfo(checkIn: "2024-12-03", checkOut: "2024-12-04"))
        )
        ResponseStatus responseStatus = new ResponseStatus(success: true, errorCode: null)
        SearchApprovalResponseType searchApprovalResponseType = new SearchApprovalResponseType(
                status: responseStatus,
                searchApprovalResult: new SearchApprovalResult(hotelApprovalInfoList: Arrays.asList(new HotelApprovalInfo()))
        )
        WrapperOfSearchApproval.ApprovalInfo approvalInfo = WrapperOfSearchApproval.builder()
                .searchApprovalResponseType(new SearchApprovalResponseType()).build()

        when: "calling convert with specific parameters"
        def result = mapper.convert(Tuple3.of(requestType, searchApprovalResponseType, approvalInfo) as Tuple3<BookingCheckRequestType, SearchApprovalResponseType, WrapperOfSearchApproval.ApprovalInfo>)

        then: "the result should be as expected"
        result == null
    }
}
