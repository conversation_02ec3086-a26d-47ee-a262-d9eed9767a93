package com.ctrip.corp.bff.hotel.book.handler.corpconfigurationservice

import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2025/6/24 23:06
 *
 */
class HandlerOfCustomConfigSearchTest extends Specification {

    def savePoint = new SavePoint()

    def setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "testHandle"() {
        given:
        def handler = new HandlerOfCustomConfigSearch()

        when:
        def response = handler.getMethodName()

        then:
        response == "customConfigSearch"
    }
}
