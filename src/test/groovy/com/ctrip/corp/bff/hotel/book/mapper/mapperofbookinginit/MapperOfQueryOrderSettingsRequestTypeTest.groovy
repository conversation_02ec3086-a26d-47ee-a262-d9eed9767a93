package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import spock.lang.Specification

class MapperOfQueryOrderSettingsRequestTypeTest extends Specification {

    def tester = Spy(MapperOfQueryOrderSettingsRequestType)

    def savePoint = new mockit.internal.state.SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert"() {
        expect:
        tester.convert(Tuple1.of(null)) == null
        tester.convert(Tuple1.of(2l)).orderId == 2l

    }
}
