package com.ctrip.corp.bff.hotel.book.qconfig

import com.ctrip.corp.bff.hotel.book.qconfig.entity.SourceDisplayConfig
import mockit.internal.state.SavePoint
import spock.lang.Specification

class QConfigOfContactConfigTest extends Specification {

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "isShowContactName"() {
        given:
        def tester = Spy(new QConfigOfContactConfig())
        1 * tester.isShowContactModule("name", _, _, _) >> true
        expect:
        tester.isShowContactName(null, null, null)
    }

    def "isShowContactEmail"() {
        given:
        def tester = Spy(new QConfigOfContactConfig())
        1 * tester.isShowContactModule("email", _, _, _) >> true
        expect:
        tester.isShowContactEmail(null, null, null)
    }

    def "isShowContactPhone"() {
        given:
        def tester = Spy(new QConfigOfContactConfig())
        1 * tester.isShowContactModule("phone", _, _, _) >> true
        expect:
        tester.isShowContactPhone(null, null, null)
    }

    def "isShowContactModule"() {
        given:
        def tester = Spy(new QConfigOfContactConfig())
        1 * tester.getSourceDisplayConfig(_, _) >> new SourceDisplayConfig()
        1 * tester.checkSupport(_, _, _, _) >> true
        expect:
        !tester.isShowContactModule("name", "", "channel", "version")

        !tester.isShowContactModule("", "source", "channel", "version")

        tester.isShowContactModule("name", "source", "channel", "version")

    }

    def "checkSupport"() {
        given:
        def tester = Spy(new QConfigOfContactConfig())
        expect:
        tester.checkSupport(not_supports, supports, corpId, uid) == res
        where:
        not_supports | supports | corpId | uid   | res
        "T"          | null     | null   | null || false
        "F"          | null     | null   | null || true
        "a,b"        | null     | null   | "b"  || false
        "a,b"        | null     | "a"    | null || false
        null         | "T"      | "a"    | null || true
        null         | "F"      | "a"    | null || false
        null         | "a"      | "a"    | null || true
        null         | "b"      | "a"    | null || false
    }
}
