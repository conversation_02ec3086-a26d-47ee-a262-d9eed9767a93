package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response

import com.ctrip.corp.agg.hotel.roomavailable.entity.BookRoomInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType
import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig
import com.ctrip.corp.bff.framework.hotel.common.util.PersonAccountUtil
import com.ctrip.corp.bff.framework.hotel.common.util.WaitFutureUtil
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.AllocationResultToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ReservationResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.common.serialize.ProtobufSerializerUtil
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalFlowInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.ApprovalInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PassengerBasicInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PhoneInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.BookModeInfo
import com.ctrip.corp.bff.hotel.book.contract.HotelContactorInfo
import com.ctrip.corp.bff.hotel.book.contract.MembershipInfo
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.contract.PointsInfo
import com.ctrip.corp.bff.hotel.book.contract.TeamRoomInfo
import com.ctrip.corp.bff.hotel.book.sharkmock.SharkMockUtil
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType
import com.ctrip.soa._21234.BasicInfo
import com.ctrip.soa._21234.SearchTripDetailResponseType
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType
import corp.user.service.CorpAccountQueryService.QueryIndividualAccountResponseType
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2024/7/11 8:49
 *
 */
class MapperOfParamValidTest extends Specification {

    def myTestClass = Spy(new MapperOfParamValidResponse())

    def savePoint = new SavePoint()

    void setup() {
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "test checkMembershipCardNumFormat method"() {
        given:
        def orderCreateRequestType = new OrderCreateRequestType()
        def resourceToken = new ResourceToken()
        resourceToken.roomResourceToken = new RoomResourceToken()
        def queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity())
        when:
        def result = myTestClass.checkMembershipCardNumFormat(orderCreateRequestType, queryCheckAvailContextResponseType)

        then:
        result == null

        when:
        orderCreateRequestType.membershipInfo = new MembershipInfo()
        orderCreateRequestType.membershipInfo.membershipNo = "12345678901234567890123456789012345678901234567890123456789012345678901234567890"
        result = myTestClass.checkMembershipCardNumFormat(orderCreateRequestType, queryCheckAvailContextResponseType)
        then:
        result == null


        when:
        queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(gdsType: "Amadues"))
        orderCreateRequestType.membershipInfo = new MembershipInfo()
        orderCreateRequestType.membershipInfo.membershipNo = "12345678901234567890123456789012345678901234567890123456789012345678901234567890"
        result = myTestClass.checkMembershipCardNumFormat(orderCreateRequestType, queryCheckAvailContextResponseType)

        then:
        result.errorCode == 479

        when:
        orderCreateRequestType.membershipInfo.membershipNo = "ABC123@"
        result = myTestClass.checkMembershipCardNumFormat(orderCreateRequestType, queryCheckAvailContextResponseType)

        then:
        result.errorCode == 479
    }


    def "test checkCostAllocationResult method"() {
        given:
        AllocationResultToken allocationResultToken = new AllocationResultToken()
        when:
        def result = myTestClass.checkCostAllocationResult(null)
        then:
        result == null

        when:
        result = myTestClass.checkCostAllocationResult(allocationResultToken)
        then:
        result == null

        when:
        allocationResultToken.totalAmount = BigDecimal.valueOf(100)
        allocationResultToken.settleAllocationAmount = new HashMap<>()
        allocationResultToken.getSettleAllocationAmount().put("A", BigDecimal.valueOf(50))
        allocationResultToken.getSettleAllocationAmount().put("B", BigDecimal.valueOf(50))
        result = myTestClass.checkCostAllocationResult(allocationResultToken)
        then:
        result == null

        when:
        allocationResultToken.totalAmount = BigDecimal.valueOf(200)
        result = myTestClass.checkCostAllocationResult(allocationResultToken)
        then:
        result.errorCode == 483
    }

    def "test checkDuplexModel method"() {
        given:
        OrderCreateRequestType request = new OrderCreateRequestType()
        request.setCorpPayInfo(new CorpPayInfo("public"))
        GetCorpUserInfoResponseType corpUserInfo = new GetCorpUserInfoResponseType()
        Map<String, String> map = new HashMap<String, String>();
        map.put("uid", "123")
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder().accountInfo(
                new GeneralSearchAccountInfoResponseType(results: map))
                .policyAccountInfo(null)
                .corpUserInfo(corpUserInfo)
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build()

        when:
        def result = myTestClass.checkDuplexModel(request, accountInfo)
        then:
        result == null

        when:
        request.setBookModeInfo(new BookModeInfo())
        result = myTestClass.checkDuplexModel(request, accountInfo)
        then:
        result == null

        when:
        request.corpPayInfo = new CorpPayInfo(corpPayType: "private")
        result = myTestClass.checkDuplexModel(request, accountInfo)
        then:
        result == null

        when:
        request.corpPayInfo = new CorpPayInfo(corpPayType: "public")
        request.bookModeInfo = new BookModeInfo(bookingType: "OTHER_ORDER_ROOM")
        corpUserInfo = new GetCorpUserInfoResponseType(hotelDuplexModel: "F")
        def searchAccountInfo = new GeneralSearchAccountInfoResponseType(results: map)
        accountInfo = WrapperOfAccount.builder()
                .accountInfo(searchAccountInfo)
                .policyAccountInfo(null)
                .corpUserInfo(corpUserInfo)
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build()
        request.hotelBookPassengerInputs = new ArrayList<>()
        result = myTestClass.checkDuplexModel(request, accountInfo)
        then:
        result.errorCode == 633

        when:
        corpUserInfo = new GetCorpUserInfoResponseType(hotelDuplexModel: "T")
        accountInfo = WrapperOfAccount.builder()
                .accountInfo(searchAccountInfo)
                .policyAccountInfo(null)
                .corpUserInfo(corpUserInfo)
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build()
        result = myTestClass.checkDuplexModel(request, accountInfo)
        then:
        result == null

        when:
        request.hotelBookPassengerInputs = new ArrayList<>()
        request.bookModeInfo = new BookModeInfo(bookingType: "SHARE_ROOM")
        request.hotelBookPassengerInputs.add(new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(employee: "T", uid: "uid1")))
        request.hotelBookPassengerInputs.add(new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(employee: "F", uid: "uid2")))
        request.integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "789"))
        result = myTestClass.checkDuplexModel(request, accountInfo)
        then:
        result.errorCode == 634

        when:
        request.hotelBookPassengerInputs.add(new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(employee: "F", uid: "uid3")))
        request.hotelBookPassengerInputs.add(new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(employee: "T", uid: "uid4")))
        request.integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "uid4"))
        result = myTestClass.checkDuplexModel(request, accountInfo)
        then:
        result.errorCode == 634

        when:
        request.hotelBookPassengerInputs = Arrays.asList(new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(employee: "T", uid: "uid4")),
                new HotelBookPassengerInput(hotelPassengerInput:  new HotelPassengerInput(employee: "F", uid: "uid3")),
                new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(employee: "F", uid: "uid3")))
        request.integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "uid4"))
        request.hotelBookInput = new HotelBookInput(roomQuantity: 2)
        result = myTestClass.checkDuplexModel(request, accountInfo)
        then:
        result.errorCode == 476
    }

    def "test checkEarnPointsClientPsg method"() {
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return null;
            }
        }
        given:
        OrderCreateRequestType request = new OrderCreateRequestType()
        QueryCheckAvailContextResponseType response = new QueryCheckAvailContextResponseType()

        when:
        def result = myTestClass.checkEarnPointsClientPsg(request, response)
        then:
        result == null

        when:
        request.setPointsInfo(new PointsInfo())
        result = myTestClass.checkEarnPointsClientPsg(request, response)
        then:
        result == null

        when:
        request.getPointsInfo().setNeedPoints("F")
        result = myTestClass.checkEarnPointsClientPsg(request, response)
        then:
        result == null

        when:
        response.setRoomInfo(new BookRoomInfoEntity())
        result = myTestClass.checkEarnPointsClientPsg(request, response)
        then:
        result == null

        when:
        response.getRoomInfo().setGdsType("GDS")
        request.getPointsInfo().setNeedPoints("T")
        request.hotelBookPassengerInputs = new ArrayList<>()
        result = myTestClass.checkEarnPointsClientPsg(request, response)
        then:
        result.errorCode == 631

        when:
        request.setHotelContactorInfo(new HotelContactorInfo())
        result = myTestClass.checkEarnPointsClientPsg(request, response)
        then:
        result.errorCode == 631

        when:
        request.getHotelContactorInfo().setPhoneInfo(new PhoneInfo())
        result = myTestClass.checkEarnPointsClientPsg(request, response)
        then:
        result.errorCode == 631

        when:
        request.hotelBookPassengerInputs = new ArrayList<>()
        request.hotelBookPassengerInputs.add(new HotelBookPassengerInput(phoneInfo: new PhoneInfo(phoneNo: "789", countryCode: "US")))
        request.hotelContactorInfo = new HotelContactorInfo(phoneInfo: new PhoneInfo(countryCode: "US", phoneNo: "123456"))
        result = myTestClass.checkEarnPointsClientPsg(request, response)
        then:
        result.errorCode == 631

        when:
        request.hotelBookPassengerInputs.add(new HotelBookPassengerInput(phoneInfo: new PhoneInfo(phoneNo: "123456", countryCode: "US")))
        result = myTestClass.checkEarnPointsClientPsg(request, response)
        then:
        result == null
    }


    def "test checkContinueCommitInfo method"() {
        given:
        def orderCreateRequestType = new OrderCreateRequestType()
        when:
        def result = myTestClass.checkContinueCommitInfo(orderCreateRequestType, null)
        then:
        result == null

        when:
        orderCreateRequestType.setOrderCreateToken("validToken")
        def orderCreateToken = new OrderCreateToken()
        orderCreateToken.addContinueTypes(ContinueTypeConst.SINGLE_APPROVAL_FLOW)
        orderCreateRequestType.approvalFlowInput = new ApprovalFlowInput()
        orderCreateRequestType.setOrderCreateToken(ProtobufSerializerUtil.pbSerialize(orderCreateToken, OrderCreateToken.class))
        result = myTestClass.checkContinueCommitInfo(orderCreateRequestType, orderCreateToken)
        then:
        result == null

        when:
        orderCreateRequestType.approvalFlowInput = null
        result = myTestClass.checkContinueCommitInfo(orderCreateRequestType, orderCreateToken)

        then:
        result.errorCode == 477
    }

    def "test checkBookingForBoss method"() {
        given:
        def orderCreateRequestType = new OrderCreateRequestType(approvalInput: new ApprovalInput(emergency: "T"),
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "corpid", userId: "self")),
                hotelBookPassengerInputs: Arrays.asList(new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "self", employee: "T"))))
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                return true
            }
        }
        when:
        def result = myTestClass.checkBookingForBoss(orderCreateRequestType)
        then:
        result.errorCode == 620

        when:
        orderCreateRequestType.hotelBookPassengerInputs.get(0).hotelPassengerInput.uid = "noself"
        result = myTestClass.checkBookingForBoss(orderCreateRequestType)
        then:
        result == null
    }

    def "test checkTravelPolicy method"() {
        given:
        def orderCreateRequestType = new OrderCreateRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public"),
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "corpid", userId: "self")),
                hotelBookPassengerInputs: Arrays.asList(new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "self", employee: "T"))))
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("hotelTravelStandControlModel", "P")
                    }
                }))
                .policyAccountInfo(null)
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build()
        when:
        def result = myTestClass.checkTravelPolicy(orderCreateRequestType, accountInfo)
        then:
        result.errorCode == 624

        when:
        orderCreateRequestType.hotelBookPassengerInputs.get(0).hotelPassengerInput.roomIndex = 1
        result = myTestClass.checkTravelPolicy(orderCreateRequestType, accountInfo)
        then:
        result == null
    }

    def "test checkWelfareRoomClientPsg method"() {
        given:
        def orderCreateRequestType = new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "corpid", userId: "self")),
                hotelBookPassengerInputs: Arrays.asList(new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "noself", employee: "T"))))
        def queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(welfareRoom: true))
        when:
        def result = myTestClass.checkWelfareRoomClientPsg(orderCreateRequestType, queryCheckAvailContextResponseType)
        then:
        result.errorCode == 619

        when:
        orderCreateRequestType.hotelBookPassengerInputs.get(0).hotelPassengerInput.uid = "self"
        result = myTestClass.checkWelfareRoomClientPsg(orderCreateRequestType, queryCheckAvailContextResponseType)
        then:
        result == null
    }

    def "test checkTripClientPsg method"() {
        given:
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                return true
            }
        }
        def orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(
                        userInfo: new UserInfo(corpId: "corpid", userId: "self"),
                        sourceFrom: SourceFrom.Online),
                hotelBookPassengerInputs: Arrays.asList(new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "self", employee: "T"))),
                corpPayInfo: new CorpPayInfo(corpPayType: "public"))
        def searchTripDetailResponseType = new SearchTripDetailResponseType(basicInfo: new BasicInfo(policyUid: "uid"))
        when:
        def result = myTestClass.checkTripClientPsg(orderCreateRequestType, searchTripDetailResponseType)
        then:
        result.errorCode == 618

        when:
        orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(
                        userInfo: new UserInfo(corpId: "corpid", userId: "self"),
                        sourceFrom: SourceFrom.Online),
                hotelBookPassengerInputs: Arrays.asList(new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "self", employee: "T")),
                        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "uid2", employee: "T"))),
                corpPayInfo: new CorpPayInfo(corpPayType: "public"))
        result = myTestClass.checkTripClientPsg(orderCreateRequestType, searchTripDetailResponseType)
        then:
        result.errorCode == 618

        when:
        orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(
                        userInfo: new UserInfo(corpId: "corpid", userId: "self"),
                        sourceFrom: SourceFrom.Online),
                hotelBookPassengerInputs: Arrays.asList(new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "uid", employee: "T"))),
                corpPayInfo: new CorpPayInfo(corpPayType: "public"))
        result = myTestClass.checkTripClientPsg(orderCreateRequestType, searchTripDetailResponseType)
        then:
        result == null
    }

    def "test checkPersonalAccount method"() {
        given:
        new MockUp<PersonAccountUtil>() {
            @Mock
            public static boolean supportPersonalAccount(QueryIndividualAccountResponseType queryIndividualAccountResponseType,
                                                         CorpPayInfo corpPayType, SourceFrom sourceFrom) {
                return false
            }
        }
        def orderCreateRequestType = new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "corpid", userId: "self")),
                bookModeInfo: new BookModeInfo(bookingWithPersonalAccount: "T"), strategyInfos: Arrays.asList(new StrategyInfo(strategyKey: "BOOKING_WITH_PERSONAL_ACCOUNT", strategyValue: "T")))
        def queryIndividualAccountResponseType = new QueryIndividualAccountResponseType()
        when:
        def result = myTestClass.checkPersonalAccount(orderCreateRequestType, queryIndividualAccountResponseType)
        then:
        result.errorCode == 610

        when:
        orderCreateRequestType.bookModeInfo.bookingWithPersonalAccount = "F"
        orderCreateRequestType.strategyInfos = null
        result = myTestClass.checkPersonalAccount(orderCreateRequestType, queryIndividualAccountResponseType)
        then:
        result == null
    }

    def "test checkClientPsg method"() {
        given:
        def orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "corpid")),
                cityInput: new CityInput(),
                teamRoomInfo: new TeamRoomInfo(teamRoom: "F"),
                hotelBookPassengerInputs: Arrays.asList(new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(infoId: "123"), name: "",
                        passengerBasicInfo: new PassengerBasicInfo(preferLastName: "", preferFirstName: "")))
        )
        def queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(gdsType: "", balanceType: "PP"))
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo()
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("hotelTravelStandControlModel", "P")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        when:
        def result = myTestClass.checkClientPsg(orderCreateRequestType, accountInfo, checkAvailInfo, null, null)
        then:
        result.errorCode == 484


        when:
        orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "corpid")),
                cityInput: new CityInput(),
                teamRoomInfo: new TeamRoomInfo(teamRoom: "F"),
                hotelBookPassengerInputs: Arrays.asList(new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(infoId: "123"), name: "name",
                        passengerBasicInfo: new PassengerBasicInfo(preferLastName: "", preferFirstName: "")))
        )
        queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(gdsType: "Amadues", balanceType: "PP"))
        checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo()
        result = myTestClass.checkClientPsg(orderCreateRequestType, accountInfo, checkAvailInfo, null, null)
        then:
        result.errorCode == 475

        when:
        orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "corpid")),
                cityInput: new CityInput(),
                teamRoomInfo: new TeamRoomInfo(teamRoom: "F"),
                hotelBookPassengerInputs: Arrays.asList(new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(infoId: ""), name: "name",
                        passengerBasicInfo: new PassengerBasicInfo(preferLastName: "", preferFirstName: "")))
        )
        checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo()
        result = myTestClass.checkClientPsg(orderCreateRequestType, accountInfo, checkAvailInfo, null, null)
        then:
        result.errorCode == 474

        when:
        orderCreateRequestType = new OrderCreateRequestType(
                hotelBookInput: new HotelBookInput(roomQuantity: 1),
                cityInput: new CityInput(),
                teamRoomInfo: new TeamRoomInfo(teamRoom: "T"),
                hotelBookPassengerInputs: Arrays.asList(new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(infoId: "123"), name: "name",
                        passengerBasicInfo: new PassengerBasicInfo(preferLastName: "zhang", preferFirstName: "san")))
        )
        checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo()
        result = myTestClass.checkClientPsg(orderCreateRequestType, accountInfo, checkAvailInfo, null, null)
        then:
        result == null


        when:
        orderCreateRequestType = new OrderCreateRequestType(
                hotelBookInput: new HotelBookInput(roomQuantity: 1),
                cityInput: new CityInput(),
                teamRoomInfo: new TeamRoomInfo(teamRoom: "T"),
                hotelBookPassengerInputs: Arrays.asList(
                        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(infoId: "123", roomIndex: 1, employee: "F"), name: "name",
                                passengerBasicInfo: new PassengerBasicInfo(preferLastName: "zhang", preferFirstName: "san")),
                        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "789", roomIndex: 1, employee: "T"), name: "name",
                                passengerBasicInfo: new PassengerBasicInfo(preferLastName: "li", preferFirstName: "si")),
                        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "456", roomIndex: 1, employee: "T"), name: "name",
                                passengerBasicInfo: new PassengerBasicInfo(preferLastName: "wang", preferFirstName: "wu"))
                )
        )
        queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(maxGuestCountPerRoom: 2, balanceType: "PP"))
        checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo()
        result = myTestClass.checkClientPsg(orderCreateRequestType, accountInfo, checkAvailInfo, null, null)
        then:
        def ex = thrown(BusinessException)
        ex.errorCode == 471


        when:
        orderCreateRequestType = new OrderCreateRequestType(
                hotelBookInput: new HotelBookInput(roomQuantity: 1),
                cityInput: new CityInput(),
                teamRoomInfo: new TeamRoomInfo(teamRoom: "T"),
                hotelBookPassengerInputs: Arrays.asList(
                        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(infoId: "123", roomIndex: 1, employee: "F"), name: "name",
                                passengerBasicInfo: new PassengerBasicInfo(preferLastName: "zhang", preferFirstName: "san")),
                        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "789", roomIndex: 1, employee: "T"), name: "name",
                                passengerBasicInfo: new PassengerBasicInfo(preferLastName: "li", preferFirstName: "si")))
                )
        accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("HotelBookPolicy", "P")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(maxGuestCountPerRoom: 2, balanceType: "PP"))
        checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo()
        result = myTestClass.checkClientPsg(orderCreateRequestType, accountInfo, checkAvailInfo, null, null)
        then:
        result == null
    }

    def "test checkGuestPerson method"() {
        given:
        when:
        def orderCreateRequestType = new OrderCreateRequestType(
                hotelBookInput: new HotelBookInput(roomQuantity: 1),
                cityInput: new CityInput(),
                teamRoomInfo: new TeamRoomInfo(teamRoom: "T"),
                hotelBookPassengerInputs: Arrays.asList(
                        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(infoId: "123", roomIndex: 1, employee: "F"), name: "name",
                                passengerBasicInfo: new PassengerBasicInfo(preferLastName: "zhang", preferFirstName: "san")),
                        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "789", roomIndex: 2, employee: "T"), name: "name",
                                passengerBasicInfo: new PassengerBasicInfo(preferLastName: "li", preferFirstName: "si")))
        )
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>() {
                    {
                        put("HotelBookPolicy", "P")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        def queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(maxGuestCountPerRoom: 2, balanceType: "PP"))
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo()
        def result = myTestClass.checkGuestPerson(orderCreateRequestType, checkAvailInfo)
        then:
        def ex = thrown(BusinessException)
        ex.errorCode == 469


        when:
        orderCreateRequestType.hotelBookInput.roomQuantity = 2
        result = myTestClass.checkGuestPerson(orderCreateRequestType, checkAvailInfo)
        then:
        result == null
    }

    def "test checkClientPsgSame method"() {
        given:
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                return false
            }
        }
        def orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "corpid")),
                cityInput: new CityInput(),
                teamRoomInfo: new TeamRoomInfo(teamRoom: "F"),
                hotelBookPassengerInputs: Arrays.asList(
                        new HotelBookPassengerInput(hotelPassengerInput:  new HotelPassengerInput(uid: "uid",
                                employee: "T"), name: "",
                                passengerBasicInfo:
                                        new PassengerBasicInfo(preferLastName: "", preferFirstName: "")),
                        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "", infoId: "uid",
                                employee: "F"), name: "",
                                passengerBasicInfo:
                                        new PassengerBasicInfo(preferLastName: "", preferFirstName: "")))
        )
        def resourceToken = new ResourceToken()
        def queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(gdsType: "", balanceType: "PP"))

        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo()
        when:
        def result = myTestClass.checkClientPsgSame(orderCreateRequestType, checkAvailInfo, null, null)
        then:
        result.errorCode == 611


        when:
        orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "corpid")),
                cityInput: new CityInput(),
                teamRoomInfo: new TeamRoomInfo(teamRoom: "F"),
                hotelBookPassengerInputs: Arrays.asList(
                        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "uid",
                                employee: "T"), name: "name",
                                passengerBasicInfo:
                                        new PassengerBasicInfo(preferLastName: "", preferFirstName: "")),
                        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "uid2",
                                employee: "T"), name: "name",
                                passengerBasicInfo:
                                        new PassengerBasicInfo(preferLastName: "", preferFirstName: "")))
        )
        result = myTestClass.checkClientPsgSame(orderCreateRequestType, checkAvailInfo, null, null)
        then:
        result.errorCode == 638

        when:
        orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "corpid")),
                cityInput: new CityInput(),
                teamRoomInfo: new TeamRoomInfo(teamRoom: "F"),
                hotelBookPassengerInputs: Arrays.asList(
                        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "uid", infoId: "",
                                employee: "T"), name: "name",
                                passengerBasicInfo:
                                        new PassengerBasicInfo(preferLastName: "", preferFirstName: "")),
                        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "", infoId: "1234",
                                employee: "F"), name: "name",
                                passengerBasicInfo:
                                        new PassengerBasicInfo(preferLastName: "", preferFirstName: "")))
        )
        result = myTestClass.checkClientPsgSame(orderCreateRequestType, checkAvailInfo, null, null)
        then:
        result.errorCode == 639

        when:
        orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "corpid")),
                cityInput: new CityInput(),
                teamRoomInfo: new TeamRoomInfo(teamRoom: "F"),
                hotelBookPassengerInputs: Arrays.asList(
                        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "", infoId: "123",
                                employee: "F"), name: "",
                                passengerBasicInfo:
                                        new PassengerBasicInfo(preferLastName: "zhang", preferFirstName: "san")),
                        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput( uid: "", infoId: "1234",
                                employee: "F"), name: "",
                                passengerBasicInfo:
                                        new PassengerBasicInfo(preferLastName: "zhang", preferFirstName: "san")))
        )
        queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(gdsType: "Amadues", balanceType: "PP"))
        checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo()
        result = myTestClass.checkClientPsgSame(orderCreateRequestType, checkAvailInfo, null, null)
        then:
        result.errorCode == 640

        when:
        orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "corpid")),
                cityInput: new CityInput(),
                teamRoomInfo: new TeamRoomInfo(teamRoom: "F"),
                hotelBookPassengerInputs: Arrays.asList(
                        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput( uid: "", infoId: "123",
                                employee: "F"), name: "",
                                passengerBasicInfo:
                                        new PassengerBasicInfo(preferLastName: "zhang", preferFirstName: "san")),
                        new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput( uid: "", infoId: "1234",
                                employee: "F"), name: "",
                                passengerBasicInfo:
                                        new PassengerBasicInfo(preferLastName: "zhang", preferFirstName: "san si")))
        )
        queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(gdsType: "Amadues", balanceType: "PP"))
        checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo()
        result = myTestClass.checkClientPsgSame(orderCreateRequestType, checkAvailInfo, null, null)
        then:
        result == null
    }

    def "test checkThirdPartyAgreementPsg method"() {
        given:
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                if (corpId.equals("testCorpId")) {
                    return true
                }
                return false
            }
        }

        def queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(roomType: "C", tmcPriceType: "NONE"))
        def orderCreateRequestType =
                new OrderCreateRequestType(
                        "corpPayInfo":
                                new CorpPayInfo("corpPayType": "private"),
                        "integrationSoaRequestType":
                                new IntegrationSoaRequestType(
                                        "userInfo":
                                                new UserInfo("corpId": "testCorpId", "userId": "self")
                                ),
                        "hotelBookPassengerInputs":
                                new ArrayList<HotelBookPassengerInput>(
                                        Arrays.asList(
                                                new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput( "uid": "not self")),
                                                new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput("uid": "not self1")),
                                                new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput("uid": "not self2"))
                                        )
                                )
                )
        when:
        def result = myTestClass.checkThirdPartyAgreementPsg(orderCreateRequestType, queryCheckAvailContextResponseType)

        then:
        result.errorCode == 644


        when:
        orderCreateRequestType.getCorpPayInfo().setCorpPayType("public")
        result = myTestClass.checkThirdPartyAgreementPsg(orderCreateRequestType, queryCheckAvailContextResponseType)

        then:
        result == null


        when:
        orderCreateRequestType.getCorpPayInfo().setCorpPayType("private")
        orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().setCorpId("notTestCorpId")
        result = myTestClass.checkThirdPartyAgreementPsg(orderCreateRequestType, queryCheckAvailContextResponseType)

        then:
        result == null


        when:
        orderCreateRequestType.getIntegrationSoaRequestType().getUserInfo().setCorpId("testCorpId")
        orderCreateRequestType.hotelBookPassengerInputs.get(0).hotelPassengerInput.uid = "self"
        result = myTestClass.checkThirdPartyAgreementPsg(orderCreateRequestType, queryCheckAvailContextResponseType)

        then:
        result == null
    }
}
