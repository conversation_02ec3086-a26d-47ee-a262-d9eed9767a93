package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.OrderResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.hotel.book.common.util.StrategyOfBookingInitUtil
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2024/12/9 15:52
 *
 */
class MapperOfHotelOrderRepeatOrderRequestTypeTest extends Specification {

    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }
    @Unroll
    def "testBuildOriginalOrderId with different scenarios"() {
        given:
        MapperOfHotelOrderRepeatOrderRequestType mapper = new MapperOfHotelOrderRepeatOrderRequestType()
        ResourceToken resourceToken = new ResourceToken(orderResourceToken: new OrderResourceToken(orderId: orderId))
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(strategyInfos: strategyInfos)

        when: "calling buildOriginalOrderId with specific parameters"
        def result = mapper.buildOriginalOrderId(resourceToken, orderCreateRequestType)

        then: "the result should be as expected"
        result == expectedResult

        where:
        orderId | strategyInfos                                                                      || expectedResult
        123L    | [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "MODIFY")]       || 123L
        456L    | [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "EXTEND")]       || 456L
        789L    | [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "APPLY_MODIFY")] || 789L
        789L    | [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "COPY_ORDER")]   || 789L
        0L      | [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "COPY_ORDER")]   || 0L
        123L    | []                                                                                 || 0L
        null    | [new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "COPY_ORDER")]   || 0L
    }
}
