package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response

import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple7
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType
import com.ctrip.ibu.platform.shark.sdk.api.L10n
import com.ctrip.ibu.platform.shark.sdk.exception.InitializationException
import com.ctrip.ibu.platform.shark.sdk.service.SharkInitializer
import com.ctrip.soa._21210.HotelClientInfoType
import com.ctrip.soa._21210.HotelOrderRepeatOrderResponseType
import com.ctrip.soa._21210.RepeatOrderInfoType
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification

class MapperOfRepeatOrderInfoResponseTest extends Specification {

    def tester = new MapperOfRepeatOrderInfoResponse()

    def savePoint = new SavePoint()

    void setup() {
        new MockUp<BFFSharkUtil> () {
            @Mock
            public static String getSharkValue(String key) {
                return key
            }
        }
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "buildRemindInfos" () {
        /*when:
        QConfigOfCustomConfig_isSupport(true)
        then:
        def res1 = tester.buildRemindInfos(null, null, null, null)
        res1*.remindType == ["FORBID_BOOKING", "ALLOW_BOOKING"]
        res1*.remindContent == ["ctrip.com.hotel.booking.biz.repeat.booking.forbid.text.bypaytype.", "ctrip.com.hotel.booking.biz.repeat.booking.continue.text.bypaytype"]
        when:啊
        QConfigOfCustomConfig_isSupport(false)
        then:
        def res2 = tester.buildRemindInfos(null, null, null, null)
        res2*.remindType == ["FORBID_BOOKING", "ALLOW_BOOKING"]
        res2*.remindContent == ["ctrip.com.hotel.booking.biz.repeat.booking.forbid.text", "ctrip.com.hotel.booking.biz.repeat.booking.continue.text"]*/


    }

    def "buildOrderElements" () {
        given:
        new MockUp<SharkInitializer>() {
            @Mock
            public static synchronized void init() throws InitializationException {
            }
        }
        new MockUp<L10n>() {
            @Mock
            L10n.DateTimeFormatter dateTimeFormatter(String locale) {
                return new L10n.DateTimeFormatter(null);
            }
        }
        new MockUp<L10n.DateTimeFormatter>() {
            @Mock
            String ymdShortString(Date date) {
                return "2022年4月21日"
            }
        }
        expect:
        tester.buildOrderElements(null, null) == null
        def res = tester.buildOrderElements(new RepeatOrderInfoType(cityName: "cityName", clientList: [new HotelClientInfoType(name: "name")], checkInTime: "2024-01-02", checkOutTime: "2024-01-03"), null)
        res*.orderElementType == ["CITYNAME", "GUESTNAMES", "CHECKDATE"]
        res*.orderElementValue == ["cityName", "name", "2022年4月21日-2022年4月21日"]
    }

    def "buildRepeatOrderRule" () {
        given:
        WrapperOfAccount.AccountInfo accountInfoF = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>(){
                    {
                        put("RepeatOrderControlCorp", "F")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        WrapperOfAccount.AccountInfo accountInfoT = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: new HashMap<String, String>(){
                    {
                        put("RepeatOrderControlCorp", "T")
                    }
                }))
                .policyAccountInfo(new GeneralSearchAccountInfoResponseType())
                .corpUserInfo(new GetCorpUserInfoResponseType())
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build();
        when:
        QConfigOfCustomConfig_isSupport(false)
        AccountInfo_getRepeatOrderControlCorp("N")
        then:
        "ALLOW_BOOKING" == tester.buildRepeatOrderRule(null, null, new CorpPayInfo(corpPayType:"private"), null, null)
        "ALLOW_BOOKING" == tester.buildRepeatOrderRule(null, null, new CorpPayInfo(corpPayType:"public"), null, null)
        "ALLOW_BOOKING" == tester.buildRepeatOrderRule(null, [], new CorpPayInfo(corpPayType:"public"), null, null)
        "ALLOW_BOOKING" == tester.buildRepeatOrderRule(null, [new RepeatOrderInfoType(corpPayType: "OWN")], new CorpPayInfo(corpPayType:"public"), null, null)
        "ALLOW_BOOKING" == tester.buildRepeatOrderRule(null, [new RepeatOrderInfoType(corpPayType: "PUB")], new CorpPayInfo(corpPayType:"public"), null, null)
        "FORBID_BOOKING" == tester.buildRepeatOrderRule(accountInfoF, [new RepeatOrderInfoType(corpPayType: "PUB")], new CorpPayInfo(corpPayType:"public"), null, null)
        "FORBID_BOOKING" == tester.buildRepeatOrderRule(accountInfoF, [new RepeatOrderInfoType(corpPayType: "PUB", repeatTag: 0)], new CorpPayInfo(corpPayType:"public"), null, null)
        "ALLOW_BOOKING" == tester.buildRepeatOrderRule(accountInfoT, [new RepeatOrderInfoType(corpPayType: "PUB", repeatTag: 1)], new CorpPayInfo(corpPayType:"public"), null, null)
        when:
        savePoint.rollback()
        QConfigOfCustomConfig_isSupport(false)
        AccountInfo_getRepeatOrderControlCorp("F")
        then:
        "FORBID_BOOKING" == tester.buildRepeatOrderRule(accountInfoF, [new RepeatOrderInfoType(corpPayType: "PUB")], new CorpPayInfo(corpPayType:"public"), null, null)
        when:
        savePoint.rollback()
        QConfigOfCustomConfig_isSupport(true)
        AccountInfo_getRepeatOrderControlCorp("N")
        then:
        "FORBID_BOOKING" == tester.buildRepeatOrderRule(accountInfoF, [new RepeatOrderInfoType(corpPayType: "PUB", repeatTag: 0)], new CorpPayInfo(corpPayType:"public"), [new HotelPayTypeInput(payCode:"ROOM", payType: "CORP_PAY")], null)
        "ALLOW_BOOKING" == tester.buildRepeatOrderRule(accountInfoT, [new RepeatOrderInfoType(corpPayType: "PUB", repeatTag: 0)], new CorpPayInfo(corpPayType:"public"), [new HotelPayTypeInput(payCode:"ROOM", payType: "SELF_PAY")], null)

    }

    def "check" () {
        expect:
        tester.check(null) == null
        tester.check(Tuple7.of(null,null,null,null,null,null,null)) == null
        tester.check(Tuple7.of(new HotelOrderRepeatOrderResponseType(),null,null,null,null,null,null)) == null
        tester.check(Tuple7.of(new HotelOrderRepeatOrderResponseType(responseCode: 20000),null,null,null,null,null,null)) == null
        tester.check(Tuple7.of(new HotelOrderRepeatOrderResponseType(responseCode: 20000,repeatOrderList: []),null,null,null,null,null,null)) == null
        tester.check(Tuple7.of(new HotelOrderRepeatOrderResponseType(responseCode: 20000,repeatOrderList: []),null,null,null,null,null,null)) == null
    }


    def AccountInfo_getRepeatOrderControlCorp(res) {
        new MockUp<WrapperOfAccount.AccountInfo> () {
            @Mock
            String getRepeatOrderControlCorp() {
                return res
            }
        }
    }

    def "buildOrderStatus" () {
        given:
        QConfigOfCustomConfig_isSupport(true)
        expect:
        tester.buildOrderStatus(null, null,"a", null,new CorpPayInfo(corpPayType: "private"),  null) == "a"
        tester.buildOrderStatus(null, null,"a",null, new CorpPayInfo(corpPayType: "public"), true) == "a"
        tester.buildOrderStatus(null, Mock(WrapperOfAccount.AccountInfo),"a",null, new CorpPayInfo(corpPayType: "public"), false) == "a"
        tester.buildOrderStatus(null, Mock(WrapperOfAccount.AccountInfo),"a",null, new CorpPayInfo(corpPayType: "public"), false) == "a"
        tester.buildOrderStatus(null, Mock(WrapperOfAccount.AccountInfo),"a",1, new CorpPayInfo(corpPayType: "public"), false) == "a"
    }

    def QConfigOfCustomConfig_isSupport(is) {
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            boolean isSupport(String key, String corpId) {
                return is
            }
        }
    }

    def "check0" () {
        given:
        def t = Spy(MapperOfRepeatOrderInfoResponse)
        t.buildRepeatOrderRule(_, _, _, _, _)  >> ""
        def input = Mock(WrapperOfAccount.AccountInfo)
        input.getRepeatBookingReason() >> "T"
        expect:
        t.check(null) == null
        t.check(Tuple7.of(null, null, null, null, null, null, null)) == null
        t.check(Tuple7.of(new HotelOrderRepeatOrderResponseType(), null, null, null, null, null, null)) == null
        t.check(Tuple7.of(new HotelOrderRepeatOrderResponseType(responseCode: 1), null, null, null, null, null, null)) == null
        t.check(Tuple7.of(new HotelOrderRepeatOrderResponseType(responseCode: 20000), null, null, null, null, null, null)) == null
        t.check(Tuple7.of(new HotelOrderRepeatOrderResponseType(responseCode: 20000, repeatOrderList: []), null, null, null, null, null, null)) == null
        t.check(Tuple7.of(new HotelOrderRepeatOrderResponseType(responseCode: 20000, repeatOrderList: [null]), null, null, null, null, null, null)) == null
        t.check(Tuple7.of(new HotelOrderRepeatOrderResponseType(responseCode: 20000, repeatOrderList: [null]), null, input, null, null, null, null)).result == false
    }

    def "buildRepeatOrderInfo" () {
        given:
        def t = Spy(MapperOfRepeatOrderInfoResponse)
        1 * t.buildRepeatOrderRule(_, _, _, _, _) >> "ALLOW_BOOKING"
        1 * t.buildRepeatOrderDetails(_, _, _, _) >> null
        1 * t.buildRemindInfos(_, _, _, _) >> []
        expect:
        t.buildRepeatOrderInfo(null, null, null, null, null, null).repeatOrderRule =="ALLOW_BOOKING"
    }

    def "buildRepeatOrderDetails" () {
        expect:
        tester.buildRepeatOrderDetails(null, null, null, null) == null
        tester.buildRepeatOrderDetails([], null, null, null) == null
        tester.buildRepeatOrderDetails([null], null, null, null) == []
        tester.buildRepeatOrderDetails([new RepeatOrderInfoType(orderId: 2)], null, null, new CorpPayInfo(corpPayType: "private")) *.orderId == ["2"]
    }


}
