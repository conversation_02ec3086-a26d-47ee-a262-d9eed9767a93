package com.ctrip.corp.bff.hotel.book.common.util

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelGeoInfoResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2024/12/17 18:55
 *
 */
class TrackingUtilTest extends Specification {
    def "testBuildBaseTrackingMap"() {
        given:
        HotelBookInput hotelBookInput = new HotelBookInput()
        HotelDateRangeInfo hotelDateRangeInfo = new HotelDateRangeInfo()
        hotelDateRangeInfo.setCheckIn("2024-12-18")
        hotelDateRangeInfo.setCheckOut("2024-12-20")
        hotelBookInput.setHotelDateRangeInfo(hotelDateRangeInfo)

        when:
        Map<String, String> trackingMap = TrackingUtil.buildBaseTrackingMap(hotelBookInput)

        then:
        trackingMap.get("checkIn") == "2024-12-18"
        trackingMap.get("checkOut") == "2024-12-20"
    }
}
