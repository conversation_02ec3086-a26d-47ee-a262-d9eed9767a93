package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.agg.hotel.roomavailable.entity.*
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.RoomResourceToken
import com.ctrip.corp.bff.framework.template.entity.SourceFrom
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.model.CalculateServiceChargeV2ResponseType
import mockit.internal.state.SavePoint
import spock.lang.Specification

class MapperOfGetCancelPolicyDescRequestTest extends Specification {
    def mapper = new MapperOfGetCancelPolicyDescRequest()

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }


    def "test getRoomInfoList"() {
        given:
        CheckAvailResponseType checkAvailResponseType = new CheckAvailResponseType(
                reservationToken: "reservationToken",
                hotelRatePlan: new HotelRatePlan(
                        roomInfo: new RoomItem(balanceType: "PP"),
                        bookingRules: new BookingRulesType(
                                cancelPolicyInfo: new CancelPolicyType(
                                        lastCancelTimeUTC: "2024-08-30T12:00:00Z",
                                        freeCancelPolicySceneType: "sceneType",
                                        guaranteePolicyInfo: new GuaranteeDetailType(
                                                guaranteeType: "FULL_PREPAY",
                                                cancelDeductDetailInfo: [
                                                        new CancelDeductPolicyType(
                                                                deductionStartTimeUTC: "2024-08-29T12:00:00Z",
                                                                deductionEndTimeUTC: "2024-08-30T12:00:00Z",
                                                                deductionType: "type",
                                                                deductionRatio: 0.5,
                                                                customDeductionPrice: new PriceType(price: 100, currency: "USD"),
                                                                originDeductionPrice: new PriceType(price: 150, currency: "USD")
                                                        )
                                                ],
                                                guaranteePriceInfo: new GuaranteePriceType(
                                                        originGuaranteePrice: new PriceType(price: 200, currency: "USD"),
                                                        customGuaranteePrice: new PriceType(price: 250, currency: "USD")
                                                )
                                        ),
                                        depositPolicyList: [
                                                new DepositPolicyType(
                                                        depositTimeUTC: "2024-08-28T12:00:00Z",
                                                        customDepositPrice: new PriceType(price: 300, currency: "USD"),
                                                        originDepositPrice: new PriceType(price: 350, currency: "USD")
                                                )
                                        ]
                                )
                        )
                )
        )
        WrapperOfCheckAvail.CheckAvailInfo checkAvailInfo = WrapperOfCheckAvail.checkAvailBuilder()
                .setCheckAvailResponseType(checkAvailResponseType)
                .setResourceToken(new ResourceToken(roomResourceToken: new RoomResourceToken())).build().getCheckAvailInfo();
        when:
        def result = mapper.getRoomInfoList(checkAvailInfo, new CalculateServiceChargeV2ResponseType(chargeRequired: true), true, null,null)

        then:
        result != null
        result.size() == 1
        result[0].getSequenceId() == 0
        result[0].getReservationToken() == "reservationToken"
        result[0].isHasServiceCharge() == false
        result[0].getCancelPolicyInfo().getLastCancelTimeUTC() == "2024-08-30T12:00:00Z"
        result[0].getCancelPolicyInfo().getFreeCancelPolicySceneType() == "sceneType"
        result[0].getCancelPolicyInfo().getGuaranteePolicyInfo().getGuaranteeType() == "FULL"
        result[0].getCancelPolicyInfo().getGuaranteePolicyInfo().getGuaranteePriceInfo().getOriginGuaranteePrice().getPrice() == 200
        result[0].getCancelPolicyInfo().getGuaranteePolicyInfo().getGuaranteePriceInfo().getOriginGuaranteePrice().getCurrency() == "USD"
        result[0].getCancelPolicyInfo().getGuaranteePolicyInfo().getGuaranteePriceInfo().getCustomGuaranteePrice().getPrice() == 250
        result[0].getCancelPolicyInfo().getGuaranteePolicyInfo().getGuaranteePriceInfo().getCustomGuaranteePrice().getCurrency() == "USD"
        result[0].getCancelPolicyInfo().getGuaranteePolicyInfo().getCancelDeductDetailList().size() == 1
        result[0].getCancelPolicyInfo().getGuaranteePolicyInfo().getCancelDeductDetailList()[0].getDeductionStartTimeUTC() == "2024-08-29T12:00:00Z"
        result[0].getCancelPolicyInfo().getGuaranteePolicyInfo().getCancelDeductDetailList()[0].getDeductionEndTimeUTC() == "2024-08-30T12:00:00Z"
        result[0].getCancelPolicyInfo().getGuaranteePolicyInfo().getCancelDeductDetailList()[0].getDeductionType() == "type"
        result[0].getCancelPolicyInfo().getGuaranteePolicyInfo().getCancelDeductDetailList()[0].getDeductionRatio() == 0.5
        result[0].getCancelPolicyInfo().getGuaranteePolicyInfo().getCancelDeductDetailList()[0].getCustomDeductionPrice().getPrice() == 100
        result[0].getCancelPolicyInfo().getGuaranteePolicyInfo().getCancelDeductDetailList()[0].getCustomDeductionPrice().getCurrency() == "USD"
        result[0].getCancelPolicyInfo().getGuaranteePolicyInfo().getCancelDeductDetailList()[0].getOriginDeductionPrice().getPrice() == 150
        result[0].getCancelPolicyInfo().getGuaranteePolicyInfo().getCancelDeductDetailList()[0].getOriginDeductionPrice().getCurrency() == "USD"
        result[0].getCancelPolicyInfo().getDepositPolicyList().size() == 1
        result[0].getCancelPolicyInfo().getDepositPolicyList()[0].getDepositTimeUTC() == "2024-08-28T12:00:00Z"
        result[0].getCancelPolicyInfo().getDepositPolicyList()[0].getCustomDepositPrice().getPrice() == 300
        result[0].getCancelPolicyInfo().getDepositPolicyList()[0].getCustomDepositPrice().getCurrency() == "USD"
        result[0].getCancelPolicyInfo().getDepositPolicyList()[0].getOriginDepositPrice().getPrice() == 350
        result[0].getCancelPolicyInfo().getDepositPolicyList()[0].getOriginDepositPrice().getCurrency() == "USD"
    }

    def "test buildCancelPolicyType"() {
        given:
        CancelPolicyType cancelPolicyType = new CancelPolicyType(
                lastCancelTimeUTC: "2024-08-30T12:00:00Z",
                freeCancelPolicySceneType: "sceneType",
                guaranteePolicyInfo: new GuaranteeDetailType(
                        guaranteeType: "FULL_PREPAY",
                        cancelDeductDetailInfo: [
                                new CancelDeductPolicyType(
                                        deductionStartTimeUTC: "2024-08-29T12:00:00Z",
                                        deductionEndTimeUTC: "2024-08-30T12:00:00Z",
                                        deductionType: "type",
                                        deductionRatio: 0.5,
                                        customDeductionPrice: new PriceType(price: 100, currency: "USD"),
                                        originDeductionPrice: new PriceType(price: 150, currency: "USD")
                                )
                        ],
                        guaranteePriceInfo: new GuaranteePriceType(
                                originGuaranteePrice: new PriceType(price: 200, currency: "USD"),
                                customGuaranteePrice: new PriceType(price: 250, currency: "USD")
                        )
                ),
                depositPolicyList: [
                        new DepositPolicyType(
                                depositTimeUTC: "2024-08-28T12:00:00Z",
                                customDepositPrice: new PriceType(price: 300, currency: "USD"),
                                originDepositPrice: new PriceType(price: 350, currency: "USD")
                        )
                ]
        )

        when:
        def result = mapper.buildCancelPolicyType(cancelPolicyType, true)

        then:
        result != null
        result.getLastCancelTimeUTC() == "2024-08-30T12:00:00Z"
        result.getFreeCancelPolicySceneType() == "sceneType"
        result.getGuaranteePolicyInfo().getGuaranteeType() == "FULL"
        result.getGuaranteePolicyInfo().getGuaranteePriceInfo().getOriginGuaranteePrice().getPrice() == 200
        result.getGuaranteePolicyInfo().getGuaranteePriceInfo().getOriginGuaranteePrice().getCurrency() == "USD"
        result.getGuaranteePolicyInfo().getGuaranteePriceInfo().getCustomGuaranteePrice().getPrice() == 250
        result.getGuaranteePolicyInfo().getGuaranteePriceInfo().getCustomGuaranteePrice().getCurrency() == "USD"
        result.getGuaranteePolicyInfo().getCancelDeductDetailList().size() == 1
        result.getGuaranteePolicyInfo().getCancelDeductDetailList()[0].getDeductionStartTimeUTC() == "2024-08-29T12:00:00Z"
        result.getGuaranteePolicyInfo().getCancelDeductDetailList()[0].getDeductionEndTimeUTC() == "2024-08-30T12:00:00Z"
        result.getGuaranteePolicyInfo().getCancelDeductDetailList()[0].getDeductionType() == "type"
        result.getGuaranteePolicyInfo().getCancelDeductDetailList()[0].getDeductionRatio() == 0.5
        result.getGuaranteePolicyInfo().getCancelDeductDetailList()[0].getCustomDeductionPrice().getPrice() == 100
        result.getGuaranteePolicyInfo().getCancelDeductDetailList()[0].getCustomDeductionPrice().getCurrency() == "USD"
        result.getGuaranteePolicyInfo().getCancelDeductDetailList()[0].getOriginDeductionPrice().getPrice() == 150
        result.getGuaranteePolicyInfo().getCancelDeductDetailList()[0].getOriginDeductionPrice().getCurrency() == "USD"
        result.getDepositPolicyList().size() == 1
        result.getDepositPolicyList()[0].getDepositTimeUTC() == "2024-08-28T12:00:00Z"
        result.getDepositPolicyList()[0].getCustomDepositPrice().getPrice() == 300
        result.getDepositPolicyList()[0].getCustomDepositPrice().getCurrency() == "USD"
        result.getDepositPolicyList()[0].getOriginDepositPrice().getPrice() == 350
        result.getDepositPolicyList()[0].getOriginDepositPrice().getCurrency() == "USD"
    }

    def "test buildGuaranteePriceInfo"() {
        given:
        GuaranteePriceType guaranteePriceInfo = new GuaranteePriceType(
                originGuaranteePrice: new PriceType(price: 200, currency: "USD"),
                customGuaranteePrice: new PriceType(price: 250, currency: "USD")
        )

        when:
        com.ctrip.corp.hotelbook.commonws.entity.GuaranteePriceType result = mapper.buildGuaranteePriceInfo(guaranteePriceInfo)

        then:
        result != null
        result.getOriginGuaranteePrice().getPrice() == 200
        result.getOriginGuaranteePrice().getCurrency() == "USD"
        result.getCustomGuaranteePrice().getPrice() == 250
        result.getCustomGuaranteePrice().getCurrency() == "USD"
    }

    def "test buildDepositPolicyList"() {
        given:
        List<DepositPolicyType> depositPolicyList = [
                new DepositPolicyType(
                        depositTimeUTC: "2024-08-28T12:00:00Z",
                        customDepositPrice: new PriceType(price: 300, currency: "USD"),
                        originDepositPrice: new PriceType(price: 350, currency: "USD")
                )
        ]

        when:
        List<com.ctrip.corp.hotelbook.commonws.entity.DepositPolicyType> result = mapper.buildDepositPolicyList(depositPolicyList)

        then:
        result != null
        result.size() == 1
        result[0].getDepositTimeUTC() == "2024-08-28T12:00:00Z"
        result[0].getCustomDepositPrice().getPrice() == 300
        result[0].getCustomDepositPrice().getCurrency() == "USD"
        result[0].getOriginDepositPrice().getPrice() == 350
        result[0].getOriginDepositPrice().getCurrency() == "USD"
    }


    def "test getChannel"() {
        expect:
        mapper.getChannel(sourceFrom) == expectedChannel

        where:
        sourceFrom            | expectedChannel
        null                  | "APP"
        SourceFrom.Online     | "ONLINE"
        SourceFrom.Offline    | "OFFLINE"
        SourceFrom.Native     | "APP"
    }
}