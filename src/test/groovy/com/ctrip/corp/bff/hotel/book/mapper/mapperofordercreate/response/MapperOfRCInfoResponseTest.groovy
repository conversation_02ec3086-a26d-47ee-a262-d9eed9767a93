package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response

import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckAgreementRcInfoType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckItemsResultType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckOverStandardRcInfoType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckRcResultType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckResultType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.EmployeeHotelCheckResultType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.RoomModeInfoType
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.hotel.book.common.enums.RcTypeEnum
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfOrderRcInfo
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.contract.RepeatOrderDetail
import com.ctrip.corp.bff.hotel.book.contract.RepeatOrderInfo
import corp.user.service.corp4jservice.GetReasoncodesResponseType
import corp.user.service.corp4jservice.ReasoncodeInfo
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;

class MapperOfRCInfoResponseTest extends Specification {

    def tester = Spy(new MapperOfRCInfoResponse())

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "check" () {
        given:
        def input = Mock(WrapperOfOrderRcInfo)
        input.getOrderCreateRequestType() >> new OrderCreateRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public"))
        input.getCheckTravelPolicyResponseType()>> new CheckTravelPolicyResponseType(responseCode: 1)
        def input1 = Mock(WrapperOfOrderRcInfo)
        input1.getOrderCreateRequestType() >> new OrderCreateRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public"))
        input1.getCheckTravelPolicyResponseType()>> new CheckTravelPolicyResponseType(responseCode: 20000, checkItemsResult: new CheckItemsResultType(employeeHotelCheckResult: new EmployeeHotelCheckResultType(inControl: false)))
        def input2 = Mock(WrapperOfOrderRcInfo)
        input2.getOrderCreateRequestType() >> new OrderCreateRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public"))
        input2.getCheckTravelPolicyResponseType()>> new CheckTravelPolicyResponseType(responseCode: 20000, checkItemsResult: new CheckItemsResultType(employeeHotelCheckResult: new EmployeeHotelCheckResultType(inControl: true)))
        def input3 = Mock(WrapperOfOrderRcInfo)
        input3.getOrderCreateRequestType() >> new OrderCreateRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public"))
        input3.getCheckTravelPolicyResponseType()>> new CheckTravelPolicyResponseType(checkRcResult: new CheckRcResultType(),responseCode: 20000, checkItemsResult: new CheckItemsResultType(employeeHotelCheckResult: new EmployeeHotelCheckResultType(inControl: true)))
        def input4 = Mock(WrapperOfOrderRcInfo)
        input4.getOrderCreateRequestType() >> new OrderCreateRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "public"))
        input4.getCheckTravelPolicyResponseType()>> new CheckTravelPolicyResponseType(checkRcResult: new CheckRcResultType(checkResultList: [new CheckResultType(checkResult: "FORBID_BOOKING")]),responseCode: 20000, checkItemsResult: new CheckItemsResultType(employeeHotelCheckResult: new EmployeeHotelCheckResultType(inControl: true)))

        expect:
        tester.check(Tuple1.of(Mock(WrapperOfOrderRcInfo))) == null
        !tester.check(Tuple1.of(input)).result
        !tester.check(Tuple1.of(input1)).result
        tester.check(Tuple1.of(input2)) == null
        tester.check(Tuple1.of(input3)) == null
        !tester.check(Tuple1.of(input4)).result

    }

    def "buildRCContentAgreement" () {
        expect:
        new MapperOfRCInfoResponse().buildRCContentAgreement(null, null, null, null) == null
        new MapperOfRCInfoResponse().buildRCContentAgreement(new CheckAgreementRcInfoType(), null, null, null) == null
        new MapperOfRCInfoResponse().buildRCContentAgreement(new CheckAgreementRcInfoType(required: true, selected: true), null, null, null) == null
        new MapperOfRCInfoResponse().buildRCContentAgreement(new CheckAgreementRcInfoType(required: false, selected: true), null, null, null) == null
    }

    def "buildRcContents" () {
        given:
        new MockUp<TokenParseUtil>() {
            @Mock
            public static <T> String generateToken(T pidEntity, Class<T> tClass) {
                return ""
            }
            }
        expect:
        new MapperOfRCInfoResponse().buildRcContents(null, null, null) == null
        new MapperOfRCInfoResponse().buildRcContents([], null, null) == null
        new MapperOfRCInfoResponse().buildRcContents([new ReasoncodeInfo(), new ReasoncodeInfo(reasonInfo: "a")], RcTypeEnum.LOW_PRICE, new IntegrationSoaRequestType()) *.type == ["LOW_PRICE"]
    }

    def "getRcTypeSuffix" () {
        expect:
        new MapperOfRCInfoResponse().getRcTypeSuffix(RcTypeEnum.AGREEMENT, null) == ""
        new MapperOfRCInfoResponse().getRcTypeSuffix(RcTypeEnum.LOW_PRICE, "price") == ""
        new MapperOfRCInfoResponse().getRcTypeSuffix(RcTypeEnum.LOW_PRICE, "STAR") == "_STAR"
        new MapperOfRCInfoResponse().getRcTypeSuffix(RcTypeEnum.LOW_PRICE, "A") == ""
    }

    def "getPriceReasons" () {
        expect:
        new MapperOfRCInfoResponse().getPriceReasons(null, null) == null
        new MapperOfRCInfoResponse().getPriceReasons(new GetReasoncodesResponseType(), null) == null
        new MapperOfRCInfoResponse().getPriceReasons(new GetReasoncodesResponseType(reasonCodes: []), null) == null
        new MapperOfRCInfoResponse().getPriceReasons(new GetReasoncodesResponseType(reasonCodes: [null, new ReasoncodeInfo(rcType: 1)]), RcTypeEnum.LOW_PRICE) *.rcType == [1]
    }

    def "buildConflictRCContent" () {
        given:
        def input = Mock(WrapperOfAccount.AccountInfo)
        input.getRepeatBookingReason() >> "F"
        def input1 = Mock(WrapperOfAccount.AccountInfo)
        input1.getRepeatBookingReason() >> "T"
        expect:
        new MapperOfRCInfoResponse().buildConflictRCContent(null, null, input, null) == null
        new MapperOfRCInfoResponse().buildConflictRCContent(null, null, null, null) == null
        new MapperOfRCInfoResponse().buildConflictRCContent(null, new RepeatOrderInfo(), input1, null) == null
        new MapperOfRCInfoResponse().buildConflictRCContent(null, new RepeatOrderInfo(repeatOrderDetails: []), input1, null) == null
        new MapperOfRCInfoResponse().buildConflictRCContent(null, new RepeatOrderInfo(repeatOrderRule: "FORBID_BOOKING",repeatOrderDetails: [new RepeatOrderDetail()]), input1, null) == null
        new MapperOfRCInfoResponse().buildConflictRCContent(new GetReasoncodesResponseType(reasonCodes: [null, new ReasoncodeInfo(rcType: 128)]), new RepeatOrderInfo(repeatOrderDetails: [new RepeatOrderDetail()]), input1, null).rcType == "CONFLICT_BOOK"

    }

    def "isShowRoomTitle" () {
        expect:
        !new MapperOfRCInfoResponse().isShowRoomTitle(new CheckOverStandardRcInfoType())
        !new MapperOfRCInfoResponse().isShowRoomTitle(new CheckOverStandardRcInfoType(roomModeInfoList: []))
        !new MapperOfRCInfoResponse().isShowRoomTitle(new CheckOverStandardRcInfoType(roomModeInfoList: [new RoomModeInfoType()]))
    }

    def "convert" () {
        given:
        new MockUp<TokenParseUtil>() {
            @Mock
            public static <T> String generateToken(T pidEntity, Class<T> tClass) {
                return ""
            }
            }
        def input = Mock(WrapperOfOrderRcInfo)
        input.getOrderCreateToken() >> new OrderCreateToken(continueTypes: [ContinueTypeConst.RC_CONTROL])
        def input1 = Mock(WrapperOfOrderRcInfo)
        input1.getOrderCreateToken() >> new OrderCreateToken(continueTypes: [ContinueTypeConst.CONFLICT_ORDER])
        def input2 = Mock(WrapperOfOrderRcInfo)
        input2.getOrderCreateToken() >> new OrderCreateToken(continueTypes: [])
        input2.getRepeatOrderInfo() >> new RepeatOrderInfo(repeatOrderDetails: [new RepeatOrderDetail(orderId: 2)])
        input2.getOrderCreateRequestType() >> new OrderCreateRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "private"),integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo()))
        def input3 = Mock(WrapperOfOrderRcInfo)
        input3.getOrderCreateToken() >> new OrderCreateToken(continueTypes: [])
        input3.getRepeatOrderInfo() >> new RepeatOrderInfo(repeatOrderDetails: [])
        input3.getOrderCreateRequestType() >> new OrderCreateRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "private"),integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo()))

        expect:
        tester.convert(Tuple1.of(input)).t2 == null
        tester.convert(Tuple1.of(input1)).t2 == null
        tester.convert(Tuple1.of(input2)).t2.orderCreateToken == ""
        tester.convert(Tuple1.of(input3)).t2 == null
    }
}
