package com.ctrip.corp.bff.hotel.book.common.util

import com.ctrip.corp.bff.framework.hotel.common.enums.TrackingEnum
import com.ctrip.corp.bff.framework.hotel.entity.contract.ApprovalOutput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPositionInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInfo
import com.ctrip.corp.bff.hotel.book.contract.BookingHotelInfo
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import com.ctrip.corp.bff.hotel.book.contract.BookingInitResponseType
import com.ctrip.corp.bff.hotel.book.contract.RoomProperty
import mockit.internal.state.SavePoint
import spock.lang.Specification

class BookingInitTraceUtilTest extends Specification {

    def savePoint = new SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }
    def "testTracking"() {
        given:
        BookingInitRequestType requestType = new BookingInitRequestType()
        HotelBookInput hotelBookInput = new HotelBookInput()
        HotelDateRangeInfo hotelDateRangeInfo = new HotelDateRangeInfo()
        hotelDateRangeInfo.setCheckIn("2024-12-18")
        hotelDateRangeInfo.setCheckOut("2024-12-20")
        hotelBookInput.setHotelDateRangeInfo(hotelDateRangeInfo)
        requestType.setHotelBookInput(hotelBookInput)
        BookingInitResponseType responseType = new BookingInitResponseType(
                bookingHotelInfo: new BookingHotelInfo(
                        hotelId: "890890",
                        hotelPositionInfo: new HotelPositionInfo(cityInfo: new CityInfo(cityId: 1, cityArea: "HK"))),
                approvalOutput: new ApprovalOutput(defaultApprovalSubNo: "123", defaultEmergencyBook: "F"))

        when:
        Map<String, String> trackingMap = BookingInitTraceUtil.tracking(requestType, responseType)

        then:
        trackingMap != null
        trackingMap.get("cityRegion") == "HK"
        trackingMap.get("checkIn") == "2024-12-18"
        trackingMap.get("cityId") == "1"
        trackingMap.get("checkOut") == "2024-12-20"
        trackingMap.get("hotelId") == "890890"
        trackingMap.get(TrackingEnum.BOOK_MODE.getCode()) == "APPROVAL"
    }

    def "test trackingForRoomProperties when roomProperties is empty"() {
        given:
        List<RoomProperty> roomProperties = []

        when:
        Map<String, String> result = BookingInitTraceUtil.trackingForRoomProperties(roomProperties)

        then:
        result == null
    }

    def "test trackingForRoomProperties when roomProperties contains elements with same key"() {
        given:
        List<RoomProperty> roomProperties = [
                new RoomProperty(propertyType: "CERTIFICATE_ROOM", propertyValue: "T"),
                new RoomProperty(propertyType: "CERTIFICATE_ROOM", propertyValue: "F")
        ]

        when:
        Map<String, String> result = BookingInitTraceUtil.trackingForRoomProperties(roomProperties)

        then:
        result.size() == 1
        result["CERTIFICATE_ROOM"] == "T|F"
    }

    def "test trackingForRoomProperties when roomProperties contains elements with different keys"() {
        given:
        List<RoomProperty> roomProperties = [
                new RoomProperty(propertyType: "CERTIFICATE_ROOM", propertyValue: "T"),
                new RoomProperty(propertyType: "OTHER_ROOM", propertyValue: "F")
        ]

        when:
        Map<String, String> result = BookingInitTraceUtil.trackingForRoomProperties(roomProperties)

        then:
        result.size() == 2
        result["CERTIFICATE_ROOM"] == "T"
        result["OTHER_ROOM"] == "F"
    }

    def "test mapCertificateRoomProperties when roomProperties is empty"() {
        given:
        List<RoomProperty> roomProperties = []

        when:
        Map<String, List<RoomProperty>> result = BookingInitTraceUtil.mapCertificateRoomProperties(roomProperties)

        then:
        result == null
    }

    def "test roomPropertyToString when roomProperties is empty"() {
        given:
        List<RoomProperty> roomProperties = []

        when:
        String result = BookingInitTraceUtil.roomPropertyToString(roomProperties)

        then:
        result == ""
    }
}
