package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import mockit.internal.state.SavePoint
import spock.lang.Specification

class MapperOfCreateOrderCheckRequestTypeTest extends Specification {

    def tester = Spy(new MapperOfCreateOrderCheckRequestType())

    def savePoint = new SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        given:
        expect:
        tester.convert(Tuple1.of(new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a"))))).basicInfo.uid == "a"
    }
}
