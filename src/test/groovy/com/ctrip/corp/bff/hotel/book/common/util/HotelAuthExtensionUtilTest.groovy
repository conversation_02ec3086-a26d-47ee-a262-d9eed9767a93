package com.ctrip.corp.bff.hotel.book.common.util

import com.ctrip.corp.agg.hotel.roomavailable.entity.AmountDetailEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookHotelInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.BookRoomInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.PriceType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryBonusPointInfoType
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType
import com.ctrip.corp.agg.hotel.roomavailable.entity.RoomDiscountInfo
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckItemsResultType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckOverStandardRcInfoType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckRcResultType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckResultType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.PriceCheckResultType
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextResponseType
import com.ctrip.corp.agg.hotel.tmc.entity.FinalPolicyType
import com.ctrip.corp.agg.hotel.tmc.entity.FinalTravelPolicyType
import com.ctrip.corp.bff.framework.hotel.common.util.WaitFutureUtil
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ReservationResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PassengerBasicInfo
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.corpsz.configuration.common.contract.GetSubAccountConfigResponseType
import com.ctrip.soa._20184.ClientInfoType
import com.ctrip.soa._20184.HotelProductInfoType
import corp.user.service.CorpAccountQueryService.GeneralSearchAccountInfoResponseType
import corp.user.service.corpUserInfoService.GetCorpUserInfoResponseType
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2024/8/20 17:33
 *
 */
class HotelAuthExtensionUtilTest extends Specification {

    @Unroll
    def "getOrderMode should return correct order mode"() {
        given:
        OrderCreateRequestType request = new OrderCreateRequestType()
        request.setCorpPayInfo(new CorpPayInfo("public"))
        GetCorpUserInfoResponseType corpUserInfo = new GetCorpUserInfoResponseType()
        def map = new HashMap<String, String>()
        map.put("BillType", billType)
        WrapperOfAccount.AccountInfo accountInfo = WrapperOfAccount.builder()
                .accountInfo(new GeneralSearchAccountInfoResponseType(results: map))
                .policyAccountInfo(null)
                .corpUserInfo(corpUserInfo)
                .subAccountConfig(new GetSubAccountConfigResponseType())
                .build()
        expect:
        HotelAuthExtensionUtil.getOrderMode(accountInfo) == expectedOrderMode
        where:
        billType || expectedOrderMode
        "E"      || "TR"
        "A"      || "OR"
        ""       || "OR"
    }

    @Unroll
    def "buildSettlementAmount should return correct settlement amount"() {
        given:
        expect:
        result == HotelAuthExtensionUtil.buildSettlementAmount(responseType)
        where:
        responseType                                                                                                                                                                                                                                    || result
        new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(roomDiscountInfo: null))                                                                                                                                                || BigDecimal.ZERO
        new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(roomDiscountInfo: new RoomDiscountInfo(customPromotionAmount: null)))                                                                                                   || BigDecimal.ZERO
        new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(customAmountInfo: new AmountDetailEntity(amount: BigDecimal.TEN), roomDiscountInfo: new RoomDiscountInfo(customPromotionAmount: new PriceType(price: BigDecimal.ONE)))) || new BigDecimal("9")
        new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(customAmountInfo: null))                                                                                                                                                || BigDecimal.ZERO
        new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(customAmountInfo: new AmountDetailEntity(amount: BigDecimal.TEN)))                                                                                                      || BigDecimal.TEN
    }

    @Unroll
    def "buildClientInfos should return correct client infos"() {
        given:
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(responseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo()
        expect:
        result == HotelAuthExtensionUtil.buildClientInfos(requestType, checkAvailInfo, null, null)
        where:
        requestType                                                                                                                                                                                                                                                    | responseType                                                                                || result
        new OrderCreateRequestType(hotelBookPassengerInputs: [])                                                                                                                                                                                                       | new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(balanceType: "PP")) || []
        new OrderCreateRequestType(hotelBookPassengerInputs: [null])                                                                                                                                                                                                   | new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(balanceType: "PP")) || []
        new OrderCreateRequestType(hotelBookPassengerInputs: [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(infoId: "infoid"), name: "name", passengerBasicInfo: new PassengerBasicInfo(preferFirstName: "san", preferLastName: "zhang"))]) | new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(balanceType: "PP")) || [new ClientInfoType(name: "zhang/san", uid: null)]
        new OrderCreateRequestType(hotelBookPassengerInputs: [new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(employee: "T", uid: "123"))])                                                                                                   | new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(balanceType: "PP")) || [new ClientInfoType(name: "", uid: "123")]
    }

    def "test buildHotelProductInfo"() {
        given:
        WrapperOfCheckAvail.BaseCheckAvailInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(new QueryCheckAvailContextResponseType(roomInfo:
                                new BookRoomInfoEntity(balanceType: "PP")))
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsId")))
                        .build().getCheckAvailContextInfo()
        OrderCreateRequestType requestType = new OrderCreateRequestType(
                cityInput: new CityInput(),
                hotelBookInput: new HotelBookInput(hotelDateRangeInfo: new HotelDateRangeInfo()))
        QueryCheckAvailContextResponseType responseType = new QueryCheckAvailContextResponseType(
                roomInfo: new BookRoomInfoEntity(),
                hotelInfo: new BookHotelInfoEntity())
        responseType.roomInfo.roomType = "M"
        responseType.hotelInfo.subHotelId = 1
        responseType.hotelInfo.masterHotelId = 2
        responseType.hotelInfo.star = 3
        requestType.cityInput.cityId = 4
        requestType.hotelBookInput.roomQuantity = 5
        requestType.hotelBookInput.hotelDateRangeInfo.checkIn = "2024-08-20"
        requestType.hotelBookInput.hotelDateRangeInfo.checkOut = "2024-08-21"
        when:
        HotelProductInfoType result = HotelAuthExtensionUtil.buildHotelProductInfo(requestType, responseType, checkAvailInfo)
        then:
        result.roomQuantity == 5
        result.star == 3
        result.cityId == 4
        result.masterHotelId == 2
        result.hotelId == 1
        result.hotelType == "M"
        result.checkInTime == "2024-08-20 00:00:00"
        result.checkOutTime == "2024-08-21 00:00:00"
    }

    def "test buildTravelControlInfo"() {
        given:
        GetTravelPolicyContextResponseType getTravelPolicyContextResponseType = new GetTravelPolicyContextResponseType()
        FinalPolicyType finalPolicyType = new FinalPolicyType()
        FinalTravelPolicyType finalTravelPolicyType = new FinalTravelPolicyType()
        finalTravelPolicyType.maxSettlementPrice = 1000
        finalTravelPolicyType.maxStar = 5
        finalTravelPolicyType.minSettlementPrice = 500
        finalTravelPolicyType.minStar = 3
        finalPolicyType.finalTravelPolicy = finalTravelPolicyType
        getTravelPolicyContextResponseType.finalPolicy = finalPolicyType
        CheckTravelPolicyResponseType checkTravelPolicyResponseType = new CheckTravelPolicyResponseType(checkItemsResult: new CheckItemsResultType(priceCheckResult: new PriceCheckResultType(inControl: true)))
        when:
        def result = HotelAuthExtensionUtil.buildTravelControlInfo(getTravelPolicyContextResponseType, checkTravelPolicyResponseType, new OrderCreateRequestType(corpPayInfo: new CorpPayInfo("public")), new ResourceToken())

        then:

        then:
        result.policyHighPrice == 1000
        result.policyHighStar == 5
        result.policyLowPrice == 500
        result.policyLowStar == 3
        result.priceMatchPolicy == true
    }
}
