package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.hotel.common.util.PbSerializerUtil
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceDetailInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CertificateInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.EmailInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.InvoiceInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PhoneInfo
import com.ctrip.corp.bff.hotel.book.contract.HotelContactorInfo
import com.ctrip.corp.bff.hotel.book.contract.HotelInvoiceInfo
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.tools.contract.DataInfo
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2024/7/16 16:03
 *
 */
class MapperOfCheckDataRequestTypeTest extends Specification {

    def myTestClass = Spy(new MapperOfCheckDataRequestType())

    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }

    def "test addInvoiceEmailData empty"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()

        when:
        List<DataInfo> result = myTestClass.addInvoiceEmailData(orderCreateRequestType)

        then:
        result.size() == 0
    }

    def "test addInvoiceEmailData"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        HotelInvoiceInfo hotelInvoiceInfo1 = new HotelInvoiceInfo()
        InvoiceInfo invoiceInfo1 = new InvoiceInfo()
        EmailInfo emailInfo1 = new EmailInfo()
        emailInfo1.setTransferEmail("<EMAIL>")
        invoiceInfo1.setEmailInfo(emailInfo1)
        hotelInvoiceInfo1.setInvoiceInfo(invoiceInfo1)
        hotelInvoiceInfo1.setHotelInvoiceType("type1")
        HotelInvoiceInfo hotelInvoiceInfo2 = new HotelInvoiceInfo()
        InvoiceInfo invoiceInfo2 = new InvoiceInfo()
        EmailInfo emailInfo2 = new EmailInfo()
        emailInfo2.setTransferEmail("<EMAIL>")
        invoiceInfo2.setEmailInfo(emailInfo2)
        hotelInvoiceInfo2.setInvoiceInfo(invoiceInfo2)
        hotelInvoiceInfo2.setHotelInvoiceType("type2")
        orderCreateRequestType.setHotelInvoiceInfos([hotelInvoiceInfo1, hotelInvoiceInfo2])

        when:
        List<DataInfo> result = myTestClass.addInvoiceEmailData(orderCreateRequestType)

        then:
        result.size() == 2
        result[0].data == "<EMAIL>"
        result[0].type == "email"
        result[0].key == "invoiceEmail_type1"
        result[1].data == "<EMAIL>"
        result[1].type == "email"
        result[1].key == "invoiceEmail_type2"
    }

    def "test addContactorData empty"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()

        when:
        List<DataInfo> result = myTestClass.addContactorData(orderCreateRequestType)

        then:
        result.size() == 0
    }

    def "test addContactorData"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        HotelContactorInfo hotelContactorInfo = new HotelContactorInfo()
        EmailInfo emailInfo = new EmailInfo()
        emailInfo.setTransferEmail("<EMAIL>")
        hotelContactorInfo.setEmailInfo(emailInfo)
        PhoneInfo phoneInfo = new PhoneInfo()
        phoneInfo.setTransferPhoneNo("*********0")
        phoneInfo.setCountryCode("86")
        hotelContactorInfo.setPhoneInfo(phoneInfo)
        orderCreateRequestType.setHotelContactorInfo(hotelContactorInfo)

        when:
        List<DataInfo> result = myTestClass.addContactorData(orderCreateRequestType)

        then:
        result.size() == 2
        result[0].data == "<EMAIL>"
        result[0].type == "email"
        result[0].key == "contactPsgEmail"
        result[1].data == "*********0"
        result[1].type == "mobile_phone_cn"
        result[1].key == "contactPsgPhone"
    }

    def "test addPassengerData empty"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()

        when:
        List<DataInfo> result = myTestClass.addPassengerData(orderCreateRequestType)

        then:
        result.size() == 0
    }

    def "test addPassengerData"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        HotelBookPassengerInput passengerInfo1 = new HotelBookPassengerInput()
        passengerInfo1.hotelPassengerInput = new HotelPassengerInput()
        EmailInfo emailInfo1 = new EmailInfo()
        emailInfo1.setTransferEmail("<EMAIL>")
        passengerInfo1.setEmailInfo(emailInfo1)
        PhoneInfo phoneInfo1 = new PhoneInfo()
        phoneInfo1.setTransferPhoneNo("*********0")
        phoneInfo1.setCountryCode("81")
        passengerInfo1.setPhoneInfo(phoneInfo1)
        CertificateInfo certificateInfo1 = new CertificateInfo()
        certificateInfo1.setCertificateNo("*********")
        certificateInfo1.setCertificateType("PASSPORT")
        passengerInfo1.setCertificateInfo(certificateInfo1)
        passengerInfo1.hotelPassengerInput.uid = "789"
        passengerInfo1.hotelPassengerInput.employee = "T"
        HotelBookPassengerInput passengerInfo2 = new HotelBookPassengerInput()
        passengerInfo2.hotelPassengerInput = new HotelPassengerInput()
        EmailInfo emailInfo2 = new EmailInfo()
        emailInfo2.setTransferEmail("<EMAIL>")
        passengerInfo2.setEmailInfo(emailInfo2)
        PhoneInfo phoneInfo2 = new PhoneInfo()
        phoneInfo2.setTransferPhoneNo("9876543210")
        phoneInfo2.setCountryCode("852")
        passengerInfo2.setPhoneInfo(phoneInfo2)
        CertificateInfo certificateInfo2 = new CertificateInfo()
        certificateInfo2.setCertificateNo("*********")
        certificateInfo2.setCertificateType("PASSPORT")
        passengerInfo2.setCertificateInfo(certificateInfo2)
        passengerInfo2.hotelPassengerInput.infoId = "345"
        orderCreateRequestType.setHotelBookPassengerInputs([passengerInfo1, passengerInfo2])

        when:
        List<DataInfo> result = myTestClass.addPassengerData(orderCreateRequestType)

        then:
        result.size() == 6
        result[0].data == "<EMAIL>"
        result[0].type == "email"
        result[0].key == "clientPsgEmail_789_0"
        result[1].data == "*********0"
        result[1].type == "mobile_phone_foreign"
        result[1].key == "clientPsgPhone_789_0"
        result[2].data == "*********"
        result[2].type == "passport"
        result[2].key == "clientPsgCard_789_0"

        result[3].data == "<EMAIL>"
        result[3].type == "email"
        result[3].key == "clientPsgEmail_345_1"
        result[4].data == "9876543210"
        result[4].type == "mobile_phone_hk"
        result[4].key == "clientPsgPhone_345_1"
        result[5].data == "*********"
        result[5].type == "passport"
        result[5].key == "clientPsgCard_345_1"
    }

    def "should return empty list when insuranceInput or orderCreateInsuranceInfoInputs is null"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()

        when:
        List<DataInfo> result = myTestClass.addInsurancePassengerData(orderCreateRequestType)

        then:
        result.size() == 0
    }

    def "should return empty list when insurancePassengerInfos is empty"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        HotelInsuranceInput insuranceInput = new HotelInsuranceInput()
        insuranceInput.hotelInsuranceDetailInputs = new ArrayList()
        orderCreateRequestType.hotelInsuranceInput = insuranceInput

        when:
        List<DataInfo> result = myTestClass.addInsurancePassengerData(orderCreateRequestType)

        then:
        result.size() == 0
    }

    def "should return list of DataInfo objects with correct values"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()
        HotelInsuranceInput insuranceInput = new HotelInsuranceInput()
        HotelInsuranceDetailInput insuranceInfoInput = new HotelInsuranceDetailInput()
        HotelBookPassengerInput passengerInfo1 = new HotelBookPassengerInput(hotelPassengerInput: new HotelPassengerInput(uid: "uid", employee: "T"))
        EmailInfo emailInfo1 = new EmailInfo()
        emailInfo1.setTransferEmail("<EMAIL>")
        passengerInfo1.setEmailInfo(emailInfo1)
        PhoneInfo phoneInfo1 = new PhoneInfo()
        phoneInfo1.setTransferPhoneNo("*********0")
        phoneInfo1.setCountryCode("86")
        passengerInfo1.setPhoneInfo(phoneInfo1)
        CertificateInfo certificateInfo1 = new CertificateInfo()
        certificateInfo1.setCertificateNo("*********")
        certificateInfo1.setCertificateType("PASSPORT")
        passengerInfo1.setCertificateInfo(certificateInfo1)
        insuranceInfoInput.setInsuranceHotelBookPassengerInputs([passengerInfo1])
        insuranceInput.hotelInsuranceDetailInputs = Arrays.asList(insuranceInfoInput)
        orderCreateRequestType.hotelInsuranceInput = insuranceInput

        when:
        List<DataInfo> result = myTestClass.addInsurancePassengerData(orderCreateRequestType)

        then:
        result.size() == 3
        result[0].data == "<EMAIL>"
        result[0].type == "email"
        result[0].key == "insurancePsgEmail_uid_0_0"
        result[1].data == "*********0"
        result[1].type == "mobile_phone_cn"
        result[1].key == "insurancePsgPhone_uid_0_0"
        result[2].data == "*********"
        result[2].type == "passport"
        result[2].key == "insurancePsgCard_uid_0_0"
    }

    def "orderCreateToken"() {
        given:
        when:
        def orderCreateTokenStr = PbSerializerUtil.pbDeserialize("H4sIAAAAAAAA_-MSCfb0c_dxjXcMCAjyD3P0iXfz8Q_XYAQA4NUpBRgAAAA", OrderCreateToken.class);
        then:
        orderCreateTokenStr != null
    }
}
