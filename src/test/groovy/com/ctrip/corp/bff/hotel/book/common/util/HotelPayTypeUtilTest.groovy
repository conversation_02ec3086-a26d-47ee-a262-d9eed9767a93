package com.ctrip.corp.bff.hotel.book.common.util

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.BffPayTypeEnum
import com.ctrip.corp.bff.framework.template.entity.contract.integration.StrategyInfo
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import com.ctrip.corp.order.data.aggregation.query.contract.OrderGenericInfoType
import com.ctrip.corp.order.data.aggregation.query.contract.QueryHotelOrderDataResponseType
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2024/12/2 14:28
 *
 */
class HotelPayTypeUtilTest extends Specification {
    @Unroll
    def "testGetSelectedRoomPayType with #description"() {
        given:

        when:
        HotelPayTypeEnum result = HotelPayTypeUtil.getSelectedRoomPayType(requestType, queryOrderDataResponseType)

        then:
        result == expectedResult

        where:
        description           | requestType                                                                                                                          | queryOrderDataResponseType                                                                          | expectedResult
        "requestType is null" | null                                                                                                                                 | Mock(QueryHotelOrderDataResponseType)                                                               | HotelPayTypeEnum.NONE
        "valid requestType"   | new BookingInitRequestType(hotelPayTypeInput: Arrays.asList(new HotelPayTypeInput(payType: "CORP_PAY", payCode: "ROOM")))            | Mock(QueryHotelOrderDataResponseType)                                                               | HotelPayTypeEnum.CORP_PAY
        "valid requestType"   | new BookingInitRequestType(strategyInfos: Arrays.asList(new StrategyInfo(strategyKey: "BOOKING_SCENARIO", strategyValue: "MODIFY"))) | new QueryHotelOrderDataResponseType(orderGenericInfo: new OrderGenericInfoType(multiPayFlag: true)) | HotelPayTypeEnum.MIX_PAY
    }

    @Unroll
    def "testGetRoomPayTypeFromOrder with #description"() {
        given:
        OrderGenericInfoType orderGenericInfoType = new OrderGenericInfoType(multiPayFlag: multiPayFlag, payType: payType)
        QueryHotelOrderDataResponseType queryOrderDataResponseType = new QueryHotelOrderDataResponseType(orderGenericInfo: orderGenericInfoType)

        when:
        HotelPayTypeEnum result = HotelPayTypeUtil.getRoomPayTypeFromOrder(queryOrderDataResponseType)

        then:
        result == expectedResult

        where:
        description                     | multiPayFlag | payType  | expectedResult
        "multiPayFlag is true"          | true         | null     | HotelPayTypeEnum.MIX_PAY
        "payType is ACCNT"              | false        | "ACCNT"  | HotelPayTypeEnum.CORP_PAY
        "payType is PRBAL"              | false        | "PRBAL"  | HotelPayTypeEnum.PRBAL
        "payType is not ACCNT"          | false        | "OTHER"  | HotelPayTypeEnum.SELF_PAY
        "payType is blank"              | false        | ""       | HotelPayTypeEnum.NONE
        "orderGenericInfo is null"      | null         | null     | HotelPayTypeEnum.NONE
    }
    def "testBuildBffPayTypeEnum"(){
        given:
        TrackingUtil.buildBaseTrackingMap(null)
        when:
        BffPayTypeEnum result = HotelPayTypeUtil.buildBffPayTypeEnum(hotelPayTypeEnum)
        then:
        result == expectedResult
        where:
        hotelPayTypeEnum || expectedResult
        HotelPayTypeEnum.CORP_PAY || BffPayTypeEnum.CORP_PAY
        HotelPayTypeEnum.SELF_PAY || BffPayTypeEnum.SELF_PAY
        HotelPayTypeEnum.CASH || BffPayTypeEnum.CASH
        HotelPayTypeEnum.MIX_PAY || BffPayTypeEnum.MIX_PAY
        HotelPayTypeEnum.FLASH_STAY_PAY || BffPayTypeEnum.FLASH_STAY_PAY
        HotelPayTypeEnum.UNION_PAY || BffPayTypeEnum.UNION_PAY
        HotelPayTypeEnum.ADVANCE_PAY || BffPayTypeEnum.ADVANCE_PAY
        HotelPayTypeEnum.NONE || BffPayTypeEnum.NONE

    }
}
