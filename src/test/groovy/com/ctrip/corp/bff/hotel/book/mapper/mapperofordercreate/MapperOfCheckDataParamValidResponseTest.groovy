package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate


import com.ctrip.corp.bff.framework.hotel.common.qconfig.QConfigOfCustomConfig
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPassengerInput
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PassengerBasicInfo
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response.MapperOfCheckDataParamValidResponse
import com.ctrip.corp.bff.hotel.book.sharkmock.SharkMockUtil
import com.ctrip.corp.bff.tools.contract.CheckDataResponseType
import com.ctrip.corp.bff.tools.contract.DataCheckResult
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2024/7/16 17:51
 *
 */
class MapperOfCheckDataParamValidResponseTest extends Specification {


    def myTestClass = Spy(new MapperOfCheckDataParamValidResponse())

    def savePoint = new SavePoint()

    void setup() {
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "check"() {
        given:
        new MockUp<QConfigOfCustomConfig>() {
            @Mock
            public static boolean isSupport(String key, String corpId) {
                return true;
            }
        }
        MapperOfCheckDataParamValidResponse mapper = new MapperOfCheckDataParamValidResponse()
        when:
        def result = mapper.check(Tuple2.of(new OrderCreateRequestType(integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(corpId: "corp"))), new CheckDataResponseType()))
        then:
        result == null
    }

    def "test validCheckDataInvoiceEmail error"() {
        given:
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return null;
            }
        }
        List<DataCheckResult> dataCheckResultFail = [
                new DataCheckResult(key: "invoiceEmail_1", checkResult: "F"),
                new DataCheckResult(key: "invoiceEmail_2", checkResult: "T"),
                new DataCheckResult(key: "KEY_OTHER", checkResult: "F")
        ]

        when:
        def result = myTestClass.validCheckDataInvoiceEmail(dataCheckResultFail)

        then:
        result.errorCode == 490
    }

    def "test validCheckDataInvoiceEmail"() {
        given:
        List<DataCheckResult> dataCheckResultFail = [
                new DataCheckResult(key: "invoiceEmail_1", checkResult: "T"),
                new DataCheckResult(key: "invoiceEmail_2", checkResult: "T")
        ]

        when:
        def result = myTestClass.validCheckDataInvoiceEmail(dataCheckResultFail)

        then:
        result == null
    }

    def "test validCheckDataContactPsg error"() {
        given:
        def dataCheckResultFailMap = new HashMap() {
            {
                put("contactPsgEmail", "F")
                put("contactPsgPhone", "F")
            }
        }
        when:
        def result = myTestClass.validCheckDataContactPsg(dataCheckResultFailMap)

        then:
        result.errorCode == 492
        result.friendlyMessage == "联系人：手机、邮箱信息不正确，请返回修改"
    }

    def "test validCheckDataContactPsg"() {
        given:
        def dataCheckResultFailMap = new HashMap() {
            {
                put("contactPsgEmail", "T")
                put("contactPsgPhone", "T")
            }
        }

        when:
        def result = myTestClass.validCheckDataContactPsg(dataCheckResultFailMap)

        then:
        result == null
    }

    def "test validCheckDataClientPsg error"() {
        given:
        def dataCheckResultFailMap = new HashMap() {
            {
                put("clientPsgEmail_uid_0", "F")
                put("clientPsgPhone_uid_0", "F")
                put("clientPsgCard_uid_0", "F")
                put("clientPsgEmail_infoid_1", "T")
                put("clientPsgPhone_infoid_1", "F")
                put("clientPsgCard_infoid_1", "T")
            }
        }
        def clients = Arrays.asList(
                new HotelBookPassengerInput(name: "张朝阳", hotelPassengerInput: new HotelPassengerInput( uid: "uid", employee: "T"), passengerBasicInfo: new PassengerBasicInfo(preferLastName: "zhang", preferFirstName: "chao yang")),
                new HotelBookPassengerInput(name: "张三",  hotelPassengerInput: new HotelPassengerInput(infoId: "infoid", employee: "F"))
        )
        when:
        def result = myTestClass.validCheckDataClientPsg(dataCheckResultFailMap, clients)

        then:
        result.errorCode == 493
        result.friendlyMessage == "入住人：张朝阳/zhang chao yang 手机、邮箱、证件;张三 手机信息不正确，请返回修改"
    }

}
