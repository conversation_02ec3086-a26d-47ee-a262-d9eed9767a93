package com.ctrip.corp.bff.hotel.book.common.wrapper

import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType
import com.ctrip.corp.agg.hotel.tmc.checktravelpolicy.entity.CheckTravelPolicyResponseType
import com.ctrip.corp.agg.hotel.tmc.context.entity.GetTravelPolicyContextResponseType
import com.ctrip.corp.bff.framework.hotel.common.wrapper.WrapperOfAccount
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.qconfig.QconfigOfCertificateInitConfig
import com.ctrip.soa.corp.order.orderindexextservice.v1.GetOrderFoundationDataResponseType
import spock.lang.Specification

class WrapperOfCheckHotelAuthExtensionTest extends Specification{
    def "builder and getters should work and cover all lines"() {
        given:
        def accountInfo = Mock(WrapperOfAccount.AccountInfo)
        def getTravelPolicyContextResponseType = Mock(GetTravelPolicyContextResponseType)
        def checkTravelPolicyResponseType = Mock(CheckTravelPolicyResponseType)
        def queryCheckAvailContextResponseType = Mock(QueryCheckAvailContextResponseType)
        def orderCreateRequestType = Mock(OrderCreateRequestType)
        def resourceToken = Mock(ResourceToken)
        def checkAvailInfo = Mock(WrapperOfCheckAvail.BaseCheckAvailInfo)
        def qconfigOfCertificateInitConfig = Mock(QconfigOfCertificateInitConfig)
        def strategyInfoMap = Mock(Map)
        def getOrderFoundationDataResponseType = Mock(GetOrderFoundationDataResponseType)
        def orderCreateToken = Mock(OrderCreateToken)

        when:
        def wrapper = WrapperOfCheckHotelAuthExtension.builder()
                .withAccountInfo(accountInfo)
                .withGetTravelPolicyContextResponseType(getTravelPolicyContextResponseType)
                .withCheckTravelPolicyResponseType(checkTravelPolicyResponseType)
                .withQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                .withOrderCreateRequestType(orderCreateRequestType)
                .withResourceToken(resourceToken)
                .withCheckAvailInfo(checkAvailInfo)
                .withQconfigOfCertificateInitConfig(qconfigOfCertificateInitConfig)
                .withStrategyInfoMap(strategyInfoMap)
                .withGetOrderFoundationDataResponseType(getOrderFoundationDataResponseType)
                .withOrderCreateToken(orderCreateToken)
                .build()

        then:
        wrapper.getAccountInfo() == accountInfo
        wrapper.getGetTravelPolicyContextResponseType() == getTravelPolicyContextResponseType
        wrapper.getCheckTravelPolicyResponseType() == checkTravelPolicyResponseType
        wrapper.getQueryCheckAvailContextResponseType() == queryCheckAvailContextResponseType
        wrapper.getOrderCreateRequestType() == orderCreateRequestType
        wrapper.getResourceToken() == resourceToken
        wrapper.getCheckAvailInfo() == checkAvailInfo
        wrapper.getQconfigOfCertificateInitConfig() == qconfigOfCertificateInitConfig
        wrapper.getStrategyInfoMap() == strategyInfoMap
        wrapper.getGetOrderFoundationDataResponseType() == getOrderFoundationDataResponseType
        wrapper.getOrderCreateToken() == orderCreateToken
    }
}
