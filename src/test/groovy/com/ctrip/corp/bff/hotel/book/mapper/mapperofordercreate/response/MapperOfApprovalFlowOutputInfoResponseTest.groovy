package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response

import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil
import com.ctrip.corp.bff.specific.contract.ApprovalFlowComputeResponseType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;

class MapperOfApprovalFlowOutputInfoResponseTest extends Specification {


    def tester = Spy(new MapperOfApprovalFlowOutputInfoResponse())

    def savePoint = new SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        given:
        new MockUp<TokenParseUtil>() {
            @Mock
            public static <T> String generateToken(T pidEntity, Class<T> tClass) {
                return ""
            }
        }
        expect:
        !tester.convert(Tuple2.of(new ApprovalFlowComputeResponseType(approvalAllDone: "T"), new OrderCreateToken())).t1
        tester.convert(Tuple2.of(new ApprovalFlowComputeResponseType(), new OrderCreateToken())).t1
    }

    def "checkApprovalFlowComputeResponseType" () {
        expect:
        !new MapperOfApprovalFlowOutputInfoResponse().checkApprovalFlowComputeResponseType(null)
        new MapperOfApprovalFlowOutputInfoResponse().checkApprovalFlowComputeResponseType(new ApprovalFlowComputeResponseType(approvalAllDone: "T"))
        !new MapperOfApprovalFlowOutputInfoResponse().checkApprovalFlowComputeResponseType(new ApprovalFlowComputeResponseType(approvalAllDone: "F"))
        new MapperOfApprovalFlowOutputInfoResponse().checkApprovalFlowComputeResponseType(new ApprovalFlowComputeResponseType(approvalAllDone: "F", approvalFlowToken: "A"))
    }

    def "buildApprovalFlowOutPutInfo" () {
        expect:
        new MapperOfApprovalFlowOutputInfoResponse().buildApprovalFlowOutPutInfo(new ApprovalFlowComputeResponseType(approvalFlowToken: "a")).approvalFlowToken == "a"
    }


    def "check" () {
        expect:
        !new MapperOfApprovalFlowOutputInfoResponse().check(Tuple2.of(null, null)) .result
    }
}
