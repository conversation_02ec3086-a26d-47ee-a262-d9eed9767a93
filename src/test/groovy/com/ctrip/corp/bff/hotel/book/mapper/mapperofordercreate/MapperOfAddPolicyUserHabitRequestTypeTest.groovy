package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPolicyInput
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PolicyInput
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import corp.user.service.dbdecouplingService.contract.AddPolicyUserHabitRequestType
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2024/12/13 14:07
 *
 */
class MapperOfAddPolicyUserHabitRequestTypeTest extends Specification {

    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }

    MapperOfAddPolicyUserHabitRequestType mapper = new MapperOfAddPolicyUserHabitRequestType()

    def "testConvert with default scenario"() {
        given:
        UserInfo userInfo = new UserInfo(userId: "user123")
        IntegrationSoaRequestType integrationSoaRequestType = new IntegrationSoaRequestType(userInfo: userInfo, requestId: "request123")
        PolicyInput policyInput = new PolicyInput(policyUid: "policy123")
        HotelPolicyInput hotelPolicyInput = new HotelPolicyInput(policyInput: policyInput)
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(integrationSoaRequestType: integrationSoaRequestType, hotelPolicyInput: hotelPolicyInput)

        when:
        AddPolicyUserHabitRequestType result = mapper.convert(Tuple1.of(orderCreateRequestType) as Tuple1<OrderCreateRequestType>)

        then:
        result.getUid() == "user123"
        result.getPolicyUid() == "policy123"
        result.getOperator() == "user123"
        result.getRid() == "request123"
    }
}
