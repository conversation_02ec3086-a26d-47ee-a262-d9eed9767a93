package com.ctrip.corp.bff.hotel.book.service

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookPassengerInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceDetailInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelInsuranceInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CertificateInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.PassengerBasicInfo
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.contract.UserActionInfo
import com.ctrip.corp.bff.hotel.book.sharkmock.SharkMockUtil
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2024/7/10 8:10
 *
 */
class ServiceOfOrderCreateTest extends Specification {
    def myTestClass = Spy(new ServiceOfOrderCreate())
    def savePoint = new SavePoint()

    void setup() {
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "Test checkCorpInsuranceInfo"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType()

        when:
        boolean result = myTestClass.checkCorpInsuranceInfo(orderCreateRequestType)

        then:
        result

        when:
        orderCreateRequestType.hotelInsuranceInput = new HotelInsuranceInput()
        result = myTestClass.checkCorpInsuranceInfo(orderCreateRequestType)
        then:
        result

        when:
        orderCreateRequestType.hotelInsuranceInput.hotelInsuranceDetailInputs = []
        result = myTestClass.checkCorpInsuranceInfo(orderCreateRequestType)
        then:
        result

        when:
        orderCreateRequestType.hotelInsuranceInput.hotelInsuranceDetailInputs = [new HotelInsuranceDetailInput()]
        myTestClass.checkCorpInsuranceInfo(orderCreateRequestType)
        then:
        def ex = thrown(BusinessException)
        ex.errorCode == 487


        when:
        orderCreateRequestType.userActionInfo = new UserActionInfo()
        orderCreateRequestType.userActionInfo.userActionToken = "ddddd"
        orderCreateRequestType.hotelInsuranceInput.hotelInsuranceDetailInputs = [new HotelInsuranceDetailInput()]
        myTestClass.checkCorpInsuranceInfo(orderCreateRequestType)
        then:
        ex = thrown(BusinessException)
        ex.errorCode == 482
    }

    def "Test checkInsurancePassenger"() {
        given:
        HotelBookPassengerInput insurancePsg = new HotelBookPassengerInput()

        when:
        boolean result = myTestClass.checkInsurancePassenger(insurancePsg)
        then:
        !result

        when:
        insurancePsg.name = "John Doe"
        result = myTestClass.checkInsurancePassenger(insurancePsg)
        then:
        !result

        when:
        insurancePsg.name = null
        insurancePsg.passengerBasicInfo = new PassengerBasicInfo()
        result = myTestClass.checkInsurancePassenger(insurancePsg)
        then:
        !result

        when:
        insurancePsg.certificateInfo = new CertificateInfo()
        insurancePsg.certificateInfo.certificateType = "PASSPORT"
        insurancePsg.certificateInfo.certificateNo = "123456"
        insurancePsg.passengerBasicInfo.birth = "1990-01-01"
        insurancePsg.name = "John Doe"
        result = myTestClass.checkInsurancePassenger(insurancePsg)
        then:
        result

        when:
        insurancePsg.certificateInfo.certificateType = "IDENTITY_CARD"
        insurancePsg.certificateInfo.certificateNo = "11010119900101001X"
        result = myTestClass.checkInsurancePassenger(insurancePsg)
        then:
        result
    }

    def "Test getAge"() {
        given:
        HotelBookPassengerInput insurancePsg = new HotelBookPassengerInput()
        insurancePsg.certificateInfo = new CertificateInfo()
        insurancePsg.certificateInfo.certificateType = 2
        when:
        int age = myTestClass.getAge(insurancePsg)
        then:
        age == 0

        when:
        insurancePsg.certificateInfo = new CertificateInfo()
        insurancePsg.certificateInfo.certificateType = "IDENTITY_CARD"
        insurancePsg.certificateInfo.certificateNo = "11010119900101001X"
        age = myTestClass.getAge(insurancePsg)
        then:
        age == 0

        when:
        insurancePsg.certificateInfo.certificateType = "PASSPORT"
        insurancePsg.passengerBasicInfo = new PassengerBasicInfo()
        insurancePsg.passengerBasicInfo.birth = "1990-01-01"
        age = myTestClass.getAge(insurancePsg)
        then:
        age == 35
    }

    def "Test checkHotelPayType"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "123456", corpId: "123456")),
                corpPayInfo: new CorpPayInfo(corpPayType: "public"),
                cityInput: new CityInput(cityId: 1),
                hotelPayTypeInput: Arrays.asList(new HotelPayTypeInput(payCode: "ROOM")),
                hotelBookPassengerInputs: Arrays.asList(new HotelBookPassengerInput()),
                hotelBookInput: new HotelBookInput(hotelDateRangeInfo: new HotelDateRangeInfo(checkIn: "2025-11-11", checkOut: "2025-11-12"), adultQuantity: 1, roomQuantity: 1))
        when:
        myTestClass.checkHotelPayType(orderCreateRequestType)
        then:
        def ex = thrown(BusinessException)
        ex.errorCode == 661
    }

}
