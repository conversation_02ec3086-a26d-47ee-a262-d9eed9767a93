package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelBrandItem
import com.ctrip.corp.agg.hotel.roomavailable.entity.HotelItem
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import spock.lang.Specification

class MapperOfSearchRegistrationFieldsRequestTypeTest extends Specification {

    def tester = Spy(MapperOfSearchRegistrationFieldsRequestType)

    def savePoint = new mockit.internal.state.SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        given:
        def input = Mock(WrapperOfCheckAvail.CheckAvailInfo)
        input.getHotelItem() >> new HotelItem(hotelBrandInfo: new HotelBrandItem(groupId: 2))
        expect:
        tester.convert(Tuple2.of(new IntegrationSoaRequestType(), input)).mgrGroupId == 2
    }
}
