package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.approve.ws.contract.MatchApprovalFlowResponseType
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple7
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple6;

class MapperOfApprovalFlowComputeRequestTypeTest extends Specification {

    def tester = Spy(new MapperOfApprovalFlowComputeRequestType())

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        expect:
        tester.convert(Tuple7.of(null, null, null, null, null, null, null)) == null
        tester.convert(Tuple7.of(null, new MatchApprovalFlowResponseType(), new OrderCreateRequestType(), null, null, null, null)) .approvalType == "B"
    }
}
