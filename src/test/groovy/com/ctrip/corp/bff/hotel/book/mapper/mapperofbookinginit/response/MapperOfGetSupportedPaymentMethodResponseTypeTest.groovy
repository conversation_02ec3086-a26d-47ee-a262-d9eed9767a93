package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit.response

import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.hotel.book.processor.ProcessorOfBookingInit
import com.ctrip.corp.hotelbook.commonws.entity.GetSupportedPaymentMethodResponseType
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1;

class MapperOfGetSupportedPaymentMethodResponseTypeTest extends Specification {

    def tester = new MapperOfGetSupportedPaymentMethodResponseType()

    def savePoint = new SavePoint()
    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "check" () {
        given: "A ProcessorOfBookingInit instance and a null GetSupportedPaymentMethodResponseType"
        new MockUp<BFFSharkUtil>() {
            @mockit.Mock
            public static String getSharkValue(String key) {
                if (key.contains("665")) {
                    return null
                }
                return key + ":value"
            }
        }

        when: "Calling checkGetSupportedPaymentMethodResponseType"
        def res1 = tester.check(Tuple1.of(null))

        then: "An exception is thrown"
        res1.errorCode == 665
        res1.friendlyMessage == "key.corp.hotel.CorpHotelBookCommonWS.getSupportedPaymentMethod.errorMsg:value"

        when: "Calling checkGetSupportedPaymentMethodResponseType"
        def res2 = tester.check(Tuple1.of(new GetSupportedPaymentMethodResponseType(responseCode: null)))

        then: "An exception is thrown"
        res2.errorCode == 665
        res2.friendlyMessage == "key.corp.hotel.CorpHotelBookCommonWS.getSupportedPaymentMethod.errorMsg:value"


        when: "Calling checkGetSupportedPaymentMethodResponseType"
        def res3 = tester.check(Tuple1.of(new GetSupportedPaymentMethodResponseType(responseCode: 6767)))

        then: "An exception is thrown"
        res3.errorCode == 665
        res3.friendlyMessage == "key.corp.hotel.CorpHotelBookCommonWS.getSupportedPaymentMethod.errorMsg.6767:value"

        expect: "Calling checkGetSupportedPaymentMethodResponseType"
        tester.check(Tuple1.of(new GetSupportedPaymentMethodResponseType(responseCode: 20000))) == null

    }
}
