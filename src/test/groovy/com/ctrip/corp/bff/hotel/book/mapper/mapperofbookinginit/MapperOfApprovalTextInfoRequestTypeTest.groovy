package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import spock.lang.Specification
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple2;

class MapperOfApprovalTextInfoRequestTypeTest extends Specification {

    def tester = Spy(MapperOfApprovalTextInfoRequestType)

    def savePoint = new mockit.internal.state.SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert"() {
        expect:
        tester.convert(Tuple2.of(new IntegrationSoaRequestType(language: "a"), null)).integrationSoaRequestType.language == "a"
    }

}
