package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate.response

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelPayTypeInput
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessException
import com.ctrip.corp.bff.framework.template.common.exception.business.BusinessExceptionBuilder
import com.ctrip.corp.bff.framework.template.common.shark.BFFSharkUtil
import com.ctrip.corp.bff.hotel.book.common.enums.HotelPayTypeEnum
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.bff.hotel.book.sharkmock.SharkMockUtil
import com.ctrip.soa._24373.CheckInfoType
import com.ctrip.soa._24373.CreateOrderCheckResponseType
import com.ctrip.soa._24373.VerifyInfoLimitType
import mockit.Mock
import mockit.MockUp
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR>
 * @date 2025/1/3 21:11
 *
 */
class MapperOfCreateOrderCheckResponseTest extends Specification {


    def savePoint = new SavePoint()

    void setup() {
        new MockUp<BFFSharkUtil>() {
            @Mock
            public static String getSharkValue(String key) {
                return SharkMockUtil.mapSharks().get(key);
            }
        }
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "testFlashInterceptThrowsException"() {
        given: "Mock dependencies and inputs"
        def mapper = new MapperOfCreateOrderCheckResponse()
        def orderCreateRequestType = new OrderCreateRequestType(hotelPayTypeInput: Arrays.asList(new HotelPayTypeInput(payType: HotelPayTypeEnum.FLASH_STAY_PAY.code, payCode: "ROOM")))
        def checkInfoType = new CheckInfoType(scenario: "FlashOrder", verifyResult: false, failCodeList: ["FlashOrderUnFinish"])
        def verifyInfoLimitType = new VerifyInfoLimitType(code: "FlashOrderUnFinish", limit: 2)
        checkInfoType.setVerifyInfoList([verifyInfoLimitType])
        def orderCheckResponse = new CreateOrderCheckResponseType(responseCode: 20000, scenarioVerifyInfoList: [checkInfoType])

        when: "Calling flashIntercept method"
        mapper.flashIntercept(orderCreateRequestType, orderCheckResponse)

        then: "A BusinessExceptionBuilder.AlertException should be thrown"
        def exception = thrown(BusinessException)
        exception.errorCode == 684
        exception.friendlyMessage == "您有2笔闪住订单在途，暂不可使用闪住功能，请更换其它支付方式"
    }

}
