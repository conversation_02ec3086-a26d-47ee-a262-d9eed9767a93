package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookingcheck

import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelBookInput
import com.ctrip.corp.bff.framework.hotel.entity.contract.HotelDateRangeInfo
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.entity.UserInfo
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CityInput
import com.ctrip.corp.bff.framework.template.entity.contract.integration.CorpPayInfo
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple1
import com.ctrip.corp.bff.hotel.book.service.BookingCheckRequestType
import com.ctrip.corp.bff.hotel.book.service.KeywordAutoComplete
import mockit.internal.state.SavePoint
import spock.lang.Specification

class MapperOfCheckBookingLimitionRequestTypeTest extends Specification {

    def tester = Spy(new MapperOfCheckBookingLimitionRequestType())

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "convert" () {
        given:
        1 * tester.buildBaseInfoType(_) >> null
        1 * tester.buildBookingInfoType(_) >> null
        expect:
        tester.convert(Tuple1.of(null)) != null
    }

    def "buildBaseInfoType" () {
        expect:
        tester.buildBaseInfoType(new BookingCheckRequestType(corpPayInfo: new CorpPayInfo(corpPayType: "private"),integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")))).uid == "a"
    }

    def "buildBookingInfoType" () {
        expect:
        tester.buildBookingInfoType(new BookingCheckRequestType(keywordAutoComplete: new KeywordAutoComplete(),cityInput: new CityInput(cityId: 2), hotelBookInput: new HotelBookInput(hotelDateRangeInfo: new HotelDateRangeInfo()),corpPayInfo: new CorpPayInfo(corpPayType: "private"),integrationSoaRequestType: new IntegrationSoaRequestType(userInfo: new UserInfo(userId: "a")))).cityId == 2
    }

    def "buildHotelIdList" () {
        expect:
        tester.buildHotelIdList("HOTEL.HOTEL.1") == [1]
    }
}
