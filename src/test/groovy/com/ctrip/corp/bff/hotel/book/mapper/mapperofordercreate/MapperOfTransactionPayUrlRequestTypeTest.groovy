package com.ctrip.corp.bff.hotel.book.mapper.mapperofordercreate

import com.ctrip.corp.agg.hotel.roomavailable.entity.BookRoomInfoEntity
import com.ctrip.corp.agg.hotel.roomavailable.entity.QueryCheckAvailContextResponseType
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ReservationResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.ContinueTypeConst
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.CreateOrderResult
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ordercreate.OrderCreateToken
import com.ctrip.corp.bff.framework.template.entity.IntegrationSoaRequestType
import com.ctrip.corp.bff.framework.template.mapper.param.tupleimpl.Tuple4
import com.ctrip.corp.bff.hotel.book.common.enums.RoomTypeEnum
import com.ctrip.corp.bff.hotel.book.common.util.OrderCreateProcessorOfUtil
import com.ctrip.corp.bff.hotel.book.common.wrapper.WrapperOfCheckAvail
import com.ctrip.corp.bff.hotel.book.contract.BookUrlInfo
import com.ctrip.corp.bff.hotel.book.contract.OrderCreateRequestType
import com.ctrip.corp.hotelbooking.hotelws.entity.CreateOrderResponseType
import com.ctrip.soa._21685.TransactionPayUrlRequestType
import mockit.internal.state.SavePoint
import spock.lang.Specification
import spock.lang.Unroll

/**
 * <AUTHOR>
 * @date 2024/12/25 19:59
 *
 */
class MapperOfTransactionPayUrlRequestTypeTest extends Specification {


    def savePoint = new SavePoint()

    void setup() {

    }

    void cleanup() {
        savePoint.rollback()
    }

    @Unroll
    def "testConvert with different scenarios"() {
        given:
        OrderCreateRequestType orderCreateRequestType = new OrderCreateRequestType(
                integrationSoaRequestType: new IntegrationSoaRequestType(requestId: "testRequestId"),
                bookUrlInfos: [new BookUrlInfo(urlType: "ORDER_DETAIL_URL", urlValue: "https://ct.ctrip.com/m/Detail/Hotel/{0}")]
        )
        CreateOrderResponseType createOrderResponseType = new CreateOrderResponseType()
        CreateOrderResult createOrderResult = new CreateOrderResult(
                orderID: 12345L, transactionUUID: "uuid-12345")
        OrderCreateToken orderCreateToken = new OrderCreateToken(
                continueTypes: Arrays.asList(ContinueTypeConst.CONFIRM_ORDER),
                createOrderResult: createOrderResult)
        def queryCheckAvailContextResponseType = new QueryCheckAvailContextResponseType(roomInfo: new BookRoomInfoEntity(gdsType: "", balanceType: "PP", roomType: roomTypeEnum.name()))
        WrapperOfCheckAvail.CheckAvailContextInfo checkAvailInfo =
                WrapperOfCheckAvail.checkAvailContextBuilder()
                        .setQueryCheckAvailContextResponseType(queryCheckAvailContextResponseType)
                        .setResourceToken(new ResourceToken(reservationResourceToken: new ReservationResourceToken(wsId: "wsid")))
                        .build().getCheckAvailContextInfo()

        when:
        TransactionPayUrlRequestType result = new MapperOfTransactionPayUrlRequestType().convert(
                Tuple4.of(orderCreateRequestType, createOrderResponseType, orderCreateToken, checkAvailInfo) as Tuple4<OrderCreateRequestType, CreateOrderResponseType, OrderCreateToken, WrapperOfCheckAvail.CheckAvailContextInfo>
        )

        then:
        result != null
        result.getRequestId() == "testRequestId"
        result.getProductLine() == 2
        result.getSubProductLine() == expectedSubProductLine
        result.getOrderId() == 12345L
        result.getTransactionScenario() == "BOOK"
        result.getTransactionUuid() == "uuid-12345"
        result.getSuccessUrl() == "https://ct.ctrip.com/m/Detail/Hotel/12345"

        where:
        roomTypeEnum   || expectedSubProductLine
        RoomTypeEnum.C || "C"
        RoomTypeEnum.M || "M"
    }
}
