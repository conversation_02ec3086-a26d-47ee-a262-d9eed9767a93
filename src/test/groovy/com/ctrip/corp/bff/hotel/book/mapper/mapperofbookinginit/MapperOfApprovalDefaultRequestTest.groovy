package com.ctrip.corp.bff.hotel.book.mapper.mapperofbookinginit

import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelGeoInfoResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.HotelResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.OrderResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.ResourceToken
import com.ctrip.corp.bff.framework.hotel.resourcetoken.entity.common.RcToken
import com.ctrip.corp.bff.hotel.book.common.util.TokenParseUtil
import com.ctrip.corp.bff.hotel.book.contract.BookingInitRequestType
import mockit.internal.state.SavePoint
import spock.lang.Specification

/**
 * <AUTHOR> @date 2024-08-13
 * */
class MapperOfApprovalDefaultRequestTest extends Specification {

    def mapperOfApprovalDefaultRequest = Spy(new MapperOfBatchApprovalDefaultRequest())

    def savePoint = new SavePoint()

    void setup() {
    }

    void cleanup() {
        savePoint.rollback()
    }

    def "buildOrderId"() {
        given:
        BookingInitRequestType bookingInitRequestType = new BookingInitRequestType()
        bookingInitRequestType.setOrderId("1234")
        ResourceToken resourceToken = new ResourceToken()
        OrderResourceToken orderResourceToken = new OrderResourceToken()
        orderResourceToken.setOrderId(123)
        resourceToken.setOrderResourceToken(orderResourceToken)
        when:
        def orderId = mapperOfApprovalDefaultRequest.buildOrderId(bookingInitRequestType, resourceToken)
        then:
        orderId == "123"
        when:
        orderId = mapperOfApprovalDefaultRequest.buildOrderId(bookingInitRequestType, null)
        then:
        orderId == "1234"
    }

    def "buildCityId"() {
        given:
        ResourceToken resourceToken = new ResourceToken()
        HotelResourceToken hotelResourceToken = new HotelResourceToken()
        HotelGeoInfoResourceToken hotelGeoInfoResourceToken = new HotelGeoInfoResourceToken()
        hotelGeoInfoResourceToken.setCityId(123)
        hotelResourceToken.setHotelGeoInfoResourceToken(hotelGeoInfoResourceToken)
        resourceToken.setHotelResourceToken(hotelResourceToken)
        when:
        def orderId = mapperOfApprovalDefaultRequest.buildCityId(resourceToken)
        then:
        orderId == 123
        when:
        orderId = mapperOfApprovalDefaultRequest.buildCityId(null)
        then:
        orderId == null
    }

    def "test-RcToken"(){
        given:
        when:
        def a = TokenParseUtil.parseToken("H4sIAAAAAAAAAOPidHQPcnX1dfULEWIKC5Nie9q67cnuaUqMIQDuZ4SeGgAAAA==", RcToken.class)
        then:
        a != null
    }
}
